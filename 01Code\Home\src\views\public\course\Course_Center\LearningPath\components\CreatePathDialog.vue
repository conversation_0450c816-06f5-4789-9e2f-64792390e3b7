<template>
  <el-dialog
    v-model="visible"
    title="创建学习路径"
    width="600px"
    :before-close="handleClose"
  >
    <div class="create-form">
      <el-form :model="pathForm" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="路径名称" prop="pathName">
          <el-input
            v-model="pathForm.pathName"
            placeholder="请输入学习路径名称"
            clearable
          />
        </el-form-item>
        
        <el-form-item label="路径描述" prop="description">
          <el-input
            v-model="pathForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入学习路径描述"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="createPath">创建</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'create-path'])

const visible = ref(false)
const formRef = ref()

const pathForm = reactive({
  pathName: '',
  description: '',
  difficulty: '',
  duration: '',
  tags: ''
})

const rules = {
  pathName: [
    { required: true, message: '请输入路径名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: false, message: '请输入路径描述', trigger: 'blur' }
  ],
  difficulty: [
    { required: true, message: '请选择难度等级', trigger: 'change' }
  ]
}

// 监听显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    // 重置表单
    resetForm()
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 重置表单
const resetForm = () => {
  Object.assign(pathForm, {
    pathName: '',
    description: '',
    difficulty: '',
    duration: '',
    tags: ''
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 创建路径
const createPath = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      const newPath = {
        id: Date.now(),
        pathName: pathForm.pathName,
        description: pathForm.description,
        difficulty: pathForm.difficulty,
        duration: pathForm.duration,
        tags: pathForm.tags ? pathForm.tags.split(',').map(tag => tag.trim()) : [],
        units: [
          {
            name: '单元1',
            nodes: [
              { name: '节点1', active: true, enabled: true },
              { name: '节点2', active: false, enabled: true }
            ]
          }
        ],
        createdAt: new Date().toISOString()
      }
      
      emit('create-path', newPath)
      ElMessage.success('学习路径创建成功！')
      visible.value = false
    } else {
      ElMessage.error('请填写完整信息')
    }
  })
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}
</script>

<style lang="scss" scoped>
.create-form {
  padding: 20px 0;

  .el-form-item {
    margin-bottom: 20px;
  }

  .el-select {
    width: 100%;
  }
}
</style>
