<template>
  <div class="learning-path-container">
    <!-- 顶部导航栏 -->
    <Topbar class="fixed-topbar" @goBack="goBack" ref="topbar" />

    <!-- 内容区域，为固定导航栏预留空间 -->
    <div class="content-area">
      <!-- 顶部操作栏 -->
      <TopToolbar :selectedPath="selectedPath" :savedPaths="savedPaths" @update:selectedPath="selectedPath = $event"
        @path-change="handlePathChange" @create-path="showCreateDialog = true" @edit-units="openEditUnitsDialog" />

      <!-- 学习路径流程图 -->
      <LearningPathFlow :savedPaths="savedPaths" :selectedPath="selectedPath" :currentUnits="currentUnits"
        @create-path="showCreateDialog = true" @edit-units="openEditUnitsDialog" @edit-nodes="editUnitNodes"
        @delete-unit="deleteUnit" />
    </div>

    <!-- 编辑单元对话框 -->
    <EditUnitsDialog v-model="showEditUnitsDialog" :units="currentUnits" @save-units="handleSaveUnits" />

    <!-- 编辑路径节点对话框 -->
    <EditNodesDialog v-model="showEditNodesDialog" :unitIndex="currentEditingUnitIndex" @save-nodes="handleSaveNodes" />

    <!-- 创建学习路径对话框 -->
    <CreatePathDialog v-model="showCreateDialog" @create-path="handleCreatePath" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'

// 导入组件
import Topbar from './components/Topbar.vue'
import TopToolbar from './components/TopToolbar.vue'
import LearningPathFlow from './components/LearningPathFlow.vue'
import EditUnitsDialog from './components/EditUnitsDialog.vue'
import EditNodesDialog from './components/EditNodesDialog.vue'
import CreatePathDialog from './components/CreatePathDialog.vue'

const route = useRoute()
const router = useRouter()

// 控制对话框显示
const showCreateDialog = ref(false)
const showEditUnitsDialog = ref(false)
const showEditNodesDialog = ref(false)

// 路径选择
const selectedPath = ref('1')

// 临时存储的路径数据
const savedPaths = ref([
  {
    id: '1',
    pathName: '测试学习路径',
    description: '这是一个测试路径',
    units: [
      {
        name: '单元1',
        nodes: [
          { name: '节点1', active: true, enabled: true },
          { name: '节点2', active: false, enabled: true },
          { name: '节点3', active: false, enabled: true }
        ]
      },
      {
        name: '单元2',
        nodes: [
          { name: '节点1', active: false, enabled: true },
          { name: '节点2', active: false, enabled: true }
        ]
      }
    ]
  }
])

// 当前选中路径的单元数据
const currentUnits = ref([
  {
    name: '单元1',
    nodes: [
      { name: '节点1', active: true, enabled: true },
      { name: '节点2', active: false, enabled: true },
      { name: '节点3', active: false, enabled: true }
    ]
  },
  {
    name: '单元2',
    nodes: [
      { name: '节点1', active: false, enabled: true },
      { name: '节点2', active: false, enabled: true }
    ]
  }
])

// 当前编辑的单元索引
const currentEditingUnitIndex = ref(-1)

// 返回
const goBack = () => {
  router.push({
    name: 'Announcement',
    params: {
      courseId: route.params.courseId,
      courseName: route.params.courseName || '默认课程'
    }
  })
}

// 处理创建路径
const handleCreatePath = (newPath) => {
  savedPaths.value.push(newPath)
  selectedPath.value = newPath.id
  currentUnits.value = [...newPath.units]
}

// 处理路径变化
const handlePathChange = (pathId) => {
  const pathData = savedPaths.value.find(path => path.id === pathId)
  if (pathData) {
    currentUnits.value = [...pathData.units]
  } else {
    currentUnits.value = []
  }
}

// 打开编辑单元对话框
const openEditUnitsDialog = () => {
  showEditUnitsDialog.value = true
}

// 处理保存单元
const handleSaveUnits = (units) => {
  currentUnits.value = [...units]

  // 更新选中路径的数据
  if (selectedPath.value) {
    const pathData = savedPaths.value.find(path => path.id === selectedPath.value)
    if (pathData) {
      pathData.units = [...units]
    }
  }
}

// 编辑单元节点
const editUnitNodes = (unitIndex) => {
  currentEditingUnitIndex.value = unitIndex
  showEditNodesDialog.value = true
}

// 处理保存节点
const handleSaveNodes = (data) => {
  const { unitIndex, nodes } = data
  if (unitIndex >= 0 && unitIndex < currentUnits.value.length) {
    currentUnits.value[unitIndex].nodes = [...nodes]

    // 更新选中路径的数据
    if (selectedPath.value) {
      const pathData = savedPaths.value.find(path => path.id === selectedPath.value)
      if (pathData) {
        pathData.units = [...currentUnits.value]
      }
    }
  }
}

// 删除单元
const deleteUnit = (unitIndex) => {
  if (currentUnits.value.length > 1) {
    currentUnits.value.splice(unitIndex, 1)

    // 更新选中路径的数据
    if (selectedPath.value) {
      const pathData = savedPaths.value.find(path => path.id === selectedPath.value)
      if (pathData) {
        pathData.units = [...currentUnits.value]
      }
    }

    ElMessage.success('单元删除成功！')
  } else {
    ElMessage.warning('至少需要保留一个单元！')
  }
}

</script>

<style lang="scss" scoped>
.learning-path-container {
  background-color: white;
  min-height: 100vh;
}

/* 固定Topbar样式 */
.fixed-topbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 内容区域样式，为固定导航栏预留空间 */
.content-area {
  margin-top: 60px; /* 60px导航栏高度 + 20px间距 */
  padding: 20px;
}

</style>