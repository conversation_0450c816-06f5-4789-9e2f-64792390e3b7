<!--src\components\CustomNotification.vue-->
<template>
  <transition name="notification">
    <div v-if="visible" class="custom-notification" :class="type">
      <div class="notification-content">
        <span class="notification-icon">
          <svg v-if="type === 'success'" viewBox="0 0 24 24">
            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
          </svg>
          <svg v-else viewBox="0 0 24 24">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
          </svg>
        </span>
        <span class="notification-message">{{ message }}</span>
      </div>
    </div>
  </transition>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const props = defineProps({
  message: String,
  type: {
    type: String,
    default: 'success',
    validator: value => ['success', 'error'].includes(value)
  },
  duration: {
    type: Number,
    default: 3000
  }
})

const visible = ref(false)

onMounted(() => {
  visible.value = true
  const timer = setTimeout(() => {
    visible.value = false
    clearTimeout(timer)
  }, props.duration)
})
</script>

<style lang="scss" scoped>
.custom-notification {
  position: fixed;
  top: 100px;
  left: 50%;
  transform: translateX(-50%);
  padding: 15px 24px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: inline-flex;
  align-items: center;
  z-index: 4000;
  animation: slideDown 0.3s ease-out forwards;

  &.success {
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    color: #52c41a;
  }

  &.error {
    background-color: #fff2f0;
    border: 1px solid #ffccc7;
    color: #ff4d4f;
  }

  .notification-content {
    display: flex;
    align-items: center;
  }

  .notification-icon {
    margin-right: 12px;
    display: flex;
    align-items: center;

    svg {
      width: 20px;
      height: 20px;
      fill: currentColor;
    }
  }

  .notification-message {
    font-size: 14px;
    line-height: 1.5;
  }
}

.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from,
.notification-leave-to {
  opacity: 0;
  transform: translate(-50%, -30px);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translate(-50%, -30px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}
</style>