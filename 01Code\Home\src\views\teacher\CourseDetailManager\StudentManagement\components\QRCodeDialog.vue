<!--src\views\teacher\CourseDetailManager\StudentManagement\components\QRCodeDialog.vue-->
<template>
  <el-dialog :model-value="visible" title="二维码邀请" width="30%" @update:model-value="updateVisible">
    <div class="qr-code-container">
      <div class="course-info">课程号：{{ courseCode }}</div>
      <div class="qr-code-image">
        <!-- 添加二维码图片显示 -->
        <img :src="qrCodeImage" alt="QR Code" style="width: 150px; height: 150px;">
      </div>
      <div class="qr-code-tip">使用"学习通"APP扫码加课</div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    courseCode: {
      type: String,
      required: true
    },
    // 添加二维码图片属性
    qrCodeImage: {
      type: String,
      default: ''
    }
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const updateVisible = (value) => {
      emit('update:visible', value);
    };

    return {
      updateVisible
    };
  }
};
</script>

<style scoped>
.qr-code-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.course-info {
  margin-bottom: 10px;
  font-weight: bold;
}

.qr-code-tip {
  margin-top: 10px;
  font-size: 12px;
  color: #666;
}
</style>