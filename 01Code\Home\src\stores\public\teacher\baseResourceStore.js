
import { defineStore } from 'pinia'
import { ref } from 'vue'
import {
  uploadFile,
  saveResource,
  saveResourceLocation,
  saveCourseLink,
  addCourseLink
} from '@/api/teacher/publicResources' // 确保路径正确

 export const useBaseResourceStore = defineStore('baseResource', () => {

  async function baseUploadResource(file, options = {}) {
    try {
        // 1. 上传文件
        const formData = new FormData();
        formData.append('file', file, file.name);
        const uploadResponse = await uploadFile(formData);
        
        // 从上传响应中获取必要信息
        const { 
            id: resourceId, 
            url: fileUrl, 
            name: fileName, 
            fileSize, 
            fileExtension 
        } = uploadResponse.result;

        // 2. 调用整合后的接口关联课程资源
        const linkData = {
            courseId: options.courseId, 
            resourceId: resourceId, 
            folderId: options.folderId || null, // 可为空
            syllabusId: null, // 可为空
            operationType: 0, // 0-上传新资源
            name: fileName,
            type: getResourceType(fileName), 
            description: options.description || '',
            url: fileUrl,
            fileSize: fileSize,
            fileExtension: fileExtension.replace('.', ''), // 去掉扩展名前的点
            publisherId: options.createdId, // 创建者ID
            ...options.additionalData // 其他可能需要的数据
        };

        const linkResponse = await addCourseLink(linkData);
        // 返回整合后的结果
        return {
            upload: uploadResponse,
            link: linkResponse,
            name: fileName,
            size: fileSize
        };

    } catch (error) {
        console.error('资源上传全流程失败:', error);
        throw error;
    }
}

// 辅助函数：根据文件名判断资源类型
function getResourceType(filename) {
    const extension = filename.split('.').pop().toLowerCase();
    const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv'];
    const pptExts = ['ppt', 'pptx'];
    const wordExts = ['doc', 'docx'];
    const pdfExts = ['pdf'];

    if (videoExts.includes(extension)) return 1; // 视频
    if (pptExts.includes(extension)) return 3; // PPT
    if (wordExts.includes(extension)) return 4; // Word
    if (pdfExts.includes(extension)) return 2; // 教材(PDF)
    return 6; // 其他情况视为外部资源
}


  async function addResourceLink(url, name, options = {}) {
    try {
      const resourceData = {
        url: url,
        publisherId: options.createdId,
        name: name,
        type: 5, // 通常链接类型与文件类型不同，可能需要调整
        fileExtension: '', // 链接没有文件扩展名
        ...options.additionalData
      }
      const saveResponse = await saveResource(resourceData)
      const resourceId = saveResponse.result.id

      // 2. 保存资源位置
      let locationResponse = null
      if (options.locationData) {
        locationResponse = await saveResourceLocation({
          resourceId: resourceId,
          folderId: options.folderId,
          locationType: options.locationData.type,
          courseId: options.locationData.courseId
        })
      }

      // 3. 关联课程资源
      const linkResponse = await saveCourseLink({
        resourceId: resourceId,
        courseId: options.locationData?.courseId,
        syllabusId: null,
        isReference: 0
      })

      // 返回整合后的结果
      return {
        save: saveResponse,
        location: locationResponse,
        link: linkResponse,
        name: name
      }

    } catch (error) {
      console.error('添加链接资源失败:', error)
      throw error
    }
  }
  

  return {
    baseUploadResource,
    addResourceLink 
  }
})