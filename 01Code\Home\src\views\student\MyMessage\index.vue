<template>
  <div class="message-container">
    <!-- 标签栏 -->
    <div class="tabs">
      <div 
        :class="['tab', { active: activeTab === 'all' }]" 
        @click="switchTab('all')"
      >全部</div>
      <div 
        :class="['tab', { active: activeTab === 'announcement' }]" 
        @click="switchTab('announcement')"
      >公告</div>
      <div 
        :class="['tab', { active: activeTab === 'private' }]" 
        @click="switchTab('private')"
      >私信</div>
    </div>
    
    <!-- 加载状态和错误提示 -->
    <div v-if="loading" class="loading">加载中...</div>
    <div v-else-if="error" class="error">加载失败：{{ error }}</div>
    <div v-else>
      <!-- 消息列表 -->
      <div class="message-list">
        <div 
          v-for="message in filteredMessages" 
          :key="message.id" 
          :class="['message-item', { unread: !message.read }]"
          @click="handleMessageClick(message)"
        >
          <!-- 未读红点 -->
          <div class="unread-dot" v-if="!message.read"></div> 
          <!-- 发布者头像 + 名称 -->
          <div class="message-avatar">
            <img 
              :src="message.publisher.avatar"  
              :alt="message.publisher.name"    
            >
          </div>
          <div class="message-content">
            <div class="message-header">
              <span class="sender">{{ message.publisher.name }}</span> <!-- 渲染发布者名称 -->
              <span class="time">{{ formatTime(message.time) }}</span>
            </div>
            <div class="message-text">{{ message.content }}</div>
          </div>
        </div>
      </div>
      
      <!-- 消息统计 -->
      <div class="message-count">
        共 <span class="count-number">{{ filteredMessages.length }}</span> 条消息
      </div>
      
      <!-- 加载更多按钮 -->
      <div class="load-more" v-if="hasMoreMessages">
        <button @click="loadMoreMessages">加载更多</button>
      </div>
    </div>
  </div>
</template>

<script>
import { useMessageStore } from '@/stores/messageStore';
import { onMounted, computed } from 'vue';

export default {
  data() {
    return {
      activeTab: 'all', 
      pageNum: 1,       
      pageSize: 10,     
    };
  },
  computed: {
    messageStore() {
      return useMessageStore();
    },
    messages() {
      return this.messageStore.messages;
    },
    loading() {
      return this.messageStore.loading;
    },
    error() {
      return this.messageStore.error;
    },
    total() {
      return this.messageStore.total;
    },
    filteredMessages() {
      if (this.loading || this.error) return [];
      
      let filtered = [...this.messages];
      if (this.activeTab === 'announcement') 
        filtered = filtered.filter(msg => msg.type === 0||1||2||3||4);
      else if (this.activeTab === 'private') 
        filtered = filtered.filter(msg => msg.type === 'private');
      
      return filtered;
    },
    hasMoreMessages() {
      return this.messages.length < this.total; 
    }
  },
  methods: {
    switchTab(tab) {
      this.activeTab = tab;
    },
    async loadMoreMessages() {
      await this.messageStore.loadMoreMessages();
    },
    async handleMessageClick(message) {
      if (!message.read) {
        await this.messageStore.markAsRead(message.id);
      }
    },
    formatTime(timestamp) {
      const now = new Date();
      const then = new Date(timestamp);
      const monthsDiff = now.getMonth() - then.getMonth() + (now.getFullYear() - then.getFullYear()) * 12;
      return `${monthsDiff}个月前`;
    }
  },
  async mounted() {
    await this.messageStore.fetchMessages(this.pageNum, this.pageSize); 
  }
};
</script>

<style scoped>
.message-container {
  width: 100%;
  margin: 0 ;
  padding: 16px;
  box-sizing: border-box;
}

.tabs {
  display: flex;
  margin-bottom: 16px;
  border-bottom: 1px solid #eee;
}

.tab {
  padding: 8px 24px;
  cursor: pointer;
  font-size: 18px;
  color: #666;
  position: relative;
}

.tab.active {
  color: #000;
  font-weight: bold;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #000;
}

.loading, .error {
  text-align: center;
  padding: 16px;
}

.message-list {
  margin-bottom: 16px;
}

/* 消息项容器：设置为相对定位，为红点的绝对定位做参考 */
.message-item {
  display: flex;
  align-items: center; 
  padding: 16px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative; 
}

.message-item:hover {
  background-color: #f9f9f9;
}

/* 未读红点样式：绝对定位，固定在左上角 */
.unread-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(255, 0, 0, 0.733);
  position: absolute; 
  top: 25px;          /* 与顶部保持一定距离*/
  left: 10px;         /* 与左侧保持一定距离*/
}

.message-avatar {
  margin-right: 16px;
}

.message-avatar img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.message-content {
  flex: 1;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.sender {
  font-weight: bold;
}

.time {
  color: #999;
  font-size: 12px;
}

.message-text {
  color: #333;
}

.message-count {
  text-align: center;
  padding: 8px;
  color: #666;
  font-size: 14px;
}

.count-number {
  color: #0066cc; 
  font-weight: 500;
}

.load-more {
  text-align: center;
  margin-top: 16px;
}

.load-more button {
  padding: 8px 24px;
  background-color: #f0f0f0;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.load-more button:hover {
  background-color: #e0e0e0;
}
</style>