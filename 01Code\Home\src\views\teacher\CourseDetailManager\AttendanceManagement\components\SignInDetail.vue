<template>
  <div class="sign-in-detail-container">
    <!-- 顶部标题和返回按钮 -->
    <div class="header-section">
      <button class="back-button" @click="goBack">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
          <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z"/>
        </svg>
        返回
      </button>
      <h2 class="sign-in-title">{{ signInName }}</h2>
    </div>

    <!-- 筛选控制区域 -->
    <div class="filter-controls">
      <div class="filter-item">
        <label for="class-select">班级：</label>
        <select id="class-select" v-model="selectedClass" class="filter-select">
          <option value="all">全部班级</option>
          <option v-for="(classItem, index) in classList" :key="index" :value="classItem">
            {{ classItem }}
          </option>
        </select>
      </div>
      
      <div class="filter-item">
        <label for="status-select">状态：</label>
        <select id="status-select" v-model="selectedStatus" class="filter-select">
          <option value="all">全部状态</option>
          <option v-for="(status, index) in statusList" :key="index" :value="status.value">
            {{ status.label }}
          </option>
        </select>
      </div>
      
      <div class="stats">
        已签到：{{ signedCount }} / 应签到：{{ totalCount }}
      </div>
    </div>
    
    <!-- 签到表格区域 -->
    <div class="sign-in-table">
      <table>
        <thead>
          <tr>
            <th>序号</th>
            <th>姓名</th>
            <th>学号</th>
            <th>状态</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(student, index) in filteredStudents" :key="student.studentId">
            <td>{{ index + 1 }}</td>
            <td>{{ student.studentName || '--' }}</td>
            <td>{{ student.studentCode || '--' }}</td>
            <td>
              <select v-model="student.checkStatus" @change="updateStatus(student)" class="status-select">
                <option v-for="status in statusList" :key="status.value" :value="status.value">
                  {{ status.label }}
                </option>
              </select>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { getAttendanceDetail,updateAttendanceStatus} from '@/api/teacher/attendance'

const router = useRouter()
const route = useRoute()

// 从路由参数获取签到名称
const signInName = ref(route.query.signInName || '签到详情')

// 状态列表
const statusList = ref([
  { value: 1, label: '已签到' },
  { value: 0, label: '未签到' },
  { value: 2, label: '迟到' },
  { value: 3, label: '旷课' },
  { value: 4, label: '请假' },
])

// 班级列表
const classList = ref([])
// 学生数据
const students = ref([])
const selectedClass = ref('all')
const selectedStatus = ref('all')

// 计算属性
const filteredStudents = computed(() => {
  return students.value.filter(student => {
    const classMatch = selectedClass.value === 'all' || student.className === selectedClass.value
    const statusMatch = selectedStatus.value === 'all' || student.checkStatus === selectedStatus.value
    return classMatch && statusMatch
  })
})

const signedCount = computed(() => {
  return students.value.filter(s => s.checkStatus === 1).length
})

const totalCount = computed(() => {
  return students.value.length
})



const updateStatus = async (student) => {
  try {
    const data = {
      attendanceId: route.params.id, // 从路由参数获取签到ID
      studentId: student.studentId,
      classId: student.classId, // 假设学生数据中有classId字段
      checkStatus: student.checkStatus
    }
    
    const res = await updateAttendanceStatus(data)
    if (res.code === 200) {
      console.log('状态更新成功')
    } else {
      // 恢复原来的状态
      const originalStatus = students.value.find(s => s.studentId === student.studentId)?.checkStatus
      if (originalStatus !== undefined) {
        student.checkStatus = originalStatus
      }
      console.error('状态更新失败:', res.message)
    }
  } catch (error) {
    // 恢复原来的状态
    const originalStatus = students.value.find(s => s.studentId === student.studentId)?.checkStatus
    if (originalStatus !== undefined) {
      student.checkStatus = originalStatus
    }
    console.error('状态更新失败:', error)
  }
}

const goBack = () => {
  router.go(-1) // 返回上一页
}

const loadAttendanceDetail = async () => {
  try {
    const res = await getAttendanceDetail(route.params.id)
    if (res.code === 200) {
      students.value = res.result
      // 提取班级列表
      const uniqueClasses = [...new Set(res.result.map(item => item.className))]
      classList.value = uniqueClasses
    }
  } catch (error) {
    console.error('加载签到详情失败:', error)
  }
}

onMounted(() => {
  loadAttendanceDetail()
})
</script>

<style scoped lang="scss">
.sign-in-detail-container {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  height:97%;
}

.header-section {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 16px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    background-color: #e6e6e6;
    border-color: #bfbfbf;
  }
  
  svg {
    margin-right: 4px;
  }
}

.sign-in-title {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
  
  .filter-item {
    display: flex;
    align-items: center;
  }
  
  .filter-select {
    padding: 6px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background-color: #fff;
    transition: all 0.3s;
    
    &:hover {
      border-color: #40a9ff;
    }
    
    &:focus {
      outline: none;
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
  
  .stats {
    margin-left: auto;
    color: #666;
    font-size: 14px;
  }
}

.sign-in-table {
  width: 100%;
  overflow-x: auto;
  
  table {
    width: 100%;
    border-collapse: collapse;
    
    th, td {
      padding: 12px 16px;
      text-align: left;
      border-bottom: 1px solid #f0f0f0;
    }
    
    th {
      background-color: #fafafa;
      font-weight: 500;
      color: #333;
    }
    
    tr:hover {
      background-color: #fafafa;
    }
  }
}

.status-select {
  padding: 4px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fff;
  transition: all 0.3s;
  
  &:hover {
    border-color: #40a9ff;
  }
  
  &:focus {
    outline: none;
    border-color: #40a9ff;
  }
}
</style>