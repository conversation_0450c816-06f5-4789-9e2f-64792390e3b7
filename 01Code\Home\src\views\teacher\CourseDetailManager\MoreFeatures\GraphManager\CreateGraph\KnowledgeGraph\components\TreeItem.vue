<template>
  <div class="tree-item" :style="{ paddingLeft: `${level * 20}px` }">
    <!-- 当前目录节点 -->
    <div
      class="tree-row"
      :class="{ 'is-root': !item.parentId }"
      @click="toggleSelect"
    >
      <span class="arrow" v-if="hasChildrenOrResources" @click.stop="toggleExpanded">
        {{ expanded ? '▼' : '▶' }}
      </span>
      <input
        v-if="false"
        type="checkbox"
        :checked="isChecked"
        @change="onCheckboxChange"
        @click.stop
      />
      <img class="icon" :src="folderIconPath" />
      <span class="title">{{ item.name }}</span>
    </div>

    <!-- 子目录与资源 -->
    <div class="tree-children" v-show="expanded" v-if="hasChildrenOrResources">
      <!-- 子目录 -->
      <TreeItem
        v-for="child in item.children"
        :key="child.id"
        :item="child"
        :level="level + 1"
        :selected="selected"
        @update:selected="val => emit('update:selected', val)"
      />

      <!-- 子资源 -->
      <div
        class="tree-row resource-row"
        v-for="res in item.resources"
        :key="res.id"
        @click="toggleSelect(res)"
      >
        <input
          type="checkbox"
          :checked="selected.includes(res.id)"
          @change="onCheckboxChange($event, res)"
          @click.stop
        />
        <img class="icon" :src="getIconByType(res.type)" />
        <span class="title">{{ res.name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import TreeItem from './TreeItem.vue'; // 自身递归引用

// 静态导入所有图标
import folderIcon from '@/assets/courseMap-icon/resourceType/folderIcon.svg';
import videoIcon from '@/assets/courseMap-icon/resourceType/videoIcon.svg';
import bookIcon from '@/assets/courseMap-icon/resourceType/bookIcon.svg';
import pptIcon from '@/assets/courseMap-icon/resourceType/pptIcon.svg';
import wordIcon from '@/assets/courseMap-icon/resourceType/wordIcon.svg';
import externalIcon from '@/assets/courseMap-icon/resourceType/externalIcon.svg';
import otherIcon from '@/assets/courseMap-icon/resourceType/otherIcon.svg';

const props = defineProps({
  item: {
    type: Object,
    default: () => ({ name: '', children: [], resources: [] })
  },
  level: {
    type: Number,
    default: 0
  },
  selected: Array
});

const emit = defineEmits(['update:selected']);

const expanded = ref(false);

const hasChildrenOrResources = computed(() => {
  return (props.item.children?.length || 0) > 0 || (props.item.resources?.length || 0) > 0;
});

const isChecked = computed(() => {
  const allIds = getAllSelectableIds(props.item);
  return allIds.every(id => props.selected.includes(id));
});

const toggleExpanded = () => {
  expanded.value = !expanded.value;
};

const toggleSelect = (node = props.item) => {
  const ids = getAllSelectableIds(node);
  const newSet = new Set(props.selected);
  const allSelected = ids.every(id => newSet.has(id));

  ids.forEach(id => {
    allSelected ? newSet.delete(id) : newSet.add(id);
  });

  emit('update:selected', Array.from(newSet));
};

const onCheckboxChange = (e, node = props.item) => {
  const ids = getAllSelectableIds(node);
  const newSet = new Set(props.selected);

  if (e.target.checked) {
    ids.forEach(id => newSet.add(id));
  } else {
    ids.forEach(id => newSet.delete(id));
  }

  emit('update:selected', Array.from(newSet));
};

const getAllSelectableIds = (node) => {
  const ids = [];
  if (node.id) ids.push(node.id);
  if (node.resources) ids.push(...node.resources.map(r => r.id));
  if (node.children) {
    node.children.forEach(child => {
      ids.push(...getAllSelectableIds(child));
    });
  }
  return ids;
};

const getIconByType = (type) => {
  switch (type) {
    case 1: return videoIcon;
    case 2: return bookIcon;
    case 3: return pptIcon;
    case 4: return wordIcon;
    case 5: return externalIcon;
    case 6: return otherIcon;
    default: return otherIcon;
  }
};

const folderIconPath = folderIcon;
</script>

<style lang="scss" scoped>
.tree-item {
  .tree-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 20px;
    padding: 10px 10px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;

    &.is-root {
      background-color: #e8f5e9;
    }

    .arrow {
      font-size: 12px;
      width: 16px;
      text-align: center;
      cursor: pointer;
      color: #999;
    }

    .icon {
      width: 30px;
      height: 30px;
    }

    .title {
      font-size: 16px;
      color: #555;
      flex: 1;
    }

    input[type='checkbox'] {
      appearance: none;
      width: 16px;
      height: 16px;
      border: 2px solid #4c7bff;
      border-radius: 4px;
      background: #fff;
      cursor: pointer;
      position: relative;

      &:checked {
        background-color: #4c7bff;
        border-color: #4c7bff;
      }

      &:checked::after {
        content: '';
        position: absolute;
        left: 4px;
        top: 1px;
        width: 5px;
        height: 9px;
        border: solid #fff;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
      }
    }
  }

  .tree-children {
    margin-left: 20px;
  }

  .resource-row {
    padding-left: 20px;
  }
}
</style>  
