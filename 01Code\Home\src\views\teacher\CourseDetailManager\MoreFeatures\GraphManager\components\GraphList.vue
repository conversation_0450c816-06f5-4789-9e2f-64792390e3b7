<template>
  <div class="graph-list">
    <div v-for="graph in graphs" :key="graph.graphId" class="graph-item">
      <span class="name">{{ graph.graphName }}</span>
      <div class="actions">
        <button class="edit-btn" @click="handleEdit(graph.graphId, graph.graphType)">
          编辑
        </button>
        <button class="delete-btn" @click="handleDelete(graph.graphId, graph.graphName)">
          删除
        </button>
      </div>
    </div>

    <div v-if="graphs.length === 0" class="empty-tip">
      暂无图谱，请先创建
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useKnowledgeGraphStore } from '@/stores/teacher/graphManager/knowledgeGraphStore'
import { useRouter, useRoute } from 'vue-router'

const store = useKnowledgeGraphStore()
const router = useRouter()
const route = useRoute()
const { graphList } = storeToRefs(store)

// 编辑图谱处理
const handleEdit = (graphId, graphType) => {
  // 类型检查（确保是数字）
  const type = Number(graphType);
  if (isNaN(type)) {
    console.error("graphType 不是数字:", graphType);
    return;
  }

  const routeName = store.getRouteNameByGraphType(type);
  router.push({
    name: routeName,
    params: {
      graphId,
      courseId: store.courseId,
      courseName: route.params.courseName || '默认课程'
    }
  });
};

// 删除图谱处理
const handleDelete = async (graphId, graphName) => {
  const confirmed = window.confirm(`确定要删除图谱「${graphName}」吗？此操作不可恢复！`)
  if (confirmed) {
    try {
      await store.deleteGraphById(graphId)
      if (!store.error) {
        await store.fetchGraphListByCourse(store.courseId)
      } else {
        alert(`删除失败：${store.error}`)
      }
    } catch (error) {
      alert('删除操作出现异常')
    }
  }
}

// 处理格式化数据
const graphs = computed(() => graphList.value)
</script>

<style scoped>
.graph-list {
  padding: 12px;
}

.graph-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  margin-bottom: 8px;
  border: 1px solid #e4e4e4;
  border-radius: 6px;
  background-color: #fafafa;
  transition: background-color 0.2s;
}

.graph-item:hover {
  background-color: #f0f0f0;
}

.name {
  font-weight: 600;
  flex: 1;
}

.time-info {
  color: #666;
  font-size: 13px;
  margin-right: 16px;
}

.actions {
  display: flex;
  gap: 8px;
}

.edit-btn {
  background-color: #4c7bff;
  color: white;
  border: none;
  padding: 4px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.edit-btn:hover {
  background-color: #3a6ae0;
}

.delete-btn {
  background-color: #ff4d4f;
  color: white;
  border: none;
  padding: 4px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.delete-btn:hover {
  background-color: #d9363e;
}

.empty-tip {
  text-align: center;
  color: #888;
  font-size: 14px;
  margin-top: 20px;
}
</style>