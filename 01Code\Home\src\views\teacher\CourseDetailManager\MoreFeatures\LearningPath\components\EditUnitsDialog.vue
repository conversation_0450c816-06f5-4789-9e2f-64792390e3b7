<template>
  <el-dialog
    v-model="visible"
    title="编辑单元"
    width="500px"
    :before-close="handleClose"
  >
    <div class="edit-units-form">
      <div class="units-list">
        <div
          v-for="(unit, index) in editingUnits"
          :key="index"
          class="unit-item"
        >
          <div class="unit-header">
            <span class="unit-number">单元{{ index + 1 }}</span>
            <div class="unit-controls">
              <el-button
                size="small"
                type="danger"
                circle
                @click="removeUnit(index)"
                :icon="Minus"
                :disabled="editingUnits.length <= 1"
              />
            </div>
          </div>
          <el-input
            v-model="unit.name"
            placeholder="请输入单元名称"
            class="unit-name-input"
          />
        </div>
      </div>

      <div class="add-chapter-section">
        <el-button type="primary" @click="addChapter" class="add-chapter-btn">
          <el-icon><Plus /></el-icon>
          添加新章节
        </el-button>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="saveUnits">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { Plus, Minus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  units: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'save-units'])

const visible = ref(false)
const editingUnits = ref([])

// 监听显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    // 复制单元数据到编辑状态
    editingUnits.value = props.units.map(unit => ({ ...unit }))
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 删除单元
const removeUnit = (index) => {
  if (editingUnits.value.length > 1) {
    editingUnits.value.splice(index, 1)
    
    // 重新编号
    editingUnits.value.forEach((unit, idx) => {
      if (!unit.name || unit.name.startsWith('单元')) {
        unit.name = `单元${idx + 1}`
      }
    })
  }
}

// 添加章节
const addChapter = () => {
  const newUnit = {
    name: `单元${editingUnits.value.length + 1}`,
    nodes: [
      { name: '节点1', active: true, enabled: true },
      { name: '节点2', active: false, enabled: true }
    ]
  }
  editingUnits.value.push(newUnit)
  
  // 重新编号
  editingUnits.value.forEach((unit, idx) => {
    if (!unit.name || unit.name.startsWith('单元')) {
      unit.name = `单元${idx + 1}`
    }
  })
}

// 保存单元
const saveUnits = () => {
  emit('save-units', [...editingUnits.value])
  ElMessage.success('单元保存成功！')
  visible.value = false
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}
</script>

<style lang="scss" scoped>
.edit-units-form {
  .units-list {
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: 20px;

    .unit-item {
      margin-bottom: 15px;
      padding: 15px;
      border: 1px solid #e4e7ed;
      border-radius: 6px;
      background-color: #fafafa;

      .unit-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;

        .unit-number {
          font-weight: 500;
          color: #409eff;
        }

        .unit-controls {
          display: flex;
          gap: 5px;
        }
      }

      .unit-name-input {
        width: 100%;
      }

      &:hover {
        background-color: #f5f7fa;
      }
    }
  }

  .add-chapter-section {
    text-align: center;
    padding: 20px 0;
    border-top: 1px solid #e4e7ed;
    margin-top: 20px;

    .add-chapter-btn {
      width: 200px;
    }
  }
}
</style>
