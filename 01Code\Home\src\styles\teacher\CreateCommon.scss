.nav-container {
    width: 100%;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 24px;
    max-width: 1200px;
    margin: 0 auto;
}

.steps-container {
    flex: 1;
    margin: 0 20px;
    max-width: 600px;
}

:deep(.el-step__head.is-process) {
    color: #409eff;
    border-color: #409eff;
}

:deep(.el-step__title.is-process) {
    color: #409eff;
}

.action-buttons {
    display: flex;
    gap: 12px;
}

.back-button {
    padding: 8px 12px;
    background: transparent;
    border: 0px;
}

.back-button:hover,
.back-button:active {
    color: #409eff;
    background: transparent;
    border: 0px;
}

.preview-button,
.save-button {
    padding: 8px 15px;
}

/* 步骤条样式调整 */
:deep(.el-step__title) {
    font-size: 14px;
}

:deep(.el-step.is-center .el-step__line) {
    left: calc(50% + 20px);
    right: calc(-50% + 20px);
}

/* 主内容区布局 */
.main_content {
    max-width: 1000px;
    margin: 24px auto;
    // padding: 32px;
    display: flex;
    flex-direction: column;
    min-height: 60vh;
}

.step-control {
    margin-top: auto;
    padding-top: 24px;
    display: flex;
    justify-content: center;
    gap: 12px;
}

/* 富文本编辑器容器 */
.quill-wrapper {
    width: 100%;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    box-sizing: border-box;
    background-color: #fff;
}

/* 强制编辑器高度、左对齐、字体样式统一 */
::v-deep(.quill-editor) {
    height: 300px !important;
    font-size: 14px;
    text-align: left;
    background-color: white;
    padding: 0;
    color: #303133;
    line-height: 1.6;
}

/* 禁止选中时反色 */
::v-deep(.ql-editor::selection) {
    background: transparent;
}



/* 响应式调整 */
@media (max-width: 768px) {
    .nav-bar {
        flex-wrap: wrap;
        padding: 12px;
    }

    .steps-container {
        order: 3;
        width: 100%;
        margin: 12px 0 0;
    }

    .back-button {
        order: 1;
    }

    .action-buttons {
        order: 2;
        margin-left: auto;
    }
}

.steps-container {
    flex: 1;
    margin: 0 16px;
}

.nav-bar {
    display: flex;
    align-items: center;
    height: 78px;
    padding: 0 20px;
    background-color: #fff;
    border-bottom: 1px solid #ebeef5;
}

.action-buttons {
    display: flex;
    gap: 12px;
}

.main_content {
    padding: 20px;
}

.step-control {
    margin-top: 20px;
    text-align: center;
}