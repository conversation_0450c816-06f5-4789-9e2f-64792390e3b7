<!-- src\views\public\course\Course_Map\components\TreeGraph.vue -->
<template>
  <!-- 树图容器 -->
  <div class="tree-graph-wrapper">
    <!-- 顶部标题栏 -->
    <div class="graph-header">
      <div class="header-item">课程名称<br><span class="course-chapter">该图谱归属的课程</span></div>

      <div class="header-item">章节名称<br><span class="course-chapter">该课程拥有的章节</span></div>

      <div class="header-item">问题<br><span class="course-chapter">与章节对应的问题</span></div>
    </div>
    
    <!-- 树图内容 -->
    <div ref="graphContainer" class="tree-graph-container"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick, defineEmits, watch, defineProps } from 'vue'
// 替换原来的 import G6 from '@antv/g6'
import * as G6 from '@antv/g6'

const selectedNodeId = ref(null)

// 定义组件接收的属性
const props = defineProps({
  data: {
    type: Object,
    default: () => ({
      id: 'root',
      label: '知识图谱',
      tags: [],
      children: []
    })
  }
})

// 定义组件触发的事件
const emit = defineEmits(['node-click'])

// 分支颜色配置（参考环形图分配，支持更多分支）
const branchColorsArr = [
  // '#ff4d4f', // 红
  // '#3b50f9', // 靛蓝
  // '#feca57', // 黄
  // '#f93b7c', // 粉
  'rgb(255,105,165)',
  'rgb(30,160,255)',
  'rgb(0,200,140)'
]

// 递归折叠除根节点和其直接子节点以外的所有节点
const collapseDeepNodes = (node, level = 0) => {
  if (node.children) {
    if (level >= 1) {
      node.collapsed = false
    }
    node.children.forEach(child => collapseDeepNodes(child, level + 1))
  }
}

// 递归为每个节点分配 branchKey，实现一级节点分配彩色
function assignBranchKey(node, branchKey = null, level = 0, branchIdx = 0) {
  node.level = level
  if (level === 0) {
    node.branchKey = null // 只有根节点深色
    //node.color = 'rgb(255,105,165)' // 根节点深色
  } else if (level === 1) {
    node.branchKey = branchIdx // 一级节点分配彩色
    //node.color = 'rgb(30,160,255)'
  } else {
    node.branchKey = branchKey // 子孙节点继承一级节点色
    //node.color = 'rgb(0,200,140)'
  }
  if (node.children) {
    node.children.forEach((child, idx) => assignBranchKey(child, node.branchKey, level + 1, idx))
  }
  
}

// 树图容器引用
const graphContainer = ref(null)
// 树图实例
let graph = null

// 组件挂载后初始化树图
onMounted(async () => {
  await nextTick()
  initGraph()
  window.addEventListener('resize', handleResize)
})

// 组件卸载前销毁树图
onBeforeUnmount(() => {
  if (graph) graph.destroy()
  window.removeEventListener('resize', handleResize)
})

// 监听数据变化
watch(() => props.data, (newData) => {
  if (newData && graph) {
    // 深拷贝，避免响应式污染
    const processedData = JSON.parse(JSON.stringify(newData))
    collapseDeepNodes(processedData)
    assignBranchKey(processedData)
    graph.changeData(processedData)
    graph.fitCenter()
    graph.zoom(0.9)
  }
}, { deep: true })

// 处理窗口大小变化
const handleResize = () => {
  if (graph && graphContainer.value) {
    graph.changeSize(
      graphContainer.value.clientWidth,
      graphContainer.value.clientHeight
    )
    //graph.fitCenter()
  }
}


// 初始化树图
const initGraph = () => {
  if (!graphContainer.value) return
  // 注册自定义节点
  G6.registerNode('custom-node', {
    draw(cfg, group) {
       const isSelected = selectedNodeId.value === cfg.id
      const isRoot = (cfg.level === 0 || cfg.branchKey === null)
      //   ? '#24285e'
      //   : branchColorsArr[cfg.branchKey % branchColorsArr.length] || '#8a6de3'
      // if (cfg.level === 0)
      // {
      //   const color = 'rgb(255,105,165)',
      // }
      // else if (cfg.level === 1)
      // {
      //   const color = 'rgb(30,160,255)',
      // }
      // else
      // {
      //   const color = 'rgb(0,200,140)',
      // }
      // const color = isRoot ? 'rgb(255,105,165)' : 'rgb(30,160,255)'
      const level0 = cfg.level === 0
      const level1 = cfg.level === 1
      const level2 = cfg.level === 2
      const color = level0 ? 'rgb(255,105,165)' : level1 ? 'rgb(30,160,255)' : 'rgb(0,200,140)'
      
      const fontSize = 16
      const paddingX = 20

      const ctx = document.createElement('canvas').getContext('2d')
      ctx.font = `${fontSize}px sans-serif`
      const labelWidth = ctx.measureText(cfg.label).width

      const tags = cfg.tags || []
      let tagsWidth = 0
      const tagPadding = 16
      tags.forEach(tag => {
        tagsWidth += ctx.measureText(tag).width + tagPadding + 8
      })
      tagsWidth = Math.max(tagsWidth - 8, 0)

      // const contentWidth = Math.min(Math.max(labelWidth, tagsWidth),graph.get('width')*0.3)
      const contentWidth = graph.get('width')*0.25
      const nodeWidth = contentWidth + paddingX * 2
      const lineHeight = fontSize * 1.5;
      const paddingY = 10
      const maxTextWidth = nodeWidth - paddingX * 2;
       // 文本换行函数
      const wrapText = (text, maxWidth) => {
        const words = text.split('');
        const lines = [];
        let currentLine = '';
        
        for (let i = 0; i < words.length; i++) {
          const testLine = currentLine + words[i];
          const metrics = ctx.measureText(testLine);
          
          if (metrics.width > maxWidth && i > 0) {
            lines.push(currentLine);
            currentLine = words[i];
          } else {
            currentLine = testLine;
          }
        }
        
        if (currentLine !== '') {
          lines.push(currentLine);
        }
        
        return lines;
      };
    
      // 获取换行后的文本行
      const textLines = wrapText(cfg.label, maxTextWidth);
      
      // 动态计算节点高度（基于行数）
      const textHeight = textLines.length * lineHeight;
      const nodeHeight = textHeight + paddingY * 2;
      

      let fillColor = "#f5f5f5"; // 默认颜色
      if (cfg.level === 0) {
        fillColor = "#FF69A6"; // 根节点深粉色
      } else if (cfg.level === 1) {
        fillColor = "#1EA0FE"; // 章节节点蓝色
      } else if (cfg.level === 2) {
        fillColor = "#00C78C"; // 问题节点绿色
      }
      // 绘制节点矩形
      // 选中时的高亮描边（比节点大一些，留出空隙）
      const highlightPadding = 6; // 高亮描边与节点之间的间距
      const highlightShape = group.addShape("rect", {
      attrs: {
        x: -highlightPadding,
        y: -highlightPadding,
        width: nodeWidth + highlightPadding * 2,
        height: nodeHeight + highlightPadding * 2,
        radius: 16, // 与主节点圆角一致
        fill: "transparent",
        stroke: color, // 使用节点主色
        lineWidth: 3, // 适当加粗
        opacity: 0, // 默认完全透明
        shadowColor: '#ff8800', // 可选发光效果
        shadowBlur: 10
      },
      name: "highlight-border", // 明确命名
      zIndex: -1 // 确保在底层
    });

      const rect = group.addShape('rect', {
        attrs: {
          x: 0,
          y: 0,
          width: nodeWidth,
          height: nodeHeight,
          fill: color,
          radius: 16,
          shadowColor: '#ccc',
          shadowBlur: 6,
          cursor: 'pointer'
        }
      });
    
      // 绘制每行文本
      textLines.forEach((line, i) => {
        group.addShape('text', {
          attrs: {
            x: nodeWidth / 2,
            y: paddingY + i * lineHeight + 3,
            text: line,
            fill: '#fff',
            fontSize,
            textAlign: 'center',
            textBaseline: 'top'
          }
        });
      });

      if (tags.length) {
        let totalWidth = tagsWidth
        let startX = (nodeWidth - totalWidth) / 2
        const startY = nodeHeight + 10

        tags.forEach(tag => {
          const tagWidth = ctx.measureText(tag).width + tagPadding
          group.addShape('rect', {
            attrs: {
              x: startX,
              y: startY,
              width: tagWidth,
              height: 24,
              fill: '#f0f0f0',
              radius: 12
            }
          })
          group.addShape('text', {
            attrs: {
              x: startX + tagWidth / 2,
              y: startY + 12,
              text: tag,
              fill: '#666',
              fontSize: 12,
              textAlign: 'center',
              textBaseline: 'middle'
            }
          })
          startX += tagWidth + 8
        })
      }

      // if (cfg.children && cfg.children.length > 0) {
      //   group.addShape('marker', {
      //     attrs: {
      //       x: nodeWidth + 10,
      //       y: nodeHeight / 2,
      //       r: 6,
      //       symbol: cfg.collapsed ? G6.Marker.expand : G6.Marker.collapse,
      //       fill: '#fff',
      //       stroke: '#666',
      //       cursor: 'pointer'
      //     },
      //     name: 'collapse-icon'
      //   })
      // }

      return rect
    },
    getAnchorPoints() {
      return [
        [0, 0.5],
        [1, 0.5]
      ]
    },
    setState(name, value, item) {
  const group = item.getContainer();
  const highlightBorder = group.find(ele => ele.get('name') === 'highlight-border');
  
  // 只处理selected状态
  if (name === 'selected') {
    if (highlightBorder) {
      highlightBorder.attr('opacity', value ? 1 : 0);
    }
    return; // 重要：避免与其他状态处理冲突
  }
  
  // 其他状态处理
  group.get('children').forEach(shape => {
    if (name === 'highlight') {
      if (shape.attr('shadowColor') !== undefined) {
        shape.attr('shadowColor', value ? '#f88c77' : '#ccc');
        shape.attr('shadowBlur', value ? 20 : 6);
      }
    }
    if (name === 'dimmed') {
      if (shape.attr('fill') !== undefined || shape.attr('stroke') !== undefined) {
        shape.attr('opacity', value ? 0.3 : 1);
      }
    }
  });
}
  })

  // 注册自定义边
  G6.registerEdge('custom-edge', {
    draw(cfg, group) {
      const { startPoint, endPoint } = cfg
      const path = [
        ['M', startPoint.x, startPoint.y],
        [
          'C',
          (startPoint.x + endPoint.x) / 2, startPoint.y,
          (startPoint.x + endPoint.x) / 2, endPoint.y,
          endPoint.x, endPoint.y
        ]
      ]

      // 连线颜色与 source 节点颜色保持一致
      let color = 'RGB(85,85,85)'
      // if (cfg.sourceNode) {
      //   const sourceModel = cfg.sourceNode.getModel()
      //   if (sourceModel.level === 0 || sourceModel.branchKey === null) {
      //     color = '#24285e'
      //   } else {
      //     color = branchColorsArr[sourceModel.branchKey % branchColorsArr.length] || '#8a6de3'
      //   }
      // }

      return group.addShape('path', {
        attrs: {
          path,
          stroke: color,
          lineWidth: 3
        }
      })
    }
  })

  // 创建树图实例
  graph = new G6.TreeGraph({
    container: graphContainer.value,
    width: graphContainer.value.clientWidth,
    height: graphContainer.value.clientHeight,
    modes: {
      // default: ['drag-canvas', 'zoom-canvas']
      default: [
        {
          type: 'drag-canvas',
          direction: 'y', // 只允许垂直拖动
        },
      ],
    },
    defaultNode: {
      type: 'custom-node'
    },
    defaultEdge: {
      type: 'custom-edge'
    },
    nodeStateStyles: {
      highlight: {
        shadowColor: '#f88c77',
        shadowBlur: 20,
        lineWidth: 2
      },
      dimmed: {
        opacity: 0.3
      }
    },
    layout: {
      type: 'compactBox',
      direction: 'LR',
      getId: d => d.id,
      // getHeight: () => 50,
      // getWidth: () => 140,
      getVGap: () => 40,
      //getHGap: () => 120,
      getWidth: (d, cfg) => {
      const containerWidth = graph.get('width');
      return containerWidth * 0.313; // 节点宽度占容器20%
      },
      
      //getHGap: () => graph.get('width') * 0.05, // 水平间距占5%
    },

    
  })

  // 在节点点击事件中更新为：
graph.on('node:click', evt => {
  const item = evt.item;
  selectedNodeId.value = item.getModel().id;
  emit('node-click', item.getModel());
  
  // 清除所有节点状态
  graph.getNodes().forEach(node => {
    graph.clearItemStates(node, ['highlight', 'dimmed', 'selected']);
  });
  
  // 收集需要高亮的节点
  const highlightNodes = new Set();
  highlightNodes.add(item);
  
  // 添加父节点链
  let parent = item.get('parent');
  while (parent) {
    highlightNodes.add(parent);
    parent = parent.get('parent');
  }
  
  // 递归添加子节点
  const addChildren = (node) => {
    const children = node.get('children') || [];
    children.forEach(child => {
      highlightNodes.add(child);
      addChildren(child);
    });
  };
  addChildren(item);
  
  // 应用状态
  graph.getNodes().forEach(node => {
    if (highlightNodes.has(node)) {
      //graph.setItemState(node, 'highlight', true);
      //graph.setItemState(node, 'selected', false);
    } else {
      graph.setItemState(node, 'dimmed', true);
      graph.setItemState(node, 'selected', false);
    }
  });
  graph.setItemState(item, 'selected', true);
  });

  graph.on('canvas:click', () => {
    selectedNodeId.value = null;
    graph.getNodes().forEach(node => {
      graph.clearItemStates(node, ['highlight', 'dimmed', 'selected']);
    });
  });

  // 如果有初始数据，加载并渲染树图
  if (props.data) {
    const processedData = JSON.parse(JSON.stringify(props.data))
    collapseDeepNodes(processedData)
    assignBranchKey(processedData)
    graph.data(processedData)
    graph.render()
    // graph.fitCenter()
    // graph.zoom(0.9)
    // graph.fitCenter()
    graph.moveTo(graph.get('width')*0.02, 0)
  }
}

// 高亮树图中带有某个标签的节点
const highlightByTag = (tag) => {
  if (!graph) return

  // 先清除所有状态
  graph.getNodes().forEach(node => {
    graph.clearItemStates(node)
  })

  // 设置匹配节点高亮
  graph.getNodes().forEach(node => {
    const model = node.getModel()
    if (model.tags && model.tags.includes(tag)) {
      graph.setItemState(node, 'highlight', true)
    } else {
      graph.setItemState(node, 'dimmed', true)
    }
  })
}

// 对外暴露方法
defineExpose({ highlightByTag })
</script>

<style lang="scss" scoped>
.tree-graph-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 500px;
  //background: #f5f7fa;
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  position: relative;

  .course-chapter {
    font-size: 12px; /* 字号缩小（示例值，可调整） */
    color: #666; /* 颜色弱化（示例值，可调整） */
    font-weight: normal; /* 取消加粗（如果第一行是加粗的） */
    /* 其他自定义样式：如下划线、缩进等 */
    /* text-decoration: underline; */
    /* padding-left: 5px; */
  }

  .graph-header {
    display: flex;
    height: 50px;
    //background: #24285e;
    color: black;
    font-weight: bold;
    border-bottom: 1px solid #eee;
    position: sticky;
    top: 0;
    z-index: 10;

    .header-item {
      flex: 1;
      flex-direction: column;
      display: flex;
      align-items: flex-start;
      justify-content: center;
      padding:0 15px;
      padding-left:3%;
      font-size: 16px;
      
      // &.course {
      //   background: #24285e;
      // }
      // &.chapter {
      //   background: #3b50f9;
      // }
      // &.question {
      //   background: #8a6de3;
      // }
    }
  }

  .tree-graph-container {
    flex: 1;
    width: 100%;
    height: calc(100% - 50px); // 减去标题栏高度
    overflow: hidden;
  }
}
</style>