import request from '@/api/service';

// 获取课程通知列表（学生端消息）
export const getStudentMessages = (params) => {
    return request({
        url: '/course/notice/list',
        method: 'get',
        params: {
            pageNum: params.pageNum,   // 必传页码
            pageSize: params.pageSize, // 必传每页数量
            userId: params.userId,     // 学生 userId
            courseId: params.courseId, // 课程 ID，选填
            noticeType: params.noticeType, // 公告类型，选填
        }
    }).then(response => {
        if (response.code === 200 || response.code === 0) {
            return response.result.records || [];
        } else {
            throw new Error(response.msg || '获取学生消息列表失败');
        }
    }).catch(error => {
        console.error('请求错误:', error);
        throw error;
    });
};

// 标记消息为已读
export const markMessageAsRead = (noticeId, userId) => {
    return request({
        url: '/course/notice/notice/markAsRead',
        method: 'post',
        params: {
            noticeId, // 通知 ID
            userId    // 用户 ID
        }
    }).then(response => {
        if (response.code === 200 || response.code === 0) {
            return response.result;
        } else {
            throw new Error(response.msg || '标记消息为已读失败');
        }
    }).catch(error => {
        console.error('请求错误:', error);
        throw error;
    });
};

// 检查用户是否已读通知
export const checkReadStatus = (noticeId, userId) => {
    return request({
        url: '/course/notice/notice/checkReadStatus',
        method: 'get',
        params: {
            noticeId, // 通知 ID
            userId    // 用户 ID
        }
    }).then(response => {
        if (response.code === 200 || response.code === 0) {
            return response.result || false; // 返回是否已读（假设后端返回布尔值）
        } else {
            throw new Error(response.msg || '检查已读状态失败');
        }
    }).catch(error => {
        console.error('请求错误:', error);
        throw error;
    });
};

// 根据用户 ID 获取用户详情（对接 /user/get 接口）
export const getUserInfoById = (userId) => {
    return request({
        url: '/user/get',
        method: 'get',
        params: {
            id: userId
        }
    }).then(response => {
        if (response.code === 200 || response.code === 0) {
            return response.result || {}; // 返回用户详情
        } else {
            throw new Error(response.msg || '获取用户详情失败');
        }
    }).catch(error => {
        console.error('请求错误:', error);
        throw error;
    });
};