import { defineStore } from 'pinia';
import {
    getAbilityStats,
    countKnowledgeNodesByGraph,
    countKnowledgeNodesByAbility,
    getMainAbilities,
    getSubAbilitiesLite
} from '@/api/public/course/courseMap/abilityGraph';

// 模拟数据
const mockAbilityStats = {
    mainCount: 3,
    subCount: 9
};

const mockKnowledgeCount = 81;

export const useAbilityGraphStore = defineStore('abilityGraph', {
    state: () => ({
        // 能力图谱数据
        abilityStats: {
            mainCount: 0,
            subCount: 0
        },        // 能力统计信息（主能力+子能力数量）
        knowledgeCount: 0,         // 关联知识点总数
        isLoading: false,          // 加载状态
        error: null,               // 错误信息
        currentGraphId: null,      // 当前图谱ID
        currentAbilityId: null,    // 当前能力ID
        abilityData: null,         // 能力图谱数据
        abilityTree: null,         // 能力树结构
        currentNodeDetail: null,   // 当前节点详细信息
        useMockData: false,        // 是否使用模拟数据
        mainAbilities: [],         // 主能力列表
        subAbilities: [],          // 子能力列表
        selectedMainAbilityId: null, // 当前选中的主能力ID
    }),

    getters: {
        // 获取主能力数量
        mainAbilityCount: (state) => state.abilityStats.mainCount,

        // 获取子能力数量
        subAbilityCount: (state) => state.abilityStats.subCount,

        // 获取总能力数量
        totalAbilityCount: (state) => state.abilityStats.mainCount + state.abilityStats.subCount,

        // 获取知识点关联数量
        knowledgeNodeCount: (state) => {
            return state.knowledgeCount;
        },

        // 检查是否有数据
        hasData: (state) => {
            return state.abilityStats.mainCount > 0 || state.abilityStats.subCount > 0 || state.knowledgeCount > 0;
        },

        // 获取知识点覆盖率
        knowledgeCoverage: (state) => {
            if (state.totalAbilityCount === 0) return 0;
            return Math.round((state.knowledgeCount / state.totalAbilityCount) * 100);
        }
    },

    actions: {
        // 设置当前图谱ID
        setCurrentGraphId(graphId) {
            this.currentGraphId = graphId;
        },

        // 设置当前能力ID
        setCurrentAbilityId(abilityId) {
            this.currentAbilityId = abilityId;
        },

        // 清除数据
        clearData() {
            this.abilityStats = { mainCount: 0, subCount: 0 };
            this.knowledgeCount = 0;
            this.abilityData = null;
            this.abilityTree = null;
            this.currentNodeDetail = null;
            this.error = null;
            this.mainAbilities = [];
            this.subAbilities = [];
            this.selectedMainAbilityId = null;
        },

        // 使用模拟数据
        useMockData() {
            this.useMockData = true;
            this.abilityStats = mockAbilityStats;
            this.knowledgeCount = mockKnowledgeCount;
            this.error = null;
            console.log('使用模拟数据:', { abilityStats: this.abilityStats, knowledgeCount: this.knowledgeCount });
        },

        // 获取能力统计信息
        async fetchAbilityStats(graphId) {
            if (!graphId) {
                this.error = '图谱ID不能为空';
                return;
            }

            this.isLoading = true;
            this.error = null;
            
            try {
                const response = await getAbilityStats(graphId);
                if (response.code === 200) {
                    this.abilityStats = response.result;
                    this.currentGraphId = graphId;
                    return response.result;
                } else {
                    this.error = response.msg || '获取能力统计失败';
                    return null;
                }
            } catch (error) {
                if (error.response?.status === 401) {
                    this.error = '登录已过期，请重新登录';
                } else if (error.response?.status === 404) {
                    // 接口不存在，使用模拟数据
                    console.warn('能力图谱接口未实现，使用模拟数据');
                    this.useMockData();
                    return mockAbilityStats;
                } else {
                    this.error = '网络请求失败，请稍后再试';
                }
                console.error('获取能力统计失败:', error);
                return null;
            } finally {
                this.isLoading = false;
            }
        },

        // 获取知识点关联数量（按图谱ID）
        async fetchKnowledgeCount(graphId) {
            if (!graphId) {
                this.error = '图谱ID不能为空';
                return;
            }

            this.isLoading = true;
            this.error = null;
            
            try {
                const response = await countKnowledgeNodesByGraph(graphId);
                if (response.code === 200) {
                    this.knowledgeCount = response.result;
                    return response.result;
                } else {
                    this.error = response.msg || '获取知识点数量失败';
                    return null;
                }
            } catch (error) {
                if (error.response?.status === 401) {
                    this.error = '登录已过期，请重新登录';
                } else if (error.response?.status === 404) {
                    // 接口不存在，使用模拟数据
                    console.warn('能力图谱接口未实现，使用模拟数据');
                    this.knowledgeCount = mockKnowledgeCount;
                    return mockKnowledgeCount;
                } else {
                    this.error = '网络请求失败，请稍后再试';
                }
                console.error('获取知识点数量失败:', error);
                return null;
            } finally {
                this.isLoading = false;
            }
        },

        // 获取知识点关联数量（按能力ID）
        async fetchKnowledgeCountByAbility(abilityId) {
            this.isLoading = true;
            this.error = null;
            
            try {
                const response = await countKnowledgeNodesByAbility(abilityId);
                if (response.code === 200) {
                    this.knowledgeCount = response.result;
                    this.currentAbilityId = abilityId;
                    return response.result;
                } else {
                    this.error = response.msg || '获取知识点数量失败';
                    return null;
                }
            } catch (error) {
                if (error.response?.status === 401) {
                    this.error = '登录已过期，请重新登录';
                } else if (error.response?.status === 404) {
                    // 接口不存在，使用模拟数据
                    console.warn('能力图谱接口未实现，使用模拟数据');
                    this.knowledgeCount = mockKnowledgeCount;
                    return mockKnowledgeCount;
                } else {
                    this.error = '网络请求失败，请稍后再试';
                }
                console.error('获取知识点数量失败:', error);
                return null;
            } finally {
                this.isLoading = false;
            }
        },

        // 获取图谱完整数据（统计信息 + 知识点数量）
        async fetchGraphCompleteData(graphId) {
            this.isLoading = true;
            this.error = null;
            
            try {
                // 并行获取两个数据
                const [statsResponse, countResponse] = await Promise.all([
                    getAbilityStats(graphId),
                    countKnowledgeNodesByGraph(graphId)
                ]);

                let hasError = false;

                // 处理能力统计数据
                if (statsResponse.code === 200) {
                    this.abilityStats = statsResponse.result;
                } else {
                    this.error = statsResponse.msg || '获取能力统计失败';
                    hasError = true;
                }

                // 处理知识点数量数据
                if (countResponse.code === 200) {
                    this.knowledgeCount = countResponse.result;
                } else {
                    this.error = countResponse.msg || '获取知识点数量失败';
                    hasError = true;
                }

                this.currentGraphId = graphId;
                
                if (!hasError) {
                    return {
                        abilityStats: this.abilityStats,
                        knowledgeCount: this.knowledgeCount
                    };
                } else {
                    return null;
                }
            } catch (error) {
                if (error.response?.status === 401) {
                    this.error = '登录已过期，请重新登录';
                } else if (error.response?.status === 404) {
                    // 接口不存在，使用模拟数据
                    console.warn('能力图谱接口未实现，使用模拟数据');
                    this.abilityStats = mockAbilityStats;
                    this.knowledgeCount = mockKnowledgeCount;
                    this.currentGraphId = graphId;
                    return {
                        abilityStats: this.abilityStats,
                        knowledgeCount: this.knowledgeCount
                    };
                } else {
                    this.error = '网络请求失败，请稍后再试';
                }
                console.error('获取图谱完整数据失败:', error);
                return null;
            } finally {
                this.isLoading = false;
            }
        },

        // 刷新当前图谱数据
        async refreshCurrentGraphData() {
            if (!this.currentGraphId) {
                this.error = '未设置当前图谱ID';
                return null;
            }
            return await this.fetchGraphCompleteData(this.currentGraphId);
        },

        // 设置错误信息
        setError(error) {
            this.error = error;
        },

        // 清除错误信息
        clearError() {
            this.error = null;
        },

        // 获取主能力列表
        async fetchMainAbilities(graphId) {
            if (!graphId) {
                this.error = '图谱ID不能为空';
                return;
            }

            try {
                const response = await getMainAbilities(graphId);
                if (response.code === 200) {
                    this.mainAbilities = response.result || response.data;
                    console.log('获取到主能力列表:', this.mainAbilities);
                } else {
                    this.error = response.msg || response.message || '获取主能力列表失败';
                }
            } catch (error) {
                if (error.response?.status === 401) {
                    this.error = '登录已过期，请重新登录';
                } else if (error.response?.status === 404) {
                    // 接口不存在，使用模拟数据
                    console.warn('主能力列表接口未实现，使用模拟数据');
                    this.mainAbilities = [
                        { abilityId: '1', nodeName: '创新能力', nodeDesc: '创新能力的描述' },
                        { abilityId: '2', nodeName: '批判思维', nodeDesc: '批判思维的描述' },
                        { abilityId: '3', nodeName: '系统思维', nodeDesc: '系统思维的描述' }
                    ];
                } else {
                    this.error = '网络请求失败，请稍后再试';
                }
                console.error('获取主能力列表失败:', error);
            }
        },

        // 获取所有数据
        async fetchAllData(graphId) {
            if (!graphId) {
                this.error = '图谱ID不能为空';
                return;
            }

            this.isLoading = true;
            this.error = null;

            try {
                console.log('开始获取所有数据，graphId:', graphId);
                // 并行获取所有数据
                await Promise.all([
                    this.fetchAbilityStats(graphId),
                    this.fetchKnowledgeCount(graphId),
                    this.fetchMainAbilities(graphId)
                ]);
                console.log('所有数据获取完成');
            } catch (error) {
                console.error('获取数据失败:', error);
                this.error = error.message || '获取数据失败';
            } finally {
                this.isLoading = false;
            }
        },

        // 获取子能力列表
        async fetchSubAbilities(parentId) {
            if (!parentId) {
                this.error = '主能力ID不能为空';
                return;
            }

            try {
                const response = await getSubAbilitiesLite(parentId);
                if (response.code === 200) {
                    this.subAbilities = response.result || response.data;
                    this.selectedMainAbilityId = parentId;
                    console.log('获取到子能力列表:', this.subAbilities);
                } else {
                    this.error = response.msg || response.message || '获取子能力列表失败';
                }
            } catch (error) {
                if (error.response?.status === 401) {
                    this.error = '登录已过期，请重新登录';
                } else if (error.response?.status === 404) {
                    // 接口不存在，使用模拟数据
                    console.warn('子能力列表接口未实现，使用模拟数据');
                    this.subAbilities = [
                        { abilityId: 'sub1', nodeName: '子能力1', nodeDesc: '子能力1的描述' },
                        { abilityId: 'sub2', nodeName: '子能力2', nodeDesc: '子能力2的描述' },
                        { abilityId: 'sub3', nodeName: '子能力3', nodeDesc: '子能力3的描述' }
                    ];
                    this.selectedMainAbilityId = parentId;
                } else {
                    this.error = '网络请求失败，请稍后再试';
                }
                console.error('获取子能力列表失败:', error);
            }
        },

        // 设置选中的主能力
        setSelectedMainAbility(abilityId) {
            this.selectedMainAbilityId = abilityId;
        },

        // 重置状态
        reset() {
            this.abilityStats = { mainCount: 0, subCount: 0 };
            this.knowledgeCount = 0;
            this.mainAbilities = [];
            this.subAbilities = [];
            this.selectedMainAbilityId = null;
            this.isLoading = false;
            this.error = null;
        }
    }
}); 