<template>
  <div class="export-management-container">
    <!-- 头部区域 -->
    <div class="header">
      <el-button
        type="text"
        icon="el-icon-arrow-left"
        @click="goBack"
      >返回教学课堂</el-button>
      <div class="title">导出管理</div>
      <!-- 用户信息，这里简单模拟 -->
      <div class="user-info">
        <span>测试</span>
        <el-dropdown>
          <i class="el-icon-arrow-down el-icon--right"></i>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>个人中心</el-dropdown-item>
              <el-dropdown-item>退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 说明文本 -->
    <div class="description">
      <p class="warning-text">
        “文件生成中” 请耐心等待，预计 1 天内生成完成。生成后的文件将存于管理控制台的报表上，请及时下载。
      </p>
    </div>

    <!-- 表格区域 -->
    <el-table
      :data="tableData"
      border
      style="width: 100%"
    >
      <el-table-column
        prop="fileName"
        label="文件名称"
      />
      <el-table-column
        prop="createTime"
        label="创建时间"
      />
      <el-table-column
        prop="status"
        label="状态"
      />
      <el-table-column
        label="操作"
      >
        <template #default="scope">
          <el-button
            type="text"
            @click="downloadFile(scope.row)"
          >下载</el-button>
          <el-button
            type="text"
            @click="deleteFile(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ElButton, ElDropdown, ElDropdownMenu, ElDropdownItem, ElTable, ElTableColumn } from 'element-plus'
import { onMounted, ref } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'

// 模拟表格数据
const tableData = ref([
  {
    fileName: '《数字媒体技术1》作业详情、考试详情、学习资源详情数据导出.zip',
    createTime: '2025-06-30 11:04:55',
    status: '文件生成完成'
  },
  {
    fileName: '《数字媒体技术1》课程运行数据、成绩详情数据导出.zip',
    createTime: '2025-06-30 11:05:51',
    status: '文件生成完成'
  },
  {
    fileName: '《数字媒体技术1》课程运行数据详情导出.zip',
    createTime: '2025-06-30 11:00:12',
    status: '文件生成完成'
  }
])

// 返回按钮逻辑（可根据路由实际调整）
const goBack = () => {
  // 这里可写路由跳转，比如：router.push('/teaching-class')
  console.log('返回教学课堂')
}

// 下载逻辑（模拟）
const downloadFile = (row) => {
  console.log('下载文件：', row.fileName)
  // 实际可调用下载接口，如 window.open(downloadUrl)
}

// 删除逻辑（模拟，增加确认弹窗）
const deleteFile = (row) => {
  ElMessageBox.confirm(
    '确定要删除该下载任务吗？',
    '提示',
    {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning',
      customClass: 'delete-confirm-box' 
    }
  )
  .then(() => {
    // 用户点击“删除”后的逻辑
    console.log('确认删除文件：', row.fileName)
    // 这里可调用实际的删除接口，比如删除成功后从 tableData 中移除该条数据
    // 示例：tableData.value = tableData.value.filter(item => item.fileName !== row.fileName)
    ElMessage.success('删除成功')
  })
  .catch(() => {
    // 用户点击“取消”后的逻辑
    ElMessage.info('已取消删除')
  })
}
</script>

<style scoped lang="scss">
.export-management-container {
  width: 95%;
  margin: 100px auto;
  min-height: 700px;
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-info span {
  margin-right: 10px;
}

.description {
  margin-bottom: 20px;
  color: #666;
  line-height: 1.6;
}

.warning-text {
  color: #f00;
  margin-top: 5px;
}

.footer {
  text-align: center;
  margin-top: 20px;
  color: #999;
  font-size: 12px;
}

/* 自定义删除确认弹窗样式 */
::v-deep .delete-confirm-box {
  .el-message-box__content {
    text-align: center;
    padding-top: 30px;
  }
  .el-message-box__message {
    font-size: 14px;
    color: #666;
    margin-bottom: 20px;
  }
  .el-message-box__btns {
    justify-content: center;
  }
  .el-button--primary {
    background-color: #409eff;
    border-color: #409eff;
  }
  .el-button--default {
    border-color: #dcdcdc;
    color: #666;
  }
}
</style>