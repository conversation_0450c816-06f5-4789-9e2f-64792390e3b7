<template>
  <div class="container">
    <CourseInfoTop v-model:course="courseData" @refresh="handleRefresh" />
    <div class="detail-container">
      <Stat :course="courseData" />
      <CourseOverview :course="courseData" />
    </div>
  </div>
</template>


<script setup>
import { ref, onMounted, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import { getCourseDetail } from '@/api/teacher/course';
import CourseInfoTop from './components/CourseInfoTop.vue';
import Stat from './components/Stat.vue';
import CourseOverview from './components/CourseOverview.vue';

const route = useRoute();
const courseData = ref({});
const refreshKey = ref(0); // 用于强制刷新组件

const fetchCourseDetail = async () => {
  try {
    const response = await getCourseDetail(route.params.courseId);
    
    const mainTeacher = response.teachers.find(t => t.teamRole === 1);
    
    courseData.value = {
      id: response.course.id,
      ...response.course,
      teacher: mainTeacher?.teacherName || '--',
      score: response.course.credits,
      time: response.course.hours,
      school: mainTeacher?.institution || '--',
      department: mainTeacher?.department || '--',
      major: response.course.major || '--',
      term: response.course.semester,
      cover: response.course.courseCover
    };
    
    console.log('课程详情:', courseData.value);
  } catch (error) {
    console.error('获取课程详情失败:', error);
  }
};

const handleRefresh = () => {
  console.log('触发刷新事件，重新获取课程数据');
  fetchCourseDetail();
};

onMounted(() => {
  fetchCourseDetail();
});
</script>

<style lang="scss" scoped>
.container {
  background-image: url('@/assets/img/Teacher/background-small.png');
  background-size: 100% auto; 
  background-position: center top;
  background-repeat: no-repeat;
  .detail-container{
    background-color: white;
    border-radius: 0.5vw;
    width: 90vw;
    margin: -2vw auto;
  }
}
</style>