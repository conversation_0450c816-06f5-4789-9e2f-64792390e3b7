<template>
  <div class="online-interaction">
    <div class="card" v-for="(item, index) in data" :key="index" :style="{ backgroundColor: item.color }">
      <div class="title">{{ item.title }}</div>
      <div class="value">{{ item.value }}</div>
      <div class="icon">{{ item.icon }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OnlineInteraction',
  data() {
    return {
      data: [
        { title: '话题总数', value: '746,701', icon: '📊', color: '#d9f0ff' },
        { title: '话题参与总人数', value: '28,442', icon: '👥', color: '#e0f7da' },
        { title: '话题参与总次数', value: '154', icon: '🔄', color: '#fce4ec' },
        { title: '对话回复数', value: '692,606', icon: '⭐', color: '#fff1f0' }
      ]
    };
  }
};
</script>

<style scoped>
.online-interaction {
  display: flex;
  justify-content: space-between;
  padding: 20px;
}

.card {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 10px;
  text-align: center;
  width: 20%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: background-color 0.3s;
}

.card:hover {
  background-color: #e0e0e0;
}

.title {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.value {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.icon {
  font-size: 18px;
  color: #999;
}
</style>