<template>
  <div class="selftest-container">
    <!-- 错误提示 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loader"></div>
      <p>{{ loadingMessage }}</p>
    </div>

    <!-- 文件上传区域 -->
    <div class="upload-section" v-if="!analyzedQuestions.length">
      <h2>计算机习题自测</h2>
      <p>请按照以下格式上传习题的文档仅限doc/docx</p>
      <div class="f">
     <img src="../../assets/photo/exampletest.png">
        </div>
      <div class="upload-area" @dragover.prevent @drop.prevent @drop="handleDrop">
        <input
          type="file"
          @change="handleFileChange"
          accept=".doc,.docx"
          ref="fileInput"
          style="display: none"
        />
        <div @click="triggerFileUpload">
          <p>拖放文件到此处或点击选择文件</p>
          <p class="file-name" v-if="fileName">{{ fileName }}</p>
        </div>
      </div>
    </div>

    <!-- 题目分析结果展示 -->
    <div class="analysis-results" v-if="analyzedQuestions.length">
      <h2>题目分析结果</h2>

      <!-- 滚动区域 -->
      <div class="scrollable-results">
        <div v-for="(question, index) in analyzedQuestions" :key="index" class="question-analysis">
          <div class="question-header">
            <h3>第 {{ index + 1 }} 题 ({{ question.type }})</h3>
            <div :class="['result-tag', question.isCorrect ? 'correct' : 'incorrect']">
              {{ question.isCorrect ? '分析完成' : '错误' }}
            </div>
          </div>

          <div class="question-content">
            <h4>题目:</h4>
            <p>{{ question.question }}</p>

            <div v-if="question.options" class="choice-options">
              <div
                v-for="(option, optIndex) in question.options.split('、')"
                :key="optIndex"
                class="option-item"
                :class="{ 'selected': option.trim().startsWith(question.answer) }"
              >
                {{ option }}
              </div>
            </div>

            <div v-if="question.type === '填空题'">
              <h4>答案:</h4>
              <p>{{ question.answer }}</p>
            </div>

            <div v-if="question.type === '简答题'">
              <h4>参考答案:</h4>
              <p>{{ question.answer }}</p>
            </div>
          </div>

          <div class="analysis-content">
            <h4>分析:</h4>
            <p>{{ question.analysis }}</p>
          </div>
        </div>
      </div>

      <div class="summary-section">
        <h3>分析完成情况</h3>
        <p>总题数: {{ analyzedQuestions.length }}</p>
<!--        <p>正确题数: {{ correctQuestions }} <span class="correct-tag">正确</span></p>-->
<!--        <p>错误题数: {{ incorrectQuestions }} <span class="incorrect-tag">错误</span></p>-->
        <button @click="restartTest" class="restart-btn">重新分析</button>
      </div>
    </div>

<!--    <div class="empty-state" v-if="!analyzedQuestions.length && !isLoading && !errorMessage">-->
<!--      <p>请上传包含习题的文档文件</p>-->
<!--    </div>-->
  </div>
</template>

<script>
import { extractQuestionsFromDoc, analyzeAllExercises } from '../../../../api/student/intelligent/selftest.js';

export default {
  data() {
    return {
      fileName: '',
      file: null,
      analyzedQuestions: [],
      isLoading: false,
      errorMessage: '',
      loadingMessage: '正在处理文档...'
    };
  },
  computed: {
    correctQuestions() {
      return this.analyzedQuestions.filter(q => q.isCorrect).length;
    },
    incorrectQuestions() {
      return this.analyzedQuestions.filter(q => !q.isCorrect).length;
    }
  },
  methods: {
    triggerFileUpload() {
      this.$refs.fileInput.click();
    },

    handleFileChange(event) {
      if (event.target.files.length > 0) {
        this.file = event.target.files[0];
        this.fileName = this.file.name;
        this.processDocument();
      }
    },

    handleDrop(event) {
      if (event.dataTransfer.files.length > 0) {
        const file = event.dataTransfer.files[0];
        if (file.name.endsWith('.docx') || file.name.endsWith('.doc')) {
          this.file = file;
          this.fileName = file.name;
          this.processDocument();
        } else {
          this.errorMessage = '只支持 .doc 或 .docx 文件';
        }
      }
    },

    async processDocument() {
      this.isLoading = true;
      this.errorMessage = '';

      try {
        // 步骤1: 提取题目
        this.loadingMessage = '正在解析文档...';
        const questions = await extractQuestionsFromDoc(this.file);

        if (questions.length === 0) {
          throw new Error('未识别到有效习题，请检查文档格式');
        }

        // 步骤2: 分析所有题目
        this.loadingMessage = '正在分析题目...';
        this.analyzedQuestions = await analyzeAllExercises(questions);

      } catch (error) {
        this.errorMessage = error.message || '文档处理失败';
      } finally {
        this.isLoading = false;
      }
    },

    restartTest() {
      this.analyzedQuestions = [];
      this.fileName = '';
      this.file = null;
      this.errorMessage = '';
    }
  }
};
</script>

<style scoped>
.selftest-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 10px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.error-message {
  padding: 10px;
  background-color: #ffebee;
  color: #c62828;
  border-radius: 4px;
  margin-bottom: 15px;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loader {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #956ef6;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.upload-section {
  margin-bottom: 30px;
  text-align: center;
}

.upload-area {
  border: 2px dashed #ccc;
  padding: 15px;
  border-radius: 8px;
  cursor: pointer;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #956ef6;
}

.upload-area input {
  display: none;
}

.file-name {
  margin-top: 10px;
  color: #666;
}

.scrollable-results {
  max-height: 55vh; /* 设置固定高度 */
  overflow-y: auto; /* 垂直滚动 */
  border: 1px solid #eee;
  border-radius: 8px;
}

.question-analysis {
  background-color: #f9f9f9;
  padding: 10px;
  border-radius: 8px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.result-tag {
  padding: 5px 15px;
  border-radius: 20px;
  font-size: 0.9em;
  font-weight: bold;
}

.correct {
  background-color: #4CAF50;
  color: white;
}

.incorrect {
  background-color: #F44336;
  color: white;
}

.question-content {
  margin-bottom: 15px;
}

.question-content h4 {
  margin-bottom: 8px;
  color: #333;
  font-weight: 600;
}

.choice-options {
  margin-top: 10px;
}

.option-item {
  padding: 10px 15px;
  margin: 8px 0;
  background-color: #f0f0f0;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.option-item.selected {
  background-color: #d6e4ff;
  border: 2px solid #4a7dff;
  font-weight: bold;
}

.analysis-content {
  background-color: #f0f7ff;
  padding: 15px;
  border-radius: 6px;
  border-left: 3px solid #4a7dff;
}

.analysis-content h4 {
  margin-bottom: 8px;
  color: #333;
  font-weight: 600;
}

.analysis-content p {
  white-space: pre-line;
  line-height: 1.6;
}

.summary-section {
  text-align: center;
  padding: 5px;
  background-color: #f5f5f5;
  border-radius: 8px;
  margin-top: 10px;
}

.summary-section p {
  margin: 2px 0;
  font-size: 0.6em;
}

.correct-tag {
  display: inline-block;
  padding: 2px 6px;
  background-color: #4CAF50;
  color: white;
  border-radius: 10px;
  font-size: 0.6em;
}

.incorrect-tag {
  display: inline-block;
  padding: 2px 6px;
  background-color: #F44336;
  color: white;
  border-radius: 10px;
  font-size: 0.6em;
}

.restart-btn {
  background-color: #2196F3;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.6em;
  margin-top: 5px;
  transition: background-color 0.3s;
}

.restart-btn:hover {
  background-color: #0b7dda;
}

.empty-state {
  text-align: center;
  color: #666;
}

.f {
  border: 2px;
  border-radius: 8px;
  transition: border-color 0.3s;
}
</style>
