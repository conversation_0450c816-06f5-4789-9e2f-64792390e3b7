<template>
    <div class="knowledge-graph-page">
        <Topbar @goBack="goBack" @insertChild="insertChild" @deleteNode="deleteNode" :disabledInsert="!selectedNodeId"
            :disabledDelete="!selectedNodeId || selectedNodeId === 'root' || selectedNodeId === store.rootNodeId"
            :selectedNodeId="selectedNodeId" v-model:showDetail="showDetail" />
        <Statsbar :stats="stats" />
        <div class="main-area">
            <div ref="graphContainer" class="graph-container"></div>
            <SideToolbar />
        </div>
        <NodeDialog :visible="showDialog" @confirm="handleDialogConfirm" @cancel="showDialog = false" />
        <KnowledgeDetailDrawer :isOpen="showDetail" :currentNode="getCurrentNode()" :stats="stats"
            :editableNode="editableNode" @update-node="updateNodeData" />
    </div>
</template>

<script setup>
import { ref, onMounted, provide, watch, onBeforeUnmount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { onBeforeRouteLeave } from 'vue-router'
import G6 from '@antv/g6'
import Topbar from './components/Topbar.vue'
import Statsbar from './components/Statsbar.vue'
import SideToolbar from './components/SideToolbar.vue'
import NodeDialog from './components/NodeDialog.vue'
import KnowledgeDetailDrawer from './components/KnowledgeDetailDrawer.vue'

import { useKnowledgeGraphStore } from '@/stores/teacher/graphManager/knowledgeGraphStore'

import { nextTick } from 'vue';

// 初始化store
const store = useKnowledgeGraphStore();
const emit = defineEmits(['update:currentNode', 'update-current-node'])

const route = useRoute()
const router = useRouter()
const showDetail = ref(false)

// 右侧抽屉栏可编辑变量
const editableNode = ref({
    nodeId: '',
    nodeName: '',
    nodeDesc: '',
    tags: []
})

//左上角统计栏
const stats = ref({
    knowledges: 0,
    resources: 0,
})

const selectedNodeId = ref(null)
const graphContainer = ref(null)
const graphRef = ref(null)
const graphData = ref(null)
const showDialog = ref(false)
const pendingParentId = ref(null)
let resizeObserver = null

//返回
const goBack = () => {
    router.push({
        name: 'GraphManager',
        params: {
            courseId: route.params.courseId,
            courseName: route.params.courseName || '默认课程'
        }
    })
}

// 路由离开守卫
onBeforeRouteLeave(() => {
    return true; // 明确允许导航
});

// 更新图表和状态
const updateGraphAndState = () => {
    if (graphRef.value) {
        const matrix = graphRef.value.getGroup().getMatrix();
        graphRef.value.changeData(graphData.value);
        if (matrix) graphRef.value.getGroup().setMatrix(matrix);

        // 确保选中状态清除
        selectedNodeId.value = null;
        graphRef.value.getNodes().forEach(node => {
            graphRef.value.setItemState(node, 'selected', false);
        });

        // 强制更新视图
        graphRef.value.refresh();
    }
};

// 新建子节点
const insertChild = () => {
    if (!selectedNodeId.value) return
    pendingParentId.value = selectedNodeId.value
    showDialog.value = true
}

// 处理新节点确认
const handleDialogConfirm = async ({ name, description }) => {
    if (!selectedNodeId.value) return;

    try {
        const result = await store.createNodeAction({
            graphId: route.params.graphId,
            parentId: selectedNodeId.value,
            name,
            description
        });

        if (result.success) {
            await store.fetchGraphTree(route.params.graphId);

            const treeData = store.currentNodeTree;
            let newGraphData = null;
            if (treeData && treeData.length > 0) {
                const node = treeData[0];
                newGraphData = {
                    id: node.nodeId || 'defaultId',
                    label: node.nodeName || '默认节点名',
                    children: convertChildren(node.children)
                };
                graphData.value = newGraphData;
            }

            selectedNodeId.value = result.node.id;

            // 更新统计信息
            const count = await store.countAllNodes(route.params.graphId);
            stats.value.knowledges = count;

            updateGraphAndState();
        } else {
            alert(`创建节点失败: ${result.error || '未知错误'}`);
        }
    } catch (error) {
        console.error('创建节点出错:', error);
        alert('创建节点时出错，请重试');
    } finally {
        showDialog.value = false;
    }
};

// 删除节点
const deleteNode = async () => {
    const idToDelete = selectedNodeId.value;
    // 确保不是根节点
    if (!idToDelete || idToDelete === 'root' || idToDelete === store.rootNodeId) {
        alert('根节点不能被删除');
        return;
    }

    const confirmed = window.confirm('确定删除该节点及其全部子节点？');
    if (!confirmed) return;

    try {
        // 执行删除操作
        const success = await store.deleteNodeAction(idToDelete);
        if (!success) throw new Error('删除操作失败');

        // 获取删除后的树结构
        const updatedTree = await store.fetchGraphTree(route.params.graphId);

        // 记录删除后的完整状态
        const afterState = {
            graphData: JSON.parse(JSON.stringify({
                id: updatedTree[0].nodeId,
                label: updatedTree[0].nodeName,
                children: convertChildren(updatedTree[0].children)
            })),
            stats: {
                knowledges: await store.countAllNodes(route.params.graphId)
            }
        };

        // 更新当前视图
        graphData.value = afterState.graphData;
        stats.value = afterState.stats;
        selectedNodeId.value = null;

        // 更新图表
        updateGraphAndState();
    } catch (error) {
        console.error('删除节点出错:', error);
        alert('删除节点失败: ' + error.message);
    }
};

// 辅助函数：计算被删除的节点数量（包括子节点）
const countDescendants = (node) => {
    if (!node || !node.children || node.children.length === 0) return 0;
    return node.children.reduce((acc, child) => acc + 1 + countDescendants(child), 0);
};

// 获取当前节点
const getCurrentNode = async () => {
    if (!selectedNodeId.value) return null;

    // 如果是根节点（根据 store.rootNodeId 或硬编码 'root'）
    if (selectedNodeId.value === 'root' || selectedNodeId.value === store.rootNodeId) {
        return {
            id: store.rootNodeId || 'root',
            label: graphData.value?.label || '根节点',
            description: '',
            tags: []
        };
    }

    // 其他节点从接口获取
    const nodeDetail = await store.fetchNodeDetail(selectedNodeId.value);
    return nodeDetail ? {
        id: nodeDetail.nodeId,
        label: nodeDetail.nodeName,
        description: nodeDetail.nodeDesc,
        tags: nodeDetail.tags || []
    } : null;
}

// 更新节点数据
const updateNodeData = (updates) => {
    const node = findNodeById(graphData.value, selectedNodeId.value)
    if (!node || node.id === 'root') return

    Object.assign(node, updates)
    updateGraphAndState()
}

// 监听 selectedNodeId 变化
watch(selectedNodeId, async (newId) => {
    if (newId) {
        const node = await getCurrentNode();
        console.log("当前节点更新:", node);
        if (node && node !== 'root') {
            editableNode.value = {
                nodeId: node.id,
                nodeName: node.label || '',
                nodeDesc: node.description || '',
                tags: [...(node.tags || [])]
            };
        }
    }
});

// 查找节点
function findNodeById(node, id) {
    if (node.id === id) return node
    if (!node.children) return null
    for (const child of node.children) {
        const result = findNodeById(child, id)
        if (result) return result
    }
    return null
}

provide('graphRef', graphRef)
provide('graphData', graphData)

//初始化
onMounted(async () => {
    const route = useRoute();
    const graphId = route.params.graphId;
    const courseId = route.params.courseId;

    // 设置courseId到store中
    if (courseId) {
        store.courseId = courseId;
    }

    try {
        if (graphId) {
            const result = await store.fetchGraphTree(graphId);
            console.log('fetchGraphTree 返回结果:', result);
            if (store.currentNodeTree && store.currentNodeTree.length > 0) {
                const node = store.currentNodeTree[0];
                const convertedData = {
                    id: node.nodeId || 'defaultId',
                    label: node.nodeName || '默认节点名',
                    description: node.nodeDesc || '',
                    tags: node.tags ? node.tags.map(tag => tag.tagName) : [],
                    children: convertChildren(node.children)
                };
                graphData.value = convertedData;
                if (store.rootNodeId) {
                    selectedNodeId.value = store.rootNodeId;
                    const count = await store.countAllNodes(route.params.graphId);
                    stats.value.knowledges = count;
                }
            }
        } else {
            graphData.value = createDefaultGraphData(route.params.courseName || '默认课程');
            selectedNodeId.value = 'root';
            stats.value.knowledges = 0;
        }

        await nextTick();

        // 初始化图表
        initGraph();
    } catch (error) {
        console.error('加载知识图谱数据失败:', error);
        graphData.value = createDefaultGraphData(route.params.courseName || '默认课程');
        selectedNodeId.value = 'root';
        stats.value.knowledges = 0;
        await nextTick();
        initGraph();
    }
});

// 递归转换子节点
function convertChildren(children) {
    if (!children || children.length === 0) {
        return [];
    }
    return children.map(child => ({
        id: child.nodeId || 'defaultId',
        label: child.nodeName || '默认节点名',
        description: child.nodeDesc || '',
        tags: child.tags ? child.tags.map(tag => typeof tag === 'object' ? tag.tagName : tag) : [],
        children: convertChildren(child.children)
    }));
}

// 图表初始化
function initGraph() {
    G6.registerNode('custom-node', {
        draw(cfg, group) {
            const isRoot = cfg.id === store.rootNodeId;
            const isSelected = cfg.id === selectedNodeId.value

            const ctx = document.createElement('canvas').getContext('2d')
            ctx.font = '14px sans-serif'
            const labelWidth = ctx.measureText(cfg.label).width

            const tags = cfg.tags || []
            let tagsWidth = 0
            const tagPadding = 6
            const tagFontSize = 12
            ctx.font = `${tagFontSize}px sans-serif`
            tags.forEach(tag => {
                tagsWidth += ctx.measureText(tag).width + tagPadding * 2
            })
            tagsWidth = Math.max(tagsWidth, 0)

            const contentWidth = Math.max(labelWidth, tagsWidth)
            const minWidth = 100
            const nodeWidth = Math.max(contentWidth + 20, minWidth)
            const nodeHeight = 50

            group.addShape('rect', {
                attrs: {
                    x: 0,
                    y: -25,
                    width: nodeWidth,
                    height: nodeHeight,
                    radius: 10,
                    fill: '#4c7bff',
                    opacity: isSelected ? 0.15 : 0,
                },
                name: 'highlight-shape',
                zIndex: -1,
            })
            group.sort()

            const rect = group.addShape('rect', {
                attrs: {
                    x: 5,
                    y: -20,
                    width: nodeWidth - 10,
                    height: 40,
                    radius: 8,
                    fill: isRoot ? '#4c7bff' : '#f5f5f5',
                    stroke: isSelected ? '#4c7bff' : '#999',
                    lineWidth: 1.5,
                },
                name: 'rect-shape',
            })

            group.addShape('text', {
                attrs: {
                    text: cfg.label,
                    fill: isRoot ? '#ffffff' : '#000000',
                    fontSize: 14,
                    textAlign: 'center',
                    textBaseline: 'middle',
                    x: nodeWidth / 2,
                    y: 0,
                    fontWeight: isRoot ? 'normal' : 'bold',
                },
                name: 'text-shape',
            })

            if (tags.length) {
                let startX = 10
                const startY = 18

                const tagColors = [
                    '#4c7bff', '#5a91f2', '#678de0', '#6ca0ff',
                    '#7caeff', '#90bbff', '#a3c8ff', '#b8d4ff'
                ]

                tags.forEach((tag, index) => {
                    const color = tagColors[index % tagColors.length]
                    const tagWidth = ctx.measureText(tag).width + tagPadding * 2

                    group.addShape('rect', {
                        attrs: {
                            x: startX,
                            y: startY,
                            width: tagWidth,
                            height: 20,
                            fill: color,
                            radius: 10,
                        },
                        name: `tag-bg-${index}`,
                    })

                    group.addShape('text', {
                        attrs: {
                            x: startX + tagWidth / 2,
                            y: startY + 10,
                            text: tag,
                            fill: '#fff',
                            fontSize: tagFontSize,
                            textAlign: 'center',
                            textBaseline: 'middle',
                        },
                        name: `tag-text-${index}`,
                    })

                    startX += tagWidth + 6
                })
            }

            if (cfg.children && cfg.children.length > 0) {
                group.addShape('marker', {
                    attrs: {
                        x: nodeWidth + 5,
                        y: 0,
                        r: 6,
                        symbol: cfg.collapsed ? G6.Marker.expand : G6.Marker.collapse,
                        fill: '#fff',
                        stroke: '#666',
                        cursor: 'pointer',
                    },
                    name: 'collapse-icon',
                })
            }

            return rect
        },
        getAnchorPoints() {
            return [
                [0, 0.5],
                [1, 0.5],
            ]
        },
        afterUpdate(cfg, group) {
            const rect = group.find(el => el.get('name') === 'rect-shape')
            if (rect) {
                rect.attr({ stroke: cfg.id === selectedNodeId.value ? '#4c7bff' : '#999' })
            }

            const highlight = group.find(el => el.get('name') === 'highlight-shape')
            if (highlight) {
                highlight.attr({ opacity: cfg.id === selectedNodeId.value ? 0.15 : 0 })
            }

            const marker = group.find(el => el.get('name') === 'collapse-icon')
            if (marker) {
                marker.attr('symbol', cfg.collapsed ? G6.Marker.expand : G6.Marker.collapse)
            }
        }
    })

    G6.registerEdge('custom-edge', {
        draw(cfg, group) {
            const { startPoint, endPoint } = cfg
            const path = [
                ['M', startPoint.x, startPoint.y],
                ['C',
                    (startPoint.x + endPoint.x) / 2, startPoint.y,
                    (startPoint.x + endPoint.x) / 2, endPoint.y,
                    endPoint.x, endPoint.y
                ]
            ]
            return group.addShape('path', {
                attrs: {
                    path,
                    stroke: '#bbb',
                    lineWidth: 2,
                },
                name: 'edge-path',
            })
        }
    })

    const graph = new G6.TreeGraph({
        container: graphContainer.value,
        width: graphContainer.value.clientWidth,
        height: graphContainer.value.clientHeight,
        animate: false,
        modes: {
            default: ['drag-canvas', 'zoom-canvas'],
        },
        defaultNode: {
            type: 'custom-node',
        },
        defaultEdge: {
            type: 'custom-edge',
        },
        layout: {
            type: 'compactBox',
            direction: 'LR',
            getId: d => d.id,
            getHeight: () => 40,
            getWidth: (d) => {
                const ctx = document.createElement('canvas').getContext('2d')
                ctx.font = '14px sans-serif'
                const labelWidth = ctx.measureText(d.label).width

                const tags = d.tags || []
                let tagsWidth = 0
                const tagPadding = 6
                const tagFontSize = 12
                ctx.font = `${tagFontSize}px sans-serif`
                tags.forEach(tag => {
                    tagsWidth += ctx.measureText(tag).width + tagPadding * 2
                })
                tagsWidth = Math.max(tagsWidth, 0)

                const contentWidth = Math.max(labelWidth, tagsWidth)
                const minWidth = 100
                return Math.max(contentWidth + 20, minWidth)
            },
            getVGap: () => 20,
            getHGap: () => 60,
            align: 'DL',
            nodeSep: 50,
            rankSep: 100,
        },
    })

    // 检查 graphData 是否包含必要的属性
    if (!graphData.value || !graphData.value.id || typeof graphData.value.children === 'undefined') {
        console.error('graphData 格式不正确，缺少必要属性:', graphData.value);
        return;
    }

    // 创建自定义的过滤数据函数
    const getFilteredGraphData = () => {
        const deepFilter = (node) => {
            if (store.pendingDeletions && store.pendingDeletions.includes(node.id)) {
                return null; // 过滤掉待删除节点
            }

            if (node.children) {
                node.children = node.children
                    .map(deepFilter)
                    .filter(Boolean); // 移除null值
            }

            return node;
        };

        return deepFilter(JSON.parse(JSON.stringify(graphData.value)));
    };

    // 使用过滤后的数据初始化图表
    graph.data(getFilteredGraphData());
    graph.render();
    graph.on('afterrender', () => {
        console.log('图表渲染完成');
    });
    graph.on('error', (err) => {
        console.error('图表渲染出错:', err);
    });
    graph.zoom(1.1)
    graph.fitCenter()
    graphRef.value = graph

    graph.on('node:click', async (evt) => {
        const { shape, item } = evt
        if (!shape || !item) return
        const name = shape.get('name')
        const model = item.getModel()
        if (name === 'collapse-icon') {
            model.collapsed = !model.collapsed
            const matrix = graph.getGroup().getMatrix()
            graph.changeData(graphData.value)
            if (matrix) graph.getGroup().setMatrix(matrix)
            return
        }

        selectedNodeId.value = item.getID()
        graph.getNodes().forEach(node => {
            graph.setItemState(node, 'selected', node.getID() === item.getID())
            graph.updateItem(node, {})
        })

        if (model && model.id) {
            const store = useKnowledgeGraphStore();
            const resourceCount = await store.countNodeResources(model.id);
            stats.value.resources = resourceCount;
        } else {
            stats.value.resources = 0; // 如果没有节点或节点没有ID，则重置为0
        }

        // 无论抽屉栏是否打开，都更新可编辑节点数据
        const currentNode = await getCurrentNode()
        if (currentNode && currentNode !== 'root') {
            editableNode.value = {
                nodeId: currentNode.id,
                nodeName: currentNode.label || '',
                nodeDesc: currentNode.description || '',
                tags: [...(currentNode.tags || [])]
            }
        }
    })

    graph.on('canvas:click', (evt) => {
        if (evt.target && evt.target.get('type') !== 'canvas') return

        selectedNodeId.value = null
        graph.getNodes().forEach(node => {
            graph.setItemState(node, 'selected', false)
            graph.updateItem(node, {})
        })
    })

    resizeObserver = new ResizeObserver(() => {
        if (graph && graphContainer.value) {
            const width = graphContainer.value.clientWidth
            const height = graphContainer.value.clientHeight
            graph.changeSize(width, height)
        }
    })
    resizeObserver.observe(graphContainer.value)
}

// 添加默认数据创建函数
function createDefaultGraphData(courseName) {
    return {
        id: 'root',
        label: courseName || '默认课程',
        description: '这是根节点的描述',
        collapsed: false,
        children: [],
    }
}

// 组件卸载清理
onBeforeUnmount(() => {
    if (resizeObserver) {
        resizeObserver.disconnect();
        resizeObserver = null;
    }
    if (graphRef.value) {
        graphRef.value.destroy();
        graphRef.value = null;
    }
});
</script>

<style lang="scss" scoped>
.knowledge-graph-page {
    height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;

    .main-area {
        flex: 1;
        position: relative;
        overflow: hidden;

        .graph-container {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 100%;
            background-color: #f5f5f5;
            background-image: radial-gradient(#ccc 1px, transparent 0);
            background-size: 20px 20px;
            background-position: 0 0;
        }
    }
}
</style>