import api from '@/api/service.js'

/**
 * 获取毕业设计作品列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页码，默认1
 * @param {number} params.pageSize - 每页数量，默认10
 * @param {string} params.name - 作品名称（可选）
 * @param {string} params.graduationYear - 毕业年份（可选）
 * @returns {Promise} API响应
 */
export const getGraduationWorksList = (params = {}) => {
  // 默认参数
  const defaultParams = {
    pageNum: 1,
    pageSize: 10
  };
  
  // 过滤掉空值参数并合并默认参数
  const filteredParams = Object.fromEntries(
    Object.entries({
      ...defaultParams,
      ...params
    }).filter(([_, v]) => v !== '' && v !== undefined && v !== null)
  );
  
  return api({
    url: '/graduation/list',
    method: 'get',
    params: filteredParams
  });
}

/**
 * 获取毕业设计分类列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页码，默认1
 * @param {number} params.pageSize - 每页数量，默认100
 * @param {string} params.name - 分类名称（可选）
 * @returns {Promise} API响应
 */
export const getGraduationCategoriesList = (params = {}) => {
  // 默认参数
  const defaultParams = {
    pageNum: 1,
    pageSize: 100 // 分类数量通常不多，设置较大值获取全部
  };
  
  // 过滤掉空值参数并合并默认参数
  const filteredParams = Object.fromEntries(
    Object.entries({
      ...defaultParams,
      ...params
    }).filter(([_, v]) => v !== '' && v !== undefined && v !== null)
  );
  
  return api({
    url: '/graduation/category/list',
    method: 'get',
    params: filteredParams
  });
}

/**
 * 获取毕业设计作品详情
 * @param {string} id - 毕业设计作品ID
 * @returns {Promise} API响应
 */
export const getGraduationWorkDetail = (id) => {
  return api({
    url: `http://8.134.236.247:1991/graduation/get`,
    method: 'get',
    params: { id }
  });
}

/**
 * 获取毕业设计作品图片列表
 * @param {string} workId - 毕业设计作品ID
 * @returns {Promise} API响应
 */
export const getGraduationImageList = (workId) => {
  return api({
    url: `http://8.134.236.247:1991/graduation/image/list`,
    method: 'get',
    params: { workId }
  });
}

/**
 * 获取毕业设计作品视频列表
 * @param {string} workId - 毕业设计作品ID
 * @returns {Promise} API响应
 */
export const getGraduationVideoList = (workId) => {
  return api({
    url: `http://8.134.236.247:1991/graduation/video/list`,
    method: 'get',
    params: { workId }
  });
}

/**
 * 获取分类详情
 * @param {string} categoryId - 分类ID
 * @returns {Promise} API响应
 */
export const getGraduationCategoryDetail = (categoryId) => {
  return api({
    url: `/graduation/category/get`,
    method: 'get',
    params: { id: categoryId }
  });
}
