<!-- src\views\teacher\CourseDetail\components\Overview\CourseSidebar.vue -->
<template>
  <div class="course-sidebar">
    <!-- 一级菜单：课程概况 -->
    <div 
      class="sidebar-header" 
      @click="toggleMainMenu('course-overview')"
      :class="{ 'open': mainMenuStates['course-overview'] }"
    >
      课程概况 
      <img 
        :src="mainMenuStates['course-overview'] ? expandIcon : collapseIcon" 
        alt="菜单图标" 
        class="menu-icon" 
      />
    </div>
    <!-- 课程概况二级菜单 -->
    <div class="sidebar-submenu" :style="{ display: mainMenuStates['course-overview'] ? 'block' : 'none' }">
      <div 
        class="submenu-item" 
        :class="{ active: currentSubmenu === 'course-intro' }"
        @click="handleSubmenuClick('course-intro')"
      >
        课程介绍
      </div>
      <div 
        class="submenu-item" 
        :class="{ active: currentSubmenu === 'teaching-team' }"
        @click="handleSubmenuClick('teaching-team')"
      >
        教学团队
      </div>
    </div>
    
    <!-- 一级菜单：教学设计 -->
    <div 
      class="sidebar-header" 
      @click="toggleMainMenu('teaching-design')"
      :class="{ 'open': mainMenuStates['teaching-design'] }"
    >
      教学设计 
      <img 
        :src="mainMenuStates['teaching-design'] ? expandIcon : collapseIcon" 
        alt="菜单图标" 
        class="menu-icon" 
      />
    </div>
    <!-- 教学设计二级菜单 -->
    <div class="sidebar-submenu" :style="{ display: mainMenuStates['teaching-design'] ? 'block' : 'none' }">
      <div 
        class="submenu-item" 
        :class="{ active: currentSubmenu === 'curriculum-design' }"
        @click="handleSubmenuClick('curriculum-design')"
      >
        课程体系设计
      </div>
      <div 
        class="submenu-item" 
        :class="{ active: currentSubmenu === 'teaching-outline' }"
        @click="handleSubmenuClick('teaching-outline')"
      >
        教学大纲
      </div>
      <!-- 新增：课程框架菜单 -->
      <div 
        class="submenu-item" 
        :class="{ active: currentSubmenu === 'course-framework' }"
        @click="handleSubmenuClick('course-framework')"
      >
        课程框架
      </div>
      <div 
        class="submenu-item" 
        :class="{ active: currentSubmenu === 'knowledge-modules' }"
        @click="handleSubmenuClick('knowledge-modules')"
      >
        课程知识模块
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineEmits } from 'vue';
// 导入自定义图标
const collapseIcon = new URL('@/assets/img/General/icon-collapse.png', import.meta.url).href;
const expandIcon = new URL('@/assets/img/General/icon-expand.png', import.meta.url).href;

// 定义自定义事件
const emits = defineEmits(['menuClick']);

// 状态管理 - 优化为对象形式管理多个一级菜单
const mainMenuStates = ref({
  'course-overview': true,  // 课程概况初始展开
  'teaching-design': false  // 教学设计初始收起
});
const currentSubmenu = ref('course-intro'); // 当前激活的二级菜单项

// 切换一级菜单展开/收起
const toggleMainMenu = (menuKey) => {
  mainMenuStates.value[menuKey] = !mainMenuStates.value[menuKey];
};

// 点击二级菜单
const handleSubmenuClick = (key) => {
  currentSubmenu.value = key;
  // 触发自定义事件，传递选中的菜单键
  emits('menuClick', key);
};
</script>
<style scoped lang="scss">
// 定义 SCSS 变量
$primary-color: #6b47dc;      // 深紫色（展开状态背景）
$primary-light: #dcd4f5;      // 淡紫色（二级菜单选中背景）
$bg-color: #fff;              // 白色（默认背景）
$text-color: #333;            // 文本颜色

.course-sidebar {
  width: 200px;
  border-right: 1px solid #e6e6e6;
  background-color: $bg-color;
  
  .sidebar-header {
    padding: 12px 16px;
    cursor: pointer;
    font-size: 14px;
    color: $text-color;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: $bg-color; // 未展开时白色背景
    transition: background-color 0.3s ease; // 平滑过渡
    
    .menu-icon {
      width: 16px;
      height: 16px;
      transition: filter 0.3s ease;
    }
    
    &.open {
      background-color: $primary-color; // 展开时深紫色背景
      color: white;
    }
  }
  
  .sidebar-submenu {
    background-color: $bg-color;
    border-bottom: 1px solid #e6e6e6;
    
    .submenu-item {
      padding: 10px 24px;
      cursor: pointer;
      font-size: 14px;
      color: $text-color;
      border-bottom: 1px dashed #e6e6e6;
      transition: background-color 0.2s ease;
      
      &:last-child {
        border-bottom: none;
      }
      
      &.active {
        background-color: $primary-light; // 选中时淡紫色背景
        color: $primary-color;
        font-weight: 500;
      }
    }
  }
}
</style>