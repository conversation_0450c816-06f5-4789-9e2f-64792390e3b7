// stores/useAssignmentStore.js
import { defineStore } from 'pinia'

export const useAssignmentStore = defineStore('assignmentStore', {
    state: () => ({
        form: null,
        questionPapers: [],
        step: 0,
    }),
    actions: {
        saveDraft(formData, questionList, step) {
            this.form = formData;
            this.questionPapers = questionList;
            this.step = step;
        },
        clearDraft() {
            this.form = null;
            this.questionPapers = [];
            this.step = 0;
        }
    }
});
