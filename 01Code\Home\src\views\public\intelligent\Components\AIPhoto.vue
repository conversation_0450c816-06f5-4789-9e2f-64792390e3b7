<template>
  <div class="image-generator-container">
    <div class="top">
      <h1>图片生成</h1>
      <p>灵思妙笔描万象，科技智能绘千形</p>
    </div>

    <div class="mid">
      <div class="image-row">
        <div class="image-card" v-for="(item, index) in imageItems" :key="index"
             @click="loadImageText(item.text)">
          <img :src="item.url" alt="示例图片"/>
          <div class="overlay-text">{{ item.text }}</div>
        </div>
      </div>
    </div>

    <div class="input-container">
      <textarea v-model="promptText" placeholder="描述你所想象的画面、角色、情绪、场景、风格..."
                @input="handleInput"></textarea>
      <button @click="generateImage" class="generate-btn">
        <span class="arrow-icon">⮞</span>
      </button>
    </div>

    <div class="sidebar-wrapper">
      <div class="toggle-btn-container">
        <button class="toggle-btn" @click="toggleSidebar">
          <span v-if="sidebarVisible">▶</span>
          <span v-else>◀</span>
        </button>
      </div>

      <div class="sidebar-container">
        <div class="sidebar" :class="{ 'sidebar-collapsed': !sidebarVisible }">
          <h3>生成的图片 Waiting~</h3>
          <div class="sidebar-content">
            <div v-if="loading" class="loading-animation">
              <img src="../../assets/photo/loading.gif" alt="加载中..."/>
            </div>
            <div v-for="(image, index) in generatedImages" :key="index" class="generated-image">
              <img :src="image.url" alt="生成的图片" @click="previewImage(image.url)"/>
              <button @click.stop="downloadImage(image.url)">下载</button>
            </div>
          </div>
        </div>

      </div>

      <div v-if="showPreview" class="preview-modal" @click.self="closePreview">
        <div class="preview-content">
          <span class="close-btn" @click="closePreview">&times;</span>
          <img :src="selectedImage" alt="预览大图"/>
        </div>
      </div>

    </div>
  </div>
</template>

<script>
import {generateImage} from '../../../../api/student/intelligent/photo.js';

export default {

  data() {
    return {
      promptText: '',
      loading: false,
      generatedImages: [],
      sidebarVisible: false,
      showPreview: false,
      selectedImage: null,
      imageItems: [
        {
          url: 'https://public.educoder.net/files/ai_create_image/p5ezxil8n/download20250525-930-sgvjvd_7d9afaa1.png',
          text: '图片风格为水墨画，一条蜿蜒的小溪穿过古老的村庄，溪水使用淡墨轻染，显得清澈见底，两岸古树依依，各种树木以不同深浅的墨色表现，营造出一种古朴宁静的氛围'
        },
        {
          url: 'https://public.educoder.net/files/ai_create_image/p5ezxil8n/download20250525-326-1u6h0y_70d7f00d.png',
          text: '很多人在海滩上奔跑，日落夕阳'
        },
        {
          url: 'https://public.educoder.net/files/ai_create_image/p5ezxil8n/download20250525-4971-1vujo0d_826cc8d5.png',
          text: '一抹少女倩影撑着油纸伞行走在天气明媚的江南湖畔'
        },
        {
          url: 'https://public.educoder.net/files/ai_create_image/p5ezxil8n/download20250525-736-1i7dqlz_e4b3d015.png',
          text: '采用风格化渲染渲染，平静的水面泛起涟漪，云雾笼罩着一丝阳光，远方传来海鸥的身影'
        },
        {
          url: 'https://public.educoder.net/files/ai_create_image/p5ezxil8n/download20250525-26338-1otlj80_e71b26e8.png',
          text: '图片风格为柯南画风的漫画，描绘一段群像，突出一些细节，是一群热血少年'
        }
      ]
    };
  },
  methods: {
    handleInput(event) {
      this.promptText = event.target.value;
    },
    async generateImage() {
      if (!this.checkUserLogin()) {
        // 未登录则跳转到登录页面
        this.$router.push('/login');
        return;
      }
      if (this.promptText.trim() === '') {
        alert('请输入描述内容');
        return;
      }
      this.loading = true;
      this.sidebarVisible = true;
      try {
        const imageUrl = await generateImage(this.promptText);
        this.generatedImages.unshift({
          url: imageUrl,
          prompt: this.promptText
        });
        this.promptText = '';
      } catch (error) {
        alert('图片生成失败，请稍后重试');
      } finally {
        this.loading = false;
      }
    },
    loadImageText(text) {
      this.promptText = text;
    },
    downloadImage(url) {
      fetch(url)
        .then(response => response.blob())
        .then(blob => {
          const link = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          link.download = `ai-image-${Date.now()}.png`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        });
    },

    //预览方法
    previewImage(url) {
      this.selectedImage = url;
      this.showPreview = true;
    },

    closePreview() {
      this.showPreview = false;
      this.selectedImage = null;
    },

    toggleSidebar() {
      this.sidebarVisible = !this.sidebarVisible;
    },

    checkUserLogin() {
      // return this.$store.state.user.isLoggedIn; // Vuex方式示例
     return localStorage.getItem('token'); // localStorage方式示例
    },
  }
};
</script>

<style scoped>
.image-generator-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 10px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.top {
  margin-bottom: 20px;
  text-align: center;
}

.top h1 {
  font-size: 24px;
  font-weight: bold;
  color: black;
}

.top p {
  margin-top: 10px;
  font-size: 16px;
  color: #666;
}

.mid {
  margin-bottom: 20px;
}

.image-row {
  display: flex;
  overflow-x: auto;
  gap: 35px;
  padding: 25px 0;
  scroll-behavior: smooth;
}

.image-card {
  margin: 5px;
  flex: 0 0 auto;
  width: 180px;
  height: 150px;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.image-card:hover {
  transform: translateY(-8px);
  cursor: pointer;
}

.image-card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.overlay-text {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 4px;
  font-size: 12px;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.image-card:hover .overlay-text {
  transform: translateY(0);
}

.input-container {
  position: relative;
  border: 1px solid #e0e0e0;
  border-radius: 10px;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

textarea {
  width: 100%;
  height: 120px;
  border: none;
  resize: none;
  padding: 20px;
  font-size: 16px;
  outline: none;
  box-sizing: border-box;
  background-color: #fafafa;
}

textarea::placeholder {
  color: #999;
}

.generate-btn {
  position: absolute;
  right: 15px;
  bottom: 15px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #956ef6;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.generate-btn:hover {
  background-color: #d2c8ea;
}

.arrow-icon {
  font-size: 20px;
  color: white;
}

.sidebar-wrapper {
  position: fixed;
  top: 228px;
  right: 0;
  z-index: 999;
  display: flex;
  align-items: flex-start;
}

.sidebar-container {
  position: fixed;
  top: 228px;
  right: 2px;
  width: 300px;
  max-height: calc(90vh - 300px);
  z-index: 999;
}

.toggle-btn-container {
  order: -1;
  position: relative;
  z-index: 1000;
}

.toggle-btn {
  background-color: #a994de;
  color: #8863e5;
  border: none;
  border-radius: 4px 0 0 4px;
  padding: 10px 12px;
  cursor: pointer;
  z-index: 1000;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
}

.sidebar {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 15px;
  height: 100%;
  max-height: inherit;
  display: flex;
  flex-direction: column;
}

.sidebar-collapsed {
  transform: translateX(calc(100% + 40px));
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
}

.loading-animation,
.generated-image {
  align-items: center;
  margin-bottom: 15px;
}

.loading-animation img {
  max-width: 80%;
  max-height: 80%;
}

.sidebar-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: #956ef6;
  border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: rgba(149, 110, 246, 0.1);
}

.generated-image {
  margin-bottom: 15px;
  text-align: center;
}

.generated-image img {
  width: 85%;
  border-radius: 8px;
  margin-bottom: 8px;
}

.generated-image button {
  padding: 5px 10px;
  background-color: #956ef6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.generated-image button:hover {
  background-color: #8d8b8b;
}

.preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.preview-content {
  position: relative;
  max-width: 90%;
  max-height: 90%;
}

.preview-content img {
  max-width: 100%;
  max-height: 90vh;
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

.close-btn {
  position: absolute;
  top: -30px;
  right: -30px;
  color: white;
  font-size: 40px;
  cursor: pointer;
  transition: transform 0.3s;
}

.close-btn:hover {
  transform: scale(1.2);
}

.generated-image {
  position: relative;
  margin-bottom: 15px;
  transition: transform 0.3s;
}

.generated-image:hover {
  transform: scale(1.02);
}

.generated-image img {
  cursor: zoom-in;
  border: 2px solid transparent;
  transition: border-color 0.3s;
}

.generated-image img:hover {
  border-color: #956ef6;
}

.sidebar {
  scrollbar-width: thin;
  scrollbar-color: #956ef6 transparent;
}

.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-thumb {
  background: #956ef6;
  border-radius: 3px;
}
</style>
