<template>
  <div class="scrollable-table-container">
    <!-- 顶部控制栏 -->
    <div class="control-bar">
      <div class="class-selector" v-if="classes && classes.length > 0">
        <label for="class-select">班级：</label>
        <select 
          id="class-select" 
          v-model="selectedClass"
          @change="handleClassChange"
        >
          <option v-for="classItem in classes" :key="classItem.id" :value="classItem.id">
            {{ classItem.name }}
          </option>
        </select>
      </div>

      <div class="right-controls">
        <button class="download-btn" @click="$emit('export-data')">
          <span class="icon">↓</span> 下载{{ title }}
        </button>

        <div class="search-box">
          <input
            type="text"
            :placeholder="`请输入学生姓名或学号`"
            v-model="searchQuery"
            @input="filterStudents"
          />
          <span class="search-icon">🔍</span>
        </div>
      </div>
    </div>

    <!-- 数据截止时间 -->
    <div v-if="cutoffDate" class="data-cutoff">
      数据截止到 {{ cutoffDate }}
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <div class="loading-text">加载中...</div>
    </div>

    <!-- 表格容器 -->
    <div class="table-container">
      <!-- 表格导航控制 -->
      <div class="table-navigation">
        <button 
          class="nav-button left" 
          @click="scrollTable(-1)"
          :disabled="scrollPosition === 0"
        >
          &lt;
        </button>
        <button 
          class="nav-button right" 
          @click="scrollTable(1)"
          :disabled="isScrollEnd"
        >
          &gt;
        </button>
      </div>

      <!-- 成绩表格容器 -->
      <div class="table-wrapper" ref="tableWrapper" @scroll="handleScroll">
        <table class="score-table" ref="table">
          <thead>
            <tr>
              <th 
                v-for="column in columns" 
                :key="column.key"
                :style="{ minWidth: column.minWidth || '120px' }"
                @click="sortBy(column.key)"
                :class="{ sortable: column.sortable }"
              >
                {{ column.title }}
                <span 
                  v-if="column.sortable" 
                  class="sort-icon" 
                  :class="{ 'active': sortField === column.key }"
                >
                  {{ sortOrder === 'asc' ? '↑' : '↓' }}
                </span>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(student, index) in filteredData" :key="index">
              <td 
                v-for="column in columns" 
                :key="column.key"
                :class="getCellClass(student[column.key], column)"
              >
                {{ formatCellValue(student[column.key], column) }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { 
  searchHomeworkGrades, 
  searchExamGrades, 
  searchRegularGrades 
} from '@/api/teacher/grade'

const props = defineProps({
  title: String,
  columns: Array,
  data: Array,
  classes: Array,
  cutoffDate: String,
  loading: Boolean
})

const emit = defineEmits(['class-change','export-data'])

const selectedClass = ref(props.classes?.[0]?.id || '')
const searchQuery = ref('')
const sortField = ref('id')
const sortOrder = ref('asc')
const scrollPosition = ref(0)
const isScrollEnd = ref(false)
const tableWrapper = ref(null)
const table = ref(null)


const filteredData = ref([...props.data])


// 根据标题获取对应的搜索函数
const getSearchFunction = () => {
  switch(props.title) {
    case '作业测试成绩':
      return searchHomeworkGrades
    case '考试成绩':
      return searchExamGrades
    case '平时成绩':
      return searchRegularGrades
    default:
      return null
  }
}

// 初始化或更新数据
const updateFilteredData = () => {
  if (searchQuery.value.trim() === '') {
    // 没有搜索条件时，显示当前班级的所有数据
    filteredData.value = props.data.filter(student => 
      selectedClass.value ? student.classId === selectedClass.value : true
    )
  }
  // 有搜索条件时，由 searchGrades() 更新 filteredData
}

// 搜索成绩
const searchGrades = async () => {
  const searchFunc = getSearchFunction();
  if (!searchFunc || !selectedClass.value) return;
  
  try {
    const res = await searchFunc(selectedClass.value, searchQuery.value);
    
    // 根据不同类型处理返回数据
    switch(props.title) {
      case '作业测试成绩':
        filteredData.value = processHomeworkData(res.result);
        break;
      case '考试成绩':
        filteredData.value = processExamData(res.result);
        break;
      case '平时成绩':
        filteredData.value = res.result || [];
        break;
      default:
        filteredData.value = res.result || [];
    }
  } catch (error) {
    console.error('搜索失败:', error);
    filteredData.value = []; 
  }
};

// 处理作业数据
const processHomeworkData = (data) => {
  if (!data) return [];
  
  return data.map(student => {
    const studentData = {
      studentId: student.studentId,
      studentName: student.studentName || '未知',
      assignmentFinalScore: student.assignmentFinalScore
    };

    // 处理作业详情
    if (student.assignmentDetails) {
      student.assignmentDetails.forEach(assignment => {
        studentData[`assignment_${assignment.assignmentId}`] = {
          studentScore: assignment.studentScore,
          submitStatus: assignment.submitStatus
        };
      });
    }
    
    return studentData;
  });
};

// 处理考试数据（类似作业处理）
const processExamData = (data) => {
  if (!data) return [];
  
  return data.map(student => {
    const studentData = {
      studentId: student.studentId,
      studentName: student.studentName || '未知',
      examFinalScore: student.examFinalScore
    };

    // 处理考试详情
    if (student.examDetails) {
      student.examDetails.forEach(exam => {
        studentData[`exam_${exam.examId}`] = {
          studentScore: exam.studentScore,
          submitStatus: exam.submitStatus
        };
      });
    }
    
    return studentData;
  });
};

// 子组件 <script setup> 中修改
const handleClassChange = () => {
  emit('class-change', selectedClass.value);
  // 班级切换后：清空搜索框，显示新班级的全部数据
  searchQuery.value = '';
  filteredData.value = props.data.filter(student => 
    student.classId === selectedClass.value
  );
};

// 处理搜索输入
const filterStudents = () => {
  if (searchQuery.value.trim() !== '') {
    searchGrades(); // 调用API搜索
  } else {
    filteredData.value = props.data.filter(student => 
      selectedClass.value ? student.classId === selectedClass.value : true
    );
  }
};



// 修改方法：排序
const sortBy = (field) => {
  if (sortField.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortField.value = field
    sortOrder.value = 'asc'
  }
  
  // 本地排序
  filteredData.value.sort((a, b) => {
    const aValue = a[sortField.value]
    const bValue = b[sortField.value]
    
    const aCompareValue = (aValue && typeof aValue === 'object') ? aValue.checkStatus : aValue
    const bCompareValue = (bValue && typeof bValue === 'object') ? bValue.checkStatus : bValue
    
    let comparison = 0
    if (aCompareValue > bCompareValue) {
      comparison = 1
    } else if (aCompareValue < bCompareValue) {
      comparison = -1
    }
    return sortOrder.value === 'asc' ? comparison : -comparison
  })
}


watch(() => props.data, (newData) => {
  if (!searchQuery.value) {
    filteredData.value = [...newData]
  }
}, { deep: true })

// 原有watch保持不变
watch(() => props.classes, (newClasses) => {
  if (newClasses && newClasses.length > 0) {
    selectedClass.value = newClasses[0].id
  }
})



const exportScores = () => {
  console.log(`导出${props.title}`)
  // 这里可以添加实际的导出逻辑
}

const scrollTable = (direction) => {
  if (!tableWrapper.value) return
  
  const containerWidth = tableWrapper.value.clientWidth
  const scrollAmount = containerWidth * 0.8
  
  const newPosition = scrollPosition.value + (scrollAmount * direction)
  scrollPosition.value = Math.max(0, Math.min(newPosition, table.value.scrollWidth - containerWidth))
  
  tableWrapper.value.scrollTo({
    left: scrollPosition.value,
    behavior: 'smooth'
  })
}

const handleScroll = () => {
  if (!tableWrapper.value) return
  
  scrollPosition.value = tableWrapper.value.scrollLeft
  checkScrollEnd()
}

const checkScrollEnd = () => {
  if (!tableWrapper.value || !table.value) return
  
  const containerWidth = tableWrapper.value.clientWidth
  const scrollWidth = table.value.scrollWidth
  const currentScroll = tableWrapper.value.scrollLeft
  
  // 更新滚动状态
  isScrollEnd.value = currentScroll + containerWidth >= scrollWidth - 1
}

const formatCellValue = (value, column) => {
  if (column.formatter) {
    return column.formatter(value)
  }
  
  // 处理对象类型的考勤状态
  if (value && typeof value === 'object' && value.checkStatusName) {
    return value.checkStatusName
  }
  
  return value || '-'
}

const getCellClass = (value, column) => {
  let cellClass = ''
  
  if (column.class) {
    if (typeof column.class === 'function') {
      cellClass = column.class(value)
    } else {
      cellClass = column.class
    }
  }
  
  // 为考勤状态添加额外样式
  if (value && typeof value === 'object' && value.hasChecked !== undefined) {
    if (!value.hasChecked) {
      cellClass += ' no-check'
    } else {
      cellClass += ` check-status-${value.checkStatus || 0}`
    }
  }
  
  return cellClass.trim()
}

/* // 生命周期钩子
onMounted(() => {
  filteredData.value = [...props.data]
  checkScrollEnd()
  window.addEventListener('resize', checkScrollEnd)
})
 */
onMounted(() => {
  if (selectedClass.value && props.data.length) {
    filteredData.value = props.data.filter(student => 
      student.classId === selectedClass.value
    );
  } else {
    filteredData.value = [...props.data];
  }
  checkScrollEnd();
  window.addEventListener('resize', checkScrollEnd);
});


// 监听数据变化
watch(() => props.data, () => {
  nextTick(() => {
    checkScrollEnd()
    // 重置滚动位置
    if (tableWrapper.value) {
      tableWrapper.value.scrollLeft = 0
      scrollPosition.value = 0
    }
  })
}, { deep: true })

// 监听classes变化
watch(() => props.classes, (newClasses) => {
  if (newClasses && newClasses.length > 0) {
    selectedClass.value = newClasses[0].id
  }
})
</script>

<style lang="scss" scoped>
.scrollable-table-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.control-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px 0;
  
  .class-selector {
    select {
      padding: 8px 12px;
      border-radius: 4px;
      border: 1px solid #ddd;
      font-size: 14px;
      min-width: 180px;
      background-color: white;
      cursor: pointer;
      
      &:focus {
        outline: none;
        border-color: #409eff;
      }
    }
  }
  
  .right-controls {
    display: flex;
    align-items: center;
    gap: 15px;
  }
  
  .download-btn {
    padding: 8px 15px;
    background-color: #409eff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: background-color 0.3s;
    font-size: 14px;
    
    &:hover {
      background-color: #66b1ff;
    }
    
    .icon {
      font-weight: bold;
    }
  }
  
  .search-box {
    position: relative;
    
    input {
      padding: 8px 12px 8px 32px;
      border-radius: 4px;
      border: 1px solid #ddd;
      width: 250px;
      font-size: 14px;
      transition: border-color 0.3s;
      
      &:focus {
        outline: none;
        border-color: #409eff;
      }
    }
    
    .search-icon {
      position: absolute;
      left: 10px;
      top: 50%;
      transform: translateY(-50%);
      color: #999;
    }
  }
}

.data-cutoff {
  color: #888;
  font-size: 14px;
  margin-bottom: 10px;
  padding-left: 5px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 100;
  
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #409eff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 10px;
  }
  
  .loading-text {
    color: #409eff;
    font-size: 16px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.table-container {
  position: relative;
  flex-grow: 1;
}

.table-navigation {
  position: absolute;
  right: 10px;
  top: 10px;
  display: flex;
  gap: 5px;
  z-index: 20;
  background: rgba(255, 255, 255, 0.9);
  padding: 5px;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  
  .nav-button {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border: 1px solid #ddd;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s;
    font-weight: bold;
    font-size: 14px;
    
    &:hover {
      background-color: #f5f5f5;
      border-color: #c0c4cc;
    }
    
    &:disabled {
      cursor: not-allowed;
      background-color: rgba(255, 255, 255, 0.3);
      color: #c0c4cc;
    }
  }
}

.table-wrapper {
  width: 100%;
  max-height: 500px;
  overflow-x: auto;
  overflow-y: auto;
  padding-bottom: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: white;
}

.score-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
  font-size: 14px;
  
  th {
    background-color: #f5f7fa;
    padding: 12px 15px;
    text-align: left;
    font-weight: bold;
    color: #909399;
    position: sticky;
    top: 0;
    z-index: 10;
    border-bottom: 1px solid #ebeef5;
    
    &.sortable {
      cursor: pointer;
      transition: background-color 0.3s;
      
      &:hover {
        background-color: #f0f2f5;
      }
    }
    
    .sort-icon {
      margin-left: 5px;
      font-size: 12px;
      opacity: 0;
      transition: opacity 0.3s;
      
      &.active {
        opacity: 1;
      }
    }
  }

  td {
    padding: 10px 15px;
    text-align: left;
    color: #606266;
    border-bottom: 1px solid #ebeef5;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  tr {
    transition: background-color 0.3s;
    
    &:nth-child(even) {
      background-color: #fafafa;
    }
    
    &:hover {
      background-color: #f5f7fa;
    }
  }
}


/* 响应式调整 */
@media (max-width: 768px) {
  .control-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    
    .right-controls {
      width: 100%;
      justify-content: space-between;
    }
    
    .search-box input {
      width: 100%;
    }
  }
  
  .table-wrapper {
    max-height: 400px;
  }
}
</style>