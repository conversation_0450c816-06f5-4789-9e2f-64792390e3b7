<!-- <template>
  <div class="dialog-mask" @click.self="$emit('close')">
    <div class="link-dialog">
      <div class="dialog-header">
        <h3>添加链接</h3>
        <span class="close" @click="$emit('close')">✕</span>
      </div>

      <div class="dialog-body">
        <div class="form-group">
          <label>链接标题</label>
          <input v-model="linkTitle" placeholder="请输入链接标题" @keyup.enter="handleConfirm" />
        </div>

        <div class="form-group">
          <label>链接地址</label>
          <input v-model="linkUrl" placeholder="请输入链接URL" @keyup.enter="handleConfirm" />
        </div>
      </div>

      <div class="dialog-footer">
        <button class="cancel-btn" @click="$emit('close')">取消</button>
        <button class="confirm-btn" @click="handleConfirm">确定</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const emit = defineEmits(['close', 'confirm'])

const linkTitle = ref('')
const linkUrl = ref('')

const handleConfirm = () => {
  if (linkTitle.value.trim() && linkUrl.value.trim()) {
    console.log('准备发送链接数据:', {  // 调试用
      title: linkTitle.value.trim(),
      url: linkUrl.value.trim()
    });
    emit('confirm', {
      title: linkTitle.value.trim(),
      url: linkUrl.value.trim()
    });
    // 清空输入框（可选）
    linkTitle.value = '';
    linkUrl.value = '';
  }
}
</script> -->
<template>
  <div class="dialog-mask" @click.self="$emit('close')">
    <div class="link-dialog">
      <div class="dialog-header">
        <h3>添加链接</h3>
        <span class="close" @click="$emit('close')">✕</span>
      </div>

      <div class="dialog-body">
        <div class="form-group">
          <label>链接标题</label>
          <input v-model="name" placeholder="请输入链接标题" @keyup.enter="handleConfirm" />
        </div>

        <div class="form-group">
          <label>链接地址</label>
          <input v-model="url" placeholder="请输入链接URL" @keyup.enter="handleConfirm" />
        </div>
      </div>

      <div class="dialog-footer">
        <button class="cancel-btn" @click="$emit('close')">取消</button>
        <button class="confirm-btn" @click="handleConfirm" :disabled="isLoading">
          {{ isLoading ? '添加中...' : '确定' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref ,computed} from 'vue'
import { useBaseResourceStore } from '@/stores/public/teacher/baseResourceStore'
import {useResourceStore} from '@/stores/public/teacher/resource'
import { useUserStore } from '@/stores/userStore'
import { useCourseStore } from '@/stores/courseStore'
const resourceStore = useResourceStore()
import { useRoute } from 'vue-router'


const props = defineProps({
  folderId: {
    type: String,
    default: null
  }
})

const emit = defineEmits(['close', 'success'])

const route = useRoute()
const baseStore = useBaseResourceStore()
const userStore = useUserStore()
const courseStore = useCourseStore()

const name = ref('')
const url = ref('')
const isLoading = ref(false)

const currentCourseId = computed(() => route.params.courseId || courseStore.currentCourseId)

const handleConfirm = async () => {
  if (!name.value.trim() || !url.value.trim()) return
  if (!currentCourseId.value) {
    alert('未选择课程，无法添加链接资源')
    return
  }

  isLoading.value = true
  
  try {
    const result = await baseStore.addResourceLink(
      url.value.trim(),
      name.value.trim(),
      {
        createdId: userStore.user.id,
        folderId: props.folderId||null, // 使用传入的folderId
        locationData: {
          type: 2, // 课程资源
          courseId: currentCourseId.value
        }
      }
    )
    
    emit('success', result)
    name.value = ''
    url.value = ''
    await resourceStore.refreshFolders();
    emit('close')
  } catch (error) {
    console.error('添加链接失败:', error)
    alert('添加链接失败，请重试')
  } finally {
    isLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
.dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;
}

.link-dialog {
  background: #fff;
  width: 500px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dialog-header {
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;

  h3 {
    margin: 0;
    font-size: 16px;
    color: #333;
  }

  .close {
    font-size: 18px;
    color: #999;
    cursor: pointer;

    &:hover {
      color: #666;
    }
  }
}

.dialog-body {
  padding: 20px;
}

.form-group {
  margin-bottom: 16px;

  label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    color: #666;
    font-weight: 500;
  }

  input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s ease;

    &:focus {
      border-color: #4c7bff;
      outline: none;
    }
  }
}

.dialog-footer {
  padding: 12px 20px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-top: 1px solid #eee;

  button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .cancel-btn {
    background-color: #f1f1f1;
    color: #666;

    &:hover {
      background-color: #e0e0e0;
    }
  }

  .confirm-btn {
    background-color: #4c7bff;
    color: white;

    &:hover {
      background-color: #3a6aeb;
    }

    &:disabled {
      background-color: #ccc;
      cursor: not-allowed;
    }
  }
}
</style>