<template>
  <div class="course-intro">
    <div class="edit-header">
      <h2>课程介绍</h2>
      <div class="action-buttons" v-if="!isEditing">
        <el-button 
          type="primary" 
          @click="toggleEditMode"
          size="small"
          :loading="loading"
        >
          编辑
        </el-button>
      </div>
      <div class="action-buttons" v-else>
        <el-button 
          size="small"
          @click="cancelEdit"
          :disabled="loading"
        >
          取消
        </el-button>
        <el-button 
          type="primary" 
          @click="saveInfo"
          size="small"
          :loading="loading"
        >
          保存
        </el-button>
      </div>
    </div>
    
    <el-skeleton :loading="loading" animated :count="5">
      <template #template>
        <el-skeleton-item variant="text" style="width: 30%; margin-bottom: 16px;" />
        <el-skeleton-item variant="text" style="width: 100%; margin-bottom: 24px;" />
      </template>
      <template #default>
        <div v-if="!isEditing">
          <div class="course-section">
            <h3>课程目标</h3>
            <div class="content ql-editor" v-html="safeHtml(courseInfo.courseTarget) || '暂无内容'"></div>
          </div>
          
          <div class="course-section">
            <h3>教学重点难点</h3>
            <div class="content ql-editor" v-html="safeHtml(courseInfo.coursePoints) || '暂无内容'"></div>
          </div>
          
          <div class="course-section">
            <h3>教学计划</h3>
            <div class="content ql-editor" v-html="safeHtml(courseInfo.coursePlan) || '暂无内容'"></div>
          </div>
          
          <div class="course-section">
            <h3>教学内容</h3>
            <div class="content ql-editor" v-html="safeHtml(courseInfo.courseContent) || '暂无内容'"></div>
          </div>
          
          <div class="course-section">
            <h3>考核要求</h3>
            <div class="content ql-editor" v-html="safeHtml(courseInfo.courseRequire) || '暂无内容'"></div>
          </div>
        </div>
        
        <el-form v-else label-position="top" class="edit-form">
          <el-form-item label="课程目标">
            <div class="quill-container">
              <quill-editor
                v-model:content="editForm.courseTarget"
                contentType="html"
                :options="editorOptions"
                placeholder="请输入课程目标"
                @ready="onEditorReady('courseTarget')"
              />
            </div>
          </el-form-item>
          
          <el-form-item label="教学重点难点">
            <div class="quill-container">
              <quill-editor
                v-model:content="editForm.coursePoints"
                contentType="html"
                :options="editorOptions"
                placeholder="请输入教学重点难点"
                @ready="onEditorReady('coursePoints')"
              />
            </div>
          </el-form-item>
          
          <el-form-item label="教学计划">
            <div class="quill-container">
              <quill-editor
                v-model:content="editForm.coursePlan"
                contentType="html"
                :options="editorOptions"
                placeholder="请输入教学计划"
                @ready="onEditorReady('coursePlan')"
              />
            </div>
          </el-form-item>
          
          <el-form-item label="教学内容">
            <div class="quill-container">
              <quill-editor
                v-model:content="editForm.courseContent"
                contentType="html"
                :options="editorOptions"
                placeholder="请输入教学内容"
                @ready="onEditorReady('courseContent')"
              />
            </div>
          </el-form-item>
          
          <el-form-item label="考核要求">
            <div class="quill-container">
              <quill-editor
                v-model:content="editForm.courseRequire"
                contentType="html"
                :options="editorOptions"
                placeholder="请输入考核要求"
                @ready="onEditorReady('courseRequire')"
              />
            </div>
          </el-form-item>
        </el-form>
      </template>
    </el-skeleton>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onBeforeUnmount } from 'vue'
import { 
  getCourseInformation, 
  saveCourseInformation,
  updateCourseInformation,
  getCourseInformationList
} from '@/api/teacher/course'
import { ElMessage } from 'element-plus'
import { QuillEditor } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css'
import DOMPurify from 'dompurify'

const props = defineProps({
  course: {
    type: Object,
    required: true
  }
})

const loading = ref(false)
const isEditing = ref(false)
const quillInstances = ref({}) // 保存编辑器实例
const autoSaveTimer = ref(null)

const courseInfo = ref({
  id: '',
  courseTarget: '',
  coursePoints: '',
  coursePlan: '',
  courseContent: '',
  courseRequire: '',
  courseId: ''
})

const editForm = reactive({
  id: '',
  courseTarget: '',
  coursePoints: '',
  coursePlan: '',
  courseContent: '',
  courseRequire: '',
  courseId: ''
})

const originalData = reactive(JSON.parse(JSON.stringify(editForm)))

// XSS防护
const safeHtml = (html) => {
  return html ? DOMPurify.sanitize(html) : ''
}

const editorOptions = reactive({
  modules: {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],
      ['blockquote', 'code-block'],
      [{ 'header': 1 }, { 'header': 2 }],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'script': 'sub'}, { 'script': 'super' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      [{ 'direction': 'rtl' }],
      [{ 'size': ['small', false, 'large', 'huge'] }],
      [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'font': [] }],
      [{ 'align': [] }],
      ['clean'],
      ['link', 'image', 'video']
    ],
    clipboard: {
      matchVisual: false // 防止粘贴内容样式混乱
    }
  },
  placeholder: '请输入内容...',
  theme: 'snow',
  bounds: document.querySelector('.quill-container') || document.body
})

// 编辑器准备就绪回调
const onEditorReady = (field, editor) => {
  quillInstances.value[field] = editor
}

// 获取课程信息
const fetchCourseInfo = async () => {
  if (!props.course.id) {
    console.warn('课程ID不存在，无法获取课程信息');
    return;
  }

  try {
    loading.value = true;

    // 1. 获取课程信息ID
    const listResponse = await getCourseInformationList({
      courseId: props.course.id,
      pageNum: 1,
      pageSize: 1,
    });

    const infoId = listResponse.records?.[0]?.id;

    if (!infoId) {
      // 如果没有课程信息，初始化空数据
      courseInfo.value = {
        id: '',
        courseTarget: '',
        coursePoints: '',
        coursePlan: '',
        courseContent: '',
        courseRequire: '',
        courseId: props.course.id,
      };
      return;
    }

    // 2. 获取详细信息
    const detailData = await getCourseInformation(infoId);

    // 更新本地数据
    courseInfo.value = detailData;
    Object.assign(editForm, detailData);
    Object.assign(originalData, JSON.parse(JSON.stringify(detailData)));
  } catch (error) {
    console.error('获取课程信息失败:', error);
    ElMessage.error(`获取课程信息失败: ${error.message}`);
  } finally {
    loading.value = false;
  }
};

// 保存课程信息
const saveInfo = async () => {
  try {
    loading.value = true;

    if (!props.course.id) {
      throw new Error('课程ID不能为空');
    }

    const sanitizedData = {
      id: editForm.id,
      courseId: props.course.id,
      courseTarget: safeHtml(editForm.courseTarget),
      coursePoints: safeHtml(editForm.coursePoints),
      coursePlan: safeHtml(editForm.coursePlan),
      courseContent: safeHtml(editForm.courseContent),
      courseRequire: safeHtml(editForm.courseRequire),
    };

    const apiCall = editForm.id ? updateCourseInformation : saveCourseInformation;
    const response = await apiCall(sanitizedData);

    // 检查响应是否包含 code（确保是完整响应）
    if (typeof response.code === 'undefined') {
      throw new Error('无效的响应格式');
    }

    if (response.code !== 200) {
      throw new Error(response.msg || '保存失败');
    }

    // 更新本地数据
    Object.assign(courseInfo.value, sanitizedData);
    ElMessage.success('保存成功');

    // 强制退出编辑模式
    isEditing.value = false;

    // 可选：重新获取最新数据（如果后端有额外处理）
    await fetchCourseInfo();

    // 清除草稿
    localStorage.removeItem(`course_draft_${props.course.id}`);
  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error(`保存失败: ${error.message}`);
    // 保持编辑状态以便修正
    isEditing.value = true;
  } finally {
    loading.value = false;
  }
};


// 取消编辑
const cancelEdit = () => {
  Object.assign(editForm, JSON.parse(JSON.stringify(originalData)))
  isEditing.value = false
}

// 进入编辑模式
const toggleEditMode = () => {
  Object.assign(originalData, JSON.parse(JSON.stringify(editForm)))
  isEditing.value = true
}

// 自动保存草稿
const setupAutoSave = () => {
  if (autoSaveTimer.value) {
    clearInterval(autoSaveTimer.value)
  }
  
  autoSaveTimer.value = setInterval(() => {
    if (isEditing.value) {
      localStorage.setItem(`course_draft_${props.course.id}`, JSON.stringify(editForm))
    }
  }, 10000)
}

// 加载草稿
const loadDraft = () => {
  const draft = localStorage.getItem(`course_draft_${props.course.id}`)
  if (draft) {
    try {
      const parsed = JSON.parse(draft)
      Object.assign(editForm, parsed)
    } catch (e) {
      console.error('加载草稿失败:', e)
    }
  }
}

// 监听课程ID变化
watch(() => props.course.id, (newVal) => {
  if (newVal) {
    fetchCourseInfo()
    loadDraft()
    setupAutoSave()
  }
}, { immediate: true })

// 组件卸载前清理
onBeforeUnmount(() => {
  if (autoSaveTimer.value) {
    clearInterval(autoSaveTimer.value)
  }
  
  // 清理编辑器实例
  Object.values(quillInstances.value).forEach(instance => {
    if (instance) {
      instance.off('text-change')
    }
  })
})
</script>

<style scoped lang="scss">
.course-intro {
  padding: 16px;
  
  .edit-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 12px;
    border-bottom: 1px solid #eee;
    
    h2 {
      margin: 0;
      font-size: 18px;
      color: #333;
    }
    
    .action-buttons {
      display: flex;
      gap: 10px;
    }
  }
  
  .course-section {
    margin-bottom: 24px;
    
    h3 {
      color: #666;
      margin-bottom: 8px;
      font-size: 16px;
      font-weight: normal;
    }
    
    .content {
      white-space: pre-wrap;
      line-height: 1.6;
      color: #333;
      padding: 8px 12px;
      background-color: #f9f9f9;
      border-radius: 4px;
      border: 1px solid #eee;
      
      &.ql-editor {
        min-height: 60px;
      }
    }
  }
  
  .edit-form {
    :deep(.el-form-item__label) {
      font-weight: bold;
      color: #666;
    }
    
    .quill-container {
      min-width: 100%;
      overflow-x: auto;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      
      :deep(.ql-container) {
        min-height: 150px;
        max-width: 800px;
        border: none;
      }
      
      :deep(.ql-editor) {
        min-height: 150px;
        max-width: 800px;
        white-space: pre-wrap;
      }
      
      :deep(.ql-toolbar) {
        max-width: 800px;
        position: sticky;
        top: 0;
        background: white;
        z-index: 1;
        border: none;
        border-bottom: 1px solid #dcdfe6;
      }
    }
    
    @media (max-width: 768px) {
      :deep(.ql-toolbar) {
        position: static;
        flex-wrap: wrap;
      }
      
      :deep(.ql-container) {
        min-height: 200px;
      }
    }
  }
}
</style>