<template>
  <div class="login-container">
    <div class="login-wrapper">  <!-- 新增的包裹容器 -->
      <div class="login-image">  <!-- 新增的图片容器 -->
        <img src="@/assets/img/General/background-login.png" alt="登录背景图" class="side-image">
      </div>
      <div class="login-card">
        <!-- 原有登录卡片内容保持不变 -->
        <div class="login-header">
          <h2 class="login-title">系统登录</h2>
          <p class="login-subtitle">欢迎回来，请登录您的账户</p>
        </div>
      
      <form @submit.prevent="handleLogin" class="login-form">
        <div class="form-group">
          <label class="form-label">账号：</label>
          <div class="form-input-container">
            <span class="form-icon"><i class="fa fa-user"></i></span>
            <input v-model="form.account" type="text" required @blur="checkCaptcha" 
                   class="form-input" placeholder="请输入账号">
          </div>
        </div>
        
        <div class="form-group">
          <label class="form-label">密码：</label>
          <div class="form-input-container">
            <span class="form-icon"><i class="fa fa-lock"></i></span>
            <input v-model="form.password" type="password" required 
                   class="form-input" placeholder="请输入密码">
          </div>
        </div>
        
        <div class="form-group">
          <label class="form-label">身份：</label>
          <div class="form-input-container">
            <span class="form-icon"><i class="fa fa-id-card"></i></span>
            <select v-model="form.role" class="form-input">
              <option value="1">学生</option>
              <option value="2">教师</option>
            </select>
          </div>
        </div>
        
        <!-- 验证码区域 -->
        <div v-if="showCaptcha" class="form-group">
          <label class="form-label">验证码：</label>
          <div class="captcha-container">
            <input v-model="form.captcha" type="text" required 
                   class="form-input captcha-input" placeholder="请输入验证码">
            <img :src="captchaImage" @click="refreshCaptcha" alt="验证码" class="captcha-image">
          </div>
        </div>
        
        <button type="submit" :disabled="loading" 
                class="login-button bg-primary hover:bg-primary/90 text-white font-medium py-3 px-4 rounded-lg transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-primary/50">
          {{ loading ? '登录中...' : '登录' }}
        </button>
      </form>
      
      <div class="login-footer">
          <p class="register-link">
            没有账号？<router-link to="/register" class="text-color hover:text-color/80 transition-colors">立即注册</router-link>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth/auth'
import { generateCaptcha } from '@/api/auth/auth'

const router = useRouter()
const authStore = useAuthStore()

const form = ref({
  account: '',
  password: '',
  role: '1',
  captcha: '',
  captchaToken: ''
})

const loading = ref(false)
const showCaptcha = ref(false)
const captchaImage = ref('')

const checkCaptcha = async () => {
  if (!form.value.account) return
  
  try {
    const required = await authStore.checkCaptcha(form.value.account)
    if (required) {
      await refreshCaptcha()
    }
    showCaptcha.value = required
  } catch (error) {
    console.error('检查验证码失败:', error)
  }
}

const refreshCaptcha = async () => {
  try {
    const { imageUrl, token } = await generateCaptcha(form.value.account);
    // 先释放之前的URL对象（如果有）
    if (captchaImage.value) {
      URL.revokeObjectURL(captchaImage.value);
    }
    captchaImage.value = imageUrl;
    form.value.captchaToken = token;
  } catch (error) {
    console.error('刷新验证码失败:', error);
    alert('获取验证码失败，请重试');
  }
};

const handleLogin = async () => {
  loading.value = true;
  try {
    const credentials = { ...form.value };
    
    // 如果显示验证码但未填写
    if (showCaptcha.value && !credentials.captcha) {
      throw new Error('请输入验证码');
    }

    await authStore.login(credentials);
    router.push('/');
  } catch (error) {
    // 登录失败后重新检查是否需要验证码
    const needsCaptcha = await authStore.checkCaptchaRequired(form.value.account);
    showCaptcha.value = needsCaptcha;
    
    if (needsCaptcha) {
      await refreshCaptcha();
    }
    
    alert(error.message || '登录失败，请重试');
  } finally {
    loading.value = false;
  }
};

onUnmounted(() => {
  if (captchaImage.value) {
    URL.revokeObjectURL(captchaImage.value);
  }
});
</script>

<style lang="scss" scoped>
@use "@/styles/auth/login.scss";
</style>