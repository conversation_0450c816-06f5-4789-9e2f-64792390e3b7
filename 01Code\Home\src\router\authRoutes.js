// src/router/authRoutes.js认证路由，需要登录但无特定角色要求的路由
export default [
  {
    path: '/profile',
    component: () => import('@/views/DefaultLayout.vue'), // 使用带导航栏的布局
    children: [
      {
        path: '',
        name: 'Profile',
        component: () => import('@/views/auth/Profile.vue'),
        meta: { requiresAuth: true }
      }
    ]
  },
  {
    path: '/notifications',
    component: () => import('@/views/DefaultLayout.vue'), // 使用带导航栏的布局
    children: [
      {
        path: '',
        name: 'Notifications',
        component: () => import('@/views/auth/Notifications.vue'),
        meta: { requiresAuth: true }
      }
    ]
  }
]
