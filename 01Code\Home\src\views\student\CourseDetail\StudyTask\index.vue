<!--src\views\student\CourseDetail\StudyTask\index.vue-->
<template>
  <div class="chapter-container">
    <template v-if="courseId">
      <div v-if="!currentVideo" class="chapter-content">
        <div v-if="loading" class="loading">
          <i class="el-icon-loading"></i> 加载中...
        </div>
        
        <div v-else class="chapter-list">
          <chapterNode
            v-for="(chapter, index) in filteredChapters"
            :key="index"
            :node="chapter"
            :depth="0"
            :search-query="searchQuery"
            :course-id="courseId"
            :anchor-id="`chapter-${chapter.id}`"
            @preview-resource="handlePreview"
            @video-clicked="handleVideoClicked"
          />
        </div>
        
        <div v-if="!loading && filteredChapters.length === 0" class="no-results">
          <i class="fa fa-search-minus"></i> 没有找到匹配的内容
        </div>
      </div>

      <video-player 
        v-else 
        :video-data="currentVideo" 
        @close="closeVideoPlayer"
      />
    </template>
    <div v-else class="loading">
      课程加载中，请稍候...
    </div>

    <el-dialog
      title="下载视频"
      :visible.sync="downloadDialogVisible"
      width="30%"
    >
      <div class="download-dialog">
        <el-form>
          <el-form-item label="文件名">
            <el-input v-model="downloadFileName" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="保存位置">
            <el-button @click="selectDownloadFolder">选择文件夹</el-button>
            <span class="folder-path">{{ downloadFolderPath || '未选择' }}</span>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="downloadDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmDownload">下载</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { getCourseChapters } from '@/api/student/course'
import chapterNode from './chapterNode.vue'
import VideoPlayer from './VideoPlayer.vue'
import { ElMessage } from 'element-plus'

const route = useRoute()
const courseId = ref(null)
const loading = ref(true)
const chapters = ref([])
const searchQuery = ref('')
const currentVideo = ref(null)
const downloadDialogVisible = ref(false)
const downloadFileName = ref('')
const downloadFolderPath = ref('')

const formatChapters = (nodes) => {
  return nodes?.map(node => ({
    ...node,
    children: node.children ? formatChapters(node.children) : []
  })) || []
}

const fetchChapters = async () => {
  try {
    if (!courseId.value) return
    
    loading.value = true
    const res = await getCourseChapters(courseId.value)
    
    if (res?.code === 200) {
      chapters.value = formatChapters(res.result)
    } else {
      console.error('获取章节失败:', res?.msg)
      ElMessage.warning('获取课程内容失败')
    }
  } catch (error) {
    console.error('请求章节出错:', error)
    ElMessage.error('加载课程失败')
  } finally {
    loading.value = false
  }
}

const filteredChapters = computed(() => {
  if (!searchQuery.value.trim()) return chapters.value
  
  function searchInNode(node) {
    const isNodeMatch = node.title.toLowerCase().includes(searchQuery.value.toLowerCase())
    
    let childrenMatch = []
    if (node.children && node.children.length > 0) {
      childrenMatch = node.children
        .map(child => searchInNode(child))
        .filter(matched => matched !== null)
    }
    
    if (isNodeMatch || childrenMatch.length > 0) {
      return {
        ...node,
        children: childrenMatch.length > 0 ? childrenMatch : node.children
      }
    }
    
    return null
  }
  
  return chapters.value
    .map(chapter => searchInNode(chapter))
    .filter(chapter => chapter !== null)
})

const handlePreview = (resource) => {
  currentVideo.value = {
    id: resource.id,
    videoTitle: resource.name,
    videoUrl: resource.url
  }
}

const handleVideoClicked = (videoData) => {
  currentVideo.value = {
    id: videoData.id,
    videoTitle: videoData.videoTitle,
    videoUrl: videoData.videoUrl
  }
}

const closeVideoPlayer = () => {
  currentVideo.value = null
}

const handleDownload = () => {
  downloadFileName.value = currentVideo.value.videoTitle
  downloadDialogVisible.value = true
}

const selectDownloadFolder = async () => {
  try {
    if (window.electron) {
      const result = await window.electron.showOpenDialog({
        properties: ['openDirectory']
      })
      if (!result.canceled && result.filePaths.length > 0) {
        downloadFolderPath.value = result.filePaths[0]
      }
    } else {
      const dirHandle = await window.showDirectoryPicker()
      downloadFolderPath.value = dirHandle.name
    }
  } catch (error) {
    console.error('选择文件夹出错:', error)
    ElMessage.error('选择文件夹失败')
  }
}

const confirmDownload = () => {
  if (!downloadFolderPath.value) {
    ElMessage.warning('请选择保存位置')
    return
  }
  
  console.log('开始下载:', {
    fileName: downloadFileName.value,
    folderPath: downloadFolderPath.value,
    videoUrl: currentVideo.value.videoUrl
  })
  
  ElMessage.success('下载任务已开始')
  downloadDialogVisible.value = false
}

onMounted(() => {
  if (route.params.courseId) {
    courseId.value = route.params.courseId
  }
})

watch(() => route.params.courseId, (newVal) => {
  if (newVal) {
    courseId.value = newVal
    fetchChapters()
  }
}, { immediate: true })
</script>

<style scoped>
.chapter-container {
  background: white;
  border-radius: 8px;
  height: 100vh;
  overflow: hidden;
}

.chapter-content {
  padding: 30px;
  height: 100%;
  overflow-y: auto;
}

.loading {
  padding: 20px;
  text-align: center;
  color: #666;
}

.no-results {
  text-align: center;
  padding: 40px 0;
  color: #6c757d;
  font-size: 14px;
}

.no-results i {
  margin-right: 8px;
}

.el-icon-loading {
  margin-right: 8px;
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.download-dialog .folder-path {
  margin-left: 10px;
  color: #666;
  font-size: 14px;
}
</style>