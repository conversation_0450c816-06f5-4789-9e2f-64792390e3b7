<template>
    <div class="account-management">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>账号管理</h1>
      </div>
  
      <!-- 主内容区 -->
      <div class="management-container">
        <!-- 左侧导航标签 -->
        <div class="sidebar">
            <h2>账号管理</h2>
          <button
            v-for="tab in tabs"
            :key="tab.title"
            @click="switchTab(tab)"
            :class="{ active: activeTab.title === tab.title }"
          >
            {{ tab.title }}
          </button>
        </div>
  
        <!-- 右侧内容区 -->
        <div class="content">
          <component :is="activeTab.component" />
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref } from 'vue';
  
  // 导入外部组件
  import BasicInfo from './BasicInfo/index.vue';
  import AuthenticationInfo from './AuthenticationInfo/index.vue';
  import SafetySettings from './SafetySettings/index.vue';
  import AccountLogin from './AccountLogin/index.vue';
  
  const tabs = [
    { title: '基本信息', component: BasicInfo },
    { title: '认证信息', component: AuthenticationInfo },
    { title: '安全设置', component: SafetySettings },
    { title: '账号登录', component: AccountLogin }
  ];
  
  // 默认激活第一个标签
  const activeTab = ref(tabs[0]);
  
  function switchTab(tab) {
    activeTab.value = tab;
  }
  </script>
  
  <style lang="scss" scoped>
  .account-management {
    padding-top: 10vw;
    padding-left: 3vw;
    padding-right: 3vw;
  }
  
  .page-header {
    color: white;
    margin-bottom: 2vw;
  
    h1 {
      font-size: 3rem;
      font-weight: 400;
      margin: 0;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }
  }
  
  .management-container {
    display: flex;
    gap: 1.5vw;
    min-height: 70vh;
    background-color: white;
    border-radius: 1vw 1vw 0 0;
    overflow: hidden;
    padding: 1vw;
    margin-top: 10vw;
  
    .sidebar {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 0.8vw;
  
      button {
        padding: 0.8vw 1vw;
        border: none;
        background-color: #f5f5f5;
        cursor: pointer;
        border-radius: 0.3vw;
        font-size: 1vw;
        transition: all 0.3s ease;
        text-align: left;
  
        &:hover {
          background-color: #e9e9e9;
        }
  
        &.active {
          background-color: $primary-color;
          color: white;
          font-weight: bold;
        }
      }
    }
  
    .content {
      flex: 4;
      padding: 1vw;
      background-color: #fafafa;
      border-radius: 0.3vw;
    }
  }
  </style>