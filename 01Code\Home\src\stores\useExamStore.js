// src/stores/useExamStore.js
import { defineStore } from 'pinia'

export const useExamStore = defineStore('examStore', {
    state: () => ({
        paperId: null,
        createdQuestions: []
    }),
    actions: {
        setPaperId(id) {
            this.paperId = id
        },
        addQuestion(question) {
            const exists = this.createdQuestions.find(q => q.id === question.id)
            if (!exists) {
                this.createdQuestions.push(question)
            }
        },
        clear() {
            this.paperId = null
            this.createdQuestions = []
        }
    }
})
