<template>
  <div class="notice-header">
    <!-- 类型切换选项卡（带统计数量） -->
    <div class="type-tabs">
      <button v-for="tab in tabs" :key="tab.type" :class="{ active: activeTab === tab.type }"
        @click="activeTab = tab.type">
        {{ tab.label }} ({{  typeCounts[tab.type] || 0 }})
      </button>
    </div>
    <!-- 上方：新建按钮 -->
    <div class="top-bar">
      <button class="new-btn" @click="router.push({ name: 'CreateAnnoucement' })">
        ＋ 新建通知
      </button>
    </div>
  </div>
</template>

<script setup>
// 保持原有逻辑不变
import { ref, computed } from 'vue'
import { useNoticeStore } from '@/stores/public/teacher/noticeStore'
import { useRouter } from 'vue-router'
import {getNoticeList} from '@/api/teacher/notice'
import { useRoute } from 'vue-router' // 新增导入

const route = useRoute() 
const router = useRouter()
const noticeStore = useNoticeStore()

const typeCounts = computed(() => noticeStore.typeCounts)

const tabs = computed(() => {
  return Object.entries(noticeStore.NOTICE_TYPES).map(([type, config]) => ({
    type,
    label: config.label
  }))
})




// 当前激活的选项卡
const activeTab = computed({
  get() {
    return noticeStore.activeTab
  },
  set(value) {
    noticeStore.activeTab = value
  }
})

onMounted(() => {
   noticeStore.fetchTypeCounts()
})
</script>

<style scoped>
.notice-header {
  margin-bottom: 20px;
  display: flex; 
  justify-content: space-between; 
  align-items: center; 
}

.top-bar {
  display: flex;
  justify-content: flex-end; /* 按钮在容器右侧 */
  align-items: center;
  margin-bottom: 0; /* 移除底部边距 */
}

.new-btn {
  padding: 8px 16px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.new-btn:hover {
  background-color: #40a9ff;
}

.type-tabs {
  display: flex;
  border-bottom: 1px solid #e8e8e8;
  margin-right: 16px; /* 与右侧按钮保持间距 */
}

.type-tabs button {
  padding: 8px 16px;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  margin-right: 16px;
}

.type-tabs button.active {
  color: #1890ff;
  border-bottom-color: #1890ff;
  font-weight: 500;
}

/* 数量样式 */
.type-tabs button span.count {
  margin-left: 5px;
  color: #1890ff;
  font-size: 12px;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}
</style>