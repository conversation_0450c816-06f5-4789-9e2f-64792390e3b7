<template>
  <div class="modal-overlay">
    <div class="modal">
      <h3>从教师列表选择成员</h3>
      
      <div class="search-container">
        <el-input
          v-model="searchQuery.teacherName"
          placeholder="教师姓名"
          clearable
        />
        <el-input
          v-model="searchQuery.teaCode"
          placeholder="工号"
          clearable
        />
        <el-button 
          type="primary" 
          @click="searchTeachers"
          :loading="searchLoading"
        >
          搜索
        </el-button>
      </div>

      <el-table
        :data="filteredTeachers"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="55"
        />
        <el-table-column label="教师头像" width="80">
          <template #default="{row}">
            <el-avatar :src="row.teacherImg || defaultAvatar" />
          </template>
        </el-table-column>
        <el-table-column prop="teacherName" label="姓名" />
        <el-table-column prop="teacherTitle" label="职称" />
        <el-table-column prop="department" label="单位" />
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          @current-change="handlePageChange"
        />
      </div>

      <div class="dialog-footer">
        <el-button @click="$emit('close')">取消</el-button>
        <el-button 
          type="primary" 
          @click="confirmAddMembers"
          :disabled="selectedTeachers.length === 0"
          :loading="confirmLoading"
        >
          确认添加({{ selectedTeachers.length }})
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { getTeacherList, createTeachingTeam } from '@/api/teacher/courseTeam'
import defaultAvatar from '@/assets/img/nodata.png'

export default {
  props: {
    courseId: {
      type: String,
      required: true
    }
  },
  setup(props, { emit }) {
    const searchQuery = ref({
      teacherName: '',
      teaCode: '',
      department: ''
    })
    
    const pagination = ref({
      pageNum: 1,
      pageSize: 10,
      total: 0
    })
    
    const allTeachers = ref([])
    const selectedTeachers = ref([])
    const searchLoading = ref(false)
    const confirmLoading = ref(false)
    
    const filteredTeachers = computed(() => {
      return allTeachers.value.filter(teacher => {
        return (
          teacher.teacherName.includes(searchQuery.value.teacherName) &&
          (!searchQuery.value.teaCode || teacher.teaCode.includes(searchQuery.value.teaCode)) &&
          (!searchQuery.value.department || teacher.department.includes(searchQuery.value.department))
      )
      })
    })
    
    const searchTeachers = async () => {
      try {
        searchLoading.value = true
        const params = {
          ...searchQuery.value,
          ...pagination.value
        }
        
        const res = await getTeacherList(params)
        if (res.code === 200) {
          allTeachers.value = res.result.records
          pagination.value.total = res.result.total
        }
      } catch (error) {
        console.error('搜索教师失败:', error)
      } finally {
        searchLoading.value = false
      }
    }
    
    const handleSelectionChange = (selection) => {
      selectedTeachers.value = selection
    }
    
    const handlePageChange = () => {
      searchTeachers()
    }
    
    const confirmAddMembers = async () => {
      try {
        confirmLoading.value = true
        const promises = selectedTeachers.value.map(teacher => {
          return createTeachingTeam({
            courseId: props.courseId,
            teacherId: teacher.id,
            teamRole: 2 // 默认教师角色
          })
        })
        
        await Promise.all(promises)
        ElMessage.success(`成功添加${selectedTeachers.value.length}位教师`)
        emit('add-member-success')
        emit('close')
      } catch (error) {
        ElMessage.error('添加失败: ' + (error.message || '未知错误'))
      } finally {
        confirmLoading.value = false
      }
    }
    
    onMounted(() => {
      searchTeachers()
    })
    
    return {
      searchQuery,
      pagination,
      allTeachers,
      selectedTeachers,
      filteredTeachers,
      searchLoading,
      confirmLoading,
      defaultAvatar,
      searchTeachers,
      handleSelectionChange,
      handlePageChange,
      confirmAddMembers
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.modal {
  background: white;
  width: 800px;
  max-width: 90%;
  max-height: 80vh;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.search-container {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.search-container .el-input {
  flex: 1;
}

.el-table {
  flex: 1;
  margin-bottom: 20px;
}

.pagination {
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>