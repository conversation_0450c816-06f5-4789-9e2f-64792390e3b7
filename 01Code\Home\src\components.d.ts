/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AnswerItem: typeof import('./components/teacher/AnswerItem.vue')['default']
    AnswerList: typeof import('./components/teacher/AnswerList.vue')['default']
    ConfirmDialog: typeof import('./components/ConfirmDialog.vue')['default']
    CustomNotification: typeof import('./components/CustomNotification.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAnchor: typeof import('element-plus/es')['ElAnchor']
    ElAnchorLink: typeof import('element-plus/es')['ElAnchorLink']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapseTransition: typeof import('element-plus/es')['ElCollapseTransition']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTree: typeof import('element-plus/es')['ElTree']
    FilePreviewDialog: typeof import('./components/FilePreviewDialog.vue')['default']
    NavBar: typeof import('./components/NavBar.vue')['default']
    NotificationCenter: typeof import('./components/NotificationCenter.vue')['default']
    PermissionGuard: typeof import('./components/PermissionGuard.vue')['default']
    ResourcePreview: typeof import('./components/ResourcePreview.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SidebarLayout: typeof import('./components/Layout/SidebarLayout.vue')['default']
    TreeNode: typeof import('./components/resource/TreeNode.vue')['default']
  }
}
