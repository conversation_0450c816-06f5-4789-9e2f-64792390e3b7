//src\views\public\base\components\PersonalizedPath.vue
<template>
  <div class="personalized-path">
    <h2 class="section-title">个性化学习路径推荐</h2>
    <div class="paths-container">
      <div 
        class="path-item" 
        v-for="(item, index) in paths" 
        :key="index"
      >
        <div class="path-header">
          <span class="path-number">{{ item.number }}</span>
          <h3 class="path-title">{{ item.title }}</h3>
        </div>
        <p class="path-desc">{{ item.desc }}</p>
        <button class="path-button">{{ item.buttonText }}</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
// 模拟数据，实际可从接口获取或通过父组件传递
const paths = ref([
  {
    number: 1,
    title: '推荐路径1',
    desc: '根据您当前学习进度，推荐您进行下一个任务: 实验任务A',
    buttonText: '继续学习'
  },
  {
    number: 2,
    title: '推荐路径2',
    desc: '薄弱环节识别: 操作准确度需要提升，推荐补充学习资料',
    buttonText: '查看学习资源'
  },
  {
    number: 3,
    title: '推荐路径3',
    desc: '兴趣匹配: 推荐您深度学习感兴趣的课程',
    buttonText: '查看相关资源'
  },
  {
    number: 4,
    title: '推荐路径4',
    desc: '巩固加强: 对您已学习心得课程进行课后巩固',
    buttonText: '课后巩固习题'
  }
]);
</script>

<style scoped lang="scss">
.personalized-path {
  width: 100%;
  padding: 2rem;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-top: 2rem;

  .section-title {
    font-size: 2rem;
    color: #333;
    margin-bottom: 2rem;
  }

  .paths-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
  }

  .path-item {
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 1.5rem;
    background-color: #f9f9f9;
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-5px);
    }

    .path-header {
      display: flex;
      align-items: center;
      margin-bottom: 1rem;

      .path-number {
        display: inline-block;
        width: 30px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        background-color: #7b61ff;
        color: #fff;
        border-radius: 50%;
        margin-right: 1rem;
        font-size: 1rem;
      }

      .path-title {
        font-size: 1.2rem;
        color: #333;
      }
    }

    .path-desc {
      font-size: 1rem;
      color: #666;
      margin-bottom: 1.5rem;
      height: 60px; /* 设置固定高度 */
      overflow: hidden; /* 如果内容超出高度则隐藏 */
      text-overflow: ellipsis; /* 显示省略号 */
      display: -webkit-box;
      -webkit-line-clamp: 3; /* 限制显示的行数 */
      -webkit-box-orient: vertical; 
    }

    .path-button {
      background-color: #7b61ff;
      color: #fff;
      border: none;
      padding: 0.8rem 1.5rem;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.3s ease;

      &:hover {
        background-color: #6247e5;
      }
    }
  }
}
</style>