import request from '@/api/service'


export function uploadFile(formData) {
  return request({
    url: '/course/resource/upload',
    method: 'post',
    data: formData,
    transformRequest: [function (data) {
      return data;
    }],
    // 不设置Content-Type，让Axios自动处理
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('token')}`
      // 移除Content-Type设置
    }
  })
}

// 保存资源信息
export function saveResource(data) {
  return request({
    url: '/course/resource/save',
    method: 'post',
    data,
  })
}

// 新增文件夹相关API
export function saveFolder(data) {
  return request({
    url: '/course/folder/save',
    method: 'post',
    data,
  })
}

export function getFolder(id) {
  return request({
    url: '/course/folder/get',
    method: 'get',
    params: { id }
  })
}

export function getFolderList(params) {
  return request({
    url: '/course/folder/list',
    method: 'get',
    params
  })
}

export function getFileList(params) {
  return request({
    url: '/course/resource/list',
    method: 'get',
    params
  })
}




export function saveResourceLocation(data) {
  return request({
    url: '/course/folder/location/save',
    method: 'post',
    data,
  })
}

export function getPublicFolderTree(params) {
  return request({
    url: '/course/link/publicFolderTree',
    method: 'get',
    params
  })
}



// 删除资源
export function deleteResources(ids) {
  return request({
    url: '/course/resource/removeAll',
    method: 'post',
    data: ids,
  })
}

export function deleteResource(id) {
  return request({
    url: '/course/resource/remove',
    method: 'post',
    data: id,
  })
}

export function deleteFolders(ids) {
  return request({
    url: '/course/folder/removeAll',
    method: 'post',
    data: ids,
  })
}



// 更新资源
export function updateResource(data) {
  return request({
    url: '/course/resource/update',
    method: 'post',
    data
  })
}

export function updateFolder(data) {
  return request({
    url: '/course/folder/update',
    method: 'post',
    data
  })
}

export function updateResourceLocation(data) {
  return request({
    url: '/course/folder/location/updateByResourceId',
    method: 'post',
    data
  })
}



/*---------------------- 课程资源 ------------------------*/
export function saveCourseLink(data) {
  return request({
    url: '/course/link/save',
    method: 'post',
    data,
  })
} 

export function getCourseFolderTree(params) {
  return request({
    url: '/course/link/folderTree',
    method: 'get',
    params
  })
}

export function moveCourseLink(data) {
  return request({
    url: '/course/link/move',
    method: 'post',
    data,
  })
} 

export function addCourseLink(data) {
  return request({
    url: '/course/link/add',
    method: 'post',
    data,
  })
} 


export function publishCourseLink(data) {
  return request({
    url: '/course/link/publish-resource',
    method: 'post',
    data,
  })
} 





