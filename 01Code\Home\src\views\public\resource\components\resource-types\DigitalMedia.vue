<!-- <template>
  <div class="digital-media-content">
    <div digital-media-title>
      
    </div>
    <ResourceCategoryCard
      v-for="(category, index) in categories"
      :key="`category-${index}`"
      :title="category.title"
      :items="category.items"
      :ref="(el) => setCategoryRef(el, index)"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ResourceCategoryCard from '../ResourceCategoryCard.vue'

// 数媒资源数据
const categories = [
  {
    title: '职业沟通技能',
    items: [
      {
        id: 'comm-1',
        title: '商务沟通基础',
        subtitle: '总书稿 | 山西日报职业学院',
        image: '../1.png'
      },
       {
        id: 'comm-1',
        title: '商务沟通基础',
        subtitle: '总书稿 | 山西日报职业学院',
        image: '/images/comm1.png'
      },
       {
        id: 'comm-1',
        title: '商务沟通基础',
        subtitle: '总书稿 | 山西日报职业学院',
        image: '/images/comm1.png'
      },
      {
        id: 'comm-1',
        title: '商务沟通基础',
        subtitle: '总书稿 | 山西日报职业学院',
        image: '/images/comm1.png'
      },
      
    ]
  }
  
]

// 分类卡片引用
const categoryRefs = ref([])
const setCategoryRef = (el, index) => {
  categoryRefs.value[index] = el
}

// 暴露给父组件的方法
const scrollToCategory = (index) => {
  categoryRefs.value[index]?.$el.scrollIntoView({
    behavior: 'smooth',
    block: 'start'
  })
}

defineExpose({ scrollToCategory })
</script>

<style scoped lang="scss">
.digital-media-content {
  padding-bottom: 40px;

  /* 分类卡片间距 */
  :deep(.category-container) {
    margin-bottom: 30px;
  }
}
</style> -->


<template>
  <div class="digital-media-content">
    <!-- 资源通用集合中心 - 动态内容 -->
    <div class="resource-section">
      <h2 class="section-title">资源通用集合中心</h2>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner">加载中...</div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <div class="error-message">{{ error }}</div>
        <button @click="fetchCategories" class="retry-button">重试</button>
      </div>

      <!-- 资源分类网格 -->
      <div v-else class="resource-grid four-columns">
        <div
          v-for="category in categories"
          :key="category.categoryId"
          class="resource-card"
          @click="handleCategoryClick(category)"
        >
          <h3>{{ category.categoryName }}</h3>
          <p v-if="category.description" class="category-description">{{ category.description }}</p>
        </div>
      </div>
    </div>

    <!-- 课程资料管理及共享 - 动态内容 -->
    <div class="resource-section">
      <h2 class="section-title">课程资料管理及共享</h2>

      <!-- 加载状态 -->
      <div v-if="courseLoading" class="loading-container">
        <div class="loading-spinner">加载中...</div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="courseError" class="error-container">
        <div class="error-message">{{ courseError }}</div>
        <button @click="fetchCourseDocuments" class="retry-button">重试</button>
      </div>

      <!-- 课程资料网格 -->
      <div v-else class="resource-grid three-columns">
        <div
          v-for="document in courseDocuments"
          :key="document.documentId"
          class="course-card"
          @click="handleDocumentClick(document)"
        >
          <h3>{{ document.documentName }}</h3>
          <p v-if="document.creator" class="document-creator">{{ document.creator }}</p>
          <p v-if="document.documentContent" class="document-description">{{ document.documentContent }}</p>
          <div v-if="document.fileUrl" class="document-actions">
            <span class="download-hint">点击下载</span>
          </div>
        </div>

        <!-- 如果没有数据，显示提示 -->
        <div v-if="courseDocuments.length === 0" class="no-data-message">
          <p>暂无课程资料</p>
        </div>
      </div>
    </div>

    <!-- 虚拟仿真资源库 - 动态内容 -->
    <div class="resource-section">
      <h2 class="section-title">虚拟仿真资源库</h2>

      <!-- 加载状态 -->
      <div v-if="simulationLoading" class="loading-container">
        <div class="loading-spinner">加载中...</div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="simulationError" class="error-container">
        <div class="error-message">{{ simulationError }}</div>
        <button @click="fetchSimulationSoftware" class="retry-button">重试</button>
      </div>

      <!-- 虚拟仿真软件网格 -->
      <div v-else class="resource-grid three-columns">
        <div
          v-for="software in simulationSoftware"
          :key="software.softwareId"
          class="simulation-card"
          @click="handleSoftwareClick(software)"
        >
          <div v-if="software.coverImage" class="software-cover">
            <img :src="software.coverImage" :alt="software.softwareName" />
          </div>
          <div class="software-info">
            <h3>{{ software.softwareName }}</h3>
            <p v-if="software.version" class="software-version">版本: {{ software.version }}</p>
            <p v-if="software.description" class="software-description">{{ software.description }}</p>
            <div v-if="software.downloadUrl" class="software-actions">
              <span class="download-hint">点击下载</span>
            </div>
          </div>
        </div>

        <!-- 如果没有数据，显示提示 -->
        <div v-if="simulationSoftware.length === 0" class="no-data-message">
          <p>暂无虚拟仿真软件</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getGeneralResourceCategoryList, getGeneralResourceProblemList, getGeneralResourceSoftwareList } from '@/api/public/resource/generalResource.js'

// 资源分类相关数据
const categories = ref([])
const loading = ref(false)
const error = ref(null)

// 课程资料相关数据
const courseDocuments = ref([])
const courseLoading = ref(false)
const courseError = ref(null)

// 虚拟仿真软件相关数据
const simulationSoftware = ref([])
const simulationLoading = ref(false)
const simulationError = ref(null)

// 获取资源分类数据
const fetchCategories = async () => {
  try {
    loading.value = true
    error.value = null

    console.log('开始获取资源分类数据...')

    const response = await getGeneralResourceCategoryList({pageNum: 1,pageSize: 20 })

    console.log('API响应:', response)

    if (response && response.code === 200) {
      categories.value = response.result.records || []
      console.log('成功获取分类数据:', categories.value)
    } else {
      throw new Error(response?.msg || '获取数据失败')
    }
  } catch (err) {
    console.error('获取资源分类失败:', err)
    error.value = err.message || '获取数据失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 获取课程资料数据
const fetchCourseDocuments = async () => {
  try {
    courseLoading.value = true
    courseError.value = null

    console.log('开始获取课程资料数据...')

    const response = await getGeneralResourceProblemList({
      pageNum: 1,
      pageSize: 20 // 获取更多资料以确保显示完整
    })

    console.log('课程资料API响应:', response)

    if (response && response.code === 200) {
      courseDocuments.value = response.result.records || []
      console.log('成功获取课程资料数据:', courseDocuments.value)
    } else {
      throw new Error(response?.msg || '获取课程资料失败')
    }
  } catch (err) {
    console.error('获取课程资料失败:', err)
    courseError.value = err.message || '获取课程资料失败，请稍后重试'
  } finally {
    courseLoading.value = false
  }
}

// 获取虚拟仿真软件数据
const fetchSimulationSoftware = async () => {
  try {
    simulationLoading.value = true
    simulationError.value = null

    console.log('开始获取虚拟仿真软件数据...')

    const response = await getGeneralResourceSoftwareList({
      pageNum: 1,
      pageSize: 20 // 获取更多软件以确保显示完整
    })

    console.log('虚拟仿真软件API响应:', response)

    if (response && response.code === 200) {
      simulationSoftware.value = response.result.records || []
      console.log('成功获取虚拟仿真软件数据:', simulationSoftware.value)
    } else {
      throw new Error(response?.msg || '获取虚拟仿真软件失败')
    }
  } catch (err) {
    console.error('获取虚拟仿真软件失败:', err)
    simulationError.value = err.message || '获取虚拟仿真软件失败，请稍后重试'
  } finally {
    simulationLoading.value = false
  }
}

// 处理分类点击事件
const handleCategoryClick = (category) => {
  console.log('点击分类:', category)
  // TODO: 根据需求实现分类点击逻辑，比如跳转到详情页或筛选内容
}

// 处理文档点击事件
const handleDocumentClick = (document) => {
  console.log('点击文档:', document)
  // 如果有文件URL，则打开文件
  if (document.fileUrl) {
    window.open(document.fileUrl, '_blank')
  }
  // TODO: 根据需求实现其他文档点击逻辑
}

// 处理软件点击事件
const handleSoftwareClick = (software) => {
  console.log('点击软件:', software)
  // 如果有下载URL，则打开下载链接
  if (software.downloadUrl) {
    window.open(software.downloadUrl, '_blank')
  }
  // TODO: 根据需求实现其他软件点击逻辑
}

// 组件挂载时获取数据
onMounted(() => {
  fetchCategories()
  fetchCourseDocuments()
  fetchSimulationSoftware()
})
</script>

<style scoped lang="scss">
.digital-media-content {
  padding: 20px;
}

.section-title {
  font-size: 1.5rem;
  color: #8a6de3;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eaeaea;
}

.resource-grid {
  display: grid;
  gap: 20px;
  margin-bottom: 2rem;
  
  &.four-columns {
    grid-template-columns: repeat(4, 1fr);
  }
  
  &.three-columns {
    grid-template-columns: repeat(3, 1fr);
  }
}

.resource-card,
.course-card,
.simulation-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-5px);
  }

  h3 {
    color: #333;
    margin-bottom: 0.5rem;
  }

  p {
    color: #666;
    font-size: 0.9rem;
  }

  .category-description {
    margin-top: 0.5rem;
    font-size: 0.85rem;
    color: #888;
  }
}

.course-card {
  border-left: 4px solid #8a6de3;

  .document-creator {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }

  .document-description {
    color: #888;
    font-size: 0.85rem;
    margin-top: 0.5rem;
    line-height: 1.4;
  }

  .document-actions {
    margin-top: 0.8rem;

    .download-hint {
      color: #8a6de3;
      font-size: 0.8rem;
      font-weight: 500;
    }
  }

  &:hover {
    .download-hint {
      color: #7a5bd3;
    }
  }
}

.simulation-card {
  border-left: 4px solid #4caf50;
  display: flex;
  flex-direction: column;

  .software-cover {
    width: 100%;
    height: 120px;
    overflow: hidden;
    border-radius: 6px;
    margin-bottom: 0.8rem;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }
  }

  .software-info {
    flex: 1;
    display: flex;
    flex-direction: column;

    h3 {
      margin-bottom: 0.5rem;
    }

    .software-version {
      color: #666;
      font-size: 0.85rem;
      margin-bottom: 0.5rem;
      font-weight: 500;
    }

    .software-description {
      color: #888;
      font-size: 0.85rem;
      line-height: 1.4;
      margin-bottom: 0.8rem;
      flex: 1;
    }

    .software-actions {
      margin-top: auto;

      .download-hint {
        color: #4caf50;
        font-size: 0.8rem;
        font-weight: 500;
      }
    }
  }

  &:hover {
    .software-cover img {
      transform: scale(1.05);
    }

    .download-hint {
      color: #45a049;
    }
  }
}

// 加载状态样式
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.loading-spinner {
  font-size: 1.1rem;
  color: #8a6de3;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 错误状态样式
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 20px;
}

.error-message {
  color: #e74c3c;
  font-size: 1rem;
  margin-bottom: 1rem;
  text-align: center;
}

.retry-button {
  background: #8a6de3;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &:hover {
    background: #7a5bd3;
  }
}

// 无数据状态样式
.no-data-message {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px 20px;
  color: #999;
  font-size: 1rem;

  p {
    margin: 0;
  }
}

@media (max-width: 1200px) {
  .resource-grid {
    &.four-columns {
      grid-template-columns: repeat(2, 1fr);
    }
    
    &.three-columns {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}

@media (max-width: 768px) {
  .resource-grid {
    &.four-columns,
    &.three-columns {
      grid-template-columns: 1fr;
    }
  }
}
</style>