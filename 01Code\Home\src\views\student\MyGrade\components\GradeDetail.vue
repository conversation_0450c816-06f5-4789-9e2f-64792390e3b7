<template>
  <div class="course-detail-container">
    <!-- 头部基本信息 -->
    <div class="course-header">
      <h2>{{ course.name }}</h2>
      <div class="meta-info">
        <span class="info-item">{{ course.type }}</span>
        <span class="info-item">{{ course.credit }}学分</span>
        <span class="info-item">班级: {{ course.className || '--' }}</span>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-icon class="loading-icon"><Loading /></el-icon>
      <span>加载课程详情中...</span>
    </div>
    
    <!-- 错误状态 -->
    <el-alert
      v-if="error"
      :title="error"
      type="error"
      show-icon
      closable
      @close="error = null"
    />

    <!-- 成绩组成部分 -->
    <template v-if="!loading && !error">
      <!-- 成绩组成部分 -->
      <el-card class="score-composition">
        <template #header>
          <div class="section-title">成绩组成</div>
        </template>
        <div class="composition-grid">
          <!-- 总成绩 -->
          <div class="total-score">
            <div class="score-display">
              <span :class="['score-value', getScoreClass(scoreDetails.totalScore)]">
                {{ scoreDetails.totalScore || '--' }}
              </span>
              <span class="score-label">总成绩</span>
            </div>
          </div>
          
          <!-- 成绩组成图表 -->
          <div class="composition-chart">
            <el-progress 
              type="dashboard" 
              :percentage="scoreDetails.totalScore"
              :color="getScoreColor(scoreDetails.totalScore)"
              :width="120"
            />
            <div class="score-breakdown">
              <div v-for="item in scoreComponents" :key="item.name" class="breakdown-item">
                <span class="breakdown-label">{{ item.name }}:</span>
                <span class="breakdown-value">{{ item.value }} ({{ item.percentage }}%)</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 学习进度部分 -->
      <div class="progress-section">
        <el-card class="personal-progress">
          <template #header>
            <div class="section-title">你的学习进度</div>
          </template>
          <div class="progress-content">
            <div class="progress-stats">
              <span class="stat-value">学习进度</span>
              <span class="stat-percent">{{ progressPercentage }}%</span>
            </div>
            <el-progress 
              :percentage="progressPercentage" 
              :stroke-width="12"
              :color="progressColor"
            />
            <div class="time-stats">
              <div class="time-item">
                <span class="time-label">学习时长</span>
                <span class="time-value">{{ courseProgress.studyTime || '--' }}小时</span>
              </div>
              <div class="time-item">
                <span class="time-label">最后学习</span>
                <span class="time-value">{{ courseProgress.lastStudyTime || '--' }}</span>
              </div>
            </div>
          </div>
        </el-card>

        <el-card class="class-progress">
          <template #header>
            <div class="section-title">班级学习进度</div>
          </template>
          <div class="progress-content">
            <div class="progress-comparison">
              <div class="comparison-item">
                <span class="comparison-label">你的进度</span>
                <el-progress 
                  :percentage="progressPercentage" 
                  :stroke-width="8"
                  :color="progressColor"
                />
              </div>
              <div class="comparison-item">
                <span class="comparison-label">班级平均</span>
                <el-progress 
                  :percentage="courseProgress.classAverageProgress || 0" 
                  :stroke-width="8"
                  color="#909399"
                />
              </div>
            </div>
            <div class="rank-info">
              <span class="rank-label">班级排名</span>
              <span class="rank-value">{{ courseProgress.classRank || '--' }}</span>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 课程章节/学习进度/平时测试 -->
      <el-tabs v-model="activeTab" class="detail-tabs">
        <el-tab-pane label="学习进度" name="progress">
          <!-- 学习进度内容 -->
          <el-card class="progress-card">         
            <!-- 章节进度表格 -->
            <el-table :data="chapterProgress" style="width: 100%">
              <el-table-column prop="index" label="序号" width="80" align="center" />
              <el-table-column prop="name" label="章节名称" width="180" />
              <el-table-column prop="duration" label="视频时长" width="120" align="center">
                <template #default="{ row }">
                  {{ formatDuration(row.duration) }}
                </template>
              </el-table-column>
              <el-table-column prop="videoProgress" label="视频进度" width="180">
                <template #default="{ row }">
                  <div class="progress-cell">
                    <el-progress 
                      :percentage="row.videoProgress" 
                      :stroke-width="8"
                      :show-text="false"
                    />
                    <span class="progress-text">{{ row.videoProgress }}%</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="quizProgress" label="测验进度" width="180">
                <template #default="{ row }">
                  <div class="progress-cell">
                    <el-progress 
                      :percentage="row.quizProgress" 
                      :stroke-width="8"
                      :show-text="false"
                      :color="row.quizProgress >= 100 ? '#67C23A' : '#409EFF'"
                    />
                    <span class="progress-text">{{ row.quizProgress }}%</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="120" align="center">
                <template #default="{ row }">
                  <el-tag :type="getStatusType(row.status)" size="small">
                    {{ row.status }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-tab-pane>
        
        <el-tab-pane label="平时测试" name="quizzes">
          <!-- 平时测试内容 -->
          <el-card class="quiz-card">
            <el-table :data="quizData" style="width: 100%">
              <el-table-column prop="chapterName" label="章节" width="180" />
              <el-table-column prop="quizName" label="测试名称" />
              <el-table-column prop="score" label="成绩" width="120">
                <template #default="{ row }">
                  <span :class="getScoreClass(row.score)">
                    {{ row.score || '--' }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="date" label="完成时间" width="180" />
              <el-table-column prop="included" label="计入总成绩" width="120">
                <template #default="{ row }">
                  <el-tag :type="row.included ? 'success' : 'info'" size="small">
                    {{ row.included ? '是' : '否' }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-tab-pane>
      </el-tabs>
    </template>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { Loading } from '@element-plus/icons-vue'
import gradeApi from '@/api/student/grade'

const props = defineProps({
  course: {
    type: Object,
    required: true
  }
})


// 响应式数据
const activeTab = ref('progress')
const loading = ref(true)
const error = ref(null)
const chapterLoading = ref(false)
const quizLoading = ref(false)

const scoreDetails = ref({
  totalScore: 0,
  dailyScore: 0,
  quizScore: 0,
  meetingScore: 0,
  finalExamScore: 0
})

const courseProgress = ref({
  completedChapters: 0,
  totalChapters: 0,
  studyTime: 0,
  lastStudyTime: '',
  classAverageProgress: 0,
  classRank: null
})

const apiResponses = ref({
  scoreResponse: null,
  progressResponse: null
})

const chapterProgress = ref([])
const quizData = ref([])

// 获取课程详情数据
const fetchCourseDetail = async () => {
  try {
    loading.value = true
    error.value = null
    
    // 并行获取成绩组成和学习进度数据
    const [scoreRes, progressResponse] = await Promise.all([
      gradeApi.getScoreDetails(props.course.id),
      gradeApi.getCourseProgress(props.course.id)
    ])

    // 存储API响应
    apiResponses.value = {
      scoreResponse: scoreRes,
    }

    console.log("成绩组成",scoreRes.result),
    console.log("学习进度",progressResponse.result)

    
    // 处理成绩组成数据 - 根据新的API响应结构调整
    if (scoreRes?.code === 200) {
      // 初始化默认值
      const defaultScores = {
        totalScore: 0,
        dailyScore: 0,
        quizScore: 0,
        meetingScore: 0,
        finalExamScore: 0
      }
      
      // 处理返回的成绩数组
      scoreRes.result?.forEach(item => {
        const scoreType = item['scoreType(成绩类型)']
        const score = item['score(得分)'] 

        console.log("ces",item['scoreType(成绩类型)']),
        console.log("ces",item['score(得分)'] )
        
        switch(scoreType) {
          case '当前课程总得分':
            defaultScores.totalScore = score
            break
          case '平时成绩':
            defaultScores.dailyScore = score
            break
          case '平时测试成绩':
            defaultScores.quizScore = score
            break
          case '考勤成绩':
            defaultScores.meetingScore = score
            break
          case '期末考试成绩':
            defaultScores.finalExamScore = score
            break
        }
      })
      
      scoreDetails.value = defaultScores

    } else {
      throw new Error(scoreRes?.msg || '获取成绩组成失败')
    }
    
    // 处理学习进度数据 - 根据实际API响应结构调整
    if (progressResponse?.code === 200) {
      // 转换时间戳为可读格式
      const lastStudyTime = progressResponse.result['lastStudyTime(最后学习时间)']
        ? new Date(progressResponse.result['lastStudyTime(最后学习时间)']).toLocaleString()
        : '--'
      
      // 转换学习时长为小时（保留1位小数）
      const studyTime = progressResponse.result['studyTime(学习时长分钟)']
        ? (progressResponse.result['studyTime(学习时长分钟)'] / 60).toFixed(1)
        : '--'
      
      courseProgress.value = {
        completedChapters: progressResponse.result['myScore(我的得分)'], 
        totalChapters: progressResponse.result['totalScore(课程总分)'],   
        studyTime: studyTime,
        lastStudyTime: lastStudyTime,
        classAverageProgress: progressResponse.result['classAvgProgress(班级平均学习进度)'] || 0,
        classRank: null // 排名信息需要后端提供
      }
    } else {
      throw new Error(progressResponse?.msg || '获取学习进度失败')
    }
    
  } catch (err) {
    error.value = err.message || '获取课程详情失败'
    console.error('获取课程详情出错:', err)
  } finally {
    loading.value = false
  }
}

// 获取章节进度数据
const fetchChapterProgress = async () => {
  try {
    chapterLoading.value = true
    const response = await gradeApi.getChapterProgress(props.course.id)

    console.log("章节进度",response.result)
    
    if (response?.code === 200) {
      // 根据实际API响应结构调整
      const chapters = response.result || []
      chapterProgress.value = chapters.map((chapter, index) => ({
        index: index + 1,
        id: chapter.id || '',
        name: chapter['chapterTitle(章节名称)']  || '未知章节',
        duration: chapter['videoDuration(视频时长秒)'] || 0,
        status: getChapterStatus(chapter),
        videoProgress: chapter['watchProgress(视频观看进度)'] || 0,
        quizProgress: chapter['testProgress(测试进度)']|| 0
      }))
    } else {
      throw new Error(response?.msg || '获取章节进度失败')
    }
  } catch (err) {
    console.error('获取章节进度出错:', err)
    error.value = err.message || '获取章节进度失败'
  } finally {
    chapterLoading.value = false
  }
}

// 获取平时测试数据
const fetchQuizData = async () => {
  try {
    quizLoading.value = true
    const response = await gradeApi.getChapterTests(props.course.id)

    console.log ("平时测试",response.result)
    
    if (response?.code === 200) {
      // 根据实际API响应结构调整
      const quizzes = response.result || []
      quizData.value = quizzes.map(quiz => ({
        id: quiz.quizId || quiz.id || '',
        chapterId: quiz.chapterId || '',
        chapterName: quiz['chapterTitle(章节名称)'] || '未知章节',
        quizName: quiz['chapterTitle(章节名称)'] || '未知测试',
        score: quiz['myTotalTestScore(我的测试分数)'] || 0,
        date: quiz['watchProgress(视频观看进度)']  || '',
        included: quiz['isIncludedInTotal(是否计入总成绩)'] || false
      }))
    } else {
      throw new Error(response?.msg || '获取平时测试数据失败')
    }
  } catch (err) {
    console.error('获取平时测试数据出错:', err)
    error.value = err.message || '获取平时测试数据失败'
  } finally {
    quizLoading.value = false
  }
}

// 监听标签页变化，按需加载数据
watch(activeTab, (newTab) => {
  if (newTab === 'progress' && chapterProgress.value.length === 0) {
    fetchChapterProgress()
  } else if (newTab === 'quizzes' && quizData.value.length === 0) {
    fetchQuizData()
  }
})

// 计算属性
const scoreComponents = computed(() => {
  // 从描述中提取百分比
  const desc = scoreDetails.value.description || ''
  const percentages = {
    dailyScore: extractPercentage(desc, '平时作业'),
    quizScore: extractPercentage(desc, '平时测试'),
    meetingScore: extractPercentage(desc, '考勤成绩'),
    finalExamScore: extractPercentage(desc, '期末考试')
  }
  
  return [
    { 
      name: '平时成绩', 
      value: scoreDetails.value.dailyScore, 
      percentage: percentages.dailyScore || 30,
      description: getDescription('平时成绩')
    },
    { 
      name: '平时测试', 
      value: scoreDetails.value.quizScore, 
      percentage: percentages.quizScore || 30,
      description: getDescription('平时测试成绩')
    },
    { 
      name: '考勤成绩', 
      value: scoreDetails.value.meetingScore, 
      percentage: percentages.meetingScore || 10,
      description: getDescription('考勤成绩')
    },
    { 
      name: '期末考试', 
      value: scoreDetails.value.finalExamScore, 
      percentage: percentages.finalExamScore || 30,
      description: getDescription('期末考试成绩')
    }
  ]
})

// 辅助函数：从描述中提取百分比
const extractPercentage = (desc, keyword) => {
  const match = desc.match(new RegExp(`${keyword}(\\d+)%`))
  return match ? parseInt(match[1]) : null
}

// 辅助函数：获取特定成绩类型的描述
const getDescription = (scoreType) => {
  const item = apiResponses.value.scoreResponse?.result?.find(
    i => i['scoreType(成绩类型)'] === scoreType
  )
  return item?.['description(描述)'] || ''
}


const progressPercentage = computed(() => {
  return Math.round((courseProgress.value.completedChapters / courseProgress.value.totalChapters) * 100) || 0
})

const progressColor = computed(() => 
  progressPercentage.value >= 80 ? '#67C23A' : 
  progressPercentage.value >= 50 ? '#409EFF' : '#E6A23C'
)

// 工具方法
const formatDuration = (seconds) => {
  if (!seconds) return '--'
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const getChapterStatus = (chapter) => {
  if ((chapter.videoProgress || 0) >= 100 && (chapter.quizProgress || 0) >= 100) return '已完成'
  if ((chapter.videoProgress || 0) > 0 || (chapter.quizProgress || 0) > 0) return '进行中'
  return '未开始'
}

const getScoreClass = (score) => {
  if (score >= 90) return 'score-excellent'
  if (score >= 80) return 'score-good'
  if (score >= 60) return 'score-pass'
  return 'score-fail'
}

const getScoreColor = (score) => {
  if (score >= 90) return '#67C23A'
  if (score >= 80) return '#409EFF'
  if (score >= 60) return '#E6A23C'
  return '#F56C6C'
}

const getStatusType = (status) => {
  switch(status) {
    case '已完成': return 'success'
    case '进行中': return 'primary'
    case '未开始': return 'info'
    default: return 'warning'
  }
}

onMounted(() => {
  fetchCourseDetail()
  // 默认加载第一个标签页的数据
  fetchChapterProgress()
})
</script>

<style scoped>

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
  color: #666;
}

.loading-icon {
  margin-right: 10px;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 内容样式 */
.course-detail-container {
  padding: 0 20px;
}

.course-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.course-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.meta-info {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #909399;
}

.info-item {
  display: flex;
  align-items: center;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

/* 成绩组成样式 */
.score-composition {
  margin-bottom: 20px;
}

.composition-grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 20px;
}

.total-score {
  display: flex;
  justify-content: center;
  align-items: center;
}

.score-display {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.score-value {
  font-size: 48px;
  font-weight: bold;
  line-height: 1;
}

.score-label {
  font-size: 14px;
  color: #909399;
  margin-top: 8px;
}

.score-excellent {
  color: #67C23A;
}

.score-good {
  color: #409EFF;
}

.score-pass {
  color: #E6A23C;
}

.score-fail {
  color: #F56C6C;
}

.composition-chart {
  display: flex;
  align-items: center;
  gap: 30px;
}

.score-breakdown {
  flex: 1;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px dashed #ebeef5;
}

.breakdown-label {
  color: #606266;
}

.breakdown-value {
  font-weight: 500;
}

/* 学习进度部分 */
.progress-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.progress-content {
  padding: 0 10px;
}

.progress-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.stat-value {
  font-size: 14px;
  color: #606266;
}

.stat-percent {
  font-size: 16px;
  font-weight: bold;
  color: #409EFF;
}

.time-stats {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.time-item {
  display: flex;
  flex-direction: column;
}

.time-label {
  font-size: 12px;
  color: #909399;
}

.time-value {
  font-size: 14px;
  font-weight: 500;
}

.progress-comparison {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.comparison-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.comparison-label {
  font-size: 13px;
  color: #606266;
}

.rank-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.rank-label {
  font-size: 14px;
  color: #606266;
}

.rank-value {
  font-size: 18px;
  font-weight: bold;
  color: #409EFF;
}

/* 标签页样式 */
.detail-tabs {
  margin-top: 20px;
}

.progress-card, .quiz-card {
  margin-top: 10px;
}

/* 进度单元格 */
.progress-cell {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-text {
  min-width: 40px;
  text-align: right;
}

/* 章节详情 */
.chapter-details {
  margin-bottom: 20px;
}

.text-muted {
  color: #c0c4cc;
}
</style>