<template>
  <div class="stat-card">
    <div class="stat-icon" v-if="icon">
      <i :class="icon"></i>
    </div>
    <div class="stat-content">
      <div class="stat-value">{{ formattedValue }}</div>
      <div class="stat-title">{{ title }}</div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  value: {
    type: [Number, String],
    required: true
  },
  icon: {
    type: String,
    default: ''
  }
})

const formattedValue = computed(() => {
  if (typeof props.value === 'number') {
    return props.value.toLocaleString()
  }
  return props.value
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid $border-color;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
  
  .stat-icon {
    margin-bottom: 12px;
    
    i {
      font-size: 24px;
      color: $primary-color;
    }
  }
  
  .stat-content {
    text-align: center;
    
    .stat-value {
      font-size: 28px;
      font-weight: 700;
      color: $primary-color;
      margin-bottom: 8px;
      line-height: 1.2;
    }
    
    .stat-title {
      font-size: 14px;
      color: $text-color;
      font-weight: 500;
      opacity: 0.8;
    }
  }
}

@media (max-width: 768px) {
  .stat-card {
    padding: 16px;
    
    .stat-content {
      .stat-value {
        font-size: 24px;
      }
      
      .stat-title {
        font-size: 12px;
      }
    }
  }
}
</style>
