<!-- src\views\public\course\Course_Center\Chapter\index.vue -->
<template>
  <div class="chapter-container">
    <!-- 左侧导航 - 动态绑定 fixed 类 -->
    <div class="chapter-nav" :class="{ 'fixed': isNavFixed }">
      <div class="nav-header">章节导航</div>
      <nav class="nav-links">
        <a 
          v-for="(chapter, index) in chaptersWithAnchors" 
          :key="index"
          :href="`#chapter-${chapter.id}`"
          class="nav-link"
          :class="{ 'active': activeAnchor === `chapter-${chapter.id}` }"
          @click.prevent="scrollToAnchor(`chapter-${chapter.id}`)"
        >
          {{ `第${chapter.chapterNumber}章 ${chapter.title}` }}
        </a>
      </nav>
    </div>

    <!-- 右侧内容 -->
    <div class="chapter-content-wrapper">
      <div class="chapter-content">
        <div v-if="loading" class="loading">
          <i class="el-icon-loading"></i> 加载中...
        </div>
        
        <div v-else class="chapter-list">
          <ChapterNode
            v-for="(chapter, index) in filteredChapters"
            :key="index"
            :node="chapter"
            :depth="0"
            :search-query="searchQuery"
            :anchor-id="`chapter-${chapter.id}`"
            @preview-resource="handlePreview"
          />
        </div>
        
        <div v-if="!loading && filteredChapters.length === 0" class="no-results">
          <i class="fa fa-search-minus"></i> 没有找到匹配的内容
        </div>

        <!-- 引入文件预览弹窗 -->
        <FilePreviewDialog
          :visible="isPreviewVisible && previewFile" 
          :file="previewFile"
          @close="isPreviewVisible = false"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { getCourseChapters } from '@/api/student/course'
import ChapterNode from './ChapterNode.vue'
import FilePreviewDialog from '@/components/FilePreviewDialog.vue'

const route = useRoute()
const courseId = route.params.courseId

// 数据状态
const loading = ref(true)
const chapters = ref([])
const searchQuery = ref('')
const activeAnchor = ref(null)
const isNavFixed = ref(false)
const scrollThreshold = 600

// 预览相关状态
const isPreviewVisible = ref(false)  // 控制弹窗显示
const previewFile = ref(null)        // 存储当前要预览的文件

// 获取课程章节数据
const fetchChapters = async () => {
  try {
    loading.value = true
    const res = await getCourseChapters(courseId)
    
    if (res.code === 200) {
      // 格式化数据，确保children存在
      console.log('章节数据:', res.result) // 检查每个节点是否有nature属性
      chapters.value = formatChapters(res.result || [])
    } else {
      console.error('获取章节失败:', res.msg)
    }
  } catch (error) {
    console.error('请求章节出错:', error)
  } finally {
    loading.value = false
  }
}

// 格式化章节数据，确保children存在
const formatChapters = (nodes) => {
  return nodes.map(node => ({
    ...node,
    children: node.children ? formatChapters(node.children) : []
  }))
}

// 获取所有有锚点的章节
const chaptersWithAnchors = computed(() => {
  return chapters.value.map(chapter => ({
    id: chapter.id,
    title: chapter.title,
    chapterNumber: chapter.chapterNumber
  }))
})

// 过滤章节列表
const filteredChapters = computed(() => {
  if (!searchQuery.value.trim()) return chapters.value
  
  function searchInNode(node) {
    const isNodeMatch = node.title.toLowerCase().includes(searchQuery.value.toLowerCase())
    
    let childrenMatch = []
    if (node.children && node.children.length > 0) {
      childrenMatch = node.children
        .map(child => searchInNode(child))
        .filter(matched => matched !== null)
    }
    
    if (isNodeMatch || childrenMatch.length > 0) {
      return {
        ...node,
        children: childrenMatch.length > 0 ? childrenMatch : node.children
      }
    }
    
    return null
  }
  
  return chapters.value
    .map(chapter => searchInNode(chapter))
    .filter(chapter => chapter !== null)
})

// 滚动到指定锚点
const scrollToAnchor = (id) => {
  const element = document.getElementById(id)
  if (element) {
    const offset = 150
    const elementPosition = element.getBoundingClientRect().top + window.pageYOffset
    window.scrollTo({
      top: elementPosition - offset,
      behavior: 'smooth'
    })
    activeAnchor.value = id
  }
}

// 监听滚动，更新当前激活的锚点
const handleScroll = () => {
  const scrollY = window.scrollY || window.pageYOffset
  isNavFixed.value = scrollY > scrollThreshold
  updateActiveAnchor()
}

const updateActiveAnchor = () => {
  const chapterElements = chaptersWithAnchors.value.map(chapter => 
    document.getElementById(`chapter-${chapter.id}`)
  ).filter(Boolean)
  
  if (chapterElements.length === 0) return
  
  let closestChapter = null
  let closestDistance = Infinity
  
  chapterElements.forEach(el => {
    const rect = el.getBoundingClientRect()
    const distance = Math.abs(rect.top)
    
    if (distance < closestDistance) {
      closestDistance = distance
      closestChapter = el.id
    }
  })
  
  if (closestChapter) {
    activeAnchor.value = closestChapter
  }
}

// 处理预览请求
const handlePreview = (resource) => {
  previewFile.value = resource  // 保存要预览的资源
  isPreviewVisible.value = true  // 显示弹窗
}

onMounted(() => {
  fetchChapters()
  window.addEventListener('scroll', handleScroll)
  
  // 初始化时检查第一个章节
  if (chaptersWithAnchors.value.length > 0) {
    activeAnchor.value = `chapter-${chaptersWithAnchors.value[0].id}`
  }
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style lang="scss" scoped>
.chapter-container {
  display: flex;
  position: relative;
  background: white;
  border-radius: 8px;
}

/* 默认导航样式（不固定） */
.chapter-nav {
  width: 14vw;
  padding: 16px;
  position: static;
  top: 0;
  left: 0;
  border: #e9ecef 1px solid;
  border-radius: 8px; /* 可选：圆角效果 */
  align-self: flex-start; 
  
  .nav-header {
    font-size: 14px;
    font-weight: 500;
    color: #6c757d;
    margin-bottom: 12px;
    //padding-left: 8px;
    padding-left: 0.45vw;
  }
  
  .nav-links {
    display: flex;
    flex-direction: column;
    gap: 4px;
    
    .nav-link {
      display: block;
      padding: 0px 12px 0px 14px; /* 左内边距16px（留出空间给border） */
      border-left: 4px solid transparent; /* 默认透明边框占位 */
      border-radius: 4px;
      font-size: 14px;
      color: #495057;
      text-decoration: none;
      transition: all 0.2s;
      margin-top: 0.5vw;
      
      &:hover {
        color: #212529;
      }
      
      &.active {
        color: $primary-color;
        font-weight: 500;
        border-left-color: $primary-color; /* 仅激活时显示颜色 */
        padding-left: 12px; /* 激活时左内边距减少（边框向左延伸） */
      }
    }
  }
}

/* 固定导航时的样式 */
.chapter-nav.fixed {
  position: fixed;
  top: 7.2vw;  
  left: 5.6vw;   
  width: 14vw;  
  overflow-y: auto; 
  z-index: 100; 
  background-color: white;
  border: #e9ecef 1px solid;
  border-radius: 8px; 
}

/* 右侧内容 */
.chapter-content-wrapper {
  flex: 1;
  padding: 1vw;
  margin-left: 0;
  min-height: 100vh;
}

/* 当导航固定时，右侧内容添加左边距 */
.chapter-nav.fixed + .chapter-content-wrapper {
  margin-left: 240px;
}

.chapter-content {
  .chapter-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .no-results {
    text-align: center;
    padding: 40px 0;
    color: #6c757d;
    font-size: 14px;
    
    i {
      margin-right: 8px;
    }
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .chapter-container {
    flex-direction: column;
  }
  
  .chapter-nav {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #e9ecef;
    
    &.fixed {
      position: static;
      height: auto;
    }
  }
  
  .chapter-content-wrapper {
    margin-left: 0 !important;
    padding: 16px;
  }
}

.loading {
  padding: 20px;
  text-align: center;
  color: #666;
}

.el-icon-loading {
  margin-right: 8px;
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>