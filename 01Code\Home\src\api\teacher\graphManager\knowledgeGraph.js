// src/api/teacher/graphManager/knowledgeGraph.js

import request from '@/api/service';

// 获取图谱数据的接口
export const createGraph = (data) => {
    return request.post('/knowledge-graph/create', data);  // 替换为实际的 API 路径
};

// 获取课程所有图谱
export const getGraphListByCourse = (courseId) => {
    return request.get('/knowledge-graph/list-by-course', {
        params: { courseId }
    });
};

// 删除图谱
export const deleteGraph = (graphId) => {
    return request.post('/knowledge-graph/delete', null, {
        params: { graphId }
    });
};

// 获取图谱信息
export const getGraphTree = (graphId) => {
    return request.get('/knowledge-graph/tree-view', {
        params: { graphId }
    });
};

// 创建节点
export const createNode = (data) => {
    return request.post('/knowledge-graph/node/create', data);
};

// 删除节点
export const deleteNode = (nodeId) => {
    return request.post('/knowledge-graph/node/delete', null, {
        params: { nodeId }
    });
};

// 更新节点信息
export const updateNodeInfo = (data) => {
    return request.post('/knowledge-graph/node/update', data);
};

// 获取所有标签列表
export const getAllTags = () => {
    return request.get('/knowledge-graph/tags/all');
};

// 关联标签
export const associateTagsToNode = (data) => {
    return request.post('/knowledge-graph/tags/associate', {
        nodeId: data.nodeId,
        tagIds: data.tagIds
    });
};

//获取子节点数量
export const getChildCount = (nodeId) => {
    return request.get('/knowledge-graph/node/child-count', {
        params: { nodeId }
    });
};
// 正确：获取课程资源树
export function getCourseResourceFolderTree(courseId) {
  return request.get('/course/link/folderTree', {
    params: { courseId }
  });
}

// 正确：关联资源
export function batchUpdateNodeResources(data) {
  return request.post('/knowledge-graph/resources/batch-update', data);
}

// 获取节点详情
export function getNodeDetail(nodeId) {
  return request.get('/knowledge-graph/node/detail', {
    params: { nodeId }
  });
}

// 统计节点关联的资源数量
export function countNodeResources(nodeId) {
  return request.get('/knowledge-graph/resources/count', {
    params: { nodeId }
  });
}

// 获取节点已关联的资源列表
export function getNodeResources(nodeId) {
  return request.get('/knowledge-graph/resources/list', {
    params: { nodeId }
  });
}

// 取消资源关联
export function unlinkResourceFromNode(nodeId, resourceId) {
  return request.post('/knowledge-graph/resources/unlink', null, {
    params: { nodeId, resourceId }
  });
}

// 统计子节点数量
export function countChildNodes(nodeId) {
  return request.get('/knowledge-graph/node/child-count', {
    params: { nodeId }
  });
}

