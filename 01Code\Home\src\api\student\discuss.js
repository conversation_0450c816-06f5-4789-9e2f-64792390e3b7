// 学生端评论结接口
import request from '@/api/service'

export function getPostListTree(courseId) {
  return request({
    url: '/post/list/tree',
    method: 'get',
    params: { 
      courseId: courseId
    }
  });
}

export function saveComment(data) {
  return request({
    url: '/comment/save',
    method: 'post',
    data
  })
}

// 点赞相关接口
export function savePostLike(data) {
  return request({
    url: '/post/like/save',
    method: 'post',
    data
  })
}

export function getPostLikeList(params = {}) {
  return request({
    url: '/post/like/list',
    method: 'get',
    params: {
      pageNum: 1,        
      pageSize: 1,      
      ...params         
    }
  })
}

export function removePostLike(data) {
  return request({
    url: '/post/like/remove',
    method: 'post',
    data
  })
}

// 收藏相关接口
export function savePostStar(data) {
  return request({
    url: '/post/star/save',
    method: 'post',
    data
  })
}

export function getPostStarList(params = {}) {
  return request({
    url: '/post/star/list',
    method: 'get',
    params: {
      pageNum: 1,        
      pageSize: 1,      
      ...params         
    }
  })
}

export function removePostStar(data) {
  return request({
    url: '/post/star/remove',
    method: 'post',
    data
  })
}

/* ------------回复相关接口----------------- */


export function saveCommentLike(data) {
  return request({
    url: '/comment/like/save',
    method: 'post',
    data
  })
}

export function getCommentLikeList(params = {}) {
  return request({
    url: '/comment/like/list',
    method: 'get',
    params: {
      pageNum: 1,        
      pageSize: 1,      
      ...params         
    }
  })
}

export function removeCommentLike(data) {
  return request({
    url: '/comment/like/remove',
    method: 'post',
    data
  })
}    

export function removeUserComment(data) {
  return request({
    url: '/comment/user/remove',
    method: 'post',
    data
  })
}    


// 评论收藏相关接口
export function saveCommentStar(data) {
  return request({
    url: '/comment/star/save',
    method: 'post',
    data
  })
}

export function getCommentStarList(params = {}) {
  return request({
    url: '/comment/star/list',
    method: 'get',
    params: {
      pageNum: 1,
      pageSize: 1,
      ...params
    }
  })
}

export function removeCommentStar(data) {
  return request({
    url: '/comment/star/remove',
    method: 'post',
    data
  })
}



