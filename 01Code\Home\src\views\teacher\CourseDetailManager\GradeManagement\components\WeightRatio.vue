<template>
  <div class="score-settings-container">
    <div class="header">
      <div class="total-score-display">
        <span class="label">总成绩</span>
        <span class="score-value">100</span>
        <span class="unit">分</span>
      </div>
      <p class="description">总成绩最高为100分，目前可分配的权重为<span class="highlight">{{ remainingWeight }}</span>分</p>
      <div class="actions">
        <el-button @click="cancelSettings">取消设置</el-button>
        <el-button type="primary" @click="saveSettings">完成设置</el-button>
      </div>
    </div>

    <el-divider />

    <div class="setting-item">
      <div class="setting-label">考勤权重</div>
      <el-input v-model="formData.attendanceWeight" type="number" class="weight-input" :min="0" :max="100"
        @change="validateInput('attendanceWeight')" />
      <span class="unit">分</span>
      <div class="setting-detail">
        <div class="detail-row">
          <span class="detail-label">每次缺勤扣</span>
          <el-input v-model="formData.attendanceDeduction" type="number" class="weight-input" :min="0" :max="100"
            @change="validateInput('attendanceDeduction')" />
          <span class="unit">分</span>
        </div>
        <div class="tip">考勤成绩扣到0为止</div>
      </div>
    </div>

    <div class="setting-item">
      <div class="setting-label">平时权重</div>
      <el-input v-model="formData.regularWeight" type="number" class="weight-input" :min="0" :max="100"
        @change="validateInput('regularWeight')" />
      <span class="unit">分</span>
      <div class="setting-detail">
        <div class="detail-description">平时权重=学习进度权重+互动表现权重</div>
        <div class="detail-tip">详细设置请点击右侧"高级设置"</div>
        <div class="advanced-btn-container">
          <el-button @click="openDialog" class="advanced-btn">高级设置</el-button>
        </div>
      </div>
    </div>

    <div class="setting-item">
      <div class="setting-label">作业权重</div>
      <el-input v-model="formData.assignmentWeight" type="number" class="weight-input" :min="0" :max="100"
        @change="validateInput('assignmentWeight')" />
      <span class="unit">分</span>
      <div class="setting-detail">
        <div class="detail-description">作业成绩=作业平均(加权)成绩 x 权重%</div>
        <div class="detail-tip">未交、未批的作业均算为0分</div>
        <div class="detail-tip">详细分配每份作业权重请点击右侧"高级设置"</div>
        <div class="advanced-btn-container">
          <el-button @click="openAdvancedSettings('homework2')" class="advanced-btn">高级设置</el-button>
        </div>
      </div>
    </div>

    <div class="setting-item">
      <div class="setting-label">考试权重</div>
      <el-input v-model="formData.examWeight" type="number" class="weight-input" :min="0" :max="100"
        @change="validateInput('examWeight')" />
      <span class="unit">分</span>
      <div class="setting-detail">
        <div class="detail-description">考试成绩 = 考试平均(加权)成绩 x 权重%</div>
        <div class="detail-tip">未交、未批的考试均算为0分</div>
        <div class="detail-tip">详细分配每场考试权重请点击右侧"高级设置"</div>
        <div class="advanced-btn-container">
          <el-button @click="homeworkDialog.open()" class="advanced-btn">高级设置</el-button>
        </div>
      </div>
    </div>
  </div>

  <DailyWeightDialog 
  ref="weightDialog" 
  :total-weight="formData.regularWeight"
  :initial-data="{
    learningProgressFullScore: formData.learningProgressWeight,
    learningProgressStartScore: formData.learningProgressThreshold,
    classInteractionFullScore: formData.interactionWeight
  }"
  @confirm="handleDailyWeightConfirm"
/>

  <HomeworkWeightDialog ref="homeworkDialog" @confirm="handleHomeworkWeightsConfirm"
    @cancel="handleHomeworkWeightsCancel" />
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { ElButton, ElDivider, ElMessage } from 'element-plus';
import { getGradeWeightConfig, updateGradeWeightConfig } from '@/api/teacher/grade';
import HomeworkWeightDialog from './HomeworkWeightDialog.vue';
import DailyWeightDialog from './DailyWeightDialog.vue';

const route = useRoute();

// 表单数据
const formData = ref({
  attendanceWeight: 0,
  attendanceDeduction: 0,
  regularWeight: 0,
  learningProgressWeight: 0,
  learningProgressThreshold: 0,
  interactionWeight: 0,
  assignmentWeight: 0,
  examWeight: 0
});

// 对话框引用
const homeworkDialog = ref();
const weightDialog = ref();

// 计算剩余权重
const remainingWeight = computed(() => {
  const totalAllocated = formData.value.attendanceWeight +
    formData.value.regularWeight +
    formData.value.assignmentWeight +
    formData.value.examWeight;
  return 100 - totalAllocated;
});

// 获取成绩权重配置
const fetchGradeWeightConfig = async () => {
  try {
    const { courseId } = route.params;
    const { classId } = route.query;
    const response = await getGradeWeightConfig(courseId, classId);
    if (response.code === 200) {
      formData.value = {
        attendanceWeight: response.result.attendanceWeight,
        attendanceDeduction: response.result.attendanceDeduction,
        regularWeight: response.result.regularWeight,
        learningProgressWeight: response.result.learningProgressWeight,
        learningProgressThreshold: response.result.learningProgressThreshold,
        interactionWeight: response.result.interactionWeight,
        assignmentWeight: response.result.assignmentWeight,
        examWeight: response.result.examWeight
      };
    }
  } catch (error) {
    ElMessage.error('获取成绩权重配置失败');
    console.error('获取成绩权重配置失败:', error);
  }
};



// 组件挂载时获取数据
onMounted(() => {
  fetchGradeWeightConfig();
});

// 打开平时权重高级设置对话框
const openDialog = () => {
  weightDialog.value.open();
};



const handleDailyWeightConfirm = (settings) => {
  formData.value.learningProgressWeight = settings.learningProgressFullScore;
  formData.value.learningProgressThreshold = settings.learningProgressStartScore;
  formData.value.interactionWeight = settings.classInteractionFullScore;
  
  // 计算并更新 regularWeight
  formData.value.regularWeight = settings.learningProgressFullScore + settings.classInteractionFullScore;
  
  ElMessage.success('平时权重设置已保存');
};

// 保存设置
const saveSettings = async () => {
  if (remainingWeight.value !== 0) {
    ElMessage.warning(`请确保所有权重总和为100分，当前还剩 ${remainingWeight.value} 分未分配。`);
    return;
  }

  try {
    const { courseId, classId } = route.params;
    const payload = {
      courseId,
      classId,
      ...formData.value
    };

    const response = await updateGradeWeightConfig(payload);
    if (response.code === 200) {
      ElMessage.success('成绩权重设置已保存！');
    } else {
      ElMessage.error(response.msg || '保存失败');
    }
  } catch (error) {
    ElMessage.error('保存成绩权重设置失败');
    console.error('保存成绩权重设置失败:', error);
  }
};

// 取消设置
const cancelSettings = () => {
  // 重新获取原始数据
  fetchGradeWeightConfig();
  ElMessage.info('设置已取消。');
};

// 其他方法
const handleHomeworkWeightsConfirm = (weights) => {
  console.log('保存的作业权重:', weights);
  ElMessage.success('作业权重已保存');
};

const handleHomeworkWeightsCancel = () => {
  console.log('取消作业权重设置');
};

const validateInput = (field) => {
  // 输入验证逻辑
  const value = formData.value[field];
  if (value < 0) {
    formData.value[field] = 0;
    ElMessage.warning('权重不能小于0');
  } else if (value > 100) {
    formData.value[field] = 100;
    ElMessage.warning('权重不能大于100');
  }
};
</script>

<style lang="scss" scoped>
/* 保持原有的样式不变 */
.score-settings-container {
  padding: 20px;
  max-width: 100%;
  background-color: #fff;
  margin: 0 auto;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;

  .header {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 20px;

    .total-score-display {
      margin-right: 20px;
      display: flex;
      align-items: baseline;

      .label {
        font-size: 24px;
        font-weight: bold;
        color: #333;
        margin-right: 10px;
      }

      .score-value {
        font-size: 36px;
        font-weight: bold;
        color: #409eff;
      }

      .unit {
        font-size: 16px;
        color: #666;
        margin-left: 5px;
      }
    }

    .description {
      color: #999;
      font-size: 14px;
      margin-right: auto;

      .highlight {
        color: #f56c6c;
        font-weight: bold;
      }
    }

    .actions {
      display: flex;
      gap: 10px;
    }
  }

  .el-divider {
    margin-top: 0;
    margin-bottom: 30px;
  }

  .setting-item {
    display: flex;
    align-items: center;
    /* 确保整个项目垂直居中 */
    margin-bottom: 15px;
    background-color: #fff;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    position: relative;

    .setting-label {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      width: 100px;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      /* 标签内容垂直居中 */
      height: 100%;
      /* 继承父元素高度 */
    }

    .weight-input {
      width: 100px;
      margin-right: 10px;
    }

    .unit {
      font-size: 14px;
      color: #666;
      margin-right: 20px;
      display: flex;
      align-items: center;
      /* 单位垂直居中 */
    }

    .setting-detail {
      flex: 1;
      margin-left: 120px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      /* 详情内容垂直居中 */
      min-height: 100%;
      /* 确保高度撑满 */

      .detail-row {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
      }

      .detail-label {
        font-size: 14px;
        color: #666;
        margin-right: 10px;
      }

      .detail-description,
      .detail-tip {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
        line-height: 1.5;
      }

      .detail-tip {
        font-size: 12px;
        color: #999;
      }

      .tip {
        font-size: 12px;
        color: #999;
        margin-top: 8px;
      }

      .advanced-btn-container {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        /* 精确垂直居中 */
        display: flex;
        align-items: center;
      }

      .advanced-btn {
        margin-left: auto;
      }
    }
  }
}
</style>