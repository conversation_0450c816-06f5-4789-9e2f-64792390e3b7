<template>
  <div class="learning-path-container">
    <!-- 顶部操作栏 -->
    <TopToolbar
      :selectedPath="selectedPath"
      :savedPaths="savedPaths"
      @update:selectedPath="selectedPath = $event"
      @path-change="handlePathChange"
      @create-path="showCreateDialog = true"
      @edit-units="openEditUnitsDialog"
    />

    <!-- 学习路径流程图 -->
    <LearningPathFlow
      :savedPaths="savedPaths"
      :selectedPath="selectedPath"
      :currentUnits="currentUnits"
      @create-path="showCreateDialog = true"
      @edit-units="openEditUnitsDialog"
      @edit-nodes="editUnitNodes"
      @delete-unit="deleteUnit"
      @node-click="handleNodeClick"
    />

    <!-- 编辑单元对话框 -->
    <EditUnitsDialog
      v-model="showEditUnitsDialog"
      :units="currentUnits"
      @save-units="handleSaveUnits"
    />

    <!-- 编辑路径节点对话框 -->
    <EditNodesDialog
      v-model="showEditNodesDialog"
      :unitIndex="currentEditingUnitIndex"
      @save-nodes="handleSaveNodes"
    />

    <!-- 创建学习路径对话框 -->
    <CreatePathDialog
      v-model="showCreateDialog"
      @create-path="handleCreatePath"
    />

    <!-- 知识点抽屉 -->
    <RoutePointDrawer
      :isOpen="showRoutePointDrawer"
      :nodeData="selectedNodeData"
      @close="closeRoutePointDrawer"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

// 导入组件
import TopToolbar from './components/TopToolbar.vue'
import LearningPathFlow from './components/LearningPathFlow.vue'
import EditUnitsDialog from './components/EditUnitsDialog.vue'
import EditNodesDialog from './components/EditNodesDialog.vue'
import CreatePathDialog from './components/CreatePathDialog.vue'
import RoutePointDrawer from './components/rightPopup/RoutePointDrawer.vue'

// 控制对话框显示
const showCreateDialog = ref(false)
const showEditUnitsDialog = ref(false)
const showEditNodesDialog = ref(false)

// 控制知识点抽屉显示
const showRoutePointDrawer = ref(false)
const selectedNodeData = ref({})

// 路径选择
const selectedPath = ref('1')

// 临时存储的路径数据
const savedPaths = ref([
  {
    id: '1',
    pathName: '测试学习路径',
    description: '这是一个测试路径',
    units: [
      {
        name: '单元1',
        nodes: [
          { id: 'node-1-1', name: '节点1', active: true, enabled: true, tags: ['基础概念'] },
          { id: 'node-1-2', name: '节点2', active: false, enabled: true, tags: ['进阶知识'] },
          { id: 'node-1-3', name: '节点3', active: false, enabled: true, tags: ['实践应用'] }
        ]
      },
      {
        name: '单元2',
        nodes: [
          { id: 'node-2-1', name: '节点1', active: false, enabled: true, tags: ['高级概念'] },
          { id: 'node-2-2', name: '节点2', active: false, enabled: true, tags: ['综合应用'] }
        ]
      }
    ]
  }
])

// 当前选中路径的单元数据
const currentUnits = ref([
  {
    name: '单元1',
    nodes: [
      { id: 'node-1-1', name: '节点1', active: true, enabled: true, tags: ['基础概念'] },
      { id: 'node-1-2', name: '节点2', active: false, enabled: true, tags: ['进阶知识'] },
      { id: 'node-1-3', name: '节点3', active: false, enabled: true, tags: ['实践应用'] }
    ]
  },
  {
    name: '单元2',
    nodes: [
      { id: 'node-2-1', name: '节点1', active: false, enabled: true, tags: ['高级概念'] },
      { id: 'node-2-2', name: '节点2', active: false, enabled: true, tags: ['综合应用'] }
    ]
  }
])

// 当前编辑的单元索引
const currentEditingUnitIndex = ref(-1)

// 处理创建路径
const handleCreatePath = (newPath) => {
  savedPaths.value.push(newPath)
  selectedPath.value = newPath.id
  currentUnits.value = [...newPath.units]
}

// 处理路径变化
const handlePathChange = (pathId) => {
  const pathData = savedPaths.value.find(path => path.id === pathId)
  if (pathData) {
    currentUnits.value = [...pathData.units]
  } else {
    currentUnits.value = []
  }
}

// 打开编辑单元对话框
const openEditUnitsDialog = () => {
  showEditUnitsDialog.value = true
}

// 处理保存单元
const handleSaveUnits = (units) => {
  currentUnits.value = [...units]

  // 更新选中路径的数据
  if (selectedPath.value) {
    const pathData = savedPaths.value.find(path => path.id === selectedPath.value)
    if (pathData) {
      pathData.units = [...units]
    }
  }
}

// 编辑单元节点
const editUnitNodes = (unitIndex) => {
  currentEditingUnitIndex.value = unitIndex
  showEditNodesDialog.value = true
}

// 处理保存节点
const handleSaveNodes = (data) => {
  const { unitIndex, nodes } = data
  if (unitIndex >= 0 && unitIndex < currentUnits.value.length) {
    currentUnits.value[unitIndex].nodes = [...nodes]

    // 更新选中路径的数据
    if (selectedPath.value) {
      const pathData = savedPaths.value.find(path => path.id === selectedPath.value)
      if (pathData) {
        pathData.units = [...currentUnits.value]
      }
    }
  }
}

// 删除单元
const deleteUnit = (unitIndex) => {
  if (currentUnits.value.length > 1) {
    currentUnits.value.splice(unitIndex, 1)

    // 更新选中路径的数据
    if (selectedPath.value) {
      const pathData = savedPaths.value.find(path => path.id === selectedPath.value)
      if (pathData) {
        pathData.units = [...currentUnits.value]
      }
    }

    ElMessage.success('单元删除成功！')
  } else {
    ElMessage.warning('至少需要保留一个单元！')
  }
}

// 处理节点点击事件
const handleNodeClick = (nodeData) => {
  selectedNodeData.value = nodeData
  showRoutePointDrawer.value = true
}

// 关闭知识点抽屉
const closeRoutePointDrawer = () => {
  showRoutePointDrawer.value = false
  selectedNodeData.value = {}
}

</script>

<style lang="scss" scoped>
.learning-path-container {
  padding: 20px;
  background-color: white;
  min-height: 100vh;
}




</style>