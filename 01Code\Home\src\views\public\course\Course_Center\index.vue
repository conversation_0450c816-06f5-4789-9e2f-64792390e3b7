<!--src\views\public\course\Course_Center\index.vue-->
<template>
  <div class="course-page">
    <!-- course-header 容器保持不变 -->
    <div class="course-header course-detail-wrapper">
      <!-- 视频播放器 -->
      <div class="video-container">
        <video controls width="100%">
          <source src="" type="video/mp4" />
          你的浏览器不支持 video 标签。
        </video>
      </div>

      <!-- 课程基本信息 -->
      <div class="course-info">
        <h1>{{ courseData.name || '课程名称' }}</h1>
        <div class="description">
          <p>教师：{{ teacherNames }}</p>
          <p>学科：{{ courseData.subjectCategory || '未设置' }}</p>
          <p>专业：{{ courseData.major || '未设置' }}</p>
          <p v-if="courseData.introduce">简介：{{ courseData.introduce }}</p>
          <p v-else>简介：暂无课程简介</p>
        </div>
        <button v-if="!isEnrolled && !isLoading" class="enroll-btn" @click="openInvitationModal">
          {{ isEnrolling ? '加入中...' : '加入课程' }}
        </button>

        <!-- 已加入状态提示 -->
        <div v-if="isEnrolled" class="enrolled-tip">
          <i class="fa fa-check-circle"></i> 已加入课程
        </div>
      </div>
    </div>

    <!-- 修改后的标签页 -->
    <div class="tabs course-detail-wrapper">
      <template v-for="tab in tabs" :key="tab.name || tab.title">
        <router-link v-if="tab.name"
          :to="{ name: tab.name, params: { courseId: $route.params.courseId, ...tab.params } }" custom
          v-slot="{ navigate, isActive }">
          <button :class="{ active: isActive }" @click="navigate">
            {{ tab.title }}
          </button>
        </router-link>
        <button v-else @click="tab.onClick">
          {{ tab.title }}
        </button>
      </template>
    </div>

    <!-- 标签页内容 -->
    <div class="tab-content course-detail-wrapper">
      <router-view></router-view>
    </div>

    <!-- 邀请码输入弹窗 -->
    <div v-if="showInvitationModal" class="invitation-modal">
      <div class="modal-content">
        <h3>输入邀请码</h3>
        <div class="form-group">
          <label for="invitationCode">邀请码</label>
          <input type="text" id="invitationCode" v-model="invitationCode" placeholder="请输入邀请码">
        </div>
        <div class="modal-actions">
          <button @click="closeInvitationModal">取消</button>
          <button @click="submitInvitationCode" :disabled="isEnrolling">确认</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { getCourseDetail } from '@/api/public/course/course'
import { enrollCourse } from '@/api/student/course'
import { useCourseStore } from '@/stores/courseStore'



const route = useRoute()
const courseStore = useCourseStore()
const isLoading = ref(true)
const courseData = ref({})
const teachers = ref([])
const isEnrolled = ref(false)      // 是否已加入课程
const isEnrolling = ref(false)     // 是否正在加入
const showInvitationModal = ref(false) // 邀请码弹窗显示状态
const invitationCode = ref('')     // 邀请码输入值

const tabs = [
  {
    title: '课程概述',
    name: 'course-overview',
    params: {}
  },
  { 
    title: '教学团队', 
    name: 'teaching-team', 
    params: { courseId: route.params.courseId } // 传递课程 ID
  },
  {
    title: '课程设计',
    name: 'course-design',
    params: {}
  },
  {
    title: '课程章节',
    name: 'course-chapter',
    params: {}
  },
  {
    title: '知识图谱',
    name: 'course-knowledge-graph',
    params: {}
  },
  { 
    title: '问题图谱', 
    name: 'course-question-graph',
    params: {}
  },
  { 
    title: '能力图谱', 
    name: 'course-ability-graph',
    params: {}
  },
  { 
    title: '学习路径', 
    name: 'course-learning-path',
    params: {}
  },
  {
    title: '教学资源',
    name: 'course-resources',
    params: {}
  },
  {
    title: '教学互动',
    name: 'course-discuss',
    params: {}
  },
  {
    title: '课程评审',
    name: 'course-review',
    params: {}
  }
];


// 计算教师名字列表
const teacherNames = computed(() => {
  if (!teachers.value.length) return '暂无教师信息'
  return teachers.value.map(t => t.teacherName).join('、')
})

// 获取课程详情
const fetchCourseDetail = async () => {
  try {
    isLoading.value = true
    const res = await getCourseDetail(route.params.courseId)

    if (res.code === 200) {
      courseData.value = res.result.course || {}
      teachers.value = res.result.teachers || []
      // 检查是否已加入课程
      isEnrolled.value = courseData.value.isJoined || false

      courseStore.setCourseEnrollmentStatus(courseData.value.isJoined)
      courseStore.setCurrentCourseId(route.params.courseId)
    } else {
      console.error('获取课程详情失败:', res.msg)
    }
  } catch (error) {
    console.error('请求课程详情出错:', error)
  } finally {
    isLoading.value = false
  }
}


// 打开邀请码弹窗
const openInvitationModal = () => {
  if (isEnrolling.value || isEnrolled.value) return
  showInvitationModal.value = true
  invitationCode.value = '' // 清空输入框
}

// 关闭邀请码弹窗
const closeInvitationModal = () => {
  showInvitationModal.value = false
}

// 提交邀请码
const submitInvitationCode = async () => {
  if (!invitationCode.value.trim()) {
    alert('请输入邀请码')
    return
  }

  try {
    isEnrolling.value = true
    closeInvitationModal() // 关闭弹窗

    // 调用加入课程API，传入邀请码
    const res = await enrollCourse({
      courseId: route.params.courseId,
      invitationCode: invitationCode.value.trim()
    })

    if (res.code === 200) {
      isEnrolled.value = true

      alert('加入课程成功！')
      fetchCourseDetail() // 刷新页面数据
    } else {
      console.error('加入课程失败:', res.msg)
      alert(res.msg || '邀请码错误或已过期，请重试')

    }
  } catch (error) {
    console.error('加入课程请求出错:', error)
    alert('网络错误，请稍后重试')
  } finally {
    isEnrolling.value = false
  }
}

onMounted(() => {
  fetchCourseDetail()
})
</script>

<style lang="scss" scoped>
// 版心
.course-detail-wrapper {
  width: 90vw;
  background: white;
  padding: 1.042vw;
  border-radius: 0.313vw;
  box-shadow: 0 0.052vw 0.208vw rgba(0, 0, 0, 0.1);
  margin: 1.042vw auto;
}

.course-page {
  font-family: 'Segoe UI', sans-serif;
  min-height: 100vh;
  padding: 8vw 1.563vw;
  background-image: url('@/assets/img/General/background-gradient.png');
  background-repeat: no-repeat;
  background-size: 100% auto;
  background-position: top;
  background-attachment: fixed;


  .logo {
    font-size: 1.042vw;
    font-weight: bold;
  }

  .nav-links {
    list-style: none;
    display: flex;
    gap: 0.833vw;

    a {
      color: white;
      text-decoration: none;
    }
  }
}

.course-header {
  padding: 2vw;
  display: flex;
  gap: 1.25vw;

  .video-container {
    flex: 1;
    border-radius: 0.417vw;
    overflow: hidden;
  }

  .course-info {
    flex: 1;
    padding-left: 1.042vw;
    padding-top: 1.042vw;

    h1 {
      margin: 0.521vw 0;
      font-size: 2vw;
      font-weight: bold;
    }

    .description {
      margin: 1.042vw 0;
      color: #343333;
      font-size: 1vw;

      p {
        margin: 1vw 0;
      }
    }

    // 加入课程按钮样式
    .enroll-btn {
      background-color: #4CAF50;
      color: white;
      border: none;
      padding: 0.5vw 1vw;
      font-size: 1vw;
      border-radius: 0.2vw;
      cursor: pointer;
      transition: background-color 0.3s;
      margin-top: 1vw;

      &:hover {
        background-color: #45a049;
      }

      &:disabled {
        background-color: #cccccc;
        cursor: not-allowed;
      }
    }

    // 已加入提示样式
    .enrolled-tip {
      color: #4CAF50;
      font-size: 1vw;
      margin-top: 1vw;
      display: flex;
      align-items: center;

      .fa-check-circle {
        margin-right: 0.5vw;
      }
    }
  }

  &>* {
    width: 50%;
  }
}

// 修正后的标签页样式
.tabs {
  display: flex;
  gap: 1.5vw; // 增加标签间距
  padding: 0vw 5vw; // 增加垂直内边距
  border-bottom: 1px solid #f0f0f0; // 添加底部边框线
  justify-content: space-between;
  align-items: center;
  height: 5vw;

  button {
    padding: 0.5vw 0; // 减少水平内边距
    border: none;
    background-color: transparent; // 移除背景色
    cursor: pointer;
    font-size: 1vw;
    color: $text-color; // 使用变量定义的文字颜色
    transition: all 0.3s ease;
    position: relative; // 为下划线定位做准备

    &:hover {
      font-size: 1.2vw;
    }

    &.active {
      font-weight: 600; // 字重变大
      font-size: 1.2vw; // 字号变大
      color: $text-color; // 保持文字颜色

      // 添加底部下划线
      &::after {
        content: '';
        position: absolute;
        bottom: 0.1vw; // 定位到容器底部
        left: 0;
        width: 100%;
        height: 0.3vw;
        background-color: $button-hover; // 浅蓝色下划线
        border-radius: 0.3vw;
      }
    }
  }

  a {
    text-decoration: none;
    color: inherit;
  }
}

.tab-content {}

/* 邀请码弹窗样式 */
.invitation-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  padding: 2vw;
  border-radius: 0.5vw;
  width: 30vw;
  max-width: 90%;
}

.modal-content h3 {
  margin-top: 0;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 1vw;
  margin-bottom: 1.5vw;
}

.form-group {
  margin-bottom: 1.5vw;
}

.form-group label {
  display: block;
  margin-bottom: 0.5vw;
  font-weight: bold;
}

.form-group input {
  width: 100%;
  padding: 0.5vw;
  border: 1px solid #ddd;
  border-radius: 0.2vw;
  box-sizing: border-box;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1vw;
  margin-top: 1.5vw;
}

.modal-actions button {
  padding: 0.5vw 1vw;
  border: none;
  border-radius: 0.2vw;
  cursor: pointer;
  font-size: 0.9vw;
}

.modal-actions button:first-child {
  background-color: #f0f0f0;
  color: #333;
}

.modal-actions button:last-child {
  background-color: #4CAF50;
  color: white;
}
</style>