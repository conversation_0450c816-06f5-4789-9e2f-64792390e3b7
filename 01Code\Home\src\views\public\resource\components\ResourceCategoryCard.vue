<template>
  <div class="category-section">
    <!-- 标题在方框外部 -->
    <h2 v-if="title" class="section-title">{{ title }}</h2>
    
    <!-- 内容方框 -->
    <div class="category-container">
      <div class="panel-grid">
        <slot>
          <!-- 默认插槽内容 -->
          <div 
            v-for="item in items" 
            :key="item.id" 
            class="course-card"
            @click="handleCardClick(item)"
          >
            <div class="course-image">
              <img :src="item.image" :alt="item.title">
            </div>
            <div class="course-info">
              <h3 class="course-title">{{ item.title }}</h3>
              <p class="course-subtitle">{{ item.subtitle }}</p>
            </div>
          </div>
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  title: String,
  items: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['cardClick'])

const handleCardClick = (item) => {
  emit('cardClick', item)
}
</script>

<style scoped lang="scss">
$border-radius: 2vw; 
$border: 2px solid #8a6de3;
$box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
$title-color: #8a6de3;

.category-section {
  margin-bottom: 2vw;
}

/* 外部标题样式 */
.section-title {
  font-size: 1.25vw;
  color: $title-color;
  margin: 0 0 0.8vw 0;
  padding-bottom: 0.5vw;
  border-bottom: 1px solid #eaeaea;
}

.category-container {
  padding: 1vw;
  border: $border;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
}

.panel-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(15.63vw, 1fr));
  gap: 1.25vw;
}

.course-card {
  border-radius: 0.42vw; 
  overflow: hidden;
  box-shadow: 0 0.1vw 0.42vw rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-0.26vw);
  }
}

.course-image {
  height: 11.25vw;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.course-info {
  padding: 0.83vw;
}

.course-title {
  font-size: 0.83vw; 
  margin-bottom: 0.42vw;
  color: #333;
}

.course-subtitle {
  font-size: 0.73vw; 
  color: #666;
}
</style>