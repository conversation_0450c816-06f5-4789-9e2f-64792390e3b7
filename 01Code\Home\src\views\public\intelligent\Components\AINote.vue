<template>
  <div class="note-container">
    <div class="headers">
      <h1>代码智能分析</h1>
      <p>为代码提供专业分析</p>
    </div>

    <div class="code-input-section">
      <div class="language-selector">
        <label for="language">编程语言:</label>
        <select id="language" v-model="selectedLanguage">
          <option value="python">Python</option>
          <option value="javascript">JavaScript</option>
          <option value="java">Java</option>
          <option value="cpp">C++</option>
          <option value="csharp">C#</option>
          <option value="go">Go</option>
          <option value="php">PHP</option>
          <option value="ruby">Ruby</option>
          <option value="swift">Swift</option>
          <option value="kotlin">Kotlin</option>
          <option value="rust">Rust</option>
          <option value="typescript">TypeScript</option>
        </select>
      </div>

      <div class="mode-toggle">
        <button
          :class="['mode-btn', { active: selectedMode === 'comment' }]"
          @click="selectedMode = 'comment'"
        >
          代码注释
        </button>
        <button
          :class="['mode-btn', { active: selectedMode === 'evaluation' }]"
          @click="selectedMode = 'evaluation'"
        >
          代码评价
        </button>
      </div>

      <div class="input-container">
        <textarea
          v-model="codeInput"
          placeholder="粘贴您的代码..."
          @keyup.ctrl.enter="generateContent"
        ></textarea>
        <button @click="generateContent" :disabled="isGenerating" class="generate-btn">
          <span v-if="isGenerating" class="loading-dots">生成中...</span>
          <span v-else>{{ selectedMode === 'comment' ? '生成注释' : '生成评价' }}</span>
        </button>
      </div>
    </div>

    <!-- 侧边栏 -->
    <div class="side-panel" :class="{ active: showSidePanel }">
      <div class="panel-header">
        <h3 v-if="selectedMode === 'comment'">嵌入注释后的代码</h3>
        <h3 v-else>代码评价结果</h3>
        <button @click="togglePanel">×</button>
      </div>

      <div class="panel-content">
        <template v-if="selectedMode === 'comment'">
          <pre><code>{{ annotatedCode }}</code></pre>
        </template>
        <div v-else class="evaluation-content">
          <pre>{{ evaluationResult }}</pre>
        </div>
      </div>
      <div class="panel-actions" >
        <button @click="copyCode">复制代码</button>
        <button @click="downloadCode">下载代码</button>
      </div>
    </div>
  </div>
</template>

<script>
import { generateCodeComment, getCodeEvaluation } from '../../../../api/student/intelligent/note.js';

export default {
  data() {
    return {
      codeInput: '',
      selectedLanguage: 'python',
      annotatedCode: '',
      evaluationResult: {},
      isGenerating: false,
      showSidePanel: false,
      selectedMode: 'comment',
    };
  },
  methods: {
    async generateContent() {
      if (!this.codeInput.trim()) {
        alert('请输入代码内容');
        return;
      }

      this.isGenerating = true;
      try {
        if (this.selectedMode === 'comment') {
          // 生成注释
          const comments = await generateCodeComment(this.codeInput, this.selectedLanguage);
          this.annotatedCode = comments;
        } else if (this.selectedMode === 'evaluation') {
          // 生成评价
          this.evaluationResult = await getCodeEvaluation(this.codeInput, this.selectedLanguage);
        }
        this.showSidePanel = true;
      } catch (error) {
        console.error('生成失败:', error);
        alert(`${this.selectedMode === 'comment' ? '注释' : '评价'}生成失败，请稍后重试`);
      } finally {
        this.isGenerating = false;
      }
    },
    copyCode() {
      const content = this.selectedMode === 'comment' ? this.annotatedCode : this.evaluationResult;
      if (!content) return;
      navigator.clipboard.writeText(content);
      alert('内容已复制到剪贴板');
    },
    downloadCode() {
      const content = this.selectedMode === 'comment' ? this.annotatedCode : this.evaluationResult;
      if (!content) return;
      const blob = new Blob([content], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${this.selectedMode === 'comment' ? 'annotated-code' : 'code-evaluation'}-${Date.now()}.${this.selectedLanguage}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    },
    togglePanel() {
      this.showSidePanel = !this.showSidePanel;
    },
  },
};
</script>

<style scoped>
.note-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  position: relative;
}

.headers {
  text-align: center;
  margin-bottom: 20px;
}

.headers h1 {
  color: #333;
  margin-bottom: 5px;
}

.headers p {
  color: #666;
}

.code-input-section {
  margin-bottom: 20px;
}

.language-selector {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.language-selector label {
  margin-right: 10px;
}

.language-selector select {
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #ddd;
  flex-grow: 1;
}

.mode-toggle {
  display: flex;
  margin-left: 10px;
}

.mode-btn {
  padding: 8px 16px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  margin-left: 10px;
}

.mode-btn.active {
  background-color: #956ef6;
  color: white;
  border-color: #956ef6;
}

.input-container {
  position: relative;
}

textarea {
  width: 100%;
  height: 300px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  resize: none;
  font-family: monospace;
  font-size: 14px;
}

.generate-btn {
  position: absolute;
  right: 10px;
  bottom: 10px;
  padding: 8px 16px;
  background-color: #956ef6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.generate-btn:hover {
  background-color: #d2c8ea;
}

.side-panel {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100%;
  background-color: white;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  transition: right 0.3s ease;
  z-index: 100;
  display: flex;
  flex-direction: column;
}

.side-panel.active {
  right: 0;
}

.panel-header {
  padding: 15px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-content {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
}

.panel-content pre {
  margin: 0;
  font-family: monospace;
  white-space: pre-wrap;
  line-height: 1.5;
  background-color: #f8f8f8;
  padding: 10px;
  border-radius: 4px;

}

.evaluation-content {
  line-height: 1.6;
}

.panel-actions {
  padding: 15px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.panel-actions button {
  padding: 8px 12px;
  background-color: #f0f0f0;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.panel-actions button:hover {
  background-color: #e0e0e0;
}

/* 添加评价结果的样式 */
.evaluation-content pre {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.5;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
  max-height: 800px;
}
</style>
