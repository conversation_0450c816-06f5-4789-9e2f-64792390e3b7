// src\api\teacher\question.js

import request from '@/api/service'

/**
 * 获取教师所有题目
 * @param {Object} params - 参数对象（如：{ isPublic: true } 或 { courseId: 12355667654 }）
 */
export function getMyQuestions(params) {
    return request.get('/question/my-questions', { params })
}
/**
 * 获取教师课程题库的题目
 * @param {Object} params - 参数对象（如：{ isPublic: true } 或 { courseId: 12355667654 }）
 */
export function getCourseQuestions(params) {
    return request.get('/question/my-questions', { params })
}

/**
 * 导入题目数据Excel文件
 * @param {File} file 直接传文件对象
 */
export function importQuestionFile(file) {
    const formData = new FormData();
    formData.append('file', file);

    return request.post('/question/import', formData, {
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    });
}


/**
 * 获取试卷分页列表（公共题库或课程试卷库）
 * @param {Object} params - 例：{ isPublic: true }、{ courseId: 'xxx' }
 */
export function getExamPage(params) {
    return request.get('/exam/page', { params })
}

/**
 * 根据课程获取该课程下的试卷
 * @param {string} courseId
 */
export function getExamByCourse(courseId) {
    return request.get('/exam/byCourse', { params: { courseId } })
}

/**
 * 获取实践项目分页列表（公共题库 or 课程）
 * @param {Object} params - 例：{ isPublic: 0/1 } 或 { courseId: 'xxx' }
 */
export function getPracticeProjectList(params) {
    return request.get('/teacher/practice-project/list', { params })
}

/**
 * 获取当前教师课程列表（用于课程下拉菜单）
 */
export function getTeacherCourses() {
    return request.get('/course/teacher')
}


/**
 * 获取单选题详情
 * @param {Object} params - 参数对象，如 { id: '题目ID' }
 */
export function getSingleChoiceDetail(params) {
    return request.get('/question/single/get', { params })
}

/**
 * 获取多选题详情
 * @param {Object} params - 参数对象，如 { id: '题目ID' }
 */
export function getMultipleChoiceDetail(params) {
    return request.get('/question/multiple/get', { params })
}

/**
 * 获取判断题详情
 * @param {Object} params - 参数对象，如 { id: '题目ID' }
 */
export function getTrueFalseDetail(params) {
    return request.get('/question/truefalse/get', { params })
}

/**
 * 获取填空题详情
 * @param {Object} params - 参数对象，如 { id: '题目ID' }
 */
export function getFillBlankDetail(params) {
    return request.get('/question/blank/get', { params })
}

/**
 * 获取问答题详情
 * @param {Object} params - 参数对象，如 { id: '题目ID' }
 */
export function getShortAnswerDetail(params) {
    return request.get('/question/short/get', { params })
}

/**
 * 获取整套试卷详情（用于预览试卷）
 * @param {Object} params - 参数对象，如 { id: '试卷ID' }
 */
export function getExamDetail(params) {
    return request.get('/exam/detail', { params })
}

/**
 * 获取实践项目详情（用于预览）
 * @param {string} id - 实践项目 ID
 */
export function getPracticeProjectDetail(id) {
    return request.get(`/teacher/practice-project/detail/${id}`)
}

/**
 * 添加单选题
 * @param {Object} data - 题目数据（content, options, correct, teacherId, isPublic）
 */
export function saveSingleChoice(data) {
    return request.post('/question/single/save', data)
}

/**
 * 添加多选题
 * @param {Object} data - 题目数据（content, options, correct, teacherId, isPublic）
 */
export function saveMultipleChoice(data) {
    return request.post('/question/multiple/save', data)
}

/**
 * 添加判断题
 * @param {Object} data - 题目数据（content, answer, teacherId, isPublic）
 */
export function saveTrueFalse(data) {
    return request.post('/question/truefalse/save', data)
}

/**
 * 添加填空题
 * @param {Object} data - 题目数据（content, answers, teacherId, isPublic）
 */
export function saveFillBlank(data) {
    return request.post('/question/blank/save', data)
}

/**
 * 添加问答题
 * @param {Object} data - 题目数据（content, sampleAnswer, teacherId, isPublic）
 */
export function saveShortAnswer(data) {
    return request.post('/question/short/save', data)
}

/**
 * 创建空试卷
 * @param {Object} data 
 */
export function createExam(data) {
    return request.post('/exam/save', data)
}

/**
* 关联试卷与题目
* @param {Object} data 
*/
export function saveExamRelations(data) {
    return request.post('/exam/relation/batch-save', data)
}

/**
 * 更新考试信息（如标题）
 * @param {Object} data 
 * @returns {Promise}
 */
export function updateExam(data) {
    return request.post('/exam/update', data)
}

/**
* 上传实践项目文件
* @param {FormData} formData - 包含 file 字段
*/
export function uploadPracticeProjectFile(formData) {
    return request.post('/teacher/practice-project/uploadProjectFile', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
    })
}

/**
* 创建实践项目
* @param {Object} data 
*/
export function createPracticeProject(data) {
    return request.post('/teacher/practice-project/create', data)
}

