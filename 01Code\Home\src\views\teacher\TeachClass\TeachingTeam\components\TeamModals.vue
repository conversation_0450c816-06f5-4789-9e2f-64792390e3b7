<template>
  <div v-if="activeModal === 'add-member'" class="modal-overlay">
    <div class="modal">
      <h3>添加成员</h3>
      <div class="search-container">
        <input
          type="text"
          v-model="searchQuery.teacherName"
          placeholder="请输入姓名"
          class="search-input"
        />
        <input
          type="text"
          v-model="searchQuery.teaCode"
          placeholder="请输入工号"
          class="search-input"
        />
        <input
          type="text"
          v-model="searchQuery.department"
          placeholder="请输入单位名称"
          class="search-input"
        />
        <button @click="searchTeachers" class="search-button">搜索</button>
      </div>
      <div class="teacher-list">
        <div
          v-for="teacher in teachers"
          :key="teacher.teacherId"
          class="teacher-item"
          @click="selectTeacher(teacher)"
        >
          <img :src="teacher.teacherImg" alt="" />
          <div>
            <p>{{ teacher.teacherName }}</p>
            <p>{{ teacher.teacherTitle }}</p>
          </div>
        </div>
      </div>
      <div class="buttons">
        <button @click="confirmAddMember" :disabled="!selectedTeacher">确认添加</button>
        <button @click="closeModals">取消</button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue';
import { getTeacherList, createTeachingTeam } from '@/api/teacher/courseTeam';

export default {
  props: {
    activeModal: String,
    selectedTeam: Object,
  },
  setup(props, { emit }) {
    const searchQuery = ref({
      teacherName: '',
      teaCode: '',
      department: ''
    });
    const teachers = ref([]);
    const selectedTeacher = ref(null);
    const pageNum = ref(1); // 默认页数
    const pageSize = ref(5); // 默认每页显示数量

    const searchTeachers = async () => {
      const params = {
        pageNum: pageNum.value,
        pageSize: pageSize.value,
      };
      if (searchQuery.value.teacherName) params.teacherName = searchQuery.value.teacherName;
      if (searchQuery.value.teaCode) params.teaCode = searchQuery.value.teaCode;
      if (searchQuery.value.department) params.department = searchQuery.value.department;

      try {
        const response = await getTeacherList(params);
        if (response && response.result && Array.isArray(response.result.records)) {
          teachers.value = response.result.records;
        } else {
          console.error('Invalid response structure');
          alert('搜索失败，请稍后再试');
        }
      } catch (error) {
        console.error('搜索教师失败:', error);
        alert('搜索失败，请稍后再试');
      }
    };

    const selectTeacher = (teacher) => {
      selectedTeacher.value = teacher;
    };

    const confirmAddMember = async () => {
      if (!selectedTeacher.value) {
        alert('请选择一个教师');
        return;
      }
      try {
        const data = {
          courseId: props.selectedTeam.courseId,
          teacherId: selectedTeacher.value.id,
          teamRole: 2, // 默认角色为教师，可以根据需要调整
        };
        console.log('Sending data:', data); // 打印发送数据
        await createTeachingTeam(data);
        emit('confirm');
        selectedTeacher.value = null; // 清空选择
        searchQuery.value = { teacherName: '', teaCode: '', department: '' }; // 清空搜索框
        teachers.value = []; // 清空教师列表
        alert('添加成员成功');
        emit('add-member-success'); // 发射添加成员成功的事件
      } catch (error) {
        console.error('添加成员失败:', error);
        alert('添加成员失败，请稍后再试');
      }
    };

    const closeModals = () => {
      emit('close');
      selectedTeacher.value = null; // 清空选择
      searchQuery.value = { teacherName: '', teaCode: '', department: '' }; // 清空搜索框
      teachers.value = []; // 清空教师列表
    };

    return {
      searchQuery,
      teachers,
      selectedTeacher,
      searchTeachers,
      selectTeacher,
      confirmAddMember,
      closeModals,
    };
  },
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal {
  background: white;
  padding: 20px;
  border-radius: 8px;
  width: 80%;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.search-input {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  flex: 1;
}

.search-button {
  padding: 8px 16px;
  background-color: #4accf3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.teacher-list {
  max-height: 300px;
  overflow-y: auto;
}

.teacher-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #ccc;
  cursor: pointer;
}

.teacher-item img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
}

.buttons {
  display: flex;
  justify-content: space-between;
  color: rgb(255, 255, 255);
  border: none;
  border-radius: 4px;
  margin-top: 20px;
}
</style>