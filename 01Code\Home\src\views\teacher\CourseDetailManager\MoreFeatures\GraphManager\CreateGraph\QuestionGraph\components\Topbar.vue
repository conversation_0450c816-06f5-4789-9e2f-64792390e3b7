<template>
  <header class="top-bar">
    <!-- 左侧返回 -->
    <div class="left-group">
      <button class="icon-btn" @click="$emit('goBack')" title="返回">
        <img :src="backIcon" alt="返回" class="icon" />
        <span>返回</span>
      </button>
    </div>

    <!-- 中间工具栏 -->
    <div class="center-group">
      <button
        class="icon-btn"
        :class="{ disabled: disabledInsert }"
        @click="$emit('insertChild')"
        :disabled="disabledInsert"
        title="插入下级">
        <img :src="insertChildIcon" alt="插入下级" class="icon" />
        <span>插入下级</span>
      </button>
      <button
        class="icon-btn"
        :class="{ disabled: disabledDelete || isRootNode }"
        @click="handleDeleteNode"
        :disabled="disabledDelete || isRootNode"
        title="删除节点">
        <img :src="deleteIcon" alt="删除节点" class="icon" />
        <span>删除节点</span>
      </button>
    </div>

    <!-- 右侧开关 -->
    <div class="right-group">
      <label class="switch-label">
        节点详情
        <input
          type="checkbox"
          :checked="modelValue"
          @change="$emit('update:showDetail', $event.target.checked)" />
        <span class="slider"></span>
      </label>
    </div>
  </header>
</template>

<script setup>
import { computed } from "vue";
import { useQuestionGraphStore } from "@/stores/teacher/graphManager/questionGraphStore";

const props = defineProps({
  modelValue: Boolean,
  disabledInsert: Boolean,
  disabledDelete: Boolean,
  selectedNodeId: [String, Number],
});

const emit = defineEmits([
  "goBack",
  "insertChild",
  "deleteNode",
  "update:showDetail",
]);

const store = useQuestionGraphStore();

const isRootNode = computed(() => {
  return (
    props.selectedNodeId === "root" || props.selectedNodeId === store.rootNodeId
  );
});

const handleDeleteNode = () => {
  if (isRootNode.value) return;
  emit("deleteNode");
};

import backIcon from "@/assets/courseMap-icon/createKgGraph/topbar/back.svg";
import insertChildIcon from "@/assets/courseMap-icon/createKgGraph/topbar/insert-child.svg";
import deleteIcon from "@/assets/courseMap-icon/createKgGraph/topbar/delete.svg";
</script>

<style lang="scss" scoped>
.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  padding: 0 20px;
  border-bottom: 1px solid #eee;
  background-color: #fff;

  .icon-btn {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    border: none;
    background: none;
    padding: 6px 10px;
    cursor: pointer;
    font-size: 14px;
    color: #333;

    span {
      font-size: 16px;
    }

    &:hover {
      background-color: #f5f5f5;
      border-radius: 4px;
    }

    .icon {
      width: 20px;
      height: 20px;
    }

    &.disabled {
      opacity: 0.4;
      cursor: not-allowed;
      pointer-events: none;
      filter: grayscale(100%);
    }
  }

  .center-group {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .right-group {
    display: flex;
    align-items: center;
  }

  .switch-label {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #555;
    gap: 6px;
    position: relative;

    input {
      width: 36px;
      height: 20px;
      appearance: none;
      background: #ccc;
      border-radius: 10px;
      position: relative;
      outline: none;
      transition: background 0.3s;
      cursor: pointer;

      &:checked {
        background: #4c7bff;
      }

      &::after {
        content: "";
        width: 16px;
        height: 16px;
        background: #fff;
        position: absolute;
        top: 2px;
        left: 2px;
        border-radius: 50%;
        transition: left 0.3s;
      }

      &:checked::after {
        left: 18px;
      }
    }
  }
}
</style>
