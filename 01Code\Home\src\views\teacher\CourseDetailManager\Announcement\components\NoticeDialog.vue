<template>
  <!-- 公告详情弹窗 -->
  <div v-if="visible" class="notice-dialog-overlay" @click.self="closeDialog">
    <div class="notice-dialog">
      <div class="dialog-header">
        <h2>{{ currentNotice.title }}</h2>
        <button class="close-btn" @click="closeDialog">&times;</button>
      </div>
      <div class="dialog-meta">
        <span class="notice-type">{{ noticeTypeLabel(currentNotice.noticeType) }}</span>
        <span class="notice-time">{{ formatDate(currentNotice.publishTime) }}</span>
      </div>
      <div class="dialog-content" v-html="sanitizedContent"></div>
      <div class="dialog-footer">
        <button v-if="editable" @click="handleEdit" class="edit-btn">编辑</button>
        <button v-if="editable" @click="handleDelete" class="delete-btn">删除</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import DOMPurify from 'dompurify'
import { formatTime } from '@/utils/dateUtils.js' 

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  notice: {
    type: Object,
    required: true,
    default: () => ({
      title: '',
      content: '',
      noticeType: 0,
      publishTime: null
    })
  },
  editable: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'edit', 'delete'])

const currentNotice = computed(() => props.notice)

const sanitizedContent = computed(() => {
  return DOMPurify.sanitize(currentNotice.value.content || '', {
    ALLOWED_TAGS: ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'img', 'br', 'div', 'span', 'a', 'strong', 'em', 'ul', 'ol', 'li'],
    ALLOWED_ATTR: ['src', 'alt', 'href', 'target']
  })
})

const closeDialog = () => {
  emit('update:visible', false)
}


const formatDate = (timestamp) => {
  if (!timestamp) return '无日期信息' 
  return formatTime(timestamp) 
}



const noticeTypeLabel = (type) => {
  const labels = {
    0: '课程公告',
    1: '作业通知',
    2: '考试通知'
  }
  return labels[type] || '公告'
}

const handleEdit = async () => {
  try {
    emit('edit', currentNotice.value)
    closeDialog()
  } catch (error) {
    console.error('编辑失败:', error)
    alert('编辑操作失败，请重试')
  }
}

const handleDelete = () => {
  if (confirm('确定要删除这条公告吗？')) {
    emit('delete', currentNotice.value.id)
    closeDialog()
  }
}
</script>

<style lang="scss" scoped>
.notice-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.notice-dialog {
  width: 600px;
  max-width: 90vw;
  max-height: 80vh;
  min-height: 300px; // 添加最小高度
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  .dialog-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
    
    h2 {
      margin: 0;
      font-size: 18px;
      color: #333;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 80%;
    }
    
    .close-btn {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: #999;
      padding: 0;
      line-height: 1;
      
      &:hover {
        color: #333;
      }
    }
  }
  
  .dialog-meta {
    padding: 8px 20px;
    background-color: #f5f5f5;
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #666;
    flex-shrink: 0;
    
    .notice-type {
      color: #1890ff;
      padding: 2px 6px;
      background-color: #e6f7ff;
      border-radius: 2px;
    }
  }
  
  .dialog-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    min-height: 150px; // 内容区域最小高度
  }
  
  .dialog-footer {
    padding: 12px 20px;
    border-top: 1px solid #e8e8e8;
    text-align: right;
    flex-shrink: 0;
    
    button {
      padding: 6px 16px;
      margin-left: 8px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    }
    
    .edit-btn {
      background-color: #1890ff;
      color: white;
      border: 1px solid #1890ff;
      
      &:hover {
        background-color: #40a9ff;
      }
    }
    
    .delete-btn {
      background-color: #fff;
      color: #ff4d4f;
      border: 1px solid #ff4d4f;
      
      &:hover {
        background-color: #fff1f0;
      }
    }
  }
}
</style>

<style lang="scss">
.notice-dialog {
  .dialog-content {
    p {
      margin: 0.5em 0;
      line-height: 1.6;
    }
    
    h1, h2, h3, h4, h5, h6 {
      margin: 1em 0 0.5em 0;
      font-weight: bold;
    }
    
    h1 {
      font-size: 1.5em;
    }
    
    h2 {
      font-size: 1.3em;
    }
    
    img {
      max-width: 100%;
      height: auto;
      margin: 10px 0;
      border-radius: 4px;
    }
    
    a {
      color: #1890ff;
      text-decoration: none;
    }
    
    ul, ol {
      padding-left: 1.5em;
      margin: 0.5em 0;
    }
    
    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
      
      &:hover {
        background: #a8a8a8;
      }
    }
  }
}
</style>