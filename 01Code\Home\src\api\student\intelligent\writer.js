import OpenAI from 'openai'
import { getKey } from '../../key.js'

const deepseekApiKey = getKey('deepseek')

export const createWritingContent = async (params) => {
  const openai = new OpenAI({
    baseURL: 'https://api.deepseek.com',
    apiKey: deepseek<PERSON>pi<PERSON>ey,
    dangerouslyAllowBrowser: true
  })

  try {
    const completion = await openai.chat.completions.create({
      model: 'deepseek-chat',
      messages: params.messages,
      temperature: 0.7,
      max_tokens: params.max_tokens || 3000,
      stream: false
    })

    // 获取原始内容
    let content = completion.choices[0].message.content
    // 过滤掉 ## 和 ** 符号
    content = content.replace(/\*\*/g, '').replace(/## /g, '')

    return content
  } catch (error) {
    console.error('API Error:', error)
    throw new Error('生成内容时发生错误，请检查网络连接后重试')
  }
}
