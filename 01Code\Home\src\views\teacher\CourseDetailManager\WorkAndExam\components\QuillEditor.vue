<template>
  <div ref="editorContainer" class="quill-editor"></div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import Quill from 'quill';
import 'quill/dist/quill.snow.css';

export default {
  name: 'QuillEditor',
  props: {
    modelValue: {
      type: String,
      default: '',
    },
  },
  setup(props, { emit }) {
    const editorContainer = ref(null);
    let quill;

    onMounted(() => {
      quill = new Quill(editorContainer.value, {
        theme: 'snow',
        modules: {
          toolbar: [
            [{ 'header': [1, 2, false] }],
            ['bold', 'italic', 'underline'], //加粗.
            [{ 'color': [] }, { 'background': [] }], //颜色、背景
            [{ 'list': 'ordered' }, { 'list': 'bullet' }], //列表
            ['blockquote'], //代码段
            ['link', 'image'], //图片、链接
            ['clean'], //清除样式
          ],
        },
      });

      quill.on('text-change', () => {
        emit('update:modelValue', quill.root.innerHTML);
      });

      // 设置初始值
      quill.root.innerHTML = props.modelValue;
    });

    watch(
      () => props.modelValue,
      (newValue) => {
        if (quill.root.innerHTML !== newValue) {
          quill.root.innerHTML = newValue;
        }
      }
    );

    onBeforeUnmount(() => {
      quill = null;
    });

    return {
      editorContainer,
    };
  },
};
</script>

<style scoped>
.quill-editor {
  height: 200px;
  /* 自定义高度 */
}
</style>
