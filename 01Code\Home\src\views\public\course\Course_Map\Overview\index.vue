<!-- src\views\public\course\Course_Map\Overview\index.vue -->
<template>
  <!-- 课程概述全页 -->
  <div class="overview-full-page">
    <!-- 课程数据加载完成后显示内容 -->
    <div class="content-container" v-if="courseData">
      <!-- 内容头部背景 -->
      <div class="content-header-background">
        <!-- 头部内容 -->
        <div class="header-content">
          <h1>{{ courseData.title }}</h1>
          <p>{{ courseData.university }}</p>
          <p>{{ courseData.teachers }}</p>
        </div>
      </div>

      <!-- 主要内容包装器 -->
      <div class="main-content-wrapper">
        <!-- 标签书签 -->
        <div class="tab-bookmarks">
          <div 
            v-for="(item, index) in navItems" 
            :key="item.key"
            class="bookmark-tab"
            :class="{ active: activeIndex === index, hover: hoverIndex === index }"
            @click="handleNavClick(index, item.key)"
            @mouseenter="hoverIndex = index"
            @mouseleave="hoverIndex = null"
          >
            <span class="tab-label">{{ item.label }}</span>
          </div>
        </div>

        <!-- 内容主体 -->
        <div class="content-main">
          <!-- 知识图谱 -->
          <knowledge-graph 
            v-if="activeTab === 'knowledge'" 
            :course-title="courseData.title"
            :graph-id="graphId"
          />
          <!-- 问题图谱 -->
          <h2 v-else-if="activeTab === 'problem'">问题图谱</h2>
          <!-- 能力图谱 -->
          <h2 v-else-if="activeTab === 'ability'">能力图谱</h2>
        </div>
      </div>
    </div>
    <!-- 课程数据加载中显示加载提示 -->
    <div v-else class="loading-container">
      加载中...
    </div>
  </div>
</template>

<script setup>

import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import KnowledgeGraph from '@/views/public/course/Course_Map/components/knowledge-graph.vue'

// 获取当前路由信息
const route = useRoute()
// 当前激活的标签索引
const activeIndex = ref(0)
// 当前激活的标签键
const activeTab = ref('knowledge')
// 鼠标悬停的标签索引
const hoverIndex = ref(null)
// 课程数据
const courseData = ref(null)

// 从路由参数获取图谱ID
const graphId = computed(() => {
  return route.params.graphId || '1388283778706706432' // 默认图谱ID
})

// 导航标签项
const navItems = [
  { key: 'knowledge', label: '知识图谱' },
  { key: 'problem', label: '问题图谱' },
  { key: 'ability', label: '能力图谱' }
]

// 组件挂载后模拟加载课程数据
onMounted(() => {
  setTimeout(() => {
    courseData.value = {
      title: '黄河治理与文化传承',
      university: '郑州大学',
      teachers: '吴漫,王博,李嘉,黄亮'
    }
  }, 300)
})

// 处理导航标签点击事件
const handleNavClick = (index, tabKey) => {
  activeIndex.value = index
  activeTab.value = tabKey
}
</script>

<style lang="scss" scoped>
.overview-full-page {
  position: relative;
  width: 100%;
  overflow-x: hidden;
  background-color: #f8f9fa;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 18px;
  color: #8a6de3;
}

.content-container {
  position: relative;
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
}

.content-header-background {
  position: relative;
  width: 100%;
  height: 300px;
  background: linear-gradient(135deg, #8a6de3 0%, #6a4fc7 100%);
  border-radius: 0 0 20px 20px;
  overflow: hidden;
  
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 20%),
      radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.1) 0%, transparent 20%);
    animation: ripple 8s infinite ease-in-out;
  }
  
  .header-content {
    position: relative;
    padding: 60px 100px 40px;
    color: white;
    z-index: 1;
    
    h1 {
      color: white;
      margin-bottom: 8px;
      font-size: 24px;
      text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    p {
      color: rgba(255, 255, 255, 0.9);
      margin-bottom: 4px;
      font-size: 14px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
  }
}

@keyframes ripple {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.main-content-wrapper {
  position: relative;
  background: rgb(243, 241, 245);
  border-radius: 20px 20px 0 0;
  margin-top: -40px;
  padding: 80px 100px 20px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 2;
}

.tab-bookmarks {
  position: absolute;
  top: 45px;
  left: 110px;
  display: flex;
  height: 50px;
  z-index: -1;
  
  .bookmark-tab {
    position: relative;
    height: 50px;
    padding: 0 30px;
    margin-right: 2px;
    background-color: #e0d6ff;
    border-radius: 8px 8px 0 0;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: flex-end;
    justify-content: center;
    box-shadow: 0 -3px 5px rgba(0, 0, 0, 0.08);
    
    .tab-label {
      opacity: 1;
      transform: translateY(-10px);
      transition: all 0.3s ease;
      color: #5a4b8a;
      font-weight: 500;
      font-size: 14px;
      white-space: nowrap;
      margin-bottom: 10px;
    }
    
    &.active {
      height: 60px;
      background-color: #8a6de3;
      transform: translateY(-18px);
      
      .tab-label {
        opacity: 1;
        transform: translateY(-12px);
        color: white;
      }
    }
    
    &.hover:not(.active) {
      height: 50px;
      background-color: #cbb8ff;
      
      .tab-label {
        opacity: 0.8;
        transform: translateY(-12px);
      }
    }
  }
}

.content-main {
  background: white;
  border-radius: 8px;
  padding: 30px;
  min-height: 70vh; /* 改为视口高度的70% */
  height: 100vh; /* 或者使用计算高度 */
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #eee;
  z-index: 999;
  overflow: hidden; /* 确保内容不会溢出 */

  h2 {
    color: #8a6de3;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
  }
}
</style>