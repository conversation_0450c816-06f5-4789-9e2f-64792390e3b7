<template>
    <div class="my-exam-page">
        <div class="exam-list">
            <div class="exam-card" v-for="exam in exams" :key="exam.id">
                <div class="semester">学期：{{ exam.semester }}</div>
                <div class="title">课程名称：{{ exam.courseName }} — {{ exam.title }}</div>
                <div class="date">考试日期：{{ exam.examDate }}</div>
                <div class="time">考试时间：{{ exam.startTime }} - {{ exam.endTime }}</div>
            </div>
        </div>

        <el-pagination v-if="totalExams > pageSize" :current-page="page" :page-size="pageSize" :total="totalExams"
            @current-change="handlePageChange" layout="prev, pager, next, jumper" />
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import {
    getAvailableExams,
    getExamDetail,
    getCourseById
} from '@/api/student/exam-assignment/studentExam'

const exams = ref([])
const page = ref(1)
const pageSize = ref(6)
const totalExams = ref(0)

function formatDate(dateStr) {
    const d = new Date(dateStr)
    return `${d.getFullYear()}年${d.getMonth() + 1}月${d.getDate()}日`
}

function formatTime(dateStr) {
    const d = new Date(dateStr)
    return `${String(d.getHours()).padStart(2, '0')}:${String(d.getMinutes()).padStart(2, '0')}`
}

async function fetchMyExams() {
    try {
        const res = await getAvailableExams({ page: page.value, size: pageSize.value })
        const examList = res.result.records || []
        totalExams.value = res.result.total || 0

        const enrichedExams = await Promise.all(
            examList.map(async (exam) => {
                const detailRes = await getExamDetail({ id: exam.id })
                const { courseName, courseId, startTime, endTime } = detailRes.result || detailRes

                const courseRes = await getCourseById({ id: courseId })
                const semester = courseRes.result?.semester || ''

                return {
                    id: exam.id,
                    title: exam.title,
                    courseName,
                    courseId,
                    semester,
                    examDate: formatDate(startTime), // 用开始时间的日期做考试日期
                    startTime: formatTime(startTime),
                    endTime: formatTime(endTime)
                }
            })
        )

        exams.value = enrichedExams
    } catch (error) {
        console.error('获取考试数据错误:', error)
    }
}

function handlePageChange(newPage) {
    page.value = newPage
    fetchMyExams()
}

onMounted(() => {
    fetchMyExams()
})
</script>
  
<style scoped>
.exam-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-items: center;
    /* 横向居中 */
    /* 卡片之间间距 */
}

.exam-card {
    width: 500px;
    /* 自定义宽度 */
    min-height: 140px;
    /* 自定义最小高度 */
    padding: 16px;
    border-radius: 10px;
    background-color: rgb(253, 253, 253);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    text-align: center;
}

.semester {
    font-weight: bold;
    margin-bottom: 6px;
    color: #3a3a3a;
}

.title {
    margin: 6px 0;
    font-size: 16px;
    color: #333;
}

.date,
.time {
    font-size: 14px;
    color: #666;
}
</style>
  