import request from '@/api/service'
import axios from 'axios';

/**
 * 获取教学团队列表
 * @param {Object} params 查询参数
 * @param {string} params.teacherId 教师ID
 * @param {number} [params.teamRole] 团队角色(1创建者)
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页数量
 */
export function getTeachingTeams(params) {
  return request({
    url: '/courseTeacher/teaching-teams',
    method: 'GET',
    params
  })
}

// 获取团队详情
export const getTeamDetail = (params) => {
  return request({
    url: '/courseTeacher/get',
    method: 'get',
    params
  })
}

// 获取课程教学团队
export function getCourseTeachers(courseId) {
  return request({
    url: '/courseTeacher/course/info',
    method: 'get',
    params: { courseId },
   
  })
}

//获取教师分页列表
export const getTeacherList = (params) => {
  return request({
    url: '/teacher/list',
    method: 'GET',
    params,
  });
};


/**
 * 解散教学团队
 * @param {string} courseId 课程ID
 */
export function disbandTeam(courseId) {
  return request({
    url: '/courseTeacher/disband',
    method: 'POST',
    params: { courseId } // 将courseId作为查询参数发送
  })
}



export function removeTeacherFromTeam(courseId, teacherId) {
  return request({
    url: '/courseTeacher/remove/teacher',
    method: 'POST',
    params: {  // 使用params作为查询参数
      courseId: courseId,  // 注意参数名要与API文档一致
      teacherId: teacherId // 注意参数名要与API文档一致
    }
  });
}

/**
 * 创建教学团队或添加教师成员
 * @param {Object} data - 请求数据
 * @param {string} data.id - 团队ID
 * @param {string} data.courseId - 课程ID
 * @param {string} data.teacherId - 教师ID
 * @param {number} data.createdTime - 创建时间
 * @param {number} data.teamRole - 团队角色
 */
export function createTeachingTeam(data) {
  return request({
    url: '/courseTeacher/save',
    method: 'POST',
    data: data,
  });
}


// 获取课程详情及教师团队信息
export function getCourseDetail(params) {
  return request({
    url: '/course/detail',
    method: 'GET',
    params
  });
}


