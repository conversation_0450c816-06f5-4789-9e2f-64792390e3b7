<template>
  <li :class="{
    'folder-item': true,
    'expanded': folder.expanded,
  }">
    <div class="folder-header" :class="{ 'selected': selectedFolderId === folder.id }" @click="handleClick">
      <span class="folder-icon" v-if="folder.children && folder.children.length > 0" @click.stop="handleToggle">
        <span class="toggle-icon">{{ folder.expanded ? '▼' : '▶' }}</span>
      </span>
      <span class="folder-name">{{ folder.name }}</span>
    </div>

    <!-- 子文件夹列表 -->
    <ul v-if="folder.expanded && folder.children && folder.children.length > 0" class="children-folder">
      <TreeNode v-for="child in folder.children" :key="child.id" :folder="child" :selectedFolderId="selectedFolderId"
        @select="$emit('select', $event)" @toggle="$emit('toggle', $event)" />
    </ul>
  </li>
</template>

<script setup>
const props = defineProps({
  folder: {
    type: Object,
    required: true
  },
  selectedFolderId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['select', 'toggle'])

const handleClick = (e) => {
  e.stopPropagation() // 阻止事件冒泡
  emit('select', props.folder.id)

}

const handleToggle = (e) => {
  e.stopPropagation()
  emit('toggle', props.folder.id)
}
</script>

<style scoped lang="scss">
.folder-item {
  padding: 8px 0;
  margin-left: 16px;
  position: relative;
  cursor: pointer;


  .folder-header {
    display: flex;
    align-items: center;

    &:hover {
      background-color: #f5f7fa;
    }

    &.selected {
      background-color: #e6f7ff;
    }

    .folder-icon {
      margin-right: 8px;
      width: 16px;
      text-align: center;
      color: #409eff;
    }

    .folder-name {
      flex: 1;
      font-weight: 500;
    }
  }

  .children-folder {
    margin-left: 24px;
    padding-left: 0;
    list-style: none;
  }


}
</style>