<!--src\components\Layout\SidebarLayout.vue-->
<template>
  <div class="sidebar">
    <nav>
      <router-link
        v-for="item in navItems"
        :to="{ path: item.path }" 
        :key="item.path"
        class="nav-item"
        :class="{ active: $route.path.includes(item.path) }" 
      >
        <i :class="item.icon || 'fa fa-circle-o'" class="nav-icon"></i>
        <span>{{ item.title }}</span>
      </router-link>
    </nav>
  </div>
</template>

<script setup>
defineProps({
  navItems: {
    type: Array,
    required: true,
    default: () => []
  }
})
</script>

<style scoped lang="scss">
.sidebar {
  padding: 20px 0;
  height: 100%;
  
  .nav-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #333;
    text-decoration: none;
    transition: background-color 0.2s;
    
    .nav-icon {
      width: 24px;
      text-align: center;
      margin-right: 10px;
      color: #888;
    }
    
    &:hover {
      background-color: #f5f5f5;
    }
    
    &.active {
      background-color: #e9f5ff;
      color: #0d6efd;
      font-weight: 500;
      
      .nav-icon {
        color: #0d6efd;
      }
    }
  }
}
</style>
    