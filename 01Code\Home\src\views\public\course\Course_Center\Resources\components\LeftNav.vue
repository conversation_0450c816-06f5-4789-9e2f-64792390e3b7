<template>
  <nav class="left-nav">
    <div v-for="tab in tabs" :key="tab" :class="{ 'active nav-item': currentTab === tab }" class="nav-item"
      @click="updateCurrentTab(tab)">
      {{ tab }}
    </div>
  </nav>
</template>

<script setup>
import { computed, defineProps, defineEmits } from 'vue'

// 定义props
const props = defineProps({
  tabs: {
    type: Array,
    required: true
  },
  modelValue: {
    type: String,
    required: true
  }
})

// 定义emits
const emits = defineEmits(['update:modelValue'])

// 使用v-model的双向绑定
const currentTab = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emits('update:modelValue', value)
  }
})

// 更新当前选中标签的方法
const updateCurrentTab = (tab) => {
  currentTab.value = tab
}
</script>

<!-- <style lang="scss" scoped>
// SCSS变量定义
$border-radius: 4px;
$box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
$border-color: #ddd;
$border-color-light: #eee;
$bg-color: white;
$bg-color-hover: #f0f0f0;
$bg-color-active: #fff;
$text-color-active: #303133;

.left-nav {
  position: sticky;
  top: 20px;
  height: fit-content;
  width: 200px;
  background: $bg-color;
  overflow-y: auto;
  border-radius: $border-radius;
  border: 1px solid $border-color-light; 
  box-shadow: $box-shadow;

  .nav-item {
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative; // 为伪元素提供定位参考
    // 防止文本被选中和显示光标
    user-select: none;
    outline: none;

    // 添加悬停时显示的背景效果
    &::before {
      content: '';
      position: absolute;
      top: 50%; // 垂直居中
      left: 50%; // 水平居中
      transform: translate(-50%, -50%) scale(0.95); // 略小于原尺寸
      width: 90%; // 宽度为原尺寸的90%
      height: 80%; // 高度为原尺寸的80%
      background-color: rgba(112, 97, 253, 0.3); // 半透明紫色
      border-radius: $border-radius; // 使用变量保持圆角一致
      opacity: 0; // 初始不可见
      transition: all 0.2s ease; // 添加过渡效果
      pointer-events: none; // 防止干扰导航项的点击事件
    }

    // 悬停状态
    &:hover {

      // 显示并轻微放大背景效果
      &::before {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
      }
    }

    // 激活状态
    &.active {
      background: $bg-color-active;
      color: $primary-color;
      border-left: 3px solid $primary-color;
      font-weight: bold;
    }
  }
}
</style> -->


<style lang="scss" scoped>
// SCSS变量定义（仅px单位转换）
$border-radius: 0.208vw; /* 4px → 0.208vw */
$box-shadow: 0 0.052vw 0.208vw rgba(0, 0, 0, 0.1); /* 1px→0.052vw, 4px→0.208vw */
$border-color: #ddd;
$border-color-light: #eee;
$bg-color: white;
$bg-color-hover: #f0f0f0;
$bg-color-active: #fff;
$text-color-active: #303133;

.left-nav {
  position: sticky;
  top: 1.042vw; /* 20px → 1.042vw */
  height: fit-content;
  width: 10.417vw; /* 200px → 10.417vw */
  background: $bg-color;
  overflow-y: auto;
  border-radius: $border-radius;
  border: 0.052vw solid $border-color-light; /* 1px → 0.052vw */
  box-shadow: $box-shadow;

  .nav-item {
    padding: 0.625vw 0.833vw; /* 12px→0.625vw, 16px→0.833vw */
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    user-select: none;
    outline: none;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) scale(0.95);
      width: 90%;
      height: 80%;
      background-color: rgba(112, 97, 253, 0.3);
      border-radius: $border-radius;
      opacity: 0;
      transition: all 0.2s ease;
      pointer-events: none;
    }

    &:hover::before {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }

    &.active {
      background: $bg-color-active;
      color: $primary-color;
      border-left: 0.156vw solid $primary-color; /* 3px → 0.156vw */
      font-weight: bold;
    }
  }
}
</style>