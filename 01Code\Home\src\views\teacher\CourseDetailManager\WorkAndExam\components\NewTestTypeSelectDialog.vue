<!-- src\views\teacher\CourseDetailManager\WorkAndExam\components\NewTestTypeSelectDialog.vue -->
<template>
    <el-dialog :model-value="modelValue" @update:modelValue="val => emit('update:modelValue', val)" title="选择试题创建方式"
        width="400px" :close-on-click-modal="false">
        <div style="margin-bottom: 16px; font-size: 15px; color: #606266;">
            请选择如何创建试题：
        </div>

        <el-row gutter="12">
            <el-col :span="12">
                <el-button type="primary" icon="Plus" style="width: 100%;" @click="onSingle">
                    单个创建
                </el-button>
            </el-col>
            <el-col :span="12">
                <el-button type="success" icon="Upload" style="width: 100%;" @click="onBatch">
                    Excel 导入
                </el-button>
            </el-col>
        </el-row>
    </el-dialog>
</template>
  
<script setup>
import { Plus, Upload } from '@element-plus/icons-vue'

defineProps({
    modelValue: Boolean
})
const emit = defineEmits(['update:modelValue', 'batch', 'single'])

const onSingle = () => {
    emit('update:modelValue', false)
    emit('single')
}

const onBatch = () => {
    emit('update:modelValue', false)
    emit('batch')
}
</script>
  