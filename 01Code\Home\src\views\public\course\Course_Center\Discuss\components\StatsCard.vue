<template>
  <div class="stats-wrapper">
    <h1>在线互动问答</h1>
    <div class="stats-container">
      <div class="stat-box light-blue">
        <img src="" alt="图标1">
        <h2>话题总数</h2>
        <p>628,374个</p>
      </div>
      <div class="stat-box light-green">
        <img src="" alt="图标2">
        <h2>话题参与总人数</h2>
        <p>23,485人次</p>
      </div>
      <div class="stat-box pink">
        <img src="" alt="图标3">
        <h2>话题参与总校次</h2>
        <p>151次</p>
      </div>
      <div class="stat-box light-pink">
        <img src="" alt="图标4">
        <h2>对话总轮数</h2>
        <p>580,197次</p>
      </div>
    </div>
  </div>
</template>

<script setup>
// 可以在这里引入图标或其他需要的资源
</script>

<style lang="scss">
.stats-wrapper {
  margin: 50px auto;
  padding: 25px;
  max-width: 95%;
  border-radius: 8px;
  background-color: #b0a7a7;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stats-container {
  display: flex;
  justify-content: space-between;

}

.stat-box {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  text-align: center;
  width: 23%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-box img {
  width: 40px;
  height: 40px;
  margin-bottom: 15px;
}

.stat-box h2 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.stat-box p {
  margin: 10px 0;
  color: #666;
  font-size: 22px;
  font-weight: bold;
}

/* 特定颜色样式 */
.light-blue {
  background-color: #e6f7ff;
  color: #1890ff;
}

.light-green {
  background-color: #f6ffed;
  color: #52c41a;
}

.pink {
  background-color: #fff0f6;
  color: #eb2f96;
}

.light-pink {
  background-color: #fff1f2;
  color: #f5222d;
}
</style>