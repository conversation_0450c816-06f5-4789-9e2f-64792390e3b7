<template>
  <div class="image-editor-container">
    <div class="top-section">
      <h1>图像编辑</h1>
      <p>智能图像处理，一键美化升级</p>
    </div>

    <div class="action-buttons">
      <button
        class="editor-btn"
        :class="{ active: currentOperation === 'globalStyle' }"
        @click="currentOperation = 'globalStyle'"
      >
        全局风格化
      </button>
      <button
        class="editor-btn"
        :class="{ active: currentOperation === 'localStyle' }"
        @click="currentOperation = 'localStyle'"
      >
        局部风格化
      </button>
      <button
        class="editor-btn"
        :class="{ active: currentOperation === 'edit' }"
        @click="currentOperation = 'edit'"
      >
        指令编辑
      </button>
      <button
        class="editor-btn"
        :class="{ active: currentOperation === 'redraw' }"
        @click="currentOperation = 'redraw'"
      >
        局部重绘
      </button>
      <button
        class="editor-btn"
        :class="{ active: currentOperation === 'removeText' }"
        @click="currentOperation = 'removeText'"
      >
        去文字水印
      </button>
      <button
        class="editor-btn"
        :class="{ active: currentOperation === 'expand' }"
        @click="currentOperation = 'expand'"
      >
        扩图
      </button>
      <button
        class="editor-btn"
        :class="{ active: currentOperation === 'colorize' }"
        @click="currentOperation = 'colorize'"
      >
        图像上色
      </button>
      <button
        class="editor-btn"
        :class="{ active: currentOperation === 'sketch' }"
        @click="currentOperation = 'sketch'"
      >
        线稿生图
      </button>
    </div>

    <div class="upload-section">
      <div class="upload-area" @click="triggerUpload">
        <input type="file" @change="handleFileChange" ref="uploadInput" accept="image/*" style="display: none" />
        <template v-if="!uploadedImage">
          <p>点击或拖放图片到此处</p>
          <p class="hint">支持JPG、PNG等常见格式</p>
        </template>
        <img :src="uploadedImage" v-else class="preview-image" />
      </div>
    </div>

    <div class="input-container">
      <textarea
        v-model="promptText"
        placeholder="输入你的问题..."
        @keyup.enter.prevent="handleSend"
      ></textarea>
      <button @click="handleSend" class="generate-btn" >

        <span  class="arrow-icon">⮞</span>
      </button>
    </div>

    <div class="result-section" v-if="generatedImage">
      <div class="result-header">
        <h2>编辑结果</h2>
        <div class="result-actions">
          <button @click="previewImage" class="preview-btn">放大预览</button>
          <button @click="downloadImage" class="download-btn">下载图片</button>
        </div>
      </div>
      <div class="result-image-container">
        <img :src="generatedImage" alt="编辑后的图片">
      </div>
    </div>

    <div class="preview-modal" v-if="showPreview">
      <div class="preview-content">
        <span class="close-preview" @click="closePreview">&times;</span>
        <img :src="generatedImage" alt="放大预览">
      </div>
    </div>
  </div>
</template>

<script>
import { submitEditTask, getTaskResult } from '../../../../api/student/intelligent/image.js';
import axios from "axios";

export default {
  data() {
    return {
      uploadedImage: '',
      generatedImage: '',
      promptText: '',
      showPreview: false,
      currentOperation: '',
      taskInProgress: false,
      taskId: null,
    };
  },

  methods: {
    triggerUpload() {
      this.$refs.uploadInput.click();
    },

    handleFileChange(event) {
      const file = event.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = (e) => {
        this.uploadedImage = e.target.result;
        this.generatedImage = '';
        this.taskInProgress = false;
        this.taskId = null;
      };
      reader.readAsDataURL(file);
    },

    async handleSend() {
      if (!this.uploadedImage) {
        alert('请先上传图片');
        return;
      }

      if (!this.currentOperation) {
        alert('请选择一个操作');
        return;
      }

      if (!this.promptText.trim()) {
        alert('请输入提示词');
        return;
      }

      try {
        this.taskInProgress = true;
        console.log('开始上传图片...'); // 调试日志

        // 转换Base64为Blob
        const blob = await fetch(this.uploadedImage).then(r => r.blob());
        const formData = new FormData();
        formData.append('file', blob, 'upload.jpg');

        // 上传URL应该是Nginx代理的地址
        const uploadResp = await axios.post('http://localhost:8092/api/images/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });

        console.log('上传响应:', uploadResp); // 调试日志
        const imageUrl = uploadResp.data;
        console.log('获取到的图片URL:', imageUrl); // 调试日志

        // 2. 用这个URL调用阿里云API
        console.log('提交编辑任务...', {
          imageUrl,
          prompt: this.promptText,
          operation: this.currentOperation
        });
        this.taskId = await submitEditTask(imageUrl, this.promptText, this.currentOperation);
        console.log('任务ID:', this.taskId);

        await this.pollTaskStatus();
      } catch (error) {
        console.error('图像处理失败:', error); // 详细错误日志
        if (error.response) {
          console.error('错误响应数据:', error.response.data);
          console.error('错误状态码:', error.response.status);
          console.error('错误头信息:', error.response.headers);
        } else if (error.request) {
          console.error('无响应:', error.request);
        }
        alert(`图像处理失败: ${error.message}`);
      } finally {
        this.taskInProgress = false;
      }
    },

    async pollTaskStatus() {
      let attempts = 0;
      const maxAttempts = 30; // 最大尝试次数
      const interval = 3000;  // 每次查询间隔（毫秒）

      while (attempts < maxAttempts) {
        try {
          const result = await getTaskResult(this.taskId);

          if (result.task_status === 'SUCCEEDED') {
            this.generatedImage = result.results[0].url;
            this.taskInProgress = false;
            return;
          } else if (result.task_status === 'FAILED') {
            throw new Error('任务执行失败');
          }
        } catch (error) {
          console.error('查询任务状态失败:', error);
          throw error;
        }

        attempts++;
        await new Promise(resolve => setTimeout(resolve, interval));
      }

      throw new Error('任务超时');
    },

    previewImage() {
      this.showPreview = true;
    },

    closePreview() {
      this.showPreview = false;
    },

    downloadImage() {
      if (!this.generatedImage) return;

      const link = document.createElement('a');
      link.href = this.generatedImage;
      link.download = `ai-edited-image-${Date.now()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }
};
</script>

<style scoped>
.image-editor-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 10px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.top-section {
  text-align: center;
  margin-bottom: 10px;
}

.top-section h1 {
  font-size: 24px;
  color: #333;
}

.top-section p {
  font-size: 16px;
  color: #666;
}

.upload-section {
  margin-bottom: 5px;
}

.upload-area {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 5px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #956ef6;
}

.upload-area p {
  margin-bottom: 5px;
  color: #666;
}

.hint {
  font-size: 12px;
  color: #999;
}

.preview-image {
  max-width: 100%;
  max-height: 300px;
  border-radius: 8px;
}

.prompt-section {
  margin-bottom: 20px;
}

textarea {
  width: 100%;
  height: 100px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  resize: none;
  font-size: 14px;
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 10px;
  margin-bottom: 20px;
}

.editor-btn {
  padding: 10px;
  background-color: #d2c8ea;
  color: black;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.editor-btn:hover {
  background-color: #ffffff;
}

.editor-btn.active {
  background-color: #956ef6;
  color: white;
}

.example-section {
  margin-bottom: 20px;
}

.example-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 15px;
}

.example-item {
  text-align: center;
}

.example-item img {
  width: 100%;
  height: 100px;
  object-fit: cover;
  border-radius: 6px;
  margin-bottom: 5px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.result-actions {
  display: flex;
  gap: 10px;
}

.preview-btn, .download-btn {
  padding: 8px 15px;
  background-color: #f0f0f0;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.preview-content {
  position: relative;
  max-width: 90%;
  max-height: 90%;
}

.preview-content img {
  max-width: 100%;
  max-height: 90vh;
  border-radius: 8px;
}

.close-preview {
  position: absolute;
  top: -30px;
  right: -30px;
  color: white;
  font-size: 30px;
  cursor: pointer;
}

.input-container {
  position: relative;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
}

textarea {
  width: 100%;
  height: 100px;
  padding: 15px 20px;
  border: 2px solid #eee;
  border-radius: 12px;
  resize: none;
  font-size: 16px;
  transition: border-color 0.3s;
  background-color: #fafafa;
}

textarea:focus {
  border-color: #956ef6;
  outline: none;
}

.generate-btn {
  position: absolute;
  right: 15px;
  bottom: 15px;
  width: 45px;
  height: 45px;
  background: #956ef6;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s;
}

.generate-btn:hover {
  background-color: #d2c8ea;
}

.arrow-icon {
  color: white;
  font-size: 24px;
}

.loading-dots {
  color: white;
  font-size: 24px;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
