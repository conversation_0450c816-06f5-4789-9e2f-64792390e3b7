<template>
  <div class="question-container">
    <div class="upload-section" v-if="!newQuestions">
      <h2>计算机以题换题</h2>
      <p>请上传包含习题的doc/docx文档，将为您提取知识点并生成新题</p>

      <!-- 题型数量选择 -->
      <div class="question-type-selector">
        <div class="type-inputs">
          <div class="type-input">
            <label for="mcq-count">选择题数量：</label>
            <input type="number" id="mcq-count" v-model.number="mcqCount" min="0" max="10">
          </div>
          <div class="type-input">
            <label for="fill-count">填空题数量：</label>
            <input type="number" id="fill-count" v-model.number="fillCount" min="0" max="10">
          </div>
          <div class="type-input">
            <label for="essay-count">简答题数量：</label>
            <input type="number" id="essay-count" v-model.number="essayCount" min="0" max="10">
          </div>
        </div>

        <div class="upload-area" @dragover.prevent @drop.prevent @drop="handleDrop">
          <input type="file" @change="handleFileChange" accept=".doc,.docx" ref="fileInput" style="display: none" />
          <div @click="triggerFileUpload">
            <p>拖放文件到此处或点击选择文件</p>
            <p class="file-name" v-if="fileName">{{ fileName }}</p>
          </div>
        </div>
      </div>
    </div>

    <div v-if="isLoading" class="loading-overlay">
      <div class="loader"></div>
      <p>正在处理文档...</p>
    </div>

    <div class="error-message" v-if="errorMessage">{{ errorMessage }}</div>

    <div class="content-wrapper" v-if="newQuestions">
      <div class="side-panel">
        <div class="panel-header">
          <h3>生成的新习题</h3>
          <button @click="togglePanel">×</button>
        </div>
        <div class="panel-content">
          <pre>{{ formattedQuestions }}</pre>
          <div class="actions">
            <button @click="copyQuestions">复制题目</button>
            <button @click="downloadQuestions">下载文件</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { processDocumentAndGenerateQuestions } from '../../../../api/student/intelligent/question.js';

export default {
  data() {
    return {
      fileName: '',
      file: null,
      newQuestions: '',
      isLoading: false,
      errorMessage: '',
      mcqCount: 1,
      fillCount: 1,
      essayCount: 1,
      showSidePanel: false
    };
  },
  computed: {
    formattedQuestions() {
      if (!this.newQuestions) return '';

      // 尝试提取知识点
      const knowledgePointsMatch = this.newQuestions.match(/知识点列表：([\s\S]*?)新习题：/);
      const knowledgePoints = knowledgePointsMatch ? knowledgePointsMatch[1].trim() : '';
      // 尝试提取题目部分
      const questionsMatch = this.newQuestions.match(/新习题：([\s\S]*)/);
      const questionsContent = questionsMatch ? questionsMatch[1].trim() : this.newQuestions;
      return `知识点列表：\n${knowledgePoints}\n\n新习题：\n${questionsContent}`;
    },
    // 计算题型数量对象
    questionCounts() {
      return {
        mcqCount: this.mcqCount,
        fillCount: this.fillCount,
        essayCount: this.essayCount
      };
    }
  },
  methods: {
    triggerFileUpload() {
      this.$refs.fileInput.click();
    },
    handleFileChange(event) {
      if (event.target.files.length > 0) {
        this.file = event.target.files[0];
        this.fileName = this.file.name;
        this.processDocument();
      }
    },
    handleDrop(event) {
      if (event.dataTransfer.files.length > 0) {
        const file = event.dataTransfer.files[0];
        if (file.name.endsWith('.docx') || file.name.endsWith('.doc')) {
          this.file = file;
          this.fileName = file.name;
          this.processDocument();
        } else {
          this.errorMessage = '只支持 .doc 或 .docx 文件';
        }
      }
    },
    async processDocument() {
      this.isLoading = true;
      this.errorMessage = '';
      this.newQuestions = '';

      try {
        // 调用统一的API处理函数
        this.newQuestions = await processDocumentAndGenerateQuestions(
          this.file,
          this.questionCounts // 传递计算属性
        );
        this.showSidePanel = true;
      } catch (error) {
        this.errorMessage = error.message || '处理失败，请重试';
        console.error('处理文档时出错:', error);
      } finally {
        this.isLoading = false;
      }
    },
    togglePanel() {
      this.showSidePanel = !this.showSidePanel;
    },
    copyQuestions() {
      if (!this.formattedQuestions) return;
      navigator.clipboard.writeText(this.formattedQuestions)
        .then(() => alert('题目已复制到剪贴板'))
        .catch(err => {
          console.error('复制失败:', err);
          this.errorMessage = '复制失败，请手动复制内容';
        });
    },
    downloadQuestions() {
      if (!this.formattedQuestions) return;
      const blob = new Blob([this.formattedQuestions], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `新习题-${this.fileName.replace(/\.[^/.]+$/, '')}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      setTimeout(() => URL.revokeObjectURL(url), 100);
    }
  }
};
</script>

<style scoped>
.question-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 10px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  height:30em;
}

.upload-section {
  margin-bottom: 20px;
  text-align: center;
}

.question-type-selector {
  margin: 20px 0;
}

.type-inputs {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
}

.type-input {
  display: flex;
  align-items: center;
  gap: 5px;
}

.type-input input {
  width: 50px;
}

.upload-area {
  border: 2px dashed #ccc;
  padding: 15px;
  border-radius: 8px;
  cursor: pointer;
  transition: border-color 0.3s;
  height: 20em;
  display: flex; /* 添加此行 */
  justify-content: center; /* 添加此行 */
  align-items: center; /* 添加此行 */
  flex-direction: column; /* 添加此行，确保文字在中心垂直排列 */
}

.upload-area:hover {
  border-color: #956ef6;
}

.file-name {
  margin-top: 10px;
  color: #666;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loader {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #956ef6;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.content-wrapper {
  margin-top: 20px;
}

.side-panel {
  position: relative;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  border-bottom: 1px solid #eee;
}

.panel-content {
  padding: 15px;
  max-height: 400px;
  overflow-y: auto;
}

.actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.actions button {
  padding: 8px 15px;
  background-color: #956ef6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.error-message {
  padding: 10px;
  background-color: #ffebee;
  color: #c62828;
  border-radius: 4px;
  margin-bottom: 15px;
}
</style>
