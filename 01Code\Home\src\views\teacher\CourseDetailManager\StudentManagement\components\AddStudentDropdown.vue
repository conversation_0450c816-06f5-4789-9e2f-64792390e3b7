<!--src\views\teacher\CourseDetailManager\StudentManagement\components\AddStudentDropdown.vue-->
<template>
  <el-dropdown @command="handleCommand" :placement="placement">
    <el-button type="primary">
      + 添加学生
    </el-button>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item command="code">邀请码邀请</el-dropdown-item>
        <el-dropdown-item command="phone">手机号邀请</el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script>
export default {
  props: {
    placement: {
      type: String,
      default: 'bottom'
    }
  },
  emits: ['add-student-command'],
  setup(props, { emit }) {
    const handleCommand = (command) => {
      emit('add-student-command', command);
    };

    return {
      handleCommand
    };
  }
};
</script>