<!--src\views\student\MyCourse\index.vue-->
<template>
  <div>
    <TaskFilterBar @filter-change="handleFilterChange" />
    <CourseList :filter-params="filterParams" />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import TaskFilterBar from '@/views/student/StudentClass/components/TaskFilterBar.vue';
import CourseList from '@/views/student/MyCourse/components/CourseList.vue';

const filterParams = ref({
  status: 0, // 0表示全部，1进行中，0已结束
  query: ''
});

const handleFilterChange = (params) => {
  filterParams.value = {
    status: params.status,
    query: params.query
  };
};
</script>