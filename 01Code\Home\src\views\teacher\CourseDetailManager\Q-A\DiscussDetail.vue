<!--src/views/teacher/CourseDetailManager/Q-A/DiscussDetail.vue-->
<template>
  <div class="nav-bar">
    <div class="nav-bar-left">
      <button class="back-btn" @click="handleBack">
        <i class="iconfont icon-back">
          <img src="@/assets/img/General/icon-return.png" alt="返回图标">
        </i>
        返回
      </button>
    </div>
    <div class="nav-bar-center">
      <span>话题详情</span>
    </div>
  </div>
  <div class="discuss-detail">
    <!-- 自定义通知组件 -->
    <CustomNotification 
      v-if="notification.visible"
      :message="notification.message"
      :type="notification.type"
    />

    <!-- 确认对话框 -->
    <ConfirmDialog
      v-model:visible="showDeleteConfirm"
      title="删除确认"
      message="确定要删除这个话题吗？删除后将无法恢复"
      confirm-text="确定删除"
      @confirm="confirmDelete"
    />
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-mask">
      <div class="loading-spinner">
        <div class="spinner"></div>
        <p>加载中...</p>
      </div>
    </div>
    
    <!-- 话题主体信息 -->
    <div class="topic-info" v-if="topicData && !loading">
      <p class="topic-title">
        <span v-if="topicData.isPinned" class="pinned-tag">【置顶】</span>
        {{ topicData.title }}
      </p>
      <div class="topic-content">
        <p>{{ topicData.content }}</p>
      </div>

      <div class="publisher-info">
        <div class="avatar-container">
          <img :src="topicData.user?.avatar || '@/assets/default-avatar.png'" alt="发布者头像" class="avatar" />
          <div v-if="topicData.user?.role === 2" class="teacher-badge">师</div>
        </div>
        <div class="publisher-meta">
          <span class="publisher-name">{{ topicData.user?.name || topicData.user?.account || '匿名用户' }}</span>
          <span class="publisher-school">{{ topicData.school || topicData.user?.institution || '' }}</span>
        </div>
      </div>

      <div class="topic-footer">
        <div class="stats">
          <span>{{ topicData.viewCount || 0 }} 人浏览</span>
          <span>{{ pagination.totalComments || 0 }} 条回答</span>
          <span>{{ topicData.updateTimeFormatted }}</span>
        </div>
        <div class="operation">
           <!-- 添加点赞按钮 -->
          <button class="like-btn" :class="{ liked: topicData.isLiked }"@click="handleLikeTopic">
            <i class="iconfont icon-like">
              <img :src="topicData.isLiked ? likeYes : likeNo" alt="点赞图标">
            </i>
            {{ topicData.isLiked ? '已点赞' : '点赞' }}
            <!-- 如果需要显示点赞数 -->
            <span v-if="topicData.likeCount > 0" class="like-count">
              {{ topicData.likeCount }}
            </span>
          </button>
          <button class="reply-btn" @click="handleReply">
            <i class="iconfont icon-reply"></i>
            回答
          </button>
          <div class="more-actions">
            <button class="more-btn">
              <i class="iconfont icon-more">
                <img src="@/assets/img/General/icon-more.png" alt="更多图标">
              </i>
            </button>
            <div class="action-menu">
              <button class="delete-btn" @click="handleDelete">
                <i class="iconfont icon-delete">
                  <img src="@/assets/img/Teacher/icon-delete-normal.png" alt="删除图标">
                </i>
                删除
              </button>
              <button class="setting-btn" @click="handleSetting">
                <i class="iconfont icon-pinned">
                  <img :src="topicData.isPinned ? pinnedYes : pinnedNo" alt="置顶图标">
                </i>
                {{ topicData.isPinned ? '取消置顶' : '设置置顶' }}
              </button>
              <button class="star-bth" @click="handleStar">
                <i class="iconfont icon-star">
                  <img :src="topicData.isStarred ? starYes : starNo" alt="收藏图标">
                </i>
                {{ topicData.isStarred ? '取消收藏' : '收藏话题' }}
              </button>
              <button class="edit-bth" @click="handleEdit">
                <i class="iconfont icon-edit">
                  <img src="@/assets/img/General/icon-edit-normal.png" alt="编辑图标">
                </i>
                编辑
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 回答输入面板 -->
    <div class="answer-panel" v-if="showAnswerPanel && !loading">
      <textarea 
        class="answer-textarea" 
        v-model="answerContent" 
        placeholder="请输入您的回答" 
        rows="4"
      ></textarea>
      <div class="answer-footer">
        <span class="tip">温馨提示：请认真作答，老师会对你回答进行评分，无效回答或刷屏会影响你的本课成绩！</span>
        <div class="btn-group">
          <button class="cancel-btn" @click="closeAnswerPanel">取消</button>
          <button 
            class="submit-btn" 
            @click="submitAnswer"
            :disabled="isSubmittingAnswer"
          >
            {{ isSubmittingAnswer ? '提交中...' : '发布回答' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 回答列表组件 -->
    <div v-if="!loading" class="answer-list-container">
      <div v-if="currentPageComments.length === 0 && topicData" class="no-answers">
        <p class="empty-text">暂无回答，快来发表你的观点吧~</p>
      </div>
      
      <AnswerList
        v-else
        :answer-list="currentPageComments"
        :current-answer-page="pagination.currentCommentPage"
        :answers-per-page="pagination.commentPageSize"
        :total-answers="pagination.totalComments"
        @like="handleLike"
        @toggle-comment="toggleCommentSection"
        @submit-comment="submitComment"
        @reply-comment="handleReplyComment"
        @update:currentAnswerPage="changeAnswerPage"
        @edit-comment="handleEditComment"
        @refresh-answers="refreshAnswers"
      />
    </div>

      <!-- 编辑话题面板 -->
    <div class="edit-modal" v-if="showEditPanel">
    <div class="edit-modal-content">
      <div class="edit-modal-header">
        <h3>编辑话题</h3>
        <button class="close-btn" @click="showEditPanel = false">
          <i class="iconfont icon-close">
            <img src="@/assets/img/General/icon-close.png" alt="关闭">
          </i>
        </button>
      </div>
      <div class="edit-modal-body">
        <input 
          class="edit-title" 
          v-model="editForm.title" 
          placeholder="请输入标题"
        >
        <textarea 
          class="edit-content" 
          v-model="editForm.content" 
          placeholder="请输入内容" 
          rows="8"
        ></textarea>
      </div>
      <div class="edit-modal-footer">
        <button class="cancel-btn" @click="showEditPanel = false">取消</button>
        <button class="submit-btn" @click="submitEdit">保存修改</button>
      </div>
    </div>
    <!-- 添加在模板中合适的位置 -->
    <ConfirmDialog
      v-model:visible="showDeleteCommentConfirm"
      title="删除确认"
      message="确定要删除这条评论吗？删除后将无法恢复"
      confirm-text="确定删除"
      @confirm="confirmDeleteComment"
    />
  </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth/auth'
import { getPostDetailWithComments, getUserDetail, deletePost, starPost, unstarPost, saveComment,getCommentLikes,updatePost,checkStarStatus,deleteComment,likePost,unlikePost,checkLikeStatus,getPostLikes    } from '@/api/teacher/discuss'
import { formatTime } from '@/utils/dateUtils'
import AnswerList from '@/components/teacher/AnswerList.vue'
import CustomNotification from '@/components/CustomNotification.vue'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import starYes from '@/assets/img/General/icon-star-no-normal.png'
import starNo from '@/assets/img/General/icon-star-yes-normal.png'
import pinnedYes from '@/assets/img/Teacher/icon-pinned-no-normal.png'
import pinnedNo from '@/assets/img/Teacher/icon-pinned-yes-normal.png'
import likeYes from '@/assets/img/General/icon-like-actived.png'
import likeNo from '@/assets/img/General/icon-like-normal.png'

const route = useRoute()
const router = useRouter()
const topicId = ref(route.params.topicId)
const courseId = ref(route.params.courseId)

const showDeleteConfirm = ref(false)
const authStore = useAuthStore()

const isSubmittingAnswer = ref(false)
const loading = ref(false)

const showEditPanel = ref(false)
const editForm = ref({
  title: '',
  content: ''
})

const showDeleteCommentConfirm = ref(false)
const deletingComment = ref(null)
const deletingAnswer = ref(null)

// 编辑话题方法
const handleEdit = () => {
  if (!topicData.value) return
  
  editForm.value = {
    title: topicData.value.title,
    content: topicData.value.content
  }
  showEditPanel.value = true
}

// 提交编辑
const submitEdit = async () => {
  if (!editForm.value.title.trim() || !editForm.value.content.trim()) {
    showNotification('标题和内容不能为空', 'error')
    return
  }

  try {
    const res = await updatePost({
      id: topicData.value.id,
      courseId: topicData.value.courseId,
      title: editForm.value.title,
      content: editForm.value.content,
      publisherId: authStore.user.id,
      publishTime: topicData.value.publishTime,
      isPinned: topicData.value.isPinned ? 1 : 0,
      status: topicData.value.status || 0,
      isStarred: topicData.value.isStarred ? 1 : 0
    })

    if (res.code === 200) {
      showNotification('话题更新成功')
      showEditPanel.value = false
      // 刷新数据
      await fetchPostDetail()
    } else {
      showNotification(res.msg || '更新话题失败', 'error')
    }
  } catch (error) {
    console.error('更新话题失败:', error)
    showNotification('更新话题失败，请稍后重试', 'error')
  }
}

// 话题数据
const topicData = ref(null)
// 当前页的评论数据
const currentPageComments = ref([])
// 分页信息
const pagination = ref({
  currentCommentPage: 1,
  commentPageSize:10,
  totalComments: 0
})

// 通知相关状态
const notification = ref({
  visible: false,
  message: '',
  type: 'success'
})

// 显示通知
const showNotification = (message, type = 'success') => {
  notification.value = {
    visible: true,
    message,
    type
  }
  // 3秒后自动隐藏
  setTimeout(() => {
    notification.value.visible = false
  }, 3000)
}

// 获取话题详情
const fetchPostDetail = async () => {
  loading.value = true
  
  try {
    const res = await getPostDetailWithComments(
      topicId.value, 
      pagination.value.currentCommentPage, 
      pagination.value.commentPageSize
    )
    
    if (res.code === 200) {
      // 检查是否有有效的数据结构
      if (!res.result) {
        throw new Error('返回数据格式不正确')
      }

      // 检查收藏状态
      const starInfo = await checkStarStatus(authStore.user.id, res.result.id)
      // 检查点赞状态
      const likeInfo = await checkLikeStatus(authStore.user.id, res.result.id)
      // 处理话题数据
      let userInfo = null
      try {
        const userRes = await getUserDetail(res.result.publisherId)
        if (userRes.code === 200) {
          userInfo = userRes.result
        }
      } catch (error) {
        console.error('获取用户详情失败:', error)
      }
      
      // 处理话题数据
      topicData.value = {
        id: res.result.id,
        postId: res.result.id, // 确保有postId字段
        courseId: res.result.courseId,
        title: res.result.title,
        content: res.result.content,
        updateTime: res.result.updateTime || res.result.publishTime,
        updateTimeFormatted: formatTime(res.result.updateTime || res.result.publishTime),
        isPinned: res.result.isPinned === 1,
        isStarred: starInfo.isStarred,
        starId: starInfo.starId || null,
        isLiked: likeInfo.isLiked,
        likeId: likeInfo.likeId || null,
        viewCount: res.result.viewCount || 0,
        school: res.result.institution,
        user: userInfo || {
          id: res.result.publisherId,
          avatar: res.result.avatar,
          institution: res.result.institution
        }
      }

      // 处理评论数据 - 确保 comments 存在且是数组
      const comments = res.result.comments || []
      
      // 获取所有评论ID
      const commentIds = comments.map(c => c.id)
      
      // 批量获取点赞信息
      const likesInfo = await fetchAllCommentLikes(commentIds)
      
      // 处理评论数据
      currentPageComments.value = await Promise.all(
        comments.map(async (comment) => {
          // 获取点赞信息
          const likeInfo = likesInfo[comment.id] || {}
          // 获取评论者详细信息
          let commenterInfo = null
          try {
            const commenterRes = await getUserDetail(comment.commenterId)
            if (commenterRes.code === 200) {
              commenterInfo = commenterRes.result
            }
          } catch (error) {
            console.error('获取评论者详情失败:', error)
          }
          
          return {
            id: comment.id, // 使用真实ID
            postId: res.result.id, // 添加postId
            avatar: comment.avatar || (commenterInfo?.avatar || defaultTeacherAvatar),
            publisher: commenterInfo?.name || commenterInfo?.account || comment.name || '匿名用户',
            school: comment.school || commenterInfo?.institution || '',
            role: commenterInfo?.role || 1, // 默认为学生
            updateTime: comment.publishTime,
            updateTimeFormatted: formatTime(comment.publishTime),
            content: comment.content,
            likeCount: likeInfo.likeCount || 0,
            isLiked: likeInfo.isLiked || false,
            likeId: likeInfo.likeId || null,
            commentCount: comment.commentCount || 0,
            showComments: false,
            comments: comment.children ? await Promise.all(
              comment.children.map(async (child) => {
                // 处理子评论的用户信息
                let childCommenterInfo = null
                try {
                  const childCommenterRes = await getUserDetail(child.commenterId)
                  if (childCommenterRes.code === 200) {
                    childCommenterInfo = childCommenterRes.result
                  }
                } catch (error) {
                  console.error('获取子评论用户详情失败:', error)
                }

                // 处理子评论的点赞信息
                const childLikeInfo = child.likeInfo || {}
                
                return {
                  id: child.id, // 使用真实ID
                  commenterId: child.commenterId,
                  name: childCommenterInfo?.name || childCommenterInfo?.account || '匿名用户',
                  avatar: childCommenterInfo?.avatar || '@/assets/default-avatar.png',
                  content: child.content,
                  publishTime: child.publishTime,
                  updateTimeFormatted: formatTime(child.publishTime),
                  isLiked: childLikeInfo.isLiked || false, // 子评论点赞状态
                  likeId: childLikeInfo.id || null          // 子评论点赞ID
                }
              })
            ) : [],
            commentInput: ''
          }
        })
      )
      
      // 更新分页信息
      pagination.value.totalComments = res.result.totalComments || 0
      pagination.value.currentCommentPage = res.result.currentCommentPage || 1
      pagination.value.commentPageSize = res.result.commentPageSize || 10
    } else {
      showNotification(res.msg || '获取话题详情失败', 'error')
    }
  } catch (error) {
    console.error('获取话题详情失败:', error)
    showNotification('获取话题详情失败，请稍后重试', 'error')
  } finally {
    loading.value = false
  }
}
// 添加刷新方法
const refreshAnswers = async () => {
  await fetchPostDetail() // 这是你原有的获取数据的方法
}
// 批量获取点赞信息
const fetchAllCommentLikes = async (commentIds) => {
  try {
    const res = await getCommentLikes({
      userId: authStore.user.id,
      pageSize: 1000, // 获取足够大的数量
      sortColumn: 'createTime',
      sortType: 'descending'
    })
    
    if (res.code === 200) {
      // 创建点赞信息映射
      const likeMap = new Map()
      res.result.records.forEach(like => {
        likeMap.set(like.commentId, {
          isLiked: true,
          likeId: like.id
        })
      })
      
      // 获取各评论的点赞总数
      const countRes = await Promise.all(
        commentIds.map(commentId => 
          getCommentLikes({ commentId, pageSize: 1 })
            .then(res => ({
              commentId,
              count: res.code === 200 ? res.result.total : 0
            }))
        )
      )
      
      // 合并结果
      const result = {}
      commentIds.forEach(commentId => {
        result[commentId] = {
          isLiked: likeMap.has(commentId),
          likeId: likeMap.get(commentId)?.likeId || null,
          likeCount: countRes.find(item => item.commentId === commentId)?.count || 0
        }
      })
      
      return result
    }
    return {}
  } catch (error) {
    console.error('批量获取点赞信息失败:', error)
    return {}
  }
}

// 返回按钮逻辑
const handleBack = () => {
  router.push({
    name: 'Q-A',
    params: { 
      courseId: courseId.value,
      courseName: route.params.courseName || '默认课程名' // 添加 courseName
    }
  })
}

// 回答面板相关状态
const showAnswerPanel = ref(false)
const answerContent = ref('')

// 显示回答面板
const handleReply = () => {
  showAnswerPanel.value = true
  answerContent.value = ''
}

// 关闭回答面板
const closeAnswerPanel = () => {
  showAnswerPanel.value = false
  answerContent.value = ''
}

// 验证是否登录
const validateBeforeSubmit = () => {
  if (!authStore.isAuthenticated) {
    console.warn('未登录用户尝试提交内容')
    showNotification('请先登录后再操作', 'error')
    return false
  }
  return true
}

// 删除按钮逻辑
const handleDelete = () => {
  showDeleteConfirm.value = true
}

// 确认删除
const confirmDelete = async () => {
  try {
    const res = await deletePost(topicId.value)
    if (res.code === 200) {
      showNotification('话题删除成功')
      setTimeout(() => {
        router.push({
          name: 'Q-A',
          params: { 
            courseId: courseId.value,
            courseName: route.params.courseName || '默认课程名' // 添加 courseName
          }
        })
      }, 1000)
    } else {
      showNotification(res.msg || '删除话题失败', 'error')
    }
  } catch (error) {
    console.error('删除话题失败:', error)
    showNotification('删除话题失败，请稍后重试', 'error')
  }
}

// 设置置顶逻辑
const handleSetting = async () => {
  const newPinnedStatus = !topicData.value.isPinned
  console.log('准备发送的isPinned值:', newPinnedStatus ? 1 : 0)
  
  try {
    const res = await updatePost({
      id: topicData.value.id,
      courseId: topicData.value.courseId,
      title: topicData.value.title,
      content: topicData.value.content,
      publisherId: authStore.user.id,
      publishTime: topicData.value.publishTime,
      isPinned: newPinnedStatus ? 1 : 0,
      status: topicData.value.status || 0,
      isStarred: topicData.value.isStarred ? 1 : 0
    })
    console.log('后端返回的数据:', res)

    if (res.code === 200) {
      topicData.value.isPinned = newPinnedStatus
      showNotification(newPinnedStatus ? '话题已置顶' : '已取消置顶')
    } else {
      showNotification(res.msg || '操作失败', 'error')
    }
  } catch (error) {
    console.error('请求详情:', error.config)
    console.error('置顶操作失败:', error)
    showNotification('操作失败，请稍后重试', 'error')
  }
}

// 收藏/取消收藏方法
const handleStar = async () => {
  try {
    if (!authStore.isAuthenticated) {
      showNotification('请先登录后再收藏', 'error')
      return
    }

    if (!topicId.value) {
      showNotification('话题ID无效', 'error')
      return
    }

    // 检查当前收藏状态
    const isCurrentlyStarred = topicData.value.isStarred
    
    if (isCurrentlyStarred) {
      // 取消收藏 - 需要先获取收藏记录ID
      const starInfo = await checkStarStatus(authStore.user.id, topicId.value)
      if (starInfo.isStarred && starInfo.starId) {
        const res = await unstarPost(starInfo.starId)
        if (res.code === 200) {
          topicData.value.isStarred = false
          showNotification('已取消收藏')
          
          // 更新本地数据
          topicData.value.starId = null
        } else {
          showNotification(res.msg || '取消收藏失败', 'error')
        }
      } else {
        showNotification('未找到收藏记录', 'error')
      }
    } else {
      // 收藏
      const res = await starPost({ 
        postId: topicId.value,
        userId: authStore.user.id
      })
      if (res.code === 200) {
        topicData.value.isStarred = true
        // 保存收藏记录ID
        topicData.value.starId = res.result.id
        showNotification('话题已收藏')
      } else {
        showNotification(res.msg || '收藏失败', 'error')
      }
    }
  } catch (error) {
    console.error('收藏操作失败:', error)
    showNotification('操作失败，请稍后重试', 'error')
  }
}
// 添加点赞话题的方法
const handleLikeTopic = async () => {
  try {
    if (!authStore.isAuthenticated) {
      showNotification('请先登录后再点赞', 'error')
      return
    }

    if (!topicId.value) {
      showNotification('话题ID无效', 'error')
      return
    }

    // 检查当前点赞状态
    const isCurrentlyLiked = topicData.value.isLiked
    
    if (isCurrentlyLiked) {
      // 取消点赞 - 需要先获取点赞记录ID
      const likeInfo = await checkLikeStatus(authStore.user.id, topicId.value)
      if (likeInfo.isLiked && likeInfo.likeId) {
        const res = await unlikePost(likeInfo.likeId)
        if (res.code === 200) {
          topicData.value.isLiked = false
          topicData.value.likeCount = (topicData.value.likeCount || 0) - 1
          showNotification('已取消点赞')
          
          // 更新本地数据
          topicData.value.likeId = null
        } else {
          showNotification(res.msg || '取消点赞失败', 'error')
        }
      } else {
        showNotification('未找到点赞记录', 'error')
      }
    } else {
      // 点赞
      const res = await likePost({ 
        postId: topicId.value,
        userId: authStore.user.id
      })
      if (res.code === 200) {
        topicData.value.isLiked = true
        topicData.value.likeCount = (topicData.value.likeCount || 0) + 1
        // 保存点赞记录ID
        topicData.value.likeId = res.result.id
        showNotification('话题已点赞')
      } else {
        showNotification(res.msg || '点赞失败', 'error')
      }
    }
  } catch (error) {
    console.error('点赞操作失败:', error)
    showNotification('操作失败，请稍后重试', 'error')
  }
}

// 点赞逻辑
const handleLike = async (data) => {
  const { answer, action, likeCount, likeId } = data
  
  // 更新UI
  currentPageComments.value = currentPageComments.value.map(item => {
    if (item.id === answer.id) {
      return {
        ...item,
        isLiked: action === 'like',
        likeCount: likeCount,
        likeId: likeId || item.likeId
      }
    }
    return item
  })
  
  // 如果需要，可以在这里添加点赞成功的提示
  showNotification(action === 'like' ? '点赞成功' : '已取消点赞')
}

// 切换评论区显示/隐藏
const toggleCommentSection = (answer) => {
  answer.showComments = !answer.showComments
}

// 提交回答（一级评论）
const submitAnswer = async () => {
  if (!validateBeforeSubmit()) return
  
  if (!answerContent.value.trim()) {
    showNotification('回答内容不能为空', 'error')
    return
  }

  isSubmittingAnswer.value = true

  try {
    const commentData = {
      postId: topicId.value,
      commenterId: authStore.user.id,
      content: answerContent.value,
      publishTime: Date.now(),
      parentId: null // 一级评论parentId为null
    }

    const res = await saveComment(commentData)
    
    if (res.code === 200) {
      showNotification('回答提交成功')
      
      // 刷新当前页评论
      await fetchPostDetail()
      closeAnswerPanel()
    }
  } catch (error) {
    showNotification('回答提交失败，请稍后重试', 'error')
  } finally {
    isSubmittingAnswer.value = false
  }
}

// 提交评论（二级评论）
const submitComment = async ({ answer, newComment, error }) => {
  if (error) {
    showNotification(error, 'error')
    return
  }

  try {
    currentPageComments.value = currentPageComments.value.map(item => {
      if (item.id === answer.id) {
        return {
          ...item,
          comments: [{
            ...newComment,
            updateTimeFormatted: formatTime(newComment.publishTime),
            avatar: newComment.avatar || '@/assets/default-avatar.png'
          }, ...item.comments],
          commentCount: (item.commentCount || 0) + 1
        }
      }
      return item
    })
    
    showNotification('回复成功') // 确保这行能执行
  } catch (err) {
    showNotification('更新评论失败', 'error')
  }
}

// 回复评论
const handleReplyComment = (answer, comment) => {
  answer.commentInput = `回复 ${comment.publisher}: `
  // 聚焦到评论输入框
  nextTick(() => {
    const textarea = document.querySelector(`.answer-item[data-id="${answer.id}"] .comment-textarea`)
    if (textarea) {
      textarea.focus()
    }
  })
}

/// 添加edit-comment事件处理
const handleEditComment = async ({ comment, updatedComment }) => {
  try {
    // 更新本地数据
    currentPageComments.value = currentPageComments.value.map(answer => {
      // 更新一级评论（回答）
      if (answer.id === comment.id) {
        return {
          ...answer,
          content: updatedComment.content,
          updateTimeFormatted: formatTime(Date.now())
        }
      }
      
      // 更新二级评论
      if (answer.comments?.some(c => c.id === comment.id)) {
        return {
          ...answer,
          comments: answer.comments.map(c => 
            c.id === comment.id ? {
              ...c,
              content: updatedComment.content,
              updateTimeFormatted: formatTime(Date.now())
            } : c
          )
        }
      }
      
      return answer
    })
    
    showNotification('更新成功')
  } catch (error) {
    console.error('更新评论失败:', error)
    showNotification('更新失败，请稍后重试', 'error')
  }
}

// 更改评论页码
const changeAnswerPage = (page) => {
  pagination.value.currentCommentPage = page
  fetchPostDetail()
}
// 修改后的 handleDeleteComment 方法
const handleDeleteComment = ({ answer, comment }) => {
  deletingComment.value = comment
  deletingAnswer.value = answer
  showDeleteCommentConfirm.value = true
}

// 确认删除方法
const confirmDeleteComment = async () => {
  try {
    const res = await deleteComment(deletingComment.value.id)
    
    if (res.code === 200) {
      showNotification('删除成功')
      
      // 更优雅的刷新方式 - 重新获取数据
      await fetchPostDetail()
      
      // 或者如果是二级评论，可以局部更新数据
      if (deletingAnswer.value) {
        currentPageComments.value = currentPageComments.value.map(answer => {
          if (answer.id === deletingAnswer.value.id) {
            return {
              ...answer,
              comments: answer.comments.filter(c => c.id !== deletingComment.value.id),
              commentCount: answer.commentCount - 1
            }
          }
          return answer
        })
      }
    } else {
      showNotification(res.msg || '删除失败', 'error')
    }
  } catch (error) {
    showNotification('删除评论失败', 'error')
  } finally {
    showDeleteCommentConfirm.value = false
    deletingComment.value = null
    deletingAnswer.value = null
  }
}

onMounted(() => {
  fetchPostDetail()
})
</script>

<style scoped lang="scss">
@use "@/styles/teacher/CourseDetailManager/DiscussDetail";

.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
  
  .loading-spinner {
    text-align: center;
    
    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 10px;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  }
}

.no-answers, .no-comments {
  padding: 40px 0;
  text-align: center;
  color: #999;
}
</style>