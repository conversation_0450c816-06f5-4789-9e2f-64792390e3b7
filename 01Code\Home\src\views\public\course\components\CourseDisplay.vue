<!--src\views\public\course\components\CourseDisplay.vue-->
<template>
  <div class="course-display">
    <!-- 筛选控制区 -->
    <div class="filter-controls">
      <!-- 学期分类 -->
      <div class="semester-filter">
        <select 
          v-model="activeSemester" 
          class="semester-select"
          @change="handleSemesterChange"
        >
          <option 
            v-for="semester in semesterOptions" 
            :key="semester.value"
            :value="semester.value"
          >
            {{ semester.label }}
          </option>
        </select>
      </div>
      
      <!-- 搜索框 -->
      <div class="search-box">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索课程名称..."
          @keyup.enter="handleSearch"
        />
        <button @click="handleSearch">
          <i class="search-icon"></i>
        </button>
      </div>
    </div>

    <!-- 无结果提示 -->
    <div class="no-results" v-if="filteredCourses.length === 0">
      <img src="@/assets/img/General/icon-searchResult.png" alt="无相关课程" />
      <p>无相关课程</p>
    </div>

    <!-- 课程列表 -->
    <div class="course-list" v-else>
      <div 
      class="course-card" 
      v-for="course in filteredCourses" 
      :key="course.id"
      @click="navigateToCourse(course.id)"
      :style="{'--bg-image': `url(${course.cover || defaultCourseCover})`}"
    >
        <!-- 知识图谱按钮 -->
        <button class="knowledge-graph-btn" v-if="course.hasKnowledgeGraph">
          <i class="icon-graph"></i>
          知识图谱
        </button>
        
        <!-- 课程信息区 -->
        <div class="course-info">
          <h4 class="course-title">{{ course.title }}</h4>
          <div class="course-properties">
            <span v-for="(property, index) in course.properties" :key="index" class="property-tag">
              {{ property }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页控件 -->
    <div class="pagination" v-if="filteredCourses.length > 0">
      <button 
        @click="prevPage" 
        :disabled="currentPage === 1 || isLoading"
        class="page-button"
      >
        上一页
      </button>
      
      <span class="page-info">
        第 {{ currentPage }} 页 / 共 {{ totalPages }} 页 (共 {{ totalCourses }} 门课程)
      </span>
      
      <button 
        @click="nextPage" 
        :disabled="currentPage === totalPages || isLoading"
        class="page-button"
      >
        下一页
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth/auth';
import { ElMessage } from 'element-plus';

const emit = defineEmits(['course-click', 'page-change', 'filter-change']);

const router = useRouter();
const userStore = useAuthStore();

const props = defineProps({
  courseType: String,
  allCourses: {
    type: Array,
    default: () => []
  },
  totalCourses: {
    type: Number,
    default: 0
  },
  isLoading: {
    type: Boolean,
    default: false
  }
});

const currentPage = ref(1);
const coursesPerPage = 16;

const semesterOptions = [
  { value: 'all', label: '全部学期' },
  { value: '第一学期', label: '第一学期' },
  { value: '第二学期', label: '第二学期' },
  { value: '第三学期', label: '第三学期' },
  { value: '第四学期', label: '第四学期' },
  { value: '第五学期', label: '第五学期' },
  { value: '第六学期', label: '第六学期' },
  { value: '第七学期', label: '第七学期' },
  { value: '第八学期', label: '第八学期' },
];

const activeSemester = ref('all');
const searchQuery = ref('');

const filteredCourses = computed(() => {
  const filtered = props.allCourses.filter(course => {
    const typeMatch = props.courseType === '全部' || course.type === props.courseType;
    const semesterMatch = activeSemester.value === 'all' || course.semester.includes(activeSemester.value);
    const searchMatch = searchQuery.value === '' || 
                      course.title.toLowerCase().includes(searchQuery.value.toLowerCase());
    return typeMatch && semesterMatch && searchMatch;
  });
  return filtered;
});

// 使用本地过滤后的总数
const filteredTotal = computed(() => {
  return searchQuery.value !== '' || activeSemester.value !== 'all' 
    ? filteredCourses.value.length 
    : props.totalCourses;
});

const totalPages = computed(() => {
  return Math.ceil(filteredTotal.value / coursesPerPage);
});

const handleSearch = () => {
  currentPage.value = 1;
  emitFilterChange();
};

const handleSemesterChange = () => {
  currentPage.value = 1;
  emitFilterChange();
};

function emitFilterChange() {
  emit('filter-change', {
    semester: activeSemester.value,
    name: searchQuery.value,
    pageNum: currentPage.value,
    pageSize: coursesPerPage
  });
}

function prevPage() {
  if (currentPage.value > 1) {
    currentPage.value--;
    emitPageChange();
  }
}

function nextPage() {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
    emitPageChange();
  }
}

function emitPageChange() {
  emit('page-change', {
    pageNum: currentPage.value,
    pageSize: coursesPerPage
  });
}

const navigateToCourse = (courseId) => {
  console.log('当前用户状态:', userStore.user); // 添加调试日志
  if (!userStore.user) {
    console.log('触发未登录提示'); // 确认是否执行到这里
    ElMessage.warning('请先登录后再查看课程详情');
    return;
  }
  router.push({ name: 'course-detail', params: { courseId } });
};

// 修正后的watch监听
watch(
  // 监视props.courseType的变化
  () => props.courseType,
  // 回调函数
  (newType, oldType) => {
    activeSemester.value = 'all';
    searchQuery.value = '';
    currentPage.value = 1;
    emitFilterChange();
  }
);
</script>


<style lang="scss" scoped>
.course-display {
  padding: 1.5vw;
  width: 100%;
  height: 1200px;
  display: flex;
  flex-direction: column;
  
  .filter-controls {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2vw;
    align-items: center;
    
    .semester-filter {
      h3 {
        margin: 0 0 1vw 0;
        font-size: 1.1rem;
        color: #555;
      }
      
      .semester-select {
        padding: 0.8vw 1.2vw;
        border: 1px solid #ddd;
        border-radius: 0.4vw;
        background: white;
        font-size: 0.95rem;
        width: 15vw;
        cursor: pointer;
        appearance: none;
        background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right 0.8vw center;
        background-size: 1.2vw;
        
        &:focus {
          outline: none;
          border-color: $primary-color;
          box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
        }
      }
    }
    
    .search-box {
      display: flex;
      align-items: center;
      border: 1px solid #ddd;
      border-radius: 0.4vw;
      overflow: hidden;
      width: 25vw;
      
      input {
        flex: 1;
        padding: 0.8vw;
        border: none;
        outline: none;
        font-size: 0.95rem;
        
        &::placeholder {
          color: #999;
        }
      }
      
      button {
        padding: 0 1vw;
        height: 100%;
        background: #f5f5f5;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        
        &:hover {
          background: #eee;
        }
        
        .search-icon {
          display: inline-block;
          width: 1.2vw;
          height: 1.2vw;
          background: url('@/assets/img/Course/index/ic_search.png') center/contain no-repeat;
        }
      }
    }
  }
  
  .course-list {
    display: grid;
    grid-template-columns: repeat(4, 1fr); /* 固定4列 */
    gap: 30px;
    padding-top: 1vw;
    width: 100%;
    min-height: 400px; /* 修复网格布局溢出问题 */
    
    .course-card {
      border-radius: 0.6vw;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s, box-shadow 0.3s;
      background: white;
      position: relative;
      width: 100%; /* 宽度自适应 */
      aspect-ratio: 4/3; /* 保持宽高比 */
      display: flex;
      flex-direction: column;
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
      }
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: var(--bg-image);
        background-size: cover;
        background-position: center;
        z-index: 0;
      }
      
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(to bottom, transparent 30%, black);
        z-index: 1;
      }
      
      .knowledge-graph-btn {
        position: absolute;
        top: 0.8vw;
        left: 0.8vw;
        width: 5vw;
        height: 1.5vw;
        border-radius: 1vw;
        background: $knowButton-bg;
        color: white;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 3;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        font-size: 0.8rem;
        
        &:hover {
          background: $knowButton-hover-bg;
          color: white;
        }
        
        .icon-graph {
          width: 1vw;
          height: 1vw;
          background-color: #333;
        }
      }
      
      .course-info {
        flex: 1;
        padding: 1.2vw;
        background-color: transparent;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        z-index: 2;
        color: white;
        
        .course-title {
          margin: 0 0 0.6vw 0;
          font-size: 1.1rem;
          color: white;
          white-space: normal;
          overflow: visible;
          text-overflow: unset;
        }
        
        .course-properties {
          display: flex;
          flex-wrap: wrap;
          gap: 0.5vw;
          margin: 0;
        }
        
        .property-tag {
          padding: 0.1vw 0.5vw;
          border-radius: 1vw;
          background: $bg-gray-color;
          border: rgba(255, 255, 255, 0.265) 1px solid;
          font-size: 0.75rem;
          color: white;
        }
      }
    }
  }

  .pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 2vw;
    padding-top: 2vw;
    border-top: 1px solid #eee;
    gap: 2vw;
    
    .page-button {
      padding: 0.5vw 1.5vw;
      border: 1px solid #ddd;
      background-color: #f8f8f8;
      cursor: pointer;
      border-radius: 0.4vw;
      transition: all 0.3s;
      font-size: 0.9rem;
      
      &:hover:not(:disabled) {
        background-color: $primary-color;
        color: white;
        border-color: $primary-color;
      }
      
      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
    
    .page-info {
      font-size: 0.9rem;
      color: #666;
    }
  }
}
.no-results {
  margin-top: 150px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4vw 0;
  
  img {
    width: 100px;
    height: 100px;
    margin-bottom: 2vw;
    opacity: 0.6;
  }
  
  p {
    font-size: 1.2rem;
    color: #999;
  }
}
</style>