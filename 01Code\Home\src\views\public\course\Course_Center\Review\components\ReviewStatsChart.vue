<template>
  <div>
    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-number">{{ stats.courseTotalPosts }}</div>
        <div class="stat-label">总帖子数</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ stats.courseTotalParticipants }}</div>
        <div class="stat-label">参与人数</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ stats.courseTotalPostLikes }}</div>
        <div class="stat-label">总点赞数</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ stats.courseTotalPostStars }}</div>
        <div class="stat-label">总收藏数</div>
      </div>
    </div>
    <!-- 数据概览 -->
    <div class="data-overview">
      <h3>数据概览</h3>
      <div class="overview-content">
        <p>当前课程共有 <strong>{{ stats.courseTotalPosts }}</strong> 个帖子，吸引了 <strong>{{ stats.courseTotalParticipants }}</strong> 人参与讨论。</p>
        <p>获得了 <strong>{{ stats.courseTotalPostLikes }}</strong> 次点赞和 <strong>{{ stats.courseTotalPostStars }}</strong> 次收藏。</p>
      </div>
    </div>
    <!-- 柱状图 -->
    <div class="chart-container">
      <h3>数据统计图</h3>
      <v-chart class="chart" :option="chartOption" />
    </div>
  </div>
</template>

<script setup>
import VChart from 'vue-echarts'
const props = defineProps({
  stats: {
    type: Object,
    required: true
  },
  courseInfo: {
    type: Object,
    required: true
  },
  chartOption: {
    type: Object,
    required: true
  }
})
</script>

<style scoped>
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}
.stat-card {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
}
.stat-card:hover {
  transform: translateY(-5px);
}
.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}
.stat-label {
  color: #606266;
  font-size: 14px;
}
.data-overview {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}
.data-overview h3 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}
.overview-content p {
  margin: 8px 0;
  color: #606266;
  line-height: 1.6;
}
.overview-content strong {
  color: #409EFF;
  font-weight: 600;
}
.chart-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}
.chart-container h3 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}
.chart {
  height: 400px;
  width: 100%;
}
@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  .stat-number {
    font-size: 24px;
  }
  .chart {
    height: 300px;
  }
}
</style> 