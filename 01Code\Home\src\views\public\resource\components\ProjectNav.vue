<template>
  <nav class="project-nav">
    <ul>
      <li 
        v-for="(item, index) in navItems" 
        :key="item.key"
        :class="{ active: activeIndex === index }"
        @click="handleNavClick(index, item.key)"
      >
        <i :class="item.icon" v-if="item.icon"></i>
        <span>{{ item.label }}</span>
      </li>
    </ul>
  </nav>
</template>

<script setup>
import { ref } from 'vue'

const emit = defineEmits(['nav-click'])
const activeIndex = ref(0)

const navItems = [
  { key: 'intro', label: '项目介绍', icon: 'fas fa-info-circle' },
  { key: 'experiment', label: '实验内容', icon: 'fas fa-flask' },
  { key: 'docs', label: '项目文档', icon: 'fas fa-file-alt' },
  { key: 'download', label: '项目压缩包下载', icon: 'fas fa-download' }
]

const handleNavClick = (index, tabKey) => {
  activeIndex.value = index
  emit('nav-click', tabKey)
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.project-nav {
  position: sticky;
  top: 20px;
  height: fit-content;
  width: 240px;
  background: white;
  border-radius: 8px;
  border: 1px solid $border-color;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  li {
    padding: 16px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 15px;
    line-height: 1.5;
    border-bottom: 1px solid $border-color;
    display: flex;
    align-items: center;
    gap: 12px;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background: rgba($primary-color, 0.05);
    }

    &.active {
      background: $primary-color;
      color: white;
      font-weight: 600;
      
      i {
        color: white;
      }
    }

    i {
      font-size: 16px;
      color: $primary-color;
      width: 20px;
      text-align: center;
    }

    span {
      flex: 1;
    }
  }
}

@media (max-width: 768px) {
  .project-nav {
    width: 100%;
    position: static;
    margin-bottom: 20px;
    
    ul {
      display: flex;
      overflow-x: auto;
    }
    
    li {
      white-space: nowrap;
      min-width: fit-content;
      border-bottom: none;
      border-right: 1px solid $border-color;
      
      &:last-child {
        border-right: none;
      }
    }
  }
}
</style>
