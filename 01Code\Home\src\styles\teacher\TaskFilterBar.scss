@use '@/styles/variables' as *;
// 定义间距变量
$spacing-sm: 8px;
$spacing-md: 12px;
$spacing-lg: 16px;

// 定义圆角
$border-radius: 4px;

.task-filter-bar {
  display: flex;
  flex-direction: column;
  padding: $spacing-md $spacing-lg;
  border-radius: $border-radius;
  
  // 第一行布局：分类筛选 + 搜索框
  .first-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative; // 为指示器提供定位参考
    
    // 分类筛选容器
    .categories-container {
      position: relative;
      overflow: hidden; // 防止内容溢出影响布局计算
      
      // 分类筛选组
      .categories-group {
        display: flex;
        position: relative; // 为子元素定位提供参考
        
        span {
          padding: $spacing-sm $spacing-md;
          margin-right: $spacing-sm;
          border-radius: $border-radius;
          cursor: pointer;
          transition: all 0.2s;
          white-space: nowrap;
          position: relative; // 确保元素有定位属性
          
          &:hover {
            background-color: $hover-color;
          }
          
          &.active {
            font-weight: 500;
          }
        }
      }
      
      // 滑动指示器
      .indicator {
        position: absolute;
        bottom: 0;
        height: 2px;
        background-color: $primary-color;
        transition: transform 0.3s ease, width 0.3s ease;
        will-change: transform, width; // 优化动画性能
      }
    }
    
    // 搜索框
    .search-box {
      display: flex;
      align-items: center;
      min-width: 200px;
      max-width: 300px;
      width: 30%;
      
      input {
        flex: 1;
        padding: $spacing-sm 32px $spacing-sm $spacing-md;
        border: 1px solid $border-color;
        border-radius: $border-radius;
        outline: none;
        transition: border-color 0.2s;
        
        &:focus {
          border-color: $primary-color;
        }
      }
      
      button {
        position: relative;
        right: $spacing-sm;
        background: none;
        border: none;
        padding: 0;
        cursor: pointer;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: -20px;
        
        .search-icon {
          width: 100%;
          height: 100%;
          filter: invert(100%); /* 反色效果 */
          transition: filter 0.2s;
        }
        
        &:hover .search-icon {
          filter: invert(50%) sepia(100%) saturate(1000%) hue-rotate(200deg); /* 悬停时变色效果 */
        }
      }
    }
  }
  
  // 分隔线
  .divider {
    height: 1px;
    background-color: $border-color;
    margin: $spacing-sm 0;
  }
  
  // 状态筛选组
  .status-group {
    display: flex;
    flex-wrap: wrap;
    
    .group-label {
      margin-right: $spacing-md;
      font-weight: 500;
      color: #333;
    }
    
    span {
      padding: $spacing-sm $spacing-md;
      margin-right: $spacing-sm;
      margin-bottom: $spacing-sm;
      border-radius: $border-radius;
      cursor: pointer;
      transition: all 0.2s;
      
      &:hover {
        background-color: $hover-color;
      }
      
      &.active {
        background-color: $primary-color;
        color: white;
      }
    }
  }
}