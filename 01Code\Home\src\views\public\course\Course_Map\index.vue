<!-- src\views\public\course\Course_Map\index.vue -->
<template>
  <!-- 课程图谱页面 -->
  <div class="course-map-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>课程图谱</h1>
    </div>

    <!-- 主体内容区域 -->
    <div class="course-map-container">
      <div class="course-content">
        <!-- 课程列表标题 -->
        <h2 class="section-title">课程列表</h2>
        <!-- 课程卡片网格 -->
        <div class="panel-grid">
          <div 
            v-for="(item, index) in courseList" 
            :key="index" 
            class="course-card"
            @click="handleCardClick(item)"
          >
            <!-- 课程图片 -->
            <div class="course-image">
              <img :src="item.image" :alt="item.title">
            </div>
            <!-- 课程信息 -->
            <div class="course-info">
              <h3 class="course-title">{{ item.title }}</h3>
              <p class="course-university">{{ item.university }}</p>
              <p class="course-teachers">{{ item.teachers }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import cover1 from '@/assets/img/Course/courseCover/cover1.png'

// 初始化路由实例
const router = useRouter()

// 课程列表数据
const courseList = ref([
  {
    id: '1',
    image: cover1,
    title: '黄河治理与文化传承',
    university: '郑州大学',
    teachers: '吴漫, 王博, 李嘉, 黄亮'
  }
])

// 处理课程卡片点击事件
const handleCardClick = (item) => {
  router.push(`/course_map_overview/${item.id}`)
}
</script>

<style lang="scss" scoped>
$border-radius: 2vw;
$primary-color: #8a6de3;
$course-tabs: #f3ecff;
$course-tabs-solid: #d6beff;

/* 整个页面背景 */
.course-map-page {
  position: relative;
  padding-top: 10vw;
  padding-left: 3vw;
  padding-right: 3vw;

  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('@/assets/img/Home/ic_home_bg.png') top/cover no-repeat;
    z-index: -1;
  }

  .page-header {
    color: white;
    margin-bottom: 2vw;
    display: flex;
    justify-content: space-between;

    h1 {
      font-size: 3rem;
      font-weight: 400;
      margin: 0;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .page-header-desc {
      display: flex;
      margin-top: 5vw;
      gap: 2vw;

      .part {
        display: flex;
        flex-direction: column;
        align-items: end;
        gap: 1vw;

        div {
          display: flex;
          align-items: flex-end;
          justify-content: center;

          h1 {
            margin: 0;
            line-height: 1;
            font-size: 3rem;
          }

          p {
            margin: 0 0 0 0.2em;
            line-height: 1;
            align-self: flex-end;
            font-size: 1rem;
          }
        }
      }
    }
  }
}

/* 主容器 */
.course-map-container {
  margin-top: 10vw;
  background-color: white;
  border-radius: 1vw 1vw 0 0;
  padding: 2vw;
  min-height: 70vh;

  .course-content {
    .section-title {
      font-size: 1.5rem;
      color: $primary-color;
      margin-bottom: 1.5vw;
      border-left: 5px solid $primary-color;
      padding-left: 0.8vw;
    }

    .panel-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(15vw, 1fr));
      gap: 2vw;
    }

    .course-card {
      border-radius: 1vw;
      overflow: hidden;
      background-color: #fff;
      box-shadow: 0 0.2vw 0.6vw rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease;
      cursor: pointer;

      &:hover {
        transform: translateY(-0.5vw);
        box-shadow: 0 0.3vw 0.9vw rgba(0, 0, 0, 0.15);
      }

      .course-image {
        height: 11vw;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .course-info {
        padding: 1vw;

        .course-title {
          font-size: 1rem;
          font-weight: bold;
          margin-bottom: 0.5vw;
          color: #222;
        }

        .course-university,
        .course-teachers {
          font-size: 0.85rem;
          color: #666;
          margin: 0.2vw 0;
        }
      }
    }
  }
}
</style>