<template>
  <div class="toolbar">
    <div class="search-container">
    <input type="text" placeholder="请输入文件名称搜索" v-model="searchQuery"  class="search-input">
     <span class="search-icon" @click="handleSearch">
        <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
          <path d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0 0 11.6 0l43.6-43.5a8.2 8.2 0 0 0 0-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"/>
        </svg>
      </span>
    </div>
    <div class="button-group">
      <button @click="$emit('create-folder')">+ 新建文件夹</button>
      <button @click="triggerFileUpload">上传文件</button>
    </div>

   <input
      type="file"
      ref="fileInput"
      
      @change="handleFileSelection"
      multiple  
      style="display: none"
    />
  </div>

</template>
<script setup>
import { ref } from 'vue';
import { usePublicResourceStore } from '@/stores/public/teacher/publicResStore'

const emit = defineEmits(['create-folder', 'upload-files'])
const resourceStore = usePublicResourceStore()

const searchQuery = ref('');
const fileInput = ref(null);

const triggerFileUpload = () => {
  fileInput.value.click(); // 触发文件选择对话框
};



const handleSearch = async () => {
  console.log(8);
  
  await resourceStore.searchFiles(searchQuery.value);
  console.log(9);
  
};


const handleFileSelection = (event) => {
  console.log('选择的文件列表（原始）:', event.target.files);
  const files = Array.from(event.target.files);
  console.log('转换后的文件数组:', files);
  if (files.length > 0) {
    console.log('第一个文件对象:', files[0]); // 确认文件对象有效
    emit('upload-files', files);
  } else {
    console.warn('没有选择任何文件');
  }
  event.target.value = '';
};



</script>

<style lang="scss" scoped>
.toolbar {
  display: flex;
  justify-content: space-between; /* 搜索框和按钮组分别在左右两侧 */
  gap: 10px;
  margin-bottom: 12px;

  .search-container {
    flex: 1;
    max-width: 300px;
    position: relative;
    display: flex;
    align-items: center;

    .search-input {
      width: 100%;
      padding: 6px 14px 6px 32px;
      border: 1px solid #ccc;
      border-radius: 45px;
      outline: none;
      transition: border-color 0.3s;

      &:focus {
        border-color: #409eff;
      }
    }

    .search-icon {
      position: absolute;
      right:  10px;
      width: 16px;
      height: 16px;
      cursor: pointer;
      color: #999;
      transition: color 0.3s;

      &:hover {
        color: #409eff;
      }

      svg {
        width: 100%;
        height: 100%;
        fill: currentColor;
      }
    }
  }

  .button-group {
    display: flex;
    gap: 10px; /* 按钮之间的间距 */
  }

  button {
    min-width: 120px; /* 统一按钮最小宽度 */
    padding: 6px 14px;
    border: none;
    background-color: #409eff;
    color: white;
    border-radius: 45px;
    cursor: pointer;
    white-space: nowrap; /* 防止文字换行 */
  }
}
</style>