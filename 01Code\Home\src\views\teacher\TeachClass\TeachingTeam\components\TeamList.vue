<template>
  <div class="team-list-container">
    <!-- 选项卡导航 -->
    <div class="tab-nav">
      <button 
        class="tab-btn"
        :class="{ active: activeTab === 'created' }"
        @click="activeTab = 'created'"
      >
        我创建的 ({{ createdTeams.length }})
      </button>
      <button 
        class="tab-btn"
        :class="{ active: activeTab === 'joined' }"
        @click="activeTab = 'joined'"
      >
        我加入的 ({{ joinedTeams.length }})
      </button>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
    </div>

    <!-- 团队列表 -->
    <template v-else>
      <div v-if="currentTeams.length > 0" class="team-list">
        <TeamCard
          v-for="team in currentTeams"
          :key="team.courseId"
          :team="team"
          :is-creator="activeTab === 'created'"
          @action="$emit('action', $event)"
          @member-removed="handleMemberRemoved"
        />
      </div>
      
      <div v-else class="empty-tip">
        <el-empty :description="activeTab === 'created' ? '您还没有创建任何教学团队' : '您还没有加入任何教学团队'">
          <template v-if="activeTab === 'created'">
            <el-button type="primary" @click="$emit('action', { type: 'create' })">
              创建新团队
            </el-button>
          </template>
        </el-empty>
      </div>
    </template>
  </div>
</template>
<script>
import { computed, ref, onMounted } from 'vue'
import TeamCard from './TeamCard.vue'

export default {
  components: { TeamCard },
props: {
  createdTeams: {
    type: Array,
    required: true, // 设为必传
    validator: (value) => {
      console.log('Received createdTeams:', value)
      return Array.isArray(value)
    }
  },
  joinedTeams: {
    type: Array,
    required: true, // 设为必传
    validator: (value) => {
      console.log('Received joinedTeams:', value)
      return Array.isArray(value)
    }
  },
  loading: Boolean
},
  emits: ['action'],
  setup(props) {
    const activeTab = ref('created')

    const currentTeams = computed(() => {
      console.log('Created Teams:', props.createdTeams);
      console.log('Joined Teams:', props.joinedTeams);
      return activeTab.value === 'created' 
        ? props.createdTeams 
        : props.joinedTeams
    })

    const handleMemberRemoved = ({ courseId, teacherId }) => {
      emit('member-removed', { courseId, teacherId });
    };

    onMounted(() => {
      console.log('Current Teams:', currentTeams.value);
    });

    return {
      activeTab,
      currentTeams,
       handleMemberRemoved
    }
  }
}
</script>

<style scoped>
.tab-nav {
  display: flex;
  border-bottom: 1px solid #e8e8e8;
  margin-bottom: 20px;
}

.tab-btn {
  padding: 10px 20px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 15px;
  color: #666;
  position: relative;
  outline: none;
}

.tab-btn.active {
  color: #1890ff;
  font-weight: 500;
}

.tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: #1890ff;
}

.team-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.loading-container {
  padding: 20px;
}

.empty-tip {
  padding: 40px 0;
  text-align: center;
}
</style>