<template>
  <div class="ability-graph-layout">
    <!-- 左侧内容区 -->
    <div class="left-panel">
      <!-- 课程目标 -->
      <div class="section course-goal">
        <h2 class="section-title">课程目标</h2>
        <div class="goal-text" :class="{ 'collapsed': !expanded }">
          <div v-if="courseTarget" v-html="courseTarget.replace(/\n/g, '<br/>')"></div>
          <div v-else style="color:#aaa;">暂无课程目标信息</div>
        </div>
        <div class="expand-btn-wrapper">
          <button class="expand-btn" @click="expanded = !expanded">
            {{ expanded ? '收起 ▲' : '展开 ▼' }}
          </button>
        </div>
      </div>
      <!-- 目标拆解 -->
      <div class="section target-breakdown">
        <h3 class="section-title">目标拆解</h3>
        <div class="breakdown-cards">
          <div class="breakdown-card main-ability">
            <div class="card-title">主能力</div>
            <div class="card-value">
              <span v-if="abilityStore.isLoading">加载中...</span>
              <span v-else>{{ abilityStore.mainAbilityCount }}<sup>个</sup></span>
            </div>
            <div class="card-icon"></div>
          </div>
          <div class="breakdown-card sub-ability">
            <div class="card-title">子能力</div>
            <div class="card-value">
              <span v-if="abilityStore.isLoading">加载中...</span>
              <span v-else>{{ abilityStore.subAbilityCount }}<sup>个</sup></span>
            </div>
            <div class="card-icon"></div>
          </div>
          <div class="breakdown-card knowledge-coverage">
            <div class="card-title">覆盖知识点</div>
            <div class="card-value">
              <span v-if="abilityStore.isLoading">加载中...</span>
              <span v-else>{{ abilityStore.knowledgeNodeCount }}<sup>个</sup></span>
            </div>
            <div class="card-icon"></div>
          </div>
        </div>
        <!-- 错误信息显示 -->
        <div v-if="abilityStore.error" class="error-message">
          {{ abilityStore.error }}
        </div>
        <!-- 无图谱提示 -->
        <div v-if="!graphId && !abilityStore.isLoading" class="no-graph-message">
          <p>该课程暂无能力图谱</p>
        </div>
      </div>
    </div>
    <!-- 右侧环形图区 -->
    <div class="right-panel" v-if="graphId">
      <div class="circle-title-outer">
        主能力分布
      </div>
      <div class="circle-chart-wrapper">
        <!-- 加载状态 -->
        <div v-if="abilityStore.isLoading" class="loading-state">
          <div class="loading-spinner"></div>
          <p>加载中...</p>
        </div>
        <!-- 空数据状态 -->
        <div v-else-if="!chartData.length" class="empty-state">
          <p>暂无主能力数据</p>
          <p style="font-size: 0.9rem; color: #999;">调试信息: mainAbilities.length = {{ abilityStore.mainAbilities.length }}</p>
        </div>
        <!-- 图表 -->
        <v-chart v-else ref="echartsRef" :option="option" style="width: 650px; height: 650px;" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAbilityGraphStore } from '@/stores/public/graphManager/abilityGraphStore'
import { useAuthStore } from '@/stores/auth/auth'
import { getGraphsByCourse } from '@/api/public/course/courseMap/knowledgeGraph'
import { getCourseInfoList } from '@/api/public/course/course'

const echartsRef = ref(null)
const router = useRouter()
const route = useRoute()
const abilityStore = useAbilityGraphStore()
const authStore = useAuthStore()

// 图谱ID
const graphId = ref(null)

// 主能力数据（从后端获取）
const chartData = computed(() => {
  const mainAbilities = abilityStore.mainAbilities
  console.log('chartData computed - mainAbilities:', mainAbilities)
  
  if (!mainAbilities || mainAbilities.length === 0) {
    console.log('没有主能力数据，返回空数组')
    return []
  }
  
  // 自动均分权重
  const value = 100 / mainAbilities.length
  const data = mainAbilities.map(ability => ({
    name: ability.nodeName || ability.abilityName || ability.name,
    value: value
  }))
  console.log('生成的图表数据:', data)
  return data
})

const option = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: function(params) {
      return params.name + '<br/>点击查看详情';
    }
  },
  legend: {
    top: '5%',
    left: 'center',
    textStyle: {
      fontSize: 12
    }
  },
  series: [
    {
      name: '主能力分布',
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 36,
          fontWeight: 'bold'
        },
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      labelLine: {
        show: false
      },
      data: chartData.value
    }
  ]
}))

const expanded = ref(false)

const onChartClick = (params) => {
  console.log('扇形点击事件', params)
  console.log('当前主能力列表:', abilityStore.mainAbilities)
  console.log('当前graphId:', graphId.value)
  
  if (params && params.name) {
    // 找到对应的主能力数据
    const mainAbility = abilityStore.mainAbilities.find(ability => 
      ability.nodeName === params.name
    )
    console.log('找到的主能力:', mainAbility)
    
    if (mainAbility) {
      console.log('准备跳转到主能力详情:', mainAbility)
      try {
        // 使用主能力名称作为路由参数，并传递graphId
        router.push({
          path: `/ability-detail/${encodeURIComponent(mainAbility.nodeName)}`,
          query: { graphId: graphId.value }
        })
        console.log('跳转成功')
      } catch (error) {
        console.error('跳转失败:', error)
      }
    } else {
      console.warn('未找到对应的主能力数据:', params.name)
      console.log('可用的主能力:', abilityStore.mainAbilities.map(a => a.nodeName))
    }
  } else {
    console.warn('点击事件参数无效:', params)
  }
}

// 获取能力图谱ID
const fetchAbilityGraphId = async () => {
  try {
    console.log('获取图谱列表，课程ID:', route.params.courseId)
    const res = await getGraphsByCourse(route.params.courseId)
    console.log('图谱列表响应:', res)
    
    if (res.code === 200 && res.result && res.result.length) {
      // 查找类型为2的能力图谱
      const abilityGraph = res.result.find(g => g.graphType === 2)
      if (abilityGraph) {
        graphId.value = abilityGraph.graphId
        console.log('找到能力图谱，ID:', graphId.value)
        // 获取图谱数据
        await fetchAbilityGraphData()
      } else {
        console.log('未找到能力图谱，可用的图谱:', res.result.map(g => ({ graphId: g.graphId, graphType: g.graphType, graphName: g.graphName })))
      }
    } else {
      console.log('获取图谱列表失败或列表为空')
    }
  } catch (error) {
    console.error('获取图谱列表失败:', error)
  }
}

// 获取能力图谱数据
const fetchAbilityGraphData = async () => {
  if (!graphId.value) {
    console.log('没有图谱ID，跳过数据获取')
    return
  }
  
  try {
    // 检查认证状态
    if (!authStore.isAuthenticated) {
      console.warn('用户未登录，无法获取能力图谱数据')
      abilityStore.setError('请先登录后再查看能力图谱')
      return
    }

    console.log('获取能力图谱数据，graphId:', graphId.value)
    await abilityStore.fetchAllData(graphId.value)
  } catch (error) {
    console.error('获取能力图谱数据失败:', error)
    if (error.response?.status === 401) {
      abilityStore.setError('登录已过期，请重新登录')
    } else {
      abilityStore.setError('获取数据失败，请稍后重试')
    }
  }
}

const courseTarget = ref('')

// 监听图表数据变化，重新绑定点击事件
watch(() => chartData.value, (newData) => {
  console.log('图表数据变化:', newData)
  if (newData.length > 0) {
    nextTick(() => {
      if (echartsRef.value && echartsRef.value.chart) {
        console.log('重新绑定点击事件')
        echartsRef.value.chart.off('click') // 先移除旧的点击事件
        echartsRef.value.chart.on('click', onChartClick)
      }
    })
  }
}, { deep: true })

// 组件挂载时获取数据
onMounted(() => {
  fetchAbilityGraphId()

  // 获取课程目标
  const courseId = route.params.courseId
  if (courseId) {
    getCourseInfoList({ courseId }).then(res => {
      if (res.code === 200 && res.result && res.result.records && res.result.records.length > 0) {
        courseTarget.value = res.result.records[0].courseTarget || ''
      } else {
        courseTarget.value = ''
      }
    }).catch(() => {
      courseTarget.value = ''
    })
  }

  // 等待图表渲染完成后添加点击事件
  nextTick(() => {
    if (echartsRef.value && echartsRef.value.chart) {
      console.log('初始绑定点击事件')
      echartsRef.value.chart.on('click', onChartClick)
    }
  })
})
</script>

<style scoped>
.ability-graph-layout {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  min-height: 600px;
  background: #fff;
  border-radius: 24px;
  padding: 40px 32px;
  box-sizing: border-box;
}
.left-panel {
  flex: 1.3;
  min-width: 420px;
  max-width: 650px;
  margin-right: 36px;
}
.right-panel {
  flex: 1.1;
  min-width: 320px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 700px;
}
.section {
  background: #f8fafd;
  border-radius: 16px;
  padding: 32px 28px 24px 28px;
  margin-bottom: 32px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}
.section-title {
  font-size: 1.4rem;
  font-weight: bold;
  margin-bottom: 18px;
}
.goal-text {
  color: #333;
  font-size: 1.05rem;
  line-height: 1.8;
  margin-bottom: 16px;
  max-height: 15.8em;
  overflow: hidden;
  position: relative;
  transition: max-height 0.3s;
}
.goal-text.collapsed {
  max-height: 14.8em; /* 约6行 */
  overflow: hidden;
}
.goal-text:not(.collapsed) {
  max-height: 1000px;
}
.expand-btn-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 0.5em;
}
.expand-btn {
  background: #f0f0f0;
  border: none;
  border-radius: 8px;
  padding: 4px 16px;
  font-size: 0.95rem;
  cursor: pointer;
}
.target-breakdown {
  padding-top: 18px;
}
.breakdown-cards {
  display: flex;
  flex-direction: row;
  gap: 24px;
  width: 100%;
}
.breakdown-card {
  flex: 1 1 0%;
  min-width: 0;
  max-width: none;
  padding: 28px 0 24px 0;
  margin: 0;
  background: #e9f1ff;
  border-radius: 12px;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  display: flex;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
}
.breakdown-card.main-ability { background: #e4dcf8; }
.breakdown-card.sub-ability { background: #d0eef8; }
.breakdown-card.knowledge-coverage { background: #f9f3cd; }
.card-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 6px;
}
.card-value {
  font-size: 1.7rem;
  font-weight: bold;
  color: #6a5acd;
  margin-bottom: 4px;
}
.card-desc {
  font-size: 0.95rem;
  color: #888;
  margin-bottom: 4px;
}
.progress-bar {
  width: 100%;
  height: 6px;
  background: #e0e0e0;
  border-radius: 4px;
  margin-top: 4px;
}
.progress {
  height: 100%;
  background: #6a5acd;
  border-radius: 4px;
}
.circle-graph {
  position: relative;
  width: 420px;
  height: 420px;
  background: #f8fafd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}
.circle-center {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 180px;
  height: 180px;
  background: #fff;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  z-index: 2;
}
.circle-title {
  font-size: 1.3rem;
  font-weight: bold;
  margin-bottom: 8px;
}
.circle-desc {
  font-size: 0.95rem;
  color: #888;
}
.circle-svg {
  width: 100%;
  height: 100%;
  z-index: 1;
}
.circle-label {
  position: absolute;
  font-size: 1.05rem;
  font-weight: 600;
  color: #333;
  text-align: center;
  pointer-events: none;
}
.label1 {
  left: 50%;
  top: 12%;
  transform: translate(-50%, 0);
}
.label2 {
  right: 10%;
  top: 50%;
  transform: translate(0, -50%);
}
.label3 {
  left: 50%;
  bottom: 12%;
  transform: translate(-50%, 0);
}
.v-chart {
  width: 650px;
  height: 650px;
  max-width: 100%;
  max-height: 100%;
}
.circle-title-outer {
  width: 650px;
  text-align: center;
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 18px;
  color: #333;
}
.circle-chart-wrapper {
  width: 650px;
  height: 650px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.error-message {
  margin-top: 16px;
  padding: 12px;
  background: #fee;
  color: #c33;
  border-radius: 8px;
  font-size: 0.9rem;
  text-align: center;
}
.no-graph-message {
  margin-top: 16px;
  padding: 12px;
  background: #f8fafd;
  color: #888;
  border-radius: 8px;
  font-size: 0.9rem;
  text-align: center;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #6a5acd;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #888;
  font-size: 1.1rem;
}


</style> 