<template>
  <div class="resource-toolbar">
    <div class="left-group">
      <div class="dropdown-wrapper" ref="dropdownWrapper">
        <button class="btn" @click="toggleDropdown">
          添加资源
          <span class="arrow">▼</span>
        </button>
        <ul v-if="showDropdown" class="dropdown-menu">
          <li @click="selectOption('local')">本地上传</li>
          <li @click="selectOption('link')">添加链接</li>
          <li @click="selectOption('personal')">个人资源</li>
        </ul>
      </div>
      <button class="btn" @click="showModal = true">+ 新建分组</button>
    </div>

    <div class="search-group">
      <input type="text" v-model="searchText" placeholder="请输入文件名称搜索" class="search-input" />
      <span class="search-icon">🔍</span>
    </div>

    <div v-if="showModal" class="modal-mask" @click.self="showModal = false">
      <div class="modal">
        <h3>新建分组</h3>
        <input v-model="newGroupName" placeholder="请输入分组名称" class="modal-input" />
        <div class="modal-actions">
          <button @click="createGroup">确定</button>
          <button @click="showModal = false">取消</button>
        </div>
      </div>
    </div>
    <ResourceDialog v-if="dialogVisible" :title="dialogTitle" :type="dialogType" :visible="dialogVisible"
      @close="dialogVisible = false" @confirm="handleDialogConfirm" />
    <AddLinkDialog 
    v-if="addLinkDialogVisible"
    :folder-id="currentFolderId"
     @close="addLinkDialogVisible = false" 
     @confirm="handleAddLinkConfirm" />
       <PersonalResourceDialog 
    v-if="personalResourceVisible" 
    @close="personalResourceVisible = false"
    @select="handleFolderSelect"
  />
  </div>
</template>

<script setup>
import ResourceDialog from './Dialog/ResourceDialog.vue'
import AddLinkDialog from './Dialog/AddLinkDialog.vue'
import PersonalResourceDialog from './Dialog/PersonalResourceDialog.vue';
import { ref, onMounted, onBeforeUnmount ,computed} from 'vue'
import {useUserStore} from '@/stores/userStore';
import { useRoute } from 'vue-router';
import {saveFolder} from '@/api/teacher/publicResources'
import {useBaseResourceStore}from '@/stores/public/teacher/baseResourceStore'
import {useResourceStore} from '@/stores/public/teacher/resource'

const route = useRoute();
const courseId = ref(route.params.courseId); // 假设路由参数名为courseId
const userStore = useUserStore();
const createdId = computed(() => userStore.user.id);
const baseResourcStore = useBaseResourceStore()
const resourceStore = useResourceStore()

const showDropdown = ref(false)
const showModal = ref(false)
const searchText = ref('')
const newGroupName = ref('')//新建分组名称
const dropdownWrapper = ref(null)
const dialogTitle = ref('')//弹窗标题
const dialogType = ref('')//弹窗类型
const dialogVisible = ref(false)//弹窗是否可见
const addLinkDialogVisible = ref(false) //添加链接弹窗是否可见
const personalResourceVisible = ref(false) //个人资源弹窗是否可见
const currentFolderId = ref('') //当前文件夹id



const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value
}



const selectOption = (type) => {
  showDropdown.value = false
  
  if (type === 'local') {
    // 本地上传使用ResourceDialog
    dialogTitle.value = '本地资源'
    dialogType.value = type
    dialogVisible.value = true
    
  } else if (type === 'personal') {
    // 个人资源使用ResourceDialog
    dialogTitle.value = '个人资源'
    dialogType.value = type
    personalResourceVisible.value = true
  } else if (type === 'link') {
    // 添加链接直接打开AddLinkDialog
    addLinkDialogVisible.value = true
  }
}

const handleDialogConfirm = (data) => {
  dialogVisible.value = false
  
  // 根据不同类型执行不同操作
  if (data.type === 'local') {
    triggerFileUpload(data.folderId)
    
  } else if (data.type === 'link') {
    showAddLinkDialog(data.folderId)
    
  } else if (data.type === 'personal') {
    handlePersonalResource(data.group)
    
  }
}

// 创建新文件夹
const createGroup = async () => {
  if (newGroupName.value.trim()) {
    try {
      // 调用 createFolder 方法来创建文件夹
      await createFolder(newGroupName.value.trim(), null);
      newGroupName.value = ''; 
      showModal.value = false;  
       await resourceStore.refreshFolders();
    } catch (error) {
      console.error('创建分组失败:', error);
    }
  }
}


// 创建文件夹
async function createFolder(name, parentId = null) {
  try {
    const folderData = {
      name,
      parentId,  // 默认parentId为null
      folderType: 0,  // 设置folderType为0
      createdId: createdId.value,  
      courseId:courseId.value
    }

    const response = await saveFolder(folderData);
    console.log('文件夹创建成功:', response);
    await resourceStore.refreshFolders();
    return response.result;
  } catch (error) {
    console.error('创建文件夹失败:', error);
  }
}


const triggerFileUpload = async (folderId) => {
  const input = document.createElement('input');
  input.type = 'file';
  input.multiple = true;
  input.onchange = async (e) => {
    const files = Array.from(e.target.files);
    
    try {
      // 上传每个文件
      for (const file of files) {
        await baseResourcStore.baseUploadResource(file, {
          courseId: courseId.value, // 从路由参数获取课程ID
          folderId: folderId, // 传入的文件夹ID
          createdId: createdId.value, // 从用户store获取创建者ID
        });
      }
      console.log('文件上传成功');
      await resourceStore.refreshFolders();
    } catch (error) {
      console.error('上传失败:', error);
    }
  };
  input.click();
};

// 显示添加链接对话框
const showAddLinkDialog = (folderId) => {
  currentFolderId.value = folderId
  addLinkDialogVisible.value = true
}

/* const handleAddLinkSuccess = (result) => {
  console.log('链接添加成功:', result)
  addLinkDialogVisible.value = false
} */

// 处理个人资源
const handlePersonalResource = (group) => {
  console.log('在分组', group, '中添加个人资源')

}


// 处理点击下拉框外部自动关闭
const handleClickOutside = (event) => {
  if (
    showDropdown.value &&
    dropdownWrapper.value &&
    !dropdownWrapper.value.contains(event.target)
  ) {
    showDropdown.value = false
  }
}



onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside)
})



</script>

<style lang="scss" scoped>
.resource-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;

  .left-group {
    display: flex;
    gap: 10px;

    .btn {
      padding: 6px 12px;
      background-color: #f9f9ff;
      border: 1px solid #c9d4f3;
      border-radius: 4px;
      color: #4c7bff;
      font-size: 14px;
      cursor: pointer;
      position: relative;

      .arrow {
        margin-left: 6px;
        font-size: 10px;
      }
    }

    .dropdown-wrapper {
      position: relative;

      .dropdown-menu {
        position: absolute;
        top: 36px;
        left: 0;
        background: white;
        border: 1px solid #ddd;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        border-radius: 4px;
        z-index: 1000;
        width: 120px;
        padding: 4px 0;

        li {
          padding: 8px 12px;
          font-size: 14px;
          list-style-type: none;
          cursor: pointer;

          &:hover {
            background-color: #f0f2f5;
          }
        }
      }
    }
  }

  .search-group {
    position: relative;

    .search-input {
      padding: 6px 32px 6px 12px;
      border: 1px solid #eee;
      border-radius: 4px;
      outline: none;
      font-size: 14px;
      color: #333;
    }

    .search-icon {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 14px;
      color: #999;
    }
  }

  .modal-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;

    .modal {
      background: white;
      padding: 20px;
      border-radius: 8px;
      width: 300px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

      h3 {
        margin-bottom: 12px;
        font-size: 16px;
      }

      .modal-input {
        width: 100%;
        padding: 6px 10px;
        margin-bottom: 16px;
        border: 1px solid #ddd;
        border-radius: 4px;
      }

      .modal-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;

        button {
          padding: 6px 12px;
          background-color: #4c7bff;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;

          &:last-child {
            background-color: #ccc;
          }
        }
      }
    }
  }
}
</style>
