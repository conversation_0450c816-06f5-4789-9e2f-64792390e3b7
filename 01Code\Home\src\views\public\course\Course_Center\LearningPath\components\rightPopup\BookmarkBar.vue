<!-- src\views\public\course\Course_Map\components\rightPopup\BookmarkBar.vue -->
<template>
  <!-- 书签栏按钮 -->
  <div v-for="(item, index) in tabs" 
       :key="item.key"
       class="bookmark-btn"
       :style="buttonStyle(index)"
       @mouseenter="hoverIndex = index"
       @mouseleave="hoverIndex = null"
       @click="handleClick(item.key)">
    <!-- 鼠标悬停时显示标签 -->
    <span class="label" v-if="hoverIndex === index">{{ item.label }}</span>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, ref, computed } from 'vue'

// 定义组件接收的属性
const props = defineProps({
  tabs: Array,
  isOpen: Boolean
})

// 定义组件触发的事件
const emit = defineEmits(['jump'])

// 鼠标悬停的按钮索引
const hoverIndex = ref(null)

// 按钮颜色数组
const colors = ['#f88c77', '#4ac46a', '#fa8ca4', '#48b8f1']

// 计算每个按钮的样式
const buttonStyle = (index) => ({
  position: 'fixed',
  top: `calc(20% + ${index * 50}px)`,
  right: props.isOpen ? 'calc(25% + 40px)' : '0px',
  backgroundColor: colors[index % colors.length],
  opacity: props.isOpen ? 1 : 0,
  transform: `translateY(-50%)`,
})

// 处理按钮点击事件
const handleClick = (key) => {
  emit('jump', key)
}
</script>

<style scoped>
.bookmark-btn {
  border-radius: 10px 0 0 30px;
  overflow: hidden;
  width: 30px;
  height: 45px;
  transition: all 0.3s ease;
  z-index: 1200;
  display: flex;
  align-items: center;
  color: #fff;
  padding: 20px 12px;
  cursor: pointer;
}

.bookmark-btn:hover {
  width: 100px;
}

.bookmark-btn .icon {
  margin-right: 8px;
}

.bookmark-btn .label {
  white-space: nowrap;
}
</style>