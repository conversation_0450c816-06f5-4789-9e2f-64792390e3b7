// src/stores/teacher/graphManager/questionGraphStore.js
import { defineStore } from "pinia";
import {
  createGraph,
  getGraphListByCourse,
  deleteGraph,
  getGraphTree,
  createNode,
  deleteNode,
  updateNodeInfo,
  getChildCount,
  getCourseResourceFolderTree,
  batchUpdateNodeResources,
  getNodeDetail,
  countNodeResources,
  getNodeResources,
  unlinkResourceFromNode,
  getNodeType,
  canCreateChild,
  copyLevel1NodesFromKnowledgeGraph,
  countQuestionNodes,
  countQuestionGraphResources,
} from "@/api/teacher/graphManager/questionGraph";

export const useQuestionGraphStore = defineStore("questionGraph", {
  state: () => ({
    graphData: null, // 当前创建的图谱数据（graphId）
    graphList: [], // 图谱列表
    isLoading: false, // 加载状态
    error: null, // 错误信息
    courseId: null, // 当前课程 ID，用于后续刷新
    rootNodeId: null, // 存储根节点ID
    currentNodeTree: null, // 存储当前图谱的树结构
    nodeCreationPending: false, // 节点创建加载状态
    nodeDetail: null, // 存储节点详细信息
    tagList: [], // 标签列表
    nodeTypes: {
      // 节点类型定义
      ROOT: "ROOT",
      CHAPTER: "CHAPTER",
      QUESTION: "QUESTION",
    },
  }),

  actions: {
    // 设置当前图谱树
    setCurrentNodeTree(newTree) {
      this.currentNodeTree = newTree;
    },

    // 设置根节点 ID
    setRootNodeId(id) {
      this.rootNodeId = id || "root";
    },

    // 创建图谱
    async createGraphAction(data) {
      this.isLoading = true;
      try {
        const response = await createGraph(data);
        if (response.code === 200) {
          this.graphData = response.result;
          this.error = null;

          // 如果是问题图谱(graphType=1)，自动复制知识图谱的章节节点
          if (data.graphType === 1 && response.result?.graphId) {
            try {
              console.log("问题图谱创建成功，开始复制知识图谱章节节点...");
              const copyResult = await this.copyLevel1Nodes(
                response.result.graphId,
                data.courseId
              );
              console.log(`成功复制 ${copyResult} 个章节节点`);
            } catch (copyError) {
              console.error("复制知识图谱节点失败:", copyError);
              // 复制失败不影响图谱创建，只记录错误
            }
          }
        } else {
          this.error = response.msg;
        }
      } catch (error) {
        this.error = "请求失败，请稍后再试";
      } finally {
        this.isLoading = false;
      }
    },

    // 获取该课程的所有图谱
    async fetchGraphListByCourse(courseId) {
      this.courseId = courseId;
      this.isLoading = true;
      try {
        const res = await getGraphListByCourse(courseId);
        if (res.code === 200) {
          this.graphList = res.result;
          this.error = null;
        } else {
          this.error = res.msg;
        }
      } catch (e) {
        this.error = "获取图谱列表失败";
      } finally {
        this.isLoading = false;
      }
    },

    // 根据 graphType 获取对应的路由名称
    getRouteNameByGraphType(graphType) {
      const routeMap = {
        0: "CreateKnowledgeGraph", // 知识图谱
        1: "CreateQuestionmGraph", // 问题图谱
        2: "CreateAbilityGraph", // 能力图谱
      };
      return routeMap[graphType] || "CreateKnowledgeGraph";
    },

    // 删除图谱
    async deleteGraphAction(graphId) {
      this.isLoading = true;
      try {
        const res = await deleteGraph(graphId);
        if (res.code === 200) {
          this.error = null;
          return true;
        } else {
          this.error = res.msg;
          return false;
        }
      } catch (e) {
        this.error = "删除图谱失败";
        return false;
      } finally {
        this.isLoading = false;
      }
    },

    // 获取图谱树结构
    async fetchGraphTree(graphId) {
      this.isLoading = true;
      try {
        const res = await getGraphTree(graphId);
        if (res.code === 200) {
          console.log("获取到的树数据:", res.result);

          // 处理数据字段映射：questionId -> nodeId
          const processNode = (node) => {
            const processedNode = {
              ...node,
              nodeId: node.questionId || node.nodeId, // 兼容两种字段名
            };
            if (node.children && node.children.length > 0) {
              processedNode.children = node.children.map(processNode);
            }
            return processedNode;
          };

          const processedResult = res.result.map(processNode);

          // 检查是否有数据
          if (processedResult.length === 0) {
            console.log("获取到空的图谱数据，可能是新图谱或数据不存在");
            this.error = null;
            return [];
          }

          const rootNode = processedResult[0];
          const rootNodeId = rootNode.nodeId || "root";

          this.setCurrentNodeTree(processedResult);
          this.setRootNodeId(rootNodeId);

          this.error = null;
          return processedResult;
        } else {
          this.error = res.msg;
          return null;
        }
      } catch (e) {
        this.error = "获取图谱结构失败";
        return null;
      } finally {
        this.isLoading = false;
      }
    },

    // 创建节点（支持节点类型判断）
    async createNodeAction(payload) {
      this.nodeCreationPending = true;
      try {
        // 确定节点类型
        let nodeType = this.nodeTypes.QUESTION; // 默认为问题节点
        if (
          payload.parentId === "root" ||
          payload.parentId === this.rootNodeId
        ) {
          nodeType = this.nodeTypes.CHAPTER; // 根节点的子节点为章节节点
        }

        const res = await createNode({
          graphId: payload.graphId,
          parentId: payload.parentId,
          nodeName: payload.name,
          nodeDesc: payload.description,
          nodeType: nodeType,
        });

        if (res.code === 200) {
          return {
            success: true,
            node: {
              id: res.result.nodeId,
              label: payload.name,
              description: payload.description,
              nodeType: nodeType,
              children: [],
            },
          };
        } else {
          this.error = res.msg;
          return { success: false, error: res.msg };
        }
      } catch (error) {
        this.error = "创建节点失败";
        return { success: false, error: "网络错误" };
      } finally {
        this.nodeCreationPending = false;
      }
    },

    // 检查节点是否可以创建子节点
    async checkCanCreateChild(nodeId) {
      try {
        const res = await canCreateChild(nodeId);
        return res.code === 200 ? res.result : false;
      } catch (error) {
        console.error("检查节点创建权限失败:", error);
        return false;
      }
    },

    // 获取节点类型
    async fetchNodeType(nodeId) {
      try {
        const res = await getNodeType(nodeId);
        return res.code === 200 ? res.result : null;
      } catch (error) {
        console.error("获取节点类型失败:", error);
        return null;
      }
    },

    // 删除节点
    async deleteNodeAction(nodeId) {
      try {
        const res = await deleteNode(nodeId);
        if (res.code === 200) {
          this.error = null;
          return true;
        } else {
          this.error = res.msg;
          return false;
        }
      } catch (error) {
        this.error = "删除节点失败";
        return false;
      }
    },

    // 更新节点信息
    async updateNodeInfo(data) {
      try {
        const res = await updateNodeInfo(data);
        if (res.code === 200) {
          this.error = null;
          return true;
        } else {
          this.error = res.msg;
          return false;
        }
      } catch (error) {
        this.error = "更新节点信息失败";
        return false;
      }
    },

    // 获取节点详情
    async fetchNodeDetail(nodeId) {
      try {
        const res = await getNodeDetail(nodeId);
        if (res.code === 200) {
          this.nodeDetail = res.result;
          return res.result;
        } else {
          this.error = res.msg;
          return null;
        }
      } catch (error) {
        this.error = "获取节点详情失败";
        return null;
      }
    },

    // 统计节点关联的资源数量
    async countNodeResources(nodeId) {
      try {
        const res = await countNodeResources(nodeId);
        return res.code === 200 ? res.result : 0;
      } catch (error) {
        console.error("统计资源数量失败:", error);
        return 0;
      }
    },

    // 获取节点已关联的资源列表
    async fetchNodeResources(nodeId) {
      try {
        const res = await getNodeResources(nodeId);
        return res.code === 200 ? res.result : [];
      } catch (error) {
        console.error("获取资源列表失败:", error);
        return [];
      }
    },

    // 取消资源关联
    async unlinkResourceFromNode(nodeId, resourceId) {
      try {
        const res = await unlinkResourceFromNode(nodeId, resourceId);
        return res.code === 200;
      } catch (error) {
        console.error("取消资源关联失败:", error);
        return false;
      }
    },

    // 统计所有节点数量
    async countAllNodes(graphId) {
      try {
        const tree = await this.fetchGraphTree(graphId);
        if (!tree || !tree[0]) return 0;

        const countNodes = (node) => {
          let count = 1;
          if (node.children && node.children.length > 0) {
            count += node.children.reduce(
              (sum, child) => sum + countNodes(child),
              0
            );
          }
          return count;
        };

        return countNodes(tree[0]) - 1; // 减去根节点
      } catch (error) {
        console.error("统计节点数量失败:", error);
        return 0;
      }
    },

    // 复制知识图谱level为1的节点到问题图谱
    async copyLevel1Nodes(questionGraphId, courseId) {
      try {
        const res = await copyLevel1NodesFromKnowledgeGraph({
          questionGraphId,
          courseId,
        });
        return res.code === 200 ? res.result : 0;
      } catch (error) {
        console.error("复制知识图谱节点失败:", error);
        return 0;
      }
    },

    // 统计问题节点数量（level=2的节点）
    async countQuestionNodesAction(graphId) {
      try {
        const res = await countQuestionNodes(graphId);
        return res.code === 200 ? res.result : 0;
      } catch (error) {
        console.error("统计问题节点数量失败:", error);
        return 0;
      }
    },

    // 统计图谱关联资源总数
    async countGraphResources(graphId) {
      try {
        const res = await countQuestionGraphResources(graphId);
        return res.code === 200 ? res.result : 0;
      } catch (error) {
        console.error("统计图谱资源总数失败:", error);
        return 0;
      }
    },

    // 关联资源到节点（批量更新）
    async associateResourcesWithNode(nodeId, resourceIds) {
      try {
        const res = await batchUpdateNodeResources({
          nodeId: nodeId,
          resourceIds: resourceIds,
        });
        if (res.code === 200) {
          // 返回操作影响的资源数量
          return {
            success: true,
            changeCount: res.result || 0,
          };
        } else {
          return {
            success: false,
            changeCount: 0,
            error: res.msg,
          };
        }
      } catch (error) {
        console.error("关联资源失败:", error);
        return {
          success: false,
          changeCount: 0,
          error: error.message || "关联资源失败",
        };
      }
    },

    // 获取课程资源树
    async fetchCourseResourceTree(courseId) {
      try {
        const res = await getCourseResourceFolderTree(courseId);
        return res.code === 200 ? res.result : [];
      } catch (error) {
        console.error("获取课程资源树失败:", error);
        return [];
      }
    },
  },
});
