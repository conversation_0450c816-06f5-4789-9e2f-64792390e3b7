<template>
  <div class="notice-list">
    <!-- 加载状态 -->
    <div v-if="noticeStore.isLoading" class="loading-tip">
      加载中...
    </div>

    <!-- 错误提示 -->
    <div v-else-if="noticeStore.error" class="error-tip">
      {{ noticeStore.error }}
    </div>

    <!-- 空状态 -->
    <div v-else-if="notices.length === 0" class="empty-tip">
      暂无{{ activeTabLabel }}，点击上方"新建通知"按钮创建
    </div>

    <!-- 公告列表 -->
    <div v-for="notice in notices" :key="notice.id" class="notice-item" @click="showNoticeDetail(notice)">
      <div class="notice-header">
        <h3>{{ notice.title }}</h3>
        <span class="notice-time">{{ formatAgo(notice.publishTime) }}</span>
      </div>
      <div class="notice-content" v-html="removeImages(notice.content)"></div>
      <div class="notice-footer">
        <span class="notice-type">{{ noticeTypeLabel(notice.noticeType) }}</span>
        <div class="notice-actions">
          <button @click.stop="editNotice(notice.id)">编辑</button>
          <button @click.stop="handleDeleteNotice(notice.id)">删除</button>
        </div>
      </div>
    </div>

    <!-- 分页组件 -->
    <div class="pagination" v-if="pagination?.total > 0">
      <button :disabled="pagination?.pageNum === 1" @click="handlePageChange(pagination.pageNum - 1)">
        上一页
      </button>
      <span>第 {{ pagination?.pageNum }} 页 / 共 {{ pagination?.pages }} 页</span>
      <button :disabled="pagination?.pageNum >= pagination?.pages" @click="handlePageChange(pagination.pageNum + 1)">
        下一页
      </button>
    </div>

    <NoticeDialog v-model:visible="showDialog" :notice="selectedNotice" :editable="true" @edit="editNotice"
      @delete="handleDeleteNotice" />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useNoticeStore } from '@/stores/public/teacher/noticeStore'
import { formatAgo } from '@/utils/dateUtils'
import NoticeDialog from './NoticeDialog.vue'

const router = useRouter()
const route = useRoute()

const noticeStore = useNoticeStore()
const showDialog = ref(false)
const selectedNotice = ref({})

const props = defineProps({
  notices: {
    type: Array,
    default: () => []
  },
  pagination: {
    type: Object,
    default: () => ({
      pageNum: 1,
      pageSize: 5,
      total: 0,
      pages: 0
    })
  }
})

const emit = defineEmits(['page-change'])
const handlePageChange = (pageNum) => {
  noticeStore.pagination.pageNum = pageNum
  emit('page-change', pageNum)
}


const NOTICE_TYPES = noticeStore.NOTICE_TYPES
const NOTICE_TYPE_MAP = noticeStore.NOTICE_TYPE_MAP

// 获取当前标签的显示名称
const activeTabLabel = computed(() => {
  return NOTICE_TYPES[noticeStore.activeTab]?.label || '公告'
})

// 获取公告类型标签
const noticeTypeLabel = (typeValue) => {
  return NOTICE_TYPES[NOTICE_TYPE_MAP[typeValue]]?.label || '公告'
}


/* const editNotice = async (noticeId) => {
  try {
    await noticeStore.prepareEdit(noticeId)
    // 再导航到编辑页面
    router.push({
      name: 'EditAnnoucement',
      params: { courseId: route.params.courseId, noticeId }
    })
  } catch (error) {
    console.error('准备编辑失败:', error)
    alert('加载公告信息失败，请重试')
  }
} */

const editNotice = async (noticeOrId) => {
  try {
    // 如果传入的是完整公告对象，直接使用
    if (typeof noticeOrId !== 'string' && typeof noticeOrId !== 'number') {
      router.push({
        name: 'EditAnnoucement',
        params: { 
          courseId: route.params.courseId, 
          noticeId: noticeOrId.id 
        },
        query: { 
          noticeData: JSON.stringify(noticeOrId) 
        }
      })
      return
    }
    await noticeStore.prepareEdit(noticeOrId)
    router.push({
      name: 'EditAnnoucement',
      params: { 
        courseId: route.params.courseId, 
        noticeId: noticeOrId 
      }
    })
  } catch (error) {
    console.error('准备编辑失败:', error)
    ElMessage.error('加载公告信息失败，请重试')
  }
}


// 删除公告
const handleDeleteNotice = async (id) => {
  if (confirm('确定要删除这条公告吗？')) {
    try {
      await noticeStore.removeNotice(id)
      // 如果当前页没有数据了，回到上一页
      if (props.notices.length === 0 && props.pagination.pageNum > 1) {
        emit('page-change', props.pagination.pageNum - 1)
      }
      // 可以添加成功提示
      ElMessage.success('删除公告成功')
    } catch (error) {
      console.error('删除失败:', error)
      // 添加错误提示
      ElMessage.error('删除失败: ' + (error.message || '未知错误'))
    }
  }
}

// 显示公告详情
const showNoticeDetail = (notice) => {
  selectedNotice.value = notice
  showDialog.value = true
}

//过滤掉照片
const removeImages = (html) => {
  if (!html) return ''
  // 创建一个临时div元素
  const temp = document.createElement('div')
  temp.innerHTML = html
  // 移除所有img标签
  const images = temp.getElementsByTagName('img')
  while (images[0]) {
    images[0].parentNode.removeChild(images[0])
  }
  return temp.innerHTML
}
</script>


<style scoped>
.notice-list {
  margin-top: 20px;
}

.empty-tip,
.loading-tip,
.error-tip {
  text-align: center;
  padding: 40px;
  color: #999;
}

.error-tip {
  color: #f56c6c;
}

.notice-item {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
  background-color: #fff;
  cursor: pointer;
}

.notice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.notice-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.notice-time {
  font-size: 12px;
  color: #999;
}

.notice-content {
  margin-bottom: 12px;
  color: #666;
  line-height: 1.1;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  height: calc(2 * 1.6em);
  position: relative;
}



.notice-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.notice-type {
  color: #1890ff;
  padding: 2px 6px;
  background-color: #e6f7ff;
  border-radius: 2px;
}

.notice-actions button {
  margin-left: 8px;
  padding: 2px 8px;
  background: none;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  cursor: pointer;
}

.notice-actions button:hover {
  color: #1890ff;
  border-color: #1890ff;
}

/* 分页样式 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  gap: 10px;
}

.pagination button {
  padding: 5px 10px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fff;
  cursor: pointer;
}

.pagination button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}
</style>