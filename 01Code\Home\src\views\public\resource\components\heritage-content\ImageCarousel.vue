<template>
  <div class="image-carousel-section">
    <div class="section-header">
      <h1 class="main-title">{{ heritageData.name }}</h1>
    </div>
    
    <div class="carousel-container">
      <!-- 左箭头按钮 -->
      <button 
        class="carousel-arrow left-arrow" 
        @click="previousGroup"
        :disabled="currentGroupIndex === 0"
      >
        <i class="fas fa-chevron-left"></i>
      </button>
      
      <!-- 图片组展示区域 -->
      <div class="images-viewport">
        <div 
          class="images-track" 
          :style="{ transform: `translateX(-${currentGroupIndex * 100}%)` }"
        >
          <div 
            v-for="(group, groupIndex) in heritageData.imageGroups" 
            :key="groupIndex"
            class="image-group"
          >
            <div 
              v-for="(image, imageIndex) in group" 
              :key="imageIndex"
              class="image-card"
              @click="handleImageClick(image)"
            >
              <img :src="image.url" :alt="image.title" class="heritage-image" />
              <div class="image-overlay">
                <span class="image-title">{{ image.title }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右箭头按钮 -->
      <button 
        class="carousel-arrow right-arrow" 
        @click="nextGroup"
        :disabled="currentGroupIndex === heritageData.imageGroups.length - 1"
      >
        <i class="fas fa-chevron-right"></i>
      </button>
    </div>
    
    <!-- 指示器 -->
    <div class="carousel-indicators">
      <button
        v-for="(group, index) in heritageData.imageGroups"
        :key="index"
        :class="['indicator', { active: currentGroupIndex === index }]"
        @click="goToGroup(index)"
      ></button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const props = defineProps({
  heritageData: {
    type: Object,
    required: true
  }
})

const currentGroupIndex = ref(0)

// 切换到上一组图片
const previousGroup = () => {
  if (currentGroupIndex.value > 0) {
    currentGroupIndex.value--
  }
}

// 切换到下一组图片
const nextGroup = () => {
  if (currentGroupIndex.value < props.heritageData.imageGroups.length - 1) {
    currentGroupIndex.value++
  }
}

// 跳转到指定组
const goToGroup = (index) => {
  currentGroupIndex.value = index
}

// 处理图片点击
const handleImageClick = (image) => {
  console.log('点击图片:', image.title)
  // 这里可以实现图片预览功能
}
</script>

<style lang="scss" scoped>
@use '@/styles/variables.scss' as *;

.image-carousel-section {
  padding: 40px 20px;
  //background: linear-gradient(135deg, $purple-primary 0%, $purple-lightest 100%);
  
  .section-header {
    text-align: center;
    margin-bottom: 40px;
    
    .main-title {
      font-size: 32px;
      font-weight: 700;
      color: $purple-primary;
      margin: 0 0 12px 0;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }
  
  .carousel-container {
    position: relative;
    max-width: 1000px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    gap: 20px;
    
    .carousel-arrow {
      width: 50px;
      height: 50px;
      background: rgba(255, 255, 255, 0.8);
      border: 2px solid $purple-primary;
      border-radius: 50%;
      color: $purple-primary;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      transition: all 0.3s ease;
      z-index: 10;
      
      &:hover:not(:disabled) {
        background: $purple-primary;
        color: white;
        transform: scale(1.1);
      }
      
      &:disabled {
        opacity: 0.3;
        cursor: not-allowed;
      }
    }
    
    .images-viewport {
      flex: 1;
      overflow: hidden;
      border-radius: 12px;
      
      .images-track {
        display: flex;
        transition: transform 0.5s ease-in-out;
        
        .image-group {
          min-width: 100%;
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          gap: 16px;
          
          .image-card {
            position: relative;
            aspect-ratio: 4/3;
            border: 2px solid $purple-primary;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
            
            &:hover {
              transform: translateY(-4px);
              box-shadow: 0 8px 24px rgba(33, 150, 243, 0.3);
              
              .image-overlay {
                opacity: 1;
              }
            }
            
            .heritage-image {
              width: 100%;
              height: 100%;
              object-fit: cover;
              transition: transform 0.3s ease;
            }
            
            .image-overlay {
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;
              background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
              color: white;
              padding: 12px;
              opacity: 0;
              transition: opacity 0.3s ease;
              
              .image-title {
                font-size: 12px;
                font-weight: 500;
              }
            }
          }
        }
      }
    }
  }
  
  .carousel-indicators {
    display: flex;
    gap: 8px;
    margin-top: 24px;
    justify-content: center;
    
    .indicator {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      border: none;
      background: rgba($purple-primary, 0.3);
      cursor: pointer;
      transition: all 0.3s ease;
      
      &.active {
        background: $purple-primary;
        transform: scale(1.2);
      }
      
      &:hover {
        background: $purple-primary;
      }
    }
  }
}

@media (max-width: 768px) {
  .image-carousel-section {
    padding: 24px 16px;
    
    .section-header .main-title {
      font-size: 24px;
    }
    
    .carousel-container {
      .carousel-arrow {
        width: 40px;
        height: 40px;
        font-size: 14px;
      }
      
      .images-viewport .images-track .image-group {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
      }
    }
  }
}
</style>