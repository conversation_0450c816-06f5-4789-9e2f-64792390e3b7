<template>
  <div class="ability-graph-page">
    <!-- 将Topbar移出滚动区域，固定在顶部 -->
    <Topbar class="fixed-topbar" @goBack="goBack" @cancel="handleCancel" @save="handleSave" @validate="handleValidate"
      ref="topbar" />

    <div class="main-area">
      <div class="content-container" ref="contentContainer">
        <!-- 左侧下拉框列表 -->
        <AbilityListPanel class="left-panel" @create-new="handleCreateNew" @select-main="handleSelectMainAbility"
          @select-sub="handleSelectSubAbility" ref="leftPanel" :style="panelStyle" />

        <!-- 右侧表单 -->
        <AbilityFormPanel class="right-panel" @edit="handleEdit" @deleted="handleAbilityDeleted" ref="formPanel" />
      </div>
    </div>

    <!-- 回到顶部按钮 -->
    <button class="back-to-top" :class="{ 'show': showBackToTop }" @click="scrollToTop">
      ↑
    </button>

    <!-- 保存中遮罩 -->
    <div v-if="isSaving" class="saving-overlay">
      <div class="saving-content">
        <div class="saving-spinner"></div>
        <p>保存中，请稍候...</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router'
import { useAbilityGraphStore } from '@/stores/teacher/graphManager/abilityGraphStore';
import Topbar from './components/Topbar.vue'
import AbilityListPanel from './components/AbilityListPanel.vue'
import AbilityFormPanel from './components/AbilityFormPanel.vue'

const route = useRoute()
const router = useRouter()
const store = useAbilityGraphStore();
const topbar = ref(null);
const formPanel = ref(null);
const contentContainer = ref(null);
const leftPanel = ref(null);
const showBackToTop = ref(false);
const isSaving = ref(false);
const panelStyle = ref({
  position: 'absolute',
  top: '0px',
  left: '80px',
  width: '300px'
});

// 返回
const goBack = () => {
  router.push({
    name: 'GraphManager',
    params: {
      courseId: route.params.courseId,
      courseName: route.params.courseName || '默认课程'
    }
  })
}

// 处理创建新能力事件
const handleCreateNew = () => {
  // 设置编辑模式
  topbar.value.setEditMode(true);
  leftPanel.value.setEditingState(true);
  // 重置表单并进入编辑状态
  formPanel.value.startNewAbility();
};

// 处理选择主能力
const handleSelectMainAbility = async (mainAbility) => {
    if (!mainAbility) {
        console.warn('未接收到有效的主能力数据');
        formPanel.value?.resetToEmptyState?.();
        return;
    }

    try {
        // 获取主能力详情（包含子能力）
        const detailRes = await store.getMainAbilityDetail(mainAbility.abilityId);

        // 如果有子能力，批量获取所有子能力的关联知识点
        let subAbilitiesWithKnowledge = [];
        if (detailRes.result?.subAbilities?.length > 0) {
            subAbilitiesWithKnowledge = await store.fetchAllSubAbilitiesKnowledgeTree(detailRes.result.subAbilities);
        }

        // 加载到右侧面板（包含所有子能力的知识点数据）
        await formPanel.value.loadAbilityDetails(
            detailRes.result || mainAbility,
            subAbilitiesWithKnowledge
        );

    } catch (error) {
        console.error('加载主能力详情失败:', error);
    }
};

// 处理选择子能力
const handleSelectSubAbility = async (mainAbility, subAbility) => {
    try {
        // 1. 获取完整的子能力列表
        const subAbilities = await store.fetchSubAbilities(mainAbility.abilityId);
        
        // 2. 只为当前选中的子能力加载知识点
        const currentSubAbility = subAbilities.find(sub => sub.abilityId === subAbility.abilityId);
        if (currentSubAbility) {
            currentSubAbility.knowledgeNodes = await store.fetchRelatedKnowledgeTree(subAbility.abilityId);
        }
        
        // 3. 加载到右侧面板
        await formPanel.value.loadAbilityDetails(
            mainAbility, 
            subAbilities
        );
        
        // 4. 滚动到选中位置
        const index = subAbilities.findIndex(sub => sub.abilityId === subAbility.abilityId);
        if (index >= 0) formPanel.value.scrollToSubAbility(index);
    } catch (error) {
        console.error('加载子能力失败:', error);
    }
};

// 处理编辑事件
const handleEdit = () => {
  topbar.value.setEditMode(true);
  leftPanel.value.setEditingState(true);
};

// 处理取消事件
const handleCancel = () => {
  formPanel.value.cancelEdit();
  topbar.value.setEditMode(false);
  leftPanel.value.setEditingState(false);
};

// 处理保存事件
const handleSave = async (callback) => {
  try {
    isSaving.value = true;
    const success = await formPanel.value.confirmEdit();
    if (success) {
      // 重新加载数据
      await store.initAbilityGraph(
        route.params.courseId,
        route.params.graphId
      );
      // 刷新左侧列表
      leftPanel.value.refreshList();
      // 退出编辑状态
      leftPanel.value.setEditingState(false);

      // 显示成功提示
      // showSuccessToast('保存成功');
    }
    callback(success);
  } catch (error) {
    console.error('保存失败:', error);
    // 显示错误提示
    // showErrorToast(error.message);
    callback(false);
  } finally {
    isSaving.value = false;
  }
};

// 处理验证请求
const handleValidate = (callback) => {
  const isValid = formPanel.value.validateForm();
  callback(isValid);
};

// 处理删除事件
const handleAbilityDeleted = (isMainAbilityDeleted = false) => {
    // 重新加载数据
    store.initAbilityGraph(route.params.courseId, route.params.graphId);
    // 刷新左侧列表
    leftPanel.value.refreshList();

    // 只有删除主能力时才重置Topbar状态
    if (isMainAbilityDeleted) {
        topbar.value.setEditMode(false);
        leftPanel.value.setEditingState(false);
    }
};

// 处理滚动条事件
const handleScroll = () => {
  if (!contentContainer.value || !leftPanel.value) return;

  // 控制回到顶部按钮显示
  showBackToTop.value = window.scrollY > 100;

  // 更新左侧面板位置
  const scrollTop = window.scrollY || document.documentElement.scrollTop;
  panelStyle.value = {
    ...panelStyle.value,
    top: `${scrollTop}px`
  };
};

// 回到顶部
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  });
};

onMounted(async () => {
    window.addEventListener('scroll', handleScroll);
    handleScroll();
    
    if (route.params.graphId) {
        // 1. 初始化能力图谱
      const initSuccess = await store.initAbilityGraph(
        route.params.courseId,
        route.params.graphId
      );
        
        if (!initSuccess) {
            console.error('初始化能力图谱失败');
            return;
        }
        
        // 2. 刷新左侧面板获取主能力列表
        await nextTick();
        if (leftPanel.value) {
            await leftPanel.value.refreshList();
            console.log("左侧面板成功刷新");
            
            // 3. 如果有主能力，加载第一个主能力的详情（包含子能力）
            if (leftPanel.value.abilityList?.length > 0) {
                const firstMainAbility = leftPanel.value.abilityList[0];
                await handleSelectMainAbility(firstMainAbility);
            }
        }
    }
});

onUnmounted(() => {
    // 移除滚动事件监听
    window.removeEventListener('scroll', handleScroll);
});
</script>

<style lang="scss" scoped>
.ability-graph-page {
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  padding-top: 60px; /* 为固定Topbar留出空间 */
}

/* 新增固定Topbar样式 */
.fixed-topbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.main-area {
  flex: 1;
  position: relative;
  overflow: hidden;
  background-color: #e8e8e8;
  padding: 20px;
  margin-top: 0;
  /* 移除之前的顶部间距 */

  .content-container {
    display: flex;
    height: 100%;
    gap: 20px;
    position: relative; // 确保绝对定位的基准
  }

  .left-panel {
    width: 300px;
    border-right: 1px solid #e8e8e8;
    padding-right: 20px;
    z-index: 10; // 确保在右侧内容上方
  }

  .right-panel {
    flex: 1;
    padding-left: 20px;
    margin-right: 80px;
    margin-left: 400px; // 为左侧面板留出空间 (300 + 80 + 20)
  }
}

/* 回到顶部按钮样式 */
.back-to-top {
  position: fixed;
  right: 30px;
  bottom: 30px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #4c7bff;
  color: white;
  border: none;
  cursor: pointer;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 100;

  &:hover {
    background-color: #3a6ae0;
    transform: translateY(-2px);
  }

  &.show {
    opacity: 1;
    visibility: visible;
  }
}

/* 保存中遮罩样式 */
.saving-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .saving-content {
    background: white;
    padding: 40px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);

    .saving-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #4c7bff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 20px;
    }

    p {
      margin: 0;
      font-size: 16px;
      color: #333;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>