<!-- src/components/PermissionGuard.vue -->
<template>
  <slot v-if="hasPermission"></slot>
  <UnauthorizedView v-else />
</template>

<script setup>
import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import UnauthorizedView from '@/views/UnauthorizedView.vue'

const props = defineProps({
  roles: {
    type: Array,
    default: () => []
  }
})

const authStore = useAuthStore()

const hasPermission = computed(() => {
  // 如果没有指定roles，则只需要登录
  if (props.roles.length === 0) return authStore.isAuthenticated
  
  // 检查用户角色是否在允许的roles中
  return props.roles.includes(authStore.user?.role)
})
</script>