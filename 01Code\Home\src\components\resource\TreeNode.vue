<template>
  <li class="tree-node" :style="{ paddingLeft: `${depth * 16 + 16}px` }">
    <!-- 文件夹节点 -->
    <div class="node-content" :class="{ 'selected': selectedGroup.id === node.id }" @click.stop="selectGroup(node)">
      <span class="toggle-icon" @click.stop="toggleExpand(node)">
        {{ node.expanded ? '▼' : '▶' }}
      </span>
      <span class="folder-name">
        {{ node.name }}
        <span v-if="node.children" class="item-count">({{ node.children.length }})</span>
      </span>
      
      <!-- 操作按钮 -->
      <div class="node-actions">
        <button v-if="depth < 3" class="action-btn" @click.stop="emitCreateFolder(node)">
          +
        </button>
      </div>
    </div>

    <!-- 子文件夹递归渲染 -->
    <ul v-if="node.expanded && node.children" class="sub-tree" :class="{ expanded: node.expanded }">
      <tree-node
        v-for="child in node.children"
        :key="child.id"
        :node="child"
        :selected-group="selectedGroup"
        :depth="depth + 1"
        @select="$emit('select', $event)"
        @toggle="$emit('toggle', $event)"
        @create-folder="$emit('create-folder', $event)"
      />
    </ul>
  </li>
</template>

<script setup>
defineProps({
  node: {
    type: Object,
    required: true,
    default: () => ({}),
  },
  selectedGroup: {
    type: Object,
    default: () => ({}),
  },
  depth: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits(['select', 'toggle', 'create-folder']);

const toggleExpand = (node) => {
  emit('toggle', node);
};

const selectGroup = (node) => {
  emit('select', node);
};

const emitCreateFolder = (node) => {
  emit('create-folder', node);
};
</script>

<style lang="scss" scoped>
.tree-node {
  list-style: none;
  margin: 4px 0;

  .node-content {
    padding: 8px 12px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: background 0.2s;

    &:hover {
      background: #f5f7fa;
    }

    &.selected {
      background: #e6f1ff;
      font-weight: 500;
    }
  }
}

.toggle-icon {
  display: inline-block;
  width: 20px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
}

.folder-name {
  flex: 1;
}

.item-count {
  font-size: 12px;
  color: #999;
  margin-left: 4px;
}

.node-actions {
  opacity: 0;
  transition: opacity 0.2s;

  .node-content:hover & {
    opacity: 1;
  }
}

.action-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  color: #4c7bff;
  cursor: pointer;
  border-radius: 4px;

  &:hover {
    background: #e6eeff;
  }
}

.sub-tree {
  margin: 0;
  padding: 0;
  transition: max-height 0.3s ease-out;
  overflow: hidden;

  &.expanded {
    max-height: 1000px;
  }

  &:not(.expanded) {
    max-height: 0;
  }
}
</style>