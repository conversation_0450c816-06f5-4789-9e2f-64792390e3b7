// src/api/teacher/correction.js
import request from '@/api/service'
import qs from 'qs'

/** -------------------- 催交考试或作业 -------------------- **/
export function urgeSubmitExamOrAssignment(data, isExam = true) {
    const url = isExam ? '/exam/urge-submit-exam' : '/assignment/urge-submit-assignment'
    return request.post(url, data)
}

/** -------------------- 批改简答题（考试或作业） -------------------- **/
// 这里是 x-www-form-urlencoded 格式，接口路径不一样
export function correctShortAnswer(data, isExam = true) {
    const formBody = Object.keys(data)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(data[key])}`)
        .join('&')

    const url = isExam
        ? '/api/teacher/exam/correct-short-answer'
        : '/api/teacher/assignment/correct-short-answer'

    return request.post(url, formBody, {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
}

/** -------------------- 修改题目得分 -------------------- **/
export function updateQuestionScore(data, isExam = true) {
    const url = isExam ? '/api/teacher/exam/update-score' : '/api/teacher/assignment/update-score'
    return request.post(url, data)
}

/** -------------------- 导出成绩 -------------------- **/
export function exportSubmitRecords(params, isExam = true) {
    const url = isExam
        ? '/api/teacher/exam-export/submit-records'
        : '/api/teacher/assignment-export/submit-records'
    return request.get(url, { params, responseType: 'blob' })
}

/** -------------------- 发布成绩 -------------------- **/
export function publishScore(data, isExam = true) {
    const url = isExam
        ? '/api/teacher/exam/publish-score'
        : '/api/teacher/assignment/publish-score'

    // 判断是否需要用 x-www-form-urlencoded
    const useForm = isExam

    if (useForm) {
        return request.post(url, qs.stringify(data), {
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
        })
    } else {
        return request.post(url, data)
    }
}

/** -------------------- 获取学生作答详情 -------------------- **/
export function getSubmitDetail(params, isExam = true) {
    const url = isExam
        ? '/api/teacher/exam/submit-detail'
        : '/api/teacher/assignment/submit-detail'

    return request.get(url, { params })
}

/** -------------------- 获取某考试或作业某班级提交记录 -------------------- **/
export function getSubmitRecordsByClass(params, isExam = true) {
    const url = isExam
        ? '/api/teacher/exam/submit-records/by-class'
        : '/api/teacher/assignment/submit-records/by-class'
    return request.get(url, { params })
}

// 分页查看某考试某班级学生提交记录
export function getExamSubmitRecords(params) {
    return request.get('/api/teacher/exam/submit-records/by-class', { params })
}

//分页查看某作业某班级学生提交记录
export function getAssignmentSubmitRecords(params) {
    return request.get('/api/teacher/assignment/submit-records/by-class', { params })
}
