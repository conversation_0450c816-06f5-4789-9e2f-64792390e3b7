<template>
  <div class="project-docs">
    <div class="content-header">
      <h2>项目文档</h2>
      <div class="header-divider"></div>
    </div>
    
    <div class="docs-categories">
      <div class="category-tabs">
        <button 
          v-for="(category, index) in docCategories" 
          :key="category.key"
          :class="{ active: activeCategory === index }"
          @click="activeCategory = index"
          class="tab-button"
        >
          <i :class="category.icon"></i>
          <span>{{ category.name }}</span>
        </button>
      </div>
      
      <div class="category-content">
        <div class="docs-grid">
          <div 
            class="doc-item" 
            v-for="doc in currentDocs" 
            :key="doc.id"
          >
            <div class="doc-icon">
              <i :class="doc.icon"></i>
            </div>
            <div class="doc-info">
              <h3>{{ doc.title }}</h3>
              <p>{{ doc.description }}</p>
              <div class="doc-meta">
                <span class="doc-size">{{ doc.size }}</span>
                <span class="doc-date">{{ doc.updateDate }}</span>
              </div>
            </div>
            <div class="doc-actions">
              <button class="action-btn preview" @click="previewDocument(doc)">
                <i class="fas fa-eye"></i>
                预览
              </button>
              <button class="action-btn download" @click="downloadDoc(doc)">
                <i class="fas fa-download"></i>
                下载
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 文档预览模态框 -->
    <div v-if="showPreview" class="preview-modal" @click="closePreview">
      <div class="preview-content" @click.stop>
        <div class="preview-header">
          <h3>{{ previewDoc.title }}</h3>
          <button class="close-btn" @click="closePreview">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="preview-body">
          <p>{{ previewDoc.content }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const activeCategory = ref(0)
const showPreview = ref(false)
const previewDoc = ref({})

const docCategories = [
  { key: 'manual', name: '操作手册', icon: 'fas fa-book' },
  { key: 'guide', name: '实验指导', icon: 'fas fa-clipboard-list' },
  { key: 'reference', name: '参考资料', icon: 'fas fa-bookmark' },
  { key: 'template', name: '模板文件', icon: 'fas fa-file-alt' }
]

const docsData = {
  manual: [
    {
      id: 1,
      title: '虚拟仿真实验操作手册',
      description: '详细介绍实验平台的使用方法和操作流程',
      size: '2.5MB',
      updateDate: '2024-03-15',
      icon: 'fas fa-file-pdf',
      content: '本手册详细介绍了虚拟仿真实验平台的各项功能和操作方法...'
    },
    {
      id: 2,
      title: '系统安装配置指南',
      description: '系统环境要求和安装配置步骤说明',
      size: '1.8MB',
      updateDate: '2024-03-10',
      icon: 'fas fa-file-pdf',
      content: '系统安装前请确保计算机满足以下最低配置要求...'
    }
  ],
  guide: [
    {
      id: 3,
      title: '标本采集实验指导书',
      description: '临床标本采集的标准操作程序和注意事项',
      size: '3.2MB',
      updateDate: '2024-03-20',
      icon: 'fas fa-file-word',
      content: '标本采集是临床检验的第一步，直接影响检验结果的准确性...'
    },
    {
      id: 4,
      title: '病原体检测实验指导',
      description: '各种病原体检测方法的详细操作指导',
      size: '4.1MB',
      updateDate: '2024-03-18',
      icon: 'fas fa-file-word',
      content: '病原体检测包括形态学检查、培养检查、免疫学检查等多种方法...'
    }
  ],
  reference: [
    {
      id: 5,
      title: '临床微生物学检验规范',
      description: '国家标准的临床微生物学检验技术规范',
      size: '5.6MB',
      updateDate: '2024-02-28',
      icon: 'fas fa-file-pdf',
      content: '本规范规定了临床微生物学检验的基本要求和技术标准...'
    },
    {
      id: 6,
      title: '感染性疾病诊疗指南',
      description: '常见感染性疾病的诊断和治疗指南',
      size: '7.3MB',
      updateDate: '2024-02-25',
      icon: 'fas fa-file-pdf',
      content: '感染性疾病的诊断需要结合临床表现、实验室检查等多方面信息...'
    }
  ],
  template: [
    {
      id: 7,
      title: '实验报告模板',
      description: '标准化的实验报告格式模板',
      size: '0.5MB',
      updateDate: '2024-03-12',
      icon: 'fas fa-file-excel',
      content: '实验报告应包含实验目的、方法、结果、讨论等基本要素...'
    },
    {
      id: 8,
      title: '质控记录表格',
      description: '实验质量控制记录的标准表格',
      size: '0.3MB',
      updateDate: '2024-03-08',
      icon: 'fas fa-file-excel',
      content: '质控记录是保证实验结果准确性的重要文档...'
    }
  ]
}

const currentDocs = computed(() => {
  const categoryKey = docCategories[activeCategory.value].key
  return docsData[categoryKey] || []
})

const previewDocument = (doc) => {
  previewDoc.value = doc
  showPreview.value = true
}

const closePreview = () => {
  showPreview.value = false
  previewDoc.value = {}
}

const downloadDoc = (doc) => {
  // 模拟下载功能
  console.log('下载文档:', doc.title)
  // 实际项目中这里会触发文件下载
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.project-docs {
  padding: 24px;
  max-width: 1000px;
  
  .content-header {
    margin-bottom: 32px;
    
    h2 {
      font-size: 28px;
      font-weight: 700;
      color: $text-color;
      margin: 0 0 16px 0;
    }
    
    .header-divider {
      width: 60px;
      height: 4px;
      background: linear-gradient(90deg, $primary-color, lighten($primary-color, 20%));
      border-radius: 2px;
    }
  }
  
  .docs-categories {
    .category-tabs {
      display: flex;
      gap: 8px;
      margin-bottom: 24px;
      border-bottom: 1px solid $border-color;
      
      .tab-button {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: none;
        border: none;
        border-radius: 8px 8px 0 0;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 14px;
        color: rgba($text-color, 0.7);
        
        &:hover {
          background: rgba($primary-color, 0.05);
          color: $primary-color;
        }
        
        &.active {
          background: $primary-color;
          color: white;
          font-weight: 600;
        }
        
        i {
          font-size: 16px;
        }
      }
    }
    
    .category-content {
      .docs-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 20px;
        
        .doc-item {
          background: white;
          border: 1px solid $border-color;
          border-radius: 12px;
          padding: 20px;
          transition: all 0.3s ease;
          
          &:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
          }
          
          .doc-icon {
            width: 48px;
            height: 48px;
            background: rgba($primary-color, 0.1);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            
            i {
              font-size: 20px;
              color: $primary-color;
            }
          }
          
          .doc-info {
            margin-bottom: 16px;
            
            h3 {
              font-size: 16px;
              font-weight: 600;
              color: $text-color;
              margin: 0 0 8px 0;
              line-height: 1.4;
            }
            
            p {
              font-size: 14px;
              color: rgba($text-color, 0.7);
              margin: 0 0 12px 0;
              line-height: 1.5;
            }
            
            .doc-meta {
              display: flex;
              gap: 16px;
              font-size: 12px;
              color: rgba($text-color, 0.5);
              
              .doc-size,
              .doc-date {
                display: flex;
                align-items: center;
                gap: 4px;
                
                &::before {
                  font-family: 'Font Awesome 5 Free';
                  font-weight: 900;
                }
              }
              
              .doc-size::before {
                content: '\f15c';
              }
              
              .doc-date::before {
                content: '\f073';
              }
            }
          }
          
          .doc-actions {
            display: flex;
            gap: 8px;
            
            .action-btn {
              flex: 1;
              padding: 8px 12px;
              border: none;
              border-radius: 6px;
              cursor: pointer;
              font-size: 12px;
              font-weight: 500;
              transition: all 0.3s ease;
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 4px;
              
              &.preview {
                background: rgba($primary-color, 0.1);
                color: $primary-color;
                
                &:hover {
                  background: rgba($primary-color, 0.2);
                }
              }
              
              &.download {
                background: $primary-color;
                color: white;
                
                &:hover {
                  background: darken($primary-color, 10%);
                }
              }
            }
          }
        }
      }
    }
  }
  
  .preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    
    .preview-content {
      background: white;
      border-radius: 12px;
      max-width: 600px;
      max-height: 80vh;
      width: 90%;
      overflow: hidden;
      
      .preview-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20px;
        border-bottom: 1px solid $border-color;
        
        h3 {
          font-size: 18px;
          font-weight: 600;
          color: $text-color;
          margin: 0;
        }
        
        .close-btn {
          width: 32px;
          height: 32px;
          background: none;
          border: none;
          border-radius: 50%;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          color: rgba($text-color, 0.5);
          transition: all 0.3s ease;
          
          &:hover {
            background: rgba($text-color, 0.1);
            color: $text-color;
          }
        }
      }
      
      .preview-body {
        padding: 20px;
        max-height: 400px;
        overflow-y: auto;
        
        p {
          font-size: 14px;
          line-height: 1.6;
          color: $text-color;
          margin: 0;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .project-docs {
    padding: 16px;
    
    .content-header h2 {
      font-size: 24px;
    }
    
    .category-tabs {
      flex-wrap: wrap;
      
      .tab-button {
        font-size: 12px;
        padding: 10px 16px;
      }
    }
    
    .docs-grid {
      grid-template-columns: 1fr;
    }
    
    .preview-content {
      width: 95%;
      max-height: 90vh;
    }
  }
}
</style>
