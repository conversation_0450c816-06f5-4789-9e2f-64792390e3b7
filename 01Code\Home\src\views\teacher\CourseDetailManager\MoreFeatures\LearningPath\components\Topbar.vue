<template>
  <header class="top-bar">
    <!-- 左侧返回按钮 -->
    <div class="left-group">
      <button class="icon-btn" @click="handleGoBack" title="返回">
        <img :src="backIcon" alt="返回" class="icon" />
        <span>返回</span>
      </button>
    </div>

    <!-- 右侧操作按钮 -->
    <div class="right-group" v-if="showActions">
      <button class="action-btn cancel-btn" @click="handleCancel">
        取消
      </button>
      <button class="action-btn save-btn" @click="handleSave" :disabled="isSaving">
        <span v-if="!isSaving">保存</span>
        <span v-else>保存中...</span>
      </button>
    </div>
  </header>
</template>

<script setup>
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import backIcon from '@/assets/courseMap-icon/createKgGraph/topbar/back.svg'

const props = defineProps({
  modelValue: <PERSON><PERSON><PERSON>,
  disabledInsert: <PERSON><PERSON><PERSON>,
  disabledDelete: <PERSON><PERSON><PERSON>,
  selectedNodeId: [String, Number]
})

const emit = defineEmits(['goBack', 'cancel', 'save', 'validate' ])

const showActions = ref(false);
const isSaving = ref(false);

// 暴露方法给父组件调用
const setEditMode = (isEditing) => {
  showActions.value = isEditing;
};

// 处理返回按钮点击
const handleGoBack = () => {
  if (showActions.value) {
    ElMessage.warning('请先保存或取消当前更改');
    return;
  }
  emit('goBack');
};

//处理取消事件
const handleCancel = () => {
  emit('cancel');
  showActions.value = false;
};

// 处理保存事件
const handleSave = async () => {
    try {
        // 先进行验证
        const isValid = await new Promise((resolve) => {
            emit('validate', resolve);
        });
        
        if (!isValid) return; // 验证失败直接返回
        
        isSaving.value = true;
        const success = await new Promise((resolve) => {
            emit('save', resolve);
        });
        
        if (success) {
            showActions.value = false;
            // 可以在这里添加保存成功的提示
        }
    } catch (error) {
        console.error('保存过程中出错:', error);
        // 可以在这里添加保存失败的提示
    } finally {
        isSaving.value = false;
    }
};

defineExpose({ setEditMode, isSaving });
</script>

<style lang="scss" scoped>
.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  padding: 0 20px;
  border-bottom: 1px solid #eee;
  background-color: #fff;

  .icon-btn {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    border: none;
    background: none;
    padding: 6px 10px;
    cursor: pointer;
    font-size: 14px;
    color: #333;

    span {
      font-size: 16px;
    }

    &:hover {
      background-color: #f5f5f5;
      border-radius: 4px;
    }

    .icon {
      width: 20px;
      height: 20px;
    }
  }

  .right-group {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .action-btn {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;

    &.cancel-btn {
      background-color: #fff;
      border: 1px solid #d9d9d9;
      color: #666;

      &:hover {
        border-color: #4c7bff;
        color: #4c7bff;
      }
    }

    &.save-btn {
      background-color: #4c7bff;
      border: 1px solid #4c7bff;
      color: white;

      &:hover {
        background-color: #3a6ae0;
        border-color: #3a6ae0;
      }

      &:disabled {
        background-color: #d9d9d9;
        border-color: #d9d9d9;
        cursor: not-allowed;
      }
    }
  }
}
</style>