<template>
  <div class="project-intro-content">
    <div class="content-header">
      <h2>项目介绍</h2>
      <div class="header-divider"></div>
    </div>
    
    <div class="intro-section">
      <h3>实验概述</h3>
      <p>{{ projectData.overview }}</p>
    </div>
    
    <div class="intro-section">
      <h3>实验目标</h3>
      <ul>
        <li v-for="goal in projectData.goals" :key="goal">{{ goal }}</li>
      </ul>
    </div>
    
    <div class="intro-section">
      <h3>适用对象</h3>
      <div class="target-audience">
        <div class="audience-item" v-for="audience in projectData.targetAudience" :key="audience.type">
          <div class="audience-icon">
            <i :class="audience.icon"></i>
          </div>
          <div class="audience-info">
            <h4>{{ audience.type }}</h4>
            <p>{{ audience.description }}</p>
          </div>
        </div>
      </div>
    </div>
    
    <div class="intro-section">
      <h3>技术特点</h3>
      <div class="features-grid">
        <div class="feature-card" v-for="feature in projectData.features" :key="feature.title">
          <div class="feature-icon">
            <i :class="feature.icon"></i>
          </div>
          <h4>{{ feature.title }}</h4>
          <p>{{ feature.description }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const projectData = ref({
  overview: "本实验内容为模拟临床标本检测感染病原体的虚拟仿真实验，学生个人入学后，同时参与，借助学生（新临床医学生）有感染性，不能在真实的临床环境中进行学习的问题，本实验通过虚拟仿真技术，让学生能够在安全的环境中学习临床标本检测的相关知识和技能。",
  
  goals: [
    "掌握临床标本采集的基本原理和操作技能",
    "熟悉病原体检测的实验流程和质量控制",
    "培养学生的临床思维和问题解决能力",
    "提高学生对感染性疾病的认识和防控意识"
  ],
  
  targetAudience: [
    {
      type: "医学专业学生",
      description: "临床医学、预防医学等相关专业本科生",
      icon: "fas fa-user-graduate"
    },
    {
      type: "医学研究生",
      description: "从事感染病学、临床检验等方向的研究生",
      icon: "fas fa-microscope"
    },
    {
      type: "医务工作者",
      description: "需要继续教育的临床医生和检验技师",
      icon: "fas fa-user-md"
    }
  ],
  
  features: [
    {
      title: "真实场景模拟",
      description: "高度还原临床实验室环境",
      icon: "fas fa-vr-cardboard"
    },
    {
      title: "交互式操作",
      description: "支持多种实验操作的交互体验",
      icon: "fas fa-hand-pointer"
    },
    {
      title: "智能评估",
      description: "实时反馈和智能评估系统",
      icon: "fas fa-brain"
    },
    {
      title: "安全可靠",
      description: "避免真实病原体接触风险",
      icon: "fas fa-shield-alt"
    }
  ]
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.project-intro-content {
  padding: 24px;
  max-width: 1000px;
  
  .content-header {
    margin-bottom: 32px;
    
    h2 {
      font-size: 28px;
      font-weight: 700;
      color: $text-color;
      margin: 0 0 16px 0;
    }
    
    .header-divider {
      width: 60px;
      height: 4px;
      background: linear-gradient(90deg, $primary-color, lighten($primary-color, 20%));
      border-radius: 2px;
    }
  }
  
  .intro-section {
    margin-bottom: 32px;
    
    h3 {
      font-size: 20px;
      font-weight: 600;
      color: $text-color;
      margin-bottom: 16px;
      position: relative;
      padding-left: 16px;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background: $primary-color;
        border-radius: 2px;
      }
    }
    
    p {
      font-size: 16px;
      line-height: 1.8;
      color: $text-color;
      margin-bottom: 16px;
    }
    
    ul {
      list-style: none;
      padding: 0;
      
      li {
        font-size: 16px;
        line-height: 1.6;
        color: $text-color;
        margin-bottom: 8px;
        padding-left: 24px;
        position: relative;
        
        &::before {
          content: '•';
          position: absolute;
          left: 0;
          color: $primary-color;
          font-weight: bold;
          font-size: 18px;
        }
      }
    }
  }
  
  .target-audience {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    
    .audience-item {
      display: flex;
      align-items: flex-start;
      gap: 16px;
      padding: 20px;
      background: white;
      border-radius: 8px;
      border: 1px solid $border-color;
      transition: all 0.3s ease;
      
      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }
      
      .audience-icon {
        width: 48px;
        height: 48px;
        background: rgba($primary-color, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        
        i {
          font-size: 20px;
          color: $primary-color;
        }
      }
      
      .audience-info {
        h4 {
          font-size: 16px;
          font-weight: 600;
          color: $text-color;
          margin: 0 0 8px 0;
        }
        
        p {
          font-size: 14px;
          color: rgba($text-color, 0.7);
          margin: 0;
          line-height: 1.5;
        }
      }
    }
  }
  
  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
    
    .feature-card {
      text-align: center;
      padding: 24px;
      background: white;
      border-radius: 12px;
      border: 1px solid $border-color;
      transition: all 0.3s ease;
      
      &:hover {
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        transform: translateY(-4px);
      }
      
      .feature-icon {
        width: 64px;
        height: 64px;
        background: linear-gradient(135deg, $primary-color, lighten($primary-color, 10%));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 16px;
        
        i {
          font-size: 24px;
          color: white;
        }
      }
      
      h4 {
        font-size: 16px;
        font-weight: 600;
        color: $text-color;
        margin: 0 0 12px 0;
      }
      
      p {
        font-size: 14px;
        color: rgba($text-color, 0.7);
        margin: 0;
        line-height: 1.5;
      }
    }
  }
}

@media (max-width: 768px) {
  .project-intro-content {
    padding: 16px;
    
    .content-header h2 {
      font-size: 24px;
    }
    
    .target-audience {
      grid-template-columns: 1fr;
    }
    
    .features-grid {
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
  }
}
</style>
