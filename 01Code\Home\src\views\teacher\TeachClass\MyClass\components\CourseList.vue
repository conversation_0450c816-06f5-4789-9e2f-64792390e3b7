<!--src\views\teacher\TeachClass\MyClass\components\CourseList.vue-->
<template>
  <div>
    <div class="top-bar">
      <p>{{ getCourseCountText() }}</p>
      <button class="new-course-btn" @click="$router.push('/teacher/create-course')">
        新建教学课堂
      </button>
    </div>
    <div v-if="filteredCourses.length > 0" class="course-list">
      <div 
        v-for="course in filteredCourses" 
        :key="course.id" 
        class="course-item"
        @click="goToCourseDetail(course.id,course.name)"
      >
        <div class="course-cover-container">
          <img :src="course.courseCover" @error="handleImageError" alt="课程封面" class="course-cover">
        </div>
        <h3>{{ course.name }}</h3>
        <p>{{ userStore.user?.name }} | {{ course.major || '未设置专业' }}</p>
        <div class="course-status">
          <span :class="getStatusClass(course.status)">{{ getStatusText(course.status) }}</span>
          <span :class="getStateClass(course.state)">{{ getStateText(course.state) }}</span>
        </div>
      </div>
    </div>

    <!-- 空状态提示 -->
    <div v-else class="empty-state">
      <img 
        src="@/assets/img/Teacher/icon-nocourse.png" 
        alt="无课程"
        class="empty-icon"
      >
      <p class="empty-text">
        {{ isEmptySearch ? '没有找到符合条件的课程' : '暂无课程，快去创建吧' }}
      </p>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useCourseStore } from '@/stores/courseStore';
import { useUserStore } from '@/stores/userStore';

const props = defineProps({
  filterParams: {
    type: Object,
    required: true,
    default: () => ({
      category: 0,
      status: 0,
      query: ''
    })
  }
});

const router = useRouter();
const courseStore = useCourseStore();
const userStore = useUserStore();
const courses = ref([]);

const handleImageError = (e) => {
  e.target.src = defaultCourseCover;
  console.error('课程封面加载失败，已使用默认图片:', e.target.src);
};

const goToCourseDetail = (courseId, courseName) => {
  router.push({ 
    name: 'CourseDetailManager',
    params: { courseId, courseName }
  });
};

// 判断是否是搜索导致的空状态
const isEmptySearch = computed(() => {
  return props.filterParams.query || 
    props.filterParams.category !== 0 || 
    props.filterParams.status !== 0;
});

// 获取状态文本
const getStatusText = (status) => {
  switch(status) {
    case 1: return '审核通过';
    case 2: return '审核未通过';
    default: return '未审核';
  }
};

// 获取状态样式类
const getStatusClass = (status) => {
  switch(status) {
    case 1: return 'status-approved';
    case 2: return 'status-rejected';
    default: return 'status-pending';
  }
};

// 获取课程状态文本
const getStateText = (state) => {
  return state === 1 ? '进行中' : '已结束';
};

// 获取课程状态样式类
const getStateClass = (state) => {
  return state === 1 ? 'state-active' : 'state-ended';
};

// 过滤课程
const filteredCourses = computed(() => {
  const { category, status, query } = props.filterParams;
  const searchTerm = query.toLowerCase();
  
  return courses.value.filter(course => {
    // 按审核状态筛选
    const statusMatch = category === 0 || 
      (category === 1 && course.status === 1) ||
      (category === 2 && course.status === 2) ||
      (category === 3 && course.status === 0);
    
    // 按课程状态筛选
    const stateMatch = status === 0 || 
      (status === 1 && course.state === 1) ||
      (status === 2 && course.state === 0);
    
    // 按搜索词筛选
    const queryMatch = !searchTerm || 
      course.name.toLowerCase().includes(searchTerm) ||
      (course.major && course.major.toLowerCase().includes(searchTerm));
    
    return statusMatch && stateMatch && queryMatch;
  });
});

const getCourseCountText = () => {
  const count = filteredCourses.value.length;
  if (count === 0) {
    return isEmptySearch.value ? '没有找到符合条件的课程' : '暂无课程';
  }
  return `共 ${count} 门课程${isEmptySearch.value ? '(筛选结果)' : ''}`;
};

onMounted(async () => {
  try {
    await courseStore.fetchTeacherCourses();
    courses.value = courseStore.courses;
  } catch (error) {
    console.error('加载课程失败:', error);
  }
});
</script>

<style lang="scss" scoped>
/* 保持原有样式不变 */
.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 20px;

  .new-course-btn {
    padding: 8px 20px;
    background-color: $primary-color;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;

    &:hover {
      background-color: $button-hover;
    }
  }
}

.course-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(270px, 1fr));
  gap: 20px;
  padding: 20px;
  width: 100%;
  box-sizing: border-box;

  .course-item {
    width: 100%;
    min-width: 270px;
    min-height: 320px; /* 设置最小高度保证内容显示 */
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
    transition: box-shadow 0.3s;
    cursor: pointer;
    position: relative;
    display: flex; /* 新增 */
    flex-direction: column; /* 新增 */

    &:hover {
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .course-cover-container {
      width: 100%;
      height: 210px; /* 固定封面高度 */
      min-height: 210px; /* 确保封面高度不变 */
      position: relative;
      overflow: hidden;
      background-color: #b5c0d9;
      flex-shrink: 0; /* 防止封面被压缩 */
    }

    .course-cover {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: center;
    }

    .course-content { /* 新增内容容器 */
      padding: 12px;
      flex: 1; /* 填充剩余空间 */
      display: flex;
      flex-direction: column;
      justify-content: space-between; /* 内容均匀分布 */
    }
  
    h3 {
      margin: 12px;
      font-size: 16px;
      color: #333;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    p {
      margin: 0 12px 12px;
      font-size: 14px;
      color: #666;
    }

    .course-status {
      display: flex;
      gap: 8px;
      margin: 0 12px 12px;

      span {
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
      }

      .status-approved {
        background-color: #e6f7ff;
        color: #1890ff;
        border: 1px solid #1890ff;
      }

      .status-rejected {
        background-color: #fff1f0;
        color: #f5222d;
        border: 1px solid #f5222d;
      }

      .status-pending {
        background-color: #fffbe6;
        color: #faad14;
        border: 1px solid #faad14;
      }

      .state-active {
        background-color: #f6ffed;
        color: #52c41a;
        border: 1px solid #52c41a;
      }

      .state-ended {
        background-color: #fafafa;
        color: #8c8c8c;
        border: 1px solid #d9d9d9;
      }
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  text-align: center;
  
  .empty-icon {
    width: 120px;
    height: 120px;
    margin-bottom: 20px;
    opacity: 0.6;
  }
  
  .empty-text {
    color: #999;
    font-size: 16px;
    margin-top: 10px;
  }
}
</style>