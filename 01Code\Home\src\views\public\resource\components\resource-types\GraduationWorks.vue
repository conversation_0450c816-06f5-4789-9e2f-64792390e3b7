<template>
  <div class="graduation-works-container">
    <!-- 搜索和筛选区域 -->
    <div class="filter-section">
      <div class="search-box">
        <input
          v-model="searchParams.name"
          type="text"
          placeholder="按作品名称搜索..."
          @keyup.enter="handleSearch"
          class="search-input"
        />
        <button @click="handleSearch" class="search-btn">
          <i class="fas fa-search"></i>
        </button>
      </div>

      <div class="year-filter">
        <select v-model="searchParams.graduationYear" @change="handleSearch" class="year-select">
          <option value="">全部年份</option>
          <option v-for="year in graduationYears" :key="year" :value="year">
            {{ year }}年
          </option>
        </select>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>正在加载毕业设计作品...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <div class="error-icon">⚠️</div>
      <p class="error-message">{{ error }}</p>
      <button @click="fetchGraduationWorks" class="retry-btn">重试</button>
    </div>

    <!-- 无数据状态 -->
    <div v-else-if="graduationWorks.length === 0" class="empty-container">
      <div class="empty-icon">🎓</div>
      <p>暂无毕业设计作品</p>
    </div>

    <!-- 作品网格 -->
    <div v-else class="card-grid">
      <div
        v-for="item in graduationWorks"
        :key="item.workId"
        class="card-item"
        :style="{ backgroundImage: `url(${item.coverImage})` }"
        @click="handleCardClick(item)"
      >
        <div class="card-overlay">
          <div class="card-content">
            <h3 class="card-title">{{ item.name || '未命名作品' }}</h3>
            <p class="card-author">作者：{{ item.author || '未知' }}</p>
            <div class="card-meta">
              <span class="file-type" v-if="item.fileType">{{ item.fileType.toUpperCase() }}</span>
              <span class="file-size" v-if="item.fileSize">{{ formatFileSize(item.fileSize) }}</span>
            </div>
            <div class="card-actions">
              <button @click.stop="handleDownload(item)" class="download-btn">
                <i class="fas fa-download"></i>
                下载
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页控件 -->
    <div v-if="graduationWorks.length > 0" class="pagination-container">
      <button
        @click="prevPage"
        :disabled="pagination.current === 1 || isLoading"
        class="page-btn"
      >
        上一页
      </button>

      <span class="page-info">
        第 {{ pagination.current }} 页 / 共 {{ pagination.pages }} 页
        (共 {{ pagination.total }} 项)
      </span>

      <button
        @click="nextPage"
        :disabled="pagination.current >= pagination.pages || isLoading"
        class="page-btn"
      >
        下一页
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { getGraduationWorksList } from '@/api/public/resource/graduation';
import { ElMessage } from 'element-plus';

// 路由实例
const router = useRouter();

// 响应式数据
const graduationWorks = ref([]);
const isLoading = ref(false);
const error = ref(null);

// 毕业年份选项
const graduationYears = ref(['2024', '2023', '2022', '2021', '2020', '2019', '2018']);

// 搜索参数
const searchParams = reactive({
  name: '',
  graduationYear: ''
});

// 分页信息
const pagination = reactive({
  current: 1,
  size: 12,
  total: 0,
  pages: 1
});

// 获取毕业设计作品列表
const fetchGraduationWorks = async (resetPage = false) => {
  if (resetPage) {
    pagination.current = 1;
  }

  isLoading.value = true;
  error.value = null;

  try {
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.size,
      ...searchParams
    };

    // 过滤空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key];
      }
    });

    const response = await getGraduationWorksList(params);

    if (response && response.result) {
      graduationWorks.value = response.result.records || [];
      pagination.total = response.result.total || 0;
      pagination.pages = response.result.pages || 1;
      pagination.current = response.result.current || 1;


    } else {
      throw new Error('响应数据格式错误');
    }

  } catch (err) {
    console.error('获取毕业设计作品失败:', err);
    error.value = err.response?.data?.msg || err.message || '获取数据失败，请重试';
    ElMessage.error(error.value);

    // 如果API失败，使用模拟数据进行测试
    graduationWorks.value = [
      {
        workId: 'test1',
        name: '测试毕业设计作品1',
        author: '张三',
        graduationYear: '2023',
        brief: '这是一个测试作品的简介',
        coverImage: '/src/assets/img/Course/courseCover/cover1.png',
        introduction: '详细介绍测试作品1的内容和特点',
        thesisUrl: 'https://example.com/thesis1.pdf',
        thesisContent: '测试作品1的论文内容...',
        createTime: 1750577557,
        updateTime: 1750577557
      },
      {
        workId: 'test2',
        name: '测试毕业设计作品2',
        author: '李四',
        graduationYear: '2023',
        brief: '这是另一个测试作品的简介',
        coverImage: '/src/assets/img/Course/courseCover/cover2.png',
        introduction: '详细介绍测试作品2的内容和特点',
        thesisUrl: 'https://example.com/thesis2.pdf',
        thesisContent: '测试作品2的论文内容...',
        createTime: 1750577557,
        updateTime: 1750577557
      }
    ];
    pagination.total = 2;
    pagination.pages = 1;
    pagination.current = 1;
    error.value = null; // 清除错误状态，显示模拟数据
  } finally {
    isLoading.value = false;
  }
};



// 搜索处理
const handleSearch = () => {
  fetchGraduationWorks(true);
};

// 分页处理
const prevPage = () => {
  if (pagination.current > 1) {
    pagination.current--;
    fetchGraduationWorks();
  }
};

const nextPage = () => {
  if (pagination.current < pagination.pages) {
    pagination.current++;
    fetchGraduationWorks();
  }
};

// 卡片点击处理
const handleCardClick = (item) => {
  try {
    // 检查必要的数据
    if (!item.workId) {
      ElMessage.error('作品ID缺失，无法查看详情');
      return;
    }

    // 跳转到毕业设计详情页
    const targetPath = `/graduation-project/${item.workId}`;
    router.push(targetPath).catch(() => {
      ElMessage.error('页面跳转失败，请重试');
      // 如果路径跳转失败，尝试使用name跳转
      router.push({
        name: 'graduation-project',
        params: { workId: item.workId }
      }).catch(() => {
        ElMessage.error('路由跳转失败');
      });
    });

  } catch (error) {
    ElMessage.error('操作失败，请重试');
  }
};

// 下载处理
const handleDownload = (item) => {
  if (item.fileUrl) {
    // 创建一个临时的a标签来触发下载
    const link = document.createElement('a');
    link.href = item.fileUrl;
    link.download = `${item.title}.${item.fileType || 'zip'}`;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    ElMessage.success(`开始下载: ${item.title}`);
  } else {
    ElMessage.warning('该作品暂无下载链接');
  }
};

// 文件大小格式化
const formatFileSize = (bytes) => {
  if (!bytes) return '未知';

  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

// 组件挂载时获取数据
onMounted(() => {
  fetchGraduationWorks();
});
</script>

<style scoped lang="scss">
.graduation-works-container {
  padding: 20px;

  .filter-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    gap: 20px;
    flex-wrap: wrap;

    .search-box {
      display: flex;
      align-items: center;
      flex: 1;
      max-width: 400px;

      .search-input {
        flex: 1;
        padding: 10px 15px;
        border: 1px solid #ddd;
        border-radius: 25px 0 0 25px;
        outline: none;
        font-size: 14px;

        &:focus {
          border-color: #8a6de3;
          box-shadow: 0 0 0 2px rgba(138, 109, 227, 0.2);
        }
      }

      .search-btn {
        padding: 10px 15px;
        background: #8a6de3;
        color: white;
        border: none;
        border-radius: 0 25px 25px 0;
        cursor: pointer;
        transition: background-color 0.3s;

        &:hover {
          background: #7a5dd3;
        }
      }
    }

    .year-filter {
      .year-select {
        padding: 10px 15px;
        border: 1px solid #ddd;
        border-radius: 6px;
        outline: none;
        font-size: 14px;
        background: white;
        min-width: 150px;

        &:focus {
          border-color: #8a6de3;
        }
      }
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #8a6de3;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 15px;
    }

    p {
      color: #666;
      font-size: 16px;
    }
  }

  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;

    .error-icon {
      font-size: 48px;
      margin-bottom: 15px;
    }

    .error-message {
      color: #e74c3c;
      font-size: 16px;
      margin-bottom: 20px;
      text-align: center;
    }

    .retry-btn {
      padding: 10px 20px;
      background: #8a6de3;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.3s;

      &:hover {
        background: #7a5dd3;
      }
    }
  }

  .empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 15px;
    }

    p {
      color: #666;
      font-size: 16px;
    }
  }

  .card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
  }

  .card-item {
    height: 250px;
    border-radius: 12px;
    overflow: hidden;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-color: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    position: relative;

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);

      .card-overlay {
        background: rgba(0, 0, 0, 0.8);
      }

      .card-actions {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .card-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.8) 100%);
      display: flex;
      align-items: flex-end;
      padding: 20px;
      transition: background-color 0.3s ease;
    }

    .card-content {
      color: white;
      width: 100%;

      .card-title {
        font-size: 18px;
        font-weight: 600;
        margin: 0 0 8px 0;
        line-height: 1.3;
        color: white !important;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        z-index: 10;
        position: relative;
      }

      .card-author {
        font-size: 14px;
        margin: 0 0 10px 0;
        opacity: 0.9;
      }

      .card-meta {
        display: flex;
        gap: 10px;
        margin-bottom: 12px;

        span {
          background: rgba(255, 255, 255, 0.2);
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 11px;
          backdrop-filter: blur(10px);
        }
      }

      .card-actions {
        opacity: 0;
        transform: translateY(10px);
        transition: all 0.3s ease;

        .download-btn {
          background: #8a6de3;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 20px;
          font-size: 12px;
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          gap: 5px;

          &:hover {
            background: #7a5dd3;
            transform: scale(1.05);
          }

          i {
            font-size: 10px;
          }
        }
      }
    }
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    padding: 20px 0;

    .page-btn {
      padding: 8px 16px;
      background: #8a6de3;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s;

      &:hover:not(:disabled) {
        background: #7a5dd3;
        transform: translateY(-1px);
      }

      &:disabled {
        background: #ccc;
        cursor: not-allowed;
        transform: none;
      }
    }

    .page-info {
      color: #666;
      font-size: 14px;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 响应式设计
@media (max-width: 768px) {
  .graduation-works-container {
    padding: 15px;

    .filter-section {
      flex-direction: column;
      align-items: stretch;

      .search-box {
        max-width: none;
      }

      .year-filter .year-select {
        min-width: auto;
      }
    }

    .card-grid {
      grid-template-columns: 1fr;
    }

    .pagination-container {
      flex-direction: column;
      gap: 10px;
    }
  }
}
</style>