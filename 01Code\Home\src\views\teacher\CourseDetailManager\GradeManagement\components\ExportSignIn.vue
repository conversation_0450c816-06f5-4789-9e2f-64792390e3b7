<template>
  <!-- 遮罩层 -->
  <div class="modal-mask" v-if="visible" @click.self="closeModal">
    <!-- 弹窗主体 -->
    <div class="modal-container">
      <!-- 弹窗头部 -->
      <div class="modal-header">
        <h2>导出签到</h2>
        <button class="close-btn" @click="closeModal">&times;</button>
      </div>

      <!-- 弹窗内容 -->
      <div class="modal-content">
        <p class="modal-description">本列表仅展示已截止的历史签到，仅可导出已截止的历史签到</p>
        
        <div class="selection-controls">
          <label class="select-all">
            <input type="checkbox" v-model="selectAll" @change="toggleAll" />
            全选
          </label>
          <span class="selected-count">已选择 {{ selectedCount }} 个标签</span>
        </div>

        <div class="sign-in-list">
          <div class="sign-in-item" v-for="(item, index) in signInItems" :key="index">
            <input 
              type="text" 
              class="search-input" 
              placeholder="可输入签到名称查询" 
              v-model="searchQuery"
            />
            <label class="sign-in-label">
              <input type="checkbox" v-model="selectedItems[index]" />
              {{ item.name }}
            </label>
            <button class="delete-btn" @click="removeItem(index)">
              <i class="fas fa-trash-alt"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- 弹窗底部按钮 -->
      <div class="modal-footer">
        <button class="cancel-btn" @click="cancelExport">暂不导出</button>
        <button class="confirm-btn" @click="exportSignIn">确定导出</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

// 控制弹窗显示/隐藏
const visible = ref(false);

// 签到数据
const signInItems = ref([
  { id: 1, name: '课程签到20250630' },
  { id: 2, name: '课程签到20250629' }
]);

// 搜索查询
const searchQuery = ref('');

// 选择相关
const selectAll = ref(false);
const selectedItems = ref(Array(signInItems.value.length).fill(false));

// 计算已选择的数量
const selectedCount = computed(() => 
  selectedItems.value.filter(item => item).length
);

// 全选/取消全选
const toggleAll = () => {
  selectedItems.value = Array(signInItems.value.length).fill(selectAll.value);
};

// 移除单个项目
const removeItem = (index) => {
  selectedItems.value[index] = false;
  selectAll.value = selectedItems.value.every(item => item);
};

// 取消导出
const cancelExport = () => {
  closeModal();
};

// 导出签到
const exportSignIn = () => {
  const selected = selectedItems.value
    .map((selected, index) => selected ? signInItems.value[index] : null)
    .filter(item => item !== null);
  
  console.log('导出选中的签到：', selected);
  // 这里可以添加实际的导出逻辑
  closeModal();
};

// 打开弹窗
const openModal = () => {
  visible.value = true;
};

// 关闭弹窗
const closeModal = () => {
  visible.value = false;
  // 重置选择状态
  selectAll.value = false;
  selectedItems.value = Array(signInItems.value.length).fill(false);
};

// 暴露方法给父组件
defineExpose({
  openModal,
  closeModal
});
</script>

<style lang="scss" scoped>
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.modal-container {
  width: 600px;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.33);
  display: flex;
  flex-direction: column;
  animation: slideIn 0.3s ease;
  overflow: hidden;
}

.modal-header {
  padding: 16px 24px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h2 {
    margin: 0;
    font-size: 18px;
    color: #333;
  }
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  padding: 0;
  
  &:hover {
    color: #666;
  }
}

.modal-content {
  padding: 20px 24px;
  flex: 1;
  overflow-y: auto;
}

.modal-description {
  margin: 0 0 16px;
  color: #666;
  font-size: 14px;
}

.selection-controls {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  
  .select-all {
    margin-right: 16px;
    display: flex;
    align-items: center;
    cursor: pointer;
    
    input {
      margin-right: 8px;
    }
  }
  
  .selected-count {
    color: #666;
    font-size: 14px;
  }
}

.sign-in-list {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
}

.sign-in-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:hover {
    background-color: #f9f9f9;
  }
}

.search-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-right: 12px;
  
  &:focus {
    outline: none;
    border-color: #409eff;
  }
}

.sign-in-label {
  flex: 2;
  display: flex;
  align-items: center;
  cursor: pointer;
  
  input {
    margin-right: 8px;
  }
}

.delete-btn {
  background: none;
  border: none;
  color: #f56c6c;
  cursor: pointer;
  padding: 4px;
  margin-left: 12px;
  
  &:hover {
    color: #f78989;
  }
}

.modal-footer {
  padding: 12px 24px;
  border-top: 1px solid #e8e8e8;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  
  button {
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
  }
  
  .cancel-btn {
    background-color: #fff;
    border: 1px solid #ddd;
    color: #666;
    
    &:hover {
      border-color: #ccc;
      color: #333;
    }
  }
  
  .confirm-btn {
    background-color: #409eff;
    border: 1px solid #409eff;
    color: #fff;
    
    &:hover {
      background-color: #66b1ff;
      border-color: #66b1ff;
    }
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateY(-20px); }
  to { transform: translateY(0); }
}
</style>