<template>
  <div class="main-container">
    <!-- 统计信息区域 -->
    <div class="stats-container">
      <div class="text-block">数媒智能广场</div>
      <div class="stat-card">
        <div class="stat-value" ref="courseCount">{{ courseCount }}</div>
        <div class="stat-label">智能工具</div>
      </div>
      <div class="stat-card">
        <div class="stat-value" ref="hoursCount">{{ hoursCount }}</div>
        <div class="stat-label">DeepSeek深度求索</div>
      </div>
    </div>

    <!-- 功能区域 -->
    <div class="content-container">
      <div class="function-card">
        <div class="function-header">
          <div class="function-title">AI功能</div>
        </div>
        <div class="function-grid">
          <button
            class="function-button"
            v-for="(item, index) in mainFunctions"
            :key="index"
            @click="handleFunctionClick(item)">
            {{ item.name }}
          </button>
        </div>
      </div>
      <div class="feature-card">
        <component :is="currentComponent" v-if="currentComponent"></component>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()
import {ref, onMounted, markRaw} from 'vue';
import AIVideo from '../intelligent/Components/AIVideo.vue';
import AIPhoto from '../intelligent/Components/AIPhoto.vue';
import AIDialog from '../intelligent/Components/AIDialog.vue';
import AIWriter from '../intelligent/Components/AIWriter.vue';
import AINote from '../intelligent/Components/AINote.vue';
import AISelftest from "../intelligent/Components/AISelftest.vue";
import AIQuestion from "../intelligent/Components/AIQuestion.vue";

// 数字动画相关
const courseCount = ref(1);
const hoursCount = ref(0);
const targetCourseCount = 12;
const targetHoursCount = 24;
const showFeatures = ref(false);

// 模拟数字增长动画
function animateNumber(el, start, end, duration) {
  let startTimestamp = null;
  const step = (timestamp) => {
    if (!startTimestamp) startTimestamp = timestamp;
    const progress = Math.min((timestamp - startTimestamp) / duration, 1);
    let value = Math.floor(progress * (end - start) + start);
    el.textContent = value;
    if (progress < 1) {
      window.requestAnimationFrame(step);
    }
  };
  window.requestAnimationFrame(step);
}

function checkUserLogin() {
  // return this.$store.state.user.isLoggedIn; // Vuex方式示例
  return localStorage.getItem('token'); // localStorage方式示例
}

// 主功能区域
const mainFunctions = [
  {name: '图片生成', component: markRaw(AIPhoto)},
  {name: '视频生成', component: markRaw(AIVideo)},
  {name: '问答助手', component: markRaw(AIDialog)},
  {name: '写作助手', component: markRaw(AIWriter)},
  {name: '代码分析', component: markRaw(AINote)},
  {name: '习题自测', component: markRaw(AISelftest)},
  {name: '以题换题', component: markRaw(AIQuestion)},
];

// 当前选中的组件
const currentComponent = ref(null);
currentComponent.value = markRaw(AIPhoto);

// 处理功能点击
function handleFunctionClick(item) {
  if (!checkUserLogin()) {
    // 未登录则跳转到登录页面
    router.push('/login')
    return
  }
  currentComponent.value = item.component;
}

// 详细功能区域
const detailFunctions = [
  {name: 'AI图片生成'},
  {name: 'AI视频生成'},
  {name: 'AI文本生成'},
  {name: 'AI设计辅助'},
  {name: 'AI分析工具'},
  {name: 'AI创意推荐'},
  {name: 'AI技能测评'},
  {name: 'AI学习助手'},
  {name: 'AI工具箱'},
];

// 处理详细功能点击
function handleFeatureClick(item) {
  console.log('详细功能点击:', item.name);
}

// 切换功能区域显示
function toggleFeatures() {
  showFeatures.value = !showFeatures.value;
}



// 页面加载时初始化动画
onMounted(() => {
  const courseEl = courseCount.value
  const hoursEl = hoursCount.value

  // 设置初始值
  courseCount.value = 0;
  hoursCount.value = 0;

  // 动画
  animateNumber(courseEl, 0, targetCourseCount, 1500);
  animateNumber(hoursEl, 0, targetHoursCount, 1500);
});
</script>

<style scoped>
:root {
  --primary-color: #6f45d8;
  --secondary-color: #916ccc;
  --text-color: #523bb8;
  --sidebar-width: 250px;
  --header-height: 60px;
}

.main-container {
  min-height: 120vh;
  background-color: rgb(136, 99, 229);
  padding: 0;
  margin: 0;
  font-family: 'Arial', sans-serif;
}

/* 统计信息区域 */
.stats-container {
  display: flex;
  justify-content: flex-end;
  padding: 20px;
  gap: 10px;
}

.text-block {
  margin-right: auto;
  margin-left: 50px;
  padding: 40px;
  color: white;
  font-size: 36px;
  font-weight: bold;
  text-align: center;
}

.stat-card {
  padding: 20px;
  width: 180px;
  text-align: center;
  color: white;
}

.stat-value {
  font-size: 48px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
}

/* 内容区域 */
.content-container {
  display: flex;
  padding: 20px;
  gap: 20px;
  height: calc(100vh - 120px);
  background-color: white;
  border-radius: 10px;
  margin: 40px;
}

.function-card {
  flex: 1;
  background-color: white;
  border-radius: 8px;
  padding: 20px;
}

.feature-card {
  flex: 7;
  background-color: white;
  border-radius: 8px;
  padding: 20px;
}

.function-header {
  margin-bottom: 15px;
  text-align: center;
}

.function-title {
  font-size: 16px;
  color: rgb(123, 76, 241);
  font-weight: bold;
}

/* 功能按钮网格 */
.function-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
}

.function-button{
  background-color: rgb(210, 200, 234);
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  color: black;
  font-size: 14px;
}

.function-button:hover{
  background-color: white;
}

/*.page-header h2 {*/
/*  color: var(--primary-color);*/
/*  font-size: 20px;*/
/*}*/
</style>
