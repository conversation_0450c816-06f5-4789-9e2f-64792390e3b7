<template>
  <div class="notice-container">
    <NoticeHeader v-model:activeTab="noticeStore.activeTab" />
    <NoticeList v-if="showNoticeList" :notices="noticeStore.notices" :pagination="noticeStore.pagination"
      @page-change="changePage" />

    <router-view />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useNoticeStore } from '@/stores/public/teacher/noticeStore'
import NoticeHeader from './components/NoticeHeader.vue'
import NoticeList from './components/NoticeList.vue'


const noticeStore = useNoticeStore()
const route = useRoute()

// 通过路由meta配置控制是否显示列表
const showNoticeList = computed(() => {
  return route.meta?.showNoticeList !== false
})

// 切换页码
const changePage = (pageNum) => {
  noticeStore.fetchNotices({ pageNum }) // 传递对象参数
}

// 初始化加载
onMounted(() => {
  noticeStore.fetchNotices()
})

// 监听标签切换
watch(() => noticeStore.activeTab, (newTab, oldTab) => {
  if (newTab !== oldTab) {
    noticeStore.fetchNotices({ pageNum: 1 }) // 切换标签时重置页码
  }
})
</script>

<style lang="scss" scoped>
.notice-container {
  min-height: 95%;
  background-color: #fff;
  padding: 30px;
}
</style>