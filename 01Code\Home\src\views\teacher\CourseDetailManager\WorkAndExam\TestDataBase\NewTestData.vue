<!-- src\views\teacher\CourseDetailManager\WorkAndExam\TestDataBase\NewTestData.vue -->
<template>
    <div class="new-test-page">
        <div class="page-header">
            <div class="header-title">新增试题</div>
            <div class="header-actions">
                <el-button @click="handleCancel">取消</el-button>
                <el-button type="primary" @click="handleSave">保存</el-button>
                <el-button type="primary" @click="handleSaveNext">保存并创建下一题</el-button>
                <el-button v-if="isPaperMode" type="primary" @click="handleFinalSave">完成保存</el-button>
            </div>
        </div>

        <div class="form-body">
            <el-form :model="form" label-position="top" style="max-width: 800px; margin: 0 auto;">
                <!-- 题型 -->
                <el-form-item label="题型">
                    <el-select v-model="form.type" placeholder="请选择题型" style="width: 200px;">
                        <el-option v-for="item in questionTypes" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-form-item>

                <!-- 题干 -->
                <el-form-item label="题干">
                    <div class="quill-wrapper">
                        <QuillEditor v-model="form.description" />
                    </div>
                </el-form-item>

                <!-- 题干附件上传 -->
                <el-form-item label="附件上传">
                    <el-button type="primary" @click="triggerFileInput">
                        <el-icon>
                            <Upload />
                        </el-icon>
                        上传附件
                    </el-button>
                    <input ref="fileInput" type="file" style="display: none" multiple @change="handleFilesSelected" />
                    <div v-if="form.attachments.length" class="attachment-list" style="margin-top: 8px;">
                        <div v-for="(file, idx) in form.attachments" :key="idx" class="attachment-item">
                            {{ file.name }}
                            <el-button type="text" size="small" @click="removeAttachment(idx)">删除</el-button>
                        </div>
                    </div>
                </el-form-item>

                <!-- 选项区域 -->
                <div v-if="showOptions" class="option-area">
                    <div class="option-header">选项（至少一项为正确答案）</div>
                    <div v-for="(option, index) in form.options" :key="index" class="option-item">
                        <div class="left-area">
                            <div class="option-checkbox">
                                <el-radio v-if="form.type === '单选题' || form.type === '判断题'" :model-value="singleCorrect"
                                    :label="index" @change="() => handleRadioSelect(index)">
                                    {{ String.fromCharCode(65 + index) }}
                                </el-radio>

                                <el-checkbox v-if="form.type === '多选题'" v-model="option.correct"
                                    :label="String.fromCharCode(65 + index)" @change="triggerUpdate++">
                                    {{ String.fromCharCode(65 + index) }}
                                </el-checkbox>
                            </div>
                        </div>
                        <div class="option-content">
                            <div class="quill-wrapper no-padding">
                                <QuillEditor v-model="option.content" />
                            </div>
                        </div>
                        <div class="option-tools">
                            <el-button type="text" @click="removeOption(index)" class="delete-icon-button"
                                aria-label="删除选项">
                                <el-icon>
                                    <Delete />
                                </el-icon>
                            </el-button>
                        </div>
                    </div>
                    <el-button type="primary" link @click="addOption">新增选项</el-button>
                </div>

                <!-- 问答题和填空题填写答案 -->
                <el-form-item v-if="form.type === '问答题' || form.type === '填空题'" label="参考答案（填写文本）">
                    <el-input type="textarea" rows="4" v-model="form.answer" placeholder="请输入参考答案" clearable
                        @input="triggerUpdate++" />
                </el-form-item>

                <!-- 单选、多选、判断题答案确认 -->
                <el-form-item v-if="showOptions" label="请选择正确答案" style="color: #f56c6c;" v-show="!isAnswerValid">
                    <div>请选择至少一个正确答案</div>
                </el-form-item>

                <!-- 答案解析 -->
                <el-form-item label="答案解析">
                    <div class="quill-wrapper">
                        <QuillEditor v-model="form.explanation" />
                    </div>
                </el-form-item>

                <!-- 答案解析附件上传 -->
                <el-form-item label="答案解析附件上传">
                    <el-button type="primary" @click="triggerFileInputExplanation">
                        <el-icon>
                            <Upload />
                        </el-icon>
                        上传附件
                    </el-button>
                    <input ref="fileInputExplanation" type="file" style="display: none" multiple
                        @change="handleFilesSelectedExplanation" />
                    <div v-if="form.attachmentsExplanation.length" class="attachment-list" style="margin-top: 8px;">
                        <div v-for="(file, idx) in form.attachmentsExplanation" :key="idx" class="attachment-item">
                            {{ file.name }}
                            <el-button type="text" size="small" @click="removeAttachmentExplanation(idx)">删除</el-button>
                        </div>
                    </div>
                </el-form-item>
            </el-form>
        </div>
    </div>

    <el-dialog v-model="cancelDialogVisible" title="确认取消" width="400px" class="custom-cancel-dialog"
        :close-on-click-modal="false" :destroy-on-close="true">
        <div class="dialog-body-text">
            当前编辑内容尚未保存，确定要取消吗？
        </div>
        <template #footer>
            <el-button @click="cancelDialogVisible = false">继续编辑</el-button>
            <el-button type="danger" @click="confirmCancel">确认取消</el-button>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import QuillEditor from '../components/QuillEditor.vue'
import { Upload, Delete } from '@element-plus/icons-vue'
import {
    saveSingleChoice,
    saveMultipleChoice,
    saveTrueFalse,
    saveFillBlank,
    saveShortAnswer,
    createExam,
    saveExamRelations
} from '@/api/teacher/question'

import { updateExam } from '@/api/teacher/question'

//创建试卷时保存前面的试题
import { useExamStore } from '@/stores/useExamStore'
const examStore = useExamStore()

import { useUserStore } from '@/stores/userStore'
const userStore = useUserStore()

const router = useRouter()
const route = useRoute()

const questionTypes = ['单选题', '多选题', '判断题', '填空题', '问答题']


const isPaperMode = computed(() => route.query.paperMode === 'true')
const courseId = computed(() => route.query.courseId || null)

watch(() => courseId.value, (newCourseId) => {
    if (!newCourseId) {
        ElMessage.warning('没有找到课程 ID')
    }
})

const paperTitle = ref('') // 让用户输入的新标题


const form = reactive({
    type: '单选题',
    description: '',
    explanation: '',
    answer: '',
    options: [],
    attachments: [],
    attachmentsExplanation: []
})

const singleCorrect = ref(null)
const showOptions = computed(() => ['单选题', '多选题', '判断题'].includes(form.type))
const triggerUpdate = ref(0)

const isAnswerValid = computed(() => {
    triggerUpdate.value
    if (!form.description.trim()) return false
    if (showOptions.value) {
        return form.options.some((opt) => opt.correct === true)
    } else {
        return form.answer.trim().length > 0
    }
})

watch(() => form.type, (newType) => {
    if (newType === '判断题') {
        form.options = [
            { content: '正确', correct: false },
            { content: '错误', correct: false }
        ]
    } else if (newType === '单选题' || newType === '多选题') {
        form.options = [
            { content: '', correct: false },
            { content: '', correct: false },
            { content: '', correct: false },
            { content: '', correct: false }
        ]
    } else {
        form.options = []
    }
    form.answer = ''
    singleCorrect.value = null
    triggerUpdate.value++
}, { immediate: true })

const handleRadioSelect = (index) => {
    if (singleCorrect.value === index) {
        singleCorrect.value = null
        form.options.forEach((opt) => (opt.correct = false))
    } else {
        singleCorrect.value = index
        form.options.forEach((opt, i) => (opt.correct = i === index))
    }
    triggerUpdate.value++
}

const addOption = () => {
    form.options.push({ content: '', correct: false })
    triggerUpdate.value++
}

const removeOption = (index) => {
    form.options.splice(index, 1)
    if (form.type === '单选题' || form.type === '判断题') {
        if (singleCorrect.value === index) {
            singleCorrect.value = null
        }
    }
    triggerUpdate.value++
}

const fileInputRef = ref(null)
const fileInputExplanationRef = ref(null)

function triggerFileInput() {
    fileInputRef.value.click()
}

function triggerFileInputExplanation() {
    fileInputExplanationRef.value.click()
}

function handleFilesSelected(event) {
    const files = event.target.files
    for (let i = 0; i < files.length; i++) {
        form.attachments.push(files[i])
    }
}

function handleFilesSelectedExplanation(event) {
    const files = event.target.files
    for (let i = 0; i < files.length; i++) {
        form.attachmentsExplanation.push(files[i])
    }
}

function removeAttachment(index) {
    form.attachments.splice(index, 1)
}

function removeAttachmentExplanation(index) {
    form.attachmentsExplanation.splice(index, 1)
}

async function handleSave() {
    if (!form.description.trim()) {
        ElMessage.error('题干不能为空')
        return
    }
    if (!isAnswerValid.value) {
        ElMessage.error('请设置正确答案')
        return
    }

    const data = buildPayload()

    try {
        // 试卷模式下，没试卷id就创建试卷，并存到 Pinia
        if (isPaperMode.value && !examStore.paperId) {
            const res = await createExam({ title: '新试卷', courseId: route.query.courseId })
            if (res.code === 200 || res.code === 0) {
                examStore.setPaperId(res.result.id)
                console.log('创建试卷ID：', res.result.id)
            } else {
                throw new Error(res.msg || '试卷创建失败')
            }
        }

        const res = await saveQuestionByType(form.type, data)
        if (res.code !== 200 && res.code !== 0) {
            throw new Error(res.msg || '题目保存失败')
        }

        if (isPaperMode.value) {
            examStore.addQuestion({
                id: res.result.id,
                type: mapTypeStringToNumber(form.type)
            })
            ElMessage.success('题目保存成功，继续添加')
            resetForm()
            console.log('新增题目ID:', res.result.id)
            console.log('当前题目ID列表:', examStore.createdQuestions)
        } else {
            ElMessage.success('保存成功')
            router.back()
        }
    } catch (error) {
        ElMessage.error(error.message || '保存失败')
    }
}

function mapTypeStringToNumber(typeStr) {
    switch (typeStr) {
        case '单选题': return 0
        case '多选题': return 1
        case '判断题': return 2
        case '填空题': return 3
        case '问答题': return 4
        default: return -1
    }
}


async function handleSaveNext() {
    await handleSave()
}
// 工具函数：将富文本转为纯文本
function extractTextFromHtml(html) {
    const div = document.createElement('div')
    div.innerHTML = html
    return div.textContent || div.innerText || ''
}

function buildPayload() {
    console.log('当前题目列表:', examStore.createdQuestions)

    const teacherId = userStore.user.id
    const isPublic = true

    const base = {
        content: extractTextFromHtml(form.description),
        explanation: extractTextFromHtml(form.explanation),
        teacherId,
        isPublic
    }

    if (form.type === '单选题') {
        return {
            ...base,
            options: JSON.stringify(
                form.options
                    .map((opt, i) => [String.fromCharCode(65 + i), extractTextFromHtml(opt.content).trim()])
                    .reduce((obj, [key, val]) => ({ ...obj, [key]: val }), {})
            ),
            correct: form.options
                .map((opt, idx) => opt.correct ? String.fromCharCode(65 + idx) : null)
                .filter(Boolean)[0] // 单选取第一个
        }
    }

    if (form.type === '多选题') {
        return {
            ...base,
            options: JSON.stringify(
                form.options
                    .map((opt, i) => [String.fromCharCode(65 + i), extractTextFromHtml(opt.content).trim()])
                    .reduce((obj, [key, val]) => ({ ...obj, [key]: val }), {})
            ),
            correct: JSON.stringify(
                form.options
                    .map((opt, idx) => opt.correct ? String.fromCharCode(65 + idx) : null)
                    .filter(Boolean)
            )
        }
    }

    if (form.type === '判断题') {
        const answerIndex = form.options.findIndex(opt => opt.correct)
        return {
            ...base,
            answer: answerIndex === 0
        }
    }

    if (form.type === '填空题') {
        return {
            ...base,
            answers: JSON.stringify(
                form.answer.split(',').map(a => a.trim())
            )
        }
    }

    if (form.type === '问答题') {
        return {
            ...base,
            sampleAnswer: extractTextFromHtml(form.answer)
        }
    }
}

async function handleFinalSave() {
    if (!examStore.paperId) {
        ElMessage.error('试卷不存在')
        return
    }
    if (examStore.createdQuestions.length === 0) {
        ElMessage.warning('请先添加题目')
        return
    }

    try {
        // 让用户输入试卷名称
        const { value: title } = await ElMessageBox.prompt('请输入试卷名称', '设置试卷标题', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            inputPlaceholder: '例如：2025年期末测试',
            inputValue: '新试卷'
        })

        // 更新试卷标题
        const updateRes = await updateExam({
            id: examStore.paperId,
            title
        })

        if (updateRes.code !== 200 && updateRes.code !== 0) {
            throw new Error(updateRes.msg || '试卷标题更新失败')
        }

        // 构造题目关联列表
        const relations = examStore.createdQuestions.map(q => ({
            id: '',
            examId: examStore.paperId,
            questionType: q.type,
            questionId: q.id,
            score: 10
        }))

        const saveRes = await saveExamRelations({
            examId: examStore.paperId,
            relations
        })

        if (saveRes.code === 200 || saveRes.code === 0) {
            ElMessage.success('试卷保存成功')
            examStore.clear()
            router.back()
        } else {
            throw new Error(saveRes.msg || '关联试题失败')
        }
    } catch (error) {
        if (error === 'cancel') return
        ElMessage.error(error.message || '保存失败')
    }
}

async function saveQuestionByType(type, data) {
    switch (type) {
        case '单选题':
            return await saveSingleChoice(data)
        case '多选题':
            return await saveMultipleChoice(data)
        case '判断题':
            return await saveTrueFalse(data)
        case '填空题':
            return await saveFillBlank(data)
        case '问答题':
            return await saveShortAnswer(data)
    }
}

function resetForm() {
    form.description = ''
    form.explanation = ''
    form.answer = ''
    form.attachments = []
    form.attachmentsExplanation = []
    singleCorrect.value = null
    if (form.type === '判断题') {
        form.options = [
            { content: '正确', correct: false },
            { content: '错误', correct: false }
        ]
    } else if (form.type === '单选题' || form.type === '多选题') {
        form.options = [
            { content: '', correct: false },
            { content: '', correct: false },
            { content: '', correct: false },
            { content: '', correct: false }
        ]
    } else {
        form.options = []
    }
    triggerUpdate.value++
}
</script>

<style scoped>
.new-test-page {
    padding-top: 60px;
}

.page-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: #fff;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    z-index: 1000;
}

.header-title {
    font-size: 18px;
    font-weight: bold;
}

.form-body {
    padding: 24px;
    max-width: 960px;
    margin: 0 auto;
}

.option-area {
    margin-bottom: 20px;
}

.option-header {
    font-weight: 600;
    margin-bottom: 8px;
}

.option-item {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    align-items: center;
}

.left-area {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 48px;
    margin-top: 0;
}

.option-checkbox {
    margin-bottom: 0;
    display: flex;
    align-items: center;
    gap: 4px;
}

.option-content {
    flex: 1;
}

.option-tools {
    width: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

.delete-icon-button {
    color: #606266;
    font-size: 20px;
    padding: 0;
    line-height: 1;
    min-width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.delete-icon-button:hover {
    color: #409eff;
    background-color: transparent;
}

.quill-wrapper {
    width: 100%;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #fff;
}

.quill-wrapper.no-padding {
    padding: 0;
}

::v-deep(.quill-editor) {
    min-height: 100px !important;
    font-size: 14px;
    line-height: 1.6;
    padding: 8px !important;
    box-sizing: border-box;
}
</style>
  