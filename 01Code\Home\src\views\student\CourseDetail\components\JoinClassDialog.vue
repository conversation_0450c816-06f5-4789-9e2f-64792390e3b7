<template>
  <div class="join-class-dialog-overlay" v-if="visible" @click.self="handleClose">
    <div class="join-class-dialog">
      <div class="join-class-dialog-header">
        <h3>加入班级</h3>
        <button class="close-btn" @click="handleClose">×</button>
      </div>
      <div class="join-class-dialog-body">
        <p>请输入班级邀请码：</p>
        <el-input
          v-model="invitationCode"
          placeholder="请输入邀请码"
          class="invitation-code-input"
        ></el-input>
      </div>
      <div class="join-class-dialog-footer">
        <button class="cancel-btn" @click="handleClose">取消</button>
        <button class="confirm-btn" @click="handleConfirm">确定</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineEmits } from 'vue';
import { ElMessage } from 'element-plus'; // 若用自定义提示可替换

const props = defineProps({
  visible: Boolean
});
const emit = defineEmits(['update:visible', 'confirmJoin']);

const invitationCode = ref('');

const handleClose = () => {
  emit('update:visible', false);
  invitationCode.value = '';
};

const handleConfirm = () => {
  if (!invitationCode.value.trim()) {
    ElMessage.warning('请输入班级邀请码');
    return;
  }
  emit('confirmJoin', invitationCode.value);
  handleClose();
};
</script>

<style scoped lang="scss">
.join-class-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.join-class-dialog {
  background-color: #fff;
  border-radius: 8px;
  width: 400px;
  max-width: 90%;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.join-class-dialog-header {
  padding: 16px 24px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 20px;
    color: #999;
    cursor: pointer;
    padding: 0 5px;
    line-height: 1;

    &:hover {
      color: #333;
    }
  }
}

.join-class-dialog-body {
  padding: 24px;

  p {
    margin: 0 0 10px;
    font-size: 16px;
    color: #666;
  }

  .invitation-code-input {
    width: 100%;
  }
}

.join-class-dialog-footer {
  padding: 12px 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-top: 1px solid #eee;

  button {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .cancel-btn {
    background-color: #f5f5f5;
    color: #666;
    border: 1px solid #ddd;

    &:hover {
      background-color: #eaeaea;
    }
  }

  .confirm-btn {
    background-color: #1890ff;
    color: #fff;
    border: 1px solid #1890ff;

    &:hover {
      background-color: #40a9ff;
      border-color: #40a9ff;
    }
  }
}
</style>