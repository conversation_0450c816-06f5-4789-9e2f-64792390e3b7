<!--src\views\public\course\index.vue-->
<template>
  <div class="course">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>课程中心</h1>
      <div class="page-header-desc">
        <div class="data"></div>
        <div class="part">
            <div>
                <h1>{{allNum }}</h1>
                <p>门</p>
            </div>
            <p>全部课程</p>
        </div>
        <div class="part">
            <div>
                <h1>{{smartNum }}</h1>
                <p>门</p>
            </div>
            <p>已建设知识图谱</p>
        </div>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="course-container">
      <!-- 左侧导航标签 -->
      <div class="course-nav">
        <h2>课程类型</h2>
        <button
          v-for="tab in tabs"
          :key="tab.title"
          @click="switchTab(tab)"
          :class="{ active: activeTab.title === tab.title }"
        >
          {{ tab.title }}
        </button>
      </div>

      <!-- 右侧内容区 -->
      <CourseDisplay 
        :course-type="activeTab.title" 
        :all-courses="coursesData"
        :total-courses="totalCourses"
        :is-loading="isLoading"
        @page-change="handlePageChange"
        @filter-change="handleFilterChange"
        @course-click="handleCourseClick"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth/auth';
import { ElMessage } from 'element-plus';
import CourseDisplay from '@/views/public/course/components/CourseDisplay.vue';
import { getCourseList } from '@/api/public/course/course';

const router = useRouter();
const userStore = useAuthStore();

const allNum = ref(0); 
const smartNum = ref(0); 
const coursesData = ref([]);
const totalCourses = ref(0);
const isLoading = ref(false);

const tabs = [
  { title: '全部' },
  { title: '通识必修课' },
  { title: '通识选修课' },
  { title: '学科基础课程' },
  { title: '专业教育课程' },
  { title: '实践教育课程' },
  { title: '多元化教育课程' }
];
const activeTab = ref(tabs[0]);

function switchTab(tab) {
  activeTab.value = tab;
}

// 处理分页变化
const handlePageChange = (pageParams) => {
  fetchCourses({
    ...pageParams,
    courseType: activeTab.value.title === '全部' ? '' : activeTab.value.title
  });
};

// 处理筛选变化
const handleFilterChange = (filterParams) => {
  fetchCourses({
    ...filterParams,
    courseType: activeTab.value.title === '全部' ? '' : activeTab.value.title
  });
};

// 获取课程数据
const fetchCourses = async (params = { pageNum: 1, pageSize: 16 }) => {
  isLoading.value = true;
  try {
    const res = await getCourseList(params);
    
    allNum.value = res.result.total || 0;
    totalCourses.value = res.result.total || 0;
    
    coursesData.value = res.result.records.map(course => ({
      id: course.id,
      title: course.name,
      type: course.courseType,
      semester: course.semester,
      cover: course.courseCover || '',
      properties: [
        course.courseType,
        course.subjectCategory,
        ...(course.specializations ? course.specializations.split('、') : [])
      ].filter(Boolean),
      hasKnowledgeGraph: false
    }));
    
    smartNum.value = 28; // 临时固定值
    
  } catch (error) {
    console.error('获取课程数据失败:', error);
  } finally {
    isLoading.value = false;
  }
};



onMounted(() => {
  fetchCourses();
});

watch(() => activeTab.value, () => {
  fetchCourses({
    pageNum: 1,
    pageSize: 16,
    semester: '',
    name: ''
  });
}, { deep: true });

watch(() => router.currentRoute.value, (newRoute) => {
  console.log('3. 路由变化:', newRoute);
}, { immediate: true });

</script>


<style lang="scss" scoped>
/* 页面整体样式 */
.course {
  position: relative;
  padding-top: 10vw;
  padding-left: 3vw;
  padding-right: 3vw;
  
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('@/assets/img/Home/ic_home_bg.png') top/cover no-repeat;
    z-index: -1;
  }
  
  .page-header {
    color: white;
    margin-bottom: 2vw;
    display: flex;
    justify-content: space-between; 
    
    h1 { 
      font-size: 3rem; 
      font-weight: 400;
      margin: 0; 
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); 
    }
    
    .page-header-desc {
      display: flex;
      margin-top: 5vw;
      gap: 2vw;
      
      .part {
        display: flex;
        flex-direction: column;
        align-items: end;
        gap: 1vw;
        
        div {
          display: flex;
          align-items: flex-end;
          justify-content: center;
          h1 {
            margin: 0; 
            line-height: 1;
            font-size: 3rem; 
          }
          
          p {
            margin: 0 0 0 0.2em;
            line-height: 1; 
            align-self: flex-end; 
            font-size: 1rem; 
          }
        }
      }
    }
  }
}

/* 课程容器样式 */
.course-container {
  display: flex;
  gap: 1.5vw;
  min-height: 70vh;
  background-color: white;
  border-radius: 1vw 1vw 0 0;
  overflow: hidden;
  padding: 2vw;
  margin-top: 10vw;
  
  .course-nav {
    flex: 0 0 15%; /* 固定宽度 */
    display: flex;
    flex-direction: column;
    gap: 1vw;
    padding-right: 1vw;
    
    h2 {
      font-size: 1.2rem;
      font-weight: 700;
      margin-bottom: 1vw;
    }
    
    button {
      padding: 0.8vw 2vw;
      border: $course-tabs-solid solid 1px;
      background-color: white;
      cursor: pointer;
      border-radius: 2vw;
      font-size: 1rem;
      transition: all 0.3s ease;
      text-align: left;
      
      &:hover {
        background-color: $course-tabs;
        color: $primary-color;
        border: $primary-color solid 1px;
      }
      
      &.active {
        background-color: $course-tabs;
        color: $primary-color;
        border: $primary-color solid 1px;
        font-weight: bold;
      }
    }
  }
  
  .course-content {
    flex: 1;
    padding: 0;
    border-radius: 0.3vw;
    overflow: hidden;
  }
}
</style>