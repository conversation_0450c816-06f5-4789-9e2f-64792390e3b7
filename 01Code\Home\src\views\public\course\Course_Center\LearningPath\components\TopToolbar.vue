<template>
  <div class="top-toolbar">
    <div class="left-section">
      <el-select
        :model-value="props.selectedPath"
        placeholder="选择学习路径"
        class="path-select"
        @change="handlePathChange"
      >
        <el-option
          v-for="path in props.savedPaths"
          :key="path.id"
          :label="path.pathName"
          :value="path.id"
        />
      </el-select>
      <el-button class="search-btn">更换路径</el-button>
    </div>

    <div class="right-section">
      <el-button class="icon-text-btn create-btn" @click="$emit('create-path')">
        <img :src="CreateIcon" alt="create" class="btn-icon" />
        创建路径
      </el-button>
      <el-button class="icon-text-btn edit-btn" @click="$emit('edit-units')">
        <img :src="EditIcon" alt="edit" class="btn-icon" />
        编辑单元
      </el-button>
    </div>
  </div>
</template>

<script setup>
import CreateIcon from '@/assets/courseMap-icon/learningPath/createIcon.svg'
import EditIcon from '@/assets/courseMap-icon/learningPath/editIcon.svg'

const props = defineProps({
  selectedPath: {
    type: String,
    default: ''
  },
  savedPaths: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:selectedPath', 'path-change', 'create-path', 'edit-units'])

const handlePathChange = (value) => {
  emit('update:selectedPath', value)
  emit('path-change', value)
}
</script>

<style lang="scss" scoped>
.top-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  margin-bottom: 20px;

  .left-section {
    display: flex;
    align-items: center;
    gap: 12px;

    .path-select {
      width: 200px;
    }

    .search-btn {
      background-color: #ff9500;
      border-color: #ff9500;
      color: white;
      
      &:hover {
        background-color: #e6850e;
        border-color: #e6850e;
      }
    }
  }

  .right-section {
    display: flex;
    gap: 12px;

    .icon-text-btn {
      display: flex;
      align-items: center;
      gap: 8px;
      background-color: white;
      border: 1px solid #dcdfe6;
      color: #606266;
      padding: 18px;
      border-radius: 4px;
      font-size: 14px;
      transition: all 0.3s ease;

      .btn-icon {
        width: 20px;
        height: 20px;
        margin-right: 5px;
      }

      &:hover {
        border-color: #c0c4cc;
        color: #409eff;
      }

      &.create-btn:hover {
        border-color: #f4c944;
        color: #f4c944;
      }

      &.edit-btn:hover {
        border-color: #85b952;
        color: #85b952;
      }
    }
  }
}
</style>
