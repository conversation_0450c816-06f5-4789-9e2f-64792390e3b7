<!--src\views\teacher\CourseDetailManager\Q-A\TopicDiscuss.vue-->
<template>
  <div class="topic-discuss">
    <!-- 在filter-header中添加批量操作按钮 -->
    <div class="filter-header">
      <div class="tab-header">
        <button 
          v-for="(tab, index) in tabs" 
          :key="index" 
          @click="handleTabChange(tab.value)"
          :class="{ active: activeTab === tab.value }"
        >
          {{ tab.label }}
        </button>
      </div>

      <div class="batch-actions" v-if="selectedPosts.length > 0">
        <button 
          class="btn delete-btn"
          @click="handleBatchDelete"
          :loading="batchDeleting"
        >
          删除选中({{ selectedPosts.length }})
        </button>
        <button class="btn clear-btn" @click="clearSelection">取消选择</button>
      </div>
      
      <el-select 
        v-model="statusFilter" 
        placeholder="审核状态" 
        class="status-filter"
        @change="handleStatusFilterChange"
        clearable
      >
        <el-option
          v-for="item in statusOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>

    <!-- 话题列表 - 添加复选框 -->
    <!-- 话题列表 - 修改后的复选框部分 -->
    <div class="topic-list">
      <div 
        v-for="(item, index) in topicList" 
        :key="index" 
        class="topic-item"
        :class="{ 'selected-item': selectedPostIds.includes(item.id) }"
      >
        <input 
          type="checkbox" 
          v-model="selectedPostIds" 
          :value="item.id"
          class="topic-checkbox"
        >
        
        <div 
          class="topic-content"
          @click="openDiscussDetail(item.id)"
        >
          <div class="topic-title">
            <span v-if="item.isPinned" class="pinned-tag">【置顶】</span>
            {{ item.title }}
          </div>
          <div class="topic-meta">
            <span>{{ item.viewCount || 0 }} 人浏览</span>
            <span>{{ item.answerCount || 0 }} 条回答</span>
            <div class="author-info">
              <div class="avatar-container">
                <img 
                  class="avatar" 
                  :src="item.user?.avatar || defaultAvatar" 
                  :alt="item.user?.name || '用户头像'"
                >
                <div v-if="item.user?.role === 2" class="teacher-badge">师</div>
              </div>
              <span>{{ item.user?.name || item.user?.account || '匿名用户' }}</span>
            </div>
            <span>{{ formatAgo(item.publishTime) }}更新</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 分页组件 -->
    <!-- 分页组件 -->
  <div class="pagination-container">
    <el-pagination
      v-if="total > 0"
      class="pagination"
      background
      layout="prev, pager, next"
      :current-page="currentPage" 
      :page-size="pageSize"
      :total="total"
      @current-change="handlePageChange"
    />
  </div>
    
    <!-- 加载状态 -->
    <div v-if="!loading && topicList.length === 0" class="empty">暂无话题</div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth/auth'
import { getTeacherPosts, batchDeletePosts } from '@/api/teacher/discuss'
import { formatAgo } from '@/utils/dateUtils'
import { ElMessage, ElMessageBox } from 'element-plus'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 默认头像
const defaultAvatar = ref('https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png')

// 选中话题ID数组
const selectedPostIds = ref([])
// 批量删除加载状态
const batchDeleting = ref(false)

// 审核状态选项
const statusOptions = ref([
  { value: 0, label: '未审核' },
  { value: 1, label: '已通过' },
  { value: 2, label: '未通过' }
])
const statusFilter = ref(null)

// 标签数据
const tabs = ref([
  { label: '最新', value: 'latest' },
  { label: '收藏', value: 'starred' }
])
const activeTab = ref('latest')

// 加载状态
const loading = ref(false)

// 存储所有话题数据
const allTopics = ref([])

// 分页设置
const currentPage = ref(1)
const pageSize = 5 // 前端每页显示数量
const total = computed(() => filterTopics().length) // 总数量基于过滤后的结果

// 计算当前显示的话题列表（已过滤和分页）
const topicList = computed(() => {
  const filtered = filterTopics()
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return filtered.slice(start, end)
})

// 选中的话题对象
const selectedPosts = computed(() => {
  return allTopics.value.filter(item => selectedPostIds.value.includes(item.id))
})

/**
 * 过滤话题数据
 */
const filterTopics = () => {
  let filtered = [...allTopics.value]
  
  // 状态过滤
  if (statusFilter.value !== null) {
    filtered = filtered.filter(item => item.status === statusFilter.value)
  }
  
  // 收藏标签过滤
  if (activeTab.value === 'starred') {
    filtered = filtered.filter(item => item.isStarred)
  }
  
  // 置顶话题排序
  return filtered.sort((a, b) => b.isPinned - a.isPinned)
}

/**
 * 获取所有话题数据
 */
const fetchAllTopics = async () => {
  try {
    loading.value = true
    const params = {
      pageNum: 1,
      pageSize: 100, // 获取适量数据，前端分页
      courseId: route.params.courseId,
      status: statusFilter.value !== null ? statusFilter.value : undefined,
      withCommentCount: true
    }
    
    const res = await getTeacherPosts(params)
    if (res.code === 200 && res.result?.records) {
      allTopics.value = res.result.records.map(topic => ({
        ...topic,
        // 构造用户信息对象
        user: {
          name: topic.publisherName || '匿名用户',
          avatar: topic.publisherAvatar || defaultAvatar.value,
          role: topic.publisherRole,
          account: topic.publisherAccount
        },
        answerCount: topic.commentCount || 0,
        // 确保有必要的字段
        isPinned: topic.isPinned || false,
        isStarred: topic.isStarred || false
      }))
    } else {
      allTopics.value = []
    }
  } catch (error) {
    console.error('获取话题列表失败:', error)
    ElMessage.error('获取话题列表失败')
    allTopics.value = []
  } finally {
    loading.value = false
  }
}

/**
 * 标签切换处理
 */
const handleTabChange = (tab) => {
  activeTab.value = tab
  currentPage.value = 1
}

/**
 * 审核状态筛选变化处理
 */
const handleStatusFilterChange = () => {
  currentPage.value = 1
}

/**
 * 分页变化处理
 */
const handlePageChange = (page) => {
  currentPage.value = page
}

/**
 * 打开话题详情页
 */
const openDiscussDetail = (topicId) => {
  const routeData = router.resolve({
    name: 'DiscussDetail',
    params: {
      courseId: route.params.courseId,
      topicId: topicId
    }
  })
  window.open(routeData.href, '_blank')
}

/**
 * 清空选择
 */
const clearSelection = () => {
  selectedPostIds.value = []
}

/**
 * 批量删除话题
 */
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedPosts.value.length} 个话题吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    batchDeleting.value = true
    const res = await batchDeletePosts(selectedPostIds.value)
    
    if (res.code === 200) {
      ElMessage.success(res.msg || '删除成功')
      clearSelection()
      await fetchAllTopics()
    } else {
      ElMessage.error(res.msg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除失败')
    }
  } finally {
    batchDeleting.value = false
  }
}

/**
 * 更新收藏列表（暴露给父组件）
 */
const updateStarredList = () => {
  // 找到当前选中话题并更新isStarred状态
  const selectedId = selectedPostIds.value[0]
  if (selectedId) {
    const topic = allTopics.value.find(t => t.id === selectedId)
    if (topic) {
      topic.isStarred = !topic.isStarred
    }
  }
  
  if (activeTab.value === 'starred') {
    currentPage.value = 1
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchAllTopics()
})

// 暴露方法给父组件
defineExpose({
  courseId: route.params.courseId,
  fetchTopics: fetchAllTopics,
  updateStarredList
})
</script>

<style lang="scss" scoped>
.topic-discuss {
  margin-bottom: 20px;
  padding: 30px;
  background-color: white;
  height: 80vh;
  position: relative;
  display: flex;
  flex-direction: column;

  .filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .tab-header {
      display: flex;
      gap: 10px;
    }

    .status-filter {
      width: 120px;
    }
  }
/*
  .topic-item {
    .topic-header {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 8px;

      .topic-title {
        font-size: 14px;
        font-weight: 500;
        color: #333;
      }

      .status-tag {
        margin-left: auto;
      }
    }
  }
    */

  .tab-header {
    display: flex;
    gap: 10px;

    button {
      padding: 8px 16px;
      border: none;
      background: transparent;
      cursor: pointer;
      color: #666;
      transition: all 0.3s ease;
      position: relative;

      &:hover {
        color: #333;
      }

      &.active {
        color: #333;
      }

      &.active::after {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        height: 3px;
        background-color: $primary-color;
      }
    }
  }

  .topic-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 20px;
    flex: 1;

    .topic-item {
      border:none;
      border-bottom: 1px solid #e5e7eb;
      padding: 16px 12px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: none; /* 移除悬浮阴影 */
        background-color: #f9fafb; /* 改为悬浮背景色变化 */
      }

      .topic-title {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 8px;
        color: #333;
      }

      .topic-meta {
        font-size: 12px;
        color: #909399;
        display: flex;
        align-items: center;

        & > *:not(:last-child) {
          margin-right: 30px;
        }

        .author-info {
          display: flex;
          align-items: center;
          gap: 8px;
          white-space: nowrap;
          flex: 1;

          .avatar-container {
            position: relative;
            width: 24px;
            height: 24px;
            
            .avatar {
              width: 24px;
              height: 24px;
              border-radius: 50%;
              object-fit: cover;
              background-color: #f0f2f5;
            }

            .teacher-badge {
              position: absolute;
              right: -3px;
              bottom: -3px;
              width: 16px;
              height: 16px;
              border-radius: 50%;
              background-color: $primary-color;
              color: white;
              font-size: 8px;
              display: flex;
              align-items: center;
              justify-content: center;
              line-height: 1;
              border: 2px solid white;
            }
          }
        }

        .update-time {
          margin-left: auto; /* 这将把更新时间推到最右侧 */
        }
      }
    }
  }

  .pagination-container {
      display: flex;
      justify-content: center; /* 水平居中 */
      margin-top: 20px; /* 可根据需要调整上边距 */
      width: 100%;
    }

  
  .empty {
    text-align: center;
    padding: 20px;
    color: #999;
  }
}

.pinned-tag {
  color: #ff4d4f;
  margin-right: 6px;
  font-weight: bold;
  font-size: 0.9em;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  .batch-actions {
    margin-left: auto;
    padding-right: 20px;

    .btn{
      background-color: white;
      color: $text-color-light;
      font-size: 0.9rem;
      padding: 4px 12px;
      border: $course-tabs solid 1px;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        background-color: $course-tabs;
        color: $text-color;
      }
    }

    .delete-btn {
      border-radius: 20px 0px 0px 20px;
    }
   .clear-btn {
      border-radius: 0px 20px 20px 0px;
    }
  }
}

.topic-item {
  display: flex;
  align-items: flex-start; /* 顶部对齐 */
  padding: 12px;
  border-bottom: 1px solid #e5e7eb;
  transition: background-color 0.3s;
  
  &.selected-item {
    background-color: #f5f7fa;
    border-left: 3px solid $primary-color;
  }
  
  .topic-checkbox {
    margin-right: 12px;
    margin-top: 3px; /* 与标题对齐 */
    width: 16px;
    height: 16px;
    cursor: pointer;
    
    /* 自定义复选框样式 */
    appearance: none;
    -webkit-appearance: none;
    background-color: white;
    border: 1px solid #dcdfe6;
    border-radius: 3px;
    position: relative;
    transition: all 0.2s ease;
    
    &:checked {
      background-color: $primary-color;
      border-color: $primary-color;
      transition: all 0.2s ease;
      
      &::after {
        content: "";
        position: absolute;
        left: 4px;
        top: 1px;
        width: 5px;
        height: 10px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
      }
    }
    
    &:hover:not(:checked) {
      border-color: $primary-color;
    }
  }
  
  .topic-content {
    flex: 1;
    cursor: pointer;
    
    .topic-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 8px;
      color: #333;
      
      &:hover {
        color: $primary-color;
      }
    }
  }
}
</style>