import request from '@/api/service'

export function fetchRegularGrades(classId) {
  return request.get(`/teacher/grade/regular-grades/${classId}`);
}

export function fetchExamGrades(classId) {
  return request.get(`/teacher/grade/exam-grades/${classId}`);
}
export function fetchHomeworkGrades(classId) {
  return request.get(`/teacher/grade/assignment-grades/${classId}`);
}

// 查询作业测试成绩
export function searchHomeworkGrades(classId, keyword) {
  return request.get(`/teacher/grade/assignment-grades/${classId}/search`, {
    params: {
      keyword: keyword
    }
  });
}

// 查询考试成绩
export function searchExamGrades(classId, keyword) {
  return request.get(`/teacher/grade/exam-grades/${classId}/search`, {
    params: {
      keyword: keyword
    }
  });
}

// 查询平时成绩
export function searchRegularGrades(classId, keyword) {
  return request.get(`/teacher/grade/regular-grades/${classId}/search`, {
    params: {
      keyword: keyword
    }
  });
}

// 查询成绩权重
export function getGradeWeightConfig(courseId, classId) {
  return request.get('/teacher/grade/weight-config', {
    params: {
      courseId:courseId,
      classId : classId
    }
  });
}
// 更新成绩权重
export function updateGradeWeightConfig(data) {
  return request.put('/teacher/grade/weight-config', data);
}