<template>
  <div class="experiment-content">
    <div class="content-header">
      <h2>实验内容</h2>
      <div class="header-divider"></div>
    </div>
    
    <div class="experiment-overview">
      <div class="overview-card">
        <h3>实验流程概览</h3>
        <p>{{ experimentData.overview }}</p>
      </div>
    </div>
    
    <div class="experiment-steps">
      <h3>实验步骤</h3>
      <div class="steps-container">
        <div 
          class="step-item" 
          v-for="(step, index) in experimentData.steps" 
          :key="index"
        >
          <div class="step-number">{{ index + 1 }}</div>
          <div class="step-content">
            <h4>{{ step.title }}</h4>
            <p>{{ step.description }}</p>
            <div class="step-details" v-if="step.details">
              <ul>
                <li v-for="detail in step.details" :key="detail">{{ detail }}</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="experiment-materials">
      <h3>实验器材与试剂</h3>
      <div class="materials-grid">
        <div class="material-category" v-for="category in experimentData.materials" :key="category.type">
          <h4>{{ category.type }}</h4>
          <ul>
            <li v-for="item in category.items" :key="item">{{ item }}</li>
          </ul>
        </div>
      </div>
    </div>
    
    <div class="safety-notes">
      <h3>安全注意事项</h3>
      <div class="safety-grid">
        <div class="safety-item" v-for="note in experimentData.safetyNotes" :key="note.title">
          <div class="safety-icon">
            <i :class="note.icon"></i>
          </div>
          <div class="safety-text">
            <h4>{{ note.title }}</h4>
            <p>{{ note.description }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const experimentData = ref({
  overview: "本实验通过虚拟仿真技术，模拟临床标本检测的完整流程，包括标本采集、处理、检测、结果分析等环节。学生可以在安全的虚拟环境中反复练习，掌握标准操作流程。",
  
  steps: [
    {
      title: "标本采集",
      description: "学习正确的标本采集方法和注意事项",
      details: [
        "选择合适的采集工具",
        "掌握无菌操作技术",
        "正确标记和保存标本"
      ]
    },
    {
      title: "标本预处理",
      description: "对采集的标本进行必要的预处理",
      details: [
        "标本的离心分离",
        "稀释和浓缩处理",
        "质量控制检查"
      ]
    },
    {
      title: "病原体检测",
      description: "使用各种检测方法识别病原体",
      details: [
        "显微镜检查",
        "培养基接种",
        "分子生物学检测",
        "免疫学检测"
      ]
    },
    {
      title: "结果判读",
      description: "正确解读检测结果并做出诊断",
      details: [
        "阳性和阴性结果判定",
        "质控结果分析",
        "报告书写规范"
      ]
    },
    {
      title: "质量控制",
      description: "确保检测结果的准确性和可靠性",
      details: [
        "内部质控管理",
        "外部质量评估",
        "仪器校准和维护"
      ]
    }
  ],
  
  materials: [
    {
      type: "采集工具",
      items: ["无菌棉拭子", "采血管", "尿杯", "痰盒", "培养皿"]
    },
    {
      type: "检测设备",
      items: ["显微镜", "离心机", "培养箱", "PCR仪", "酶标仪"]
    },
    {
      type: "试剂耗材",
      items: ["培养基", "染色液", "检测试剂盒", "移液器", "离心管"]
    },
    {
      type: "防护用品",
      items: ["实验服", "手套", "口罩", "护目镜", "帽子"]
    }
  ],
  
  safetyNotes: [
    {
      title: "生物安全",
      description: "严格遵守生物安全操作规程，避免病原体暴露",
      icon: "fas fa-biohazard"
    },
    {
      title: "化学安全",
      description: "正确使用化学试剂，注意通风和个人防护",
      icon: "fas fa-flask"
    },
    {
      title: "设备安全",
      description: "规范操作实验设备，定期维护保养",
      icon: "fas fa-cogs"
    },
    {
      title: "废物处理",
      description: "按规定分类处理实验废物，避免环境污染",
      icon: "fas fa-trash-alt"
    }
  ]
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.experiment-content {
  padding: 24px;
  max-width: 1000px;
  
  .content-header {
    margin-bottom: 32px;
    
    h2 {
      font-size: 28px;
      font-weight: 700;
      color: $text-color;
      margin: 0 0 16px 0;
    }
    
    .header-divider {
      width: 60px;
      height: 4px;
      background: linear-gradient(90deg, $primary-color, lighten($primary-color, 20%));
      border-radius: 2px;
    }
  }
  
  .experiment-overview {
    margin-bottom: 32px;
    
    .overview-card {
      background: rgba($primary-color, 0.05);
      border: 1px solid rgba($primary-color, 0.2);
      border-radius: 12px;
      padding: 24px;
      
      h3 {
        font-size: 20px;
        font-weight: 600;
        color: $primary-color;
        margin: 0 0 16px 0;
      }
      
      p {
        font-size: 16px;
        line-height: 1.8;
        color: $text-color;
        margin: 0;
      }
    }
  }
  
  .experiment-steps {
    margin-bottom: 32px;
    
    h3 {
      font-size: 20px;
      font-weight: 600;
      color: $text-color;
      margin-bottom: 24px;
      position: relative;
      padding-left: 16px;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background: $primary-color;
        border-radius: 2px;
      }
    }
    
    .steps-container {
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        left: 24px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: $border-color;
      }
      
      .step-item {
        display: flex;
        margin-bottom: 32px;
        position: relative;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .step-number {
          width: 48px;
          height: 48px;
          background: $primary-color;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 18px;
          flex-shrink: 0;
          position: relative;
          z-index: 1;
        }
        
        .step-content {
          margin-left: 24px;
          flex: 1;
          
          h4 {
            font-size: 18px;
            font-weight: 600;
            color: $text-color;
            margin: 0 0 12px 0;
          }
          
          p {
            font-size: 16px;
            line-height: 1.6;
            color: rgba($text-color, 0.8);
            margin: 0 0 16px 0;
          }
          
          .step-details ul {
            list-style: none;
            padding: 0;
            margin: 0;
            
            li {
              font-size: 14px;
              line-height: 1.5;
              color: rgba($text-color, 0.7);
              margin-bottom: 8px;
              padding-left: 20px;
              position: relative;
              
              &::before {
                content: '→';
                position: absolute;
                left: 0;
                color: $primary-color;
                font-weight: bold;
              }
            }
          }
        }
      }
    }
  }
  
  .experiment-materials {
    margin-bottom: 32px;
    
    h3 {
      font-size: 20px;
      font-weight: 600;
      color: $text-color;
      margin-bottom: 24px;
      position: relative;
      padding-left: 16px;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background: $primary-color;
        border-radius: 2px;
      }
    }
    
    .materials-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
      gap: 20px;
      
      .material-category {
        background: white;
        border: 1px solid $border-color;
        border-radius: 8px;
        padding: 20px;
        
        h4 {
          font-size: 16px;
          font-weight: 600;
          color: $primary-color;
          margin: 0 0 16px 0;
          padding-bottom: 8px;
          border-bottom: 1px solid $border-color;
        }
        
        ul {
          list-style: none;
          padding: 0;
          margin: 0;
          
          li {
            font-size: 14px;
            line-height: 1.5;
            color: $text-color;
            margin-bottom: 8px;
            padding-left: 16px;
            position: relative;
            
            &::before {
              content: '•';
              position: absolute;
              left: 0;
              color: $primary-color;
              font-weight: bold;
            }
          }
        }
      }
    }
  }
  
  .safety-notes {
    h3 {
      font-size: 20px;
      font-weight: 600;
      color: $text-color;
      margin-bottom: 24px;
      position: relative;
      padding-left: 16px;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background: $warning-color;
        border-radius: 2px;
      }
    }
    
    .safety-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
      
      .safety-item {
        display: flex;
        align-items: flex-start;
        gap: 16px;
        padding: 20px;
        background: rgba($warning-color, 0.05);
        border: 1px solid rgba($warning-color, 0.2);
        border-radius: 8px;
        
        .safety-icon {
          width: 48px;
          height: 48px;
          background: rgba($warning-color, 0.1);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          
          i {
            font-size: 20px;
            color: $warning-color;
          }
        }
        
        .safety-text {
          h4 {
            font-size: 16px;
            font-weight: 600;
            color: $text-color;
            margin: 0 0 8px 0;
          }
          
          p {
            font-size: 14px;
            color: rgba($text-color, 0.7);
            margin: 0;
            line-height: 1.5;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .experiment-content {
    padding: 16px;
    
    .content-header h2 {
      font-size: 24px;
    }
    
    .materials-grid {
      grid-template-columns: 1fr;
    }
    
    .safety-grid {
      grid-template-columns: 1fr;
    }
    
    .steps-container::before {
      left: 20px;
    }
    
    .step-item .step-number {
      width: 40px;
      height: 40px;
      font-size: 16px;
    }
  }
}
</style>
