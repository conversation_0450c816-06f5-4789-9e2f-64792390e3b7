<template>
  <div class="resource-association">
    <div class="resources-list">
      <ResourceList :items="resourceTree" v-model:selected="selectedIds" />
    </div>
    <ResourceFooter
      :selectedCount="selectedIds.length"
      @confirm="handleConfirm" />
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import ResourceList from "./components/ResourceList.vue";
import ResourceFooter from "./components/ResourceFooter.vue";
import { useQuestionGraphStore } from "@/stores/teacher/graphManager/questionGraphStore";

const router = useRouter();
const route = useRoute();
const store = useQuestionGraphStore();

const nodeId = route.params.nodeId;

const resourceTree = ref([]);

const selectedIds = ref([]);

const handleConfirm = async () => {
  console.log("=== 开始资源关联调试 ===");
  console.log("节点ID:", nodeId);
  console.log("选中的资源ID列表:", selectedIds.value);
  console.log("选中资源数量:", selectedIds.value.length);

  if (!nodeId) {
    console.error("❌ 节点ID为空，无法进行关联");
    alert("错误：节点ID为空");
    return;
  }

  if (!selectedIds.value || selectedIds.value.length === 0) {
    console.warn("⚠️ 没有选中任何资源");
    alert("请先选择要关联的资源");
    return;
  }

  try {
    console.log("📡 调用store.associateResourcesWithNode...");
    const result = await store.associateResourcesWithNode(
      nodeId,
      selectedIds.value
    );

    console.log("📡 API调用结果:", result);

    // 检查返回结果的结构
    if (typeof result === "object" && result !== null) {
      console.log("✅ 返回对象结构:", {
        success: result.success,
        changeCount: result.changeCount,
        error: result.error,
      });

      if (result.success) {
        console.log("🎉 关联成功！影响的资源数量:", result.changeCount);
        alert(
          `关联成功：${selectedIds.value.join(", ")} (影响${
            result.changeCount
          }个资源)`
        );
        router.go(-1);
      } else {
        console.error("❌ 关联失败:", result.error);
        alert(`关联失败：${result.error || "未知错误"}`);
      }
    } else if (result === true) {
      console.log("🎉 关联成功（返回布尔值true）");
      alert(`关联成功：${selectedIds.value.join(", ")}`);
      router.go(-1);
    } else {
      console.error("❌ 关联失败，返回值:", result);
      alert("关联失败，请重试");
    }
  } catch (error) {
    console.error("💥 关联过程中发生异常:", error);
    console.error("异常详情:", {
      message: error.message,
      stack: error.stack,
      name: error.name,
    });
    alert(`关联异常：${error.message}`);
  }

  console.log("=== 资源关联调试结束 ===");
};
onMounted(async () => {
  console.log("🚀🚀🚀 === 资源关联页面初始化调试 V2 === 🚀🚀🚀");
  console.log("🔥 当前节点ID:", nodeId);
  console.log("🔥 路由参数:", route.params);
  console.log("🔥 当前时间:", new Date().toLocaleTimeString());

  // 优先从路由参数获取，如果没有则从store获取
  let courseId = route.params.courseId;
  if (!courseId) {
    courseId = store.courseId;
    console.log("从store获取课程ID:", courseId);
  } else {
    console.log("从路由参数获取课程ID:", courseId);
  }

  if (!courseId) {
    console.error("❌ 缺少课程ID，无法获取资源树");
    alert("错误：缺少课程ID");
    return;
  }

  try {
    console.log("📡 开始获取资源树...");

    // 检查store中是否有fetchResourceTree方法
    if (typeof store.fetchResourceTree === "function") {
      console.log("✅ 使用 store.fetchResourceTree 方法");
      resourceTree.value = await store.fetchResourceTree(courseId);
    } else if (typeof store.fetchCourseResourceTree === "function") {
      console.log("✅ 使用 store.fetchCourseResourceTree 方法");
      resourceTree.value = await store.fetchCourseResourceTree(courseId);
    } else {
      console.error("❌ store中没有找到资源树获取方法");
      alert("错误：缺少资源树获取方法");
      return;
    }

    console.log("📡 获取到的资源树:", resourceTree.value);
    console.log("资源树节点数量:", resourceTree.value?.length || 0);

    // 获取节点详情并设置已选中的资源
    console.log("📡 开始获取节点详情...");
    const nodeDetail = await store.fetchNodeDetail(nodeId);
    console.log("📡 获取到的节点详情:", nodeDetail);

    if (nodeDetail && nodeDetail.resources) {
      console.log("✅ 节点详情中包含resources字段");
      console.log("resources数据:", nodeDetail.resources);

      // 尝试不同的ID字段映射
      let ids = [];
      if (nodeDetail.resources.length > 0) {
        const firstResource = nodeDetail.resources[0];
        console.log("第一个资源对象结构:", firstResource);

        if (firstResource.resourceId) {
          ids = nodeDetail.resources.map((r) => r.resourceId);
          console.log("✅ 使用resourceId字段:", ids);
        } else if (firstResource.id) {
          ids = nodeDetail.resources.map((r) => r.id);
          console.log("✅ 使用id字段:", ids);
        } else {
          console.warn("⚠️ 资源对象中没有找到id或resourceId字段");
          console.log("资源对象的所有字段:", Object.keys(firstResource));
        }
      }

      selectedIds.value = ids;
      console.log("✅ 已设置选中的资源ID列表:", selectedIds.value);
    } else {
      console.log("⚠️ 节点详情中没有找到 resources 字段或详情为空");
      if (nodeDetail) {
        console.log("节点详情的所有字段:", Object.keys(nodeDetail));
      }
    }
  } catch (error) {
    console.error("💥 初始化过程中发生异常:", error);
    console.error("异常详情:", {
      message: error.message,
      stack: error.stack,
      name: error.name,
    });
    alert(`初始化失败：${error.message}`);
  }

  console.log("=== 资源关联页面初始化调试结束 ===");
});
</script>

<style lang="scss" scoped>
.resource-association {
  display: flex;
  flex-direction: column;
  height: 100vh;

  .resources-list {
    flex: 1;
    overflow-y: auto;
    padding: 40px 80px 80px 80px;
    background: #fff;
    border-top: 1px solid #eee;
  }
}
</style>
