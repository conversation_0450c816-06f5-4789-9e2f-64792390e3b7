import request from '@/api/service'

// 发布公告
export function saveNotice(data) {
  return request({
    url: '/course/notice/save',
    method: 'post',
    data
  })
}
export function saveNotices(data) {
  return request({
    url: '/course/notice/batch-publish',
    method: 'post',
    data
  })
}

export function getNoticeList(params) {
  return request({
    url: '/course/notice/list',
    method: 'get',
    params
  })
}

// 删除公告
export function deleteNotice(id) {
  return request({
    url: '/course/notice/remove',
    method: 'delete',
    data: {
      id: id
    }
  })
}

// 编辑公告
export function updateNotice(data) {
  return request({
    url: '/course/notice/update',
    method: 'put',
    data: {
      id: data.id,           // 必须传入的公告ID
      title: data.title,     // 公告标题
      content: data.content, // 公告内容
      noticeType: data.noticeType, // 公告类型
      classId: data.classId, // 课程ID
    }
  })
}

export function getNoticeDetail(noticeId) {
  return request({
    url: `/course/notice/notice/${noticeId}/detail`, // 适配你的接口路径
    method: 'get'
  })
}




