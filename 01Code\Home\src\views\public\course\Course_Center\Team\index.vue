<template>
  <div class="teacher-team">
    <h2>教学团队</h2>
    <div v-if="loading" class="loading">加载中...</div>
    <div v-else-if="error" class="error">{{ error }}</div>
    <div v-else class="teacher-list">
      <div v-if="teachers.length === 0" class="empty">没有教学团队数据</div>
      <div class="teacher-card" v-for="teacher in teachers" :key="teacher.userid">
        <div class="img-container">
          <img :src="teacher.avatar || defaultAvatar" alt="teacher avatar" class="avatar">
        </div>        
        <div class="teacher-info">
          <h3>{{ teacher.teacherName }}</h3>
          <p class="title">{{ formatTitle(teacher) }}</p>
          <p class="description">{{ teacher.teacherIntro || '暂无简介' }}</p>
          <button @click="showDetails(teacher)">查看更多</button>
        </div>
      </div>
    </div>

    <!-- 模态框 -->
    <div v-if="selectedTeacher" class="modal">
      <div class="modal-content">
        <span class="close" @click="selectedTeacher = null">&times;</span>
        <div class="modal-img-container">
          <img :src="selectedTeacher.avatar || defaultAvatar" alt="teacher avatar" class="modal-avatar">
        </div>
        <div class="modal-info">
          <h3>{{ selectedTeacher.teacherName }}</h3>
          <p class="modal-title">{{ formatTitle(selectedTeacher) }}</p>
          <p class="modal-description">
            <strong>职称:</strong> {{ selectedTeacher.teacherTitle || '暂无' }}<br>
            <strong>学历:</strong> {{ selectedTeacher.educations || '暂无' }}<br>
            <strong>学院:</strong> {{ selectedTeacher.department || '暂无' }}<br>
            <strong>教师简介:</strong> {{ selectedTeacher.teacherIntro || '暂无' }}<br>
           
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { getCourseTeachers } from '@/api/teacher/courseTeam';
import defaultAvatar from '@/assets/img/nodata.png';

const route = useRoute(); // 使用路由钩子

const teachers = ref([]);
const selectedTeacher = ref(null);
const loading = ref(false);
const error = ref('');

// 格式化教师职称信息
const formatTitle = (teacher) => {
  const titles = [];
  if (teacher.masterTitle && teacher.masterName) {
    titles.push(`${teacher.masterTitle}: ${teacher.masterName}`);
  }
  if (teacher.teacherTitle) {
    titles.push(teacher.teacherTitle);
  }
  return titles.join(' | ') || '教师';
};

// 获取教学团队数据
const fetchTeachers = async () => {
  try {
    loading.value = true;
    error.value = '';
    const response = await getCourseTeachers(route.params.courseId); // 使用路由参数
    console.log('API Response:', response); // 输出查看返回的数据
    if (response.code === 200 && response.result) {
      teachers.value = response.result;
    } else {
      error.value = response.msg || '获取教学团队数据失败';
    }
  } catch (err) {
    error.value = err.message || '请求教学团队数据出错';
  } finally {
    loading.value = false;
  }
};

function showDetails(teacher) {
  selectedTeacher.value = teacher;
}

// 组件挂载时获取数据
onMounted(() => {
  fetchTeachers();
});
</script>

<style scoped>
.teacher-team {
  background-color: #fff;
  padding: 20px;
  width: 90%;
  margin: 0 auto;
  border-radius: 10px;
}

.teacher-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;
}

.teacher-card {
  background-color: #ede8e85a;
  border: 1px solid #ede8e85a;
  border-radius: 8px;
  padding: 20px;
  width: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.img-container {
  margin-bottom: 15px;
}

.avatar {
  width: 120px;
  height: 120px;
  border-radius: 10px;
  object-fit: cover;
}

.teacher-info {
  width: 100%;
}

.teacher-info h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #333;
}

.teacher-info .title {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #555;
  font-weight: 500;
}

.teacher-info .description {
  margin: 0 0 15px 0;
  font-size: 13px;
  color: #000;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 5; /* 控制显示的行数，根据需要调整 */
  -webkit-box-orient: vertical;
  height: 7em; /* 根据行数和字体大小调整 */
}

.teacher-info button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
}

.teacher-info button:hover {
  background-color: #0056b3;
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  pointer-events: auto; /* 允许事件传递 */
}

.modal-content {
  background-color: white;
  padding: 30px;
  border-radius: 10px;
  width: 80%;
  max-width: 650px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
}

.close {
  position: absolute;
  top: 15px;
  right: 15px;
  font-size: 30px;
  cursor: pointer;
  color: #666;
}

.close:hover {
  color: #333;
}

.modal-img-container {
  margin-bottom: 20px;
}

.modal-avatar {
  width: 150px;
  height: 150px;
  border-radius: 10px;
  object-fit: cover;
}

.modal-info {
  width: 100%;
}

.modal-info h3 {
  margin: 0 0 10px 0;
  font-size: 22px;
  color: #333;
}

.modal-title {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #555;
  font-weight: 500;
}

.modal-description {
  margin: 0;
  font-size: 14px;
  color: #000;
  line-height: 1.6;
  text-align: left;
  padding: 0 10px;
}

.loading, .error {
  text-align: center;
  padding: 20px;
  color: #666;
}

.error {
  color: #f56c6c;
}
</style>