import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getStudentNoticeList } from '@/api/student/course'

// 公告类型常量
const NOTICE_TYPES = {
  course: {
    value: 0,
    label: '课程公告'
  },
  homework: {
    value: 1,
    label: '作业通知'
  },
  exam: {
    value: 2,
    label: '考试通知'
  },
  system: {
    value: 3,
    label: '系统通知'
  }
}

const NOTICE_TYPE_MAP = {
  0: 'course',
  1: 'homework',
  2: 'exam',
  3: 'system'
}

export const useStudentNoticeStore = defineStore('studentNotice', () => {
  // 状态
  const notices = ref([])
  const activeTab = ref('course') // 当前激活的标签
  const isLoading = ref(false)
  const error = ref(null)
  const selectedNotice = ref(null) // 当前选中的公告
  const pagination = ref({
    pageNum: 1,
    pageSize: 10,
    total: 0,
    pages: 0
  })

  // 获取公告列表
  const fetchNotices = async (params = {}) => {
    try {
      isLoading.value = true
      error.value = null

      // 构建请求参数
      const requestParams = {
        pageNum: params.pageNum || pagination.value.pageNum,
        pageSize: params.pageSize || pagination.value.pageSize,
        courseId: params.courseId || null,
        noticeType: params.noticeType !== undefined ? params.noticeType : null,
        ...params.filters
      }

      const response = await getStudentNoticeList(requestParams)

      if (response.code === 200) {
        notices.value = response.result.records || []
        pagination.value = {
          pageNum: response.result.current || 1,
          pageSize: response.result.size || 10,
          total: response.result.total || 0,
          pages: response.result.pages || 1
        }

        // 如果使用了Mock数据，显示提示
        if (response.msg?.includes('Mock数据')) {
          console.warn('当前显示的是Mock数据，后端服务可能不可用')
        }

        return response.result
      }

      throw new Error(response.msg || '获取公告列表失败')
    } catch (err) {
      const errorMessage = err.response?.data?.msg || err.message || '获取公告列表失败'
      error.value = errorMessage
      console.error('获取公告列表失败:', err)

      // 如果是网络错误或服务器错误，提供更友好的错误信息
      if (err.code === 'NETWORK_ERROR' || err.response?.status >= 500) {
        error.value = '服务器连接失败，请检查网络连接或稍后重试'
      }

      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 根据activeTab过滤公告
  const filteredNotices = () => {
    const currentTypeValue = NOTICE_TYPES[activeTab.value]?.value
    return notices.value
      .filter(notice => notice.noticeType === currentTypeValue)
      .sort((a, b) => b.publishTime - a.publishTime)
  }

  // 获取所有公告（不过滤类型）
  const allNotices = () => {
    return notices.value.sort((a, b) => b.publishTime - a.publishTime)
  }

  // 设置选中的公告
  const setSelectedNotice = (notice) => {
    selectedNotice.value = notice
  }

  // 清空选中的公告
  const clearSelectedNotice = () => {
    selectedNotice.value = null
  }

  // 重置状态
  const resetState = () => {
    notices.value = []
    activeTab.value = 'course'
    error.value = null
    selectedNotice.value = null
    pagination.value = {
      pageNum: 1,
      pageSize: 10,
      total: 0,
      pages: 0
    }
  }

  // 返回状态和动作
  return {
    notices,
    NOTICE_TYPES,
    NOTICE_TYPE_MAP,
    pagination,
    activeTab,
    isLoading,
    error,
    selectedNotice,
    fetchNotices,
    filteredNotices,
    allNotices,
    setSelectedNotice,
    clearSelectedNotice,
    resetState
  }
})
