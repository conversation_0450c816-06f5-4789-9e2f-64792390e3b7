<!--src/components/teacher/AnswerItem.vue-->
<template>
  <div class="answer-item">
    <div class="avatar-container">
      <img :src="answer.avatar || '@/assets/default-avatar.png'" alt="回答者头像" class="avatar" />
      <div v-if="answer.role === 2" class="teacher-badge">师</div>
    </div>
    <div class="answer-content">
      <div class="answer-header">                                           
        <div class="answer-meta">
          <span class="answer-name">{{ answer.publisher }}</span>
          <span class="answer-school">{{ answer.school }}</span>
        </div>
        <span class="answer-time">{{ answer.updateTimeFormatted || '刚刚' }}</span>
      </div>
      <div class="answer-text" v-html="answer.content"></div>
      <div class="answer-operation">
  <div class="action-left">
    <button class="like-btn" :class="{ liked: answer.isLiked }" @click="handleLike">
      <i class="iconfont icon-like">
        <img src="@/assets/img/General/icon-like-normal.png" alt="点赞图标">
      </i>
      <span>{{ answer.likeCount || 0 }}个赞</span>
    </button>

    <button class="comment-btn" @click="toggleCommentSection">
      <i class="iconfont icon-comment">
        <img src="@/assets/img/General/icon-reply-normal.png" alt="评论图标">
      </i>
      <span>{{ answer.commentCount || 0 }} 条回复</span>
    </button>
  </div>
  
  <div class="action-right">
     <button 
        class="edit-btn" @click="handleEdit(answer)">
        <i class="iconfont icon-edit">
          <img src="@/assets/img/General/icon-edit-normal.png" alt="编辑图标">
        </i>
        <span>编辑</span>
      </button>

    <button class="star-bth" :class="{ starred: answer.isStarred }" @click.stop="handleStar(answer)">
      <i class="iconfont icon-favorite">
        <img src="@/assets/img/General/icon-star-yes-normal.png" alt="收藏图标">
      </i>
      <span>{{ answer.isStarred ? '已收藏' : '收藏' }}</span>
    </button>
    
    <button class="delete-btn" @click="handleDelete">
      <i class="iconfont icon-delete">
        <img src="@/assets/img/Teacher/icon-delete-normal.png" alt="删除图标">
      </i>
      <span>删除</span>
    </button>
  </div>
</div>
      
      <div class="comment-list" v-show="answer.showComments">
        <div v-if="answer.comments.length === 0" class="no-comments">暂无评论</div>
 <!-- 评论输入区域 -->
    <div class="comment-input-area">
      <textarea 
        class="comment-textarea" 
        v-model="answer.commentInput" 
        placeholder="发表评论..." 
        rows="2"
      ></textarea>
      <button 
        class="comment-submit-btn" 
        @click="submitComment"
        :disabled="isSubmittingComment"
      >
        {{ isSubmittingComment ? '提交中...' : '评论' }}
      </button>
    </div>
    
    <!-- 评论列表 -->
    <div v-if="answer.comments.length > 0" class="comment-items-container">
      <div class="comment-items" v-for="(comment, commentIndex) in answer.comments" :key="comment.id || commentIndex">
        <div class="comment-item">
          <div class="comment-avatar-container">
            <img :src="comment.avatar || '@/assets/default-avatar.png'" alt="评论者头像" class="comment-avatar" />
          </div>
          <div class="comment-content">
            <div class="comment-meta">
              <span class="comment-name">{{ comment.name || comment.userInfo?.name || '匿名用户' }}</span>
              <span class="comment-time">{{ comment.updateTimeFormatted || '刚刚' }}</span>
            </div>
            <div class="comment-text" v-html="comment.content"></div>
            <div class="comment-actions">
              <button class="reply-comment-btn" @click="handleReplyComment(comment)">
                <i class="iconfont icon-reply">
                  <img src="@/assets/img/General/icon-reply2-normal.png" alt="二级回复图标">
                </i>
                <span>回复</span>
              </button>
               <button 
                v-if="comment.commenterId === authStore.user.id"
                class="edit-comment-btn" 
                @click="handleEdit(comment)"
              >
                <i class="iconfont icon-edit">
                  <img src="@/assets/img/General/icon-edit-normal.png" alt="编辑图标">
                </i>
                <span>编辑</span>
              </button>
              <button class="delete-comment-btn" @click="handleDeleteComment(comment)">
                <i class="iconfont icon-delete">
                  <img src="@/assets/img/Teacher/icon-delete-normal.png" alt="删除图标">
                </i>
                <span>删除</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
        
        
      </div>
    </div>
  </div>
  <!-- 编辑模板 -->
    
  <!-- 自定义确认对话框 -->
  <div class="custom-confirm" v-if="showConfirmDialog">
    <div class="custom-confirm-content">
      <h3>{{ confirmTitle }}</h3>
      <p>{{ confirmMessage }}</p>
      <div class="custom-confirm-buttons">
        <button class="custom-confirm-cancel" @click="closeConfirmDialog(false)">取消</button>
        <button class="custom-confirm-ok" @click="closeConfirmDialog(true)">确定</button>
      </div>
    </div>
  </div>
  <!-- 自定义通知组件 -->
  <div class="custom-notification" v-if="notification.visible" :class="notification.type">
    <p>{{ notification.message }}</p>
  </div>
  <div class="edit-modal-overlay" v-if="showEditPanel" @click.self="showEditPanel = false">
    <div class="edit-modal">
      <div class="modal-header">
        <h3 class="modal-title">编辑内容</h3>
        <button class="close-button" @click="showEditPanel = false">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 6L6 18M6 6L18 18" stroke="#666" stroke-width="2" stroke-linecap="round"/>
          </svg>
        </button>
      </div>
      
      <div class="modal-body">
        <textarea 
          class="edit-textarea"
          v-model="editContent"
          placeholder="请输入内容..."
          rows="6"
          autofocus
        ></textarea>
      </div>
      
      <div class="modal-footer">
        <button class="secondary-button" @click="showEditPanel = false">
          取消
        </button>
        <button class="primary-button" @click="submitEdit">
          保存更改
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { useAuthStore } from '@/stores/auth/auth'
import { useRoute } from 'vue-router'
import { saveComment, getUserDetail,likeComment, removeLike,updateComment,deleteComment,saveCommentStar, getCommentStarList, removeCommentStar   } from '@/api/teacher/discuss'
import { formatTime } from '@/utils/dateUtils'

const props = defineProps({
  answer: {
    type: Object,
    required: true
  }
})

const emit = defineEmits([
  'like', 
  'toggle-comment', 
  'submit-comment', 
  'reply-comment',
  'delete-comment',
  'edit-comment',
  'star',
])

const authStore = useAuthStore()
const route = useRoute()
const currentPage = ref(1)
const isSubmittingComment = ref(false)

const userInfoMap = ref(new Map())
const userInfoCache = new Map()

// 自定义确认对话框状态
const showConfirmDialog = ref(false)
const confirmTitle = ref('确认操作')
const confirmMessage = ref('')
const confirmCallback = ref(null)


// 自定义通知状态
const notification = ref({
  visible: false,
  message: '',
  type: 'success' // success, error, warning
})

const showEditPanel = ref(false)
const editingItem = ref(null)
const editContent = ref('')

const isLoadingStar = ref(false)

// 打开确认对话框
const openConfirmDialog = (title, message, callback) => {
  confirmTitle.value = title
  confirmMessage.value = message
  confirmCallback.value = callback
  showConfirmDialog.value = true
}

// 关闭确认对话框
const closeConfirmDialog = (confirmed) => {
  showConfirmDialog.value = false
  if (typeof confirmCallback.value === 'function') {
    confirmCallback.value(confirmed)
  }
}

// 显示通知
const showNotification = (message, type = 'success') => {
  notification.value = {
    visible: true,
    message,
    type
  }
  // 3秒后自动隐藏
  setTimeout(() => {
    notification.value.visible = false
  }, 3000)
}

// 获取用户信息
const fetchUserInfo = async (userId) => {
  if (userInfoCache.has(userId)) {
    return userInfoCache.get(userId)
  }
  
  try {
    const res = await getUserDetail(userId)
    if (res.code === 200) {
      userInfoCache.set(userId, res.result)
      return res.result
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    return null
  }
}

// 处理挂载时获取二级评论用户信息
onMounted(async () => {
  if (props.answer.comments.length > 0) {
    for (const comment of props.answer.comments) {
      // 处理一级评论的用户信息
      if (comment.commenterId && !userInfoMap.value.has(comment.commenterId)) {
        await fetchUserInfo(comment.commenterId, comment)
      }
      
      // 处理二级评论的用户信息
      if (comment.children && comment.children.length > 0) {
        for (const childComment of comment.children) {
          if (childComment.commenterId && !userInfoMap.value.has(childComment.commenterId)) {
            await fetchUserInfo(childComment.commenterId, childComment)
          }
        }
      }
    }
  }
})

// 点赞处理
const handleLike = async () => {
  try {
    // 如果已经点赞，则取消点赞
    if (props.answer.isLiked) {
      // 这里需要知道点赞记录的ID，可以在获取评论数据时返回
      const res = await removeLike(props.answer.likeId)
      if (res.code === 200) {
        emit('like', {
          answer: props.answer,
          action: 'unlike',
          likeCount: props.answer.likeCount - 1,
          likeId: null
        })
      }
    } else {
      // 未点赞则进行点赞
      const res = await likeComment(props.answer.id)
      if (res.code === 200) {
        emit('like', {
          answer: props.answer,
          action: 'like',
          likeCount: props.answer.likeCount + 1,
          likeId: res.result.id // 保存点赞ID，用于取消点赞
        })
      }
    }
  } catch (error) {
    console.error('点赞操作失败:', error)
    showNotification('点赞操作失败', 'error')
  }
}

// 切换评论区显示/隐藏
const toggleCommentSection = () => emit('toggle-comment', props.answer)

// 提交评论
const submitComment = async () => {
  if (!props.answer.commentInput.trim()) {
    emit('submit-comment', { 
      answer: props.answer,
      error: '评论内容不能为空'
    })
    return
  }

  isSubmittingComment.value = true

  try {
    const commentData = {
      postId: props.answer.postId || route.params.topicId,
      commenterId: authStore.user.id,
      content: props.answer.commentInput,
      publishTime: Date.now(),
      parentId: props.answer.id // 确保这是后端返回的真实ID
    }

    const res = await saveComment(commentData)
    
    if (res.code === 200) {
      // 构建新评论对象
      const newComment = {
        id: res.result.id,
        commenterId: authStore.user.id,
        content: props.answer.commentInput,
        publishTime: res.result.publishTime,
        updateTimeFormatted: formatTime(res.result.publishTime),
        name: authStore.user.name,
        avatar: authStore.user.avatar,
        school: authStore.user.institution,
        role: authStore.user.role
      }

      // 触发事件通知父组件
      emit('submit-comment', {
        answer: props.answer,
        newComment: newComment
      })
      
      // 清空输入框
      props.answer.commentInput = ''
      
      // 自动展开评论区域（如果已关闭）
      props.answer.showComments = true
      
      // 滚动到最新评论
      nextTick(() => {
        const commentsContainer = document.querySelector(`.answer-item[data-id="${props.answer.id}"] .comment-list`)
        if (commentsContainer) {
          commentsContainer.scrollTop = commentsContainer.scrollHeight
        }
      })
    } else {
      throw new Error(res.msg || '评论提交失败')
    }
  } catch (error) {
    console.error('评论提交错误:', error)
    emit('submit-comment', {
      answer: props.answer,
      error: error.message
    })
  } finally {
    isSubmittingComment.value = false
  }
}

// 回复评论
const handleReplyComment = (comment) => {
  props.answer.commentInput = `回复 @${comment.name}: `
  nextTick(() => {
    const textarea = document.querySelector(`.answer-item[data-id="${props.answer.id}"] .comment-textarea`)
    if (textarea) {
      textarea.focus()
      textarea.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  })
}

// 删除评论(二级)
const handleDeleteComment = (comment) => {
  openConfirmDialog(
    '删除确认', 
    '确定要删除这条评论吗？删除后将无法恢复',
    async (confirmed) => {
      if (confirmed) {
        try {
          const res = await deleteComment(comment.id)
          
          if (res.code === 200) {
            emit('delete-comment', {
              answer: props.answer,
              comment: comment,
              success: true,
              needRefresh: true 
            })
            showNotification('删除成功')
          } else {
            showNotification(res.msg || '删除失败', 'error')
          }
        } catch (error) {
          console.error('删除评论失败:', error)
          showNotification('删除评论失败', 'error')
        }
      }
    }
  )
}

// 删除回答（一级）
const handleDelete = () => {
  openConfirmDialog(
    '删除确认', 
    '确定要删除这个回答吗？删除后将无法恢复',
    async (confirmed) => {
      if (confirmed) {
        try {
          // 调用删除接口
          const res = await deleteComment(props.answer.id)
          
          if (res.code === 200) {
            // 通知父组件删除成功
            emit('delete-comment', {
              answer: props.answer,
              comment: props.answer,
              success: true
            })
            showNotification('删除成功')
          } else {
            showNotification(res.msg || '删除失败', 'error')
          }
        } catch (error) {
          console.error('删除回答失败:', error)
          showNotification('删除回答失败', 'error')
        }
      }
    }
  )
}
// 打开编辑面板
const handleEdit = (item) => {
  editingItem.value = item
  editContent.value = item.content
  showEditPanel.value = true
}

// 提交编辑
const submitEdit = async () => {
  if (!editContent.value.trim()) {
    emit('edit-comment', {
      error: '内容不能为空'
    })
    return
  }

  try {
    const commentData = {
      id: editingItem.value.id,
      postId: editingItem.value.postId || route.params.topicId,
      commenterId: authStore.user.id,
      content: editContent.value,
      publishTime: Date.now(),
      parentId: editingItem.value.parentId || null
    }

    // 调用真实API
    const res = await updateComment(commentData)
    
    if (res.code === 200) {
      showEditPanel.value = false
      emit('edit-comment', {
        comment: editingItem.value,
        updatedComment: {
          ...editingItem.value,
          content: editContent.value,
          updateTimeFormatted: formatTime(Date.now())
        }
      })
      showNotification('更新成功')
    } else {
      throw new Error(res.msg || '更新失败')
    }
  } catch (error) {
    emit('edit-comment', {
      error: error.message || '更新时发生错误'
    })
    showNotification(error.message || '更新时发生错误', 'error')
  }
}
// 检查收藏状态的方法
const checkStarStatus = async () => {
  if (!authStore.isAuthenticated) return
  
  try {
    const res = await getCommentStarList({
      commentId: props.answer.id,
      userId: authStore.user.id
    })
    
    if (res.code === 200 && res.result.records.length > 0) {
      props.answer.isStarred = true
      props.answer.starId = res.result.records[0].id
    } else {
      props.answer.isStarred = false
      props.answer.starId = null
    }
  } catch (error) {
    console.error('获取收藏状态失败:', error)
  }
}
//收藏/取消收藏评论
const handleStar = async (answer) => {
  if (isLoadingStar.value) return
  isLoadingStar.value = true
  
  try {
    if (answer.isStarred) {
      // 取消收藏流程
      if (!answer.starId) {
        const res = await getCommentStarList({
          commentId: answer.id,
          userId: authStore.user.id
        })
        if (res.code === 200 && res.result.records.length > 0) {
          answer.starId = res.result.records[0].id
        } else {
          throw new Error('找不到收藏记录')
        }
      }
      
      const cancelRes = await removeCommentStar({ id: answer.starId })
      if (cancelRes.code === 200) {
        answer.isStarred = false
        answer.starCount = Math.max(0, (answer.starCount || 1) - 1)
        answer.starId = null
        showNotification('已取消收藏')
      }
    } else {
      // 收藏流程
      const res = await saveCommentStar({
        postId: answer.id,
        userId: authStore.user.id
      })
      if (res.code === 200) {
        answer.isStarred = true
        answer.starCount = (answer.starCount || 0) + 1
        answer.starId = res.result.id
        showNotification('收藏成功')
      }
    }
    
    // 触发star事件通知父组件
    emit('star', answer)
  } catch (error) {
    console.error('收藏操作失败:', error)
    showNotification(error.message || '操作失败，请稍后重试', 'error')
  } finally {
    isLoadingStar.value = false
  }
}

onMounted(async () => {
  await checkStarStatus()
})
</script>

<style scoped lang="scss">
@use "@/styles/teacher/CourseDetailManager/AnswerItem";
.answer-operation {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .star-bth, .delete-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    font-size: 14px;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s;
    
    &:hover {
      background-color: #f5f5f5;
      color: #333;
    }
    
    .iconfont {
      display: flex;
      align-items: center;
      
      img {
        width: 16px;
        height: 16px;
      }
    }
  }
  
  .delete-btn {
    color: #ff4d4f;
    
    &:hover {
      background-color: #fff2f0;
    }
  }
}

/* 自定义确认对话框样式 */
.custom-confirm {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  
  .custom-confirm-content {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    max-width: 400px;
    width: 90%;
    
    h3 {
      margin-top: 0;
      color: #333;
    }
    
    p {
      color: #666;
      margin-bottom: 20px;
    }
    
    .custom-confirm-buttons {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      
      button {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        
        &.custom-confirm-cancel {
          background-color: #f0f0f0;
          color: #333;
        }
        
        &.custom-confirm-ok {
          background-color: #ff4d4f;
          color: white;
        }
      }
    }
  }
}

/* 自定义通知样式 */
.custom-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 10px 20px;
  border-radius: 4px;
  color: white;
  z-index: 1000;
  opacity: 0.9;
  transition: opacity 0.3s;
  
  &.success {
    background-color: #52c41a;
  }
  
  &.error {
    background-color: #ff4d4f;
  }
  
  &.warning {
    background-color: #faad14;
  }
}
</style>