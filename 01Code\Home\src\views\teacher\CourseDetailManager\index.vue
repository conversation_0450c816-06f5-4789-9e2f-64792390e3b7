<!--src\views\teacher\CourseDetailManager\index.vue-->
<template>
  <div class="layout">
    <div class="main-container">
      <CourseSidebar />
      <main class="content">
        <router-view></router-view>
      </main>
    </div>
  </div>
</template>

<script setup>
import CourseSidebar from './components/CourseSidebar.vue';
import { useRoute } from 'vue-router';

const route = useRoute();
</script>

<style lang="scss" scoped>
// 颜色变量定义
$primary-color: #165DFF;
$text-color: #333;
$border-color: #E5E6EB;

.layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
    overflow: hidden; // 禁止滚动
}

.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background-color: white;
  border-bottom: 1px solid $border-color;
  z-index: 1000;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.main-container {
  display: flex;
  flex: 1;
  margin-top: 60px; 
  overflow: hidden;
}

.sidebar {
  position: fixed; // 固定定位
  top: 60px; // 距离顶部为 navbar 的高度
  left: 0;
  bottom: 0; // 从顶部到底部撑满
  width: 230px;
  background-color: #fff;
  border-right: 1px solid $border-color;
  padding-top: 20px;
  box-shadow: 1px 0 3px rgba(0, 0, 0, 0.05);
  overflow-y: auto; // 如果内容太多可以滚动
}

.content {
  flex: 1;
  background-color: $bg-color;
  border: #165DFF;
  margin:40px 30px 0 250px; // 距离左侧边栏的距离为260px
}
</style>