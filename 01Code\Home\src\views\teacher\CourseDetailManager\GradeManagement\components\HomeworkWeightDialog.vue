<template>
  <el-dialog
    v-model="visible"
    title="作业权重分配"
    width="800px"
    :close-on-click-modal="false"
  >
    <div class="homework-weight-dialog">
      <div class="class-info">
        <span>教学班：</span>
        <span class="class-name">{{ classInfo }}</span>
      </div>

      <el-table
        :data="homeworkList"
        border
        style="width: 100%"
        class="weight-table"
      >
        <el-table-column prop="name" label="作业名称" width="180" />
        <el-table-column prop="fullScore" label="满分值" width="100" />
        <el-table-column prop="creator" label="创建人" width="120" />
        <el-table-column prop="createTime" label="创建时间" width="150" />
        <el-table-column label="权重占比">
          <template #default="{ row }">
            <el-input-number
              v-model="row.weight"
              :min="0"
              :max="100"
              :precision="0"
              :controls="false"
              @change="calculateTotalWeight"
            />
            <span>%</span>
          </template>
        </el-table-column>
      </el-table>

      <div class="weight-summary">
        注: 总权重需100%, 当前总和为 {{ totalWeight }}%
      </div>
      <div class="weight-tip">
        设置后若新增作业,其权重默认为0;若删除作业,自定义权重失效,需重新分配
      </div>

      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">保存</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';

const props = defineProps({
  classInfo: {
    type: String,
    default: '数字媒体技术111班'
  },
  homeworkList: {
    type: Array,
    default: () => [
      { name: '111', fullScore: 111, creator: '杨兵', createTime: '2025-06-22', weight: 0 },
      { name: '1111', fullScore: 1, creator: '杨兵', createTime: '2025-07-08', weight: 0 },
      { name: '阿萨德啊', fullScore: 100, creator: '杨兵', createTime: '2025-07-08', weight: 0 },
      { name: '按时大', fullScore: 100, creator: '杨兵', createTime: '2025-07-08', weight: 0 }
    ]
  }
});

const emit = defineEmits(['confirm', 'cancel']);

const visible = ref(false);
const homeworkList = ref([...props.homeworkList]);

const totalWeight = computed(() => {
  return homeworkList.value.reduce((sum, item) => sum + item.weight, 0);
});

const calculateTotalWeight = () => {
  // 可以在这里添加额外的计算逻辑
};

const open = () => {
  visible.value = true;
};

const handleCancel = () => {
  visible.value = false;
  emit('cancel');
};

const handleConfirm = () => {
  if (totalWeight.value !== 100) {
    ElMessage.warning('总权重必须等于100%');
    return;
  }
  
  emit('confirm', {
    homeworkWeights: homeworkList.value.map(item => ({
      name: item.name,
      weight: item.weight
    }))
  });
  visible.value = false;
};

defineExpose({ open });
</script>

<style lang="scss" scoped>
.homework-weight-dialog {
  padding: 10px;

  .class-info {
    margin-bottom: 20px;
    font-size: 14px;

    .class-name {
      font-weight: bold;
    }
  }

  .weight-table {
    margin-bottom: 15px;

    :deep(.el-input-number) {
      width: 80px;
      margin-right: 5px;
    }
  }

  .weight-summary {
    color: #f56c6c;
    font-size: 14px;
    margin-bottom: 5px;
  }

  .weight-tip {
    color: #999;
    font-size: 12px;
    margin-bottom: 20px;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
  }
}
</style>