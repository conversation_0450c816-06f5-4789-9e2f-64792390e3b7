<template>
  <div class="team-card">
    <div class="team-header">
      <h3 class="team-name">{{ team.name }}</h3>
      <div v-if="isCreator" class="team-actions">
        <button class="settings-btn" @click.stop="toggleSettings">
          <i class="icon-settings">⚙️</i>
        </button>
        <div v-if="showSettings" class="settings-dropdown">
          <button @click="handleAction('add-member')">添加成员</button>
          <button @click="handleAction('remove-member')">删除成员</button>
          <button @click="handleAction('disband')" class="danger-action">解散团队</button>
        </div>
      </div>
    </div>
    
    <div class="members-list">
      <div v-if="loading" class="loading-members">
        <el-skeleton :rows="3" animated />
      </div>
      
      <div 
        v-for="member in team.members" 
        :key="member.teacherId" 
        class="member-card"
      >
        <div class="member-avatar">
          <img 
            :src="member.teacherImg || defaultAvatar" 
            class="avatar-img"
            @error="handleImageError"
          >
        </div>
        <div class="member-info">
          <h4 class="member-name">{{ member.teacherName }}</h4>
          <div class="member-meta">
            <span class="member-role">{{ member.roleName }}</span>
            <span class="member-title">{{ member.teacherTitle }}</span>
            <span class="member-institution">{{ member.institution }}</span>
          </div>
          <p class="member-intro">{{ member.teacherIntro }}</p>
        </div>
         <button class="delete-button" @click="handleRemoveMember(member)">移除</button>
      </div>
      
      <div v-if="!loading && (!team.members || team.members.length === 0)" class="empty-members">
        <el-empty description="暂无教师信息"></el-empty>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { removeTeacherFromTeam } from '@/api/teacher/courseTeam';
import defaultAvatar from '@/assets/img/nodata.png'

export default {
  name: 'TeamCard',
  props: {
    team: {
      type: Object,
      required: true
    },
    isCreator: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { emit }) {
    const showSettings = ref(false)
    const loading = ref(false) // This might be passed down from parent if needed
    const defaultAvatarPath = defaultAvatar // 引入默认头像

    const toggleSettings = () => {
      showSettings.value = !showSettings.value
    }

    // 定义删除成员的方法
    const handleRemoveMember = async (member) => {
    try {
      loading.value = true;
      const response = await removeTeacherFromTeam(props.team.courseId, member.teacherId);
      if (response.data.code === 0) {
        emit('member-removed', { 
          courseId: props.team.courseId, 
          teacherId: member.teacherId 
        });
      } else {
        ElMessage.error(response.data.msg || '移除失败');
      }
    } catch (error) {
      console.error('移除成员失败:', error);
      ElMessage.error('移除成员失败');
    } finally {
      loading.value = false;
    }
  };

    const handleAction = (type) => {
      if (type === 'add-member') {
        // 弹出添加成员的界面
        emit('action', { type, team: props.team });
      } else if (type === 'disband') {
        const confirmDisband = confirm('确定要解散这个团队吗？');
        if (confirmDisband) {
          emit('action', { type, team: props.team });
        }
      } else {
        emit('action', { type, team: props.team });
      }
      showSettings.value = false;
    };

    const handleImageError = (event) => {
      event.target.src = defaultAvatarPath
    }

    const getTeacherAvatar = (teacher) => {
      return teacher.teacherImg || defaultAvatar
    }

    return {
      showSettings,
      loading,
      toggleSettings,
      handleAction,
      handleImageError,
      getTeacherAvatar,
      handleImageError,
      defaultAvatarPath,
      handleRemoveMember
    }
  }
}
</script>

<style scoped>
.team-card {
  border: 1px solid #e0e0e0;
  border-radius: 10px;
  padding: 16px;
  margin-bottom: 20px;
  background-color: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.team-card:hover {
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.team-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.team-name {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.team-actions {
  position: relative;
}

.settings-btn {
  background: none;
  border: none;
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.settings-btn:hover {
  background-color: #f5f5f5;
}

.icon-settings {
  font-size: 18px;
  color: #666;
}

.settings-dropdown {
  position: absolute;
  right: 0;
  top: 100%;
  width: 140px;
  background: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 10;
  padding: 8px 0;
}

.settings-dropdown button {
  display: block;
  width: 100%;
  padding: 8px 16px;
  text-align: left;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  transition: all 0.2s;
}

.settings-dropdown button:hover {
  background-color: #f5f5f5;
  color: #1890ff;
}

.settings-dropdown .danger-action:hover {
  color: #ff4d4f;
}

.members-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.delete-button {
  width: 50px;
  height: 25px;
  font-size: 10px;
  background-color: #ff4d4f; /* 红色背景 */
  color: white; /* 白色文字 */
  border: none; /* 无边框 */
  padding: 5px 10px; /* 内边距 */
  border-radius: 4px; /* 圆角 */
  cursor: pointer; /* 鼠标悬停时显示指针 */
  transition: background-color 0.3s; /* 背景颜色过渡效果 */
}

.delete-button:hover {
  background-color: #d63031; /* 悬停时的背景颜色 */
   transform: scale(1.05);
}

.member-card {
  display: flex;
  padding: 12px;
  border-radius: 8px;
  background-color: #fafafa;
  transition: background-color 0.2s;
  position: relative; /* 使按钮绝对定位 */
}

.member-card:hover {
  background-color: #f0f0f0;
}

.member-avatar {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  margin-right: 12px;
}

.avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e8e8e8;
  background-color: #f5f5f5;
}

.member-info {
  flex: 1;
  min-width: 0;
}

.member-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.member-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
  flex-wrap: wrap;
}

.member-role {
  font-size: 12px;
  color: #fff;
  background-color: #1890ff;
  padding: 2px 8px;
  border-radius: 4px;
  line-height: 1.5;
}

.member-title,
.member-institution {
  font-size: 13px;
  color: #666;
}

.member-intro {
  margin: 0;
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.loading-members {
  padding: 20px;
  grid-column: 1 / -1;
}

.empty-members {
  padding: 40px 0;
  grid-column: 1 / -1;
  text-align: center;
}

@media (max-width: 768px) {
  .members-list {
    grid-template-columns: 1fr;
  }
  
  .member-card {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .member-avatar {
    margin-right: 0;
    margin-bottom: 12px;
  }
  
  .member-info {
    text-align: center;
  }
  
  .member-meta {
    justify-content: center;
  }
}
</style>