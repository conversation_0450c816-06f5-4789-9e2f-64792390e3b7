<!-- 路由：src\views\teacher\CourseDetailManager\WorkAndExam\index.vue -->
<template>
    <div class="task-exam-page">
        <!-- 顶部切换导航 -->
        <div class="top-bar">
            <el-radio-group v-model="mode" class="trapezoid-group" @change="handleModeChange">
                <el-radio-button class="trapezoid-button" label="assignment">作业测试</el-radio-button>
                <el-radio-button class="trapezoid-button" label="exam">考试</el-radio-button>
            </el-radio-group>
            <CreateButton />
        </div>

        <!-- 二级导航栏 -->
        <div class="sub-bar-row">
            <div class="status-btn-group">
                <el-button v-for="item in statusList" :key="item.name" type="text" class="status-btn"
                    :class="{ active: statusFilter === item.name }" @click="statusFilter = item.name">
                    {{ item.label }}
                </el-button>
            </div>

            <div class="sub-bar-right">
                <el-checkbox v-model="onlyMine">我发布的</el-checkbox>
                <el-input v-model="searchKeyword" placeholder="搜索标题" clearable class="search-input">
                    <template #suffix>
                        <el-icon>
                            <Search />
                        </el-icon>
                    </template>
                </el-input>
            </div>
        </div>

        <!-- 主体内容区域 -->
        <div class="content-wrapper">
            <div class="content-area">
                <el-empty v-if="filteredList.length === 0" description="暂无数据" />
                <el-card v-for="item in filteredList" :key="item.title" class="task-card" shadow="hover"
                    @click="handleCardClick(item)">
                    <div class="card-header">
                        <div class="left-section">
                            <span class="type-tag">{{ mode === 'assignment' ? '作业' : '考试' }}</span>
                            <h3 class="task-title">{{ item.title }}</h3>
                        </div>
                        <div class="right-section">
                            <span class="status-label">{{ getStatus(item) }}</span>
                            <el-dropdown>
                                <el-icon class="dropdown-icon">
                                    <MoreFilled />
                                </el-icon>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item @click="preview(item)">预览</el-dropdown-item>
                                        <el-dropdown-item @click="edit(item)">修改</el-dropdown-item>
                                        <el-dropdown-item @click="handleDelete(item)">删除</el-dropdown-item>
                                    </el-dropdown-menu>

                                </template>
                            </el-dropdown>
                        </div>
                    </div>

                    <div class="task-description">{{ item.description }}</div>

                    <div class="task-time-row">
                        <span>开始时间：{{ item.startTime }}</span>
                        <span>截止时间：{{ item.endTime }}</span>
                    </div>
                    <!-- 只有“已结束”状态才显示批改统计 -->
                    <div v-if="getStatus(item) === '已结束'" class="correction-status">
                        <span>已批：{{ item.correctedCount }}</span>
                        <span style="margin-left: 12px;">未批：{{ item.uncorrectedCount }}</span>
                        <span style="margin-left: 12px;">未交：{{ item.unsubmittedCount }}</span>
                    </div>
                </el-card>

            </div>

            <!-- 分页条 -->
            <div class="pagination-container">
                <el-pagination background layout="prev, pager, next" :total="totalItems" :page-size="pageSize"
                    :current-page="currentPage" @current-change="handlePageChange" />
            </div>
        </div>

        <!-- 预览弹窗 -->
        <el-dialog v-model="previewVisible" title="作业/考试预览" width="800px">
            <!-- 使用 ExamPreviewDialog 组件 -->
            <ExamPreviewDialog :examData="currentItem" />
        </el-dialog>


        <!-- 修改弹窗 -->
        <el-dialog v-model="editVisible" title="修改作业/考试信息" width="500px">
            <el-form :model="editForm" label-width="100px">
                <el-form-item label="标题">
                    <el-input v-model="editForm.title" />
                </el-form-item>

                <el-form-item label="状态">
                    <el-select v-model="editForm.status" placeholder="选择状态">
                        <el-option label="草稿" value="草稿" />
                        <el-option label="已发布" value="已发布" />
                    </el-select>
                </el-form-item>

                <el-form-item label="截止时间">
                    <el-date-picker v-model="editForm.endTime" type="datetime" placeholder="选择截止时间"
                        value-format="YYYY-MM-DD HH:mm" />
                </el-form-item>
            </el-form>

            <template #footer>
                <el-button @click="editVisible = false">取消</el-button>
                <el-button type="primary" @click="saveEdit">保存</el-button>
            </template>
        </el-dialog>

        <!-- 删除弹窗 -->
        <el-dialog v-model="deleteDialogVisible" title="确认取消" width="400px" class="custom-cancel-dialog"
            :close-on-click-modal="false" :destroy-on-close="true">
            <div class="dialog-body-text">
                确定要删除 <strong>{{ currentItem?.title }}</strong> 吗？
            </div>

            <template #footer>
                <el-button type="danger" @click="handleConfirmDelete">确认删除</el-button>
                <el-button @click="deleteDialogVisible = false">取消</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/userStore'
import { MoreFilled, Search } from '@element-plus/icons-vue'
import CreateButton from './components/CreateButton.vue'
import ExamPreviewDialog from './components/ExamPreviewDialog.vue'
import { getSubmitRecordsByClass } from '@/api/teacher/correction'

import {
    fetchAssignmentOrExamList,
    addAssignmentOrExam,
    fetchAssignmentOrExamDetail,
    updateAssignmentOrExam,
    deleteAssignmentOrExam,
    fetchBoundClassList
} from '@/api/teacher/assignment'


const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const courseId = route.params.courseId

// 当前模式：作业 / 考试
const mode = ref('assignment')
const statusFilter = ref('all')
const onlyMine = ref(false)
const searchKeyword = ref('')

// 列表数据
const allList = ref([])

// 分页参数
const currentPage = ref(1)
const pageSize = ref(4)
const totalItems = ref(0)

// 弹窗控制
const previewVisible = ref(false)
const editVisible = ref(false)
const deleteDialogVisible = ref(false)

// 当前项（预览/编辑/删除）
const currentItem = ref(null)
const editForm = ref({
    title: '',
    status: '',
    endTime: '',
})

// 状态中文映射
const getStatus = (item) => {
    const now = new Date()
    const start = new Date(item.startTime)
    const end = new Date(item.endTime)

    if (item.status === '草稿' || item.status === 0) return '未发布'
    if (now < start) return '未开始'
    if (now >= start && now <= end) return '进行中'
    if (now > end) return '已结束'
    return '未知'
}

const statusList = [
    { name: 'all', label: '全部' },
    { name: 'draft', label: '未发布' },
    { name: 'start', label: '未开始' },
    { name: 'ongoing', label: '进行中' },
    { name: 'ended', label: '已结束' },
]

//转换时间戳
const formatTime = (timestamp) => {
    const date = new Date(timestamp)
    const yyyy = date.getFullYear()
    const MM = String(date.getMonth() + 1).padStart(2, '0')
    const dd = String(date.getDate()).padStart(2, '0')
    const hh = String(date.getHours()).padStart(2, '0')
    const mm = String(date.getMinutes()).padStart(2, '0')
    return `${yyyy}/${MM}/${dd}  ${hh}:${mm}`
}

// 过滤后的展示列表
const filteredList = computed(() => {
    let list = [...allList.value]

    if (statusFilter.value !== 'all') {
        const map = {
            draft: '未发布',
            start: '未开始',
            ongoing: '进行中',
            ended: '已结束',
        }
        list = list.filter(i => getStatus(i) === map[statusFilter.value])
    }

    // 只显示“我发布的”或筛选班级
    if (onlyMine.value) list = list.filter(i => i.mine)

    const kw = searchKeyword.value.trim().toLowerCase()
    if (kw) list = list.filter(i => i.title.toLowerCase().includes(kw))

    // 计算分页：根据当前页数，限制列表项
    const startIndex = (currentPage.value - 1) * pageSize.value
    const endIndex = currentPage.value * pageSize.value
    return list.slice(startIndex, endIndex)
})

const fetchList = async () => {
    try {
        const params = {
            courseId,
            status: '',
            page: currentPage.value,
            size: pageSize.value,
        }

        const isExam = mode.value === 'exam'
        const res = await fetchAssignmentOrExamList(params, isExam)

        const teacherName = userStore.user?.name
        const records = res.result?.records || []

        const mappedList = records.map(item => ({
            ...item,
            title: item.title || '未命名',
            status: item.status === 0 ? '草稿' : '已发布',
            description: item.description || '暂无描述',
            startTime: formatTime(item.createdTime),
            endTime: formatTime(item.endTime),
            mine: item.teacherName === teacherName,
            correctedCount: 0,
            uncorrectedCount: 0,
            unsubmittedCount: 0,
        }))

        await Promise.all(
            mappedList.map(async (item) => {
                if (getStatus(item) !== '已结束') return

                try {
                    const classRes = await fetchBoundClassList(item.id, isExam)
                    const classList = classRes.result || []

                    let corrected = 0
                    let uncorrected = 0
                    let unsubmitted = 0

                    for (const cls of classList) {
                        const submitRes = await getSubmitRecordsByClass({
                            examId: isExam ? item.id : undefined,
                            assignmentId: !isExam ? item.id : undefined,
                            classId: cls.id,
                            page: 1,
                            size: 100
                        }, isExam)

                        const list = submitRes.result?.records || []

                        for (const s of list) {
                            const isSubmitted = s.status === 1
                            const reviewStatus = s.reviewStatus

                            if (!isSubmitted) {
                                unsubmitted++
                            } else if (reviewStatus === 2) {
                                corrected++
                            } else {
                                uncorrected++
                            }
                        }
                    }

                    item.correctedCount = corrected
                    item.uncorrectedCount = uncorrected
                    item.unsubmittedCount = unsubmitted
                } catch (err) {
                    console.warn(`[获取批改数据失败] ${item.title}`, err)
                }
            })
        )

        allList.value = mappedList
        totalItems.value = res.result?.total || 0
    } catch (error) {
        console.warn('请求失败:', error)
        ElMessage.error('获取作业/考试列表失败')
    }
}

// 切换作业/考试模式
const handleModeChange = () => {
    currentPage.value = 1
    fetchList()
}


const handlePageChange = (val) => {
    currentPage.value = val;
    fetchList();
};

// 预览
const preview = async (item) => {
    try {
        const isExam = mode.value === 'exam'
        const res = await fetchAssignmentOrExamDetail(item.id, isExam)
        const data = res.result || {}

        const questions = []

        if (Array.isArray(data.singleChoiceList)) {
            questions.push(...data.singleChoiceList.map(q => ({
                ...q.question,
                score: q.score,
                type: '单选题'
            })))
        }
        if (Array.isArray(data.multipleChoiceList)) {
            questions.push(...data.multipleChoiceList.map(q => ({
                ...q.question,
                score: q.score,
                type: '多选题'
            })))
        }
        if (Array.isArray(data.trueFalseList)) {
            questions.push(...data.trueFalseList.map(q => ({
                ...q.question,
                score: q.score,
                type: '判断题'
            })))
        }
        if (Array.isArray(data.fillInBlankList)) {
            questions.push(...data.fillInBlankList.map(q => ({
                ...q.question,
                score: q.score,
                type: '填空题'
            })))
        }
        if (Array.isArray(data.shortAnswerList)) {
            questions.push(...data.shortAnswerList.map(q => ({
                ...q.question,
                score: q.score,
                type: '问答题'
            })))
        }

        currentItem.value = {
            ...item,
            title: data.title || item.title,
            totalScore: data.totalScore || 0,
            questions,
            startTime: data.startTime,
            endTime: data.endTime,
        }

        previewVisible.value = true
    } catch (error) {
        ElMessage.error('获取题目信息失败')
    }
}


// 修改
const edit = (item) => {
    currentItem.value = item
    editForm.value = {
        title: item.title,
        status: item.status,
        endTime: item.endTime,
    }
    editVisible.value = true
}

// 保存修改
const saveEdit = async () => {
    try {
        const updatedData = {
            id: currentItem.value.id,
            title: editForm.value.title,
            status: editForm.value.status === '已发布' ? 1 : 0,
            endTime: new Date(editForm.value.endTime).getTime()
        }
        await updateAssignmentOrExam(updatedData, mode.value === 'exam')
        ElMessage.success('修改成功')
        editVisible.value = false
        fetchList()
    } catch (error) {
        ElMessage.error('修改失败')
    }
}

// 删除确认
const handleDelete = (item) => {
    currentItem.value = item
    deleteDialogVisible.value = true
}

// 确认删除
const handleConfirmDelete = async () => {
    try {
        await deleteAssignmentOrExam(currentItem.value.id, mode.value === 'exam')
        ElMessage.success(`已删除：${currentItem.value.title}`)
        deleteDialogVisible.value = false
        fetchList()  // 删除后刷新列表
    } catch (error) {
        ElMessage.error('删除失败')
    }
}


onMounted(() => {
    fetchList()
})

watch([mode, statusFilter], () => {
    currentPage.value = 1
    fetchList()
})

// 已结束状态
const handleCardClick = async (item) => {
    if (getStatus(item) !== '已结束') return

    const isExam = mode.value === 'exam'
    const id = item.id

    try {
        console.log('[获取班级列表] 请求参数:', { id, isExam })

        const res = await fetchBoundClassList(id, isExam)

        console.log('[获取班级列表] 响应数据:', res)

        const classList = res.result || []

        if (!classList.length) {
            ElMessage.warning('未获取到绑定班级')
            return
        }

        const classId = classList[0].id

        router.push({
            name: isExam ? 'ExamCorrection' : 'AssignmentCorrection',
            query: {
                id,
                title: item.title,
                classId,
                isExam: isExam.toString(),
                classIds: JSON.stringify(classList)
            },
            params: {
                courseId
            }
        })
    } catch (error) {
        console.error('[获取班级列表] 请求失败:', error)
        ElMessage.error('获取班级列表失败')
    }
}

</script>

<style scoped>
.task-exam-page {
    background-color: white;
    padding: 30px;
}

.top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.sub-bar-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 16px;
    border-bottom: 1.2px solid #d1d1d1;
    padding: 8px 16px 24px;
}

.status-btn-group {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    flex: 1;
    min-width: 300px;
}

.status-btn {
    padding: 6px 20px;
    border-radius: 4px;
    font-weight: 500;
    color: #606266;
}

.status-btn:hover,
.status-btn.active {
    color: #409eff;
    border-color: #a0c4ff;
}

.sub-bar-right {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.search-input {
    width: 200px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.left-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.right-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.dropdown-icon {
    cursor: pointer;
    font-size: 18px;
    color: #999;
}

.type-tag {
    background-color: #e0f0ff;
    color: #409eff;
    padding: 4px 10px;
    border-radius: 4px;
    font-size: 14px;
}

.task-title {
    font-weight: 600;
    font-size: 18px;
}

.status-label {
    font-weight: bold;
    font-size: 14px;
    color: #ffbf40;
}

.task-description {
    margin: 12px 0;
    font-size: 15px;
    color: #666;
}

.task-time-row {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #888;
}

.custom-cancel-dialog .dialog-body-text {
    font-size: 16px;
    color: #333;
    padding: 12px 0;
}

/* 提示框样式 */
.error-message {
    position: absolute;
    top: 78px;
    /* 导航栏高度 */
    left: 50%;
    transform: translateX(-50%);
    z-index: 100;
    background-color: rgba(255, 0, 0, 0.7);
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
}

/* 分页条样式 */
.content-wrapper {
    display: flex;
    flex-direction: column;
    min-height: 70vh;
    /* 根据页面整体高度适当设置 */
}

.content-area {
    flex: 1;
    padding: 0px;
}

.pagination-container {
    display: flex;
    justify-content: flex-end;
    padding: 0px 30px;
}

/* 已批、未批、未交 */
.task-card {
    position: relative;
    margin-bottom: 15px;
    padding-bottom: 20px;
    min-height: 140px;
}

.correction-status {
    position: absolute;
    bottom: 12px;
    right: 16px;
    display: flex;
    gap: 16px;
    font-size: 14px;
    color: #606266;
}
</style>