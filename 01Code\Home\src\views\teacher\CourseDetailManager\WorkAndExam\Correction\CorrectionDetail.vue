<!-- src\views\teacher\CourseDetailManager\WorkAndExam\Correction\CorrectionDetail.vue -->
<template>
    <div class="correction-page">
        <!-- 顶部导航栏 -->
        <div class="nav-bar">
            <div class="left-section">
                <el-button type="text" @click="$router.back()">
                    <el-icon>
                        <ArrowLeft />
                    </el-icon> 返回
                </el-button>
            </div>
            <div class="center-section">
                <h3>{{ assignmentTitle }}</h3>
            </div>
            <div class="right-section">
                <span class="student-name">{{ student.name }}</span>
                <el-button type="danger" @click="handleReject">打回试卷</el-button>
                <el-button type="primary" @click="handleFinish">批阅完成</el-button>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <div class="paper-detail">
                <!-- 单选题 -->
                <div v-if="singleChoiceList.length">
                    <h3>单选题</h3>
                    <div v-for="(q, index) in singleChoiceList" :key="q.id" class="question-block">
                        <div class="question-header">
                            {{ index + 1 }}. <span v-html="q.content"></span> （{{ q.score }}分）
                        </div>
                        <div class="options">
                            <div v-for="opt in q.options" :key="opt.value" class="option-item">
                                <input type="radio" :name="'single-' + q.id" :checked="q.studentAnswer === opt.value"
                                    disabled :class="getOptionClass(opt.value, q.studentAnswer, q.correct)" />
                                <label :class="getOptionClass(opt.value, q.studentAnswer, q.correct)">
                                    {{ opt.label }}. {{ opt.text }}
                                </label>
                            </div>
                        </div>
                        <div class="student-answer">
                            <div class="answer-summary">
                                <div> <strong>学生答案：</strong>{{ q.studentAnswerText }}</div>
                                <div> <strong>正确答案：</strong>{{ q.correctAnswerText }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 多选题 -->
                <div v-if="multipleChoiceList.length">
                    <h3>多选题</h3>
                    <div v-for="(q, index) in multipleChoiceList" :key="q.id" class="question-block">
                        <div class="question-header">
                            {{ index + 1 }}. <span v-html="q.content"></span> （{{ q.score }}分）
                        </div>
                        <div class="options">
                            <div v-for="opt in q.options" :key="opt.value" class="option-item">
                                <input type="checkbox"
                                    :checked="Array.isArray(q.studentAnswer) && q.studentAnswer.includes(opt.value)"
                                    disabled :class="getCheckboxClass(opt.value, q.studentAnswer, q.correct)" />
                                <label :class="getCheckboxClass(opt.value, q.studentAnswer, q.correct)">
                                    {{ opt.label }}. {{ opt.text }}
                                </label>
                            </div>
                        </div>
                        <div class="student-answer">
                            <div class="answer-summary">
                                <div> <strong>学生答案：</strong>{{ q.studentAnswerText }}</div>
                                <div> <strong>正确答案：</strong>{{ q.correctAnswerText }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 判断题 -->
                <div v-if="trueFalseList.length">
                    <h3>判断题</h3>
                    <div v-for="(q, index) in trueFalseList" :key="q.id" class="question-block">
                        <div class="question-header">
                            {{ index + 1 }}. <span v-html="q.content"></span> （{{ q.score }}分）
                        </div>
                        <div class="options">
                            <div class="option-item">
                                <input type="radio" :name="'tf-' + q.id" :checked="q.studentAnswer === true" disabled
                                    :class="getOptionClass(true, q.studentAnswer, q.correct)" />
                                <label :class="getOptionClass(true, q.studentAnswer, q.correct)">正确</label>
                            </div>
                            <div class="option-item">
                                <input type="radio" :name="'tf-' + q.id" :checked="q.studentAnswer === false" disabled
                                    :class="getOptionClass(false, q.studentAnswer, q.correct)" />
                                <label :class="getOptionClass(false, q.studentAnswer, q.correct)">错误</label>
                            </div>
                        </div>
                        <div class="student-answer">
                            <div class="answer-summary">
                                <div> <strong>学生答案：</strong>{{ q.studentAnswerText }}</div>
                                <div> <strong>正确答案：</strong>{{ q.correctAnswerText }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 填空题 -->
                <div v-if="fillInBlankList.length">
                    <h3>填空题</h3>
                    <div v-for="(q, index) in fillInBlankList" :key="q.id" class="question-block">
                        <div class="question-header">
                            {{ index + 1 }}. <span v-html="q.content"></span>（{{ q.score }}分）
                        </div>
                        <div class="student-answer">
                            <div v-if="q.studentAnswer?.length">
                                <div v-for="(ans, i) in q.studentAnswer" :key="i"><strong>学生答案：</strong>{{ ans }}</div>
                            </div>
                            <div v-else>未作答</div>
                            <div class="answer-summary">
                                <div> <strong>正确答案：</strong>{{ q.correctAnswerText }}</div>
                            </div>
                        </div>

                    </div>
                </div>

                <!-- 简答题 -->
                <div v-if="shortAnswerList.length">
                    <h3>简答题</h3>
                    <div v-for="(q, index) in shortAnswerList" :key="q.id" class="question-block">
                        <div class="question-header">
                            {{ index + 1 }}. <span v-html="q.content"></span>（{{ q.score }}分）
                        </div>
                        <div class="student-answer">
                            <strong>学生答案：</strong>
                            <div v-html="q.studentAnswer || '未作答'"></div>
                            <div class="answer-summary">
                                <strong>参考答案：</strong>
                                <div>{{ q.correctAnswerText }}</div>
                            </div>
                        </div>

                        <el-form label-position="top" class="correction-form">
                            <el-form-item label="评分">
                                <el-input-number v-model="q.teacherScore" :min="0" :max="q.score" size="small"
                                    controls-position="right" />
                            </el-form-item>


                            <el-form-item label="评语">
                                <el-input type="textarea" v-model="q.comment" placeholder="输入评语（可选）" rows="2" />
                            </el-form-item>
                        </el-form>
                    </div>
                </div>
            </div>

            <!-- 右侧总结栏 -->
            <div class="summary-panel">
                <div><label>总分：</label> <span>{{ totalScore }}</span></div>
                <el-form label-position="top" class="overall-comment">
                    <el-form-item label="总体评语">
                        <el-input type="textarea" v-model="overallComment" placeholder="输入总体评语" rows="4" />
                    </el-form-item>
                </el-form>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ArrowLeft } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getSubmitDetail, correctShortAnswer } from '@/api/teacher/correction'
import { fetchAssignmentOrExamDetail } from '@/api/teacher/assignment'
const router = useRouter()
// 路由参数
const route = useRoute()
const assignmentTitle = ref(route.query.title || '试卷标题')
const student = ref({ name: route.query.studentName || '李华' })

// 题目列表
const singleChoiceList = ref([])
const multipleChoiceList = ref([])
const trueFalseList = ref([])
const fillInBlankList = ref([])
const shortAnswerList = ref([])

const overallComment = ref('')

const handleReject = () => {
    ElMessage.info('已打回试卷')
    // TODO: 调用打回接口
}

// 批阅完成
const handleFinish = async () => {
    // 先检查简答题是否都有分数
    const unscored = shortAnswerList.value.filter(q => q.teacherScore === null || q.teacherScore === undefined || q.teacherScore === '')

    if (unscored.length > 0) {
        ElMessage.warning('请先完成所有简答题评分后再提交')
        return
    }

    const submitRecordId = route.query.submitRecordId
    let hasError = false

    for (const q of shortAnswerList.value) {
        try {
            await correctShortAnswer({
                submitRecordId,
                questionId: q.id,
                score: q.teacherScore,
            })
        } catch {
            hasError = true
            ElMessage.error(`题目 ${q.id} 批改失败`)
        }
    }

    if (!hasError) {
        ElMessage.success('已提交全部评分')
        router.back()
    }
}


// 选项样式判定函数
function getOptionClass(val, studentAnswer, correct) {
    if (studentAnswer === val && val === correct) return 'correct'
    if (studentAnswer === val && val !== correct) return 'incorrect'
    return ''
}

function getCheckboxClass(val, studentAnswers, correctAnswers) {
    if (studentAnswers?.includes(val)) {
        if (correctAnswers?.includes(val)) return 'correct'
        else return 'incorrect'
    }
    return ''
}

// 解析选项字符串为数组 [{label, value, text}]
function parseOptions(optionStr) {
    try {
        const obj = JSON.parse(optionStr)
        return Object.entries(obj).map(([key, text]) => ({
            label: key,
            value: key,
            text,
        }))
    } catch {
        return []
    }
}

// 解析学生答案字符串或布尔等
function parseAnswer(val) {
    if (val === 'true') return true
    if (val === 'false') return false
    try {
        const parsed = JSON.parse(val)
        return Array.isArray(parsed) ? parsed : val
    } catch {
        return val
    }
}

// 合并答案和题目，补充选项和解析答案
const questionMap = ref({})
const examDetail = ref({})

const renderAnswer = (val) => {
    if (Array.isArray(val)) return val.join(', ')
    if (typeof val === 'boolean') return val ? '正确' : '错误'
    return val || '未作答'
}

const mergeAnswerAndQuestion = (ans) => {
    const base = questionMap.value[ans.questionId]
    if (!base) return null

    // 学生答案直接是ans.answer字段
    const parsedStudentAns = parseAnswer(ans.answer)
    const parsedCorrect = parseAnswer(base.correctAnswer)

    return {
        id: ans.questionId,
        content: base.content,
        score: base.score || 10,
        options: base.options ? parseOptions(base.options) : [],
        correct: parsedCorrect,
        studentAnswer: parsedStudentAns,
        studentAnswerText: renderAnswer(parsedStudentAns),
        correctAnswerText: renderAnswer(parsedCorrect),
        teacherScore: ans.score || 0,
        comment: ans.comment || '',
        type: base.type,
    }
}

// 加载试卷详情，构造题目映射
const loadAssignmentOrExamDetail = async () => {
    const isExam = route.query.isExam === 'true'
    const id = route.query.id
    if (!id) {
        ElMessage.error('未提供考试/作业ID')
        return
    }
    try {
        const res = await fetchAssignmentOrExamDetail(id, isExam)
        if (res.code === 200 && res.result) {
            examDetail.value = res.result
            questionMap.value = {}

            const questionTypes = [
                { key: 'singleChoiceList', type: 0 },
                { key: 'multipleChoiceList', type: 1 },
                { key: 'trueFalseList', type: 2 },
                { key: 'fillInBlankList', type: 3 },
                { key: 'shortAnswerList', type: 4 },
            ]

            questionTypes.forEach(({ key, type }) => {
                const questions = res.result[key] || []
                questions.forEach(({ question: q, score }) => {
                    let correctAnswer = ''
                    switch (type) {
                        case 0: // 单选
                            correctAnswer = q.correct || ''
                            break
                        case 1: // 多选
                            correctAnswer = q.correct || ''
                            break
                        case 2: // 判断
                            correctAnswer = q.answer !== undefined ? q.answer : ''
                            break
                        case 3: // 填空
                            correctAnswer = q.answers || ''
                            break
                        case 4: // 简答
                            correctAnswer = q.sampleAnswer || ''
                            break
                    }
                    questionMap.value[q.id] = {
                        ...q,
                        score: score || 10,
                        type,
                        correctAnswer,
                    }
                })
            })
        } else {
            ElMessage.error('试卷详情加载失败：' + res.msg)
        }
    } catch {
        ElMessage.error('获取试卷详情失败')
    }
}

// 加载学生提交答题详情，合并数据到对应列表
const loadSubmitDetail = async () => {
    const isExam = route.query.isExam === 'true'
    const submitRecordId = route.query.submitRecordId

    try {
        const res = await getSubmitDetail({ submitRecordId }, isExam)
        if (res.code === 200 && res.result) {
            const answers = res.result.answers || []

            // 清空
            singleChoiceList.value = []
            multipleChoiceList.value = []
            trueFalseList.value = []
            fillInBlankList.value = []
            shortAnswerList.value = []

            answers.forEach((ans) => {
                const merged = mergeAnswerAndQuestion(ans)
                if (!merged) return

                switch (merged.type) {
                    case 0:
                        singleChoiceList.value.push(merged)
                        break
                    case 1:
                        multipleChoiceList.value.push(merged)
                        break
                    case 2:
                        trueFalseList.value.push(merged)
                        break
                    case 3:
                        fillInBlankList.value.push(merged)
                        break
                    case 4:
                        shortAnswerList.value.push(merged)
                        break
                }
            })
        }
    } catch {
        ElMessage.error('加载提交详情失败')
    }
}

// 计算总分
const totalScore = computed(() => {
    const autoScore = [
        ...singleChoiceList.value,
        ...multipleChoiceList.value,
        ...trueFalseList.value,
        ...fillInBlankList.value
    ].reduce((sum, q) => sum + (q.teacherScore || 0), 0)

    const manualScore = shortAnswerList.value.reduce((sum, q) => sum + (q.teacherScore || 0), 0)

    return autoScore + manualScore
})


onMounted(async () => {
    await loadAssignmentOrExamDetail()
    await loadSubmitDetail()
})
</script>

<style scoped>
.correction-page {
    background: #fff;
    padding: 24px 32px;
    min-height: 100vh;
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    color: #333;
}

.nav-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    border-bottom: 2px solid #e0e6ed;
    padding-bottom: 12px;
    font-weight: 600;
    font-size: 18px;
    color: #2c3e50;
}

.left-section,
.center-section,
.right-section {
    flex: 1;
    display: flex;
    align-items: center;
}

.center-section {
    justify-content: center;
    /* font-size: 16px; */
    font-weight: 700;
}

.right-section {
    justify-content: flex-end;
    gap: 16px;
}

.student-name {
    color: #555;
    font-size: 15px;
    font-weight: 500;
    margin-right: 28px;
}

/*  */
.content-area {
    display: flex;
    gap: 24px;
}

.paper-detail {
    flex: 3;
    overflow: auto;
}

.summary-panel {
    flex: 1;
    background: #f9f9f9;
    border-left: 1px solid #ddd;
    padding: 16px;
    position: sticky;
    top: 90px;
    height: fit-content;
}


.question-block {
    border: 1.5px solid #d1d9e6;
    padding: 18px 22px;
    margin-bottom: 22px;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgb(0 0 0 / 0.05);
    background-color: #fafbfc;
    transition: border-color 0.3s ease;
}

.question-header {
    font-weight: 700;
    font-size: 17px;
    margin-bottom: 14px;
    color: #34495e;
}

.options {
    margin-left: 24px;
}

.option-item {
    line-height: 28px;
    margin-bottom: 10px;
    user-select: none;
    display: flex;
    align-items: center;
    font-size: 15px;
    cursor: default;
}

.option-item input[type="radio"],
.option-item input[type="checkbox"] {
    margin-right: 10px;
    width: 18px;
    height: 18px;
    cursor: default;
    accent-color: #409EFF;
}

.option-item label {
    user-select: none;
    cursor: default;
}

/* 正确选项：绿色，背景微调 */
.correct {
    color: #2ecc71;
    font-weight: 600;
}

.correct label {
    background-color: #e6f4ea;
    padding: 2px 6px;
    border-radius: 4px;
}

.answer-summary {
    font-size: 14px;
    color: #555;
}


/* 错误选项：红色，背景微调 */
.incorrect {
    color: #e74c3c;
    font-weight: 600;
}

.incorrect label {
    background-color: #fdecea;
    padding: 2px 6px;
    border-radius: 4px;
}

.student-answer {
    margin-top: 10px;
    background: #f4f6f9;
    padding: 10px 14px;
    border-left: 5px solid #409EFF;
    font-size: 14px;
    color: #2c3e50;
    border-radius: 4px;
    line-height: 1.5;
}

.correction-form {
    margin-top: 16px;
}

.summary-panel label {
    font-weight: 700;
    margin-right: 12px;
    color: #34495e;
}

.overall-comment {
    margin-top: 20px;
}

.el-input__inner,
.el-input-number__input {
    font-size: 14px;
    padding: 6px 8px;
}

.el-button--danger {
    background-color: #e74c3c;
    border-color: #e74c3c;
}

.el-button--danger:hover {
    background-color: #c0392b;
    border-color: #c0392b;
}

.el-button--primary {
    background-color: #409EFF;
    border-color: #409EFF;
}

.el-button--primary:hover {
    background-color: #357ABD;
    border-color: #357ABD;
}

.selected {
    color: #409EFF;
    font-weight: bold;
}

.selected label {
    background-color: #e8f4ff;
    padding: 2px 6px;
    border-radius: 4px;
}
</style>
