<template>
  <div class="graph-container">
    <GraphToolbar />
    <hr>
    <GraphList />
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useKnowledgeGraphStore } from '@/stores/teacher/graphManager/knowledgeGraphStore'

import GraphToolbar from './components/GraphToolbar.vue'
import GraphList from './components/GraphList.vue'

const store = useKnowledgeGraphStore()
const route = useRoute()

onMounted(() => {
  const courseId = route.params.courseId
  if (courseId) {
    store.fetchGraphListByCourse(courseId)
  }
})
</script>


<style lang="scss" scoped>
.graph-container {
  background-color: #ffffff; 
  padding: 20px;
  height: 80vh;
}

hr {
  border: none;
  border-top: 1px solid #f0f0f0;
}
</style>