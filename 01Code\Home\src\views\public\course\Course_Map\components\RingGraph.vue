<template>
  <!-- 环形图组件的根容器 -->
  <div class="ring-graph-panel">
    <!-- 图表渲染容器 -->
    <div ref="ringGraphContainer" class="ring-graph-container"></div>

    <!-- 右键菜单 -->
    <div
      v-if="contextMenu.visible"
      class="context-menu"
      :style="{ top: contextMenu.y + 'px', left: contextMenu.x + 'px' }"
      @mouseenter="cancelHideMenuTimer"
      @mouseleave="startHideMenuTimer"
    >
      <div class="menu-item" @click="viewNodeDetails">查看详情</div>
    </div>

    <!-- 层级控制按钮和菜单 -->
    <div class="level-control-box">
      <button class="level-control-btn" @click="showLevelMenu = !showLevelMenu">层级管理</button>
      <div v-if="showLevelMenu" class="level-menu">
        <div class="level-menu-item" @click="selectLevel(99)">展开全部</div>
        <div class="level-menu-item" @click="selectLevel(3)">展开 3 级</div>
        <div class="level-menu-item" @click="selectLevel(2)">展开 2 级</div>
        <div class="level-menu-item" @click="selectLevel(1)">展开 1 级</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, defineEmits, computed } from 'vue'
import { useRoute } from 'vue-router'
import G6 from '@antv/g6'
import { getRingView } from '@/api/public/course/courseMap/knowledgeGraph.js'

// 定义向外触发的事件
const emit = defineEmits(['node-click'])

// 图表容器引用
const ringGraphContainer = ref(null)
// 层级菜单显示状态
const showLevelMenu = ref(false)
// 当前展开层级
const currentLevel = ref(2)
// 可见节点ID集合
const visibleNodeIds = ref(new Set())
// G6图表实例
let graph = null
// 菜单隐藏计时器
let menuHideTimer = null

// 右键菜单状态
const contextMenu = ref({
  visible: false,
  x: 0,
  y: 0,
  node: null
})

// 垂直偏移量
const offsetY = -110

// 分支颜色配置
const branchColors = ['#ff6b6b', '#4d96ff', '#6bcb77', '#feca57']

// 获取图谱ID，仿照树状图谱方式
const props = defineProps({
  graphId: { type: String, default: null },
  maxLevel: { type: Number, default: null }
})
const route = useRoute()
function getGraphId() {
  return props.graphId || route.params.graphId || 'center'
}

// --- flatListToTree ---
function flatListToTree(flatList, idKey = 'nodeId', parentKey = 'parentId') {
  const idMap = {}
  const tree = []
  flatList.forEach(node => { idMap[node[idKey]] = { ...node, children: [] } })
  flatList.forEach(node => {
    const parentId = node[parentKey]
    if (parentId && idMap[parentId]) {
      idMap[parentId].children.push(idMap[node[idKey]])
    } else {
      tree.push(idMap[node[idKey]])
    }
  })
  return tree.length === 1 ? tree[0] : { nodeName: 'root', children: tree }
}

// --- flatListToEdges ---
function flatListToEdges(flatList, idKey = 'nodeId', parentKey = 'parentId') {
  return flatList
    .filter(node => node[parentKey])
    .map(node => ({
      source: node[parentKey],
      target: node[idKey]
    }))
}

// --- dynamicRadial 布局注册 ---
function computeWeight(node) {
  if (!node.children || node.children.length === 0) {
    node.weight = 1
  } else {
    node.weight = 1 + node.children.reduce((sum, child) => sum + computeWeight(child), 0)
  }
  return node.weight
}
function assignAngles(node, startAngle, endAngle) {
  node.angle = (startAngle + endAngle) / 2
  node.startAngle = startAngle
  node.endAngle = endAngle
  if (!node.children || node.children.length === 0) return
  const totalWeight = node.children.reduce((sum, child) => sum + child.weight, 0)
  let currentAngle = startAngle
  for (const child of node.children) {
    const angleSpan = (child.weight / totalWeight) * (endAngle - startAngle)
    assignAngles(child, currentAngle, currentAngle + angleSpan)
    currentAngle += angleSpan
  }
}
function assignPositions(node, depth, radiusStep) {
  const r = depth * radiusStep
  node.x = r * Math.cos(node.angle)
  node.y = r * Math.sin(node.angle)
  node.level = depth
  if (node.children) {
    for (const child of node.children) {
      assignPositions(child, depth + 1, radiusStep)
    }
  }
}

// --- 静态 treeData 兜底 ---
const treeData = {
  nodeId: 'center',
  nodeName: '导论',
  children: Array.from({ length: 4 }, (_, i) => ({
    nodeId: `n${i + 1}`,
    nodeName: `一级${i + 1}`,
    children: Array.from({ length: 3 + i }, (_, j) => ({
      nodeId: `n${i + 1}-${j + 1}`,
      nodeName: `二级${i + 1}-${j + 1}`,
      children: Array.from({ length: 2 }, (_, k) => ({
        nodeId: `n${i + 1}-${j + 1}-${k + 1}`,
        nodeName: `三级${i + 1}-${j + 1}-${k + 1}`
      }))
    }))
  }))
}

// 扁平化节点和边数据
const nodes = []
const edges = []
// 层级数据
const levels = []

// 颜色混合函数
function mixColors(c1, c2, ratio) {
  const hexToRgb = hex => {
    let shorthand = /^#?([a-f\d])([a-f\d])([a-f\d])$/i
    hex = hex.replace(shorthand, (m, r, g, b) => r + r + g + g + b + b)
    let res = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return res ? { r: parseInt(res[1], 16), g: parseInt(res[2], 16), b: parseInt(res[3], 16) } : null
  }
  const rgbToHex = (r, g, b) =>
    '#' + [r, g, b].map(x => {
      const h = Math.round(x).toString(16)
      return h.length === 1 ? '0' + h : h
    }).join('')
  const a = hexToRgb(c1)
  const b = hexToRgb(c2)
  return rgbToHex(a.r * (1 - ratio) + b.r * ratio, a.g * (1 - ratio) + b.g * ratio, a.b * (1 - ratio) + b.b * ratio)
}

// 遍历树形数据，收集节点和边信息
function traverse(node, level = 0, parentId = null, branchColor = null, darkBranchColor = null) {
  if (!levels[level]) levels[level] = []
  levels[level].push(node)

  let currentColor = branchColor
  let currentDarkColor = darkBranchColor
  if (level === 1) {
    const branchIndex = levels[1].length - 1
    currentColor = branchColors[branchIndex % branchColors.length]
    currentDarkColor = mixColors(currentColor, '#000000', 0.3)
  }

  nodes.push({
    id: node.id,
    label: node.label,
    color: currentColor,
    level: level,
    hasChildren: !!node.children?.length,
  })

  if (parentId) {
    edges.push({
      source: parentId,
      target: node.id,
      id: `${parentId}-${node.id}`,
      style: {
        stroke: currentDarkColor,
        lineWidth: 2,
        opacity: level === 1 ? 0.8 : 0.7
      }
    })
  }

  node.children?.forEach(child => traverse(child, level + 1, node.id, currentColor, currentDarkColor))
}

function transformRingData(data) {
  if (!Array.isArray(data) || data.length === 0) return []
  const nodeMap = new Map()
  const rootNodes = []
  data.forEach(item => {
    nodeMap.set(item.nodeId, {
      id: item.nodeId,
      label: item.nodeName,
      level: item.level,
      parentId: item.parentId,
      isLeaf: item.isLeaf,
      children: []
    })
  })
  data.forEach(item => {
    const node = nodeMap.get(item.nodeId)
    if (item.parentId && nodeMap.has(item.parentId)) {
      nodeMap.get(item.parentId).children.push(node)
    } else {
      rootNodes.push(node)
    }
  })
  return rootNodes
}

// 根据层级更新可见节点集合
function updateVisibleSetByLevel(level) {
  const newSet = new Set()
  const maxLevel = level === 99 ? levels.length - 1 : level
  levels.forEach((arr, idx) => {
    if (idx <= maxLevel) arr.forEach(n => newSet.add(n.id))
  })
  visibleNodeIds.value = newSet
}

// 为节点分配环形布局
function assignCircularLayout(centerX, centerY) {
  const step = 90
  levels.forEach((levelNodes, i) => {
    const radius = step * i
    const total = levelNodes.length
    levelNodes.forEach((node, j) => {
      const angle = (2 * Math.PI * j) / total
      const x = centerX + radius * Math.cos(angle)
      const y = centerY + radius * Math.sin(angle)
      const target = nodes.find(n => n.id === node.id)
      target.x = x
      target.y = y

      const c = target.color || '#ccc'
      if (i === 0) {
        target.size = 68
        target.style = { fill: '#5A4B8A', stroke: '#5A4B8A' }
        target.labelCfg = { style: { fill: '#fff', fontWeight: 'bold', fontSize: 15 } }
      } else if (i === 1) {
        target.type = 'hexagon'
        target.size = [44, 44]
        target.style = {
          fill: c,
          stroke: mixColors(c, '#000000', 0.3),
          lineWidth: 2,
          radius: 8
        }
        target.labelCfg = { style: { fill: '#000', fontWeight: 'bold', fontSize: 13 } }
      } else {
        target.type = 'circle'
        target.size = 40 - i * 5
        target.style = {
          fill: mixColors(c, '#ffffff', (i - 1) / 4),
          stroke: c,
          lineWidth: 2
        }
        target.labelCfg = { style: { fill: mixColors(c, '#000', 0.4), fontSize: 14 - i } }
      }
    })
  })
}

// 获取可见图表数据
function getVisibleGraphData() {
  const visibleNodes = nodes.filter(n => visibleNodeIds.value.has(n.id))
  const visibleEdges = edges.filter(
    e => visibleNodeIds.value.has(e.source) && visibleNodeIds.value.has(e.target)
  )
  return { nodes: visibleNodes, edges: visibleEdges }
}

// 更新布局和数据
function updateLayoutAndData() {
  if (!ringGraphContainer.value) return
  const cx = ringGraphContainer.value.clientWidth / 2
  const cy = ringGraphContainer.value.clientHeight / 2 + offsetY
  assignCircularLayout(cx, cy)
  updateGraphData()
}

// 更新图表数据
function updateGraphData() {
  if (!graph) return
  const { nodes: filteredNodes, edges: filteredEdges } = getVisibleGraphData()
  graph.changeData({ nodes: filteredNodes, edges: filteredEdges })
}

// 选择层级
function selectLevel(level) {
  showLevelMenu.value = false
  currentLevel.value = level
  if (!graph) return
  // 只显示到指定层级
  graph.getNodes().forEach(node => {
    const model = node.getModel()
    if (typeof model.level === 'number' && model.level > level && level !== 99) {
      graph.hideItem(node)
    } else {
      graph.showItem(node)
    }
  })
  graph.getEdges().forEach(edge => {
    const model = edge.getModel()
    const source = graph.findById(model.source)
    const target = graph.findById(model.target)
    if (source && target && !graph.isItemVisible(source)) {
      graph.hideItem(edge)
    } else if (source && target && !graph.isItemVisible(target)) {
      graph.hideItem(edge)
    } else {
      graph.showItem(edge)
    }
  })
}

// 处理窗口大小变化
function handleResize() {
  if (graph && ringGraphContainer.value) {
    graph.changeSize(ringGraphContainer.value.clientWidth, ringGraphContainer.value.clientHeight)
  }
}

// 高亮节点路径
function highlightPath(nodeId) {
  if (!graph) return
  graph.getNodes().forEach(node => graph.setItemState(node, 'dimmed', true))
  graph.getEdges().forEach(edge => graph.setItemState(edge, 'dimmed', true))
  if (nodeId === 'center') {
    graph.getNodes().forEach(node => graph.clearItemStates(node, 'dimmed'))
    graph.getEdges().forEach(edge => graph.clearItemStates(edge, 'dimmed'))
    return
  }
  const nodesToHighlight = new Set()
  const edgesToHighlight = new Set()
  let currentId = nodeId
  while (currentId && currentId !== 'center') {
    nodesToHighlight.add(currentId)
    const edge = graph.getEdges().find(e => e.getModel().target === currentId)
    if (edge) {
      edgesToHighlight.add(edge.getID())
      currentId = edge.getModel().source
    } else {
      break
    }
  }
  nodesToHighlight.add('center')
  const findDescendants = (id) => {
    graph.getEdges().forEach(edge => {
      const model = edge.getModel()
      if (model.source === id) {
        nodesToHighlight.add(model.target)
        edgesToHighlight.add(edge.getID())
        findDescendants(model.target)
      }
    })
  }
  findDescendants(nodeId)
  nodesToHighlight.forEach(id => {
    const item = graph.findById(id)
    if (item) graph.clearItemStates(item, 'dimmed')
  })
  edgesToHighlight.forEach(id => {
    const item = graph.findById(id)
    if (item) graph.clearItemStates(item, 'dimmed')
  })
}

// 判断节点是否可展开
const isExpandable = computed(() => {
  if (!contextMenu.value.node) return false
  const { node } = contextMenu.value
  if (!node.children || node.children.length === 0) return false
  return true
})

// 查看节点详情
function viewNodeDetails() {
  if (contextMenu.value.node) {
    emit('node-click', contextMenu.value.node)
  }
  contextMenu.value.visible = false
  cancelHideMenuTimer()
}

// 切换节点子树展开/收起
function toggleNodeChildren(evt) {
  const { item } = evt
  const node = item.getModel()

  if (!node.hasChildren) return

  const childrenEdges = edges.filter(e => e.source === node.id)
  if (childrenEdges.length === 0) return

  const areChildrenVisible = childrenEdges.some(edge => visibleNodeIds.value.has(edge.target))

  if (areChildrenVisible) {
    // 收起子树
    const nodesToHide = new Set()
    const findDescendants = (parentId) => {
      const descendantsEdges = edges.filter(e => e.source === parentId)
      descendantsEdges.forEach(edge => {
        if (visibleNodeIds.value.has(edge.target)) {
          nodesToHide.add(edge.target)
          findDescendants(edge.target)
        }
      })
    }
    findDescendants(node.id)
    nodesToHide.forEach(id => visibleNodeIds.value.delete(id))
  } else {
    // 展开子树
    childrenEdges.forEach(edge => visibleNodeIds.value.add(edge.target))
  }

  visibleNodeIds.value = new Set(visibleNodeIds.value) // 触发响应式更新
  updateGraphData()
}

// 显示右键菜单
function showContextMenu(evt) {
  cancelHideMenuTimer()
  const node = evt.item.getModel()
  contextMenu.value.node = node
  contextMenu.value.x = evt.canvasX + 10
  contextMenu.value.y = evt.canvasY + 10
  contextMenu.value.visible = true
  highlightPath(node.id)
}

// 开始隐藏菜单计时器
function startHideMenuTimer() {
  menuHideTimer = setTimeout(() => {
    contextMenu.value.visible = false
    if (graph) {
      graph.getNodes().forEach(n => graph.clearItemStates(n))
      graph.getEdges().forEach(e => graph.clearItemStates(e))
    }
  }, 300)
}

// 取消隐藏菜单计时器
function cancelHideMenuTimer() {
  if (menuHideTimer) {
    clearTimeout(menuHideTimer)
    menuHideTimer = null
  }
}

// 组件挂载时初始化，集成接口数据加载
onMounted(async () => {
  window.addEventListener('resize', handleResize)
  let usedTreeData = treeData
  let flatData = []
  try {
    const response = await getRingView(getGraphId(), props.maxLevel)
    if (response.code === 200 && Array.isArray(response.result) && response.result.length > 0) {
      usedTreeData = flatListToTree(response.result)
      flatData = response.result
    } else {
      flatData = []
    }
  } catch (e) {
    flatData = []
  }
  // 1. 用 dynamicRadial 计算节点坐标
  computeWeight(usedTreeData)
  assignAngles(usedTreeData, 0, 2 * Math.PI)
  assignPositions(usedTreeData, 0, 120)
  // 2. 扁平化节点并分配样式
  const branchColorMap = {} // 一级节点id -> color
  let branchIdx = 0
  function styleTraverse(node, level = 0, parentId = null, branchColor = null, darkBranchColor = null) {
    let currentColor = branchColor
    let currentDarkColor = darkBranchColor
    if (level === 1) {
      currentColor = branchColors[branchIdx % branchColors.length]
      branchColorMap[node.nodeId] = currentColor
      currentDarkColor = mixColors(currentColor, '#000000', 0.3)
      branchIdx++
    } else if (level > 1 && parentId && branchColorMap[parentId]) {
      currentColor = branchColorMap[parentId]
      currentDarkColor = mixColors(currentColor, '#000000', 0.3)
    }
    let nodeType = 'circle', nodeSize = 30, nodeStyle = {}, labelCfg = {}
    if (level === 0) {
      nodeSize = 68
      nodeStyle = { fill: '#5A4B8A', stroke: '#5A4B8A' }
      labelCfg = { style: { fill: '#fff', fontWeight: 'bold', fontSize: 15 } }
    } else if (level === 1) {
      nodeType = 'hexagon'
      nodeSize = [44, 44]
      nodeStyle = {
        fill: currentColor,
        stroke: currentDarkColor,
        lineWidth: 2,
        radius: 8
      }
      labelCfg = { style: { fill: '#000', fontWeight: 'bold', fontSize: 13 } }
    } else {
      nodeType = 'circle'
      nodeSize = 40 - level * 5
      nodeStyle = {
        fill: mixColors(currentColor || '#ccc', '#ffffff', (level - 1) / 4),
        stroke: currentColor || '#ccc',
        lineWidth: 2
      }
      labelCfg = { style: { fill: mixColors(currentColor || '#ccc', '#000', 0.4), fontSize: 14 - level } }
    }
    nodes.push({
      ...node,
      id: node.nodeId,
      label: node.nodeName,
      level,
      type: nodeType,
      size: nodeSize,
      style: nodeStyle,
      labelCfg,
      color: currentColor
    })
    if (node.children) node.children.forEach(child => styleTraverse(child, level + 1, node.nodeId, currentColor, currentDarkColor))
  }
  styleTraverse(usedTreeData)
  // === 根节点居中 ===
  if (nodes.length) {
    const cx = ringGraphContainer.value.clientWidth / 2;
    const cy = ringGraphContainer.value.clientHeight / 2;
    const rootX = nodes[0].x;
    const rootY = nodes[0].y;
    const dx = cx - rootX;
    const dy = cy - rootY;
    nodes.forEach(n => {
      n.x += dx;
      n.y += dy;
    })
  }
  // 3. 生成 edges
  const edges = flatData.length ? flatListToEdges(flatData, 'nodeId', 'parentId') : []
  if (!edges.length) {
    // 静态数据兜底
    nodes.forEach(n => {
      if (n.children && n.children.length) {
        n.children.forEach(child => {
          edges.push({ source: n.nodeId, target: child.nodeId })
        })
      }
    })
  }
  // 4. 用 G6.Graph 渲染
  graph = new G6.Graph({
    container: ringGraphContainer.value,
    width: ringGraphContainer.value.clientWidth,
    height: ringGraphContainer.value.clientHeight,
    modes: { default: ['drag-canvas', 'zoom-canvas'] },
    defaultNode: { size: 30, style: { fill: '#9EC9FF', stroke: '#5B8FF9' } },
    defaultEdge: {
      type: 'line',
      style: {
        stroke: '#A3B1BF',
        lineWidth: 2,
        endArrow: false
      }
    },
    nodeStateStyles: {
      dimmed: {
        fill: '#e0e0e0',
        stroke: '#bdbdbd',
        opacity: 0.5
      }
    },
    edgeStateStyles: {
      dimmed: {
        stroke: '#e0e0e0',
        opacity: 0.4
      }
    }
  })
  graph.data({ nodes, edges })
  graph.render()
  // 右键菜单
  graph.on('node:click', showContextMenu)
  graph.on('canvas:click', () => { contextMenu.value.visible = false })
  graph.on('drag', () => { contextMenu.value.visible = false })
  graph.on('node:mouseenter', showContextMenu)
  graph.on('node:mouseleave', startHideMenuTimer)
  // 默认层级过滤
  selectLevel(currentLevel.value)
})

// 组件卸载前清理
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  if (graph) graph.destroy()
})
</script>

<style scoped>
.ring-graph-panel {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 500px;
}
.ring-graph-container {
  width: 100%;
  height: 100%;
  background: #f9f9f9;
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}
.level-control-box {
  position: absolute;
  right: 32px;
  bottom: 32px;
}
.level-control-btn {
  padding: 8px 18px;
  background: #8a6de3;
  color: #fff;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}
.level-menu {
  position: absolute;
  right: 0;
  bottom: 50px;
  width: 110px;
  background: #fff;
  border: 1px solid #eee;
  border-radius: 6px;
}
.level-menu-item {
  padding: 8px 18px;
  cursor: pointer;
}
.level-menu-item:hover {
  background: #f5f0ff;
  color: #8a6de3;
}
.context-menu {
  position: absolute;
  background-color: white;
  border: 1px solid #e2e2e2;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 6px;
  z-index: 20;
  transition: opacity 0.2s;
}
.menu-item {
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
}
.menu-item:hover {
  background-color: #f5f0ff;
  color: #8a6de3;
}
.menu-item.disabled {
  color: #aaa;
  cursor: not-allowed;
  background-color: #f8f8f8;
}
</style>