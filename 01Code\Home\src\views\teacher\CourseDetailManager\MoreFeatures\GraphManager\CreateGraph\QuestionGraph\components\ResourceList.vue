<template>
  <div class="resource-tree">
    <TreeItem
      v-for="item in items"
      :key="item.id"
      :item="item"
      :level="0"
      :selected="selected"
      @update:selected="val => $emit('update:selected', val)"
    />
  </div>
</template>

<script setup>
import TreeItem from './TreeItem.vue'

defineProps({
  items: Array,
  selected: Array
})

defineEmits(['update:selected'])
</script>

<style lang="scss" scoped>
.resource-tree {
  font-size: 14px;
  color: #333;
}
</style>
