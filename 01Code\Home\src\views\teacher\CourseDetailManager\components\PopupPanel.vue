<!-- src\views\teacher\CourseDetailManager\components\PopupPanel.vue -->
<template>
  <div v-if="isOpen" class="popup-panel" 
       :style="{ top: position.top + 'px', left: position.left + 'px' }"
       @click.away="handleOutsideClick">
    <div class="panel-content">
      <div class="panel-header">
        <h3 class="panel-title">{{ title }}</h3>
        <button @click="closePanel" class="close-btn">×</button>
      </div>
      <div class="panel-body">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { onOutsideClick } from '@/utils/outsideClick';

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '功能菜单'
  },
  position: {
    type: Object,
    default: () => ({ top: 0, left: 0 })
  }
});

const emit = defineEmits(['close', 'itemClick']);

// 点击外部关闭面板
let outsideClickHandler = null;

onMounted(() => {
  if (props.isOpen) {
    setupOutsideClick();
  }
});

const setupOutsideClick = () => {
  if (onOutsideClick) {
    outsideClickHandler = onOutsideClick(document.body, (event) => {
      if (props.isOpen && 
          !document.querySelector('.popup-panel').contains(event.target)) {
        closePanel();
      }
    });
  }
};

const closePanel = () => {
  emit('close');
};

const handleOutsideClick = (event) => {
  // 可以在这里添加额外的点击外部处理逻辑
  closePanel();
};

onBeforeUnmount(() => {
  if (outsideClickHandler && outsideClickHandler.off) {
    outsideClickHandler.off();
  }
});
</script>

<style lang="scss" scoped>
.popup-panel {
  position: absolute;
  width: 240px;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  padding: 0;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
  border: 1px solid #e5e6eb;
  overflow: hidden;
}

.panel-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e5e6eb;
}

.panel-title {
  color: #165DFF;
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.close-btn {
  background: none;
  border: none;
  color: #999;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.panel-body {
  padding: 8px 0;
  max-height: 400px;
  overflow-y: auto;
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}
</style>