@use '@/styles/variables' as *;

.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 3000;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: clamp(0.5rem, 1vw, 1rem) clamp(1rem, 2vw, 3rem);
  background: transparent;
  transition: all 0.3s ease;
  min-height: 50px;
  max-height: 70px;
  font-size: clamp(1rem, 1.2vw, 1.2rem);

  &-left {
    display: flex;
    align-items: center;
    gap: clamp(0.5rem, 1vw, 1rem);

    p {
      font-weight: bold; 
      margin: 0; 
      color: $linkhover-color;
      transition: color 0.3s ease;
      font-size: clamp(1rem, 1.5vw, 1.5rem);
    }
  }

  &-center {
    display: flex;
    align-items: center;
    gap: clamp(0.5rem, 1vw, 1.5rem);
    justify-content: center;
  }

  &-right {
    display: flex;
    align-items: center;
    gap: clamp(0.5rem, 1vw, 1.5rem);
    justify-content: flex-end;
  }

  .nav-link {
    margin: 0 clamp(0.25rem, 0.5vw, 0.75rem);
    text-decoration: none;
    color: $link-color;
    padding: clamp(0.25rem, 0.5vw, 0.5rem) clamp(0.5rem, 1vw, 1rem);
    border-radius: 4px;
    transition: color 0.3s ease;
    font-size: clamp(1rem, 1.2vw, 1.2rem);

    &:hover {
      color:white;
      
    }

    &.router-link-exact-active {
      color: white;
      font-weight: bold;
    }
  }

  .login-btn,
  .register-btn {
    padding: clamp(0.25rem, 0.5vw, 0.5rem) clamp(0.5rem, 1vw, 1rem);
    color: $link-color;
    border-radius: 4px;
    text-decoration: none;
    transition: color 0.3s ease;
    font-size: clamp(1rem, 1.2vw, 1.2rem);

    &:hover {
      color: $linkhover-color;
    }
    
  }

  /* 滚动时的样式 */
  &.scrolled {
    background: $secondary-color;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    
    .navbar-left p {
      color: $text-color;
    }
    
    .nav-link {
      color: $text-color;
      
      &:hover {
        color: $primary-color;
      }

      &.router-link-exact-active {
      color: $primary-color;
      font-weight: bold;
    }
    }

    .login-btn,
    .register-btn {
      color: $text-color;
      
      &:hover {
        color: $primary-color;
      }
    }

    .user-profile {
      .user-name {
        color: $text-color !important;
      }
      
      .user-role {
        color: $text-color !important;
      }
    }
  }

  /* 强制白色背景状态 */
  &.force-white,
  &.force-white.scrolled {
    background: white !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

    .navbar-left p,
    .nav-link,
    .login-btn,
    .register-btn,
    .user-profile .user-name {
      color: $text-color !important;
    }

    .nav-link {
      &:hover {
        color: $primary-color !important;
      }
      
      &.router-link-exact-active {
      color: $primary-color;
      font-weight: bold;
    }
    }

    .login-btn,
    .register-btn {
      &:hover {
        color: $primary-color !important;
      }
    }
  }

  /* 用户信息样式 */
  .user-profile {
    .user-name {
      color: $linkhover-color;
    }
    
    .user-role {
      color: $border-color;
    }
  }
}

/* 下拉菜单样式 */
.user-dropdown {
  position: relative;
  cursor: pointer;

  .user-profile {
    display: flex;
    align-items: center;
    gap: clamp(0.25rem, 0.5vw, 0.75rem);
    padding: clamp(0.25rem, 0.5vw, 0.5rem) clamp(0.5rem, 1vw, 1rem);
    border-radius: 4px;
    transition: background-color 0.3s;

    &:hover {
      background-color: $bg-gray-color;
    }

    .user-avatar {
      width: clamp(28px, 2.5vw, 36px);
      height: clamp(28px, 2.5vw, 36px);
      border-radius: 50%;
      object-fit: cover;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .user-info {
      display: flex;
      flex-direction: column;
      line-height: 1.2;

      .user-name {
        font-size: clamp(0.75rem, 1vw, 0.875rem);
        font-weight: 500;
        transition: color 0.3s ease;
      }

      .user-role {
        font-size: clamp(0.625rem, 0.8vw, 0.75rem);
        transition: color 0.3s ease;
      }
    }
  }

  .dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    min-width: clamp(140px, 10vw, 180px);
    background: $secondary-color;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    overflow: hidden;

    .dropdown-item {
      display: flex;
      align-items: center;
      padding: clamp(0.375rem, 0.5vw, 0.5rem) clamp(0.75rem, 1vw, 1rem);
      color: $text-color;
      text-decoration: none;
      transition: all 0.3s ease;
      background: none;
      border: none;
      width: 100%;
      text-align: left;
      cursor: pointer;
      font: inherit;
      font-size: clamp(0.75rem, 1vw, 0.875rem);

      &:hover {
        background-color: $hover-bg;
        color: $primary-color;
      }
    }

    .dropdown-divider {
      height: 1px;
      background-color: $border-color;
      margin: 4px 0;
    }
  }
}

/* 过渡动画 */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.2s ease;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 响应式断点 */
@media (max-width: 768px) {
  .navbar {
    padding: 0.5rem 1rem;
    font-size: 14px;
    
    &-center {
      gap: 0.5rem;
    }
    
    &-left p {
      font-size: 1rem;
    }
  }
  
  .nav-link {
    font-size: 0.875rem;
    padding: 0.25rem 0.5rem;
  }
  
  .user-dropdown .user-profile .user-avatar {
    width: 28px;
    height: 28px;
  }
}