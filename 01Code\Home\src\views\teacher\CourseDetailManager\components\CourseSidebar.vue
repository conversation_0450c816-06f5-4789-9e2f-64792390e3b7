<!--src\views\teacher\CourseDetailManager\components\CourseSidebar.vue-->
<template>
  <div class="sidebar">
    <div class="sidebar-header">
      <div class="header-image">
        <img :src="courseData.cover" :alt="courseData.name">
        <span class="header-overlay-text" @click="goToCourseDetail">课程主页</span>
      </div>
      <div class="header-content">
        <div class="header-text">
          <h2 class="course-title">{{ courseData.name }}</h2>
        </div>
        <div class="header-code" v-if="courseData.invitationCode">
          <span class="code-text">邀请码:</span>
          <span class="code-value">{{ courseData.invitationCode }}</span>
          <button class="copy-btn" @click.stop="copyCode">
            <span class="copy-icon">⎘</span>
            <span class="copy-text">复制</span>
            <span class="copy-tooltip" :class="{ 'show': showCopyTooltip }">已复制!</span>
          </button>
        </div>
      </div>
    </div>
    <ul class="sidebar-menu">
      <li ref="moreFeaturesItem" v-for="(menuItem, index) in menuItems" :key="index" class="menu-item"
        :class="{ active: activeIndex === index }" @click.prevent="handleMenuItemClick(index)"
        @mouseenter="handleMouseEnter(index)" @mouseleave="handleMouseLeave(index)">
        <div class="menu-icon-wrapper">
          <img v-if="menuItem.icon" :src="getIconUrl(menuItem, index)" :alt="menuItem.text" class="menu-icon"
            @error="handleIconError">
        </div>
        <span class="menu-text">{{ menuItem.text }}</span>
        <span v-if="menuItem.badge" class="badge">{{ menuItem.badge }}</span>
      </li>
    </ul>

    <!-- 新增功能面板 -->
    <div v-if="showMorePanel" class="more-features-panel" ref="moreFeaturesPanelRef" :style="panelPosition">
      <div class="features-grid">
        <div v-for="(feature, index) in features" :key="index" class="feature-item" @click="navigateToFeature(feature)"
          @mouseenter="handleFeatureMouseEnter(index)" @mouseleave="handleFeatureMouseLeave(index)"
          :class="{ active: featureActiveIndex === index }">
          <div class="feature-icon">
            <img :src="getFeatureIconUrl(feature, index)" :alt="feature.name" class="icon-img" @error="handleIconError">
          </div>
          <div class="feature-name">{{ feature.name }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { getCourseDetail } from '@/api/teacher/course';

const courseData = ref({
  name: '加载中...',
  cover: defaultCourseCover, // 默认封面图
  invitationCode: ''
});
const showCopyTooltip = ref(false);

const showCopySuccess = () => {
  showCopyTooltip.value = true;
  setTimeout(() => {
    showCopyTooltip.value = false;
  }, 2000);
};

const copyCode = () => {
  if (!courseData.value.invitationCode) return;

  try {
    // 现代浏览器 Clipboard API
    if (navigator.clipboard) {
      navigator.clipboard.writeText(courseData.value.invitationCode)
        .then(() => {
          showCopySuccess();
        })
        .catch(() => {
          fallbackCopy();
        });
    } else {
      // 旧浏览器回退方案
      fallbackCopy();
    }
  } catch (error) {
    fallbackCopy();
  }
};


const fallbackCopy = () => {
  const textarea = document.createElement('textarea');
  textarea.value = courseData.value.invitationCode;
  textarea.style.position = 'fixed';  // 避免滚动到页面底部
  document.body.appendChild(textarea);
  textarea.select();

  try {
    document.execCommand('copy');
    showCopySuccess();
  } catch (error) {
    console.error('复制失败:', error);
    alert('复制失败，请手动复制');
  } finally {
    document.body.removeChild(textarea);
  }
};

// 图标路径生成函数
const getIconPath = (iconName) => {
  return `/src/assets/img/Teacher/${iconName}.png`;
};

// 初始化菜单项，添加状态管理
const menuItems = ref([
  { text: '通知公告', routeName: 'Announcement', icon: 'icon-notice', hover: false },
  { text: '学习资源', routeName: 'LearningResources', icon: 'icon-resource', hover: false },
  { text: '学习任务', routeName: 'LearningTask', icon: 'icon-task', hover: false },
  { text: '作业/考试', routeName: 'AssignmentExamHome', icon: 'icon-work', hover: false },
  { text: '学生管理', routeName: 'StudentManagement', icon: 'icon-studentManager', hover: false },
  { text: '更多功能', routeName: 'MoreFeatures', badge: 6, icon: 'icon-more', hover: false },
])

// 初始化更多功能面板项目
const features = ref([
  { name: '图谱管理', icon: 'icon-kg', routeName: 'GraphManager', hover: false },
  { name: '学习路径', icon: 'icon-kg', routeName: 'LearningPath', hover: false },
  { name: '课程统计', icon: 'icon-statistics', routeName: 'CourseStatistics', hover: false },
  { name: '成绩管理', icon: 'icon-grade', routeName: 'GradeManagement', hover: false },
  { name: '课程管理', icon: 'icon-course', routeName: 'CourseManagement', hover: false },
  { name: '问答讨论', icon: 'icon-Q&A', routeName: 'Q-A', hover: false },
  { name: '考勤管理', icon: 'icon-Q&A', routeName: 'AttendanceManagement', hover: false },
]);

// 根据当前路由设置激活项
const activeIndex = ref(0)
const featureActiveIndex = ref(-1); // 功能面板选中项索引
const router = useRouter();
const route = useRoute();
const courseId = route.params.courseId;

const showMorePanel = ref(false);
const moreFeaturesPanelRef = ref(null);
const moreFeaturesItem = ref(null);
const panelPosition = ref({ top: '490px', left: '240px' });

// 获取菜单项图标URL
const getIconUrl = (menuItem, index) => {
  const isActive = activeIndex.value === index;
  const isHover = menuItem.hover;
  const iconState = isActive || isHover ? 'actived' : 'normal';
  return getIconPath(`${menuItem.icon}-${iconState}`);
};

// 获取功能面板图标URL
const getFeatureIconUrl = (feature, index) => {
  const isActive = featureActiveIndex.value === index;
  const isHover = feature.hover;
  const iconState = isActive || isHover ? 'actived' : 'normal';
  return getIconPath(`${feature.icon}-${iconState}`);
};

// 根据当前路由设置激活状态
const setActiveStateFromRoute = () => {
  const currentRouteName = route.name
  
  // 处理问答讨论和话题详情
  if (currentRouteName === 'Q-A' || currentRouteName === 'DiscussDetail') {
    const moreFeaturesIndex = menuItems.value.findIndex(item => item.routeName === 'MoreFeatures')
    if (moreFeaturesIndex !== -1) {
      activeIndex.value = moreFeaturesIndex
      // 设置问答讨论为激活状态
      featureActiveIndex.value = features.value.findIndex(f => f.routeName === 'Q-A')
    }
    return
  }
  
  // 处理其他更多功能子项
  const featureIndex = features.value.findIndex(f => f.routeName === currentRouteName)
  if (featureIndex !== -1) {
    const moreFeaturesIndex = menuItems.value.findIndex(item => item.routeName === 'MoreFeatures')
    if (moreFeaturesIndex !== -1) {
      activeIndex.value = moreFeaturesIndex
      featureActiveIndex.value = featureIndex
    }
    return
  }
  
  // 处理主菜单项
  const menuIndex = menuItems.value.findIndex(item => item.routeName === currentRouteName)
  if (menuIndex !== -1) {
    activeIndex.value = menuIndex
    featureActiveIndex.value = -1
    showMorePanel.value = false
  }
}

const goToCourseDetail = () => {
  router.push({
    name: 'CourseDetail',
    params: { courseId }
  });
};

const handleMenuItemClick = (index) => {
  if (showMorePanel.value) {
    showMorePanel.value = false;

    if (index !== 5) {
      activeIndex.value = index;
      const routeName = menuItems.value[index].routeName;
      router.push({
        name: routeName,
        params: { courseId }
      });
    }
    return;
  }

  activeIndex.value = index;
  const routeName = menuItems.value[index].routeName;

  if (index === 5) {
    showMorePanel.value = true;
  } else {
    router.push({
      name: routeName,
      params: { courseId }
    });
  }
};

const navigateToFeature = (feature, index) => {
  featureActiveIndex.value = index; // 记录选中的功能项
  showMorePanel.value = false;
  router.push({
    name: feature.routeName,
    params: { courseId }
  });
};

// 处理菜单项鼠标进入
const handleMouseEnter = (index) => {
  menuItems.value[index].hover = true;
};

// 处理菜单项鼠标离开
const handleMouseLeave = (index) => {
  menuItems.value[index].hover = false;
};

// 处理功能面板项鼠标进入
const handleFeatureMouseEnter = (index) => {
  features.value[index].hover = true;
};

// 处理功能面板项鼠标离开
const handleFeatureMouseLeave = (index) => {
  features.value[index].hover = false;
};

// 获取课程详情
const fetchCourseDetail = async () => {
  try {
    const response = await getCourseDetail(courseId);

    if (response?.course) {
      courseData.value = {
        ...courseData.value,
        name: response.course.name || '未命名课程',
        cover: response.course.courseCover || defaultCourseCover,
        invitationCode: response.course.invitationCode || ''
      };
    }
  } catch (error) {
    console.error('获取课程详情失败:', error);
  }
};

watch(courseData, (newVal) => {
  console.log('courseData变化:', newVal);
}, { deep: true });

onMounted(() => {
  nextTick(() => {
    console.log('moreFeaturesItem DOM:', moreFeaturesItem.value);
    document.addEventListener('click', handleOutsideClick);
  });
  fetchCourseDetail();
  setActiveStateFromRoute();
});

onUnmounted(() => {
  document.removeEventListener('click', handleOutsideClick);
});

watch(() => route.name, () => {
  setActiveStateFromRoute();
})

const handleOutsideClick = (event) => {
  const isPanelClicked = moreFeaturesPanelRef.value?.contains(event.target);
  // 修正：遍历 ref 数组检查是否包含目标元素
  const isMenuItemClicked = moreFeaturesItem.value?.some(el =>
    el?.contains(event.target)
  );

  if (showMorePanel.value && !isPanelClicked && !isMenuItemClicked) {
    showMorePanel.value = false;
  }
};
</script>

<style lang="scss" scoped>
@use '@/styles/variables';
// 定义变量

.sidebar {
  position: relative; /* 确保创建堆叠上下文 */
  z-index: 100; /* 高于内容区域 */
  width: 230px; /* 固定宽度 */
  background-color: #fff;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
  font-family: Arial, sans-serif;

  .sidebar-header {
    padding: 10px;
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;

    .header-image {
      position: relative;
      width: 180px;
      height: 120px;
      background-color: #af8989;
      border-radius: 4px;
      margin: 0 auto;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 4px;
      }
    }

    .header-overlay-text {
      position: absolute;
      top: 0;
      left: 0;
      color: $primary-color;
      font-size: 14px;
      cursor: pointer;
      z-index: 1;
      background-color: rgba(255, 255, 255, 0.8);
      padding: 2px 6px;
      border-radius: 0 0 3px 0;
    }

    .header-text {
      display: flex;
      flex-direction: column;
      margin-left: 10px;
      margin: 0 auto;

      .course-title {
        font-size: 14px;
        color: #333;
        margin: 0;
        font-weight: 500;
      }
    }

    .header-content {
      width: 100%;
      text-align: center;
      margin-top: 10px;
    }

    .header-code {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 8px;
      font-size: 14px;
      color: #666;

      .code-text {
        margin-right: 5px;
      }

      .code-value {
        font-family: monospace;
        font-weight: bold;
        color: variables.$primary-color;
        margin-right: 10px;
      }

      .copy-btn {
        position: relative;
        display: flex;
        align-items: center;
        background: #f5f5f5;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 2px 8px;
        cursor: pointer;
        transition: all 0.2s;
        font-size: 12px;

        &:hover {
          background: #eaeaea;
          border-color: #ccc;
        }

        .copy-icon {
          margin-right: 4px;
          font-size: 14px;
        }

        .copy-tooltip {
          position: absolute;
          top: -30px;
          left: 50%;
          transform: translateX(-50%);
          background: rgba(0, 0, 0, 0.7);
          color: white;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          opacity: 0;
          transition: opacity 0.3s;
          pointer-events: none;
          white-space: nowrap;

          &.show {
            opacity: 1;
          }

          &::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            border-width: 5px 5px 0;
            border-style: solid;
            border-color: rgba(0, 0, 0, 0.7) transparent transparent;
          }
        }
      }
    }
  }

  .sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;

    .menu-item {
      padding: 20px 40px 20px 50px;
      cursor: pointer;
      display: flex;
      align-items: center;
      position: relative;
      transition: background-color 0.2s;

      .menu-icon-wrapper {
        width: 24px;
        height: 24px;
        margin-right: 16px;
        display: flex;
        align-items: center;
        justify-content: center;

        .menu-icon {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }

      .menu-text {
        flex-grow: 1;
        display: inline-block;
        color: #333;
        font-size: 16px;
      }

      .badge {
        margin-left: 8px;
        background-color: $primary-color;
        color: #fff;
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 12px;
        line-height: 1;
      }

      // 鼠标悬停状态
      &:hover {
        background-color: $course-tabs;

        .menu-text {
          color: $primary-color;
        }
      }

      // 激活状态
      &.active {
        background-color: $course-tabs;
        border-left: 3px solid $primary-color;

        .menu-text {
          color: $primary-color;
          font-weight: 500;
        }
      }
    }
  }
}

.more-features-panel {
  position: fixed;
  z-index: 9999;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 16px;
  min-width: 300px;
  transform: translateY(10px);
  animation: fadeIn 0.2s ease-out;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent; // 初始透明边框

  // 鼠标悬停和激活状态
  &:hover,
  &.active {
    border-color: $primary-color; // 边框颜色变为主色

    .feature-name {
      color: $primary-color; // 文字颜色变为主色
    }
  }
}

.feature-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;

  .icon-img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.feature-name {
  font-size: 14px;
  color: #333;
  text-align: center;
  white-space: nowrap;
}
</style>