<template>
    <div class="answer-detail-page">
        <h2 class="title">{{ title }}（{{ isExam ? '考试' : '作业' }}）</h2>

        <div class="score-summary" v-if="totalScore !== null">
            总得分：<span class="score">{{ totalScore }} 分</span>
        </div>

        <el-divider />

        <div v-for="(q, idx) in allQuestions" :key="q.id" class="question-block">
            <h3>第 {{ idx + 1 }} 题（{{ typeMap[q.type] }}）</h3>
            <div class="question-content" v-html="q.content" />

            <!-- 选项类题目 -->
            <div v-if="[0, 1, 2].includes(q.type)" class="options-area">
                <div v-for="opt in parseOptions(q.options)" :key="opt" :class="[
                    'option',
                    studentAnswer(q).includes(opt) ? 'selected' : '',
                    correctAnswer(q).includes(opt) ? 'correct' : '',
                    studentAnswer(q).includes(opt) && !correctAnswer(q).includes(opt)
                        ? 'wrong'
                        : '',
                ]">
                    {{ opt }}
                </div>
            </div>

            <!-- 填空/简答 -->
            <div v-if="[3, 4].includes(q.type)" class="text-answer">
                <div>我的答案：{{ studentAnswer(q) || '未作答' }}</div>
                <div>参考答案：{{ correctAnswer(q).join(' / ') }}</div>
            </div>

            <div class="score-line">得分：{{ q.correctScore ?? '未评分' }} 分</div>
        </div>
    </div>
</template>
  
<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { getSubmitDetail } from '@/api/teacher/correction'

const route = useRoute()

const id = route.query.id
const isExam = route.query.isExam === 'true'
const title = route.query.title || ''

const allQuestions = ref([])

const typeMap = {
    0: '单选题',
    1: '多选题',
    2: '判断题',
    3: '填空题',
    4: '简答题',
}

const parseOptions = (str) => {
    try {
        return JSON.parse(str || '[]')
    } catch {
        return []
    }
}

const studentAnswer = (q) => {
    try {
        if (q.studentAnswer === null || q.studentAnswer === undefined) return ''
        return Array.isArray(q.studentAnswer)
            ? q.studentAnswer
            : JSON.parse(q.studentAnswer || '[]')
    } catch {
        return []
    }
}

const correctAnswer = (q) => {
    try {
        return Array.isArray(q.correctAnswer)
            ? q.correctAnswer
            : JSON.parse(q.correctAnswer || '[]')
    } catch {
        return []
    }
}

const totalScore = computed(() => {
    if (!allQuestions.value.length) return null
    return allQuestions.value.reduce((sum, q) => {
        return sum + (Number(q.correctScore ?? 0))
    }, 0)
})

const fetchData = async () => {
    try {
        const res = await getSubmitDetail({ submitRecordId: id, isExam })
        const data = res.result || {}
        const merged = [
            ...(data.singleChoiceList || []),
            ...(data.multipleChoiceList || []),
            ...(data.trueFalseList || []),
            ...(data.fillInBlankList || []),
            ...(data.shortAnswerList || []),
        ]
        allQuestions.value = merged
    } catch (err) {
        console.error('加载试卷失败', err)
    }
}

onMounted(() => {
    fetchData()
})
</script>
  
<style scoped>
.answer-detail-page {
    padding: 30px;
    background-color: #fff;
    min-height: 90vh;
}

.title {
    font-size: 22px;
    font-weight: bold;
    margin-bottom: 12px;
}

.score-summary {
    font-size: 18px;
    margin-bottom: 10px;
    color: #409eff;
}

.score {
    font-weight: bold;
    font-size: 20px;
}

.question-block {
    border: 1px solid #eee;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 20px;
}

.question-content {
    margin-bottom: 10px;
}

.options-area {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.option {
    padding: 6px 12px;
    border-radius: 4px;
    background-color: #f6f6f6;
    border: 1px solid #ccc;
}

.option.selected {
    background-color: #d1eaff;
}

.option.correct {
    border-color: #67c23a;
}

.option.wrong {
    border-color: #f56c6c;
}

.text-answer {
    font-size: 14px;
    line-height: 1.8;
}

.score-line {
    margin-top: 8px;
    font-weight: bold;
    color: #409eff;
}
</style>
  