<template>
  <div class="theme-model">
    <!-- Main header with tab name and total resources -->
    <div class="module-header">
      <h2>{{ currentTab }}</h2>
      <div class="total-resources">
        <span v-if="currentTab === '追根溯源识茶貌'">
          <div class="resource-group">
            <div v-for="(res, index) in parseResources('4个视频 4个教材 25个题目 4个PPT 7个外部资源')" :key="index"
              class="resource-item">
              <div class="resource-count">{{ res.count }}</div>
              <div class="resource-type">{{ res.type }}</div>
            </div>
          </div>
        </span>
        <span v-if="currentTab === '千载流芳知茶史'">
          <div class="resource-group">
            <div v-for="(res, index) in parseResources('8个视频 7个教材 33个题目 7个PPT 8个外部资源')" :key="index"
              class="resource-item">
              <div class="resource-count">{{ res.count }}</div>
              <div class="resource-type">{{ res.type }}</div>
            </div>
          </div>
        </span>
      </div>
    </div>

    <!-- Knowledge units container -->
    <div class="knowledge-units">
      <!-- Unit 1: 源自中国的茶树之本 -->
      <div class="knowledge-unit" v-if="currentTab === '追根溯源识茶貌'">
        <h3 class="unit-title">源自中国的茶树之本</h3>
        <div class="unit-resources">
          <div class="resource-group">
            <div v-for="(res, index) in parseResources('1个视频 1个教材 10个题目 1个PPT 4个外部资源')" :key="index"
              class="resource-item">
              <div class="resource-count">{{ res.count }}</div>
              <div class="resource-type">{{ res.type }}</div>
            </div>
          </div>
        </div>

        <div class="knowledge-items">
          <div class="knowledge-item">
            <div class="item-info">
              <span class="item-name">发乎神农的中国饮茶</span>
              <span class="item-tag" v-if="false"></span> <!-- Empty for now -->
            </div>
            <div class="item-resources">
              <div class="resource-group">
                <div v-for="(res, index) in parseResources('1个视频 1个教材 3个题目 1个PPT 2个外部资源')" :key="index"
                  class="resource-item">
                  <div class="resource-count">{{ res.count }}</div>
                  <div class="resource-type">{{ res.type }}</div>
                </div>
              </div>
            </div>
          </div>

          <div class="knowledge-item">
            <div class="item-info">
              <span class="item-name">茶为比屋之饮闻于天下</span>
            </div>
            <div class="item-resources">
              <div class="resource-group">
                <div v-for="(res, index) in parseResources('1个视频 1个教材 6个题目 1个PPT 1个外部资源')" :key="index"
                  class="resource-item">
                  <div class="resource-count">{{ res.count }}</div>
                  <div class="resource-type">{{ res.type }}</div>
                </div>
              </div>
            </div>
          </div>

          <div class="knowledge-item">
            <div class="item-info">
              <span class="item-name">茶之为饮的发展变迁</span>
            </div>
            <div class="item-resources">
              <div class="resource-group">
                <div v-for="(res, index) in parseResources('1个视频 1个教材 6个题目 1个PPT 0个外部资源')" :key="index"
                  class="resource-item">
                  <div class="resource-count">{{ res.count }}</div>
                  <div class="resource-type">{{ res.type }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 注意：此处有重复的 knowledge-unit，请删除一个以避免布局问题 -->

      <!-- Unit 2: 茶文化解析 -->
      <div class="knowledge-unit" v-if="currentTab === '各具千秋的茶叶分类'">
        <h3 class="unit-title">茶文化解析</h3>
        <div class="unit-resources">
          <div class="resource-group">
            <div v-for="(res, index) in parseResources('2个视频 1个教材 5个题目 1个PPT 2个外部资源')" :key="index"
              class="resource-item">
              <div class="resource-count">{{ res.count }}</div>
              <div class="resource-type">{{ res.type }}</div>
            </div>
          </div>
        </div>

        <div class="knowledge-items">
          <div class="knowledge-item">
            <div class="item-info">
              <span class="item-name">概念性知识</span>
              <span class="item-tag">重点</span>
              <span class="item-tag">概述</span>
            </div>
            <div class="item-resources"></div>
          </div>

          <div class="knowledge-item">
            <div class="item-info">
              <span class="item-name">人类与茶最初的亲密接触</span>
            </div>
            <div class="item-resources">
              <div class="resource-group">
                <div v-for="(res, index) in parseResources('1个视频 1个教材 5个题目 2个PPT 0个外部资源')" :key="index"
                  class="resource-item">
                  <div class="resource-count">{{ res.count }}</div>
                  <div class="resource-type">{{ res.type }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  currentTab: {
    type: String,
    required: true
  }
})

// 解析资源字符串的方法
function parseResources(str) {
  const regex = /(\d+)个(\S+)/g;
  const result = [];
  let match;

  while ((match = regex.exec(str)) !== null) {
    result.push({
      count: match[1],
      type: match[2]
    });
  }

  return result;
}
</script>

<style scoped lang="scss">
.theme-model {
  color: #2c3e50;
  border-radius: .83333vw;
  overflow: hidden;
  border: 1px solid #e9e9e9;

  .module-header {
    /*     display: flex;
    padding: 2.08333vw;
    justify-content: space-between;
    background: #efedff; */
    display: flex;
    padding: 2.08333vw;
    justify-content: space-between;
    align-items: center;
    /* 添加垂直居中 */
    background: #efedff;
    gap: 0;
    /* 移除容器间间隙 */

    h2 {
      margin: 0;
      font-size: 20px;
      padding-right: 0;
      /* 移除标题右侧内边距 */
    }

    .total-resources {
      color: #666;
      font-size: 14px;
      white-space: nowrap;

      .resource-group {
        display: flex;
        gap: 10px;
        white-space: nowrap;

        .resource-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;

          .resource-count {
            font-weight: bold;
            font-size: 14px;
          }

          .resource-type {
            font-size: 12px;
            color: #999;
          }
        }
      }
    }
  }

  .knowledge-units {
    padding: 20px;
    min-height: 300px;

    .knowledge-unit {
      margin-bottom: 30px;
      padding: 20px;

      .unit-title {
        padding: 2.08333
      }

      .unit-resources {
        color: #666;
        font-size: 14px;
        margin-bottom: 15px;
      }

      .resource-group {
        display: flex;
        gap: 10px;
        white-space: nowrap;
      }

      .resource-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
      }

      .resource-count {
        font-weight: bold;
        font-size: 14px;
      }

      .resource-type {
        font-size: 12px;
        color: #999;
      }

      .knowledge-items {
        border: 1px solid #eee;
        border-radius: 4px;

        .knowledge-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid #eee;
          padding: 20px 20px;

          &:last-child {
            border-bottom: none;
          }

          .item-info {
            .item-name {
              margin-right: 10px;
            }

            .item-tag {
              display: inline-block;
              padding: 2px 6px;
              background-color: #f0f0f0;
              border-radius: 4px;
              font-size: 12px;
              color: #666;
              margin-right: 5px;
            }
          }

          .item-resources {
            color: #666;
            font-size: 14px;
            white-space: nowrap;
          }
        }
      }
    }
  }
}
</style>