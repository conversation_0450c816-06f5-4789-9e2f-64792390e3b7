<!--src\views\teacher\TeachClass\MyClass\index.vue-->
<template>
  <div>
    <TaskFilterBar @filter-change="handleFilterChange" />
    <CourseList :filter-params="filterParams" />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import TaskFilterBar from '../components/TaskFilterBar.vue';
import CourseList from './components/CourseList.vue';

const filterParams = ref({
  category: 0,
  status: 0,
  query: ''
});

const handleFilterChange = (params) => {
  filterParams.value = {
    category: params.category,
    status: params.status,
    query: params.query
  };
};
</script>