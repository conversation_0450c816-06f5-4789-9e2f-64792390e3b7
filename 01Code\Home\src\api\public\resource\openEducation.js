import api from '@/api/service.js'

/**
 * 获取开放教育资源列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页码，默认1
 * @param {number} params.pageSize - 每页数量，默认10
 * @param {string} params.name - 资源名称（可选）
 * @param {string} params.teacher - 教师（可选）
 * @param {string} params.school - 学校（可选）
 * @param {string} params.sortType - 排序类型[ascending]升序,[descending]降序（可选）
 * @param {string} params.sortColumn - 排序字段（可选）
 * @returns {Promise} API响应
 */
export const getOpenEducationList = (params = {}) => {
  // 默认参数
  const defaultParams = {
    pageNum: 1,
    pageSize: 10
  };
  
  // 过滤掉空值参数并合并默认参数
  const filteredParams = Object.fromEntries(
    Object.entries({
      ...defaultParams,
      ...params
    }).filter(([_, v]) => v !== '' && v !== undefined && v !== null)
  );
  
  return api({
    url: '/open/education/list',
    method: 'get',
    params: filteredParams
  });
}
