/*@/styles/auth/register.scss*/
@use 'sass:color';
@use '@/styles/variables.scss' as *;

.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(-45deg, 
    color.adjust($primary-color, $lightness: 40%), 
    color.adjust($bg-color, $lightness: 5%),
    color.adjust($primary-color, $lightness: 30%),
    color.adjust($bg-color, $lightness: 10%));
  background-size: 400% 400%;
  animation: gradientBG 15s ease infinite;
  
  .register-wrapper {
    display: flex;
    width: 100%;
    max-width: 900px;
    min-height: 550px;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    
    .register-card {
      flex: 1;
      max-width: 450px;
      background-color: rgba(255, 255, 255, 0.26);
      padding: 50px 40px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      
      .register-title {
        color: $text-color;
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 30px;
        text-align: center;
      }
      
      .register-form {
        .form-group {
          margin-bottom: 22px;
          
          .form-label {
            display: block;
            color: $text-color;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
          }
          
          .form-input-container {
            position: relative;
            
            .form-icon {
              position: absolute;
              left: 15px;
              top: 50%;
              transform: translateY(-50%);
              color: rgba($text-color, 0.5);
              font-size: 16px;
            }
            
            .form-input {
              width: 100%;
              padding: 12px 15px 12px 40px;
              border: 1px solid $border-color;
              border-radius: 8px;
              font-size: 14px;
              color: $text-color;
              transition: all 0.2s ease;
              
              &:focus {
                outline: none;
                border-color: $primary-color;
                box-shadow: 0 0 0 3px rgba($primary-color, 0.15);
              }
              
              &::placeholder {
                color: rgba($text-color, 0.4);
              }
            }
            
            select.form-input {
              appearance: none;
              background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%239CA3AF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
              background-repeat: no-repeat;
              background-position: right 15px center;
              background-size: 16px;
            }
          }
        }
        
        .register-button {
          width: 100%;
          margin-top: 10px;
          cursor: pointer;
          background-color: $primary-color;
          border: none;
          color: white;
          font-size: 16px;
          font-weight: 500;
          padding: 14px;
          border-radius: 8px;
          transition: all 0.3s ease;
          box-shadow: 0 4px 6px rgba($primary-color, 0.2);
          
          &:hover:not(:disabled) {
            background-color: color.adjust($primary-color, $lightness: -5%);
            box-shadow: 0 6px 8px rgba($primary-color, 0.3);
            transform: translateY(-2px);
          }
          
          &:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none !important;
          }
        }
        
        .success-message {
          color: $primary-color;
          text-align: center;
          margin-top: 15px;
          font-size: 14px;
        }
        
        .login-link {
          text-align: center;
          margin-top: 20px;
          color: rgba($text-color, 0.7);
          font-size: 14px;
          
          a {
            color: $primary-color;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s ease;
            
            &:hover {
              text-decoration: underline;
              color: color.adjust($primary-color, $lightness: -10%);
            }
          }
        }
      }
    }
    
    .register-image {
      flex: 1;
      position: relative;
      background-color: #bbdefb;
      
      .side-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(to bottom, rgba(33, 150, 243, 0.2), rgba(13, 71, 161, 0.7));
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 40px;
        color: white;
        text-align: center;
        
        .image-title {
          font-size: 28px;
          font-weight: 600;
          margin-bottom: 15px;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .image-subtitle {
          font-size: 16px;
          opacity: 0.9;
          max-width: 300px;
          line-height: 1.6;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }
      }
    }
  }
}

@keyframes gradientBG {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .register-container {
    .register-wrapper {
      flex-direction: column-reverse;
      max-width: 450px;
      min-height: auto;
      
      .register-image {
        display: none;
      }
      
      .register-card {
        border-radius: 16px;
        padding: 40px 30px;
      }
    }
  }
}

@media (max-width: 480px) {
  .register-container {
    padding: 15px;
    
    .register-wrapper {
      .register-card {
        padding: 30px 20px;
        
        .register-title {
          font-size: 24px;
          margin-bottom: 20px;
        }
      }
    }
  }
}