<template>
  <div class="ability-detail-page">
    <!-- 返回栏 -->
    <div class="top-back-bar">
      <span class="back-icon" @click="goBack">&#8592;</span>
      <span class="back-text">能力图谱</span>
      <span class="slash">/</span>
      <span class="current-title">能力详情</span>
    </div>
    <!-- 左侧导航栏 -->
    <div class="left-sidebar">
      <div class="sidebar-section">
        <h3 class="sidebar-title">能力目标图谱</h3>
        <div class="mini-chart-container">
          <!-- 加载状态 -->
          <div v-if="abilityStore.isLoading" class="loading-state">
            <div class="loading-spinner"></div>
            <p>加载中...</p>
          </div>
          <!-- 空数据状态 -->
          <div v-else-if="!chartData.length" class="empty-state">
            <p>暂无主能力数据</p>
            <p style="font-size: 0.9rem; color: #999;">调试信息: mainAbilities.length = {{ abilityStore.mainAbilities.length }}</p>
          </div>
          <!-- 图表 -->
          <v-chart v-else ref="echartsRef" :option="option" style="width: 100%; height: 200px;" />
        </div>
      </div>
      <div class="sidebar-section">
        <h3 class="sidebar-title">目录导航:</h3>
        <div class="ability-dropdown" @click="dropdownOpen = !dropdownOpen" tabindex="0" @blur="dropdownOpen = false" style="position:relative;">
          <div class="custom-select-box" :class="{ open: dropdownOpen }">
            <span class="selected-label">{{ optionList.find(opt => opt.value === selectedMainAbility)?.label }}</span>
            <span class="arrow" :class="{ open: dropdownOpen }">&#9662;</span>
          </div>
          <div v-if="dropdownOpen" class="custom-select-dropdown">
            <div
              v-for="opt in optionList"
              :key="opt.value"
              :class="['custom-select-option', { selected: selectedMainAbility === opt.value }]"
              @mousedown.prevent="selectMainAbility(opt.value)"
            >
              <span class="option-label main">{{ opt.label }}</span>
            </div>
          </div>
        </div>
        <div class="ability-nav-list">
          <div
            v-for="(item, idx) in subAbilities"
            :key="item.abilityId"
            :class="['ability-nav-card', { active: idx === activeIndex }]"
            @click="scrollToSubAbility(item.abilityId, idx)"
          >
            <div class="dot-line">
              <span :class="['dot', { active: idx === activeIndex }]" />
              <div v-if="idx < subAbilities.length - 1" class="line"></div>
            </div>
            <div class="card-content">
              <div class="card-title" :class="{ active: idx === activeIndex }">
                子能力
              </div>
              <div class="card-desc">{{ item.nodeName }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧内容区 -->
    <div class="right-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>加载中...</p>
      </div>
      
      <!-- 错误状态 -->
      <div v-else-if="error" class="error-state">
        <p>{{ error }}</p>
        <button @click="fetchMainAbilities" class="retry-btn">重试</button>
      </div>
      
      <!-- 正常内容 -->
      <div v-else>
        <div class="panel-card knowledge-goal-card">
        <section class="content-block">
          <div class="block-header">
            <span class="main-ability-tag">★ 主能力</span>
            <h2 class="block-title">{{ currentMainAbility?.nodeName || '加载中...' }}</h2>
            <span class="block-header-spacer"></span>
          </div>
          <p class="description">
            {{ currentMainAbility?.nodeDesc || '暂无描述' }}
          </p>
          <div class="info-cards info-cards-wide">
            <div class="info-card info-card-blue"><span>包含子能力</span><strong>{{ subAbilities.length }}</strong>个</div>
            <div class="info-card info-card-green"><span>覆盖知识点</span><strong>{{ mainAbilityKnowledgeCount !== null ? mainAbilityKnowledgeCount : '—' }}</strong>个</div>
          </div>
        </section>
      </div>
              <div
          v-for="(item, idx) in subAbilities"
          :key="item.abilityId"
          class="panel-card sub-ability-card"
        >
          <section class="content-block" :id="'sub-ability-' + item.abilityId">
            <div class="block-header">
              <span class="sub-ability-tag">★ 子能力</span>
              <h2 class="block-title">{{ item.nodeName }}</h2>
              <span class="block-header-spacer"></span>
            </div>
            <p class="description">{{ item.nodeDesc || '暂无描述' }}</p>
            <div class="sub-section">
              <h4>覆盖知识点</h4>
              <SubAbilityTreeGraph :abilityId="item.abilityId" />
            </div>
            <!-- 这里可以继续补充子能力的详细内容 -->
          </section>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router'
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import SubAbilityTreeGraph from './SubAbilityTreeGraph.vue'
import { getMainAbilities, getSubAbilitiesLite, getMainAbilityDetail, countKnowledgeByMainAbility } from '@/api/public/course/courseMap/abilityGraph'
import { useAbilityGraphStore } from '@/stores/teacher/graphManager/abilityGraphStore'

const echartsRef = ref(null)
const route = useRoute()
const router = useRouter()
const abilityStore = useAbilityGraphStore()

// 从路由参数获取主能力名称
const abilityName = computed(() => decodeURIComponent(route.params.abilityName || ''))

// 主能力数据
const mainAbilities = ref([])
const selectedMainAbility = ref('')
const dropdownOpen = ref(false)
const activeIndex = ref(0)
const loading = ref(false)
const error = ref('')
const mainAbilityKnowledgeCount = ref(null)

// 当前选中的主能力
const currentMainAbility = computed(() => {
  // 优先使用下拉框选中的主能力
  if (selectedMainAbility.value) {
    const selected = mainAbilities.value.find(m => m.abilityId === selectedMainAbility.value)
    if (selected) return selected
  }
  
  // 如果没有选中，则使用路由参数中的主能力名称
  if (abilityName.value) {
    return mainAbilities.value.find(m => m.nodeName === abilityName.value)
  }
  
  // 如果都没有，返回第一个主能力
  return mainAbilities.value.length > 0 ? mainAbilities.value[0] : null
})

// 子能力列表
const subAbilities = ref([])

// 获取主能力列表
const fetchMainAbilities = async () => {
  loading.value = true
  error.value = ''
  
  try {
    // 从路由查询参数或localStorage获取graphId
    const graphId = route.query.graphId || localStorage.getItem('currentGraphId') || 'default-graph-id'
    console.log('AbilityDetail - 使用graphId:', graphId)
    console.log('AbilityDetail - 路由参数:', route.params)
    console.log('AbilityDetail - 查询参数:', route.query)
    
    // 使用 abilityStore 获取主能力数据
    await abilityStore.fetchMainAbilities(graphId)
    mainAbilities.value = abilityStore.mainAbilities
    console.log('AbilityDetail - 获取到主能力列表:', mainAbilities.value)
    
    // 如果 abilityStore 中没有数据，尝试从 localStorage 获取
    if (!mainAbilities.value || mainAbilities.value.length === 0) {
      console.log('AbilityDetail - abilityStore 中没有数据，尝试从 localStorage 获取')
      const storedMainAbilities = localStorage.getItem('mainAbilities')
      if (storedMainAbilities) {
        try {
          mainAbilities.value = JSON.parse(storedMainAbilities)
          console.log('AbilityDetail - 从 localStorage 获取到主能力列表:', mainAbilities.value)
        } catch (e) {
          console.error('AbilityDetail - 解析 localStorage 数据失败:', e)
        }
      }
    }
    
    // 如果路由中有主能力名称，设置为选中状态
    if (abilityName.value) {
      console.log('AbilityDetail - 从路由获取到主能力名称:', abilityName.value)
      const targetAbility = mainAbilities.value.find(m => m.nodeName === abilityName.value)
      if (targetAbility) {
        console.log('AbilityDetail - 找到目标主能力:', targetAbility)
        selectedMainAbility.value = targetAbility.abilityId
        // 获取主能力覆盖知识点数量
        try {
          const res = await countKnowledgeByMainAbility(targetAbility.abilityId)
          if (res.code === 200) {
            mainAbilityKnowledgeCount.value = res.result
          } else {
            mainAbilityKnowledgeCount.value = null
          }
        } catch (e) {
          mainAbilityKnowledgeCount.value = null
        }
        await fetchSubAbilities(targetAbility.abilityId)
      } else {
        console.warn('AbilityDetail - 未找到匹配的主能力:', abilityName.value)
        console.log('AbilityDetail - 可用的主能力:', mainAbilities.value.map(m => m.nodeName))
      }
    } else if (mainAbilities.value.length > 0) {
      // 如果没有路由参数，默认选中第一个主能力
      console.log('AbilityDetail - 默认选中第一个主能力')
      selectedMainAbility.value = mainAbilities.value[0].abilityId
      // 获取主能力覆盖知识点数量
      try {
        const res = await countKnowledgeByMainAbility(mainAbilities.value[0].abilityId)
        if (res.code === 200) {
          mainAbilityKnowledgeCount.value = res.result
        } else {
          mainAbilityKnowledgeCount.value = null
        }
      } catch (e) {
        mainAbilityKnowledgeCount.value = null
      }
      await fetchSubAbilities(mainAbilities.value[0].abilityId)
    } else {
      console.warn('AbilityDetail - 没有可用的主能力数据')
    }
  } catch (err) {
    console.error('AbilityDetail - 获取主能力列表失败:', err)
    error.value = '获取主能力列表失败'
  } finally {
    loading.value = false
  }
}

// 获取子能力列表
const fetchSubAbilities = async (parentId) => {
  if (!parentId) {
    console.log('AbilityDetail - parentId为空，跳过子能力获取')
    subAbilities.value = []
    return
  }

  loading.value = true
  error.value = ''

  try {
    console.log('AbilityDetail - 开始获取子能力列表，parentId:', parentId)
    // 使用新的主能力详情接口获取完整的子能力信息
    const response = await getMainAbilityDetail(parentId)
    if (response.code === 200) {
      const detailData = response.result || response.data
      // 从主能力详情中提取子能力列表
      subAbilities.value = detailData.subAbilities || detailData.children || []
      console.log('AbilityDetail - 获取到子能力列表:', subAbilities.value)
    } else {
      // 如果新接口失败，回退到原来的接口
      console.warn('AbilityDetail - 主能力详情接口失败，回退到精简接口')
      const fallbackResponse = await getSubAbilitiesLite(parentId)
      if (fallbackResponse.code === 200) {
        subAbilities.value = fallbackResponse.result || fallbackResponse.data
        console.log('AbilityDetail - 回退获取到子能力列表:', subAbilities.value)
      } else {
        error.value = fallbackResponse.msg || fallbackResponse.message || '获取子能力列表失败'
      }
    }
  } catch (err) {
    console.error('AbilityDetail - 获取子能力列表失败:', err)
    error.value = '获取子能力列表失败'
  } finally {
    loading.value = false
  }
}

function selectMainAbility(id) {
  console.log('选择主能力:', id)
  selectedMainAbility.value = id
  dropdownOpen.value = false
  activeIndex.value = 0
  // 获取主能力覆盖知识点数量
  mainAbilityKnowledgeCount.value = null
  countKnowledgeByMainAbility(id).then(res => {
    if (res.code === 200) {
      mainAbilityKnowledgeCount.value = res.result
    } else {
      mainAbilityKnowledgeCount.value = null
    }
  }).catch(() => {
    mainAbilityKnowledgeCount.value = null
  })
  // 获取对应的子能力列表
  fetchSubAbilities(id)
  // 更新URL，但不重新加载页面
  const selectedAbility = mainAbilities.value.find(m => m.abilityId === id)
  if (selectedAbility) {
    const newPath = `/ability-detail/${encodeURIComponent(selectedAbility.nodeName)}`
    const currentQuery = { ...route.query }
    router.replace({
      path: newPath,
      query: currentQuery
    })
  }
}

const optionList = computed(() => 
  mainAbilities.value.map(m => ({ value: m.abilityId, label: m.nodeName }))
)

const goBack = () => {
  router.back()
}

// 主能力数据（从后端获取）
const chartData = computed(() => {
  const mainAbilities = abilityStore.mainAbilities
  console.log('AbilityDetail - chartData computed - mainAbilities:', mainAbilities)
  
  if (!mainAbilities || mainAbilities.length === 0) {
    console.log('AbilityDetail - 没有主能力数据，返回空数组')
    return []
  }
  
  // 自动均分权重
  const value = 100 / mainAbilities.length
  const data = mainAbilities.map(ability => ({
    name: ability.nodeName || ability.abilityName || ability.name,
    value: value
  }))
  console.log('AbilityDetail - 生成的图表数据:', data)
  return data
})

const option = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    top: '5%',
    left: 'center',
    orient: 'horizontal',
    itemGap: 8,
    itemWidth: 12,
    itemHeight: 12,
    textStyle: {
      fontSize: 11,
      width: 60,
      overflow: 'truncate',
      ellipsis: '...'
    },
    formatter: function(name) {
      // 限制图例文本长度，超过8个字符则截断并添加省略号
      if (name.length > 8) {
        return name.substring(0, 8) + '...';
      }
      return name;
    }
  },
  series: [
    {
      name: '主能力分布',
      type: 'pie',
      radius: ['35%', '65%'],
      center: ['50%', '65%'], // 向下移动环图位置
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 14,
          fontWeight: 'bold',
          formatter: function(params) {
            // 在hover时显示完整名称
            return params.name;
          }
        },
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      labelLine: {
        show: false
      },
      data: chartData.value
    }
  ]
}))

function scrollToSubAbility(id, idx) {
  activeIndex.value = idx;
  const el = document.getElementById('sub-ability-' + id);
  if (el) {
    el.scrollIntoView({ behavior: 'smooth', block: 'start' });
  }
}

// 图表点击事件处理
const onChartClick = (params) => {
  console.log('扇形点击事件', params)
  console.log('当前主能力列表:', abilityStore.mainAbilities)
  
  if (params && params.name) {
    // 找到对应的主能力数据
    const mainAbility = abilityStore.mainAbilities.find(ability => 
      ability.nodeName === params.name
    )
    console.log('找到的主能力:', mainAbility)
    
    if (mainAbility) {
      console.log('准备跳转到主能力详情:', mainAbility)
      try {
        // 使用主能力名称作为路由参数，并传递graphId
        const graphId = route.query.graphId || localStorage.getItem('currentGraphId')
        router.push({
          path: `/ability-detail/${encodeURIComponent(mainAbility.nodeName)}`,
          query: { graphId: graphId }
        })
        console.log('跳转成功')
      } catch (error) {
        console.error('跳转失败:', error)
      }
    } else {
      console.warn('未找到对应的主能力数据:', params.name)
      console.log('可用的主能力:', abilityStore.mainAbilities.map(a => a.nodeName))
    }
  } else {
    console.warn('点击事件参数无效:', params)
  }
}

// 监听图表数据变化，重新绑定点击事件
watch(() => chartData.value, (newData) => {
  console.log('图表数据变化:', newData)
  if (newData.length > 0) {
    nextTick(() => {
      if (echartsRef.value && echartsRef.value.chart) {
        console.log('重新绑定点击事件')
        echartsRef.value.chart.off('click') // 先移除旧的点击事件
        echartsRef.value.chart.on('click', onChartClick)
      }
    })
  }
}, { deep: true })

// 组件挂载时获取数据
onMounted(async () => {
  console.log('AbilityDetail - 组件挂载开始')
  console.log('AbilityDetail - 当前路由参数:', route.params)
  console.log('AbilityDetail - 当前查询参数:', route.query)
  
  await fetchMainAbilities()
  
  // 等待图表渲染完成后添加点击事件
  nextTick(() => {
    if (echartsRef.value && echartsRef.value.chart) {
      console.log('AbilityDetail - 初始绑定点击事件')
      echartsRef.value.chart.on('click', onChartClick)
    } else {
      console.log('AbilityDetail - echartsRef未准备好，跳过点击事件绑定')
    }
  })
  
  console.log('AbilityDetail - 组件挂载完成')
})
</script>

<style scoped>
.top-back-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 56px;
  background: #fff;
  display: flex;
  align-items: center;
  font-size: 1.2rem;
  font-weight: 500;
  color: #888;
  z-index: 10;
  padding: 0 32px;
  box-sizing: border-box;
  border-bottom: 1.5px solid #eee;
}
.back-icon {
  font-size: 1.6rem;
  cursor: pointer;
  margin-right: 12px;
  color: #8a6de3;
  transition: color 0.2s;
}
.back-icon:hover {
  color: #6a5acd;
}
.back-text {
  margin-right: 8px;
}
.slash {
  margin: 0 8px;
  color: #bbb;
}
.current-title {
  color: #222;
  font-weight: bold;
}
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #6a5acd;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #c33;
  text-align: center;
}

.retry-btn {
  margin-top: 16px;
  padding: 8px 16px;
  background: #6a5acd;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.retry-btn:hover {
  background: #5a4acd;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #6a5acd;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #888;
  font-size: 1rem;
  text-align: center;
}

.ability-detail-page {
  position: relative;
  display: flex;
  background: #f8f9fc;
  padding: 24px;
  gap: 24px;
  padding-top: 72px;
}
.left-sidebar {
  width: 340px;
  flex-shrink: 0;
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  height: fit-content;
  margin-top: 0;
  position: sticky;
  top: 24px;
}
.sidebar-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 16px;
}
.mini-chart-container {
  width: 100%;
  height: 200px;
  background: #f0f2f5;
  border-radius: 8px;
  margin-bottom: 24px;
}
.ability-dropdown {
  margin-bottom: 18px;
}
.custom-select-box {
  width: 100%;
  padding: 12px 20px;
  border-radius: 16px;
  border: 1.5px solid #e6f0ea;
  font-size: 1.18rem;
  font-weight: bold;
  color: #222;
  background: #fff;
  outline: none;
  box-shadow: 0 2px 8px rgba(138,109,227,0.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  position: relative;
  transition: border 0.2s, box-shadow 0.2s;
}
.custom-select-box.open, .custom-select-box:focus {
  border: 1.5px solid #8a6de3;
  box-shadow: 0 4px 16px rgba(138,109,227,0.15);
}
.selected-label {
  flex: 1;
}
.arrow {
  margin-left: 12px;
  font-size: 1.2em;
  color: #bbb;
  transition: transform 0.2s;
}
.arrow.open {
  transform: rotate(180deg);
  color: #8a6de3;
}
.custom-select-dropdown {
  position: absolute;
  left: 0;
  top: 110%;
  width: 100%;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(138,109,227,0.13);
  z-index: 100;
  padding: 8px 0;
  margin-top: 4px;
  animation: fadeIn 0.18s;
}
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-8px); }
  to { opacity: 1; transform: translateY(0); }
}
.custom-select-option {
  padding: 14px 28px;
  font-size: 1.08rem;
  cursor: pointer;
  transition: background 0.18s;
  display: flex;
  align-items: center;
}
.custom-select-option:hover {
  background: #f8fafd;
}
.custom-select-option.selected {
  background: #f3f0ff;
}
.option-label.main {
  color: #888;
  font-weight: bold;
  font-size: 1.13rem;
}
.option-label.sub {
  color: #888;
  font-weight: normal;
}
.ability-nav-list {
  margin-top: 8px;
}
.ability-nav-card {
  display: flex;
  align-items: flex-start;
  background: #f8fafd;
  border-radius: 16px;
  margin-bottom: 18px;
  padding: 16px 20px;
  transition: background 0.2s;
  position: relative;
}
.ability-nav-card.active {
  background: #f3f0ff;
  box-shadow: 0 2px 8px rgba(138,109,227,0.08);
}
.dot-line {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 14px;
}
.dot {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: #fff;
  border: 3px solid #e6e6e6;
  margin-bottom: 2px;
  transition: border 0.2s, background 0.2s;
}
.dot.active {
  border: 3px solid #8a6de3;
  background: #8a6de3;
}
.line {
  width: 2px;
  height: 40px;
  background: #e6e6e6;
  margin-top: 2px;
}
.card-content {
  flex: 1;
}
.card-title {
  font-weight: bold;
  color: #8a6de3;
  font-size: 1.1rem;
  margin-bottom: 4px;
}
.card-title.active {
  color: #8a6de3;
}
.card-desc {
  color: #222;
  font-size: 1rem;
}
.right-content {
  flex-grow: 1;
  background: #fff;
  border-radius: 12px;
  padding: 32px;
  max-width: 1200px;
  margin: 0 auto;
  max-height: none;
  overflow-y: visible;
  margin-top: 0;
}
.content-block {
  margin-bottom: 40px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}
.block-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 18px;
}
.block-title {
  flex: 1;
  text-align: center;
  font-size: 1.8rem;
  font-weight: bold;
  color: #222;
  margin: 0;
}
.block-header-spacer {
  width: 120px;
  min-width: 60px;
  height: 1px;
  /* 占位用 */
}
.next-link {
  display: inline-flex;
  align-items: center;
  background: #ede7fa;
  color: #8a6de3;
  font-weight: bold;
  font-size: 1rem;
  border-radius: 1.2em;
  padding: 6px 16px 6px 12px;
  text-decoration: none;
  box-shadow: none;
  transition: background 0.2s, color 0.2s;
  margin-left: 24px;
}
.next-link:hover {
  background: #d6c6fa;
  color: #6a5acd;
}
.next-link::after {
  content: '\2197';
  font-size: 1em;
  margin-left: 6px;
  font-weight: bold;
}
.description {
  color: #555;
  line-height: 1.7;
  margin-bottom: 20px;
}
.info-cards {
  display: flex;
  gap: 16px;
  background: #f8fafd;
  padding: 16px;
  border-radius: 8px;
}
.info-card {
  background: #fff;
  padding: 12px 16px;
  border-radius: 8px;
  flex: 1;
  text-align: center;
}
.info-card span {
  display: block;
  color: #888;
  font-size: 0.9rem;
  margin-bottom: 8px;
}
.info-card strong {
  font-size: 1.5rem;
  font-weight: bold;
}
.sub-section {
  margin-top: 24px;
}
.sub-section h4 {
  font-size: 1.1rem;
  margin-bottom: 12px;
}
.placeholder-graph {
  width: 100%;
  height: 400px;
  background: #f0f2f5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  border-radius: 8px;
}
.question-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}
.question-card {
  background: #f8fafd;
  padding: 16px;
  border-radius: 8px;
  font-size: 0.95rem;
}
.expand-more {
  text-align: center;
  margin-top: 16px;
  color: #8a6de3;
  cursor: pointer;
}
.panel-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.06);
  margin-bottom: 32px;
  padding: 32px 32px 24px 32px;
}
.knowledge-goal-card {
  border-left: 6px solid #8a6de3;
}
.sub-ability-card {
  border-left: 6px solid #6ad3c7;
}
.main-ability-tag, .sub-ability-tag {
  display: inline-block;
  font-size: 1.1rem;
  font-weight: bold;
  border-radius: 18px;
  padding: 4px 18px;
  margin-bottom: 0;
  margin-right: 18px;
  vertical-align: middle;
}
.main-ability-tag { background: #8a6de3; color: #fff; }
.sub-ability-tag { background: #6ad3c7; color: #fff; }
.knowledge-goal-card h2, .sub-ability-card h2 {
  font-size: 1.8rem;
  font-weight: bold;
  color: #222;
  margin: 0 0 0 0;
  text-align: left;
  display: inline-block;
}
.info-cards-wide {
  display: flex;
  gap: 32px;
  margin-top: 32px;
  background: #fff;
  border-radius: 0;
  padding: 0;
  box-shadow: none;
}
.info-card-blue {
  background: #d6e8fd;
  color: #222;
  flex: 1 1 0;
}
.info-card-green {
  background: #f5d8d8;
  color: #222;
  flex: 1 1 0;
}
.sub-ability-card .block-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 18px;
}
.sub-ability-card h2 {
  text-align: left;
  display: inline-block;
  margin: 0 0 0 0;
}
</style> 