<template>
  <div class="plan-container">
    <!-- 主功能选择 -->
    <div class="header-section">
      <h1>灵犀 智能教案生成助手</h1>
      <div class="type-selector">
        <select v-model="selectedType" class="type-dropdown">
          <option v-for="(type, index) in lessonTypes" :key="index" :value="type.value">
            {{ type.label }}
          </option>
        </select>
      </div>
    </div>

    <!-- 内容生成区 -->
    <div class="content-wrapper">
      <div class="input-section">
        <div class="input-container">
          <div class="form-group">
            <label for="title">教案标题</label>
            <input type="text" id="title" v-model="form.title" required />
          </div>
          <div class="form-group">
            <label for="style">教案风格</label>
            <input type="text" id="style" v-model="form.style" required />
          </div>
          <div class="form-group">
            <label for="keyPoints">核心知识点</label>
            <textarea id="keyPoints" v-model="form.keyPoints" required rows="3"></textarea>
          </div>
          <div class="form-group">
            <label for="studentType">面向学生类型</label>
            <input type="text" id="studentType" v-model="form.studentType" required />
          </div>

          <button @click="handleGenerate" class="generate-btn" :disabled="!canGenerate">
            <span v-if="isGenerating" class="loading">
              <span class="dot">.</span><span class="dot">.</span><span class="dot">.</span>
            </span>
            <span v-else>生成教案</span>
          </button>
        </div>
      </div>

      <!-- 右侧弹窗 -->
      <div class="side-panel" :class="{ 'panel-visible': showSidePanel }">
        <div class="panel-header">
          <h3>生成的教案</h3>
          <div class="panel-controls">
            <button @click="downloadContent" class="action-btn">↓ 下载</button>
            <button @click="copyContent" class="action-btn">📋 复制</button>
            <button @click="togglePanel" class="close-btn">×</button>
          </div>
        </div>
        <div class="panel-content">
          <pre class="generated-text">{{ generatedContent }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { reactive, toRefs, computed } from 'vue'
import { generateLessonPlan } from '../../../../api/teacher/intelligent/plan.js'

export default {
  setup() {
    const state = reactive({
      lessonTypes: [
        { label: '标准教案', value: 'standard' },
        { label: '互动式教案', value: 'interactive' },
        { label: '探究式教案', value: 'inquiry' },
        { label: '翻转课堂教案', value: 'flipped' }
      ],
      selectedType: 'standard',
      form: {
        title: '',
        style: '',
        keyPoints: '',
        studentType: ''
      },
      generatedContent: '',
      isGenerating: false,
      showSidePanel: false
    })

    const togglePanel = () => {
      state.showSidePanel = !state.showSidePanel
    }

    const canGenerate = computed(() => {
      return (
        state.form.title.trim() !== '' &&
        state.form.style.trim() !== '' &&
        state.form.keyPoints.trim() !== '' &&
        state.form.studentType.trim() !== '' &&
        !state.isGenerating
      )
    })

// 修改生成函数
    const handleGenerate = async () => {
      try {
        state.isGenerating = true
        const { title, style, keyPoints, studentType } = state.form

        // 调用API生成教案
        let content = await generateLessonPlan(title, style, keyPoints, studentType)

        // 清理格式
        content = content.replace(/#+\s*/g, '') // 去掉#标题符号
          .replace(/\*\*/g, '') // 去掉加粗符号
          .replace(/\*/g, '') // 去掉其他星号
       //   .replace(/```[\s\S]*?```/g, '') // 去掉代码块
          .replace(/-{3,}/g, '') // 去掉分割线
          .replace(/\n{3,}/g, '\n\n') // 合并多余空行

        state.generatedContent = content
        state.showSidePanel = true
      } catch (error) {
        console.error('生成失败:', error)
        state.generatedContent = '教案生成失败，请稍后重试。错误: ' + error.message
      } finally {
        state.isGenerating = false
      }
    }

    const copyContent = () => {
      navigator.clipboard.writeText(state.generatedContent)
      alert('教案内容已复制到剪贴板！')
    }

    const downloadContent = () => {
      if (!state.generatedContent) return

      const blob = new Blob([state.generatedContent], { type: 'text/plain' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${state.form.title || '教案'}.txt`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }

    return {
      ...toRefs(state),
      canGenerate,
      handleGenerate,
      copyContent,
      downloadContent,
      togglePanel
    }
  }
}
</script>

<style scoped>
.plan-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  max-width: 900px;
  margin: 0 auto;
  padding: 10px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.header-section {
  text-align: center;
  margin-bottom: 0.1rem;
}

.type-selector {
  margin: 0.5rem 0;
}

.type-dropdown {
  padding: 0.4rem 1.2rem;
  border: 2px solid #7c5cfc;
  border-radius: 8px;
  background: white;
  font-size: 1.1rem;
  width: 100%;
  max-width: 300px;
}

.content-wrapper {
  display: grid;
  gap: 1rem;
}

.input-section {
  background: white;
  padding: 1rem;
  border-radius: 12px;
}

.form-group {
  margin-bottom: 0.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.4rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s;
}

.form-group input:focus,
.form-group textarea:focus {
  border-color: #7c5cfc;
  outline: none;
}

.form-group textarea {
  min-height: 10px;
  resize: vertical;
}

.generate-btn {
  background-color: #7c5cfc;
  color: white;
  border: none;
  padding: 0.4rem 0.5rem;
  border-radius: 6px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s;
  width: 100%;
  margin-top: 1rem;
}

.generate-btn:hover {
  background-color: #6a4bdf;
}

.generate-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.loading .dot {
  animation: bounce 1.4s infinite;
  display: inline-block;
}

.loading .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.loading .dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-3px); }
}

/* 侧边栏样式 */
.side-panel {
  position: fixed;
  right: -400px;
  top: 0;
  width: 380px;
  height: 100vh;
  background: white;
  box-shadow: -2px 0 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.panel-visible {
  right: 0;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #eee;
  background: #f9f9f9;
}

.panel-controls {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.action-btn {
  background: none;
  border: 1px solid #7c5cfc;
  color: #7c5cfc;
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s;
}

.action-btn:hover {
  background: #7c5cfc;
  color: white;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  transition: color 0.2s;
  padding: 0 0.5rem;
}

.close-btn:hover {
  color: #ff4444;
}

.generated-text {
  white-space: pre-wrap;
  line-height: 1.6;
  font-size: 1rem;
  background: #f9f9f9;
  padding: 1rem;
  border-radius: 4px;
}
</style>
