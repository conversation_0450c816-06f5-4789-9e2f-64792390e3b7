import request from '@/api/service';
// 能力图谱相关API接口

/**
 * 根据课程ID获取能力图谱列表
 * @param {string} courseId - 课程ID
 * @returns {Promise} 返回能力图谱列表
 */
export function getAbilityGraphsByCourse(courseId) {
  return request({
    url: '/ability-graph/list-by-course',
    method: 'get',
    params: { courseId }
  });
}

/**
 * 获取图谱能力统计信息
 * @param {string} graphId - 图谱ID
 * @returns {Promise} 返回能力统计信息（主能力+子能力数量）
 */
export function getAbilityStats(graphId) {
  return request({
    url: `/ability-graph/graphs/${graphId}/stats`,
    method: 'get'
  });
}

/**
 * 获取图谱主能力列表
 * @param {string} graphId - 图谱ID
 * @returns {Promise} 返回主能力列表（用于扇形图/导航下拉框）
 */
export function getMainAbilities(graphId) {
  return request({
    url: `/ability-graph/graphs/${graphId}/main-abilities`,
    method: 'get'
  });
}

/**
 * 获取子能力精简列表
 * @param {string} parentId - 主能力ID
 * @returns {Promise} 返回子能力列表（用于导航栏）
 */
export function getSubAbilitiesLite(parentId) {
  return request({
    url: `/ability-graph/abilities/${parentId}/sub-abilities`,
    method: 'get'
  });
}

/**
 * 获取关联知识点树形结构
 * @param {string} abilityId - 能力节点ID
 * @returns {Promise} 返回关联的知识节点树形结构
 */
export function getRelatedKnowledgeTree(abilityId) {
  return request({
    url: `/ability-graph/abilities/${abilityId}/knowledge-tree`,
    method: 'get'
  });
}

/**
 * 统计关联知识点数量（按能力ID）
 * @param {string} abilityId - 能力ID
 * @returns {Promise} 返回关联的知识点总数
 */
export function countKnowledgeNodesByAbility(abilityId) {
  return request({
    url: `/ability-graph/abilities/${abilityId}/knowledge-count`,
    method: 'get'
  });
}

/**
 * 统计图谱关联知识点总数
 * @param {string} graphId - 图谱ID
 * @returns {Promise} 返回关联的知识点总数
 */
export function countKnowledgeNodesByGraph(graphId) {
  return request({
    url: `/ability-graph/graphs/${graphId}/knowledge-count`,
    method: 'get'
  });
}

/**
 * 获取主能力详情
 * @param {string} abilityId - 主能力ID
 * @returns {Promise} 返回主能力详情及子能力列表
 */
export function getMainAbilityDetail(abilityId) {
  return request({
    url: `/ability-graph/abilities/${abilityId}/detail`,
    method: 'get'
  });
}

/**
 * 统计主能力覆盖知识点数量（去重）
 * @param {string} mainAbilityId - 主能力ID
 * @returns {Promise} 返回主能力覆盖知识点数量
 */
export function countKnowledgeByMainAbility(mainAbilityId) {
  return request({
    url: `/ability-graph/main-abilities/${mainAbilityId}/knowledge-count`,
    method: 'get'
  });
}
