// src/api/teacher/assignment.js
import request from '@/api/service'

// 获取 token（从 user 里解析）
function getToken() {
    try {
        const user = JSON.parse(localStorage.getItem('user'))
        console.log('token 是：', user?.token)
        return user?.token || ''
    } catch {
        return ''
    }
}


// 添加作业或考试
export const addAssignmentOrExam = (data, isExam = false) => {
    const url = isExam ? '/exam/save' : '/assignment/save'
    return request.post(url, data, {
        headers: {
            Authorization: `Bearer ${getToken()}`
        }
    })
}

// 修改作业或考试
export const updateAssignmentOrExam = (data, isExam = false) => {
    const url = isExam ? '/exam/update' : '/assignment/update'
    return request.post(url, data, {
        headers: {
            Authorization: `Bearer ${getToken()}`
        }
    })
}

// 删除作业或考试
export const deleteAssignmentOrExam = (id, isExam = false) => {
    const url = isExam ? '/exam/remove' : '/assignment/remove'
    return request.post(url, { id }, {
        headers: {
            Authorization: `Bearer ${getToken()}`
        }
    })
}

// 绑定到班级（必须传 courseId）
export const bindToClass = ({ id, courseId, classId, isExam = false }) => {
    const url = isExam ? '/exam/bind-class' : '/assignment/bind-class';

    if (!id || !classId || !courseId) {
        console.warn('缺少绑定参数:', { id, courseId, classId });
        return Promise.reject(new Error('id、courseId 或 classId 不能为空'));
    }

    const queryParams = new URLSearchParams({
        [isExam ? 'examId' : 'assignmentId']: id,
        courseId,
        classId
    }).toString();

    return request.post(`${url}?${queryParams}`, {}); // POST + query param
};

// 绑定到课程
export function bindToCourse(id, courseId, isExam) {
    const url = isExam ? '/exam/bind-course' : '/assignment/bind-course';

    if (!id || !courseId) {
        console.warn('缺少绑定参数:', { id, courseId });
        return Promise.reject(new Error('id 或 courseId 不能为空'));
    }

    const queryParams = new URLSearchParams({
        [isExam ? 'examId' : 'assignmentId']: id,
        courseId,
    }).toString();

    return request.post(`${url}?${queryParams}`, {}); // POST + 空 body
}



// 获取列表（GET 请求加 token）
export const fetchAssignmentOrExamList = (params, isExam = false) => {
    const url = isExam
        ? '/exam/page'
        : '/assignment/page'

    return request.get(url, {
        params,
        headers: {
            Authorization: `Bearer ${getToken()}`
        }
    })
}

// 引用题目到作业/考试
export function batchSaveAssignmentRelation(data) {
    return request.post('/assignment/relation/batch-save', data)
}

export function batchSaveExamRelation(data) {
    return request.post('/exam/relation/batch-save', data)
}

// 获取作业/考试详细信息
export function fetchAssignmentOrExamDetail(id, isExam) {
    // 根据 id 和 isExam 参数，调用对应的后端接口获取详细信息
    if (isExam) {
        return request.get(`/exam/detail?id=${id}`); // 获取考试的详细信息
    } else {
        return request.get(`/assignment/detail?id=${id}`); // 获取作业的详细信息
    }
}


/**
 * 获取考试/作业绑定的班级列表
 * @param {string} id - 作业或考试 ID
 * @param {boolean} isExam - true 表示考试，false 表示作业
 * @returns {Promise<Array>} 班级数组
 */
export function fetchBoundClassList(id, isExam) {
    const url = isExam
        ? `/api/teacher/exam/class-id-list`
        : `/api/teacher/assignment/class-id-list`

    const params = isExam ? { examId: id } : { assignmentId: id }

    return request.get(url, { params })
}