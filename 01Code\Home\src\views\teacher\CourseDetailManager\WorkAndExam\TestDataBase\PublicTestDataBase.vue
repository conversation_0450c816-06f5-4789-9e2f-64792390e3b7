<!-- 路径：src\views\teacher\CourseDetailManager\WorkAndExam\TestDataBase\PublicTestDataBase.vue -->
<template>
    <div class="project-manager">
        <NavBar />
        <div class="navbar-space" />

        <div class="main">
            <SideMenu />

            <div class="content">
                <div class="tabs">
                    <el-tabs v-model="activeTab">
                        <el-tab-pane label="我的题库" name="myQuestions" />
                        <el-tab-pane label="我的试卷库" name="myPapers" />
                        <el-tab-pane label="我的实践项目" name="myProjects" />
                    </el-tabs>

                    <div class="tab-actions">
                        <el-input v-model="tabSearch" placeholder="搜索题目 / 项目" style="width: 220px; margin-right: 12px;"
                            clearable />
                        <el-button type="primary" style="margin-right: 10px" @click="handleNewTestData">
                            <el-icon>
                                <Plus />
                            </el-icon>新增试题
                        </el-button>
                    </div>
                </div>


                <FilterBar v-model="filters" :questionTypes="currentQuestionTypes" :total="filteredData.length"
                    @update="handleFilterChange" @batchDelete="batchDelete" @toggleSortDesc="toggleSortDesc" />

                <div class="question-list" v-loading="loading" element-loading-background="transparent">
                    <div v-for="item in paginatedData" :key="item.id" class="question-card" @click="handlePreview(item)">
                        <el-checkbox v-model="item.checked" class="card-checkbox" @click.stop />
                        <div class="question-title">
                            <el-icon v-if="['实践项目', '整套试卷'].includes(item.type)" style="margin-right: 6px;">
                                <Folder />
                            </el-icon>
                            {{ item.title }}
                        </div>
                        <div class="question-meta">
                            创建时间: {{ item.createTime }} &nbsp;&nbsp; 更新时间: {{ item.updateTime }}
                        </div>
                    </div>
                </div>

                <!-- 公共弹窗 -->
                <el-dialog v-model="previewVisible" :title="getDialogTitle()" width="800px">
                    <!-- 整套试卷 -->
                    <ExamPreviewDialog v-if="currentPreview?.type === '整套试卷'" :examData="currentPreview" :inline="true" />

                    <!-- 普通题型 -->
                    <template v-else-if="['单选题', '多选题'].includes(currentPreview?.type)">
                        <p><strong>题目：</strong>{{ currentPreview.content }}</p>
                        <ul style="list-style: none; padding-left: 0;">
                            <li v-for="(val, key) in currentPreview.options" :key="key">
                                {{ key }}：{{ val }}
                            </li>
                        </ul>
                        <p><strong>正确答案：</strong>{{ formatAnswer(currentPreview.correct) }}</p>
                    </template>

                    <template v-else-if="currentPreview?.type === '判断题'">
                        <p><strong>题目：</strong>{{ currentPreview.content }}</p>
                        <p><strong>答案：</strong>{{ currentPreview.answer }}</p>
                    </template>

                    <template v-else-if="currentPreview?.type === '填空题'">
                        <p><strong>题目：</strong>{{ currentPreview.content }}</p>
                        <p><strong>答案：</strong>{{ currentPreview.answers.join(', ') }}</p>
                    </template>

                    <template v-else-if="currentPreview?.type === '问答题'">
                        <p><strong>题目：</strong>{{ currentPreview.content }}</p>
                        <p><strong>参考答案：</strong>{{ currentPreview.sampleAnswer }}</p>
                    </template>

                    <template v-else-if="currentPreview?.type === '实践项目'">
                        <div style="margin-bottom: 12px;">
                            <el-button type="primary" icon="el-icon-download"
                                @click="downloadFile(currentPreview.fileUrl)">下载文档</el-button>
                        </div>
                        <iframe :src="getOfficeOnlineUrl(currentPreview.fileUrl)" style="width: 100%; height: 600px;"
                            frameborder="0"></iframe>
                    </template>

                </el-dialog>


                <div class="pagination">
                    <el-pagination v-model:current-page="currentPage" :page-size="pageSize" layout="prev, pager, next"
                        :total="filteredData.length" background />
                </div>

            </div>
        </div>
    </div>

    <div class="bottom-bar">
        <el-button @click="onCancel">取消</el-button>
        <div class="selected-count">已选 {{ selectedTests.length }} 道题</div>
        <el-button type="primary" @click="onConfirm">引用所选试题</el-button>
    </div>

    <input ref="fileInputRef" type="file" accept=".pdf,.docx,.zip" style="display: none" @change="handleFileChange" />

    <template>
        <div>
            <el-button type="primary" @click="showCreateDialog = true">新建试卷</el-button>

            <el-dialog title="请选择创建方式" :visible.sync="showCreateDialog" width="400px" :before-close="handleDialogClose">
                <div style="display: flex; flex-direction: column; gap: 15px; padding: 20px 0;">
                    <el-button type="success" @click="triggerFileUpload">上传整套试卷（Word）</el-button>
                    <el-button type="primary" @click="goToCreatePage">自己创建题目</el-button>
                </div>
            </el-dialog>
        </div>
        <input type="file" ref="excelFileInput" style="display:none" @change="handleExcelUpload" />

    </template>
    <!-- 新建考试弹窗 -->
    <NewTestTypeSelectDialog v-model="showNewTestDialog" @single="() => router.push('/new-test')"
        @batch="triggerExcelUpload" />
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import NavBar from '@/components/NavBar.vue'
import SideMenu from '../components/SideMenu.vue'
import FilterBar from '../components/FilterBar.vue'
import { Plus, Folder } from '@element-plus/icons-vue'
import ExamPreviewDialog from '../components/ExamPreviewDialog.vue'

import { useCourseStore } from '@/stores/courseStore'
const courseStore = useCourseStore()

import { useQuestionStore } from '@/stores/useQuestionStore'

const testStore = useQuestionStore()

// 用 ref 存一下，避免每次都重复 query 取值
const courseId = ref('')
const courseName = ref('')

// 新建题目弹窗
import NewTestTypeSelectDialog from '../components/NewTestTypeSelectDialog.vue'

const showNewTestDialog = ref(false)

const showCreateDialog = ref(false)

function handleDialogClose(done) {
    // 关闭前可以加提示
    done()
}
//题目列表
import {
    getMyQuestions,
    importQuestionFile,
    getExamPage,
    getPracticeProjectList,
    getTeacherCourses
} from '@/api/teacher/question'

// 题目详情获取
import {
    getSingleChoiceDetail,
    getMultipleChoiceDetail,
    getTrueFalseDetail,
    getFillBlankDetail,
    getShortAnswerDetail,
    getExamDetail,
    getPracticeProjectDetail
} from '@/api/teacher/question'

const route = useRoute()
const router = useRouter()

const activeTab = ref('myQuestions')
const tabSearch = ref('')
const currentPage = ref(1)
const pageSize = 5

const previewVisible = ref(false)
const currentPreview = ref(null)

const filters = ref({
    qType: 'all',
    startTime: null,
    endTime: null,
    sortDesc: true,
    course: 'all'
})

function getDialogTitle() {
    if (!currentPreview.value) return '查看'
    return currentPreview.value.title || '查看'
}

const tableData = ref([])
const courseList = ref([])

const loading = ref(false)

const typeLabelMap = {
    0: '单选题',
    1: '多选题',
    2: '判断题',
    3: '填空题',
    4: '问答题',
    5: '实践项目',
    6: '整套试卷'
}

const tabQuestionTypeMap = {
    myQuestions: ['单选题', '多选题', '判断题', '填空题', '问答题'],
    myPapers: ['整套试卷'],
    myProjects: ['实践项目']
}

const currentQuestionTypes = computed(() => {
    const types = tabQuestionTypeMap[activeTab.value] || []
    return [{ label: '全部', value: 'all' }, ...types.map(t => ({ label: t, value: t }))]
})

function formatDate(ts) {
    const date = new Date(ts)
    return date.toISOString().split('T')[0]
}

function formatQuestion(q) {
    return {
        id: q.id,
        title: q.content?.slice(0, 30) + (q.content.length > 30 ? '...' : ''),
        type: typeLabelMap[q.type] || '未知类型',
        content: q.content || '',
        createTime: formatDate(q.createTime || Date.now()),
        updateTime: formatDate(q.updateTime || Date.now()),
        checked: false
    }
}
// 课程列表
async function fetchCourseList() {
    try {
        const res = await getTeacherCourses()
        if (res.code === 200) {
            courseList.value = res.result || []
        }
    } catch (err) {
        console.error('获取课程列表失败', err)
    }
}
//获取列表
async function fetchDataByTab() {
    loading.value = true
    try {
        const params = { isPublic: 1 }

        // 仅当 course 有效且不是 'all' 时，才加 courseId
        if (filters.value.course && filters.value.course !== 'all') {
            params.courseId = filters.value.course
        }

        console.log('[fetchDataByTab] 当前Tab:', activeTab.value, '请求参数:', params)

        if (activeTab.value === 'myQuestions') {
            const types = [0, 1, 2, 3, 4]
            const result = []
            for (const type of types) {
                const res = await getMyQuestions({ ...params, type })
                if (res.code === 200) {
                    result.push(...res.result.map(formatQuestion))
                } else {
                    console.warn(`获取题型 ${type} 失败`, res)
                }
            }
            tableData.value = result
        }

        else if (activeTab.value === 'myPapers') {
            const paperParams = {
                ...params,
                pageNum: 1,
                pageSize: 100
            }
            const res = await getExamPage(paperParams)
            if (res.code === 200) {
                tableData.value = res.result.records.map(item => ({
                    id: item.id,
                    title: item.title,
                    type: '整套试卷',
                    content: item.title,
                    createTime: formatDate(item.createdTime),
                    updateTime: formatDate(item.updatedTime || item.createdTime),
                    checked: false
                }))
            } else {
                console.warn('获取试卷失败:', res)
            }
        }

        else if (activeTab.value === 'myProjects') {
            const projectParams = {
                ...params,
                page: 1,
                size: 100
            }

            const res = await getPracticeProjectList(projectParams)
            if (res.code === 200) {
                const records = Array.isArray(res.result?.records)
                    ? res.result.records
                    : res.result

                tableData.value = records.map(item => ({
                    id: item.id,
                    title: item.title,
                    type: '实践项目',
                    content: item.title,
                    createTime: formatDate(item.createdTime),
                    updateTime: formatDate(item.updatedTime || item.createdTime),
                    checked: false
                }))
                console.log("实践项目响应：", res.result);
            } else {
                console.warn('获取实践项目失败:', res)
            }
        }

        currentPage.value = 1
    } catch (err) {
        console.error('加载题库失败', err)
        ElMessage({ message: '加载题库失败', type: 'error', offset: 90 })
    } finally {
        loading.value = false
    }
}

onMounted(async () => {

    const query = route.query

    courseId.value = query.courseId || ''
    courseName.value = query.courseName || ''

    if (courseId.value) {
        courseStore.currentCourseId = courseId.value
    }

    await fetchCourseList()
    await fetchDataByTab()
})

watch(activeTab, async () => {
    filters.value.qType = 'all'
    filters.value.startTime = null
    filters.value.endTime = null
    filters.value.sortDesc = true
    filters.value.course = 'all'
    tabSearch.value = ''
    await fetchDataByTab()
})

function handleFilterChange(newFilters) {
    Object.assign(filters.value, newFilters)
    fetchDataByTab()
}

const filteredData = computed(() => {
    let result = tableData.value

    if (tabSearch.value.trim()) {
        const kw = tabSearch.value.trim().toLowerCase()
        result = result.filter(item => item.title.toLowerCase().includes(kw))
    }

    if (filters.value.qType !== 'all') {
        result = result.filter(item => item.type === filters.value.qType)
    }

    if (filters.value.startTime) {
        const start = new Date(filters.value.startTime).getTime()
        result = result.filter(i => new Date(i.createTime).getTime() >= start)
    }
    if (filters.value.endTime) {
        const end = new Date(filters.value.endTime).getTime()
        result = result.filter(i => new Date(i.createTime).getTime() <= end)
    }

    result = result.sort((a, b) => {
        const t1 = new Date(a.createTime).getTime()
        const t2 = new Date(b.createTime).getTime()
        return filters.value.sortDesc ? t2 - t1 : t1 - t2
    })

    return result
})

const paginatedData = computed(() => {
    const start = (currentPage.value - 1) * pageSize
    const end = currentPage.value * pageSize
    return filteredData.value.slice(start, end)
})

const selectedTests = computed(() => filteredData.value.filter(i => i.checked))

async function batchDelete() {
    const checked = selectedTests.value
    if (!checked.length) {
        return ElMessage({ message: '请先选择题目', type: 'warning', offset: 90 })
    }
    try {
        await ElMessageBox.confirm(`确定删除选中的 ${checked.length} 道题目吗？`, '确认删除', {
            type: 'warning',
            offset: 90
        })
        tableData.value = tableData.value.filter(i => !i.checked)
        ElMessage({ message: '删除成功', type: 'success', offset: 90 })
    } catch {
        // 用户取消
    }
}

function toggleSortDesc() {
    filters.value.sortDesc = !filters.value.sortDesc
    fetchDataByTab()
}

// 格式化答案：去除 ["A", "C"] 变成 A, C
const formatAnswer = (ans) => {
    if (!ans) return ''
    try {
        const parsed = JSON.parse(ans)
        if (Array.isArray(parsed)) {
            return parsed.join(', ')
        }
        return parsed
    } catch {
        return ans
    }
}

const handlePreview = async (item) => {
    try {
        const typeMap = {
            '单选题': getSingleChoiceDetail,
            '多选题': getMultipleChoiceDetail,
            '判断题': getTrueFalseDetail,
            '填空题': getFillBlankDetail,
            '问答题': getShortAnswerDetail,
            '整套试卷': getExamDetail,
            '实践项目': getPracticeProjectDetail,
        }

        const apiFn = typeMap[item.type]
        if (!apiFn) {
            return ElMessage.error('不支持的题型：' + item.type)
        }

        // 实践项目接口特殊（需要拼 URL）
        const res = item.type === '实践项目'
            ? await apiFn(item.id)
            : await apiFn({ id: item.id })

        if (res.code !== 200 && res.code !== 0) {
            return ElMessage.error('预览失败：' + res.msg)
        }

        let previewData = {}

        if (item.type === '单选题' || item.type === '多选题') {
            const opts = JSON.parse(res.result.options || '{}')
            previewData = {
                ...res.result,
                options: opts,
                type: item.type,
            }
        } else if (item.type === '判断题') {
            previewData = {
                ...res.result,
                answer: res.result.answer ? '正确' : '错误',
                type: item.type,
            }
        } else if (item.type === '填空题') {
            // 先去掉答案字符串的[]和""，拆成数组
            let answers = []
            try {
                const parsed = JSON.parse(res.result.answers || '[]')
                if (Array.isArray(parsed)) {
                    answers = parsed
                } else if (typeof parsed === 'string') {
                    answers = [parsed]
                }
            } catch {
                answers = (res.result.answers || '').split(',')
            }
            previewData = {
                ...res.result,
                answers,
                type: item.type,
            }
        } else if (item.type === '问答题') {
            previewData = {
                ...res.result,
                type: item.type,
            }
        } else if (item.type === '整套试卷') {
            const formatList = (list, type) =>
                (list || []).map(q => ({
                    ...q.question,  // 展开 question 对象
                    score: q.score,
                    type
                }))

            previewData = {
                title: res.result.title,
                totalScore: res.result.totalScore,
                questions: [
                    ...formatList(res.result.singleChoiceList, '单选题'),
                    ...formatList(res.result.multipleChoiceList, '多选题'),
                    ...formatList(res.result.trueFalseList, '判断题'),
                    ...formatList(res.result.fillInBlankList, '填空题'),
                    ...formatList(res.result.shortAnswerList, '问答题'),
                ],
                type: '整套试卷'
            }
        } else if (item.type === '实践项目') {
            previewData = {
                type: '实践项目',
                fileUrl: res.result.fileUrl || item.content || '', // 用后端返回的fileUrl
                title: res.result.title || item.title || '实践项目'
            }
        }

        currentPreview.value = previewData
        previewVisible.value = true
    } catch (err) {
        console.error('预览出错', err)
        ElMessage.error('预览失败：' + (err.message || '未知错误'))
    }
}


//实践项目
const downloadFile = (url) => {
    if (!url) {
        ElMessage.error('无效的文件地址')
        return
    }
    const link = document.createElement('a')
    link.href = url
    const fileName = url.split('/').pop().split('?')[0]
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
}

const getOfficeOnlineUrl = (url) => {
    if (!url) return ''
    return `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(url)}`
}

const mode = computed(() => route.query.mode || 'assignment')

function buildReturnPath(mode) {
    const id = courseId.value
    const name = courseName.value
    return `/teacher/course-detail-manager/${encodeURIComponent(id)}/${encodeURIComponent(name)}/assignment-exam/create?courseId=${encodeURIComponent(id)}&courseName=${encodeURIComponent(name)}&mode=${encodeURIComponent(mode)}&step=1`
    // return `/teacher/course-detail-manager/${encodeURIComponent(id)}/assignment-exam/create?courseId=${encodeURIComponent(id)}&courseName=${encodeURIComponent(name)}&mode=${encodeURIComponent(mode)}&step=1`
}

function onCancel() {
    if (!courseId.value) {
        ElMessage.warning('课程信息缺失，无法返回')
        return
    }
    router.push(buildReturnPath(mode.value))
}

function onConfirm() {
    if (selectedTests.value.length === 0) {
        ElMessage.warning('请选择至少一个题目')
        return
    }

    testStore.addTests(selectedTests.value)
    router.push(buildReturnPath(mode.value))
}

function handleNewTestData() {
    if (activeTab.value === 'myQuestions') {
        showNewTestDialog.value = true
    } else if (activeTab.value === 'myPapers') {
        // 如果是我的试卷，传递 `paperMode=true`
        router.push({
            path: '/new-test',
            query: { paperMode: true }
        })
    } else {
        fileInputRef.value?.click()
    }
}

const excelFileInput = ref(null);

function triggerExcelUpload() {
    if (!excelFileInput.value) {
        console.warn('文件 input 尚未挂载');
        return;
    }
    excelFileInput.value.click();
}

async function handleExcelUpload(event) {
    const file = event.target.files?.[0];
    if (!file) {
        console.warn('[上传中断] 未选择文件');
        return;
    }

    try {
        console.log('[准备上传] 文件信息:', file);

        const res = await importQuestionFile(file);

        console.log('[上传响应]', res);

        if (res.code === 200) {
            ElMessage.success({ message: '导入成功', offset: 90 });
            await fetchDataByTab();
        } else {
            ElMessage.error({ message: '导入失败：' + (res.msg || '未知错误'), offset: 90 });
        }
    } catch (err) {
        console.error('[上传异常]', err);
        ElMessage.error({ message: '上传异常：' + (err.message || '未知错误'), offset: 90 });
    } finally {
        // 清空 input 值，保证可重复选同一文件触发 change 事件
        if (excelFileInput.value) {
            excelFileInput.value.value = '';
        }
    }
}

</script>

<style scoped>
.project-manager {
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 78px);
}

.navbar-space {
    height: 78px;
    flex-shrink: 0;
}

.main {
    display: flex;
    top: 64px;
    flex: 1;
    overflow: hidden;
}


.side-menu {
    width: 200px;
    min-height: calc(100vh - 60px);
    border-right: 1px solid #ebeef5;
}

.el-menu-item.is-active {
    background-color: #409eff !important;
    color: white !important;
}

.content {
    max-height: calc(100vh - 78px);
    flex: 1;
    padding: 24px;
}

.tabs {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.tab-actions {
    display: flex;
    align-items: center;
}

.question-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.question-card {
    background: white;
    padding: 16px;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    position: relative;
}

.card-checkbox {
    position: absolute;
    top: 16px;
    left: 16px;
}

.question-title {
    margin-left: 32px;
    font-weight: 600;
    font-size: 15px;
    color: #333;
    margin-bottom: 6px;
    display: flex;
    align-items: center;
}

.question-meta {
    margin-left: 32px;
    font-size: 12px;
    color: #888;
}

/* 底部操作栏 */
.bottom-bar {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 60px;
    padding: 0 20px;
    background: #fff;
    border-top: 1px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.selected-count {
    font-size: 14px;
    color: #666;
}

/* 分页条容器 */
.pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px;
    margin-bottom: 80px;
    /* 留出 bottom-bar 高度避免遮挡 */
}

/* 内容区域自动撑满剩余高度并允许滚动 */
.content {
    display: flex;
    flex-direction: column;
    max-height: calc(100vh - 78px);
    flex: 1;
}

/* 题目列表区域自动撑满内容空间 */
.question-list {
    flex: 1;
}
</style>
  