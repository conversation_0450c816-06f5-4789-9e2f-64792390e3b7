<template>
  <div class="notice-list">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-tip">
      正在加载公告...
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-tip">
      <div class="error-icon">⚠️</div>
      <div class="error-message">{{ error }}</div>
      <button @click="$emit('retry')" class="retry-btn">重试</button>
    </div>

    <!-- 空状态 -->
    <div v-else-if="displayNotices.length === 0" class="empty-tip">
      暂无{{ activeTabLabel }}
    </div>

    <!-- 公告列表 -->
    <div v-else>
      <div 
        v-for="notice in displayNotices" 
        :key="notice.id" 
        class="notice-item" 
        @click="showNoticeDetail(notice)"
      >
        <div class="notice-header">
          <h3 class="notice-title">{{ notice.title }}</h3>
          <span class="notice-time">{{ formatTime(notice.publishTime) }}</span>
        </div>
        <div class="notice-content" v-html="getContentPreview(notice.content)"></div>
        <div class="notice-footer">
          <span class="notice-type">{{ getNoticeTypeLabel(notice.noticeType) }}</span>
          <div class="notice-meta">
            <span v-if="notice.readCount" class="read-count">阅读 {{ notice.readCount }}</span>
            <span v-if="notice.resources && notice.resources.length > 0" class="has-attachment">
              📎 {{ notice.resources.length }}个附件
            </span>
          </div>
        </div>
      </div>

      <!-- 分页组件 -->
      <div v-if="pagination.pages > 1" class="pagination">
        <button 
          :disabled="pagination.pageNum <= 1" 
          @click="changePage(pagination.pageNum - 1)"
          class="page-btn"
        >
          上一页
        </button>
        
        <span class="page-info">
          第 {{ pagination.pageNum }} 页，共 {{ pagination.pages }} 页
        </span>
        
        <button 
          :disabled="pagination.pageNum >= pagination.pages" 
          @click="changePage(pagination.pageNum + 1)"
          class="page-btn"
        >
          下一页
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useStudentNoticeStore } from '@/stores/student/noticeStore'

const props = defineProps({
  notices: {
    type: Array,
    default: () => []
  },
  pagination: {
    type: Object,
    default: () => ({
      pageNum: 1,
      pageSize: 10,
      total: 0,
      pages: 0
    })
  }
})

const emit = defineEmits(['page-change', 'notice-click', 'retry'])

const noticeStore = useStudentNoticeStore()

// 计算属性
const isLoading = computed(() => noticeStore.isLoading)
const error = computed(() => noticeStore.error)

// 显示的公告列表
const displayNotices = computed(() => {
  return props.notices || []
})

// 当前标签的显示名称
const activeTabLabel = computed(() => {
  return noticeStore.NOTICE_TYPES[noticeStore.activeTab]?.label || '公告'
})

// 获取公告类型标签
const getNoticeTypeLabel = (typeValue) => {
  return noticeStore.NOTICE_TYPES[noticeStore.NOTICE_TYPE_MAP[typeValue]]?.label || '公告'
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date

  // 如果是今天
  if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  }

  // 如果是昨天
  const yesterday = new Date(now)
  yesterday.setDate(yesterday.getDate() - 1)
  if (date.getDate() === yesterday.getDate() && date.getMonth() === yesterday.getMonth()) {
    return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  }

  // 其他情况显示日期
  return date.toLocaleDateString('zh-CN')
}

// 获取内容预览
const getContentPreview = (content) => {
  if (!content) return ''
  // 移除HTML标签，只保留文本内容，限制长度
  const textContent = content.replace(/<[^>]*>/g, '')
  return textContent.length > 100 ? textContent.substring(0, 100) + '...' : textContent
}

// 显示公告详情
const showNoticeDetail = (notice) => {
  noticeStore.setSelectedNotice(notice)
  emit('notice-click', notice)
}

// 切换页码
const changePage = (pageNum) => {
  emit('page-change', pageNum)
}
</script>

<style lang="scss" scoped>
@use '@/styles/variables';

.notice-list {
  .loading-tip, .empty-tip, .error-tip {
    text-align: center;
    padding: 40px;
    color: #999;
    font-size: 14px;
  }

  .error-tip {
    color: #f56c6c;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;

    .error-icon {
      font-size: 32px;
    }

    .error-message {
      font-size: 14px;
      text-align: center;
      max-width: 400px;
    }

    .retry-btn {
      padding: 8px 16px;
      background-color: $primary-color;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s;

      &:hover {
        background-color: darken($primary-color, 10%);
      }
    }
  }

  .notice-item {
    background-color: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border-color: $primary-color;
    }

    .notice-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 12px;

      .notice-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0;
        flex: 1;
        margin-right: 16px;
      }

      .notice-time {
        font-size: 12px;
        color: #999;
        white-space: nowrap;
      }
    }

    .notice-content {
      color: #666;
      font-size: 14px;
      line-height: 1.6;
      margin-bottom: 12px;
    }

    .notice-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .notice-type {
        background-color: $primary-color;
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
      }

      .notice-meta {
        display: flex;
        gap: 12px;
        font-size: 12px;
        color: #999;

        .has-attachment {
          color: $primary-color;
        }
      }
    }
  }

  .pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    margin-top: 24px;
    padding: 16px 0;

    .page-btn {
      padding: 8px 16px;
      border: 1px solid #ddd;
      background-color: #fff;
      color: #666;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover:not(:disabled) {
        background-color: $primary-color;
        color: white;
        border-color: $primary-color;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    .page-info {
      font-size: 14px;
      color: #666;
    }
  }
}
</style>
