// src/main.js
import { createApp } from 'vue'
import App from './App.vue'
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'
import router from './router'
import { addDynamicRoutes } from '@/router/dynamicRoutes'
import { useAuthStore } from '@/stores/auth/auth.js'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import VueECharts from 'vue-echarts'
import 'echarts'

const app = createApp(App)
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)
app.use(pinia)
app.use(router)
app.use(Antd)
app.use(ElementPlus);
app.component('v-chart', VueECharts)

router.isReady().then(() => {
  const authStore = useAuthStore()

  // 同步初始化认证状态（从localStorage恢复）
  authStore.initializeAuthState()

  // 如果已认证，同步加载动态路由
  if (authStore.isAuthenticated && authStore.user?.role !== undefined) {
    addDynamicRoutes(authStore.user.role)
  }

  app.mount('#app')
})