<template>
  <div class="course-review-container">
    <ReviewStatsHeader :course-info="courseInfo" :course-id="courseId" />
    <ReviewStatus :loading="loading" :error="error" :on-retry="fetchStats" v-if="loading || error" />
    <ReviewStatsChart v-else :stats="stats" :course-info="courseInfo" :chart-option="chartOption" />
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import { getCourseStats } from '@/api/public/course/review'
import { getCourseDetail } from '@/api/public/course/course'
import ReviewStatsHeader from './components/ReviewStatsHeader.vue'
import ReviewStatsChart from './components/ReviewStatsChart.vue'
import ReviewStatus from './components/ReviewStatus.vue'

use([
  CanvasRenderer,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

const route = useRoute()
const loading = ref(false)
const error = ref('')
const courseInfo = ref({})
const stats = ref({
  courseTotalPosts: 0,
  courseTotalParticipants: 0,
  courseTotalPostLikes: 0,
  courseTotalPostStars: 0
})
const courseId = computed(() => route.params.courseId)

const fetchCourseInfo = async () => {
  try {
    const response = await getCourseDetail(courseId.value)
    if (response && response.code === 200) {
      courseInfo.value = response.result.course || {}
    }
  } catch (err) {
    // 课程信息获取失败不影响主流程
  }
}

const fetchStats = async () => {
  if (!courseId.value) {
    error.value = '课程ID不存在'
    return
  }
  try {
    loading.value = true
    error.value = ''
    const response = await getCourseStats(courseId.value)
    if (response && response.code === 200) {
      stats.value = response.result
    } else {
      throw new Error(response?.msg || '获取数据失败')
    }
  } catch (err) {
    error.value = err.message || '网络错误，请稍后重试'
  } finally {
    loading.value = false
  }
}

watch(courseId, (newCourseId) => {
  if (newCourseId) {
    fetchCourseInfo()
    fetchStats()
  }
}, { immediate: true })

const chartOption = computed(() => ({
  title: {
    text: `${courseInfo.value.name || '课程'}数据统计`,
    left: 'center',
    textStyle: {
      fontSize: 16,
      fontWeight: 'bold'
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    formatter: function(params) {
      let result = params[0].name + '<br/>'
      params.forEach(param => {
        result += `${param.marker} ${param.seriesName}: ${param.value}<br/>`
      })
      return result
    }
  },
  legend: {
    data: ['帖子数', '参与人数', '点赞数', '收藏数'],
    bottom: 10
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '15%',
    top: '15%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['课程数据'],
    axisLabel: {
      fontSize: 12
    }
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      fontSize: 12
    }
  },
  series: [
    {
      name: '帖子数',
      type: 'bar',
      data: [stats.value.courseTotalPosts],
      barWidth: 'auto',
      barMaxWidth: '25%',
      itemStyle: {
        color: '#409EFF'
      }
    },
    {
      name: '参与人数',
      type: 'bar',
      data: [stats.value.courseTotalParticipants],
      barWidth: 'auto',
      barMaxWidth: '25%',
      itemStyle: {
        color: '#67C23A'
      }
    },
    {
      name: '点赞数',
      type: 'bar',
      data: [stats.value.courseTotalPostLikes],
      barWidth: 'auto',
      barMaxWidth: '25%',
      itemStyle: {
        color: '#E6A23C'
      }
    },
    {
      name: '收藏数',
      type: 'bar',
      data: [stats.value.courseTotalPostStars],
      barWidth: 'auto',
      barMaxWidth: '25%',
      itemStyle: {
        color: '#F56C6C'
      }
    }
  ]
}))
</script>

<style scoped>
.course-review-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}
</style>