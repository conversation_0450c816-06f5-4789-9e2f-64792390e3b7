<template>
    <div class="submission-list">
        <el-table :data="data" style="width: 100%" @row-click="handleRowClick" highlight-current-row>
            <el-table-column prop="studentId" label="学号" width="120"></el-table-column>
            <el-table-column prop="studentName" label="姓名" width="120"></el-table-column>
            <el-table-column v-if="isSubmitted" prop="submitTime" label="提交时间"></el-table-column>
            <el-table-column v-if="isSubmitted" prop="score" label="分数" width="100">
                <template #default="{ row }">
                    <span v-if="row.score !== null && row.score !== undefined">{{ row.score }}</span>
                    <span v-else style="color: #F56C6C;">未批改</span>
                </template>
            </el-table-column>
            <el-table-column v-if="!isSubmitted" label="状态" width="100">
                <template #default>
                    <el-tag type="danger">未提交</el-tag>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script setup>
const props = defineProps({
    data: {
        type: Array,
        required: true
    },
    isSubmitted: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['select'])

const handleRowClick = (row) => {
    emit('select', row)
}
</script>

<style scoped>
.submission-list {
    margin-top: 15px;
}
</style>