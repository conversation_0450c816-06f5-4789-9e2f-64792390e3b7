import request from '@/api/service'

//创建签到表
export function createAttendance(data) {
  return request.post('/teacher/attendance/create', data)
}

//获取全部签到表
export function fetchAttendanceList() {
  return request.get('/teacher/attendance/list');
}

//删除签到表
export function deleteAttendance(attendanceId) {
  return request.delete(`/teacher/attendance/delete/${attendanceId}`);
}

//更新签到表
export function updateAttendance(data) {
  return request.put('/teacher/attendance/update', data);
}


// 更新学生考勤状态
export function updateAttendanceStatus(data) {
  return request.put('/teacher/attendance/status/update', data);
}

//获取学生签到详情
export function getAttendanceDetail(attendanceId) {
  return request({
    url: `/teacher/attendance/detail/${attendanceId}`,
    method: 'get'
  });
}

//获取班级学生签到率
export function getClassAttendanceRate(classId) {
  return request({
    url: `/teacher/attendance/attendance-rate/class/${classId}`,
    method: 'get'
  });
}



