<template>
  <!-- 原有所有内容完全不变 -->
  <div class="score-container">
    <div class="header flex justify-between items-center mb-4">
      <h1 class="text-xl font-bold">总成绩</h1>
      <div class="flex space-x-2">
        <el-button type="primary" :icon="Download" @click="handleDownload">
          下载总成绩
        </el-button>
        <!-- 只修改这个按钮的点击事件 -->
        <el-button type="primary" :icon="Setting" @click="showClassSelection">
          成绩加权设置
        </el-button>
        <el-button type="primary" :icon="Box" @click="handleArchive">
          成绩归档封存
        </el-button>
      </div>
    </div>

    <div class="text-lg font-medium mb-2">数字媒体技术</div>
    <div class="text-md font-medium mb-4">总成绩分布</div>

    <div class="flex justify-between items-center mb-4">
      <div></div>
      <div class="flex space-x-6">
        <div class="flex items-center">
          <span class="text-gray-600 mr-2">平均分：</span>
          <span class="font-semibold">85.6</span>
        </div>
        <div class="flex items-center">
          <span class="text-gray-600 mr-2">最高分：</span>
          <span class="font-semibold">98</span>
        </div>
        <div class="flex items-center">
          <span class="text-gray-600 mr-2">最低分：</span>
          <span class="font-semibold">62</span>
        </div>
      </div>
    </div>

    <div class="chart-container">
      <div ref="chartRef" class="w-full" style="height: 400px;"></div>
    </div>

    <!-- 班级选择弹窗 -->
    <el-dialog v-model="classDialogVisible" title="选择班级" width="500px">
    <el-select
      v-model="selectedClassId"
      placeholder="请选择班级"
      style="width: 100%"
    >
      <el-option
        v-for="classItem in classOptions"
        :key="classItem.id"
        :label="classItem.name"
        :value="classItem.id"
      />
    </el-select>
    
    <template #footer>
      <el-button @click="classDialogVisible = false">取消</el-button>
      <el-button 
        type="primary" 
        @click="confirmClassSelection"
        :disabled="!selectedClassId"
      >
        确定
      </el-button>
    </template>
  </el-dialog>
  </div>
</template>

<script>
// 原有导入保持不变
import { defineComponent, ref } from 'vue';
import * as echarts from 'echarts';
import { ElButton, ElDialog, ElSelect, ElOption } from 'element-plus';
import { Download, Setting, Box } from '@element-plus/icons-vue';
import { useRouter, useRoute } from 'vue-router';
import { fetchClassList } from '@/api/teacher/class'; // 根据实际路径调整

export default defineComponent({
  name: 'ScoreStatistics',
  components: {
    ElButton,
    ElDialog,
    ElSelect,
    ElOption,
    Download,
    Setting,
    Box
  },
  setup() {
    // 原有所有代码保持不变
    const chartRef = ref(null);
    const router = useRouter();
    const route = useRoute();
  const classOptions = ref([]); // 初始化为空数组
  const loading = ref(false);   // 新增：加载状态
    
    // 新增的弹窗相关代码（唯一新增的逻辑）
    const classDialogVisible = ref(false);
     const selectedClassId = ref(''); 
 
    const showClassSelection = () => {
      classDialogVisible.value = true;
    };
    
const confirmClassSelection = () => {
  router.push({
    name: 'WeightRatio', 
    query: {
      classId: selectedClassId.value
    }
  });
  classDialogVisible.value = false;
};
     const loadClassList = async () => {
    loading.value = true;
    try {
      const { result } = await fetchClassList({
        courseId: route.params.courseId // 从路由参数获取课程ID
      });
      classOptions.value = result.map(item => ({
        id: item.id,
        name: `${item.name}(${item.studentCount}人)` // 可选：显示学生人数
      }));
    } catch (error) {
      ElMessage.error('获取班级列表失败');
      console.error('获取班级列表失败', error);
    } finally {
      loading.value = false;
    }
  };
    // 原有方法保持不变
    const handleDownload = () => alert('下载功能');
    const handleArchive = () => alert('归档功能');
    
    // 原有图表初始化代码保持不变
    onMounted(() => {
      const chart = echarts.init(chartRef.value);
      chart.setOption({
        // 原有图表配置
        xAxis: {
          type: 'category',
          data: ['0-10', '11-20', '21-30', '31-40', '41-50', '51-60', '61-70', '71-80', '81-90', '91-100']
        },
        yAxis: { type: 'value' },
        series: [{
          data: [0, 2, 4, 10, 20, 40, 35, 25, 15, 5],
          type: 'bar'
        }]
      });
    });
      onMounted(() => {
    loadClassList();
  });


    return {
      // 原有所有返回内容保持不变
      chartRef,
      handleDownload,
      handleArchive,
      Download,
      Setting,
      Box,
      
      // 新增的弹窗相关返回
      classDialogVisible,
      selectedClassId,
      classOptions,
      showClassSelection,
      confirmClassSelection
    };
  }
});
</script>

<style scoped>
/* 原有所有样式完全保持不变 */
.score-container {
  padding: 20px;
  height: 100vh;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.chart-container {
  margin-top: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.header {
  margin-bottom: 20px;
}
</style>