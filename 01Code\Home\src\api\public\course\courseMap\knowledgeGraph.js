import request from '@/api/service';
// 知识图谱相关API接口

/**
 * 根据课程ID获取关联图谱列表
 * @param {string} courseId - 课程ID
 * @returns {Promise} 返回图谱列表
 */
export function getGraphsByCourse(courseId) {
    return request({
      url: '/knowledge-graph/list-by-course',
      method: 'get',
      params: { courseId }
    })
  }

/**
 * 获取树形图数据
 * @param {string} graphId - 图谱ID
 * @returns {Promise} 返回树形图数据
 */
export function getTreeView(graphId) {
  return request({
    url: '/knowledge-graph/tree-view',
    method: 'get',
    params: { graphId }
  })
}

/**
 * 获取环形图数据
 * @param {string} graphId - 图谱ID
 * @param {number} maxLevel - 最大显示层级（可选）
 * @returns {Promise} 返回环形图数据
 */
export function getRingView(graphId, maxLevel = null) {
  const params = { graphId }
  if (maxLevel !== null) {
    params.maxLevel = maxLevel
  }
  return request({
    url: '/knowledge-graph/ring-view',
    method: 'get',
    params
  })
}

/**
 * 获取所有标签
 * @returns {Promise} 返回所有标签列表
 */
export function getAllTags() {
  return request({
    url: '/knowledge-graph/tags/all',
    method: 'get'
  })
}

/**
 * 获取标签统计
 * @param {string} graphId - 图谱ID
 * @returns {Promise} 返回标签统计数据
 */
export function getTagCount(graphId) {
  return request({
    url: '/knowledge-graph/tags/count',
    method: 'get',
    params: { graphId }
  })
} 