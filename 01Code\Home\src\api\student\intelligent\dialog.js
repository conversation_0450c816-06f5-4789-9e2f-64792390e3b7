import OpenAI from 'openai';
import {getKey} from '../../key.js';

const deepseekApiKey = getKey('deepseek');

export const createChatCompletion = async (messages, maxTokens, streamHandler) => {
  const openai = new OpenAI({
    baseURL: 'https://api.deepseek.com',
    apiKey: deepseekApiKey,
    dangerouslyAllowBrowser: true
  });

  try {
    const stream = await openai.chat.completions.create({
      model: 'deepseek-chat',
      messages,
      stream: true,
      max_tokens: maxTokens,
      temperature: 0.7
    });

    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content || '';
      streamHandler(content);
    }
    return true;
  } catch (error) {
    console.error('API请求失败:', error);
    throw error;
  }
};
