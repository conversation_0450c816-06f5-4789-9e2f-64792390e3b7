.project-manager,
.course-test-database {
    display: flex;
    flex-direction: column;
    min-height: 90vh;
}

.navbar-space {
    height: 78px;
    flex-shrink: 0;
}

.main {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.content {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
    background-color: #f5f7fa;
}

.tabs {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 4px;
    margin-bottom: 12px;
}

.question-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.question-card {
    background: white;
    padding: 16px;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    position: relative;
}

.card-checkbox {
    position: absolute;
    top: 16px;
    left: 16px;
}

.question-title {
    margin-left: 32px;
    font-weight: 600;
    font-size: 15px;
    color: #333;
    margin-bottom: 6px;
    display: flex;
    align-items: center;
}

.question-meta {
    margin-left: 32px;
    font-size: 12px;
    color: #888;
}