<template>
  <div class="cultural-heritage-container">
    <!-- 搜索和筛选区域 -->
    <div class="filter-section">
      <div class="search-box">
        <input
          v-model="searchParams.name"
          type="text"
          placeholder="搜索非遗名称..."
          @keyup.enter="handleSearch"
          class="search-input"
        />
        <button @click="handleSearch" class="search-btn">
          <i class="fas fa-search"></i>
        </button>
      </div>

      <div class="sort-controls">
        <select v-model="searchParams.sortColumn" @change="handleSearch" class="sort-select">
          <option value="">默认排序</option>
          <option value="name">按名称排序</option>
          <option value="createTime">按创建时间排序</option>
        </select>

        <select v-model="searchParams.sortType" @change="handleSearch" class="sort-select" :disabled="!searchParams.sortColumn">
          <option value="ascending">升序</option>
          <option value="descending">降序</option>
        </select>
      </div>
    </div>

    <!-- 非遗文化卡片区域 -->
    <div class="section">
      <div class="title">非遗文化</div>

      <!-- 加载状态 -->
      <div v-if="localLoading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>正在加载本地非遗资料...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="localError" class="error-container">
        <div class="error-icon">⚠️</div>
        <p class="error-message">{{ localError }}</p>
        <button @click="fetchLocalHeritage" class="retry-btn">重试</button>
      </div>

      <!-- 无数据状态 -->
      <div v-else-if="localCards.length === 0" class="empty-container">
        <div class="empty-icon">🏛️</div>
        <p>暂无本地非遗资料</p>
      </div>

      <!-- 卡片网格 -->
      <div v-else class="grid">
        <div
          v-for="item in currentLocalCards"
          :key="item.heritageId"
          class="card"
          :style="{ backgroundImage: `url(${item.coverImage})` }"
          @click="handleCardClick(item)"
        >
          <div class="card-overlay">
            <p class="card-title">{{ item.name }}</p>
            <p class="card-description">{{ item.description }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 了解更多图标区域 -->
    <div class="section">
      <div class="title">了解更多</div>

      <!-- 加载状态 -->
      <div v-if="onlineLoading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>正在加载在线非遗资料...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="onlineError" class="error-container">
        <div class="error-icon">⚠️</div>
        <p class="error-message">{{ onlineError }}</p>
        <button @click="fetchOnlineHeritage" class="retry-btn">重试</button>
      </div>

      <!-- 无数据状态 -->
      <div v-else-if="onlineIcons.length === 0" class="empty-container">
        <div class="empty-icon">🌐</div>
        <p>暂无在线非遗资料</p>
      </div>

      <!-- 图标容器 -->
      <div v-else class="icon-container">
        <div
          v-for="icon in currentOnlineIcons"
          :key="icon.heritageId"
          class="icon"
          :style="{ backgroundImage: `url(${icon.logo})` }"
          @click="handleIconClick(icon)"
          :title="icon.name"
        >
          <div class="icon-overlay">
            <span class="icon-name">{{ icon.name }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页控件 -->
    <div class="pagination">
      <button @click="prevPage" :disabled="currentPage === 1 || localLoading || onlineLoading">
        上一页
      </button>
      <span class="page-info">
        第 {{ currentPage }} 页 / 共 {{ totalPages }} 页
        (本地: {{ localPagination.total }}, 在线: {{ onlinePagination.total }})
      </span>
      <button @click="nextPage" :disabled="currentPage === totalPages || localLoading || onlineLoading">
        下一页
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { getLocalHeritageList, getOnlineHeritageList } from '@/api/public/resource/heritage';
import { ElMessage } from 'element-plus';

// 路由实例
const router = useRouter();

// 响应式数据
const localCards = ref([]);
const onlineIcons = ref([]);
const localLoading = ref(false);
const onlineLoading = ref(false);
const localError = ref(null);
const onlineError = ref(null);

// 搜索参数
const searchParams = reactive({
  name: '',
  sortType: 'ascending',
  sortColumn: ''
});

// 分页信息
const currentPage = ref(1);
const itemsPerPage = 6;

const localPagination = reactive({
  pageNum: 1,
  pageSize: 6,
  total: 0,
  pages: 1
});

const onlinePagination = reactive({
  pageNum: 1,
  pageSize: 6,
  total: 0,
  pages: 1
});

// 计算属性
const totalPages = computed(() => {
  return Math.max(localPagination.pages, onlinePagination.pages);
});

const currentLocalCards = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage;
  const end = start + itemsPerPage;
  return localCards.value.slice(start, end);
});

const currentOnlineIcons = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage;
  const end = start + itemsPerPage;
  return onlineIcons.value.slice(start, end);
});

// 获取本地非遗数据
const fetchLocalHeritage = async (resetPage = false) => {
  if (resetPage) {
    currentPage.value = 1;
    localPagination.pageNum = 1;
  }

  localLoading.value = true;
  localError.value = null;

  try {
    const params = {
      pageNum: localPagination.pageNum,
      pageSize: localPagination.pageSize,
      ...searchParams
    };

    // 过滤空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key];
      }
    });

    const response = await getLocalHeritageList(params);

    if (response && response.result) {
      localCards.value = response.result.records || [];
      localPagination.total = response.result.total || 0;
      localPagination.pages = response.result.pages || 1;
      localPagination.pageNum = response.result.current || 1;
    } else {
      throw new Error('本地非遗数据响应格式错误');
    }

  } catch (err) {
    console.error('获取本地非遗数据失败:', err);
    localError.value = err.response?.data?.msg || err.message || '获取本地非遗数据失败，请重试';
    ElMessage.error(localError.value);
  } finally {
    localLoading.value = false;
  }
};

// 获取在线非遗数据
const fetchOnlineHeritage = async (resetPage = false) => {
  if (resetPage) {
    currentPage.value = 1;
    onlinePagination.pageNum = 1;
  }

  onlineLoading.value = true;
  onlineError.value = null;

  try {
    const params = {
      pageNum: onlinePagination.pageNum,
      pageSize: onlinePagination.pageSize,
      ...searchParams
    };

    // 过滤空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key];
      }
    });

    const response = await getOnlineHeritageList(params);

    if (response && response.result) {
      onlineIcons.value = response.result.records || [];
      onlinePagination.total = response.result.total || 0;
      onlinePagination.pages = response.result.pages || 1;
      onlinePagination.pageNum = response.result.current || 1;
    } else {
      throw new Error('在线非遗数据响应格式错误');
    }

  } catch (err) {
    console.error('获取在线非遗数据失败:', err);
    onlineError.value = err.response?.data?.msg || err.message || '获取在线非遗数据失败，请重试';
    ElMessage.error(onlineError.value);

    // 如果API失败，使用模拟数据进行测试
    console.log('使用在线非遗模拟数据进行测试...');
    onlineIcons.value = [
      {
        heritageId: 'her003',
        name: '端午节',
        coverImage: 'https://example.com/dragonboat-cover.jpg',
        logo: 'https://example.com/dragonboat-logo.png',
        category: 'online',
        description: '中国传统节日习俗',
        onlineUrl: 'www.baidu.com',
        createTime: 1750577557,
        updateTime: 1750577557
      },
      {
        heritageId: 'her005',
        name: '太极拳',
        coverImage: 'https://example.com/taichi-cover.jpg',
        logo: 'https://example.com/taichi-logo.png',
        category: 'online',
        description: '中国传统武术，注重内外兼修',
        onlineUrl: 'https://www.unesco.org',
        createTime: 1750577557,
        updateTime: 1750577557
      },
      {
        heritageId: 'her006',
        name: '京剧',
        coverImage: 'https://example.com/peking-opera-cover.jpg',
        logo: 'https://example.com/peking-opera-logo.png',
        category: 'online',
        description: '中国传统戏曲艺术',
        onlineUrl: 'www.google.com',
        createTime: 1750577557,
        updateTime: 1750577557
      }
    ];
    onlinePagination.total = 3;
    onlinePagination.pages = 1;
    onlinePagination.pageNum = 1;
    onlineError.value = null; // 清除错误状态，显示模拟数据
  } finally {
    onlineLoading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  fetchLocalHeritage(true);
  fetchOnlineHeritage(true);
};

// 分页处理
const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
    localPagination.pageNum = currentPage.value;
    onlinePagination.pageNum = currentPage.value;
    fetchLocalHeritage();
    fetchOnlineHeritage();
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
    localPagination.pageNum = currentPage.value;
    onlinePagination.pageNum = currentPage.value;
    fetchLocalHeritage();
    fetchOnlineHeritage();
  }
};

// 卡片点击处理
const handleCardClick = (item) => {
  console.log('点击了本地非遗:', item);
  // 跳转到详情页，传递非遗ID
  router.push(`/heritage-detail/${item.heritageId}`);
};

// 图标点击处理
const handleIconClick = (item) => {
  console.log('点击了在线非遗:', item);

  if (item.onlineUrl) {
    // 确保URL格式正确
    let url = item.onlineUrl;

    // 如果URL不包含协议，则添加https://
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url;
    }

    try {
      // 在新窗口中打开链接
      window.open(url, '_blank');
      console.log(`跳转到在线非遗链接: ${url}`);
    } catch (error) {
      console.error('打开链接失败:', error);
      ElMessage.error('打开链接失败，请检查网络连接');
    }
  } else {
    // 如果没有onlineUrl，显示提示信息
    ElMessage.warning(`${item.name} 暂无在线链接`);
    console.warn('在线非遗项目缺少onlineUrl字段:', item);
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchLocalHeritage();
  fetchOnlineHeritage();
});
</script>

<style scoped lang="scss">
.cultural-heritage-container {
  padding: 20px;

  .filter-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    gap: 20px;
    flex-wrap: wrap;

    .search-box {
      display: flex;
      align-items: center;
      flex: 1;
      max-width: 400px;

      .search-input {
        flex: 1;
        padding: 10px 15px;
        border: 1px solid #ddd;
        border-radius: 25px 0 0 25px;
        outline: none;
        font-size: 14px;

        &:focus {
          border-color: #8a6de3;
          box-shadow: 0 0 0 2px rgba(138, 109, 227, 0.2);
        }
      }

      .search-btn {
        padding: 10px 15px;
        background: #8a6de3;
        color: white;
        border: none;
        border-radius: 0 25px 25px 0;
        cursor: pointer;
        transition: background-color 0.3s;

        &:hover {
          background: #7a5dd3;
        }
      }
    }

    .sort-controls {
      display: flex;
      gap: 10px;

      .sort-select {
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 6px;
        outline: none;
        font-size: 14px;

        &:focus {
          border-color: #8a6de3;
        }

        &:disabled {
          background-color: #f5f5f5;
          cursor: not-allowed;
        }
      }
    }
  }

  .section {
    margin-bottom: 40px;

    .title {
      font-size: 1.5rem;
      font-weight: bold;
      margin-bottom: 20px;
      color: #8a6de3;
      border-bottom: 2px solid #8a6de3;
      padding-bottom: 10px;
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;

    .loading-spinner {
      width: 32px;
      height: 32px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #8a6de3;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 12px;
    }

    p {
      color: #666;
      font-size: 14px;
    }
  }

  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;

    .error-icon {
      font-size: 36px;
      margin-bottom: 12px;
    }

    .error-message {
      color: #e74c3c;
      font-size: 14px;
      margin-bottom: 15px;
      text-align: center;
    }

    .retry-btn {
      padding: 8px 16px;
      background: #8a6de3;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.3s;

      &:hover {
        background: #7a5dd3;
      }
    }
  }

  .empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;

    .empty-icon {
      font-size: 36px;
      margin-bottom: 12px;
    }

    p {
      color: #666;
      font-size: 14px;
    }
  }

  .grid {
    display: grid;
    grid-template-columns: repeat(3, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
  }

  .card {
    height: 200px;
    background-color: #f8f9fa;
    border-radius: 12px;
    overflow: hidden;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    position: relative;

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);

      .card-overlay {
        background: rgba(0, 0, 0, 0.7);
      }
    }

    .card-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 20px;
      transition: background-color 0.3s ease;
      text-align: center;
    }

    .card-title {
      margin: 0 0 8px 0;
      font-size: 1.2rem;
      font-weight: 600;
      color: white;
      line-height: 1.3;
    }

    .card-description {
      margin: 0;
      font-size: 0.9rem;
      color: rgba(255, 255, 255, 0.9);
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }

  .icon-container {
    display: flex;
    gap: 15px;
    justify-content: space-around;
    flex-wrap: wrap;
  }

  .icon {
    width: 100px;
    height: 100px;
    background-color: #f8f9fa;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    position: relative;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      transform: translateY(-5px) scale(1.05);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);

      .icon-overlay {
        opacity: 1;
      }
    }

    .icon-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(138, 109, 227, 0.9);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;

      .icon-name {
        color: white;
        font-size: 12px;
        font-weight: 600;
        text-align: center;
        padding: 5px;
      }
    }
  }

  .pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    padding: 20px 0;
    margin-top: 30px;
    border-top: 1px solid #eee;

    button {
      padding: 10px 20px;
      background: #8a6de3;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s;

      &:hover:not(:disabled) {
        background: #7a5dd3;
        transform: translateY(-1px);
      }

      &:disabled {
        background: #ccc;
        cursor: not-allowed;
        transform: none;
      }
    }

    .page-info {
      color: #666;
      font-size: 14px;
      text-align: center;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 响应式设计
@media (max-width: 768px) {
  .cultural-heritage-container {
    padding: 15px;

    .filter-section {
      flex-direction: column;
      align-items: stretch;

      .search-box {
        max-width: none;
      }

      .sort-controls {
        justify-content: space-between;
      }
    }

    .grid {
      grid-template-columns: 1fr;
    }

    .icon-container {
      justify-content: center;
    }

    .pagination {
      flex-direction: column;
      gap: 10px;
    }
  }
}
</style>