<template>
  <el-dialog :model-value="visible" @update:model-value="$emit('update:visible', $event)" :title="dialogTitle" width="600px"
    :close-on-click-modal="false">
    <el-form :model="formData" ref="formRef" :rules="rules" label-width="120px" class="create-form">
      <el-form-item label="签到名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入签到名称" maxlength="40" class="form-input"></el-input>
      </el-form-item>

      <el-form-item label="签到班级" prop="classIds">
        <el-select v-model="formData.classIds"
          placeholder="请选择班级"
          class="form-input"
          multiple 
          filterable
          clearable 
          >
          <el-option v-for="item in classList" :key="item.id" :label="item.name" :value="item.id">
            <span>{{ item.name }}</span>
            <span style="float: right; color: #8492a6; font-size: 12px">
              ({{ item.teacherName }} - {{ item.courseName }})
            </span>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="签到方式" prop="method">
        <el-radio-group v-model="formData.method" class="form-input">
          <el-radio label="快捷签到">快捷签到</el-radio>
          <el-radio label="签到码签到">签到码签到</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="签到日期" prop="date">
        <el-date-picker v-model="formData.date" type="date" placeholder="选择签到日期" format="YYYY-MM-DD"
          value-format="YYYY-MM-DD" class="form-input"></el-date-picker>
      </el-form-item>

      <el-form-item label="开始时间" prop="startTime">
        <el-time-picker v-model="formData.startTime" placeholder="选择开始时间" format="HH:mm" value-format="HH:mm"
          class="form-input"></el-time-picker>
      </el-form-item>

      <el-form-item label="结束时间" prop="endTime">
        <el-time-picker v-model="formData.endTime" placeholder="选择结束时间" format="HH:mm" value-format="HH:mm"
          class="form-input"></el-time-picker>
      </el-form-item>

      <el-form-item label="迟到时间" prop="lateTime">
        <el-time-picker v-model="formData.lateTime" placeholder="选择迟到时间阈值" format="HH:mm" value-format="HH:mm"
          class="form-input"></el-time-picker>
      </el-form-item>
    </el-form>

    <div class="form-hint">超过开始时间但在此时限内视为迟到</div>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit">
        {{ submitButtonText }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, onMounted ,nextTick} from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { fetchClassList } from '@/api/teacher/class'
import {createAttendance,updateAttendance } from '@/api/teacher/attendance'
import { convertToTimestamp, formatTime } from '@/utils/dateUtils'; // 导入工具方法

const route = useRoute();
const classList = ref([]);
const courseId = route.params.courseId;

const emit = defineEmits(['submit', 'cancel', 'update:visible']);

const props = defineProps({
  visible: Boolean,
  mode: { 
    type: String,
    default: 'create'
  },
  editData: {
    type: Object,
    default: null
  }
});

const dialogTitle = computed(() => props.mode === 'edit' ? '修改签到' : '创建签到');
const submitButtonText = computed(() => props.mode === 'edit' ? '确定修改' : '确定创建');

const formRef = ref(null);
const formData = reactive({
  name: '',
  classIds: [], 
  method: '快捷签到',
  date: '',
  startTime: null,
  endTime:null,
  lateTime: null
});

const rules = reactive({
  name: [
    { required: true, message: '请输入签到名称', trigger: 'blur' }
  ],
  className: [
    { required: true, message: '请选择签到班级', trigger: 'change' }
  ],
  date: [
    { required: true, message: '请选择签到日期', trigger: 'change' }
  ],
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' }
  ]
});

watch(() => props.editData, (newVal) => {
  if (newVal && props.mode === 'edit') {
    const createDateFromMillisecondTimestampOrUndefined = (timestamp) => {
      if (!timestamp) return undefined;
      const date = new Date(timestamp);
      // 确保返回的时间格式是 HH:mm 格式的字符串
      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
    };

    Object.assign(formData, {
      name: newVal.name,
      classIds: newVal.classIds || [],
      method: newVal.checkType === 0 ? '快捷签到' : '签到码签到',
      date: newVal.startTime ? formatTime(newVal.startTime).split(' ')[0] : '',
      startTime: createDateFromMillisecondTimestampOrUndefined(newVal.startTime),
      endTime: createDateFromMillisecondTimestampOrUndefined(newVal.endTime),
      lateTime: createDateFromMillisecondTimestampOrUndefined(newVal.lateTime)
    });
  }
}, { immediate: true });


const handleSubmit = async () => {
  try {
    await nextTick();
    if (!formRef.value) {
      throw new Error('表单引用未初始化');
    }
    
    await formRef.value.validate();


    
    const data = {
      name: formData.name,
      classIds: formData.classIds,
      courseId: courseId,
      checkType: formData.method === '快捷签到' ? 0 : 1,
      checkDate: convertToTimestamp(formData.date, '00:00'),
      startTime: convertToTimestamp(formData.date, formData.startTime),
      endTime: convertToTimestamp(formData.date, formData.endTime),
      lateTime: convertToTimestamp(formData.date, formData.lateTime || '00:00')
    };

    // 根据模式调用不同API
    if (props.mode === 'edit') {
      data.id = props.editData.id; // 添加签到ID用于编辑
      await updateAttendance(data);
      ElMessage.success('修改成功！');
    } else {
      await createAttendance(data);
      ElMessage.success('创建成功！');
    }
    
    emit('submit'); // 通知父组件操作成功
    handleCancel();
  } catch (error) {
    console.error('提交失败:', error);
    ElMessage.error(`操作失败: ${error.message}`);
  }
};

const handleCancel = () => {
  formRef.value.resetFields();
  emit('update:visible', false);
  emit('cancel');
};

onMounted(async () => {
  try {
    const response = await fetchClassList({courseId});
    classList.value = response.result;
    console.log('获取到的完整班级数据:', JSON.parse(JSON.stringify(classList.value)));
  } catch (error) {
    console.error('获取班级列表失败:', error);
    ElMessage.error('获取班级列表失败');
  }
});
</script>

<style lang="scss" scoped>
.create-form {
  margin-top: 10px;

  .form-input {
    width: 400px;
  }

  :deep(.el-radio-group.form-input) {
    width: 400px;
    display: flex;
  }

  :deep(.el-date-editor.form-input),
  :deep(.el-time-picker.form-input) {
    width: 400px !important;
  }

  :deep(.el-select.form-input) {
    width: 400px;
  }
}

.form-hint {
  color: #606266;
  font-size: 12px;
  margin-top: 5px;
  margin-left: 120px;
}
</style>