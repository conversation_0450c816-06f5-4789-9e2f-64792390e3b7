<template>
  <div class="course-create-container">
    <div class="card">
      <h1 class="title">创建课程</h1>
      <p class="subtitle">仅需一步即可创建课程</p>

      <div class="form-group">
        <input type="text" placeholder="输入课程名称" v-model="courseName" class="input-field" />
        <button @click="createCourse" class="primary-button">
          创建课程
        </button>
      </div>


      <div class="selection-container">
        <div class="school-semester-group">
          <label>所属学校：</label>
          <select v-model="selectedSchool" class="select-field">
            <option v-for="school in schools" :key="school" :value="school">
              {{ school }}
            </option>
          </select>
        </div>

        <div class="school-semester-group">
          <label>所属学期：</label>
          <span>2024-2025</span>
          <select v-model="selectedSemester" class="select-field">
            <option v-for="semester in semesters" :key="semester" :value="semester">
              {{ semester }}
            </option>ac
          </select>
        </div>
      </div>


    </div>
    <CourseCreateSuccess ref="successPopup" :invitationCode="invitationCode" :courseNumber="courseNumber" />
  </div>
</template>

<script setup>
import CourseCreateSuccess from './CourseCreateSuccess.vue';
import { ref, nextTick } from 'vue';
import { useCourseStore } from '@/stores/courseStore'
import { nanoid } from 'nanoid';//用于生成课程码

const courseStore = useCourseStore()
const courseName = ref('');
const selectedSchool = ref('桂林电子科技大学 - 艺术与设计学院');
const selectedSemester = ref('第一学期');
const successPopup = ref(null)

const invitationCode=ref("");
const courseNumber=ref('')

const schools = [
  '桂林电子科技大学 - 艺术与设计学院',
  '北京大学 - 计算机科学技术学院',
  '清华大学 - 电子工程系',
  '复旦大学 - 经济学院',
  '浙江大学 - 信息与电子工程学院'
]

const semesters = [
  '第一学期',
  '第二学期',
  '第三学期',
];

const createCourse = async () => {
  if (!courseName.value.trim()) {
    alert('请输入课程名称')
    return
  }

  try {
    const courseData = {
      name: courseName.value,
      major: selectedSchool.value.split(' - ')[1], // 提取学校名称
      semester: selectedSemester.value,
      // invitationCode: invitationCode.value,
    }
    //console.log();

   const res =  await courseStore.createNewCourse(courseData)
    // 在创建成功后
    invitationCode.value = res.invitationCode; // 设置邀请码
    courseNumber.value = res.id;                 // 设置课程ID


    await nextTick()
    // 显示成功弹窗
    if (successPopup.value?.openPopup) {
      successPopup.value.openPopup()
      
    }
  } catch (error) {
    console.error('创建课程失败:', error)
    alert('创建课程失败，请稍后重试')
  }
}
</script>

<style lang="scss" scoped>
// 定义变量

$primary-color: #165DFF;
$secondary-color: #4080FF;
$text-color: #333;
$border-color: #E5E6EB;
$bg-color: #ffffff;
$radius: 8px;
$spacing: 16px;

.course-create-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  padding: $spacing;
  background-color: $bg-color;

  .card {
    position: absolute;
    padding: calc($spacing * 2);
    top: 50%;
    transform: translateY(-50%);

    .title {
      color: $text-color;
      font-size: 24px;
      font-weight: 600;
      text-align: center;
      margin-bottom: 8px;
    }

    .subtitle {
      text-align: center;
      color: #666;
      font-size: 14px;
      margin-bottom: calc($spacing * 2);
    }

    .form-group {
      margin-bottom: $spacing;
      display: flex;
      border-radius: 50px; // 父容器圆角
      overflow: hidden; // 确保子元素不溢出圆角
      border: 1px solid $border-color; // 可选：添加边框

      .input-field {
        flex: 7;
        padding: 10px 12px;
        border: none;
        border-radius: 50px 0 0 50px; // 只有左侧圆角
        font-size: 14px;
        transition: box-shadow 0.2s;

        &:focus {
          outline: none;
          box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.2);
        }
      }

      .primary-button {
        flex: 3;
        background-color: $primary-color;
        color: white;
        border: none;
        border-radius: 0 50px 50px 0; // 只有右侧圆角
        padding: 12px 0;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
          background-color: $secondary-color;
        }

        &:active {
          transform: scale(0.98);
        }
      }
    }
  }

  .selection-container {
    display: flex;
    justify-content: space-between;
    padding: $spacing;

    .school-semester-group {
      display: flex;
      align-items: center;
      padding: 10px 12px;
      margin-bottom: $spacing;

      label {
        margin-right: 10px;
      }

      .select-field {
        flex: 1;
        border: none;
        padding: 0;
        border-radius: 0;
        font-size: 14px;
        transition: border-color 0.2s, box-shadow 0.2s;

        &:focus {
          outline: none;
          box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.2);
        }
      }
    }
  }

}
</style>