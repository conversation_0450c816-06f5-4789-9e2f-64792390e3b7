//学生端个人管理
import request from '@/api/service'

// 通过学生ID获取学生信息
// 获取学生基本信息
export const getStudentInfo = () => {
  return request({
    url: '/student/account/info',
    method: 'get'
  })
}

// 修改学生信息
export const updateStudentInfo = (data) => {
  return request({
    url: '/student/account/update',
    method: 'post',
    data: {
      major: data.major,
      name: data.name,
      gender: data.gender,
      enrollmentYear: data.enrollmentYear, 
      education: data.education,
      introduction: data.introduction
    }
  })
}

// 上传学生证件照
export const uploadStudentPhoto = (file) => {
  const formData = new FormData();
  formData.append('file', file.raw || file); // 兼容两种文件对象格式

  return request({
    url: '/student/account/card/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data', // 明确设置内容类型
      // 如果需要token认证可添加：
      // 'Authorization': `Bearer ${getToken()}`
    },
    transformRequest: [data => data] // 防止axios自动转换
  });
}



