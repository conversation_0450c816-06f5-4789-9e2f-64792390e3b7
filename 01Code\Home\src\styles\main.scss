// src/styles/main.scss
@use './navbar';

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#app {
  max-width: 100%; // 或你需要的最大宽度
  margin: 0 auto; // 水平居中
  width: 100%;
}

body {
  font-family: $font-main;
  color: $text-color;
  background-color: $bg-color;
  line-height: 1.5;
}

// 全局容器
.container {
  max-width: 100%;
  margin: 0 auto;
  width: 100%;
}

// 通知中心样式
.notification-center {
  position: relative;
  display: inline-block;

  button {
    background: none;
    border: none;
    cursor: pointer;
    position: relative;
    padding: 0.5rem;
  }

  .badge {
    position: absolute;
    top: 0;
    right: 0;
    background-color: $error-color;
    color: white;
    border-radius: 50%;
    width: 1.25rem;
    height: 1.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
  }
}

.notification-dropdown {
  position: absolute;
  right: 0;
  top: 100%;
  width: 320px;
  background: white;
  border: 1px solid $border-color;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid $border-color;

  h3 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
  }

  button {
    font-size: 0.875rem;
    color: $primary-color;
  }
}

.notification-list {
  max-height: 400px;
  overflow-y: auto;
}

.notification-item {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid $border-color;

  &.unread {
    background-color: #f0f9ff;
  }

  p {
    margin: 0 0 0.25rem 0;
  }

  small {
    color: #6b7280;
    font-size: 0.75rem;
  }
}