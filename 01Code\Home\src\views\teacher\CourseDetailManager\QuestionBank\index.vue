<template>
    <div class="question-bank-wrapper">
        <div class="tabs">
            <el-tabs v-model="activeTab">
                <el-tab-pane label="我的题库" name="myQuestions" />
                <el-tab-pane label="我的试卷库" name="myPapers" />
            </el-tabs>

            <div class="tab-actions">
                <el-input v-model="tabSearch" placeholder="搜索题目 / 项目" style="width: 220px; margin-right: 12px" clearable />
                <el-button type="primary" style="margin-right: 10px" @click="handleNewTestData">
                    <el-icon>
                        <Plus />
                    </el-icon>新增试题
                </el-button>
                <el-dropdown>
                    <el-button type="primary">
                        工具
                        <el-icon style="margin-left: 4px">
                            <ArrowDown />
                        </el-icon>
                    </el-button>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item>批量导出</el-dropdown-item>
                            <el-dropdown-item>批量导入</el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>
        </div>

        <FilterBar v-model="filters" :questionTypes="currentQuestionTypes" :owners="owners" :total="filteredData.length"
            @update="handleFilterChange" @batchMark="batchMark" @batchPreview="batchPreview" @batchDelete="batchDelete"
            @batchUnlink="batchUnlink" @toggleSortDesc="toggleSortDesc" />

        <div class="question-list">
            <div v-for="(item, index) in filteredData" :key="index" class="question-card" @click="handlePreview(item)">
                <el-checkbox v-model="item.checked" class="card-checkbox" @click.stop />
                <div class="question-title">
                    <el-icon v-if="['整套试卷'].includes(item.type)" style="margin-right: 6px">
                        <Folder />
                    </el-icon>
                    {{ item.title }}
                </div>
                <div class="question-meta">
                    创建时间: {{ item.createTime }} 更新时间: {{ item.updateTime }}
                </div>
            </div>
        </div>

        <el-dialog v-model="previewVisible" title="预览" width="60%" :before-close="closePreview">
            <template v-if="currentPreview">
                <div v-if="currentPreview.type === '整套试卷'">
                    <iframe :src="currentPreview.content" frameborder="0" style="width: 100%; height: 400px" />
                </div>
                <div v-else>
                    <p><strong>题目：</strong>{{ currentPreview.title }}</p>
                    <p><strong>题型：</strong>{{ currentPreview.type }}</p>
                    <p><strong>内容：</strong>{{ currentPreview.content }}</p>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
  
<script setup>
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import FilterBar from '../WorkAndExam/components/FilterBar.vue'
import { ArrowDown, Plus, Folder } from '@element-plus/icons-vue'

const router = useRouter()
const activeTab = ref('myQuestions')
const tabSearch = ref('')
const previewVisible = ref(false)
const currentPreview = ref(null)

const filters = ref({ qType: 'all', owner: 'all', startTime: null, endTime: null, sortDesc: true })
const owners = ref(['张三', '李四', '王五', '赵六', '钱七'])

const tableData = ref([
    { title: '单选题示例', type: '单选题', content: '选择一个正确答案', createTime: '2025-06-10', updateTime: '2025-06-11', owner: '张三', checked: false },
    { title: '多选题示例', type: '多选题', content: '选择多个正确答案', createTime: '2025-06-12', updateTime: '2025-06-13', owner: '李四', checked: false },
    { title: '判断题示例', type: '判断题', content: '判断对错', createTime: '2025-06-14', updateTime: '2025-06-15', owner: '王五', checked: false },
    { title: '填空题示例', type: '填空题', content: '填入空白', createTime: '2025-06-16', updateTime: '2025-06-17', owner: '赵六', checked: false },
    { title: '问答题示例', type: '问答题', content: '简述内容', createTime: '2025-06-18', updateTime: '2025-06-19', owner: '钱七', checked: false },
    { title: '整套试卷', type: '整套试卷', content: 'http://example.com/full-paper-doc', createTime: '2025-06-22', updateTime: '2025-06-23', owner: '吴九', checked: false },
])

const tabQuestionTypeMap = {
    myQuestions: ['单选题', '多选题', '判断题', '填空题', '问答题'],
    myPapers: ['整套试卷']
}

const currentQuestionTypes = computed(() => {
    const types = tabQuestionTypeMap[activeTab.value] || []
    return [{ label: '全部', value: 'all' }, ...types.map(t => ({ label: t, value: t }))]
})

const filteredData = computed(() => {
    let result = tableData.value
    const allowedTypes = tabQuestionTypeMap[activeTab.value] || []
    result = result.filter(item => allowedTypes.includes(item.type))
    if (tabSearch.value) {
        const keyword = tabSearch.value.trim().toLowerCase()
        result = result.filter(item => item.title.toLowerCase().includes(keyword))
    }
    if (filters.value.qType !== 'all') {
        result = result.filter(item => item.type === filters.value.qType)
    }
    if (filters.value.owner !== 'all') {
        if (filters.value.owner === 'me') {
            result = result.filter(item => item.owner === '张三')
        } else {
            result = result.filter(item => item.owner === filters.value.owner)
        }
    }
    if (filters.value.startTime) {
        const start = new Date(filters.value.startTime).getTime()
        result = result.filter(item => new Date(item.createTime).getTime() >= start)
    }
    if (filters.value.endTime) {
        const end = new Date(filters.value.endTime).getTime()
        result = result.filter(item => new Date(item.createTime).getTime() <= end)
    }
    result = result.sort((a, b) => {
        const timeA = new Date(a.createTime).getTime()
        const timeB = new Date(b.createTime).getTime()
        return filters.value.sortDesc ? timeB - timeA : timeA - timeB
    })
    return result
})

watch(activeTab, () => {
    filters.value.qType = 'all'
})

const handleFilterChange = (newFilters) => {
    Object.assign(filters.value, newFilters)
}

const handlePreview = (item) => {
    currentPreview.value = item
    previewVisible.value = true
}

const closePreview = () => {
    previewVisible.value = false
    currentPreview.value = null
}

const handleNewTestData = () => {
    router.push('/new-test')
}

const batchMark = () => { }
const batchPreview = () => { }
const batchDelete = () => { }
const batchUnlink = () => { }
const toggleSortDesc = () => {
    filters.value.sortDesc = !filters.value.sortDesc
}
</script>
  
<style scoped>
.question-bank-wrapper {
    background-color: #fff;
    padding: 1.2vw;
    border-radius: 0.5vw;
    min-height: 60vh;
}

.tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1vw;
}

.tab-actions {
    display: flex;
    align-items: center;
}

.question-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.question-card {
    background: white;
    padding: 16px;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    position: relative;
}

.card-checkbox {
    position: absolute;
    top: 16px;
    left: 16px;
}

.question-title {
    margin-left: 32px;
    font-weight: 600;
    font-size: 15px;
    color: #333;
    margin-bottom: 6px;
    display: flex;
    align-items: center;
}

.question-meta {
    margin-left: 32px;
    font-size: 12px;
    color: #888;
}
</style>