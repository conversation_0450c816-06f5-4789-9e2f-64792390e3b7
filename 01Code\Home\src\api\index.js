// import axios from 'axios'

// const api = axios.create({
//   baseURL: import.meta.env.VITE_API_BASE_URL,
//   timeout: 10000
// })

// // 请求拦截器
// api.interceptors.request.use(config => {
//   //从localStorage获取存储的 token
//   const token = localStorage.getItem('token')
//   //如果有token，则在请求头中添加token:格式为 Bearer <token>（JWT 标准格式）
//   if (token) {
//     config.headers.Authorization = `Bearer ${token}`
//   }
//   //返回修改后的config对象，继续发送请求
//   return config
// })

// // 响应拦截器
// api.interceptors.response.use(
//   response => response.data,
//   error => {
//     // 统一错误处理
//     if (error.response) {
//       switch (error.response.status) {
//         case 401:
//           // token过期处理
//           router.push('/login')
//           break
//         case 403:
//           // 权限不足处理
//           showToast('无访问权限')
//           break
//         case 500:
//           // 服务器错误处理
//           showToast('服务器错误，请稍后重试')
//           break
//         default:
//           console.error('请求错误:', error)
//       }
//     }
//     return Promise.reject(error)
//   }
// )

// export default api