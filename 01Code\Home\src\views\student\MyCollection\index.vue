<template>
  <div class="collection-app">
    <div class="app-header">
      <input 
        type="text" 
        class="search-input" 
        placeholder="请在此处输入文件名称搜索"
        v-model="searchQuery"
      >
      <div class="action-buttons">
        <button class="upload-btn" @click="handleUpload">上传文件</button>
        <button class="new-collection-btn" @click="handleNewFolder(null)">+新建收藏夹</button>
      </div>
    </div>
    
    <div v-if="selectedItems.length > 0" class="action-bar">
      <button class="action-btn download-btn" @click="handleDownload">下载</button>
      <button class="action-btn delete-btn" @click="handleDelete">删除</button>
      <button class="action-btn rename-btn" @click="handleRename">重命名</button>
      <button class="action-btn move-btn" @click="handleMove">移动到</button>
    </div>
    
    <div class="collection-list">
      <table class="collection-table">
        <thead>
          <tr>
            <th class="checkbox-header"></th>
            <th>文件名</th>
            <th>大小</th>
            <th>创建时间</th>
          </tr>
        </thead>
        <tbody>
          <CollectionItem
            v-for="item in filteredCollections"
            :key="item.id"
            :item="item"
            :isSelected="selectedItems.some(i => i.id === item.id)"
            :selectedItems="selectedItems"
            :renamingItem="renamingItem"
            @select-change="handleSelectChange"
            @toggle-folder="toggleFolder"
            @rename="handleRenameConfirm"
          />
        </tbody>
      </table>
    </div>
    
    <DownloadModal 
      v-if="showDownloadModal"
      :visible="showDownloadModal"
      :initialPackageName="packageName"
      @close="showDownloadModal = false"
      @confirm="handleMultiDownload"
    />
    
    <DeleteConfirmation 
      v-if="showDeleteConfirmation"
      :visible="showDeleteConfirmation"
      :folderName="deleteMessage"
      @confirm="confirmDelete"
      @cancel="showDeleteConfirmation = false"
    />
    
    <MoveModal 
      v-if="showMoveModal"
      :visible="showMoveModal"
      :folders="filteredCollections.filter(item => item.type === 'folder')"
      @confirm="confirmMove"
      @cancel="showMoveModal = false"
      @new-folder="handleNewFolder"
    />
  </div>
</template>

<script>
import CollectionItem from './components/CollectionItem.vue';
import DownloadModal from './components/DownloadModal.vue';
import DeleteConfirmation from './components/DeleteConfirmation.vue';
import MoveModal from './components/MoveModal.vue';
import { generateDownloadName } from '@/utils/fileUtils.js';

export default {
  name: 'App',
  components: {
    CollectionItem,
    DownloadModal,
    DeleteConfirmation,
    MoveModal
  },
  data() {
    return {
      collections: [
        { 
          id: 1, 
          name: '新建收藏夹', 
          type: 'folder', 
          selected: false, 
          createdAt: new Date(),
          expanded: false,
          children: [
            { id: 3, name: '2.子文件.DOCX', type: 'file', selected: false, size: '2MB', createdAt: new Date() }
          ] 
        },
        { id: 2, name: '1.专业内容.DOCX', type: 'file', selected: false, size: '1.5MB', createdAt: new Date() }
      ],
      selectedItems: [],
      searchQuery: '',
      showDownloadModal: false,
      packageName: '',
      showDeleteConfirmation: false,
      showMoveModal: false,
      moveTarget: null,
      newFolderName: '新建文件夹',
      username: 'student123',
      realName: '张三',
      renamingItem: null,
      deleteTarget: null
    };
  },
  computed: {
    filteredCollections() {
      return this.collections.filter(item => 
        item.name.toLowerCase().includes(this.searchQuery.toLowerCase())
      );
    },
    deleteMessage() {
      if (this.deleteTarget) {
        return this.deleteTarget.name;
      }
      return this.selectedItems.length > 1 
        ? `选中的${this.selectedItems.length}个项目` 
        : this.selectedItems[0]?.name || '选中的项目';
    }
  },
  methods: {
    handleSelectChange(id) {
      const findAndToggle = (items) => {
        items.forEach(item => {
          if (item.id === id) {
            item.selected = !item.selected;
          } else if (item.children) {
            findAndToggle(item.children);
          }
        });
      };
      findAndToggle(this.collections);
      this.updateSelectedItems();
    },
    handleDownload() {
      if (this.selectedItems.length === 0) return;
      
      if (this.selectedItems.length === 1) {
        const item = this.selectedItems[0];
        const downloadName = generateDownloadName(
          this.username, 
          this.realName, 
          item.name
        );
        alert(`正在下载: ${downloadName}`);
      } else {
        this.showDownloadModal = true;
      }
    },
    handleMultiDownload(packageName) {
      if (!packageName) {
        alert('请输入文件包名');
        return;
      }
      const downloadName = generateDownloadName(
        this.username, 
        this.realName, 
        packageName
      );
      alert(`正在下载压缩包: ${downloadName}`);
      this.showDownloadModal = false;
      this.clearSelection();
    },
    handleDelete() {
      if (this.selectedItems.length === 0) return;
      this.deleteTarget = null;
      this.showDeleteConfirmation = true;
    },
    confirmDelete() {
      const idsToDelete = this.deleteTarget
        ? [this.deleteTarget.id]
        : this.selectedItems.map(item => item.id);
      
      // 递归删除节点
      const deepRemove = (items) => {
        return items.filter(item => {
          // 如果当前项在删除列表中，则跳过（即删除）
          if (idsToDelete.includes(item.id)) {
            return false;
          }
          // 如果有子项，递归处理子项
          if (item.children) {
            item.children = deepRemove(item.children);
          }
          return true;
        });
      };
      
      this.collections = deepRemove(this.collections);
      this.showDeleteConfirmation = false;
      this.clearSelection();
    },
    handleRename() {
      if (this.selectedItems.length === 1) {
        this.renamingItem = this.selectedItems[0];
      }
    },
    handleRenameConfirm(newName) {
      if (this.renamingItem) {
        this.renamingItem.name = newName;
        this.renamingItem = null;
      }
    },
    handleMove() {
      if (this.selectedItems.length === 0) return;
      this.showMoveModal = true;
    },
    handleNewFolder(parentFolder) {
      const newFolder = {
        id: Date.now(),
        name: this.newFolderName,
        type: 'folder',
        selected: false,
        createdAt: new Date(),
        expanded: false,
        children: []
      };
      if (parentFolder) {
        parentFolder.children.push(newFolder);
      } else {
        this.collections.push(newFolder);
      }
      alert(`新建文件夹: ${newFolder.name}`);
    },
    confirmMove(targetFolder) {
      if (!targetFolder) {
        alert('请选择目标文件夹');
        return;
      }
      alert(`已将${this.selectedItems.length}个项目移动到${targetFolder.name}`);
      this.showMoveModal = false;
      this.clearSelection();
    },
    handleUpload() {
      alert('上传文件功能');
    },
    clearSelection() {
      const flattenItems = (items) => {
        return items.reduce((acc, item) => {
          acc.push(item);
          if (item.children) {
            acc = acc.concat(flattenItems(item.children));
          }
          return acc;
        }, []);
      };
      flattenItems(this.collections).forEach(item => {
        item.selected = false;
      });
      this.selectedItems = [];
    },
    updateSelectedItems() {
      const flattenItems = (items) => {
        return items.reduce((acc, item) => {
          acc.push(item);
          if (item.children) {
            acc = acc.concat(flattenItems(item.children));
          }
          return acc;
        }, []);
      };
      this.selectedItems = flattenItems(this.collections).filter(item => item.selected);
    },
    toggleFolder(id) {
      const findAndToggle = (items) => {
        items.forEach(item => {
          if (item.id === id) {
            item.expanded = !item.expanded;
          } else if (item.children) {
            findAndToggle(item.children);
          }
        });
      };
      findAndToggle(this.collections);
    }
  },
  watch: {
    collections: {
      handler() {
        this.updateSelectedItems();
      },
      deep: true
    }
  }
}
</script>

<style scoped>
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  background-color: #f5f7fa;
  color: #333;
}

.collection-app {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.search-input {
  width: 300px;
  padding: 10px 15px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.upload-btn, .new-collection-btn {
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.upload-btn {
  background-color: #f0f2f5;
  color: #606266;
}

.new-collection-btn {
  background-color: #409eff;
  color: white;
}

.upload-btn:hover {
  background-color: #e7eaf3;
}

.new-collection-btn:hover {
  background-color: #3a8ee6;
}

.action-bar {
  background-color: #f5f7fa;
  padding: 10px 15px;
  margin-bottom: 15px;
  border-radius: 4px;
  display: flex;
  gap: 10px;
}

.action-btn {
  padding: 8px 15px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.3s;
}

.action-btn:hover {
  background-color: #f5f7fa;
}

.download-btn {
  color: #409eff;
}

.delete-btn {
  color: #f56c6c;
}

.rename-btn {
  color: #67c23a;
}

.move-btn {
  color: #909399;
}

.collection-list {
  overflow-x: auto;
}

.collection-table {
  width: 100%;
  border-collapse: collapse;
}

.collection-table th {
  text-align: left;
  padding: 12px 15px;
  background-color: #f5f7fa;
  font-weight: 600;
  color: #606266;
  border-bottom: 2px solid #dcdfe6;
}

.collection-table td {
  padding: 12px 15px;
  border-bottom: 1px solid #ebeef5;
}
</style>