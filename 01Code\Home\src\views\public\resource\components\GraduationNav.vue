<template>
  <div class="graduation-nav">
    <!-- 项目封面 -->
    <div class="project-cover">
      <img :src="coverImage" :alt="projectTitle" class="cover-image" />
      <div class="cover-overlay">
        <h3 class="cover-title">{{ projectTitle }}</h3>
      </div>
    </div>
    
    <!-- 导航按钮 -->
    <div class="nav-buttons">
      <button
        v-for="(item, index) in navItems"
        :key="item.key"
        :class="['nav-btn', { active: activeIndex === index }]"
        @click="handleNavClick(index, item.key)"
      >
        {{ item.label }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const props = defineProps({
  coverImage: {
    type: String,
    default: '/src/assets/img/default-cover.jpg'
  },
  projectTitle: {
    type: String,
    default: '项目标题'
  }
})

const emit = defineEmits(['nav-click'])
const activeIndex = ref(0)

const navItems = [
  { key: 'introduction', label: '项目介绍' },
  { key: 'thesis', label: '毕设论文' },
  { key: 'gallery', label: '项目图片' }
]

const handleNavClick = (index, tabKey) => {
  activeIndex.value = index
  emit('nav-click', tabKey)
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.graduation-nav {
  width: 100%;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  
  .project-cover {
    position: relative;
    width: 100%;
    height: 280px;
    overflow: hidden;
    
    .cover-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }
    
    .cover-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
      padding: 24px 20px 20px;
      
      .cover-title {
        color: white;
        font-size: 20px;
        font-weight: 600;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }
    }
    
    &:hover .cover-image {
      transform: scale(1.05);
    }
  }
  
  .nav-buttons {
    padding: 0;
    
    .nav-btn {
      width: 100%;
      padding: 16px 20px;
      background: white;
      border: none;
      border-bottom: 1px solid $border-color;
      font-size: 16px;
      font-weight: 500;
      color: $text-color;
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: left;
      
      &:last-child {
        border-bottom: none;
      }
      
      &:hover {
        background: rgba($primary-color, 0.05);
        color: $primary-color;
      }
      
      &.active {
        background: $primary-color;
        color: white;
        font-weight: 600;
        position: relative;
        
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 4px;
          background: rgba(255, 255, 255, 0.3);
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .graduation-nav {
    .project-cover {
      height: 200px;
      
      .cover-overlay .cover-title {
        font-size: 18px;
      }
    }
    
    .nav-buttons .nav-btn {
      padding: 14px 16px;
      font-size: 14px;
    }
  }
}
</style>
