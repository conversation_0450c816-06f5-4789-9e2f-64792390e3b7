{"_id": "lightningcss", "_rev": "2188063-630412605dda706c018fa98f", "dist-tags": {"latest": "1.30.1"}, "name": "lightningcss", "time": {"created": "2022-08-22T23:33:52.092Z", "modified": "2025-05-14T03:40:01.221Z", "0.0.0": "2022-08-22T23:33:46.640Z", "1.14.0": "2022-09-08T05:19:27.637Z", "1.15.0": "2022-09-15T04:16:39.764Z", "1.15.1": "2022-09-16T03:49:31.058Z", "1.16.0": "2022-09-20T03:23:22.629Z", "1.16.1": "2022-11-06T18:02:15.102Z", "1.17.0": "2022-11-29T16:48:13.331Z", "1.17.1": "2022-11-30T17:30:55.837Z", "1.18.0": "2023-01-04T17:00:55.895Z", "1.19.0": "2023-02-13T15:59:38.311Z", "1.20.0": "2023-04-19T22:00:00.743Z", "1.21.0": "2023-06-07T05:58:39.573Z", "1.21.1": "2023-06-25T02:39:19.975Z", "1.21.2": "2023-07-02T02:48:39.475Z", "1.21.3": "2023-07-03T15:16:12.970Z", "1.21.4": "2023-07-04T03:38:22.705Z", "1.21.5": "2023-07-05T04:30:38.395Z", "1.21.6": "2023-08-20T06:04:35.535Z", "1.21.7": "2023-08-20T18:11:12.643Z", "1.21.8": "2023-09-11T04:47:42.365Z", "1.22.0": "2023-09-17T22:49:26.580Z", "1.22.1": "2023-11-07T22:24:01.106Z", "1.23.0": "2024-01-14T23:48:13.008Z", "1.24.0": "2024-02-23T00:41:55.749Z", "1.24.1": "2024-03-15T04:23:30.281Z", "1.25.0": "2024-05-17T19:22:57.617Z", "1.25.1": "2024-05-25T06:06:40.629Z", "1.26.0": "2024-08-06T15:33:16.081Z", "1.27.0": "2024-09-11T03:01:05.281Z", "1.28.0": "2024-11-03T21:18:09.208Z", "1.28.1": "2024-11-03T22:58:44.671Z", "1.28.2": "2024-11-25T05:25:14.090Z", "1.29.0": "2025-01-09T05:48:06.402Z", "1.29.1": "2025-01-09T18:03:03.583Z", "1.29.2": "2025-03-06T06:22:22.083Z", "1.29.3": "2025-03-14T17:50:21.141Z", "1.30.0": "2025-05-11T06:39:33.998Z", "1.30.1": "2025-05-14T03:39:08.694Z"}, "versions": {"0.0.0": {"name": "lightningcss", "version": "0.0.0", "description": "placeholder", "_id": "lightningcss@0.0.0", "_nodeVersion": "14.18.2", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-tYo+7zQFwWzcUlEXC91mVSASNEUn6bZFzPI7KMAgIKaaJfyQyYY6nlDHc6tVoiCXMGOP5v/kVCU04lycN2zlIA==", "shasum": "a377b4657296f851761c9dd8f3795ef0077e0cba", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-0.0.0.tgz", "fileCount": 1, "unpackedSize": 83, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID8os5M/GU3o4D1t8Y1ZrjOvUu4BLieL6UzLWdOhbw46AiEAnmBA/7XghfWxzfXLEpJZR4HdLr6BD6IBxWxXYGq6hnY="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBBJaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqhtA//dDFfbOr2sOI0dUVWI0xvjZHv7STcEYAYTOWCy0AYfVSD7Axu\r\naiq337lQKC5A3Dn08gobxSj86dLuS9jrAWEumLBf2lSiK7Jpb7ZdY4iqvsZc\r\n9PP2l+Umw820nanuyNOWCRBKHBqnnQT3CO1YbFevsabi6H0gHiVGuVpa2tAe\r\nlopergaXClyF+hHWYcV+GH0S0tNxQwLHgFsh+DZGg6u7eaBiXejTOw273mjq\r\nlIqaMXDoxJkrmvJFbjWUf0JKQw1GyaVfoJwEj0NaGTXRTcyL9oLbmkMfsglU\r\n3LqlEVC+yCBVRotGWSkvbX26VOEgBfgFUl9QNe0eduvzEKzBSjsYqKPg6u4q\r\nQP5s0Q16cmilpBMfS0bjf5Ef3SIABuRWn4hyF6POfDYJlUz7FbDSzd8lLpyr\r\nSt7EDr3TUwKy6EpzsT9fuuBKDnh+dO94JJTTyq5su6xrwz3sR8xdXq44utco\r\nknSx06mhqkx71Zp0pr1Jcs6URCuNhAWBVnvcpAKKGb2zt56SiRM4OKmwr9yb\r\nAygFvYdxOQMqyG7PBsp++DVUejCVqT/NTwbSPAVonj8/dKZLipdqS/E7tnLZ\r\npZl/tOj5Cwf1ZyPItm9tKZVSnEfV+FEWeAkZmopiBykmP4KGDfor+BCyHjcX\r\nu+R6Aa9tIpzAc+ZdCH8NXFAiy4pHccIWbFs=\r\n=LbKI\r\n-----END PGP SIGNATURE-----\r\n", "size": 163}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_0.0.0_1661211226478_0.5878413341610953"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-08-22T23:33:50.308Z"}, "1.14.0": {"name": "lightningcss", "version": "1.14.0", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"import": "./node/index.mjs", "require": "./node/index.js"}, "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.14.0", "lightningcss-linux-x64-gnu": "1.14.0", "lightningcss-win32-x64-msvc": "1.14.0", "lightningcss-darwin-arm64": "1.14.0", "lightningcss-linux-arm64-gnu": "1.14.0", "lightningcss-linux-arm-gnueabihf": "1.14.0", "lightningcss-linux-arm64-musl": "1.14.0", "lightningcss-linux-x64-musl": "1.14.0"}, "devDependencies": {"@mdn/browser-compat-data": "^5.1.6", "@napi-rs/cli": "^2.6.2", "autoprefixer": "^10.4.8", "caniuse-lite": "^1.0.30001373", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "jest-diff": "^27.4.2", "node-fetch": "^3.1.0", "parcel": "^2.0.1", "postcss": "^8.3.11", "puppeteer": "^12.0.1"}, "scripts": {"build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "wasm-pack build node --target nodejs", "wasm:build-release": "wasm-pack build node --target nodejs --release", "wasm-browser:build": "wasm-pack build node --target web", "wasm-browser:build-release": "wasm-pack build node --target web --release", "playground:start": "parcel playground/index.html", "playground:build": "yarn wasm-browser:build-release && parcel build playground/index.html"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.14.0", "lightningcss-linux-x64-gnu": "1.14.0", "lightningcss-win32-x64-msvc": "1.14.0", "lightningcss-darwin-arm64": "1.14.0", "lightningcss-linux-arm64-gnu": "1.14.0", "lightningcss-linux-arm-gnueabihf": "1.14.0", "lightningcss-linux-arm64-musl": "1.14.0", "lightningcss-linux-x64-musl": "1.14.0"}, "gitHead": "2d17e0e1ed1482525985ef4d30809b4c06d76944", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_id": "lightningcss@1.14.0", "_nodeVersion": "16.17.0", "_npmVersion": "8.15.0", "dist": {"integrity": "sha512-Vwa1JWiLGRLntf4TXRu/zF2RaHRfJjbZwUzkl2C4LhnRQzsEwkyeAQjAxtpJ5NDnVVUf10q1olEzkFF4AWZLOw==", "shasum": "84daa8abfeeff9fc9dacf9a1ea9ee6c9bbcc597d", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.14.0.tgz", "fileCount": 9, "unpackedSize": 47210, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIACOc+F6gbQcIB9fKq1O+rfyyIbE5DNgHbaJcC399cX8AiBChUUD2XEFw54JL59YZiZezPAZCBUF2Bn61Y2eoPT2bQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjGXtfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmok/w//S0KMVmaubl9DAU93NllDaa03pKQg73Pf8l3lIoI8mSFW+UAh\r\nPhEgZnEdNV/HfIuaas7pR9QSrRxnreTVyXhjbMBve61PCL6RF1LcXCcbD+d7\r\ng0TP8zJGuw5r0KGSQRuYTF8XKxWfVuKUtidtmBd6mH8WssLpy22GQWkIpuVj\r\nq7llgKdj26/7zQPPDL3Lh8Y6uTnEX1zIFMTnWKS02tZmm6V2AOnEWtu/uvxq\r\nDp+aGoIzmBLFdoS5okWgPstWKd16mqaVioPVJu3LFVo7idQkqNHto2Gc4Maw\r\nAwa56BxzYLhpPcOvWRfqLW5B/QJo0mQoMV2eQ0KnU+mFHxaVioG0V6Kwdh4E\r\np5p8kkgDFwBKPwf57OzxAlBsJThm96VbZhZgmnfw7h+iiV7b+AM4w77eKIaH\r\nDEojBgO76XdVa0yYjgaunj+XwQ7l4VXP/2VCXXQfXe5BXFpLelx9cx+d4aEr\r\nadpOTznPh51dYY2MJr0vKH4RGzcqtyhstWW68bmjfCH35ca8gz6QdIPAv/98\r\nmeoE+bEKTZtHp0KIx5McaFyWW30/oL29nhbNmvEtYUsv1/Vpn9e2uJNEX1VV\r\nVTiz6l/38b4/9f29HknMj76NFtQZWRAa629CUQc2BNW2Kk7Ps58nCi68ZA+Z\r\n5k32BAo4U1t59D5lpLWyQnJhj8TYfxXlfjs=\r\n=1sTC\r\n-----END PGP SIGNATURE-----\r\n", "size": 13544}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.14.0_1662614367488_0.09680869139613835"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-09-08T05:20:02.682Z"}, "1.15.0": {"name": "lightningcss", "version": "1.15.0", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.15.0", "lightningcss-linux-x64-gnu": "1.15.0", "lightningcss-win32-x64-msvc": "1.15.0", "lightningcss-darwin-arm64": "1.15.0", "lightningcss-linux-arm64-gnu": "1.15.0", "lightningcss-linux-arm-gnueabihf": "1.15.0", "lightningcss-linux-arm64-musl": "1.15.0", "lightningcss-linux-x64-musl": "1.15.0"}, "devDependencies": {"@mdn/browser-compat-data": "^5.1.6", "@napi-rs/cli": "^2.6.2", "autoprefixer": "^10.4.8", "caniuse-lite": "^1.0.30001373", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "jest-diff": "^27.4.2", "node-fetch": "^3.1.0", "parcel": "^2.7.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "process": "^0.11.10", "puppeteer": "^12.0.1", "sharp": "^0.29.1", "util": "^0.12.4"}, "scripts": {"build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "wasm-pack build node --target nodejs", "wasm:build-release": "wasm-pack build node --target nodejs --release", "wasm-browser:build": "wasm-pack build node --target web", "wasm-browser:build-release": "wasm-pack build node --target web --release", "website:start": "parcel website/index.html website/playground/index.html", "website:build": "yarn wasm-browser:build-release && parcel build website/index.html website/playground/index.html"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.15.0", "lightningcss-linux-x64-gnu": "1.15.0", "lightningcss-win32-x64-msvc": "1.15.0", "lightningcss-darwin-arm64": "1.15.0", "lightningcss-linux-arm64-gnu": "1.15.0", "lightningcss-linux-arm-gnueabihf": "1.15.0", "lightningcss-linux-arm64-musl": "1.15.0", "lightningcss-linux-x64-musl": "1.15.0"}, "gitHead": "1a23835a0d72afc06ab5f98cbbee5fb03ae09074", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_id": "lightningcss@1.15.0", "_nodeVersion": "16.17.0", "_npmVersion": "8.15.0", "dist": {"integrity": "sha512-FF1SDM8Mo43lExnWkrXesDMoKFYM5iDbqbLl408VH6VyB4TdMBEQpWW4bbUrE03Vhl7ioIUMEJfgzAV7vaeYkw==", "shasum": "a7e8b4e76718f6a75c504f324563628d2d8dfeb9", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.15.0.tgz", "fileCount": 9, "unpackedSize": 48031, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDsKeXlBEdD7UGsCqqgwOrLIANu9wh7tq7vwjkjIJoeJAiBg2NFDfpHw8FZxW5FWzn6j22zqbNsXpwikzUi9YWbV/A=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIqcnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5dxAAnuQG+VKf6dPhZLmrNgFjiqJpUYWUgwGNA58IaIDraJC+grRW\r\naCOGzf/kct6eIvvcz8l/7Dk+BfCyJHBkAgirN6Hu5YaPHr2+6SdwuI5YymMw\r\nO3rNpOIglSu6zf4lJN1FPSNsXprNLz7bkJdfl+pgSo4nr1JsxxmK0ZgD+r7e\r\nIqpZkMfgCun+UYZVDy4QZzPz64nCyBPwa6iU+MpasGd+HfcvKsQR7qKDgOnp\r\n2yzMrfDJJv0E8fdVXF2j1wbv8Vws5gtP0M21K7B9C7gZTH/wYTtcP1d2K3aW\r\nTFs4+Xc6T+f8cL6JmoSKmqwRROyaKR6bRdnMjI9mnXy74zqLCHG8GeUFB7Kb\r\nXAPICaon9LJ3P+dk9PAQrTEj8TYYM6C23nuLEV9SOtJZ4y+56lH+ekszQDVY\r\nO3rVQtT5pDS91H2i+mFJMKPzz99Te5tvMy1qVsmWEam9YuBTyZiBm/7Sq1dx\r\ncxiK38S1T+aHqzDOwMOe61imV1sLGAlQvhMn5KGsVG51SpwtPtWNd9PkXkke\r\n7jvRUygwp7P4x2bJMvMXJeej31nir5LG2c6BXSUeoK56yfquWw+gi/Tok12k\r\nAlzn/obljULXCpBKMS4ft5FyFIkUnLiCOHXPn2/uzBu678Qo/Y/NkdLQ5qem\r\new6k3si9SKQUXq8Wfy9yfYCN+ZxjFr5fpnE=\r\n=xj2L\r\n-----END PGP SIGNATURE-----\r\n", "size": 13703}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.15.0_1663215399585_0.556770465620932"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-09-15T04:19:38.935Z"}, "1.15.1": {"name": "lightningcss", "version": "1.15.1", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.15.1", "lightningcss-linux-x64-gnu": "1.15.1", "lightningcss-win32-x64-msvc": "1.15.1", "lightningcss-darwin-arm64": "1.15.1", "lightningcss-linux-arm64-gnu": "1.15.1", "lightningcss-linux-arm-gnueabihf": "1.15.1", "lightningcss-linux-arm64-musl": "1.15.1", "lightningcss-linux-x64-musl": "1.15.1"}, "devDependencies": {"@mdn/browser-compat-data": "^5.1.6", "@napi-rs/cli": "^2.6.2", "autoprefixer": "^10.4.8", "caniuse-lite": "^1.0.30001373", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "jest-diff": "^27.4.2", "node-fetch": "^3.1.0", "parcel": "^2.7.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "process": "^0.11.10", "puppeteer": "^12.0.1", "sharp": "^0.29.1", "util": "^0.12.4"}, "scripts": {"build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "wasm-pack build node --target nodejs", "wasm:build-release": "wasm-pack build node --target nodejs --release", "wasm-browser:build": "wasm-pack build node --target web", "wasm-browser:build-release": "wasm-pack build node --target web --release", "website:start": "parcel website/index.html website/playground/index.html", "website:build": "yarn wasm-browser:build-release && parcel build website/index.html website/playground/index.html"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.15.1", "lightningcss-linux-x64-gnu": "1.15.1", "lightningcss-win32-x64-msvc": "1.15.1", "lightningcss-darwin-arm64": "1.15.1", "lightningcss-linux-arm64-gnu": "1.15.1", "lightningcss-linux-arm-gnueabihf": "1.15.1", "lightningcss-linux-arm64-musl": "1.15.1", "lightningcss-linux-x64-musl": "1.15.1"}, "gitHead": "eb66ece2f9b47bf4ef60848736dde697ec80a319", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_id": "lightningcss@1.15.1", "_nodeVersion": "16.17.0", "_npmVersion": "8.15.0", "dist": {"integrity": "sha512-d4fqKl8sqpdM27OsseAbKKxhD2otiu6stS7yWlm/DA7wOQAIDKMu/dke54cEN8ED19v9H2pEzMPGTsRfnq3Rdg==", "shasum": "a951a0ef651a0d026de4f3ec03bd2a6feae25da4", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.15.1.tgz", "fileCount": 9, "unpackedSize": 48031, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHS3Ela7Y5dRjUXSzM7dQCocnHSiSi9C/ecGvbmWT56aAiEAjaqN5nLPq4aMmKLvF5L9sfm7FI/CY4Faxa40aeFqETw="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjI/JLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrm8g/+Kz+iey3G9nl+7ivPzSBF7G8h0DEpvkqG09Ay6Ikybe+F3Q8M\r\ncfN4ls37mJPmU3lk+t3PbNRfmL9cy70LFWBsNckdAkVUst73Z0yFhcTxF9tb\r\ntVeV0AvL1aCRlBd4xLX2J0XBTwQF1aJI/rCD1viI0QAarj4SaGyQGpHvgib5\r\naz9k4ybt504ieti6OmsBmwADcSMd5FFe3zrPekogp7HUyJMdWBerozK5znsT\r\nM58OM4e7AORNXPT5aK3QsiG6kth86ovU4xWx8rdFGAAz+dFPqvqt4eR16lgV\r\nGfayvewkm3mUyVjUezzaIWNKW5pZKYV245QeJn4CuLhppSUL8U7jnuZMWGeh\r\nDCKXjk3/V0tWQXRlABoapvrj/bUtdvpg2TPkUT5RaVf5OMDiLWhmeLc/2Ykv\r\nAGfJNKCTRRxCJ6U9j+GZ6wIPiPkOG10Bwc+mCcj9EL/fjYrg4Bpl4FDt1kcD\r\nvgIdiSrtV8d7PHIMn08K1Hl+kIroM47BhlfU3Zk2/YyJiAjbIzPI7it2IF8/\r\n3d+U7GfE4O/lXJlTNbDpzjEvEfymUiiYREI84UOhvNaqn4vS104H9kj0KYfT\r\nhvOOjXCOIot53B6b4UPynIBarQh3aVHq/NTsRt0uAvYpU2MTRPchnfVokGSp\r\nq7xzAQnvHfRCkj7sivV/HVWF3M5UVtyejtc=\r\n=2nyn\r\n-----END PGP SIGNATURE-----\r\n", "size": 13705}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.15.1_1663300170930_0.059896875757631474"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-09-16T03:50:29.101Z"}, "1.16.0": {"name": "lightningcss", "version": "1.16.0", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.16.0", "lightningcss-linux-x64-gnu": "1.16.0", "lightningcss-win32-x64-msvc": "1.16.0", "lightningcss-darwin-arm64": "1.16.0", "lightningcss-linux-arm64-gnu": "1.16.0", "lightningcss-linux-arm-gnueabihf": "1.16.0", "lightningcss-linux-arm64-musl": "1.16.0", "lightningcss-linux-x64-musl": "1.16.0"}, "devDependencies": {"@mdn/browser-compat-data": "^5.1.6", "@napi-rs/cli": "^2.6.2", "autoprefixer": "^10.4.8", "caniuse-lite": "^1.0.30001373", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "jest-diff": "^27.4.2", "node-fetch": "^3.1.0", "parcel": "^2.7.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "process": "^0.11.10", "puppeteer": "^12.0.1", "sharp": "^0.29.1", "util": "^0.12.4"}, "scripts": {"build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "wasm-pack build node --target nodejs", "wasm:build-release": "wasm-pack build node --target nodejs --release", "wasm-browser:build": "wasm-pack build node --target web", "wasm-browser:build-release": "wasm-pack build node --target web --release", "website:start": "parcel website/index.html website/playground/index.html", "website:build": "yarn wasm-browser:build-release && parcel build website/index.html website/playground/index.html"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.16.0", "lightningcss-linux-x64-gnu": "1.16.0", "lightningcss-win32-x64-msvc": "1.16.0", "lightningcss-darwin-arm64": "1.16.0", "lightningcss-linux-arm64-gnu": "1.16.0", "lightningcss-linux-arm-gnueabihf": "1.16.0", "lightningcss-linux-arm64-musl": "1.16.0", "lightningcss-linux-x64-musl": "1.16.0"}, "gitHead": "200120e24aa31449703707db14ffef1bf40a7fea", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_id": "lightningcss@1.16.0", "_nodeVersion": "16.17.0", "_npmVersion": "8.15.0", "dist": {"integrity": "sha512-5+ZS9h+xeADcJTF2oRCT3yNZBlDYyOgQSdrWNBCqsIwm8ucKbF061OBVv/WHP4Zk8FToNhwFklk/hMuOngqsIg==", "shasum": "4c8e4ef8133d54488d1482a115d3758259191c52", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.16.0.tgz", "fileCount": 9, "unpackedSize": 48103, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF7PkYozn9e4n2i2MUe967ntP3r/GpApq84ETtkpmlaYAiEAkOeFqHhxNWPRjRdXLYeOuo6zkNCisNl4pJHket5EB0o="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKTIqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrOZg//c22Vv3A3+bw4EvWsDF+sK5SipztBknmChmbipa3TqaqlDPkD\r\nS3RJvNzN7SU1WTUni0kTp7G9wNLqQgBo9DkH6/xn02J1wfVRY2LshaxEP41p\r\nnJNVf0lVMfDeCs3vqVDibzmyeIfRnbucDUSqezpzMgYsuB2w2dR2ni6Xo+0u\r\nJT8z6Vd4K/ydxFfcd9pkaS0qyh6cUYNvWZGpCinOM+k6qxT6vM3w1FPrgHWd\r\nIdpLWuh1dDd/+ZjNA3Sg09/6bJoC+bByicLoLqOp4cUva5cEBHOR/lAp4Ojw\r\ndfISaikcY8Q0McaCtxC1Nhu+cyVTZAyoBBVXjhsvCNe4JGOcrIQ7KvCeKZGE\r\n4Q9Fb44Y53I7/vsNyYtMTFcV28t9YrVo8Gcx33X5gkdvjh6SxYsTEdkrp8k4\r\nEnbVNJ1IEuJ2tyXr+sljdWRdiALdA38ctsTnZ3TdP5CLHq41ltxW/4vdntMs\r\nYC1qo/Me3qOaX5UWsgLwjddRVcjUAjp0np7ANYi6ncwHsfzaDBOiYYfpSuRX\r\nq0mETG6QIdMuqZfc6OT48yuvudjxmoHTUicLWXe36YrTfNl7DQKE2iQXstrp\r\nWgXA3FAmCB2/Z0VQHCt456quWl8X7uEFDylY1YTWDyRuO9HJAX8raLecLgnl\r\nhdvObwGO6MpV9EOiOKTTAC2HqN5+mNj/Yys=\r\n=U1z9\r\n-----END PGP SIGNATURE-----\r\n", "size": 13738}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.16.0_1663644202478_0.39201670039409153"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-09-20T03:23:50.828Z"}, "1.16.1": {"name": "lightningcss", "version": "1.16.1", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.16.1", "lightningcss-linux-x64-gnu": "1.16.1", "lightningcss-win32-x64-msvc": "1.16.1", "lightningcss-darwin-arm64": "1.16.1", "lightningcss-linux-arm64-gnu": "1.16.1", "lightningcss-linux-arm-gnueabihf": "1.16.1", "lightningcss-linux-arm64-musl": "1.16.1", "lightningcss-linux-x64-musl": "1.16.1"}, "devDependencies": {"@mdn/browser-compat-data": "^5.1.6", "@napi-rs/cli": "^2.6.2", "autoprefixer": "^10.4.8", "caniuse-lite": "^1.0.30001373", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "jest-diff": "^27.4.2", "node-fetch": "^3.1.0", "parcel": "^2.7.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "process": "^0.11.10", "puppeteer": "^12.0.1", "sharp": "^0.29.1", "util": "^0.12.4"}, "scripts": {"build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "wasm-pack build node --target nodejs", "wasm:build-release": "wasm-pack build node --target nodejs --release", "wasm-browser:build": "wasm-pack build node --target web", "wasm-browser:build-release": "wasm-pack build node --target web --release", "website:start": "parcel website/index.html website/playground/index.html", "website:build": "yarn wasm-browser:build-release && parcel build website/index.html website/playground/index.html"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.16.1", "lightningcss-linux-x64-gnu": "1.16.1", "lightningcss-win32-x64-msvc": "1.16.1", "lightningcss-darwin-arm64": "1.16.1", "lightningcss-linux-arm64-gnu": "1.16.1", "lightningcss-linux-arm-gnueabihf": "1.16.1", "lightningcss-linux-arm64-musl": "1.16.1", "lightningcss-linux-x64-musl": "1.16.1"}, "gitHead": "87e808fd771374ae705886351ee62c4fac3d3630", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_id": "lightningcss@1.16.1", "_nodeVersion": "16.18.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-zU8OTaps3VAodmI2MopfqqOQQ4A9L/2Eo7xoTH/4fNkecy6ftfiGwbbRMTQqtIqJjRg3f927e+lnyBBPhucY1Q==", "shasum": "b5a16632b6824d023af2fb7d35b1c6fc42608bc6", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.16.1.tgz", "fileCount": 9, "unpackedSize": 50242, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF/in4jOiujRGAcjQRwNqlhsceFWBEp2p0jlk3ePcSyHAiEAyjpGM7dvjCBiUCBEUahjaVt0FaGZifZgrIy80cwR9qE="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZ/anACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2Kg//YMEGmP5eWpKliNPHAr4OhylbraVu+Sk1FhO90wTOJZ4yAZgi\r\nmdqi3xZKIr6Eq6aw0c694MRXh+3bIgnoK4We7PZM03L4BavtTP7p//3MxprW\r\nAb0s4bik3yGymX70zl3yWnHg40hw5ZbwjKW5PO01SHwZqbeIBc4HQovbozzH\r\nagBaBbzrWQL1Yz/MaARY6MrZoHwI8ziDoDBygSMRTIgD29qZtLdw3+JUl3/v\r\n9gIyxJSjdJUDggNqhRND+EQV3r9K7hjyYwOuFM6pCOLfj4WsNxZCma+DNbkw\r\nv0ElGi7CUjM+lYv9c6Zjn0ObebtFq3hY5sX5/de+r/8bQkgiwpfFquNFMvOS\r\n2vIjmu0fpVK8ZfTTPCAPQf4Mt7HS8hQht7XhBlqhPjJ67VWYo36Tkse5F2Mc\r\nPG3x9bQDubwJBJyv+uCrNFz9Co3U+7EMEBIjAf9EVgejmhojZa1TA5c0PJkm\r\nH2MBuuE1uGBSBudaPZ9Z+xVJIzTZ1eJZjdVbrkEku5MmCBffila45hOwVEi7\r\nxdZ1sBZ0ileBtJGtvT0lG9J7hhIpuUqSpIMpwQhN1RzNgtzKD/9lnIM0hCOb\r\nknRfd7ohi/VQ2YuBlYfm3Ew0WqVEdIF+Ltz8SBZOEgaD8zEtTXoQ68irjR3t\r\nExSnoMncuDX6giW6YMsvh7PcE9UtnA8+M6w=\r\n=SBjs\r\n-----END PGP SIGNATURE-----\r\n", "size": 14407}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.16.1_1667757734919_0.8678963770183297"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-11-06T18:02:22.162Z"}, "1.17.0": {"name": "lightningcss", "version": "1.17.0", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.17.0", "lightningcss-linux-x64-gnu": "1.17.0", "lightningcss-win32-x64-msvc": "1.17.0", "lightningcss-darwin-arm64": "1.17.0", "lightningcss-linux-arm64-gnu": "1.17.0", "lightningcss-linux-arm-gnueabihf": "1.17.0", "lightningcss-linux-arm64-musl": "1.17.0", "lightningcss-linux-x64-musl": "1.17.0"}, "devDependencies": {"@mdn/browser-compat-data": "^5.1.6", "@napi-rs/cli": "^2.6.2", "autoprefixer": "^10.4.8", "caniuse-lite": "^1.0.30001373", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "jest-diff": "^27.4.2", "node-fetch": "^3.1.0", "parcel": "^2.7.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "process": "^0.11.10", "puppeteer": "^12.0.1", "sharp": "^0.29.1", "util": "^0.12.4"}, "scripts": {"build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "wasm-pack build node --target nodejs", "wasm:build-release": "wasm-pack build node --target nodejs --release", "wasm-browser:build": "wasm-pack build node --target web", "wasm-browser:build-release": "wasm-pack build node --target web --release", "website:start": "parcel website/index.html website/playground/index.html", "website:build": "yarn wasm-browser:build-release && parcel build website/index.html website/playground/index.html"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.17.0", "lightningcss-linux-x64-gnu": "1.17.0", "lightningcss-win32-x64-msvc": "1.17.0", "lightningcss-darwin-arm64": "1.17.0", "lightningcss-linux-arm64-gnu": "1.17.0", "lightningcss-linux-arm-gnueabihf": "1.17.0", "lightningcss-linux-arm64-musl": "1.17.0", "lightningcss-linux-x64-musl": "1.17.0"}, "gitHead": "d4853b204cc80b051d29be324e2d16f621e76336", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_id": "lightningcss@1.17.0", "_nodeVersion": "16.18.1", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-flO6jocjeV0MUDkYCtg1+jwLqAlqrV/t3n7rUrJ6/tcntm5zfBXsewwOj7PSSv9iTNqFKJX8zIkx/M1W2AG3kw==", "shasum": "32fec5f10def6bd32a608ae302d0266ba7d90332", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.17.0.tgz", "fileCount": 9, "unpackedSize": 50234, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHj+CTHYKisC0my5PGAYYglt0kKH9CoAT/S1l/CzvfoMAiBSjxVI1k06IqYMt2grBeayuo5Nw3D4fO6iTO9k/0aEBg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhjfNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqfUg//Ya9cVtAgmYDFspP6xb65Dtda52S+ob1cu0P16Pr6mioJ8jH/\r\nalzmdvBGuFjG1N66sEoA94KHPH7x75Vt9dGUWrN4EI5BvoRjcrRywyCHo1Af\r\nFZm+3y122WiLgdwev5XhiLIRsHdZpGvb+wk4BYvJ31IRmBtuaEUCoQrD8fRr\r\nJl+xx2zixQpBpRi1OiWGLuGYAolNg29ak+xPunjZIFY02pXke7VPi4vtOL1p\r\n2GchKfzA2+sUYM6MMjnxh+hIljozz1AyEnlY4kABG2SjJcCVPOyEh5jGFwYe\r\nBzY4wpjwuV7ka6FC5KgKwmjswPh6bvCv45aVf4ZSkuTi6WAcIxrSludbG3gS\r\nXzt2Ti64ACSNbuLAefD3osYEyL1k0piVxVHQHnh8ep1WjuEi9LbVRw1RXa0y\r\nXMQfJrBhTSIs+d4RpzrWHx937TWZkmL1NSBRcrfocqaKMSvp5wiB9LDUxKga\r\nVs3S1zgZ45timN50w+fRPFx7zMDCPVnd9trngzkIyPNYEUn11qLukDMtUcAU\r\nqGy5ti7cX+WLB8gXd89huYqvT0qMl74+0yNAl9M3WuybwmkhAZdz5eCM7wiO\r\ncO7rGAZMRsZydPtwFFAWWnmjQUC4LdYwXNcO2ADAXEPmAd+YHcWbkOmncRDO\r\nMriDCFsyXzeyXsycKySUUen8Cosj7TJxYIY=\r\n=Vstt\r\n-----END PGP SIGNATURE-----\r\n", "size": 14401}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.17.0_1669740493125_0.3032199652699441"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-11-29T17:22:36.744Z"}, "1.17.1": {"name": "lightningcss", "version": "1.17.1", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.17.1", "lightningcss-linux-x64-gnu": "1.17.1", "lightningcss-win32-x64-msvc": "1.17.1", "lightningcss-darwin-arm64": "1.17.1", "lightningcss-linux-arm64-gnu": "1.17.1", "lightningcss-linux-arm-gnueabihf": "1.17.1", "lightningcss-linux-arm64-musl": "1.17.1", "lightningcss-linux-x64-musl": "1.17.1"}, "devDependencies": {"@mdn/browser-compat-data": "^5.1.6", "@napi-rs/cli": "^2.6.2", "autoprefixer": "^10.4.8", "caniuse-lite": "^1.0.30001373", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "jest-diff": "^27.4.2", "node-fetch": "^3.1.0", "parcel": "^2.7.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "process": "^0.11.10", "puppeteer": "^12.0.1", "sharp": "^0.29.1", "util": "^0.12.4"}, "scripts": {"build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "wasm-pack build node --target nodejs", "wasm:build-release": "wasm-pack build node --target nodejs --release", "wasm-browser:build": "wasm-pack build node --target web", "wasm-browser:build-release": "wasm-pack build node --target web --release", "website:start": "parcel website/index.html website/playground/index.html", "website:build": "yarn wasm-browser:build-release && parcel build website/index.html website/playground/index.html"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.17.1", "lightningcss-linux-x64-gnu": "1.17.1", "lightningcss-win32-x64-msvc": "1.17.1", "lightningcss-darwin-arm64": "1.17.1", "lightningcss-linux-arm64-gnu": "1.17.1", "lightningcss-linux-arm-gnueabihf": "1.17.1", "lightningcss-linux-arm64-musl": "1.17.1", "lightningcss-linux-x64-musl": "1.17.1"}, "gitHead": "27cb9c2a382fd8d9702de8def9dd18db54280c43", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_id": "lightningcss@1.17.1", "_nodeVersion": "16.18.1", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-DwwM/YYqGwLLP3he41wzDXT/m+8jdEZ80i9ViQNLRgyhey3Vm6N7XHn+4o3PY6wSnVT23WLuaROIpbpIVTNOjg==", "shasum": "cce53acf117a6f9494bc77e8ac6550286d621243", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.17.1.tgz", "fileCount": 9, "unpackedSize": 50234, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDACC9dVq9rZi8blG1qK/qUBBOMKzuRxXvh8I66HrqsJwIgClFRl9hBWu+yOGZG+PMCIZJP9Ny2uZcixg0qf3SEeso="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjh5NPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmptnw//QSX7at7VIP3xec6/RiuwVdzz5P8/K/mArrfHhW0oFt+CZT4i\r\nJyfPAucBvOCaNBfNAvrC6DE5zLx1nVTd9j4hkKDPsVI/8h4/IymmlB+LDDhC\r\n4vL7ouLDnFgl9wbup5h6cfLA7fSa/RYJYtYRrXEb/00nyCrFXDe3fEvC7qoh\r\n+qScXe16XvWc1+NEhvJAdsxEETm9pEyxe1HD0mHfxJKfhA5PC4+NLuvK09vp\r\nUHFHovGy9NwVIsfOZGx1KspEDyKAvlYq2IxJmquoYHHXVNnZWIlkcu861Hc0\r\nMQyaBVxYmTztP8bWBDCP0xsgtSrLsmy6qGgHbu5BNr7tkac/7GbMVuTbG+WG\r\n2ScXk6HLEoarg04Bmzk0dRY1RPdOqDr3f3BuQ0kZC4G+wIbtvExPLuKf6IHE\r\n3u8di1cmowPZjtscLfqXSByjBXaiGHLoAY7gO3NwCm7WbOWScQQNtdIu62vz\r\nir1i7D6hNt3HskUo2pfDlOvdvl5m22iAw+UbHFxTZgcW9UtYHX6VY8clwKuE\r\n5Ng9yNjtJo4whGdYJZ2nMuycW2/2aSoEVHHlK6ih2Mkf6KcrPZBGuVgIr700\r\neIY06jZdDvgX4O9KtpGg/D/Ylso0acLRizOcoiLxKxQQpLnA+6ggai3/2nm/\r\nmCqAG6m9FPtZQxR3eycKUYCtUPWTnHxOEAc=\r\n=UVg1\r\n-----END PGP SIGNATURE-----\r\n", "size": 14401}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.17.1_1669829455488_0.12738308139795573"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-11-30T17:38:08.477Z"}, "1.18.0": {"name": "lightningcss", "version": "1.18.0", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.18.0", "lightningcss-linux-x64-gnu": "1.18.0", "lightningcss-win32-x64-msvc": "1.18.0", "lightningcss-darwin-arm64": "1.18.0", "lightningcss-linux-arm64-gnu": "1.18.0", "lightningcss-linux-arm-gnueabihf": "1.18.0", "lightningcss-linux-arm64-musl": "1.18.0", "lightningcss-linux-x64-musl": "1.18.0"}, "devDependencies": {"@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "^5.1.6", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.8", "codemirror": "^6.0.1", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "sharp": "^0.31.1", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:."}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && cp target/wasm32-unknown-unknown/debug/lightningcss_node.wasm wasm/. && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && cp target/wasm32-unknown-unknown/release/lightningcss_node.wasm wasm/. && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.18.0", "lightningcss-linux-x64-gnu": "1.18.0", "lightningcss-win32-x64-msvc": "1.18.0", "lightningcss-darwin-arm64": "1.18.0", "lightningcss-linux-arm64-gnu": "1.18.0", "lightningcss-linux-arm-gnueabihf": "1.18.0", "lightningcss-linux-arm64-musl": "1.18.0", "lightningcss-linux-x64-musl": "1.18.0"}, "gitHead": "6fb77292c9ef4320fa85a1e26fec3fbdbae3cf8a", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_id": "lightningcss@1.18.0", "_nodeVersion": "16.18.1", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-uk10tNxi5fhZqU93vtYiQgx/8a9f0Kvtj5AXIm+VlOXY+t/DWDmCZWJEkZJmmALgvbS6aAW8or+Kq85eJ6TDTw==", "shasum": "ca3327a1a7571a83bbb9733ed4e4cded775bdadf", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.18.0.tgz", "fileCount": 13, "unpackedSize": 445038, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDG7fg+eIQNIb0mXCG7vDfr5fpz8PKdcvlwNU+hztxMJAIgZTaYvGQVBYciEawNkFPYLXkj8t9cwv9LwX/1zztWshw="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtbDHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqQvBAAgynOIdOywEwAzatXmN/bq8DuUS8wr3WaGirYP6KeGN/fEz1S\r\nM7UH9IHxIXYJcMCEAog3NUjnoqVtmGHtLWQkhYuvzNhK2iQkxP50dsU/IM7l\r\nBl4VWkTFs5znFHbCL+vC5tItaFKkPuxmAXF2wQfR74IuCQfAkoUBWTNofAIY\r\n/9Ze6UuOoWOiIZulvEMmcJpJVTugjtaMpeFFoQVCL6eAAGKgQ7lOoz73mrsa\r\nx4z7+RcOEnvO7wHDztwrTx9gf9IKi64t5hEpOqe1iYbmD2LltTiQiFnZCBlX\r\nYL9K6GhURMz6/DflAeHcouVRNOlx+OQcox5h0Y0mJ4QiJCml6gbwb58lhllz\r\nO6xVBhGIopLMt1RaiHVYsrpBEN9gyfUUzvjyrzbzCrVwpPqrG/7OzQiCzgcQ\r\n3/JEiQA8AUBGhNCMUrACEqJGvIFL71XEu6OK/NBT+yg9DNPNpezC0kQjBtCW\r\nlx9qwnPqOGvJcbJek8d8UFsXAavd9rffsO23qMDLWottmTgEyrvi0KpR8W84\r\nzbyDoBXTvz04EbyYqhXEj6y7SkSLRGi9yXtfNe1oBGqFu8cZeJasgS/O3iiF\r\netUTNDVLcWOE2J5g8dou8m2jGTa+XzZvFhbUx7AwedtQFnPVT0mC+QHUMY83\r\nxTRjwDRXh+0F1MCj1O7f2fOnzaNBtKAJs7E=\r\n=dotC\r\n-----END PGP SIGNATURE-----\r\n", "size": 74422}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.18.0_1672851655709_0.44543979866792194"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-04T17:25:54.382Z"}, "1.19.0": {"name": "lightningcss", "version": "1.19.0", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.19.0", "lightningcss-linux-x64-gnu": "1.19.0", "lightningcss-win32-x64-msvc": "1.19.0", "lightningcss-darwin-arm64": "1.19.0", "lightningcss-linux-arm64-gnu": "1.19.0", "lightningcss-linux-arm-gnueabihf": "1.19.0", "lightningcss-linux-arm64-musl": "1.19.0", "lightningcss-linux-x64-musl": "1.19.0"}, "devDependencies": {"@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "^5.2.35", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.13", "codemirror": "^6.0.1", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "sharp": "^0.31.1", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:."}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && cp target/wasm32-unknown-unknown/debug/lightningcss_node.wasm wasm/. && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && cp target/wasm32-unknown-unknown/release/lightningcss_node.wasm wasm/. && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.19.0", "lightningcss-linux-x64-gnu": "1.19.0", "lightningcss-win32-x64-msvc": "1.19.0", "lightningcss-darwin-arm64": "1.19.0", "lightningcss-linux-arm64-gnu": "1.19.0", "lightningcss-linux-arm-gnueabihf": "1.19.0", "lightningcss-linux-arm64-musl": "1.19.0", "lightningcss-linux-x64-musl": "1.19.0"}, "gitHead": "05c23f1269321d4b072f6264a5c9cb6edd8a02bb", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_id": "lightningcss@1.19.0", "_nodeVersion": "16.19.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-yV5UR7og+Og7lQC+70DA7a8ta1uiOPnWPJfxa0wnxylev5qfo4P+4iMpzWAdYWOca4jdNQZii+bDL/l+4hUXIA==", "shasum": "fbbad0975de66252e38d96b5bdd2a62f2dd0ffbf", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.19.0.tgz", "fileCount": 13, "unpackedSize": 454123, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFDF1+Q9Usq1cQhuT60OMahuLsv9+Ju+vYkHur6enHqOAiABMzTTzNpttwZRm28E9v6Y4mCRRjygBRuhVxev4poK5Q=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6l5qACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqmGg/8DbRlMaabhivPWQm0R2MRoUB2vU48sRNS/GnvwYsWlcNpmFz4\r\nKFr6jAe2H3SkhW6HMm6fsWzC6FAftRr7jsu9eu2j7OZPvWAYuMMQg+n2g7XB\r\n375PsJLmw7HHqcnaXaDbdE1SNt7hIRa+39SXvrjmB6yCirXfSUzdZInoHo2X\r\n8dPA08qcpslOa+/QuT3JcWP8vvWu9yrIaVH6O7pbSPGxOSmT+tud1nebRkmK\r\nn1SAPm05+KI3Zv/bowEOZCwtxWYthGI+tgu7EdXIng949xaQZeWZsrbkZzU3\r\n7uoydlxRM7AO/jt+W/txZN/5AZ8swVNg9SYS9e1qiT7FuiwDjBXD98PhIocH\r\nyDHnkpnqnsQ67sN0SDrhjwCHOf1bDYkia2yTnZ+rxXsrJjBTE76sz+8qyfL8\r\nBecBL3JeUrsUyaUDHgmIqYWfMVoewxswzVlhB68HZ5HYnd//3tcYrbe/blIp\r\nZPFZvTV4L/iW4ysUeVUJbwrlGbtV9MKmvjvC39NiY84/T1raLPHeLWE/gTqm\r\npW8D+tfgsYsZV07QAO5oA0XA3dOUT7k4q6nIW3Xy9s9blPg10XePGcKgfioC\r\nJh0C3NRsui9vzBkgJIAhLy5Q5PUMPzSYPtidWYM9RfHG88KC20Cf3V9H6m0P\r\nb7HTvde86Y23hIUv5Jt7irrRC/Ap1P0278A=\r\n=7a+k\r\n-----END PGP SIGNATURE-----\r\n", "size": 76766}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.19.0_1676303978125_0.9511368526157389"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-13T15:59:38.311Z", "publish_time": 1676303978311}, "1.20.0": {"name": "lightningcss", "version": "1.20.0", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"types": "./node/index.d.ts", "import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.20.0", "lightningcss-linux-x64-gnu": "1.20.0", "lightningcss-win32-x64-msvc": "1.20.0", "lightningcss-darwin-arm64": "1.20.0", "lightningcss-linux-arm64-gnu": "1.20.0", "lightningcss-linux-arm-gnueabihf": "1.20.0", "lightningcss-linux-arm64-musl": "1.20.0", "lightningcss-linux-x64-musl": "1.20.0"}, "devDependencies": {"@babel/parser": "^7.21.4", "@babel/traverse": "^7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "^5.2.49", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.14", "codemirror": "^6.0.1", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.31.1", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:."}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && cp target/wasm32-unknown-unknown/debug/lightningcss_node.wasm wasm/. && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && cp target/wasm32-unknown-unknown/release/lightningcss_node.wasm wasm/. && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.20.0", "lightningcss-linux-x64-gnu": "1.20.0", "lightningcss-win32-x64-msvc": "1.20.0", "lightningcss-darwin-arm64": "1.20.0", "lightningcss-linux-arm64-gnu": "1.20.0", "lightningcss-linux-arm-gnueabihf": "1.20.0", "lightningcss-linux-arm64-musl": "1.20.0", "lightningcss-linux-x64-musl": "1.20.0"}, "gitHead": "4028d4d9026750b31b813ecb4ca5387dba8dd396", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_id": "lightningcss@1.20.0", "_nodeVersion": "18.15.0", "_npmVersion": "9.5.0", "dist": {"integrity": "sha512-4bj8aP+Vi+or8Gwq/hknmicr4PmA8D9uL/3qY0N0daX5vYBMYERGI6Y93nzoeRgQMULq+gtrN/FvJYtH0xNN8g==", "shasum": "efa36a52feae9b0c8537c8e650a7819f549a4a23", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.20.0.tgz", "fileCount": 13, "unpackedSize": 464327, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQClv7Us5FIEFyUBhxCK+ywOieBqm0FtEW09MwWG5/YdfgIhAPPzYy55FZPFEqGx08AsVkzG+NVndU6wY55AHvgRJRxI"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQGRgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRYA/+MSJQAZduAICNqYoelfK4hPlWPeNBraHtoTD3xPgVNUJqicVV\r\nh31D/d8BtE4IAKqjRFH1uhgXtNKnSeSjjuhMeiMzQZQ2j5BXCe5b5I3taulz\r\noRobsVxQmwVu8x4LL9gosh1ciNNaVxGAJS2oqRMenkO6Qhi5bJ9l7+W3oZ9u\r\nrcq6b4zcmLc4yuU/QYWfGmTcX1gROmEkJs5Z1iyzkvVyIg/Jg+g1EkK03ZJa\r\n5p2a1WwfDzvNA9ZTsLjcYcbHkHMAlHZ3CarVTM2GLG1DfHONsjME7CdOkMGr\r\nJt7r47et15F2ur8mTBigQysqRYQuhDlO5NR499Sj+LK5fc9yJu7TZlhRjk78\r\nqiPP0TVGAhCdHn3sG9uqm6LdQ8zsO5jcSr9PGLbCETCYd8Wb/R5310v3s8mm\r\njfwCjvkp0HaoukUWlDVHnObYd5mwoi3jjxcMlbbyCQet4cDG2CyhvvcC84ER\r\nToJ1E8hrCzvwECDNu7hFUPR7V0vwWa3YarD/VXqXCw6o9U8PIyMFKlFG8DaW\r\n7TyDW9R80KpG5T/2PIVWhGDciAWujkR9uhpFs2n+kol3PzT5V0gjOdeOO2FF\r\nD+AbHxKySHTaIFJK/UYYvY52hbrcAGX3dZTG2VH1DhpvbnZ3zm+jHouoFE33\r\n+gyfEijDbD+RFz5wZZhEA55Z4Fh11IpWGw0=\r\n=P0dZ\r\n-----END PGP SIGNATURE-----\r\n", "size": 78591}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.20.0_1681941600520_0.6629170136611668"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-19T22:00:00.743Z", "publish_time": 1681941600743}, "1.21.0": {"name": "lightningcss", "version": "1.21.0", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"types": "./node/index.d.ts", "import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.21.0", "lightningcss-linux-x64-gnu": "1.21.0", "lightningcss-win32-x64-msvc": "1.21.0", "lightningcss-darwin-arm64": "1.21.0", "lightningcss-linux-arm64-gnu": "1.21.0", "lightningcss-linux-arm-gnueabihf": "1.21.0", "lightningcss-linux-arm64-musl": "1.21.0", "lightningcss-linux-x64-musl": "1.21.0"}, "devDependencies": {"@babel/parser": "^7.21.4", "@babel/traverse": "^7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "^5.2.49", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.14", "codemirror": "^6.0.1", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.31.1", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:."}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && cp target/wasm32-unknown-unknown/debug/lightningcss_node.wasm wasm/. && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && cp target/wasm32-unknown-unknown/release/lightningcss_node.wasm wasm/. && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.21.0", "lightningcss-linux-x64-gnu": "1.21.0", "lightningcss-win32-x64-msvc": "1.21.0", "lightningcss-darwin-arm64": "1.21.0", "lightningcss-linux-arm64-gnu": "1.21.0", "lightningcss-linux-arm-gnueabihf": "1.21.0", "lightningcss-linux-arm64-musl": "1.21.0", "lightningcss-linux-x64-musl": "1.21.0"}, "gitHead": "11f4179f81041e14a04536d3bb0618f06de4e454", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_id": "lightningcss@1.21.0", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-HDznZexdDMvC98c79vRE+oW5vFncTlLjJopzK4azReOilq6n4XIscCMhvgiXkstYMM/dCe6FJw0oed06ck8AtA==", "shasum": "31ebf4717f42e801e622186f28cd58db7c914ef7", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.21.0.tgz", "fileCount": 14, "unpackedSize": 467632, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDNQJF6k7KUQKdshyV59ACXYbXsLMp/ASCXK7Ct6uKtHwIgTviHL4IgPtPwyzmJePTpXvW7vXGpHoypZwd2Yn2LQd4="}], "size": 79748}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.21.0_1686117519384_0.4179409722767806"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-07T05:58:39.573Z", "publish_time": 1686117519573, "_source_registry_name": "default"}, "1.21.1": {"name": "lightningcss", "version": "1.21.1", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"types": "./node/index.d.ts", "import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.21.1", "lightningcss-linux-x64-gnu": "1.21.1", "lightningcss-win32-x64-msvc": "1.21.1", "lightningcss-darwin-arm64": "1.21.1", "lightningcss-linux-arm64-gnu": "1.21.1", "lightningcss-linux-arm-gnueabihf": "1.21.1", "lightningcss-linux-arm64-musl": "1.21.1", "lightningcss-linux-x64-musl": "1.21.1"}, "devDependencies": {"@babel/parser": "^7.21.4", "@babel/traverse": "^7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "^5.2.49", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.14", "codemirror": "^6.0.1", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.31.1", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:."}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && cp target/wasm32-unknown-unknown/debug/lightningcss_node.wasm wasm/. && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && cp target/wasm32-unknown-unknown/release/lightningcss_node.wasm wasm/. && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.21.1", "lightningcss-linux-x64-gnu": "1.21.1", "lightningcss-win32-x64-msvc": "1.21.1", "lightningcss-darwin-arm64": "1.21.1", "lightningcss-linux-arm64-gnu": "1.21.1", "lightningcss-linux-arm-gnueabihf": "1.21.1", "lightningcss-linux-arm64-musl": "1.21.1", "lightningcss-linux-x64-musl": "1.21.1"}, "gitHead": "a36c105af3bb83370f64e28a8cf98b7dea52a6e0", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_id": "lightningcss@1.21.1", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-TKkVZzKnJVtGLI+8QMXLH2JdNcxjodA06So+uXA5qelvuReKvPyCJBX/6ZznADA76zNijmDc3OhjxvTBmNtCoA==", "shasum": "4d1f87a08db839009af1a24fd7cc13bd24a5acb5", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.21.1.tgz", "fileCount": 14, "unpackedSize": 467632, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAoj2cF0AL4SJiuEMDMwaCecg5AIpkrvN5NS7YkzU6upAiEAhvppftC8rxiZ8Hz0cQMqLUEDTV2qU9l8GM2KzUDcslQ="}], "size": 79750}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.21.1_1687660759839_0.7560442766240296"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-25T02:39:19.975Z", "publish_time": 1687660759975, "_source_registry_name": "default"}, "1.21.2": {"name": "lightningcss", "version": "1.21.2", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"types": "./node/index.d.ts", "import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.21.2", "lightningcss-linux-x64-gnu": "1.21.2", "lightningcss-win32-x64-msvc": "1.21.2", "lightningcss-darwin-arm64": "1.21.2", "lightningcss-linux-arm64-gnu": "1.21.2", "lightningcss-linux-arm-gnueabihf": "1.21.2", "lightningcss-linux-arm64-musl": "1.21.2", "lightningcss-linux-x64-musl": "1.21.2"}, "devDependencies": {"@babel/parser": "^7.21.4", "@babel/traverse": "^7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "^5.2.49", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.14", "codemirror": "^6.0.1", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.31.1", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:."}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && cp target/wasm32-unknown-unknown/debug/lightningcss_node.wasm wasm/. && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && cp target/wasm32-unknown-unknown/release/lightningcss_node.wasm wasm/. && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.21.2", "lightningcss-linux-x64-gnu": "1.21.2", "lightningcss-win32-x64-msvc": "1.21.2", "lightningcss-darwin-arm64": "1.21.2", "lightningcss-linux-arm64-gnu": "1.21.2", "lightningcss-linux-arm-gnueabihf": "1.21.2", "lightningcss-linux-arm64-musl": "1.21.2", "lightningcss-linux-x64-musl": "1.21.2"}, "gitHead": "e4963a58a813e02b47a7dd0320defcf0c4182512", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_id": "lightningcss@1.21.2", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-6GoGcH4CHGLN6hq5lcmtkKxolXw8nsmgoaYsy6AilwH0vS0GUpKlgFegaf7LmDZqxawjV6LmymQFBZYe+sS45w==", "shasum": "dd64ba83584560f10dedb195fbca70b8fd85890e", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.21.2.tgz", "fileCount": 14, "unpackedSize": 467655, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD2k/UX+DDN8bKnYwt0S8YoMWU/CZmLi8EPM1KgjzJCaQIhALd8CSHNeL1Q64Oy11S2UBfZolBhxwP4LYE74EldbNh2"}], "size": 79752}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.21.2_1688266119326_0.8581151001736362"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-02T02:48:39.475Z", "publish_time": 1688266119475, "_source_registry_name": "default"}, "1.21.3": {"name": "lightningcss", "version": "1.21.3", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"types": "./node/index.d.ts", "import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.21.3", "lightningcss-linux-x64-gnu": "1.21.3", "lightningcss-win32-x64-msvc": "1.21.3", "lightningcss-darwin-arm64": "1.21.3", "lightningcss-linux-arm64-gnu": "1.21.3", "lightningcss-linux-arm-gnueabihf": "1.21.3", "lightningcss-linux-arm64-musl": "1.21.3", "lightningcss-linux-x64-musl": "1.21.3"}, "devDependencies": {"@babel/parser": "^7.21.4", "@babel/traverse": "^7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "^5.2.49", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.14", "codemirror": "^6.0.1", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.31.1", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:."}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && cp target/wasm32-unknown-unknown/debug/lightningcss_node.wasm wasm/. && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && cp target/wasm32-unknown-unknown/release/lightningcss_node.wasm wasm/. && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.21.3", "lightningcss-linux-x64-gnu": "1.21.3", "lightningcss-win32-x64-msvc": "1.21.3", "lightningcss-darwin-arm64": "1.21.3", "lightningcss-linux-arm64-gnu": "1.21.3", "lightningcss-linux-arm-gnueabihf": "1.21.3", "lightningcss-linux-arm64-musl": "1.21.3", "lightningcss-linux-x64-musl": "1.21.3"}, "gitHead": "83bab71e80781b4d2b128f5938099b2f0c8239bd", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_id": "lightningcss@1.21.3", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-15I4wc2jLMpzP4OW71UIR1PmRlOliW6ol2EQQTz1kgG7Y8uKo+c7MUMWhBCO1GCmv4G/YHZlOq5Iw3PyhylgUw==", "shasum": "cfa1b1312b600f1d326fa89207097026c0994c97", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.21.3.tgz", "fileCount": 14, "unpackedSize": 467680, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFZyY+LTocbodhrXmeRQLXLkNsW6sZzY82ImsTdXOKJ1AiBZScp50wsJcgrShxtEbaBO+MhrSHexMo2guDCdZbW3xw=="}], "size": 79759}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.21.3_1688397372686_0.12230565277082062"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-03T15:16:12.970Z", "publish_time": 1688397372970, "_source_registry_name": "default"}, "1.21.4": {"name": "lightningcss", "version": "1.21.4", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"types": "./node/index.d.ts", "import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.21.4", "lightningcss-linux-x64-gnu": "1.21.4", "lightningcss-win32-x64-msvc": "1.21.4", "lightningcss-darwin-arm64": "1.21.4", "lightningcss-linux-arm64-gnu": "1.21.4", "lightningcss-linux-arm-gnueabihf": "1.21.4", "lightningcss-linux-arm64-musl": "1.21.4", "lightningcss-linux-x64-musl": "1.21.4"}, "devDependencies": {"@babel/parser": "^7.21.4", "@babel/traverse": "^7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "^5.2.49", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.14", "codemirror": "^6.0.1", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.31.1", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:."}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && cp target/wasm32-unknown-unknown/debug/lightningcss_node.wasm wasm/. && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && cp target/wasm32-unknown-unknown/release/lightningcss_node.wasm wasm/. && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.21.4", "lightningcss-linux-x64-gnu": "1.21.4", "lightningcss-win32-x64-msvc": "1.21.4", "lightningcss-darwin-arm64": "1.21.4", "lightningcss-linux-arm64-gnu": "1.21.4", "lightningcss-linux-arm-gnueabihf": "1.21.4", "lightningcss-linux-arm64-musl": "1.21.4", "lightningcss-linux-x64-musl": "1.21.4"}, "gitHead": "bd517fde245dcce75a4b2eb21a25b147d54eb64a", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_id": "lightningcss@1.21.4", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-/MhQpBRQgs5j35eC1ZMqOOQqDZyNCUKZRoMp3ftprEOb4vVkjfe1VoknD9BKskCT+bbqBf3Yt9XglJLXXZ+pfA==", "shasum": "f6e9c4cddef5cfd950d02126246d0bc44aef0e0b", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.21.4.tgz", "fileCount": 14, "unpackedSize": 467632, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQClT9ofrih+f+LTtvyte0ttXU0UPLOgoaNPc/ySkAfEoQIgP2tdb+05De9B6U5+pNHbWT8VHsUwBOmLFFy5KnX/ryk="}], "size": 79747}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.21.4_1688441902597_0.7463694417361033"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-04T03:38:22.705Z", "publish_time": 1688441902705, "_source_registry_name": "default"}, "1.21.5": {"name": "lightningcss", "version": "1.21.5", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"types": "./node/index.d.ts", "import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.21.5", "lightningcss-linux-x64-gnu": "1.21.5", "lightningcss-win32-x64-msvc": "1.21.5", "lightningcss-darwin-arm64": "1.21.5", "lightningcss-linux-arm64-gnu": "1.21.5", "lightningcss-linux-arm-gnueabihf": "1.21.5", "lightningcss-linux-arm64-musl": "1.21.5", "lightningcss-linux-x64-musl": "1.21.5"}, "devDependencies": {"@babel/parser": "^7.21.4", "@babel/traverse": "^7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "^5.2.49", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.14", "codemirror": "^6.0.1", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.31.1", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:."}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && cp target/wasm32-unknown-unknown/debug/lightningcss_node.wasm wasm/. && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && cp target/wasm32-unknown-unknown/release/lightningcss_node.wasm wasm/. && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.21.5", "lightningcss-linux-x64-gnu": "1.21.5", "lightningcss-win32-x64-msvc": "1.21.5", "lightningcss-darwin-arm64": "1.21.5", "lightningcss-linux-arm64-gnu": "1.21.5", "lightningcss-linux-arm-gnueabihf": "1.21.5", "lightningcss-linux-arm64-musl": "1.21.5", "lightningcss-linux-x64-musl": "1.21.5"}, "gitHead": "d44a73c3c000ccebbe8355b7cf9272236e573b4d", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_id": "lightningcss@1.21.5", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-/pEUPeih2EwIx9n4T82aOG6CInN83tl/mWlw6B5gWLf36UplQi1L+5p3FUHsdt4fXVfOkkh9KIaM3owoq7ss8A==", "shasum": "1a175329048912f6480c1703ff2957aad0dcedb4", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.21.5.tgz", "fileCount": 14, "unpackedSize": 467632, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCv4F6JagsJ0pt+xd7T5Q/o5KM7MutIUUq1nDmPtpBRmQIgIBZ0qJiT08Rs7hZTVOHfk3pZBquvMHC8C28bGtegqAw="}], "size": 79750}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.21.5_1688531438248_0.020223294533619596"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-05T04:30:38.395Z", "publish_time": 1688531438395, "_source_registry_name": "default"}, "1.21.6": {"name": "lightningcss", "version": "1.21.6", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"types": "./node/index.d.ts", "import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.21.6", "lightningcss-linux-x64-gnu": "1.21.6", "lightningcss-win32-x64-msvc": "1.21.6", "lightningcss-darwin-arm64": "1.21.6", "lightningcss-linux-arm64-gnu": "1.21.6", "lightningcss-linux-arm-gnueabihf": "1.21.6", "lightningcss-linux-arm64-musl": "1.21.6", "lightningcss-linux-x64-musl": "1.21.6", "lightningcss-freebsd-x64": "1.21.6"}, "devDependencies": {"@babel/parser": "^7.21.4", "@babel/traverse": "^7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "^5.2.49", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.14", "codemirror": "^6.0.1", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.31.1", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:."}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && cp target/wasm32-unknown-unknown/debug/lightningcss_node.wasm wasm/. && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && cp target/wasm32-unknown-unknown/release/lightningcss_node.wasm wasm/. && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.21.6", "lightningcss-linux-x64-gnu": "1.21.6", "lightningcss-win32-x64-msvc": "1.21.6", "lightningcss-darwin-arm64": "1.21.6", "lightningcss-linux-arm64-gnu": "1.21.6", "lightningcss-linux-arm-gnueabihf": "1.21.6", "lightningcss-linux-arm64-musl": "1.21.6", "lightningcss-linux-x64-musl": "1.21.6", "lightningcss-freebsd-x64": "1.21.6"}, "gitHead": "f485eb51d13e677a674cb10e170c71138446b71d", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_id": "lightningcss@1.21.6", "_nodeVersion": "18.17.0", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-6J1/sHNgeLDiq0x1uPwKNLP4VWxdynH4+ewmJX28ivF4x/EmBAcBtIKkDGWZKgpy2Zq6FhgVWRWG7pf+fWybHw==", "shasum": "fc98c0e4c9d1b47f05cf7ac426823d7f71f81590", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.21.6.tgz", "fileCount": 14, "unpackedSize": 470890, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA2Wypr9iEyxTxz4SWbnlzvnxtAcswgN6ogI2dPqgUUNAiEAo6xlkajShVHrtnOtop0UMgPdS5yn5+O/xEXT7fLeacw="}], "size": 80181}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.21.6_1692511475368_0.11446468796761722"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-20T06:04:35.535Z", "publish_time": 1692511475535, "_source_registry_name": "default"}, "1.21.7": {"name": "lightningcss", "version": "1.21.7", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"types": "./node/index.d.ts", "import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.21.7", "lightningcss-linux-x64-gnu": "1.21.7", "lightningcss-win32-x64-msvc": "1.21.7", "lightningcss-darwin-arm64": "1.21.7", "lightningcss-linux-arm64-gnu": "1.21.7", "lightningcss-linux-arm-gnueabihf": "1.21.7", "lightningcss-linux-arm64-musl": "1.21.7", "lightningcss-linux-x64-musl": "1.21.7", "lightningcss-freebsd-x64": "1.21.7"}, "devDependencies": {"@babel/parser": "^7.21.4", "@babel/traverse": "^7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "^5.2.49", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.14", "codemirror": "^6.0.1", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.31.1", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:."}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && cp target/wasm32-unknown-unknown/debug/lightningcss_node.wasm wasm/. && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && cp target/wasm32-unknown-unknown/release/lightningcss_node.wasm wasm/. && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.21.7", "lightningcss-linux-x64-gnu": "1.21.7", "lightningcss-win32-x64-msvc": "1.21.7", "lightningcss-darwin-arm64": "1.21.7", "lightningcss-linux-arm64-gnu": "1.21.7", "lightningcss-linux-arm-gnueabihf": "1.21.7", "lightningcss-linux-arm64-musl": "1.21.7", "lightningcss-linux-x64-musl": "1.21.7", "lightningcss-freebsd-x64": "1.21.7"}, "gitHead": "393013928888d47ec7684d52ed79f758d371bd7b", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_id": "lightningcss@1.21.7", "_nodeVersion": "18.17.0", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-xITZyh5sLFwRPYUSw15T00Rm7gcQ1qOPuQwNOcvHsTm6nLWTQ723w7zl42wrC5t+xtdg6FPmnXHml1nZxxvp1w==", "shasum": "9b864625a4ad734aeaecb28649c20705e335e199", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.21.7.tgz", "fileCount": 14, "unpackedSize": 470890, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICVbE3SqKsicKND4/xvPHTJoik7f3Cmr6Zi9yIFVlH5AAiBNgjPDgj6IJnoIPkwmUibvG1rLtKZJ9fyVRWH22aQamw=="}], "size": 80181}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.21.7_1692555072353_0.518765809783853"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-20T18:11:12.643Z", "publish_time": 1692555072643, "_source_registry_name": "default"}, "1.21.8": {"name": "lightningcss", "version": "1.21.8", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"types": "./node/index.d.ts", "import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.21.8", "lightningcss-linux-x64-gnu": "1.21.8", "lightningcss-win32-x64-msvc": "1.21.8", "lightningcss-darwin-arm64": "1.21.8", "lightningcss-linux-arm64-gnu": "1.21.8", "lightningcss-linux-arm-gnueabihf": "1.21.8", "lightningcss-linux-arm64-musl": "1.21.8", "lightningcss-linux-x64-musl": "1.21.8", "lightningcss-freebsd-x64": "1.21.8"}, "devDependencies": {"@babel/parser": "^7.21.4", "@babel/traverse": "^7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "^5.2.49", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.14", "codemirror": "^6.0.1", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.31.1", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:."}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && wasm-opt target/wasm32-unknown-unknown/debug/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && wasm-opt target/wasm32-unknown-unknown/release/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "tsc": "tsc -p node/tsconfig.json", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.21.8", "lightningcss-linux-x64-gnu": "1.21.8", "lightningcss-win32-x64-msvc": "1.21.8", "lightningcss-darwin-arm64": "1.21.8", "lightningcss-linux-arm64-gnu": "1.21.8", "lightningcss-linux-arm-gnueabihf": "1.21.8", "lightningcss-linux-arm64-musl": "1.21.8", "lightningcss-linux-x64-musl": "1.21.8", "lightningcss-freebsd-x64": "1.21.8"}, "gitHead": "81cb0daee30dad924b14af7fbc739ddfe31d7f9e", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_id": "lightningcss@1.21.8", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-jEqaL7m/ZckZJjlMAfycr1Kpz7f93k6n7KGF5SJjuPSm6DWI6h3ayLZmgRHgy1OfrwoCed6h4C/gHYPOd1OFMA==", "shasum": "a02e4a8979208ffb61d7c6deebb75c4abce0b5d6", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.21.8.tgz", "fileCount": 14, "unpackedSize": 471114, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDyD5c/zsCUR2vjh5XUqLNz1ctHUpTlNx6YxjvcJPI1PwIhAMIp0XedR27LXxSB1wiE6X4XUwCuKD0urPINkWxa+CHC"}], "size": 80244}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.21.8_1694407662207_0.9118146797401456"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-11T04:47:42.365Z", "publish_time": 1694407662365, "_source_registry_name": "default"}, "1.22.0": {"name": "lightningcss", "version": "1.22.0", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"types": "./node/index.d.ts", "import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.22.0", "lightningcss-linux-x64-gnu": "1.22.0", "lightningcss-win32-x64-msvc": "1.22.0", "lightningcss-darwin-arm64": "1.22.0", "lightningcss-linux-arm64-gnu": "1.22.0", "lightningcss-linux-arm-gnueabihf": "1.22.0", "lightningcss-linux-arm64-musl": "1.22.0", "lightningcss-linux-x64-musl": "1.22.0", "lightningcss-freebsd-x64": "1.22.0"}, "devDependencies": {"@babel/parser": "^7.21.4", "@babel/traverse": "^7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "^5.2.49", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.14", "codemirror": "^6.0.1", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.31.1", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:."}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && wasm-opt target/wasm32-unknown-unknown/debug/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && wasm-opt target/wasm32-unknown-unknown/release/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "tsc": "tsc -p node/tsconfig.json", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.22.0", "lightningcss-linux-x64-gnu": "1.22.0", "lightningcss-win32-x64-msvc": "1.22.0", "lightningcss-darwin-arm64": "1.22.0", "lightningcss-linux-arm64-gnu": "1.22.0", "lightningcss-linux-arm-gnueabihf": "1.22.0", "lightningcss-linux-arm64-musl": "1.22.0", "lightningcss-linux-x64-musl": "1.22.0", "lightningcss-freebsd-x64": "1.22.0"}, "gitHead": "7ff93ca5c69ba9df415e1e2319d275e2cec249d7", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_id": "lightningcss@1.22.0", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-+z0qvwRVzs4XGRXelnWRNwqsXUx8k3bSkbP8vD42kYKSk3z9OM2P3e/gagT7ei/gwh8DTS80LZOFZV6lm8Z8Fg==", "shasum": "76c9a17925e660741858e88b774172cb1923bb4a", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.22.0.tgz", "fileCount": 14, "unpackedSize": 472252, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGkeUYDnIc+FnkB6llAbtV2C7/NX1V9QhdCRcieEeyrdAiEA4U47dmBiK8OLU0OJI1OaqDNg8rM9GFawZJ9pB8tHTN4="}], "size": 80488}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.22.0_1694990966395_0.04153840999613734"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-17T22:49:26.580Z", "publish_time": 1694990966580, "_source_registry_name": "default"}, "1.22.1": {"name": "lightningcss", "version": "1.22.1", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"types": "./node/index.d.ts", "import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3"}, "devDependencies": {"@babel/parser": "^7.21.4", "@babel/traverse": "^7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "^5.3.29", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.16", "codemirror": "^6.0.1", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.31.1", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:."}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && wasm-opt target/wasm32-unknown-unknown/debug/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && wasm-opt target/wasm32-unknown-unknown/release/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "tsc": "tsc -p node/tsconfig.json", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.22.1", "lightningcss-linux-x64-gnu": "1.22.1", "lightningcss-win32-x64-msvc": "1.22.1", "lightningcss-darwin-arm64": "1.22.1", "lightningcss-linux-arm64-gnu": "1.22.1", "lightningcss-linux-arm-gnueabihf": "1.22.1", "lightningcss-linux-arm64-musl": "1.22.1", "lightningcss-linux-x64-musl": "1.22.1", "lightningcss-freebsd-x64": "1.22.1"}, "_id": "lightningcss@1.22.1", "gitHead": "4994306f8f8d98a2b37433fced50efdacbbab901", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-Fy45PhibiNXkm0cK5FJCbfO8Y6jUpD/YcHf/BtuI+jvYYqSXKF4muk61jjE8YxCR9y+hDYIWSzHTc+bwhDE6rQ==", "shasum": "8108ddecb2e859032bdd99908abd2b37515b1750", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.22.1.tgz", "fileCount": 14, "unpackedSize": 472252, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC6Ig5XqW1tATPIjcNbDd8dMiUjyHtv98C4NPq05/Rn0QIgRguyU2SoomnEAOMADT2BqKSFTAqqnMUIZZCgT8DXLF0="}], "size": 80489}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.22.1_1699395840854_0.24398579912702778"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-07T22:24:01.106Z", "publish_time": 1699395841106, "_source_registry_name": "default"}, "1.23.0": {"name": "lightningcss", "version": "1.23.0", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"types": "./node/index.d.ts", "import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3"}, "devDependencies": {"@babel/parser": "^7.21.4", "@babel/traverse": "^7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "^5.3.29", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.16", "codemirror": "^6.0.1", "cssnano": "^5.0.8", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.31.1", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:."}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && wasm-opt target/wasm32-unknown-unknown/debug/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && wasm-opt target/wasm32-unknown-unknown/release/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "tsc": "tsc -p node/tsconfig.json", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.23.0", "lightningcss-linux-x64-gnu": "1.23.0", "lightningcss-win32-x64-msvc": "1.23.0", "lightningcss-darwin-arm64": "1.23.0", "lightningcss-linux-arm64-gnu": "1.23.0", "lightningcss-linux-arm-gnueabihf": "1.23.0", "lightningcss-linux-arm64-musl": "1.23.0", "lightningcss-linux-x64-musl": "1.23.0", "lightningcss-freebsd-x64": "1.23.0"}, "_id": "lightningcss@1.23.0", "gitHead": "b47f496a86075adcb6719aee3a8867e749a880b9", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-SEArWKMHhqn/0QzOtclIwH5pXIYQOUEkF8DgICd/105O+GCgd7jxjNod/QPnBCSWvpRHQBGVz5fQ9uScby03zA==", "shasum": "58c94a533d02d8416d4f2ec9ab87641f61943c78", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.23.0.tgz", "fileCount": 14, "unpackedSize": 477293, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD3BnZDYk5Y/Cmvy3xseL2q2EPlzLRazwOokXW2kTfwBQIhALaBd6KswsuDNplVQeKkcr2pI+7E+s4WgmYkEJjo20Nb"}], "size": 81364}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.23.0_1705276092805_0.987008242601793"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-14T23:48:13.008Z", "publish_time": 1705276093008, "_source_registry_name": "default"}, "1.24.0": {"name": "lightningcss", "version": "1.24.0", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"types": "./node/index.d.ts", "import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3"}, "devDependencies": {"@babel/parser": "^7.21.4", "@babel/traverse": "^7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "~5.5.0", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.17", "caniuse-lite": "^1.0.30001585", "codemirror": "^6.0.1", "cssnano": "^5.0.8", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.31.1", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:."}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && wasm-opt target/wasm32-unknown-unknown/debug/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && wasm-opt target/wasm32-unknown-unknown/release/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "tsc": "tsc -p node/tsconfig.json", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.24.0", "lightningcss-linux-x64-gnu": "1.24.0", "lightningcss-win32-x64-msvc": "1.24.0", "lightningcss-darwin-arm64": "1.24.0", "lightningcss-linux-arm64-gnu": "1.24.0", "lightningcss-linux-arm-gnueabihf": "1.24.0", "lightningcss-linux-arm64-musl": "1.24.0", "lightningcss-linux-x64-musl": "1.24.0", "lightningcss-freebsd-x64": "1.24.0"}, "_id": "lightningcss@1.24.0", "gitHead": "30b9d79b3d277dfb90d90d78cd9182ac755ad6cf", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_nodeVersion": "18.19.1", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-y36QEEDVx4IM7/yIZNsZJMRREIu26WzTsauIysf5s76YeCmlSbRZS7aC97IGPuoFRnyZ5Wx43OBsQBFB5Ne7ng==", "shasum": "41bcdf5de381ae5fe1e7f9a2bf6bcaded556082c", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.24.0.tgz", "fileCount": 14, "unpackedSize": 480094, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDp0eOFWeLqPlk3tL6RGkqlNdFG8tZl8PQg4Ov0TZP1uQIgdg7IpdRnuJMdl8DbzchdU/o8PnW0SYe8Qw+wGykT9eM="}], "size": 82060}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.24.0_1708648915535_0.11672508386851366"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-23T00:41:55.749Z", "publish_time": 1708648915749, "_source_registry_name": "default"}, "1.24.1": {"name": "lightningcss", "version": "1.24.1", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"types": "./node/index.d.ts", "import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3"}, "devDependencies": {"@babel/parser": "^7.21.4", "@babel/traverse": "^7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "~5.5.0", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.17", "caniuse-lite": "^1.0.30001585", "codemirror": "^6.0.1", "cssnano": "^5.0.8", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.31.1", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:."}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && wasm-opt target/wasm32-unknown-unknown/debug/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && wasm-opt target/wasm32-unknown-unknown/release/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "tsc": "tsc -p node/tsconfig.json", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.24.1", "lightningcss-linux-x64-gnu": "1.24.1", "lightningcss-win32-x64-msvc": "1.24.1", "lightningcss-darwin-arm64": "1.24.1", "lightningcss-linux-arm64-gnu": "1.24.1", "lightningcss-linux-arm-gnueabihf": "1.24.1", "lightningcss-linux-arm64-musl": "1.24.1", "lightningcss-linux-x64-musl": "1.24.1", "lightningcss-freebsd-x64": "1.24.1"}, "_id": "lightningcss@1.24.1", "gitHead": "baa1a2b7fa52eeb3827f8edcc2e14de33cd69ad0", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_nodeVersion": "18.19.1", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-kUpHOLiH5GB0ERSv4pxqlL0RYKnOXtgGtVe7shDGfhS0AZ4D1ouKFYAcLcZhql8aMspDNzaUCumGHZ78tb2fTg==", "shasum": "8b86a5ee6e6ae9e035ff92892bd047b8d687581e", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.24.1.tgz", "fileCount": 14, "unpackedSize": 480081, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDgU/zO8Hmdc/h1T/aDoNUOXQcwNWC9l9N8eUiniffamwIhALtzCFBF37ZohaSlUAvX3XE7fNljWF1VIYPd+EH8/FLN"}], "size": 82059}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.24.1_1710476610084_0.9600599489994011"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-15T04:23:30.281Z", "publish_time": 1710476610281, "_source_registry_name": "default"}, "1.25.0": {"name": "lightningcss", "version": "1.25.0", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"types": "./node/index.d.ts", "import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3"}, "devDependencies": {"@babel/parser": "^7.21.4", "@babel/traverse": "^7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "~5.5.28", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.19", "caniuse-lite": "^1.0.30001620", "codemirror": "^6.0.1", "cssnano": "^5.0.8", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.31.1", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:."}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && wasm-opt target/wasm32-unknown-unknown/debug/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && wasm-opt target/wasm32-unknown-unknown/release/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "tsc": "tsc -p node/tsconfig.json", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.25.0", "lightningcss-linux-x64-gnu": "1.25.0", "lightningcss-win32-x64-msvc": "1.25.0", "lightningcss-darwin-arm64": "1.25.0", "lightningcss-linux-arm64-gnu": "1.25.0", "lightningcss-linux-arm-gnueabihf": "1.25.0", "lightningcss-linux-arm64-musl": "1.25.0", "lightningcss-linux-x64-musl": "1.25.0", "lightningcss-freebsd-x64": "1.25.0"}, "_id": "lightningcss@1.25.0", "gitHead": "81d21b9f5201c6eb8365fbb4fa81cbd947c3474f", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-B08o6QQikGaY4rPuQohtFVE+X2++mm/QemwAJ/1sgnMgTwwUnafJbTmSSBWC8Tv4JPfhelXZB6sWA0Y/6eYJmQ==", "shasum": "6d2409ebe90c60bf420499fadb3ac72e81df44b0", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.25.0.tgz", "fileCount": 14, "unpackedSize": 486606, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA3BTUoU4TtFdgfIsKD6uZ8awN9BK6yt9dC2lERtre9EAiBt8nEtgAouB/c+71mnkNVqDgqTh+J8kPXugSRo9xIi/Q=="}], "size": 83170}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.25.0_1715973777307_0.5247496033093162"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-17T19:22:57.617Z", "publish_time": 1715973777617, "_source_registry_name": "default"}, "1.25.1": {"name": "lightningcss", "version": "1.25.1", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"types": "./node/index.d.ts", "import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3"}, "devDependencies": {"@babel/parser": "^7.21.4", "@babel/traverse": "^7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "~5.5.28", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.19", "caniuse-lite": "^1.0.30001620", "codemirror": "^6.0.1", "cssnano": "^5.0.8", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.31.1", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:."}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && wasm-opt target/wasm32-unknown-unknown/debug/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && wasm-opt target/wasm32-unknown-unknown/release/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "tsc": "tsc -p node/tsconfig.json", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.25.1", "lightningcss-linux-x64-gnu": "1.25.1", "lightningcss-win32-x64-msvc": "1.25.1", "lightningcss-darwin-arm64": "1.25.1", "lightningcss-linux-arm64-gnu": "1.25.1", "lightningcss-linux-arm-gnueabihf": "1.25.1", "lightningcss-linux-arm64-musl": "1.25.1", "lightningcss-linux-x64-musl": "1.25.1", "lightningcss-freebsd-x64": "1.25.1"}, "_id": "lightningcss@1.25.1", "gitHead": "fa6c015317e5cca29fa8a09b12d09ac53dcfbc77", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_nodeVersion": "18.20.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-V0RMVZzK1+rCHpymRv4URK2lNhIRyO8g7U7zOFwVAhJuat74HtkjIQpQRKNCwFEYkRGpafOpmXXLoaoBcyVtBg==", "shasum": "6136c166ac61891fbc1af7fba7b620c50f58fb2d", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.25.1.tgz", "fileCount": 14, "unpackedSize": 486606, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDQ7Ljx+GdemS3JSWkkf1oPSLQKrFUb0tpMze7F1H027AIgdl0j768tFOU8c9e3vNooF/k3JFj9PttyzDzCzBTs4Pw="}], "size": 83172}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.25.1_1716617200439_0.5272192716022965"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-25T06:06:40.629Z", "publish_time": 1716617200629, "_source_registry_name": "default"}, "1.26.0": {"name": "lightningcss", "version": "1.26.0", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"types": "./node/index.d.ts", "import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3"}, "devDependencies": {"@babel/parser": "^7.21.4", "@babel/traverse": "^7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "~5.5.44", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.20", "caniuse-lite": "^1.0.30001649", "codemirror": "^6.0.1", "cssnano": "^5.0.8", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.31.1", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:."}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && wasm-opt target/wasm32-unknown-unknown/debug/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && wasm-opt target/wasm32-unknown-unknown/release/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "tsc": "tsc -p node/tsconfig.json", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.26.0", "lightningcss-linux-x64-gnu": "1.26.0", "lightningcss-win32-x64-msvc": "1.26.0", "lightningcss-win32-arm64-msvc": "1.26.0", "lightningcss-darwin-arm64": "1.26.0", "lightningcss-linux-arm64-gnu": "1.26.0", "lightningcss-linux-arm-gnueabihf": "1.26.0", "lightningcss-linux-arm64-musl": "1.26.0", "lightningcss-linux-x64-musl": "1.26.0", "lightningcss-freebsd-x64": "1.26.0"}, "_id": "lightningcss@1.26.0", "gitHead": "0bcd896e81a8a2c5d847f626a37e2cffea79e2a0", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-a/XZ5hdgifrofQJUArr5AiJjx26SwMam3SJUSMjgebZbESZ96i+6Qsl8tLi0kaUsdMzBWXh9sN1Oe6hp2/dkQw==", "shasum": "ca2ff6ebabb044c6e49e57f9491a6f85db03b256", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.26.0.tgz", "fileCount": 14, "unpackedSize": 491160, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBhmenwb22sMuaCisr59mXna/UWZsebXhH5nk3Qf7qm7AiA/Gm90pkqDeFVRdFva4dU58BdwYT1AheHhEc6j3WwgfQ=="}], "size": 83896}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.26.0_1722958395845_0.11599933489207981"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-06T15:33:16.081Z", "publish_time": 1722958396081, "_source_registry_name": "default"}, "1.27.0": {"name": "lightningcss", "version": "1.27.0", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"types": "./node/index.d.ts", "import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3"}, "devDependencies": {"@babel/parser": "^7.21.4", "@babel/traverse": "^7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "~5.5.51", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.20", "caniuse-lite": "^1.0.30001660", "codemirror": "^6.0.1", "cssnano": "^5.0.8", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.31.1", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:."}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && wasm-opt target/wasm32-unknown-unknown/debug/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && wasm-opt target/wasm32-unknown-unknown/release/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "tsc": "tsc -p node/tsconfig.json", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.27.0", "lightningcss-linux-x64-gnu": "1.27.0", "lightningcss-win32-x64-msvc": "1.27.0", "lightningcss-win32-arm64-msvc": "1.27.0", "lightningcss-darwin-arm64": "1.27.0", "lightningcss-linux-arm64-gnu": "1.27.0", "lightningcss-linux-arm-gnueabihf": "1.27.0", "lightningcss-linux-arm64-musl": "1.27.0", "lightningcss-linux-x64-musl": "1.27.0", "lightningcss-freebsd-x64": "1.27.0"}, "_id": "lightningcss@1.27.0", "gitHead": "eb49015cf887ae720b80a2856ccbdf61bf940ef1", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-8f7aNmS1+etYSLHht0fQApPc2kNO8qGRutifN5rVIc6Xo6ABsEbqOr758UwI7ALVbTt4x1fllKt0PYgzD9S3yQ==", "shasum": "d4608e63044343836dd9769f6c8b5d607867649a", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.27.0.tgz", "fileCount": 14, "unpackedSize": 491858, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCFSo/YdAOwZieRkUAOMJZ/rEQhy2wZtCE54scDHuTibQIgHTg/n/EXPl6sgaLfVcgRvw3gPA9LNABp+1thJAUCWBI="}], "size": 84035}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.27.0_1726023665105_0.9399921535135849"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-11T03:01:05.281Z", "publish_time": 1726023665281, "_source_registry_name": "default"}, "1.28.0": {"name": "lightningcss", "version": "1.28.0", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"types": "./node/index.d.ts", "import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3"}, "devDependencies": {"@babel/parser": "^7.21.4", "@babel/traverse": "^7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "~5.6.12", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.20", "caniuse-lite": "^1.0.30001677", "codemirror": "^6.0.1", "cssnano": "^5.0.8", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.31.1", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:.", "caniuse-lite": "^1.0.30001677"}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && wasm-opt target/wasm32-unknown-unknown/debug/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && wasm-opt target/wasm32-unknown-unknown/release/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "tsc": "tsc -p node/tsconfig.json", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.28.0", "lightningcss-linux-x64-gnu": "1.28.0", "lightningcss-win32-x64-msvc": "1.28.0", "lightningcss-win32-arm64-msvc": "1.28.0", "lightningcss-darwin-arm64": "1.28.0", "lightningcss-linux-arm64-gnu": "1.28.0", "lightningcss-linux-arm-gnueabihf": "1.28.0", "lightningcss-linux-arm64-musl": "1.28.0", "lightningcss-linux-x64-musl": "1.28.0", "lightningcss-freebsd-x64": "1.28.0"}, "_id": "lightningcss@1.28.0", "gitHead": "8a67583105757e4a25378d65d243b87a345b2c2d", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-wKfbUqWC+/vM29bxFlfuVHlhSRg8dNKhA5RjuoodLpZx32uozV3+z0SAXLHCynbBOiann/VVWOIyxP1QZs/GLA==", "shasum": "d91231a8653a1388e57051faf602e5d580572d54", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.28.0.tgz", "fileCount": 14, "unpackedSize": 492062, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGGUtDjn+mGfP3YBBv38/Vm2y2ejYQkSVES9Hvxe3VuHAiEAu1dlp25UFf/81BV5+0hi9HlHoR/iwxDfolG+Kfo7Ufc="}], "size": 84054}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.28.0_1730668689024_0.41723643585238857"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-03T21:18:09.208Z", "publish_time": 1730668689208, "_source_registry_name": "default"}, "1.28.1": {"name": "lightningcss", "version": "1.28.1", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"types": "./node/index.d.ts", "import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3"}, "devDependencies": {"@babel/parser": "^7.21.4", "@babel/traverse": "^7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "~5.6.12", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.20", "caniuse-lite": "^1.0.30001677", "codemirror": "^6.0.1", "cssnano": "^5.0.8", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.31.1", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:.", "caniuse-lite": "^1.0.30001677"}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && wasm-opt target/wasm32-unknown-unknown/debug/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && wasm-opt target/wasm32-unknown-unknown/release/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "tsc": "tsc -p node/tsconfig.json", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.28.1", "lightningcss-linux-x64-gnu": "1.28.1", "lightningcss-win32-x64-msvc": "1.28.1", "lightningcss-win32-arm64-msvc": "1.28.1", "lightningcss-darwin-arm64": "1.28.1", "lightningcss-linux-arm64-gnu": "1.28.1", "lightningcss-linux-arm-gnueabihf": "1.28.1", "lightningcss-linux-arm64-musl": "1.28.1", "lightningcss-linux-x64-musl": "1.28.1", "lightningcss-freebsd-x64": "1.28.1"}, "_id": "lightningcss@1.28.1", "gitHead": "a3390fd4140ca87f5035595d22bc9357cf72177e", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-KRDkHlLlNj3DWh79CDt93fPlRJh2W1AuHV0ZSZAMMuN7lqlsZTV5842idfS1urWG8q9tc17velp1gCXhY7sLnQ==", "shasum": "311b44052e4dcb17e31929a584a9a68864a456ed", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.28.1.tgz", "fileCount": 14, "unpackedSize": 492062, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDMelfmWBWCF1l6vx9xMD9s7kJveuuugBOzmairvQwhEgIhAKt8C2+n8yqg+AFBkoZBVo8ioaTF5zfO6UDrYFyj322i"}], "size": 84056}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.28.1_1730674724419_0.15472475220964466"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-03T22:58:44.671Z", "publish_time": 1730674724671, "_source_registry_name": "default"}, "1.28.2": {"name": "lightningcss", "version": "1.28.2", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"types": "./node/index.d.ts", "import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3"}, "devDependencies": {"@babel/parser": "^7.21.4", "@babel/traverse": "^7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "~5.6.18", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.20", "caniuse-lite": "^1.0.30001684", "codemirror": "^6.0.1", "cssnano": "^5.0.8", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.31.1", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:.", "caniuse-lite": "^1.0.30001677"}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && wasm-opt target/wasm32-unknown-unknown/debug/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && wasm-opt target/wasm32-unknown-unknown/release/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "tsc": "tsc -p node/tsconfig.json", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.28.2", "lightningcss-linux-x64-gnu": "1.28.2", "lightningcss-win32-x64-msvc": "1.28.2", "lightningcss-win32-arm64-msvc": "1.28.2", "lightningcss-darwin-arm64": "1.28.2", "lightningcss-linux-arm64-gnu": "1.28.2", "lightningcss-linux-arm-gnueabihf": "1.28.2", "lightningcss-linux-arm64-musl": "1.28.2", "lightningcss-linux-x64-musl": "1.28.2", "lightningcss-freebsd-x64": "1.28.2"}, "_id": "lightningcss@1.28.2", "gitHead": "9b2e8bbe732d7c101272ddab03ac21b88bf55c4a", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-ePLRrbt3fgjXI5VFZOLbvkLD5ZRuxGKm+wJ3ujCqBtL3NanDHPo/5zicR5uEKAPiIjBYF99BM4K4okvMznjkVA==", "shasum": "cc26fad9ad64a621bd39ac6248095891cf584cce", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.28.2.tgz", "fileCount": 14, "unpackedSize": 492062, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFbmFspnn2OklwheLQQEuibLaVEs5QutV+s8CNYOZtOrAiEAwO/7sOWu0X8kktTXq8jn4PNtqRWMvnPnaj3BcGt6yF0="}], "size": 84058}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lightningcss_1.28.2_1732512313897_0.6023756311890227"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-25T05:25:14.090Z", "publish_time": 1732512314090, "_source_registry_name": "default"}, "1.29.0": {"name": "lightningcss", "version": "1.29.0", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"types": "./node/index.d.ts", "import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3"}, "devDependencies": {"@babel/parser": "7.21.4", "@babel/traverse": "7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "~5.6.26", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.20", "caniuse-lite": "^1.0.30001690", "codemirror": "^6.0.1", "cssnano": "^7.0.6", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.33.5", "typescript": "^5.7.2", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:.", "caniuse-lite": "^1.0.30001677"}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && wasm-opt target/wasm32-unknown-unknown/debug/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && wasm-opt target/wasm32-unknown-unknown/release/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "tsc": "tsc -p node/tsconfig.json", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.29.0", "lightningcss-linux-x64-gnu": "1.29.0", "lightningcss-win32-x64-msvc": "1.29.0", "lightningcss-win32-arm64-msvc": "1.29.0", "lightningcss-darwin-arm64": "1.29.0", "lightningcss-linux-arm64-gnu": "1.29.0", "lightningcss-linux-arm-gnueabihf": "1.29.0", "lightningcss-linux-arm64-musl": "1.29.0", "lightningcss-linux-x64-musl": "1.29.0", "lightningcss-freebsd-x64": "1.29.0"}, "_id": "lightningcss@1.29.0", "gitHead": "222fa9f7ad8b4004afd7a30c2f99cab3c8621191", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-4BkG0a3g6ZJ5F5iQ22lZsod0KJrIzYdbiBCMo9FgpDrxCx+uUapwkZmUVgisiZvHUj53BJGQiWWT+YXr1ZcZQQ==", "shasum": "f7b376fbbe8367091a09529ad48e715ff09ad98c", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.29.0.tgz", "fileCount": 14, "unpackedSize": 499892, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDcykaNDaIwL06iie2oCMN5lH5TGjAmcpNz+9Fga4EXJwIgUqK3czyX/Lg0ZNLzK/KWhTM3gUqj4yoDuzx1glZgdks="}], "size": 85395}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/lightningcss_1.29.0_1736401686201_0.7057597621897034"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-09T05:48:06.402Z", "publish_time": 1736401686402, "_source_registry_name": "default"}, "1.29.1": {"name": "lightningcss", "version": "1.29.1", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"types": "./node/index.d.ts", "import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^1.0.3"}, "devDependencies": {"@babel/parser": "7.21.4", "@babel/traverse": "7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "~5.6.26", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.20", "caniuse-lite": "^1.0.30001690", "codemirror": "^6.0.1", "cssnano": "^7.0.6", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.33.5", "typescript": "^5.7.2", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:.", "caniuse-lite": "^1.0.30001677"}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && wasm-opt target/wasm32-unknown-unknown/debug/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && wasm-opt target/wasm32-unknown-unknown/release/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "tsc": "tsc -p node/tsconfig.json", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.29.1", "lightningcss-linux-x64-gnu": "1.29.1", "lightningcss-win32-x64-msvc": "1.29.1", "lightningcss-win32-arm64-msvc": "1.29.1", "lightningcss-darwin-arm64": "1.29.1", "lightningcss-linux-arm64-gnu": "1.29.1", "lightningcss-linux-arm-gnueabihf": "1.29.1", "lightningcss-linux-arm64-musl": "1.29.1", "lightningcss-linux-x64-musl": "1.29.1", "lightningcss-freebsd-x64": "1.29.1"}, "_id": "lightningcss@1.29.1", "gitHead": "b10f9baf8878411bf2b09dfe8d64ba09ef7a4eac", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-FmGoeD4S05ewj+AkhTY+D+myDvXI6eL27FjHIjoyUkO/uw7WZD1fBVs0QxeYWa7E17CUHJaYX/RUGISCtcrG4Q==", "shasum": "1d4d62332fc5ba4b6c28e04a8c5638c76019702b", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.29.1.tgz", "fileCount": 14, "unpackedSize": 499892, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCS8R4BqqAQrIOfoQPBtA0BqISGYXtXhZXxCRQQY14s5QIhALSAJe2NaOGSTv7yyo+1TQ+0WXYTSvEEa7xWj8vCDewC"}], "size": 85397}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/lightningcss_1.29.1_1736445783372_0.4931658587705514"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-09T18:03:03.583Z", "publish_time": 1736445783583, "_source_registry_name": "default"}, "1.29.2": {"name": "lightningcss", "version": "1.29.2", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"types": "./node/index.d.ts", "import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^2.0.3"}, "devDependencies": {"@babel/parser": "7.21.4", "@babel/traverse": "7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "~5.7.0", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.20", "caniuse-lite": "^1.0.30001702", "codemirror": "^6.0.1", "cssnano": "^7.0.6", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.33.5", "typescript": "^5.7.2", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:."}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && wasm-opt target/wasm32-unknown-unknown/debug/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && wasm-opt target/wasm32-unknown-unknown/release/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "tsc": "tsc -p node/tsconfig.json", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.29.2", "lightningcss-linux-x64-gnu": "1.29.2", "lightningcss-win32-x64-msvc": "1.29.2", "lightningcss-win32-arm64-msvc": "1.29.2", "lightningcss-darwin-arm64": "1.29.2", "lightningcss-linux-arm64-gnu": "1.29.2", "lightningcss-linux-arm-gnueabihf": "1.29.2", "lightningcss-linux-arm64-musl": "1.29.2", "lightningcss-linux-x64-musl": "1.29.2", "lightningcss-freebsd-x64": "1.29.2"}, "_id": "lightningcss@1.29.2", "gitHead": "f2303df29448a55c5f4f284f747eb53760769fe4", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-6b6gd/RUXKaw5keVdSEtqFVdzWnU5jMxTUjA2bVcMNPLwSQ08Sv/UodBVtETLCn7k4S1Ibxwh7k68IwLZPgKaA==", "shasum": "f5f0fd6e63292a232697e6fe709da5b47624def3", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.29.2.tgz", "fileCount": 14, "unpackedSize": 500013, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDC+NKc9nztmk797hhpOESJGm+VJCLZKrwusiiNvFIHKAIgfb8MpadVCALVoI90o8/KxKZ9YWDIJhN526OUPLhsIDo="}], "size": 85424}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/lightningcss_1.29.2_1741242141899_0.9755732107520936"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-06T06:22:22.083Z", "publish_time": 1741242142083, "_source_registry_name": "default"}, "1.29.3": {"name": "lightningcss", "version": "1.29.3", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"types": "./node/index.d.ts", "import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^2.0.3"}, "devDependencies": {"@babel/parser": "7.21.4", "@babel/traverse": "7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "~5.7.3", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.21", "caniuse-lite": "^1.0.30001704", "codemirror": "^6.0.1", "cssnano": "^7.0.6", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.33.5", "typescript": "^5.7.2", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:."}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && wasm-opt target/wasm32-unknown-unknown/debug/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && wasm-opt target/wasm32-unknown-unknown/release/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "tsc": "tsc -p node/tsconfig.json", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.29.3", "lightningcss-linux-x64-gnu": "1.29.3", "lightningcss-win32-x64-msvc": "1.29.3", "lightningcss-win32-arm64-msvc": "1.29.3", "lightningcss-darwin-arm64": "1.29.3", "lightningcss-linux-arm64-gnu": "1.29.3", "lightningcss-linux-arm-gnueabihf": "1.29.3", "lightningcss-linux-arm64-musl": "1.29.3", "lightningcss-linux-x64-musl": "1.29.3", "lightningcss-freebsd-x64": "1.29.3"}, "_id": "lightningcss@1.29.3", "gitHead": "80eb8617c5f8f5519ed85bd3eb6d8953f4d32493", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-GlOJwTIP6TMIlrTFsxTerwC0W6OpQpCGuX1ECRLBUVRh6fpJH3xTqjCjRgQHTb4ZXexH9rtHou1Lf03GKzmhhQ==", "shasum": "ddb8c6367f6d63432a4e81278421f2a9b3ac6efb", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.29.3.tgz", "fileCount": 14, "unpackedSize": 500181, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDC16fbDV7AWQhq3NbDIo7YbZv8ZqeiWulzke9GGgDLbAIgY4NykSvTMshOwXKEWLH2+mYUX2hXHXTeAuEDU7p3py8="}], "size": 85458}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/lightningcss_1.29.3_1741974620949_0.8507545765750344"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-14T17:50:21.141Z", "publish_time": 1741974621141, "_source_registry_name": "default"}, "1.30.0": {"name": "lightningcss", "version": "1.30.0", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"types": "./node/index.d.ts", "import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^2.0.3"}, "devDependencies": {"@babel/parser": "7.21.4", "@babel/traverse": "7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "~6.0.13", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.21", "caniuse-lite": "^1.0.30001717", "codemirror": "^6.0.1", "cssnano": "^7.0.6", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.33.5", "typescript": "^5.7.2", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:."}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && wasm-opt target/wasm32-unknown-unknown/debug/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && wasm-opt target/wasm32-unknown-unknown/release/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "tsc": "tsc -p node/tsconfig.json", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.30.0", "lightningcss-linux-x64-gnu": "1.30.0", "lightningcss-win32-x64-msvc": "1.30.0", "lightningcss-win32-arm64-msvc": "1.30.0", "lightningcss-darwin-arm64": "1.30.0", "lightningcss-linux-arm64-gnu": "1.30.0", "lightningcss-linux-arm-gnueabihf": "1.30.0", "lightningcss-linux-arm64-musl": "1.30.0", "lightningcss-linux-x64-musl": "1.30.0", "lightningcss-freebsd-x64": "1.30.0"}, "_id": "lightningcss@1.30.0", "gitHead": "474c67c046b4a54217166057296fa4ca28764858", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-uuurN2onfoNwQtaWnX9UYLz6DlZHnUd88SceOXDAQzQ5+FJ+ELPgcC/EVtRJoFOveXe44zRE+foh2KMD/vQxqQ==", "shasum": "aa12c41502c3c8c80d1bed6829930e5d7218a812", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.30.0.tgz", "fileCount": 14, "unpackedSize": 501111, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDDzgRTl1sELNndPulK/84O2c9j20lEkNRnc6rvopZuMAIhAKuAjRbWEae24/zBuqNnV4uXJYGyO1IVcTw0JD2qSEBP"}], "size": 85555}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/lightningcss_1.30.0_1746945573834_0.6680077101903901"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-11T06:39:33.998Z", "publish_time": 1746945573998, "_source_registry_name": "default"}, "1.30.1": {"name": "lightningcss", "version": "1.30.1", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "node/index.js", "types": "node/index.d.ts", "exports": {"types": "./node/index.d.ts", "import": "./node/index.mjs", "require": "./node/index.js"}, "browserslist": "last 2 versions, not dead", "targets": {"main": false, "types": false}, "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "napi": {"name": "lightningcss"}, "dependencies": {"detect-libc": "^2.0.3"}, "devDependencies": {"@babel/parser": "7.21.4", "@babel/traverse": "7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "~6.0.13", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.21", "caniuse-lite": "^1.0.30001717", "codemirror": "^6.0.1", "cssnano": "^7.0.6", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.33.5", "typescript": "^5.7.2", "util": "^0.12.4", "uvu": "^0.5.6"}, "resolutions": {"lightningcss": "link:."}, "scripts": {"prepare": "patch-package", "build": "node scripts/build.js && node scripts/build-flow.js", "build-release": "node scripts/build.js --release && node scripts/build-flow.js", "prepublishOnly": "node scripts/build-flow.js", "wasm:build": "cargo build --target wasm32-unknown-unknown -p lightningcss_node && wasm-opt target/wasm32-unknown-unknown/debug/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "wasm:build-release": "cargo build --target wasm32-unknown-unknown -p lightningcss_node --release && wasm-opt target/wasm32-unknown-unknown/release/lightningcss_node.wasm --asyncify --pass-arg=asyncify-imports@env.await_promise_sync -Oz -o wasm/lightningcss_node.wasm && node scripts/build-wasm.js", "website:start": "parcel 'website/*.html' website/playground/index.html", "website:build": "yarn wasm:build-release && parcel build 'website/*.html' website/playground/index.html", "build-ast": "cargo run --example schema --features jsonschema && node scripts/build-ast.js", "tsc": "tsc -p node/tsconfig.json", "test": "uvu node/test"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.30.1", "lightningcss-linux-x64-gnu": "1.30.1", "lightningcss-win32-x64-msvc": "1.30.1", "lightningcss-win32-arm64-msvc": "1.30.1", "lightningcss-darwin-arm64": "1.30.1", "lightningcss-linux-arm64-gnu": "1.30.1", "lightningcss-linux-arm-gnueabihf": "1.30.1", "lightningcss-linux-arm64-musl": "1.30.1", "lightningcss-linux-x64-musl": "1.30.1", "lightningcss-freebsd-x64": "1.30.1"}, "_id": "lightningcss@1.30.1", "gitHead": "f2dc67c4d3fe92f26693c02366db1e60cae0db27", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-xi6IyHML+c9+Q3W0S4fCQJOym42pyurFiJUHEcEyHS0CeKzia4yZDEsLlqOFykxOdHpNy0NmvVO31vcSqAxJCg==", "shasum": "78e979c2d595bfcb90d2a8c0eb632fe6c5bfed5d", "tarball": "https://registry.npmmirror.com/lightningcss/-/lightningcss-1.30.1.tgz", "fileCount": 14, "unpackedSize": 501111, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDiezUFJ0hmsgVUWaxnULkdyh4cY54citJvvbFj0CrE/wIgNCoT8VLgCehN2R28gsQiUaHFwn3u7yHJ6fy4verfw2U="}], "size": 85556}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/lightningcss_1.30.1_1747193948529_0.02523679833792536"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-14T03:39:08.694Z", "publish_time": 1747193948694, "_source_registry_name": "default"}}, "description": "A CSS parser, transformer, and minifier written in Rust", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# ⚡️ Lightning CSS\n\nAn extremely fast CSS parser, transformer, and minifier written in Rust. Use it with [Pa<PERSON><PERSON>](https://parceljs.org), as a standalone library or CLI, or via a plugin with any other tool.\n\n<img width=\"680\" alt=\"performance and build size charts\" src=\"https://user-images.githubusercontent.com/19409/189022599-28246659-f94a-46a4-9de0-b6d17adb0e22.png#gh-light-mode-only\">\n<img width=\"680\" alt=\"performance and build size charts\" src=\"https://user-images.githubusercontent.com/19409/189022693-6956b044-422b-4f56-9628-d59c6f791095.png#gh-dark-mode-only\">\n\n## Features\n\n- **Extremely fast** – Parsing and minifying large files is completed in milliseconds, often with significantly smaller output than other tools. See [benchmarks](#benchmarks) below.\n- **Typed property values** – many other CSS parsers treat property values as an untyped series of tokens. This means that each transformer that wants to do something with these values must interpret them itself, leading to duplicate work and inconsistencies. Lightning CSS parses all values using the grammar from the CSS specification, and exposes a specific value type for each property.\n- **Browser-grade parser** – Lightning CSS is built on the [cssparser](https://github.com/servo/rust-cssparser) and [selectors](https://github.com/servo/stylo/tree/main/selectors) crates created by Mozilla and used by Firefox and Servo. These provide a solid general purpose CSS-parsing foundation on top of which Lightning CSS implements support for all specific CSS rules and properties.\n- **Minification** – One of the main purposes of Lightning CSS is to minify CSS to make it smaller. This includes many optimizations including:\n  - Combining longhand properties into shorthands where possible.\n  - Merging adjacent rules with the same selectors or declarations when it is safe to do so.\n  - Combining CSS transforms into a single matrix or vice versa when smaller.\n  - Removing vendor prefixes that are not needed, based on the provided browser targets.\n  - Reducing `calc()` expressions where possible.\n  - Converting colors to shorter hex notation where possible.\n  - Minifying gradients.\n  - Minifying CSS grid templates.\n  - Normalizing property value order.\n  - Removing default property sub-values which will be inferred by browsers.\n  - Many micro-optimizations, e.g. converting to shorter units, removing unnecessary quotation marks, etc.\n- **Vendor prefixing** – Lightning CSS accepts a list of browser targets, and automatically adds (and removes) vendor prefixes.\n- **Browserslist configuration** – Lightning CSS supports opt-in browserslist configuration discovery to resolve browser targets and integrate with your existing tools and config setup.\n- **Syntax lowering** – Lightning CSS parses modern CSS syntax, and generates more compatible output where needed, based on browser targets.\n  - CSS Nesting\n  - Custom media queries (draft spec)\n  - Logical properties\n  * [Color Level 5](https://drafts.csswg.org/css-color-5/)\n    - `color-mix()` function\n    - Relative color syntax, e.g. `lab(from purple calc(l * .8) a b)`\n  - [Color Level 4](https://drafts.csswg.org/css-color-4/)\n    - `lab()`, `lch()`, `oklab()`, and `oklch()` colors\n    - `color()` function supporting predefined color spaces such as `display-p3` and `xyz`\n    - Space separated components in `rgb` and `hsl` functions\n    - Hex with alpha syntax\n    - `hwb()` color syntax\n    - Percent syntax for opacity\n    - `#rgba` and `#rrggbbaa` hex colors\n  - Selectors\n    - `:not` with multiple arguments\n    - `:lang` with multiple arguments\n    - `:dir`\n    - `:is`\n  - Double position gradient stops (e.g. `red 40% 80%`)\n  - `clamp()`, `round()`, `rem()`, and `mod()` math functions\n  - Alignment shorthands (e.g. `place-items`)\n  - Two-value `overflow` shorthand\n  - Media query range syntax (e.g. `@media (width <= 100px)` or `@media (100px < width < 500px)`)\n  - Multi-value `display` property (e.g. `inline flex`)\n  - `system-ui` font family fallbacks\n- **CSS modules** – Lightning CSS supports compiling a subset of [CSS modules](https://github.com/css-modules/css-modules) features.\n  - Locally scoped class and id selectors\n  - Locally scoped custom identifiers, e.g. `@keyframes` names, grid lines/areas, `@counter-style` names, etc.\n  - Opt-in support for locally scoped CSS variables and other dashed identifiers.\n  - `:local()` and `:global()` selectors\n  - The `composes` property\n- **Custom transforms** – The Lightning CSS visitor API can be used to implement custom transform plugins.\n\n## Documentation\n\nLightning CSS can be used from [Parcel](https://parceljs.org), as a standalone library from JavaScript or Rust, using a standalone CLI, or wrapped as a plugin within any other tool. See the [Lightning CSS website](https://lightningcss.dev/docs.html) for documentation.\n\n## Benchmarks\n\n<img width=\"680\" alt=\"performance and build size charts\" src=\"https://user-images.githubusercontent.com/19409/189022599-28246659-f94a-46a4-9de0-b6d17adb0e22.png#gh-light-mode-only\">\n<img width=\"680\" alt=\"performance and build size charts\" src=\"https://user-images.githubusercontent.com/19409/189022693-6956b044-422b-4f56-9628-d59c6f791095.png#gh-dark-mode-only\">\n\n```\n$ node bench.js bootstrap-4.css\ncssnano: 544.809ms\n159636 bytes\n\nesbuild: 17.199ms\n160332 bytes\n\nlightningcss: 4.16ms\n143091 bytes\n\n\n$ node bench.js animate.css\ncssnano: 283.105ms\n71723 bytes\n\nesbuild: 11.858ms\n72183 bytes\n\nlightningcss: 1.973ms\n23666 bytes\n\n\n$ node bench.js tailwind.css\ncssnano: 2.198s\n1925626 bytes\n\nesbuild: 107.668ms\n1961642 bytes\n\nlightningcss: 43.368ms\n1824130 bytes\n```\n\nFor more benchmarks comparing more tools and input, see [here](http://goalsmashers.github.io/css-minification-benchmark/). Note that some of the tools shown perform unsafe optimizations that may change the behavior of the original CSS in favor of smaller file size. Lightning CSS does not do this – the output CSS should always behave identically to the input. Keep this in mind when comparing file sizes between tools.\n", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "license": "MPL-2.0", "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "_source_registry_name": "default"}