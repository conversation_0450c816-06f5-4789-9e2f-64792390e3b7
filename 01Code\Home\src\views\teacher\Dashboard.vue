//src\views\teacher\Dashboard.vue
<template>
  <div class="teacher-dashboard">
    <h1>教师控制台</h1>
    
    <!-- 教师专属功能 -->
    <section class="teacher-actions">
      <button @click="createCourse">创建新课程</button>
      <button @click="manageStudents">学生管理</button>
      <button @click="gradeAssignments">批改作业</button>
    </section>

    <!-- 数据统计 -->
    <div class="stats-grid">
      <div class="stat-card">
        <h3>在授课程</h3>
        <p>{{ courses.length }}</p>
      </div>
      <div class="stat-card">
        <h3>待批改作业</h3>
        <p>{{ pendingAssignments }}</p>
      </div>
    </div>

    <!-- 最近通知 -->
    <NotificationCenter />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useAuthStore } from '@/stores/auth/auth.js'
import NotificationCenter from '@/components/NotificationCenter.vue'

const authStore = useAuthStore()
const courses = ref([])
const pendingAssignments = ref(0)

// 模拟教师专属方法
const createCourse = () => {
  console.log('创建课程逻辑...')
}

const manageStudents = () => {
  console.log('学生管理逻辑...')
}

const gradeAssignments = () => {
  console.log('批改作业逻辑...')
}

// 模拟获取教师数据
const fetchTeacherData = () => {
  // 实际项目中这里应该是API请求
  setTimeout(() => {
    courses.value = ['数学', '物理', '化学']
    pendingAssignments.value = 12
  }, 500)
}

fetchTeacherData()
</script>

<style scoped lang="scss">
@use '@/styles/teacher/dashboard.scss';
</style>