<template>
  <div class="dialog-mask" v-if="visible">
    <div class="dialog-box" :class="{ shake: shakeDialog }">
      <h3>新建子节点</h3>

      <div class="form">
        <label>节点名称</label>
        <input v-model="name" type="text" placeholder="请输入节点名称" />
        <div class="error" v-if="showEmptyError">节点名称不能为空</div>
        <div class="error" v-if="showDuplicateError">节点名称已存在</div>

        <label>节点描述</label>
        <textarea v-model="description" placeholder="请输入节点描述" />
      </div>

      <div class="actions">
        <button @click="onCancel">取消</button>
        <button @click="onConfirm">确定</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, inject } from 'vue'

const props = defineProps(['visible'])
const emit = defineEmits(['confirm', 'cancel'])

const name = ref('')
const description = ref('')
const showEmptyError = ref(false)
const showDuplicateError = ref(false)
const shakeDialog = ref(false)

// 获取图表数据引用
const graphData = inject('graphData')

// 检查节点名称是否已存在
const isNodeNameExist = (nodeName) => {
    if (!graphData.value) return false
    
    const checkNode = (node) => {
        if (node.label === nodeName) return true
        if (node.children) {
            return node.children.some(child => checkNode(child))
        }
        return false
    }
    
    return checkNode(graphData.value)
}

const onConfirm = () => {
    // 重置错误状态
    showEmptyError.value = false
    showDuplicateError.value = false
    
    if (!name.value.trim()) {
        showEmptyError.value = true
        triggerShake()
        return
    }
    
    // 检查节点名称是否已存在
    if (isNodeNameExist(name.value.trim())) {
        showDuplicateError.value = true
        triggerShake()
        return
    }
    
    emit('confirm', {
        name: name.value.trim(),
        description: description.value.trim(),
    })
    name.value = ''
    description.value = ''
}

const triggerShake = () => {
    shakeDialog.value = true
    setTimeout(() => (shakeDialog.value = false), 500)
}

const onCancel = () => {
    emit('cancel')
    name.value = ''
    description.value = ''
    showEmptyError.value = false
    showDuplicateError.value = false
}
</script>

<style lang="scss" scoped>
.dialog-mask {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;

  .dialog-box {
    background: #fff;
    padding: 24px;
    width: 360px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
    transition: transform 0.2s ease;

    &.shake {
      animation: shake 0.3s ease;
    }

    @keyframes shake {
      0% {
        transform: translateX(0);
      }

      25% {
        transform: translateX(-8px);
      }

      50% {
        transform: translateX(8px);
      }

      75% {
        transform: translateX(-6px);
      }

      100% {
        transform: translateX(0);
      }
    }

    .form {
      label {
        display: block;
        margin-top: 16px;
        font-weight: bold;
        font-size: 14px;
      }

      input[type="text"],
      textarea {
        width: 100%;
        margin-top: 6px;
        padding: 8px;
        border: 1px solid #ccc;
        border-radius: 6px;
        font-size: 14px;
      }

      textarea {
        resize: vertical;
        min-height: 80px;
      }

      .error {
        color: red;
        font-size: 12px;
        margin-top: 4px;
      }
    }

    .actions {
      display: flex;
      justify-content: flex-end;
      margin-top: 24px;
      gap: 12px;

      button {
        padding: 6px 14px;
        font-size: 14px;
        border-radius: 6px;
        cursor: pointer;
      }
    }
  }
}
</style>