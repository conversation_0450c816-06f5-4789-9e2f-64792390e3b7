<template>
  <div class="rename-input-container" v-if="visible">
    <input
      type="text"
      v-model="newName"
      @blur="handleBlur"
      @keyup.enter="handleConfirm"
      placeholder="输入新名称..."
      class="rename-input"
      ref="renameInput"
    >
    <div class="rename-actions">
      <button class="confirm-rename" @click="handleConfirm">✓</button>
      <button class="cancel-rename" @click="handleCancel">×</button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RenameInput',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    originalName: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      newName: this.originalName
    };
  },
  watch: {
    originalName(newValue) {
      this.newName = newValue;
    }
  },
  methods: {
    handleConfirm() {
      if (this.newName.trim()) {
        this.$emit('confirm', this.newName);
      }
    },
    handleCancel() {
      this.$emit('cancel');
    },
    handleBlur() {
      if (this.newName !== this.originalName) {
        this.$emit('confirm', this.newName);
      }
    }
  },
  mounted() {
    this.$refs.renameInput.focus();
  }
}
</script>

<style scoped>
.rename-input-container {
  position: relative;
  display: inline-block;
}
.rename-input {
  padding: 6px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  width: 200px;
}
.rename-actions {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
}
.confirm-rename, .cancel-rename {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  margin: 0 5px;
}
.confirm-rename {
  color: #67c23a;
}
.cancel-rename {
  color: #f56c6c;
}
</style>