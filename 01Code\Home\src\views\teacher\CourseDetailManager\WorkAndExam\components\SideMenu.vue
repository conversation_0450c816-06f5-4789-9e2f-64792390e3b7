<template>
    <el-menu class="side-menu" :default-active="route.path">
        <el-menu-item index="/public-database" @click="goToPublic">
            <el-icon>
                <FolderOpened />
            </el-icon>
            公共题库
        </el-menu-item>
        <el-menu-item index="/course-database" @click="goToCourse">
            <el-icon>
                <Folder />
            </el-icon>
            课程题库
        </el-menu-item>
    </el-menu>
</template>

  
<script setup>
import { useRoute, useRouter } from 'vue-router'
import { Folder, FolderOpened } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

const sharedParams = {
    courseId: route.query.courseId || '',
    courseName: route.query.courseName || '',
    mode: route.query.mode || 'assignment'
}

const goToPublic = () => {
    router.push({ path: '/public-database', query: sharedParams })
}

const goToCourse = () => {
    router.push({ path: '/course-database', query: sharedParams })
}
</script>
  
<style scoped>
.side-menu {
    width: 200px;
    min-height: calc(100vh - 78px);
    border-right: 1px solid #ebeef5;
}

.el-menu-item.is-active {
    background-color: #409eff !important;
    color: white !important;
}</style>
