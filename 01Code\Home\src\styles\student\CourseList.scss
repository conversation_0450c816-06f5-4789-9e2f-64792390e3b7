// src/styles/student/CourseList.scss (教师端也使用此样式)
@use "@/styles/variables.scss" as *;

.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 20px;
}

.course-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(270px, 1fr));
  gap: 20px;
  padding: 20px;
  width: 100%;
  box-sizing: border-box;

  .course-item {
    width: 100%;
    min-width: 270px;
    min-height: 320px; /* 设置最小高度保证内容显示 */
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
    transition: box-shadow 0.3s;
    cursor: pointer;
    position: relative;
    display: flex; /* 新增 */
    flex-direction: column; /* 新增 */

    &:hover {
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .course-cover-container {
      width: 100%;
      height: 210px; /* 固定封面高度 */
      min-height: 210px; /* 确保封面高度不变 */
      position: relative;
      overflow: hidden;
      background-color: #b5c0d9;
      flex-shrink: 0; /* 防止封面被压缩 */
    }

    .course-cover {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: center;
    }

    .course-content { /* 新增内容容器 */
      padding: 12px;
      flex: 1; /* 填充剩余空间 */
      display: flex;
      flex-direction: column;
      justify-content: space-between; /* 内容均匀分布 */
    }
  
    h3 {
      margin: 12px;
      font-size: 16px;
      color: #333;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    p {
      margin: 0 12px 12px;
      font-size: 14px;
      color: #666;
    }

    .course-status {
      display: flex;
      gap: 8px;
      margin: 0 12px 12px;

      span {
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
      }

      .status-approved {
        background-color: #e6f7ff;
        color: #1890ff;
        border: 1px solid #1890ff;
      }

      .status-rejected {
        background-color: #fff1f0;
        color: #f5222d;
        border: 1px solid #f5222d;
      }

      .status-pending {
        background-color: #fffbe6;
        color: #faad14;
        border: 1px solid #faad14;
      }

      .state-active {
        background-color: #f6ffed;
        color: #52c41a;
        border: 1px solid #52c41a;
      }

      .state-ended {
        background-color: #fafafa;
        color: #8c8c8c;
        border: 1px solid #d9d9d9;
      }
    }
  }
}

// 分页控制器样式（教师端如需分页可添加此代码）
.pagination-container {
  display: flex;
  justify-content: center;
  margin: 30px 0;
  padding: 0 20px;
  
  .pagination {
    display: flex;
    align-items: center;
    gap: 8px;
    list-style: none;
    padding: 0;
    
    li {
      a, button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        border: 1px solid #e8e8e8;
        border-radius: 4px;
        color: #666;
        text-decoration: none;
        cursor: pointer;
        transition: all 0.2s;
        
        &:hover, &.active {
          background-color: $primary-color;
          color: white;
          border-color: $primary-color;
        }
        
        &.disabled {
          color: #ccc;
          cursor: not-allowed;
          &:hover {
            background-color: transparent;
          }
        }
      }
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  text-align: center;
  .empty-icon {
    margin-top: 30px;
    margin-bottom: 20px;
    opacity: 0.6;
  }
  .empty-text {
    color: #999;
    font-size: 16px;
    margin-top: 10px;
  }
}