<template>
  <el-dialog
    v-model="visible"
    title="编辑路径节点（拖动可调整顺序）"
    width="600px"
    :before-close="handleClose"
  >
    <div class="edit-nodes-form">
      <el-tree
        :data="treeData"
        :props="treeProps"
        node-key="id"
        :default-expand-all="true"
        :expand-on-click-node="false"
        draggable
        :allow-drop="allowDrop"
        :allow-drag="allowDrag"
        @node-drop="handleNodeDrop"
      >
        <template #default="{ node, data }">
          <div class="tree-node">
            <el-checkbox 
              v-model="data.enabled" 
              @change="handleNodeToggle(data)"
              class="node-checkbox"
            />
            <span class="node-label" :class="data.type">{{ data.label }}</span>
          </div>
        </template>
      </el-tree>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="saveNodes">完成编辑</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  unitIndex: {
    type: Number,
    default: -1
  }
})

const emit = defineEmits(['update:modelValue', 'save-nodes'])

const visible = ref(false)
const treeData = ref([])

const treeProps = {
  children: 'children',
  label: 'label'
}

// 监听显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    // 构建树形数据结构
    treeData.value = [
      {
        id: 1,
        label: '章节 1',
        type: 'chapter',
        enabled: true,
        children: [
          {
            id: 11,
            label: '知识点 1.1',
            type: 'knowledge',
            enabled: true,
            children: [
              { id: 111, label: '实训 1.1.1', type: 'practice', enabled: true },
              { id: 112, label: '视频 1.1.1', type: 'video', enabled: true },
              { id: 113, label: '题库 1.1.2', type: 'question', enabled: false },
              { id: 114, label: '知识点 1.2', type: 'knowledge', enabled: true }
            ]
          }
        ]
      },
      {
        id: 2,
        label: '章节 2',
        type: 'chapter',
        enabled: false,
        children: []
      },
      {
        id: 3,
        label: '章节 3',
        type: 'chapter',
        enabled: true,
        children: []
      }
    ]
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 保存节点配置
const saveNodes = () => {
  // 将树形结构转换为节点数组，只保存enabled为true的节点
  const nodes = []
  const extractNodes = (treeNodes) => {
    treeNodes.forEach(node => {
      // 只添加enabled为true的节点
      if (node.enabled) {
        nodes.push({
          name: node.label,
          type: node.type,
          active: false,
          enabled: true
        })
      }
      if (node.children && node.children.length > 0) {
        extractNodes(node.children)
      }
    })
  }

  extractNodes(treeData.value)
  
  emit('save-nodes', {
    unitIndex: props.unitIndex,
    nodes: nodes
  })
  
  ElMessage.success('节点保存成功！')
  visible.value = false
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}

// 树形组件相关方法
const allowDrop = (draggingNode, dropNode, type) => {
  return type !== 'inner'
}

const allowDrag = (draggingNode) => {
  return true
}

const handleNodeDrop = (draggingNode, dropNode, dropType, ev) => {
  console.log('节点拖拽:', draggingNode, dropNode, dropType)
}

// 处理节点复选框切换
const handleNodeToggle = (data) => {
  console.log('节点状态切换:', data.label, data.enabled)
}
</script>

<style lang="scss" scoped>
.edit-nodes-form {
  max-height: 500px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;

  .tree-node {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 5px 0;

    .node-checkbox {
      margin-right: 8px;
    }

    .node-label {
      flex: 1;
      font-size: 14px;

      &.chapter {
        color: #e6a23c;
        font-weight: 500;
      }

      &.knowledge {
        color: #409eff;
      }

      &.practice {
        color: #67c23a;
      }

      &.video {
        color: #909399;
      }

      &.question {
        color: #f56c6c;
      }
    }
  }
}
</style>
