<template>
  <div class="resource-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>数媒资源</h1>
    </div>
    <!-- 主内容区 -->
    <div class="main-content">
      <div class="navigation-sidebar">
        <LeftNav @nav-click="handleNavigationClick" />
      </div>
      <div class="content-area">
        <ResourceContent :active-type="currentTab" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import LeftNav from './components/LeftNav.vue'
import ResourceContent from './components/ResourceContent.vue'

// 当前激活的标签页
const currentTab = ref('digital-media')

// 处理导航点击
const handleNavigationClick = (tabKey) => {
  currentTab.value = tabKey
}
</script>

<style lang="scss" scoped>
$border-radius: 2vw;
$primary-color: #8a6de3;
$course-tabs: #f3ecff;
$course-tabs-solid: #d6beff;

.resource-container {
  position: relative;
  padding-top: 10vw;
  padding-left: 3vw;
  padding-right: 3vw;
  min-height: 100vh;

  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('@/assets/img/Home/ic_home_bg.png') top/cover no-repeat;
    z-index: -1;
  }

  .page-header {
    color: white;
    margin-bottom: 2vw;
    display: flex;
    justify-content: space-between;

    h1 {
      font-size: 3rem;
      font-weight: 400;
      margin: 0;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }
  }

  .main-content {
    display: flex;
    gap: 1.5vw;
    min-height: 70vh;
    background-color: white;
    border-radius: 1vw 1vw 0 0;
    overflow: hidden;
    padding: 2vw;
    margin-top: 10vw;

    .navigation-sidebar {
      flex: 0 0 15%;
      display: flex;
      flex-direction: column;
      gap: 1vw;
      padding-right: 1vw;
    }

    .content-area {
      flex: 1;
      padding: 0;
      border-radius: 0.3vw;
      overflow: hidden;
    }
  }
}
</style>
