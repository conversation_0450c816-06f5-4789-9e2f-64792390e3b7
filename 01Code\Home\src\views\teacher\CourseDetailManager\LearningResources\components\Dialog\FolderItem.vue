<template>
  <li>
    <div 
      class="folder-item" 
      :class="{ 'selected': isSelected }"
      @click="toggleFolder(folder)"
    >
      <span class="toggle-icon" v-if="hasChildren">
        {{ expandedFolders.has(folder.id) ? '▼' : '▶' }}
      </span>
      <span 
        class="folder-name"
        @click.stop="selectFolder(folder)"
      >
        {{ folder.name }}
      </span>
    </div>

    <ul v-if="expandedFolders.has(folder.id) && hasChildren" style="margin-left: 20px">
      <FolderItem 
        v-for="child in folder.children" 
        :key="child.id" 
        :folder="child" 
        :expanded-folders="expandedFolders"
        :selected-item="selectedItem"
        @select-folder="$emit('select-folder', $event)"
        @select-file="$emit('select-file', $event)"
      />

      <li v-if="folder.resources && folder.resources.length > 0" class="resource-list">
        <ul>
          <li v-for="file in folder.resources" :key="file.id">
            <div 
              class="file-item" 
              :class="{ 'selected': selectedItem?.id === file.id }"
              @click.stop="$emit('select-file', file)"
            >
              <span>{{ file.name }}</span>
            </div>
          </li>
        </ul>
      </li>
    </ul>
  </li>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  folder: {
    type: Object,
    required: true
  },
  expandedFolders: {
    type: Set,
    required: true
  },
  selectedItem: Object
})

const emit = defineEmits(['select-folder', 'select-file'])

const hasChildren = computed(() => {
  return props.folder.children?.length > 0
})

const isSelected = computed(() => {
  return props.selectedItem?.id === props.folder.id
})

const toggleFolder = (folder) => {
  if (!folder.children || folder.children.length === 0) return
  
  if (props.expandedFolders.has(folder.id)) {
    props.expandedFolders.delete(folder.id)
  } else {
    props.expandedFolders.add(folder.id)
  }
}

const selectFolder = (folder) => {
  emit('select-folder', folder)
}
</script>



<style scoped lang="scss">
.folder-item {
  display: flex;
  align-items: center;
  padding: 6px 0;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f5f7fa;
  }

  &.selected {
    background-color: #e6f7ff;
  }

  .toggle-icon {
    font-size: 12px;
    color: #666;
    width: 20px;
    text-align: center;
    margin-right: 4px;
  }

  .folder-name {
    flex: 1;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f0f2f5;
    }

    &.selected {
      background-color: #e6f7ff;
      font-weight: 500;
    }
  }
}

.sub-folder {
  margin-left: 24px;
}

.resource-list {
  margin-top: 4px;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;

  &:hover {
    background-color: #f5f7fa;
  }

  &.selected {
    background-color: #e6f7ff;
    font-weight: 500;
  }

  .file-extension {
    margin-left: 8px;
    color: #666;
    font-size: 0.8em;
  }
}

li {
  list-style-type: none;
}
</style>