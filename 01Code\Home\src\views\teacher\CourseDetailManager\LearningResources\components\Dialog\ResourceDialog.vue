<template>
  <div class="dialog-mask" @click.self="close">
    <div class="dialog">
      <div class="dialog-header">
<!--         <span class="title">上传{{ title }}至</span> -->
        <span class="title">{{ title }}</span>
        <span class="close" @click="close">✕</span>
      </div>

      <div class="dialog-body">
        <div class="tree-container">
          <ul v-if="folderTree.length > 0" class="folder-tree">
            <li class="folder-item all-folder" :class="{ 'selected': selectedFolderId === '' }"
              @click="selectedFolderId = 'null'">
              <div class="folder-header">
                <span class="folder-name">全部</span>
              </div>
            </li>
            <TreeNode v-for="folder in folderTree" v-show="folder.id !== 'root'" :key="folder.id" :folder="folder"
              :selectedFolderId="selectedFolderId" @select="selectedFolderId = $event" @toggle="handleFolderToggle" />
          </ul>
          <div v-else class="no-folders">
            <div class="empty-message">暂无文件夹</div>
          </div>
        </div>
      </div>

      <div class="dialog-footer">
        <button class="cancel" @click="close">取消</button>
        <button class="confirm" @click="confirm" >
          确定
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useResourceStore } from '@/stores/public/teacher/resource'
import { storeToRefs } from 'pinia'
import TreeNode from '../TreeNode.vue'

const props = defineProps({
  title: String,
  type: String
})

const handleFolderToggle = (folderId) => {
  const folder = resourceStore.findFolder(folderId)
  if (folder) {
    folder.expanded = !folder.expanded
  }
  console.log(folderId);

}

const emit = defineEmits(['close', 'confirm'])

const resourceStore = useResourceStore()
const { folderTree } = storeToRefs(resourceStore)
const selectedFolderId = ref('')

const close = () => {
  emit('close')
}


const confirm = () => {
  emit('confirm', {
    folderId: selectedFolderId.value,
    type: props.type
    
  })
  close()
}
</script>

<style scoped lang="scss">
.dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;

  .dialog {
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 10px;
    width: 500px;
    height: 500px;
    padding: 0;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);

    .dialog-header {
      padding: 16px;
      font-weight: bold;
      font-size: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #eee;

      .close {
        cursor: pointer;
        font-size: 18px;
        color: #999;
      }
    }

    .dialog-body {
      flex: 1;
      overflow-y: auto;
      padding: 20px;
      max-height: calc(500px - 100px);
      overflow-y: auto;

      .tree-container {
        .folder-tree {
          list-style: none;
          padding: 0;

          .folder-item.all-folder {
            margin-left: 0;
            padding-left: 16px;
            border-bottom: 1px solid #eee;
            padding-bottom: 8px;
            margin-bottom: 8px;

            &:hover {
              background-color: #f5f7fa;
            }

            &.selected {
              background-color: #e6f7ff;
            }
          }
        }

        .no-folders {
          .empty-message {
            text-align: center;
            padding: 20px;
            color: #999;
          }
        }
      }
    }

    .dialog-footer {
      display: flex;
      flex-shrink: 0;
      justify-content: flex-end;
      gap: 10px;
      padding: 12px 16px;
      border-top: 1px solid #eee;

      button {
        padding: 6px 14px;
        border: none;
        border-radius: 4px;
        font-size: 14px;
        cursor: pointer;

        &.cancel {
          background: #f1f1f1;
        }

        &.confirm {
          background: #4c7bff;
          color: white;

          &:disabled {
            background: #cccccc;
            cursor: not-allowed;
          }
        }
      }
    }
  }
}
</style>