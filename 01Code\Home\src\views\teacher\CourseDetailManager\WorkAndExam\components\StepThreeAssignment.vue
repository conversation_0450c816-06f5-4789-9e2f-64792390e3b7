<template>
    <div>
        <h4>基础设置</h4>
        <el-form label-position="top" label-width="120px">
            <el-form-item label="发布时间">
                <el-date-picker v-model="form.publishTime" type="datetime" placeholder="请选择发布时间" value-format="x"
                    style="width: 300px;" />
            </el-form-item>
            <el-form-item label="截止时间">
                <el-date-picker v-model="form.deadline" type="datetime" placeholder="请选择截止时间" value-format="x"
                    style="width: 300px;" />
            </el-form-item>
        </el-form>

        <h4 style="margin-top: 24px;">答题设置</h4>
        <el-checkbox-group v-model="form.answerSettings">
            <el-checkbox label="允许迟交" />
            <el-checkbox label="允许学生截止前修改" />
            <el-checkbox label="允许申请重做" />
        </el-checkbox-group>
    </div>
</template>
  
<script setup>
const props = defineProps({
    form: Object,
})
</script>
  