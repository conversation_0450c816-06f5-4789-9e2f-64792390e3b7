<template>
  <div class="project-introduction">
    <div class="content-section">
      <div class="intro-text">
        <p class="chinese-intro">
          本虚拟仿真实验基于真实的环境色彩设计项目真实设计流程，实验包括理论学习（掌握色彩理论基础）、单人模式（离线色彩设计仿真）、双人模式（客户色彩设计实践）。通过虚拟仿真技术，借助元宇宙会议模式，实验设置"设计师"、"客户"双重身份3D虚拟数字人，它"沉浸式"模拟真实工作流程，包括客户沟通、色彩数据采集、项目色彩数据整合、项目色彩方案设计及优化。通过本虚拟仿真实验，学生实验过程中，从色彩理论学习到项目讨论开展，客户偏好调研、项目色彩数据报告整合，初步和最终色彩设计优化。这本虚拟仿真实验让学生掌握环境色彩设计的理论知识，模拟真实设计项目，提升其实践色彩设计能力。
        </p>
        
        <div class="divider"></div>
        
        <p class="english-intro">
          This virtual simulation experiment is based on the real design process of environmental color design projects. The experiment includes theoretical learning (mastering basic color theory), single-player mode (offline color design simulation), and dual-player mode (client color design practice). Utilizing virtual simulation technology and the metaverse meeting mode, the experiment sets up dual roles of "designer" and "client" through 3D virtual digital avatars. It "immersively" simulates real work processes, including client communication, color data collection, and project establishment. During the experiment, students progress from learning color theory to engaging in project discussions, conducting client preference research, integrating project color data reports, and refining initial and final color designs. This virtual simulation experiment allows students to master theoretical knowledge of environmental color design, simulate real design projects, and enhance their practical color design skills.
        </p>
      </div>
    </div>
    
    <div class="project-features">
      <h3>项目特色</h3>
      <div class="features-grid">
        <div class="feature-item">
          <div class="feature-icon">
            <i class="fas fa-palette"></i>
          </div>
          <div class="feature-content">
            <h4>色彩理论基础</h4>
            <p>系统学习色彩搭配原理和设计规范</p>
          </div>
        </div>
        
        <div class="feature-item">
          <div class="feature-icon">
            <i class="fas fa-users"></i>
          </div>
          <div class="feature-content">
            <h4>双人协作模式</h4>
            <p>模拟真实的设计师与客户交互场景</p>
          </div>
        </div>
        
        <div class="feature-item">
          <div class="feature-icon">
            <i class="fas fa-vr-cardboard"></i>
          </div>
          <div class="feature-content">
            <h4>沉浸式体验</h4>
            <p>3D虚拟环境提供真实的设计体验</p>
          </div>
        </div>
        
        <div class="feature-item">
          <div class="feature-icon">
            <i class="fas fa-chart-line"></i>
          </div>
          <div class="feature-content">
            <h4>数据驱动设计</h4>
            <p>基于数据分析优化色彩设计方案</p>
          </div>
        </div>
      </div>
    </div>
    
    <div class="learning-objectives">
      <h3>学习目标</h3>
      <ul class="objectives-list">
        <li>掌握环境色彩设计的基本理论和方法</li>
        <li>熟悉色彩搭配原则和设计流程</li>
        <li>提升与客户沟通和需求分析能力</li>
        <li>培养数据收集和分析的专业技能</li>
        <li>增强色彩设计的实践应用能力</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
// 这个组件主要用于展示静态内容，不需要复杂的逻辑
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.project-introduction {
  padding: 32px;
  max-width: 800px;
  
  .content-section {
    margin-bottom: 40px;
    
    .intro-text {
      .chinese-intro,
      .english-intro {
        font-size: 16px;
        line-height: 1.8;
        color: $text-color;
        margin: 0 0 24px 0;
        text-align: justify;
      }
      
      .chinese-intro {
        font-weight: 500;
      }
      
      .english-intro {
        color: rgba($text-color, 0.8);
        font-style: italic;
      }
      
      .divider {
        width: 60px;
        height: 3px;
        background: linear-gradient(90deg, $primary-color, lighten($primary-color, 20%));
        border-radius: 2px;
        margin: 32px 0;
      }
    }
  }
  
  .project-features {
    margin-bottom: 40px;
    
    h3 {
      font-size: 22px;
      font-weight: 600;
      color: $text-color;
      margin: 0 0 24px 0;
      position: relative;
      padding-left: 16px;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 24px;
        background: $primary-color;
        border-radius: 2px;
      }
    }
    
    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
      
      .feature-item {
        display: flex;
        align-items: flex-start;
        gap: 16px;
        padding: 20px;
        background: rgba($primary-color, 0.03);
        border: 1px solid rgba($primary-color, 0.1);
        border-radius: 12px;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba($primary-color, 0.1);
        }
        
        .feature-icon {
          width: 48px;
          height: 48px;
          background: rgba($primary-color, 0.1);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          
          i {
            font-size: 20px;
            color: $primary-color;
          }
        }
        
        .feature-content {
          flex: 1;
          
          h4 {
            font-size: 16px;
            font-weight: 600;
            color: $text-color;
            margin: 0 0 8px 0;
          }
          
          p {
            font-size: 14px;
            color: rgba($text-color, 0.7);
            margin: 0;
            line-height: 1.5;
          }
        }
      }
    }
  }
  
  .learning-objectives {
    h3 {
      font-size: 22px;
      font-weight: 600;
      color: $text-color;
      margin: 0 0 24px 0;
      position: relative;
      padding-left: 16px;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 24px;
        background: $success-color;
        border-radius: 2px;
      }
    }
    
    .objectives-list {
      list-style: none;
      padding: 0;
      margin: 0;
      
      li {
        font-size: 16px;
        line-height: 1.6;
        color: $text-color;
        margin-bottom: 12px;
        padding-left: 32px;
        position: relative;
        
        &::before {
          content: '✓';
          position: absolute;
          left: 0;
          top: 0;
          width: 24px;
          height: 24px;
          background: rgba($success-color, 0.1);
          color: $success-color;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          font-size: 14px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .project-introduction {
    padding: 20px;
    
    .content-section .intro-text {
      .chinese-intro,
      .english-intro {
        font-size: 14px;
      }
    }
    
    .features-grid {
      grid-template-columns: 1fr;
    }
    
    .feature-item {
      padding: 16px;
    }
  }
}
</style>
