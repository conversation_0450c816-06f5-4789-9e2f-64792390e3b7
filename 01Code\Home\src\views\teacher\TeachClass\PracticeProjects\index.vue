<template>
    <div class="practice-project-page">
        <!-- 顶部栏 -->
        <div class="top-bar">
            <el-input v-model="search" placeholder="搜索实践项目" style="width: 220px; margin-right: 12px;" clearable />
            <el-button type="primary" @click="handleNewProject">
                <el-icon>
                    <Plus />
                </el-icon> 新增项目
            </el-button>
        </div>

        <!-- 项目列表 -->
        <div class="project-list">
            <div v-for="item in filteredProjects" :key="item.title" class="project-card" @click="handlePreview(item)">
                <div class="card-left">
                    <el-icon class="icon">
                        <Folder />
                    </el-icon>
                    <div class="project-info">
                        <div class="project-title">{{ item.title }}</div>
                        <div class="project-meta">
                            <!-- 预留创新时间，可能之后添加修改试题的功能 -->
                            创建时间: {{ item.createTime }} 更新时间: {{ item.updateTime }}
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <!-- 预览弹窗 -->
        <el-dialog v-model="previewVisible" title="预览实践项目" width="60%" :before-close="closePreview">
            <iframe v-if="currentPreview" :src="currentPreview.content" frameborder="0"
                style="width: 100%; height: 400px;" />
        </el-dialog>
    </div>
</template>
  
<script setup>
import { ref, computed } from 'vue'
import { Plus, Folder } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const search = ref('')
const previewVisible = ref(false)
const currentPreview = ref(null)

const projects = ref([
    { title: '实践项目一', content: 'http://example.com/project1', createTime: '2025-06-01', updateTime: '2025-06-10', owner: '张三' },
    { title: '实践项目二', content: 'http://example.com/project2', createTime: '2025-06-02', updateTime: '2025-06-11', owner: '李四' }
])

const filteredProjects = computed(() => {
    const keyword = search.value.toLowerCase()
    return projects.value.filter(p => p.title.toLowerCase().includes(keyword))
})

const handleNewProject = () => {
    router.push('/new-practice-project') // 新增项目路由
}

const handlePreview = (item) => {
    currentPreview.value = item
    previewVisible.value = true
}

const closePreview = () => {
    previewVisible.value = false
    currentPreview.value = null
}
</script>
  
<style scoped>
.practice-project-page {
    padding: 20px;
}

.top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.project-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.project-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    transition: all 0.2s ease;
}

.project-card:hover {
    background-color: #f8f8f8;
}

.card-left {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.project-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.project-title {
    font-weight: 600;
    font-size: 16px;
    color: #333;
}

.project-meta {
    font-size: 12px;
    color: #888;
}

.card-right {
    font-size: 13px;
    color: #666;
    white-space: nowrap;
}
</style>
  