import request from '@/api/service'

export default {
  /**
   * 按学期查询成绩
   * @param {string} semester - 学期（如："2024-1"），为空则查询所有学期
   * @returns {Promise} 包含成绩数据的Promise
   */
  getGradesBySemester(semester = '') {
    return request({
      url: '/student/grade/semester',
      method: 'get',
      params: { semester }
    })
  },

  /**
   * 查询课程成绩组成明细
   * @param {string} courseId - 课程ID
   * @returns {Promise} 包含分数明细数据的Promise
   */
  getScoreDetails(courseId) {
    return request({
      url: '/student/grade/score-details/${courseId}',
      method: 'get'
    })
  },

  /**
   * 查询课程学习进度
   * @param {string} courseId - 课程ID
   * @returns {Promise} 包含学习进度数据的Promise
   */
  getCourseProgress(courseId) {
    return request({
      url: `/student/grade/progress/${courseId}`,
      method: 'get'
    })
  },


    /**
   * 查询课程章节及进度
   * @param {string} courseId - 课程ID
   * @returns {Promise} 包含章节进度数据的Promise
   */
  getChapterProgress(courseId) {
    return request({
      url: `/student/grade/chapters/${courseId}`,
      method: 'get'
    })
  },
  
  /**
   * 查询课程章节平时测试详情
   * @param {string} courseId - 课程ID
   * @returns {Promise} 包含章节测试数据的Promise
   */
  getChapterTests(courseId) {
    return request({
      url: `/student/grade/chapter-tests/${courseId}`,
      method: 'get'
    })
  },
  
  /**
   * 获取课程完整详情（聚合多个接口）
   * @param {string} courseId - 课程ID
   * @returns {Promise} 包含所有课程详情的Promise
   */
  async getCourseFullDetail(courseId) {
    try {
      const [scoreDetails, chapterProgress, chapterTests, courseProgress] = await Promise.all([
        this.getScoreDetails(courseId),
        this.getChapterProgress(courseId),
        this.getChapterTests(courseId),
        this.getCourseProgress(courseId)
      ])

      return {
        scoreDetails: scoreDetails.data,
        chapterProgress: chapterProgress.data,
        chapterTests: chapterTests.data,
        courseProgress: courseProgress.data
      }
    } catch (error) {
      console.error('获取课程详情失败:', error)
      throw error
    }
  }
  
  
}