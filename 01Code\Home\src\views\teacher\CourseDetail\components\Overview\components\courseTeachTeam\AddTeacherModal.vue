<template>
  <div class="modal-overlay" @click.self="$emit('close')">
    <div class="modal-content">
      <div class="modal-header">
        <h3>{{ isEditMode ? '编辑老师信息' : '添加老师' }}</h3>
        <button class="close-btn" @click="$emit('close')">×</button>
      </div>
      
      <div class="modal-body">
        <div class="teacher-form">
          <div class="form-row">
            <div class="avatar-section">
              <div class="avatar-upload">
                <label>上传头像</label>
                <div class="upload-container">
                  <div class="avatar-box">
                    <div class="avatar-preview">
                      <img :src="teacher.avatar || '/images/default-avatar.png'">
                    </div>
                    <div class="upload-info">
                      <p>仅支持jpg、gif、png图片文件且文件小于3M，建议尺寸170*170像素</p>
                      <input 
                        type="file" 
                        id="avatarInput" 
                        @change="handleAvatarUpload" 
                        accept="image/jpeg,image/png,image/gif" 
                        hidden
                      >
                      <button class="upload-btn" @click="triggerFileInput">选择图片</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="info-section">
              <div class="form-grid">
                <div class="form-group">
                  <label>老师姓名<span class="required">*</span></label>
                  <input v-model="teacher.name" placeholder="请输入老师姓名">
                </div>
                
                <div class="form-group">
                  <label>所属学校<span class="required">*</span></label>
                  <input v-model="teacher.school" placeholder="请输入老师所属学校">
                </div>
                
                <div class="form-group">
                  <label>出生年月</label>
                  <input type="date" v-model="teacher.birthDate">
                </div>
                
                <div class="form-group">
                  <label>团队职责<span class="required">*</span></label>
                  <select v-model="teacher.role">
                    <option value="">请选择</option>
                    <option value="leader">团队负责人</option>
                    <option value="member">团队成员</option>
                    <option value="advisor">指导老师</option>
                  </select>
                </div>
                
                <div class="form-group">
                  <label>电子邮箱</label>
                  <input v-model="teacher.email" type="email" placeholder="请输入老师电子邮箱">
                </div>
                
                <div class="form-group">
                  <label>校内职称<span class="required">*</span></label>
                  <input v-model="teacher.title" placeholder="请输入老师校内职称">
                </div>
              </div>
            </div>
          </div>
          
          <div class="form-row2">
            <div class="form-group full-width">
              <label>教学经历</label>
              <textarea v-model="teacher.experience" placeholder="请输入教学经历..."></textarea>
            </div>
          </div>
          
          <div class="form-row2">
            <div class="form-group full-width">
              <label>教学任务</label>
              <textarea v-model="teacher.tasks" placeholder="请输入教学任务..."></textarea>
            </div>
          </div>
        </div>
      </div>
      
      <div class="modal-footer">
        <button class="cancel-btn" @click="$emit('close')">取消</button>
        <button class="save-btn" @click="handleSubmit">保存</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    teacherData: {
      type: Object,
      default: null
    },
    isEditMode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      teacher: {
        avatar: '',
        name: '',
        school: '',
        birthDate: '',
        role: '',
        email: '',
        title: '',
        experience: '',
        tasks: ''
      }
    }
  },
  created() {
    if (this.isEditMode && this.teacherData) {
      this.teacher = {...this.teacherData}
    }
  },
  methods: {
    handleSubmit() {
      if (!this.teacher.name || !this.teacher.school || !this.teacher.role || !this.teacher.title) {
        alert('请填写所有必填字段（标有*的字段）')
        return
      }
      
      this.$emit('confirm', {...this.teacher})
    },
    handleAvatarUpload(event) {
      const file = event.target.files[0]
      if (file) {
        const validTypes = ['image/jpeg', 'image/png', 'image/gif']
        if (!validTypes.includes(file.type)) {
          alert('仅支持jpg、png、gif格式的图片')
          return
        }
        
        if (file.size > 3 * 1024 * 1024) {
          alert('图片大小不能超过3M')
          return
        }
        
        const reader = new FileReader()
        reader.onload = (e) => {
          this.teacher.avatar = e.target.result
        }
        reader.readAsDataURL(file)
      }
    },
    triggerFileInput() {
      document.getElementById('avatarInput').click()
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.modal-header {
  padding: 20px 30px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 28px;
  cursor: pointer;
  padding: 0;
  color: #999;
}

.modal-body {
  padding: 25px 30px;
}

.teacher-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
}

.avatar-section {
  flex: 1;
  min-width: 300px;
}

.info-section {
  flex: 2;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.avatar-upload {
  margin-bottom: 0;
}

.avatar-upload label {
  display: block;
  margin-bottom: 12px;
  font-weight: 500;
  color: #333;
  font-size: 16px;
}

.upload-container {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 15px;
}

.avatar-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.avatar-preview {
  width: 170px;
  height: 170px;
  overflow: hidden;
  border: 1px solid #eee;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.upload-info {
  text-align: center;
}

.upload-info p {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #999;
  line-height: 1.5;
}

.upload-btn {
  display: block;
  width: 120px;
  padding: 8px 12px;
  margin: 0 auto;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.upload-btn:hover {
  background: #40a9ff;
}

.form-group {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  margin-bottom: 10px;
  font-weight: 500;
  color: #333;
  font-size: 15px;
}

.required {
  color: #f5222d;
  margin-left: 4px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 15px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 15px;
}

.form-group textarea {
  min-height: 100px;
  resize: vertical;
}

.full-width {
  grid-column: 1 / -1;
}

.modal-footer {
  padding: 20px 30px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

.cancel-btn {
  padding: 10px 20px;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  color: #666;
  cursor: pointer;
  font-size: 15px;
}

.save-btn {
  padding: 10px 20px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 15px;
}

.save-btn:hover {
  background: #40a9ff;
}
</style>