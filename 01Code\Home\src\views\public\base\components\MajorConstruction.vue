// src\views\public\base\components\MajorConstruction.vue
<template>
  <div class="major-construction">
    <div class="construction-content">
      <div class="construction-header">
        <h2>{{ majorInfo.degree }} | {{ majorInfo.name }}</h2>
        <p>{{ majorInfo.description }}</p>
      </div>
      
      <div class="construction-stats">
        <div class="stats-group">
          <div class="stat-item" v-for="item in majorStats.basic" :key="item.title">
            <p class="stat-number">{{ item.value }}</p>
            <p class="stat-title">{{ item.title }}</p>
          </div>
        </div>
        
        <div class="stats-group">
          <div class="stat-item" v-for="item in majorStats.detail" :key="item.title">
            <p class="stat-number">{{ item.value }}</p>
            <p class="stat-title">{{ item.title }}</p>
          </div>
        </div>
      </div>
      
      <div class="distribution-section">
        <div class="distribution-item" v-for="(item, index) in distributionData" :key="index">
          <h3>{{ item.title }}</h3>
          <div class="distribution-content">
            <p v-if="item.type === 'ideological'">包含思政点的课程图谱{{ item.courseCount }} 门</p>
            <p v-if="item.type === 'ideological'">共建设思政点{{ item.pointCount }} 个</p>
            
            <p v-if="item.type === 'practical'">包含实践点的课程图谱{{ item.courseCount }} 门</p>
            <p v-if="item.type === 'practical'">共建设实践点{{ item.pointCount }} 个</p>
            <button @click="handleViewDetails(index)">{{ item.buttonText }}</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useMajorInfoStore } from '@/stores/public/Base/majorInfo'; // 假设使用Pinia管理状态

// 接收父组件传递的参数
const props = defineProps({
  majorId: {
    type: Number,
    default: 1
  },
  showImage: {
    type: Boolean,
    default: true
  }
});

// 组件内部状态
const isLoading = ref(true);
const error = ref(null);
const majorInfo = ref({});
const majorStats = ref({});
const distributionData = ref([]);

// 模拟后端数据（开发环境使用模拟数据，生产环境注释掉）
const useMockData = import.meta.env.DEV; // 仅在开发环境使用模拟数据

const fetchMajorData = async () => {
  if (useMockData) {
    // 模拟数据（直接返回，不发送请求）
    majorInfo.value = {
      degree: '工学学士',
      name: '数字媒体技术专业',
      description: '本专业培养具有数字媒体技术基础理论、专业知识和实践能力，能在数字媒体领域从事设计、开发与应用的高级专门人才。'
    };
    
    majorStats.value = {
      basic: [
        { title: '培养目标', value: 5 },
        { title: '毕业要求', value: 14 },
      ],
      detail: [
        { title: '课程体系', value: 49 },
        { title: '能力数量', value: 28 },
        { title: '问题数量', value: 0 },
        { title: '知识数量', value: 4528 },
      ]
    };
    
    distributionData.value = [
      {
        title: '思政点分布',
        courseCount: 12,
        pointCount: 36,
        buttonText: '查看详情',
        type: 'ideological'
      },
      {
        title: '实践点分布',
        courseCount: 18,
        pointCount: 54,
        buttonText: '查看详情',
        type: 'practical'
      }
    ];
    return;
  }
}
/*
// 获取数据的函数
const fetchMajorData = async () => {
  try {
    // 从后端API获取数据
    const [infoRes, statsRes, distributionRes] = await Promise.all([
      fetch(`/api/majors/${props.majorId}`),
      fetch(`/api/majors/${props.majorId}/stats`),
      fetch(`/api/majors/${props.majorId}/distribution`)
    ]);
    
    if (!infoRes.ok || !statsRes.ok || !distributionRes.ok) {
      throw new Error('Failed to fetch major data');
    }
    
    majorInfo.value = await infoRes.json();
    majorStats.value = await statsRes.json();
    distributionData.value = await distributionRes.json();
  } catch (err) {
    error.value = err.message;
  } finally {
    isLoading.value = false;
  }
};
*/
// 处理查看详情事件
const handleViewDetails = (index) => {
  // 根据不同类型触发不同事件
  emit('view-details', distributionData.value[index].type);
};

// 生命周期钩子
onMounted(() => {
  fetchMajorData();
});

// 提供给父组件的事件
const emit = defineEmits(['view-details']);
</script>

<style scoped lang="scss">
.major-construction {
  display: flex;
  width: 100%;
  background-color: #f9f9f9;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
  
  .construction-image {
    flex: 1;
    background-size: cover;
    background-position: center;
    display: none; // 默认隐藏，根据props控制显示
    
    @media (min-width: 769px) {
      display: block;
    }
  }
  
  .construction-content {
    flex: 2;
    padding: 2rem;
    
    .construction-header {
      margin-bottom: 1.5rem;
      
      h2 {
        font-size: 1.8rem;
        color: #333;
        margin-bottom: 0.5rem;
      }
      
      p {
        color: #666;
        font-size: 1rem;
      }
    }
    
    .construction-stats {
      display: grid;
      grid-template-columns: 1fr;
      gap: 1.5rem;
      margin-bottom: 1.5rem;
      
      .stats-group {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1rem;
        
        .stat-item {
          background-color: white;
          border-radius: 8px;
          padding: 1rem;
          text-align: center;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
          
          .stat-number {
            font-size: 1.8rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.3rem;
          }
          
          .stat-title {
            font-size: 0.9rem;
            color: #666;
          }
        }
      }
    }
    
    .distribution-section {
      display: grid;
      grid-template-columns: 1fr;
      gap: 1rem;
      
      @media (min-width: 768px) {
        grid-template-columns: 1fr 1fr;
      }
      
      .distribution-item {
        background-color: white;
        border-radius: 8px;
        padding: 1.2rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        
        h3 {
          font-size: 1.2rem;
          color: #333;
          margin-bottom: 0.8rem;
        }
        
        .distribution-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          p {
            color: #666;
            margin: 0.3rem 0;
          }
          
          button {
            background-color: #409eff;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 0.5rem 1rem;
            cursor: pointer;
            transition: background-color 0.3s;
            
            &:hover {
              background-color: #3088ee;
            }
          }
        }
      }
    }
  }
}
</style>    