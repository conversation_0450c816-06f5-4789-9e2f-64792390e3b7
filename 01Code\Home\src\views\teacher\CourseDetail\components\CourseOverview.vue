<!--src\views\teacher\CourseDetail\components\CourseOverview.vue-->
<template>
  <div class="course-overview-container">
    <CourseSidebar @menuClick="handleMenuClick" />
    <CourseContent :currentComponent="currentComponent" :course="course" />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import CourseSidebar from './Overview/CourseSidebar.vue'; 
import CourseContent from './Overview/CourseContent.vue';
import CourseIntro from './Overview/components/CourseIntro.vue';
import TeachingTeam from './Overview/components/courseTeachTeam/TeachingTeam.vue';
import CurriculumDesign from './Overview/components/CurriculumDesign.vue';
import TeachingOutline from './Overview/components/TeachingOutline.vue';
import KnowledgeModules from './Overview/components/KnowledgeModules.vue';
// 新增：导入课程框架组件
import CourseFramework from './Overview/components/CourseFramework.vue';

// 接收父组件传递的课程数据
const props = defineProps({
  course: {
    type: Object,
    default: () => ({})
  }
});

// 定义当前要显示的组件
const currentComponent = ref(CourseIntro);

// 处理菜单点击事件
const handleMenuClick = (key) => {
  // 根据不同的菜单键设置不同的组件
  switch(key) {
    case 'course-intro':
      currentComponent.value = CourseIntro;
      break;
    case 'teaching-team':
      currentComponent.value = TeachingTeam;
      break;
    case 'curriculum-design':
      currentComponent.value = CurriculumDesign;
      break;
    case 'teaching-outline':
      currentComponent.value = TeachingOutline;
      break;
    case 'knowledge-modules':
      currentComponent.value = KnowledgeModules;
      break;
    // 新增：课程框架菜单处理
    case 'course-framework':
      currentComponent.value = CourseFramework;
      break;
    default:
      currentComponent.value = CourseIntro;
  }
};
</script>

<style scoped>
.course-overview-container {
  display: flex;
  min-height: calc(100vw - 60px); 
}
</style>