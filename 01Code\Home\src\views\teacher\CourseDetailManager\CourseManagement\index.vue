<template>
  <div class="course-management-container">
    <!-- 归档状态显示 -->
    <div class="archive-status" v-if="archiveStatusVisible">
      {{ archiveStatusText }}
    </div>

    <div class="action-buttons">
      <!-- 结课归档按钮 -->
      <div class="button-wrapper" @click="handleEndCourse">
        <div class="icon-container">
          <svg class="custom-icon" viewBox="0 0 24 24">
            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/>
          </svg>
        </div>
        <span class="button-text">结课归档</span>
      </div>

      <!-- 其他按钮保持不变 -->
      <div class="button-wrapper" @click="handleExportCourse">
        <div class="icon-container">
          <svg class="custom-icon" viewBox="0 0 24 24">
            <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
          </svg>
        </div>
        <span class="button-text">课程数据导出</span>
      </div>

      <div class="button-wrapper" @click="handleDeleteCourse">
        <div class="icon-container">
          <svg class="custom-icon" viewBox="0 0 24 24">
            <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
          </svg>
        </div>
        <span class="button-text">删除课程</span>
      </div>
    </div>

    <ExportDataModal 
      v-model:visible="isExportModalVisible"
      @confirm="handleExportConfirm"
    />
      
    <CountdownDialog
      v-model:visible="showCountdownDialog"
      title="重要提示"
      :message="messageContent"
      confirm-text="我已知晓规则"
      cancel-text="再想想"
      :duration="5"
      @confirm="handleArchiveConfirm"
      @cancel="handleArchiveCancel"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import request from '@/api/service'
import ExportDataModal from './components/ExportDataModal.vue'
import CountdownDialog from './components/CountdownDialog.vue'

const route = useRoute()
const router = useRouter()
const courseId = route.params.courseId

// 定义消息内容
const messageContent = ref(`
  <p>结课归档后，课程和群聊将会被移入[已结束]，系统将停止更新本课所有班级的学生成绩，学生的所有成绩将被固化。</p>
  <p style="color: red;">请慎重操作！</p>
`)

/* 导出课程数据相关 */
const isExportModalVisible = ref(false)
const handleExportCourse = () => {
  isExportModalVisible.value = true
}
const handleExportConfirm = (selectedTypes) => {
  console.log('选中的导出类型:', selectedTypes)
  isExportModalVisible.value = false
}

/* 删除课程相关 */
const handleDeleteCourse = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要删除此课程吗？此操作不可撤销！',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    const response = await request.post('/course/remove', { id: courseId })
    if (response.code === 0) {
      ElMessage.success(response.msg || '课程删除成功')
      router.push('/teacher/teach-class')
    } else {
      ElMessage.error(response.msg || '课程删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除课程失败:', error)
      ElMessage.error(error.response?.data?.msg || error.message || '删除课程失败')
    }
  }
}

/* 结课归档相关 */
const archiveStatusVisible = ref(false)
const archiveStatusText = ref('')
const showCountdownDialog = ref(false)

const handleEndCourse = () => {
  console.log('结课归档按钮点击')
  showCountdownDialog.value = true
}

const handleArchiveConfirm = async () => {
  try {
    const response = await request.post('/course/archive', { id: courseId })
    if (response.code === 0) {
      const now = new Date()
      const formattedTime = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')} ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
      archiveStatusText.value = `已归档（最新归档时间：${formattedTime}）`
      archiveStatusVisible.value = true
      ElMessage.success('结课归档成功')
    } else {
      ElMessage.error(response.msg || '结课归档失败，请重试')
    }
  } catch (error) {
    ElMessage.error(error.response?.data?.msg || error.message || '结课归档失败')
  }
}

const handleArchiveCancel = () => {
  ElMessage.info('已取消结课归档操作')
}
</script>

<!-- 样式部分保持不变 -->

<style scoped>
.course-management-container {
  display: flex;
  flex-direction: column;
  justify-content: center; 
  align-items: center; 
  min-height: 80vh; 
  padding: 20px;
  box-sizing: border-box; 
  background-color: #fff;
  border-radius: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.034);
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 54px;
  width: 100%;
  max-width: 1800px; 
}

.button-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 230px;
  padding: 100px 20px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.button-wrapper:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.button-wrapper:nth-child(1) {
  background-color: #f0f7ff;
  border: 1px solid #d0e3ff;
  color: #409eff;
}

.button-wrapper:nth-child(2) {
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  color: #606266;
}

.button-wrapper:nth-child(3) {
  background-color: #fff0f0;
  border: 1px solid #ffd0d0;
  color: #f56c6c;
}

.icon-container {
  margin-bottom: 12px;
}

.custom-icon {
  width: 64px;
  height: 64px;
  fill: currentColor;
}

.button-text {
  font-size: 16px;
  font-weight: 500;
}

.archive-status {
  padding: 10px 20px;
  background-color: #f0f9eb;
  color: #67c23a;
  border-radius: 4px;
  margin-bottom: 20px;
  font-size: 14px;
  border: 1px solid #e1f3d8;
}
</style>