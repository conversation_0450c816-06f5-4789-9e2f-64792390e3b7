
import {
  uploadFile,
  saveResource,
  saveFolder,
  getFolder,
  getPublicFolderTree,
  saveResourceLocation,
  deleteResources,
  deleteFolders,
  updateResource,
  updateFolder,
  updateResourceLocation
} from '@/api/teacher/publicResources'
import { useUserStore } from '@/stores/userStore'
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'


export const usePublicResourceStore = defineStore('publicResource', () => {
  const userStore = useUserStore()
  const createdId = computed(() => userStore.user.id)
  const fullFolderTree = ref([]) 
  const orphanResources = ref([]) 
  const searchKeyword = ref('')
  const isSearchMode = ref(false)


  // 当前文件夹状态
  const currentFolder = ref(null)
  const breadcrumbs = ref([])

  // 初始化根文件夹
  const initRootFolder = () => {
    currentFolder.value = {
      id: 'root',
      name: '全部文档',
      type: 'folder',
      parent: null,
      children: []
    }
  }

  // 上传资源 
  async function uploadResource(file, options = {}) {
    try {
      // 1. 创建FormData并添加文件
      const formData = new FormData()
      formData.append('file', file, file.name)

      // 2. 上传文件
      const uploadResponse = await uploadFile(formData)
      const fileUrl = uploadResponse.result.url


      // 3. 准备资源数据
      const resourceData = {
        url: fileUrl,
        publisherId: createdId.value,
        name: file.name,
        type: 1,
        fileExtension: file.name.split('.').pop() || '',
        sysName: file.name,
      }

      // 4. 保存资源信息
      const saveResponse = await saveResource(resourceData)
      const resourceId = saveResponse.result.id

      // 5. 保存资源位置
      const folderId = currentFolder.value.id === 'root' ? null : currentFolder.value.id
      await saveResourceLocation({
        resourceId: resourceId,
        folderId: folderId,
        locationType: 1
      })

      return {
        ...saveResponse.data,
        name: file.name,
        size: file.size
      }
    } catch (error) {
      console.error('上传资源失败:', error)
      throw _formatError(error)
    }
  }

  // 创建文件夹
  async function createFolder(name, parentId = null) {
    try {
      const folderData = {
        name,
        parentId: parentId === 'root' ? null : parentId,
        folderType: 1,
        createdId: createdId.value,
      }

      const response = await saveFolder(folderData)
      return response.result
    } catch (error) {
      console.error('创建文件夹失败:', error)
      throw _formatError(error)
    }
  }

  // 获取文件夹内容方法
  async function fetchFolderContents(folderId = null, folderName = null) {
    try {
      const requestParams = {
        publisherId: createdId.value,
        ...(folderName && { folderName }) 
      }
      const response = await getPublicFolderTree(requestParams)
      const folderTree = response.result.folderTree || []
      const orphanResources = response.result.orphanResources || []

      // 处理根目录情况
      if (folderId === 'root' || !folderId) {
        const rootFolders = folderTree.map(formatFolderItem)
        const rootFiles = orphanResources.map(formatFileItem)
        return [...rootFolders, ...rootFiles]
      }

      // 查找指定文件夹
      const findFolder = (folders, targetId) => {
        for (const folder of folders) {
          if (folder.id === targetId) {
            return folder
          }
          if (folder.children && folder.children.length > 0) {
            const found = findFolder(folder.children, targetId)
            if (found) return found
          }
        }
        return null
      }

      const targetFolder = findFolder(folderTree, folderId)
      if (!targetFolder) return []

      const folderItems = (targetFolder.children || []).map(formatFolderItem)
      const fileItems = (targetFolder.resources || []).map(formatFileItem)

      return [...folderItems, ...fileItems]
    } catch (error) {
      console.error('获取文件夹内容失败:', error)
      throw _formatError(error)
    }
  }



  async function fetchFullFolderTree() {
  try {
    const response = await getPublicFolderTree({ publisherId: createdId.value })
    fullFolderTree.value = response.result.folderTree || []

    // 递归处理文件夹中的文件和子文件夹
    const formatTree = (folderTree) => {
      return folderTree.map(folder => {
        return {
          ...folder,
          children: folder.children ? formatTree(folder.children) : [],
          resources: folder.resources || [],
        }
      })
    }

    fullFolderTree.value = formatTree(fullFolderTree.value)
    orphanResources.value = response.result.orphanResources || []
    return fullFolderTree.value
  } catch (error) {
    console.error('获取完整文件夹树失败:', error)
    throw _formatError(error)
  }
}

async function searchFiles(keyword) {
  searchKeyword.value = keyword.trim();
  isSearchMode.value = !!keyword;


    try {
      // 执行搜索，获取符合关键词的文件和文件夹
      const res = await getPublicFolderTree({
        publisherId: createdId.value,
        folderName: keyword
      });

      const searchResults = [
        ...(res.result.folderTree || []).map(formatFolderItem),
        ...(res.result.orphanResources || []).map(formatFileItem)
      ];

      currentFolder.value = {
        ...currentFolder.value,  // 保留原有字段
        children: searchResults   // 更新 children 为搜索结果
      };
    } catch (error) {
      console.error('搜索失败:', error);
      ElMessage.error(`搜索失败: ${error.message || '未知错误'}`);
    }
  
}



  // 格式化文件夹数据 
  function formatFolderItem(item) {
    return {
      id: item.id,
      name: item.name,
      type: 'folder',
      size: '--',
      parent: item.parentId || 'root',
      ...item
    }
  }

  // 格式化文件数据 
  function formatFileItem(item) {
    return {
      id: item.id,
      name: item.name,
      type: 'file',
      parent: item.folderId || 'root',
      url: item.url,
      fileExtension: item.fileExtension,
      ...item
    }
  }

  // 删除资源
  async function removeResources(ids) {
    try {
      const items = Array.isArray(ids[0]) ? ids : ids.map(id => ({ id }));

      // 获取完整类型信息
      const typedItems = currentFolder.value?.children
        ? items.map(item => {
          const fullItem = currentFolder.value.children.find(f => f.id === item.id);
          return {
            id: item.id,
            type: fullItem?.type || 'file' // 默认按文件处理
          };
        })
        : items; // 如果无法获取类型，按原始参数处理

      // 分离文件和文件夹
      const filesToDelete = typedItems.filter(item =>
        item.type === 'file' || item.type === 1
      );
      const foldersToDelete = typedItems.filter(item =>
        item.type === 'folder' || item.type === 2
      );

      // 执行删除
      const [fileResponse, folderResponse] = await Promise.all([
        filesToDelete.length > 0
          ? deleteResources(filesToDelete.map(({ id }) => ({ id })))
          : Promise.resolve({ success: true }),
        foldersToDelete.length > 0
          ? deleteFolders(foldersToDelete.map(({ id }) => ({ id })))
          : Promise.resolve({ success: true })
      ]);

      // 更新本地状态
      if (currentFolder.value) {
        currentFolder.value.children = currentFolder.value.children.filter(
          item => !typedItems.some(deletedItem => deletedItem.id === item.id)
        );
      }

      return {
        success: true,
        fileResponse,
        folderResponse
      };
    } catch (error) {
      console.error('删除资源失败:', error);
      throw _formatError(error);
    }
  }

  // 导航到文件夹
  async function navigateToFolder(folderId) {
    try {
      const contents = await fetchFolderContents(folderId)
      if (folderId === 'root') {
        currentFolder.value = {
          id: 'root',
          name: '全部文档',
          type: 'folder',
          children: contents,
          parent: null
        }
      } else {
        const folderInfo = await getFolder(folderId)
        currentFolder.value = {
          id: folderInfo.result.id,
          name: folderInfo.result.name,
          type: 'folder',
          children: contents,
          parent: folderInfo.result.parentId || 'root'
        }
      }

      await updateBreadcrumbs()
    } catch (error) {
      console.error('导航到文件夹失败:', error)
      throw _formatError(error)
    }
  }


  async function updateBreadcrumbs() {
    if (!currentFolder.value) {
      breadcrumbs.value = []
      return
    }

    const crumbs = []
    let currentId = currentFolder.value.id

    // 从当前文件夹开始向上追溯，直到根目录
    while (currentId && currentId !== 'root') {
      const folderInfo = await getFolder(currentId)
      crumbs.unshift({
        id: folderInfo.result.id,
        name: folderInfo.result.name
      })
      currentId = folderInfo.result.parentId
    }

    // 添加根目录作为第一个面包屑
    if (crumbs.length > 0) {
      crumbs.unshift({
        id: 'root',
        name: '全部文档'
      })
    }

    breadcrumbs.value = crumbs
  }



  function _formatError(error) {
    if (error.response) {
      return new Error(
        error.response.data.message ||
        error.response.data.error ||
        `服务器错误: ${error.response.status}`
      )
    } else if (error.request) {
      return new Error('网络错误，请检查您的网络连接')
    } else {
      return error instanceof Error ? error : new Error(String(error))
    }
  }

  async function downloadFile(fileId) {
    try {
      console.log('[下载开始] fileId:', fileId)

      // 1. 查找文件
      const findFile = (items) => {
        for (const item of items) {
          if (item.id === fileId) {
            return item.url ? item : null
          }
          if (item.children) {
            const found = findFile(item.children)
            if (found) return found
          }
          if (item.resources) {
            const found = findFile(item.resources)
            if (found) return found
          }
        }
        return null
      }
      const fileToDownload = findFile(currentFolder.value?.children || [])

      if (!fileToDownload || !fileToDownload.url) {
        ElMessage.error('未找到要下载的文件或文件无效')
        return
      }

      const fileUrl = fileToDownload.url
      const fileName = fileToDownload.name

      // 2. 创建隐藏的iframe下载（避免CORS问题）
      const iframe = document.createElement('iframe')
      iframe.style.display = 'none'
      iframe.src = fileUrl
      document.body.appendChild(iframe)

      // 3. 备用方案：传统下载方式
      setTimeout(() => {
        const link = document.createElement('a')
        link.href = fileUrl
        link.download = fileName
        link.target = '_blank'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        document.body.removeChild(iframe)
      }, 1000)

      ElMessage.success(`正在下载文件: ${fileName}`)
    } catch (error) {
      console.error('下载文件失败:', error)
      ElMessage.error(`下载文件失败: ${error.message || '未知错误'}`)
    }
  }



  async function updateResourceInfo(resourceData) {
    try {
      // 校验必须参数
      if (!resourceData.id) {
        throw new Error('更新资源必须提供资源ID');
      }

      // 构造更新数据，只包含实际提供的字段
      const payload = {
        id: resourceData.id,
        ...(resourceData.type !== undefined && { type: resourceData.type }),
        ...(resourceData.url !== undefined && { url: resourceData.url }),
        ...(resourceData.description !== undefined && { description: resourceData.description }),
        ...(resourceData.name !== undefined && { name: resourceData.name }),
        ...(resourceData.coverUrl !== undefined && { coverUrl: resourceData.coverUrl })
      };

      const response = await updateResource(payload);

      if (currentFolder.value?.children) {
        const index = currentFolder.value.children.findIndex(item => item.id === resourceData.id);
        if (index !== -1) {
          currentFolder.value.children[index] = {
            ...currentFolder.value.children[index],
            ...payload
          };
        }
      }

      return response;
    } catch (error) {
      console.error('更新资源信息失败:', error);
      throw _formatError(error);
    }
  }

  // 更新文件夹信息
  async function updateFolderInfo(folderData) {
    try {
      // 校验必须参数
      if (!folderData.id) {
        throw new Error('更新文件夹必须提供文件夹ID');
      }

      // 构造更新数据，只包含实际提供的字段
      const payload = {
        id: folderData.id,
        ...(folderData.name !== undefined && { name: folderData.name }),
        ...(folderData.parentId !== undefined && { parentId: folderData.parentId }),
        ...(folderData.folderType !== undefined && { folderType: folderData.folderType }),
        ...(folderData.createdId !== undefined && { createdId: folderData.createdId }),
        ...(folderData.courseId !== undefined && { courseId: folderData.courseId }),
      };

      // 调用文件夹更新API
      const response = await updateFolder(payload);

      // 更新当前文件夹中的子文件夹信息
      if (currentFolder.value?.children) {
        const index = currentFolder.value.children.findIndex(item => item.id === folderData.id);
        if (index !== -1) {
          currentFolder.value.children[index] = {
            ...currentFolder.value.children[index],
            ...payload
          };
        }
      }

      return response;
    } catch (error) {
      console.error('更新文件夹信息失败:', error);
      throw _formatError(error);
    }
  }

  // 更新文件位置信息
  async function updateResourceLocationInfo(locationData) {
    try {
      // 校验必须参数
      if (!locationData.id) {
        throw new Error('更新文件位置必须提供ID');
      }
      if (!locationData.folderId) {
        throw new Error('更新文件位置必须提供folderId');
      }

      const payload = {
        resourceId: locationData.id,
        folderId: locationData.folderId, // folderId是必需参数，始终包含
      };

      // 调用文件夹位置更新API
      const response = await updateResourceLocation(payload);
      // 更新当前文件夹中的子文件夹信息（如果适用）
      if (currentFolder.value?.children) {
        const index = currentFolder.value.children.findIndex(item => item.id === locationData.id);
        if (index !== -1) {
          currentFolder.value.children[index] = {
            ...currentFolder.value.children[index],
            folderId: locationData.folderId,
            ...(locationData.resourceId !== undefined && { resourceId: locationData.resourceId }),
          };
        }
      }

      return response;
    } catch (error) {
      console.error('更新文件夹位置信息失败:', error);
      throw _formatError(error);
    }
  }

  return {
    fullFolderTree,
    orphanResources,
    createdId,
    currentFolder,
    breadcrumbs,
      searchFiles, 
    fetchFullFolderTree,
    removeResources,
    downloadFile,
    initRootFolder,
    uploadResource,
    createFolder,
    fetchFolderContents,
    navigateToFolder,
    updateBreadcrumbs,
    updateResourceInfo,
    updateFolderInfo,
    updateResourceLocationInfo

  }
})