<template>
    <div class="ability-form-panel">
        <!-- 空状态显示 -->
        <div class="empty-state" v-if="!mainAbility.id && !isEditing">
            <img :src="emptyIcon" alt="空状态" class="empty-icon" />
            <p class="empty-text">请从左侧选择能力目标或创建新能力</p>
        </div>

        <template v-else>
            <!-- 头部区域 -->
            <div class="form-header">
                <div class="header-left">
                    <img :src="arrowIcon" alt="主能力图标" class="ability-icon" />
                    <span class="ability-title">主能力</span>
                </div>
                <div class="header-right">
                    <!-- 编辑按钮（非编辑模式显示） -->
                    <button class="icon-btn" @click="handleEdit" v-if="!isEditing && !isNewAbility">
                        <img :src="editIcon" alt="编辑" class="action-icon" />
                        <span class="tooltip">编辑</span>
                    </button>

                    <!-- 删除按钮（非新建状态显示） -->
                    <button class="icon-btn" @click="handleDelete" v-if="!isNewAbility">
                        <img :src="deleteIcon" alt="删除" class="action-icon" />
                        <span class="tooltip">删除</span>
                    </button>
                </div>
            </div>

            <!-- 主能力详情 -->
            <div class="main-ability-section" ref="mainAbilitySection">
                <div class="form-item">
                    <label class="form-label">
                        主能力名称<span class="required-mark">*</span>
                    </label>
                    <input type="text" class="form-input" placeholder="请输入主能力名称" v-model="mainAbility.name"
                        :disabled="!isEditing" />
                    <div class="error-message" v-if="showNameError">
                        {{ showNameError === true ? '主能力名称不能为空' : showNameError }}
                    </div>
                </div>

                <div class="form-item">
                    <label class="form-label">能力目标描述</label>
                    <textarea class="form-textarea" placeholder="请输入能力目标描述（选填）" v-model="mainAbility.description"
                        :disabled="!isEditing"></textarea>
                </div>
            </div>

            <!-- 子能力列表 -->
            <div class="sub-abilities-section" v-if="subAbilities.length > 0">
                <!-- 子能力项 -->
                <div class="sub-ability-item" v-for="(subAbility, index) in subAbilities" :key="index"
                    :ref="el => setSubAbilityRef(el, index)">
                    <div class="sub-ability-header">
                        <div class="header-left">
                            <img :src="subAbilityIcon" alt="子能力图标" class="sub-ability-icon" />
                            <span class="sub-ability-title">子能力 {{ index + 1 }}</span>
                        </div>
                        <button class="icon-btn" @click="removeSubAbility(index)" v-if="isEditing">
                            <img :src="deleteIcon" alt="删除" class="action-icon" />
                            <span class="tooltip">删除子能力</span>
                        </button>
                    </div>

                    <div class="form-item">
                        <label class="form-label">
                            子能力名称<span class="required-mark">*</span>
                        </label>
                        <input type="text" class="form-input" placeholder="请输入子能力名称" v-model="subAbility.name"
                            :disabled="!isEditing" />
                        <div class="error-message" v-if="showSubAbilityNameErrors[index]">
                            {{ showSubAbilityNameErrors[index] === true ? '子能力名称不能为空' :
                            showSubAbilityNameErrors[index].includes('同一图谱内') ? '该子能力名称已存在' :
                            showSubAbilityNameErrors[index].includes('节点名称') ? '该名称已存在' :
                            showSubAbilityNameErrors[index] }}
                        </div>
                    </div>

                    <div class="form-item">
                        <label class="form-label">子能力描述</label>
                        <textarea class="form-textarea" placeholder="请输入子能力描述" v-model="subAbility.description"
                            :disabled="!isEditing"></textarea>
                    </div>

                    <div class="form-item">
                        <label class="form-label">子能力关联知识模块与知识点</label>
                        <KnowledgeTreeView :isEditing="isEditing" :initialData="subAbility.knowledgeNodes"
                            :subAbilityIndex="index" :mainAbilityId="mainAbility.id"
                            @update="knowledgeNodes => updateSubAbilityKnowledge(index, knowledgeNodes)" />
                    </div>
                </div>
            </div>

            <!-- 添加子能力按钮 -->
            <button class="add-sub-ability-btn" @click="handleAddSubAbility" :disabled="!isEditing"
                v-if="!isNewAbility && mainAbility.id">
                <span>⊕ 添加子能力</span>
            </button>
        </template>
    </div>
</template>

<script setup>
import { ref, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import { useAbilityGraphStore } from '@/stores/teacher/graphManager/abilityGraphStore';
import { ElMessageBox, ElMessage } from 'element-plus'
import KnowledgeTreeView from './KnowledgeTreeView.vue';

import emptyIcon from '@/assets/courseMap-icon/createAbGraph/emptyIcon.svg';
import arrowIcon from '@/assets/courseMap-icon/createAbGraph/arrowIcon.svg';
import editIcon from '@/assets/courseMap-icon/createAbGraph/editIcon.svg';
import deleteIcon from '@/assets/courseMap-icon/createAbGraph/deleteIcon.svg';
import subAbilityIcon from '@/assets/courseMap-icon/createAbGraph/arrowIcon.svg';

const route = useRoute();
const store = useAbilityGraphStore();

const emit = defineEmits(['edit']);

// 主能力数据
const mainAbility = ref({
    id: null,
    name: '',
    description: ''
});

// 子能力数据
const subAbilities = ref([]);
const isEditing = ref(false);
const isNewAbility = ref(false);
const showNameError = ref(false);
const showSubAbilityNameErrors = ref([]);
// 原始数据保存
const originalMainAbility = ref({});
const originalSubAbilities = ref([]);
// 跟踪当前选中的子能力ID
const selectedSubAbilityId = ref(null); 

// DOM引用
const mainAbilitySection = ref(null);
const subAbilityRefs = ref([]);

// 设置子能力引用
const setSubAbilityRef = (el, index) => {
    if (el) {
        subAbilityRefs.value[index] = el;
    }
};

// 加载能力详情
const loadAbilityDetails = async (mainAbilityData, subAbilitiesData = []) => {
  try {
    // 1. 确保知识树数据已加载
    if (!store.knowledgeTreeData && store.knowledgeGraphId) {
      await store.fetchKnowledgeTree(store.knowledgeGraphId);
    }
    
    // 2. 获取主能力详情（包含子能力）
    const detailRes = await store.getMainAbilityDetail(mainAbilityData.abilityId);
    const fullData = detailRes.result || {};
    
    // 3. 更新当前显示数据
    mainAbility.value = {
      id: fullData.abilityId || mainAbilityData.abilityId,
      name: fullData.nodeName || mainAbilityData.nodeName,
      description: fullData.nodeDesc || mainAbilityData.nodeDesc || ''
    };

    // 4. 处理子能力数据，确保包含关联的知识点
    subAbilities.value = subAbilitiesData.map(sub => ({
      id: sub.abilityId,
      name: sub.nodeName,
      description: sub.nodeDesc || '',
      knowledgeNodes: sub.knowledgeNodes || [] // 使用传入的知识点数据
    }));

    // 5. 保存原始数据
    originalMainAbility.value = { ...mainAbility.value };
    originalSubAbilities.value = JSON.parse(JSON.stringify(subAbilities.value));

    // 6. 重置状态
    isEditing.value = false;
    isNewAbility.value = false; // 确保这不是新建状态
    showNameError.value = false;
    showSubAbilityNameErrors.value = [];
  } catch (error) {
    console.error('加载能力详情失败:', error);
    throw error;
  }
};

// 辅助函数：展平知识树结构，提取所有nodeId
const flattenKnowledgeTree = (treeNodes) => {
    const result = [];
    const flatten = (nodes) => {
        nodes.forEach(node => {
            result.push(node.nodeId);
            if (node.children && node.children.length) {
                flatten(node.children);
            }
        });
    };
    flatten(treeNodes);
    return result;
};

// 滚动到指定子能力
const scrollToSubAbility = async (index) => {
    await nextTick();
    if (subAbilityRefs.value[index]) {
        subAbilityRefs.value[index].scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        });
    }
};

// 添加子能力
const handleAddSubAbility = () => {
    subAbilities.value.push({
        id: null,
        name: '',
        description: '',
        knowledgeNodes: []
    });
    showSubAbilityNameErrors.value.push(false);
};

// 更新子能力关联的知识节点
const updateSubAbilityKnowledge = async (index, knowledgeNodes) => {
    try {
        // 更新本地状态
        subAbilities.value[index].knowledgeNodes = knowledgeNodes;
        
        // 如果是编辑模式且不是新建能力，直接调用API更新
        if (isEditing.value && !isNewAbility.value && subAbilities.value[index].id) {
            await store.updateRelatedKnowledgeNodes(
                subAbilities.value[index].id,
                knowledgeNodes
            );
        }
    } catch (error) {
        console.error('更新关联知识点失败:', error);
        ElMessage.error('更新关联知识点失败');
    }
};

// 开始新建能力
const startNewAbility = () => {
    // 保存当前数据作为原始数据（如果有）
    if (mainAbility.value.id) {
        originalMainAbility.value = {
            id: mainAbility.value.id,
            name: mainAbility.value.name,
            description: mainAbility.value.description
        };

        originalSubAbilities.value = subAbilities.value.map(sub => ({
            id: sub.id,
            name: sub.name,
            description: sub.description,
            knowledgeNodes: [...sub.knowledgeNodes]
        }));
    }

    // 重置表单
    mainAbility.value = {
        id: null, // 确保id为null表示新建
        name: '',
        description: ''
    };

    subAbilities.value = [];
    showNameError.value = false;
    showSubAbilityNameErrors.value = [];

    // 设置状态
    isEditing.value = true;
    isNewAbility.value = true; // 设置为新建状态

    // 通知父组件
    emit('edit');
};

// 验证表单
const validateForm = () => {
    let isValid = true;

    // 验证主能力名称
    showNameError.value = !mainAbility.value.name.trim();
    if (showNameError.value) {
        isValid = false;
        // 滚动到错误位置
        nextTick(() => {
            mainAbilitySection.value?.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
        });
    }

    // 验证子能力名称
    showSubAbilityNameErrors.value = subAbilities.value.map(sub => !sub.name.trim());
    if (showSubAbilityNameErrors.value.some(error => error)) {
        isValid = false;
        // 滚动到第一个错误的子能力
        nextTick(() => {
            const firstErrorIndex = showSubAbilityNameErrors.value.findIndex(error => error);
            if (firstErrorIndex >= 0 && subAbilityRefs.value[firstErrorIndex]) {
                subAbilityRefs.value[firstErrorIndex].scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
            }
        });
    }

    return isValid;
};

// 保存能力
const saveAbility = async () => {
    // 重置错误状态
    showNameError.value = false;
    showSubAbilityNameErrors.value = [];

    // 验证表单
    if (!validateForm()) {
        return false;
    }

    try {
        if (isNewAbility.value) {
            // 创建主能力
            const mainAbilityData = {
                graphId: store.graphId,
                parentId: store.rootNodeId,
                nodeName: mainAbility.value.name.trim(),
                nodeDesc: mainAbility.value.description.trim()
            };

            let mainAbilityId;
            try {
                const res = await store.createMainAbility(mainAbilityData);
                if (res.code === 400) {
                    throw new Error(res.msg || '主能力名称重复');
                }
                mainAbilityId = res.result;
            } catch (mainError) {
                // 为主能力错误添加标识
                mainError.isMainAbilityError = true;
                throw mainError;
            }

            // 创建子能力
            for (const [index, subAbility] of subAbilities.value.entries()) {
                try {
                    const subAbilityData = {
                        graphId: store.graphId,
                        parentId: mainAbilityId,
                        nodeName: subAbility.name.trim(),
                        nodeDesc: subAbility.description.trim(),
                        knowledgeNodes: subAbility.knowledgeNodes || []
                    };

                    const subRes = await store.createSubAbility(subAbilityData);
                    if (subRes.code === 400) {
                        throw new Error(subRes.msg || `子能力 ${index + 1} 名称重复`);
                    }

                    // 更新关联知识点
                    if (subAbility.knowledgeNodes?.length) {
                        await store.updateRelatedKnowledgeNodes(
                            subRes.result,
                            subAbility.knowledgeNodes
                        );
                    }
                } catch (subError) {
                    // 为子能力错误添加索引信息
                    subError.subAbilityIndex = index;
                    throw subError;
                }
            }
        } else {
            // 更新主能力
            const mainUpdateData = {
                nodeId: mainAbility.value.id,
                nodeName: mainAbility.value.name.trim(),
                nodeDesc: mainAbility.value.description.trim()
            };

            try {
                const mainRes = await store.updateAbilityInfo(mainUpdateData);
                if (mainRes.code === 400) {
                    throw new Error(mainRes.msg || '主能力名称重复');
                }
            } catch (mainError) {
                // 为主能力错误添加标识
                mainError.isMainAbilityError = true;
                throw mainError;
            }

            // 更新子能力
            for (const [index, subAbility] of subAbilities.value.entries()) {
                try {
                    if (subAbility.id) {
                        // 更新现有子能力
                        const subUpdateData = {
                            nodeId: subAbility.id,
                            nodeName: subAbility.name.trim(),
                            nodeDesc: subAbility.description.trim()
                        };

                        const subRes = await store.updateAbilityInfo(subUpdateData);
                        if (subRes.code === 400) {
                            throw new Error(subRes.msg || `子能力 ${index + 1} 名称重复`);
                        }

                        // 更新关联知识点
                        if (subAbility.knowledgeNodes) {
                            await store.updateRelatedKnowledgeNodes(
                                subAbility.id,
                                subAbility.knowledgeNodes
                            );
                        }
                    } else {
                        // 新建子能力
                        const subAbilityData = {
                            graphId: store.graphId,
                            parentId: mainAbility.value.id,
                            nodeName: subAbility.name.trim(),
                            nodeDesc: subAbility.description.trim(),
                            knowledgeNodes: subAbility.knowledgeNodes || []
                        };

                        const subRes = await store.createSubAbility(subAbilityData);
                        if (subRes.code === 400) {
                            throw new Error(subRes.msg || `子能力 ${index + 1} 名称重复`);
                        }

                        // 更新关联知识点
                        if (subAbility.knowledgeNodes?.length) {
                            await store.updateRelatedKnowledgeNodes(
                                subRes.result,
                                subAbility.knowledgeNodes
                            );
                        }
                    }
                } catch (subError) {
                    // 为子能力错误添加索引信息
                    subError.subAbilityIndex = index;
                    throw subError;
                }
            }
        }

        return true;
    } catch (error) {
        console.log('保存错误详情:', error);
        console.log('错误消息:', error.message);
        console.log('是否新建能力:', isNewAbility.value);
        console.log('子能力索引:', error.subAbilityIndex);

        // 处理主能力名称重复
        if (error.isMainAbilityError ||
            error.message.includes('主能力名称重复') ||
            error.message.includes('创建主能力失败: 名称重复') ||
            error.message.includes('更新能力节点失败: 名称重复') ||
            (error.message.includes('名称重复') && isNewAbility.value && !error.subAbilityIndex)) {
            console.log('匹配到主能力名称重复');
            showNameError.value = '该主能力名称已存在';
        }
        // 处理子能力名称重复
        else if (error.message.includes('子能力') ||
                 error.message.includes('创建子能力失败: 名称重复') ||
                 (error.subAbilityIndex !== undefined)) {
            // 优先使用错误对象中的索引信息
            let subIndex = error.subAbilityIndex;

            // 如果没有索引信息，尝试从错误消息中提取
            if (subIndex === undefined) {
                const match = error.message.match(/子能力 (\d+)/);
                subIndex = match ? parseInt(match[1]) - 1 :
                    subAbilities.value.findIndex(sub =>
                        error.config?.data?.nodeName === sub.name.trim());
            }

            if (subIndex >= 0 && subIndex < subAbilities.value.length) {
                showSubAbilityNameErrors.value[subIndex] = '该子能力名称已存在';
            }
        }
        // 处理更新时的名称重复（通过请求数据判断是主能力还是子能力）
        else if (error.message.includes('节点名称已存在') ||
                 error.message.includes('同一图谱内已存在相同名称的节点') ||
                 error.message.includes('名称重复')) {
            const requestData = error.config?.data ? JSON.parse(error.config.data) : null;

            if (requestData?.nodeId === mainAbility.value.id) {
                // 更新主能力时的名称重复
                showNameError.value = '该主能力名称已存在';
            } else if (requestData?.parentId === mainAbility.value.id) {
                // 更新子能力时的名称重复
                const subIndex = subAbilities.value.findIndex(
                    sub => sub.name.trim() === requestData.nodeName
                );
                if (subIndex >= 0) {
                    showSubAbilityNameErrors.value[subIndex] = '该子能力名称已存在';
                }
            } else if (isNewAbility.value && requestData?.parentId === store.rootNodeId) {
                // 新建主能力时的名称重复
                showNameError.value = '该主能力名称已存在';
            } else if (!isNewAbility.value && requestData?.parentId === mainAbility.value.id) {
                // 新建子能力时的名称重复
                const subIndex = subAbilities.value.findIndex(
                    sub => sub.name.trim() === requestData.nodeName
                );
                if (subIndex >= 0) {
                    showSubAbilityNameErrors.value[subIndex] = '该子能力名称已存在';
                }
            }
        }

        // 滚动到错误位置
        nextTick(() => {
            if (showNameError.value) {
                mainAbilitySection.value?.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
            } else {
                const firstErrorIndex = showSubAbilityNameErrors.value.findIndex(error => error);
                if (firstErrorIndex >= 0 && subAbilityRefs.value[firstErrorIndex]) {
                    subAbilityRefs.value[firstErrorIndex].scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                }
            }
        });

        return false;
    }
};

// 取消编辑
const cancelEdit = () => {
    if (isNewAbility.value) {
        // 如果是新建能力，直接重置为空状态
        resetToEmptyState();
    } else {
        // 恢复原始数据
        mainAbility.value = {
            ...originalMainAbility.value
        };
        subAbilities.value = originalSubAbilities.value.map(sub => ({ ...sub }));

        // 重置状态
        isEditing.value = false;
        showNameError.value = false;
        showSubAbilityNameErrors.value = [];
    }
};

// 确认编辑
const confirmEdit = async () => {
    try {
        const success = await saveAbility();
        if (success) {
            isEditing.value = false;
            isNewAbility.value = false;
        }
        return success;
    } catch (error) {
        console.error('保存失败:', error);
        return false;
    }
};

// 处理编辑
const handleEdit = () => {
    isEditing.value = true;
    emit('edit');
};

// 处理主能力删除
const handleDelete = async () => {
    try {
        await ElMessageBox.confirm(
            `确定要删除主能力"${mainAbility.value.name}"吗？${subAbilities.value.length > 0 ? '该主能力下的所有子能力也将被删除！' : ''}`,
            '确认删除',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        );

        // 删除主能力
        const res = await store.deleteAbilityNode(mainAbility.value.id);

        if (res) {
            ElMessage.success('删除成功');
            // 重置为空状态
            resetToEmptyState();
            // 添加第二个参数true表示这是主能力删除
            emit('deleted', true);
        } else {
            ElMessage.error('删除失败');
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除失败:', error);
            ElMessage.error(error.message || '删除失败');
        }
    }
};

// 处理子能力删除
const removeSubAbility = async (index) => {
    try {
        const subAbility = subAbilities.value[index];

        await ElMessageBox.confirm(
            `确定要删除子能力"${subAbility.name}"吗？`,
            '确认删除',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        );

        // 如果有ID，调用API删除
        if (subAbility.id) {
            const res = await store.deleteAbilityNode(subAbility.id);
            if (!res) throw new Error('删除失败');
        }

        // 从前端列表中移除
        subAbilities.value.splice(index, 1);
        showSubAbilityNameErrors.value.splice(index, 1);

        ElMessage.success('删除成功');
        // 添加第二个参数false表示这是子能力删除
        emit('deleted', false);
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除子能力失败:', error);
            ElMessage.error(error.message || '删除子能力失败');
        }
    }
};

// 重置为空状态
const resetToEmptyState = () => {
    mainAbility.value = {
        id: null,
        name: '',
        description: ''
    };
    subAbilities.value = [];
    isEditing.value = false;
    isNewAbility.value = false;
    showNameError.value = false;
    showSubAbilityNameErrors.value = [];
};

defineExpose({
    loadAbilityDetails,
    scrollToSubAbility,
    startNewAbility,
    cancelEdit,
    confirmEdit,
    handleDelete,
    validateForm
});
</script>

<style lang="scss" scoped>
.ability-form-panel {
    display: flex;
    flex-direction: column;
    min-height: 85vh;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    text-align: center;

    .empty-icon {
        width: 100px;
        height: 100px;
        margin-bottom: 20px;
        opacity: 0.6;
    }

    .empty-text {
        color: #999;
        font-size: 16px;
    }
}

.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 16px;
    margin-bottom: 20px;
    border-bottom: 1px solid #e8e8e8;

    .header-left {
        display: flex;
        align-items: center;

        .ability-icon {
            width: 25px;
            height: 25px;
            margin-right: 8px;
        }

        .ability-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
    }

    .header-right {
        display: flex;
        gap: 12px;
    }
}

.main-ability-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e8e8e8;
}

.sub-abilities-section {
    margin-bottom: 20px;
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e8e8e8;
}

.sub-ability-item {
    margin-bottom: 24px;
    padding: 16px;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    background-color: #fafafa;
}

.sub-ability-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e8e8e8;

    .header-left {
        display: flex;
        align-items: center;

        .sub-ability-icon {
            width: 20px;
            height: 20px;
            margin-right: 8px;
        }

        .sub-ability-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
    }
}

.form-item {
    margin-bottom: 20px;

    .form-label {
        display: block;
        margin-bottom: 8px;
        font-size: 14px;
        color: #333;
        font-weight: 500;

        .required-mark {
            color: #ff4d4f;
            margin-left: 4px;
        }
    }

    .form-input {
        width: 100%;
        padding: 10px 12px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        font-size: 14px;
        transition: border-color 0.3s;
        background-color: #fff;

        &:focus {
            border-color: #4c7bff;
            outline: none;
        }

        &::placeholder {
            color: #bfbfbf;
        }

        &:disabled {
            background-color: #f5f5f5;
            color: #777;
            border-color: #e0e0e0;
            cursor: not-allowed;
        }
    }

    .form-textarea {
        width: 100%;
        min-height: 100px;
        padding: 10px 12px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        font-size: 14px;
        resize: vertical;
        transition: border-color 0.3s;
        background-color: #fff;

        &:focus {
            border-color: #4c7bff;
            outline: none;
        }

        &::placeholder {
            color: #bfbfbf;
        }

        &:disabled {
            background-color: #f5f5f5;
            color: #666;
            border-color: #e0e0e0;
            cursor: not-allowed;
        }
    }
}

.add-sub-ability-btn {
    padding: 8px 16px;
    width: 150px;
    color: #4c7bff;
    background-color: white;
    border: 0px;
    font-size: 15px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
        color: #93affd;
    }

    &:disabled {
        color: #999;
        cursor: not-allowed;
    }

    span {
        margin-left: 4px;
    }
}

.error-message {
    color: #ff4d4f;
    font-size: 14px;
    margin-top: 4px;
}

.icon-btn {
    position: relative;
    background: none;
    border: none;
    cursor: pointer;
    padding: 6px;
    border-radius: 4px;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    display: inline-flex;
    align-items: center;
    justify-content: center;

    &:hover {
        background-color: #f5f5f5;

        .tooltip {
            opacity: 1;
            visibility: visible;
            transform: translateX(-50%) translateY(8px);
        }
    }

    .action-icon {
        width: 30px;
        height: 30px;
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    }

    .tooltip {
        position: absolute;
        left: 50%;
        top: 100%;
        transform: translateX(-50%) translateY(8px);
        background-color: #333;
        color: white;
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        pointer-events: none;
        z-index: 1000;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

        &::before {
            content: "";
            position: absolute;
            left: 50%;
            bottom: 100%;
            transform: translateX(-50%);
            border-width: 6px;
            border-style: solid;
            border-color: transparent transparent #333 transparent;
        }
    }
}
</style>