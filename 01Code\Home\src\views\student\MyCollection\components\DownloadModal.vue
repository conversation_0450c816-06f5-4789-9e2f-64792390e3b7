<template>
  <div class="modal-overlay" v-if="visible">
    <div class="modal-content">
      <div class="modal-header">
        <h3>下载文件</h3>
        <button class="close-btn" @click="handleClose">×</button>
      </div>
      <div class="modal-body">
        <p>请输入下载文件包名</p>
        <input
          type="text"
          v-model="packageName"
          placeholder="请输入文件包名"
          class="package-name-input"
        >
      </div>
      <div class="modal-footer">
        <button class="confirm-btn" @click="handleConfirm">确定</button>
        <button class="cancel-btn" @click="handleClose">取消</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DownloadModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    initialPackageName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      packageName: this.initialPackageName
    };
  },
  watch: {
    initialPackageName(newValue) {
      this.packageName = newValue;
    }
  },
  methods: {
    handleConfirm() {
      this.$emit('confirm', this.packageName);
      this.packageName = '';
    },
    handleClose() {
      this.$emit('close');
      this.packageName = '';
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}
.modal-content {
  background-color: #fff;
  border-radius: 8px;
  width: 400px;
  max-width: 90%;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
}
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}
.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #909399;
}
.modal-body {
  padding: 20px;
}
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}
.confirm-btn, .move-btn, .delete-btn {
  padding: 8px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}
.confirm-btn, .move-btn {
  background-color: #409eff;
  color: white;
}
.confirm-btn:hover, .move-btn:hover {
  background-color: #3a8ee6;
}
.cancel-btn {
  padding: 8px 20px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
  color: #606266;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}
.cancel-btn:hover {
  background-color: #f5f7fa;
}
</style>