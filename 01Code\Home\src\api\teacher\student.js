import request from '@/api/service'

// 获取学生列表
export const getStudents = (params) => {
    return request({
        url: '/class/students',
        method: 'get',
        params: params  // 直接传递整个params对象，不强制包含classId
    }).then(response => {
        if (response.code === 200 || response.code === 0) {
            return response.result.records || [];
        } else {
            throw new Error(response.msg || '获取学生列表失败');
        }
    }).catch(error => {
        console.error('请求错误:', error);
        throw error;
    });
};

// 获取班级列表
export const getClasses = (params) => {
    return request({
        url: '/class/list',
        method: 'get',
        params: { courseId: params.courseId }
    }).then(response => {
        return response.result || [];
    });
}

// 获取验证码图片
export const getVerificationCode = () => {
    return request({
        url: '/verification/code',
        method: 'get',
        responseType: 'blob'
    })
}

// 获取课程二维码
export const getCourseQrCode = (params) => {
    return request({
        url: '/course/qrcode',
        method: 'get',
        params: { courseId: params.courseId },
        responseType: 'blob'
    })
}

// 手机号邀请学生
export const inviteByPhone = (params) => {
    return request({
        url: '/student/invite',
        method: 'post',
        data: params,
        headers: {
            'Content-Type': 'application/json;charset=UTF-8'
        }
    })
}

// 批量通过审核
export const enrollStudents = async (params) => {
    const { studentIds, status } = params;
    const requests = studentIds.map(id => {
        return request({
            url: '/class/updateStudentStatus',
            data: { id, status },
            method: 'post',
            headers: {
                'Content-Type': 'application/json;charset=UTF-8'
            }
        });
    });
    return Promise.all(requests);
}

// 批量拒绝学生
export const rejectStudents = async (params) => {
    const { studentIds, status } = params;
    const requests = studentIds.map(id => {
        return request({
            url: '/class/updateStudentStatus',
            data: { id, status },
            method: 'post',
            headers: {
                'Content-Type': 'application/json;charset=UTF-8'
            }
        });
    });
    return Promise.all(requests);
}

// 批量删除学生
export const deleteStudents = async (params) => {
    const { studentIds, status } = params;
    const requests = studentIds.map(id => {
        return request({
            url: '/class/updateStudentStatus',
            data: { id, status },
            method: 'post',
            headers: {
                'Content-Type': 'application/json;charset=UTF-8'
            }
        });
    });
    return Promise.all(requests);
}

// 创建新班级
export const createClass = (params) => {
    return request({
        url: '/class/save',
        method: 'post',
        data: {
            name: params.name,
            courseId: params.courseId,
            //teacherId: params.teacherId
        },
        headers: {
            'Content-Type': 'application/json;charset=UTF-8'
        }
    })
}

// 删除班级
export const deleteClass = (classId) => {
    return request({
        url: '/class/remove',
        method: 'post',
        data: { id: classId },
        headers: {
            'Content-Type': 'application/json;charset=UTF-8'
        }
    });
};

// 导出学生数据
export const exportStudentData = (params) => {
    return request({
        url: '/student/export',
        method: 'get',
        params,
        responseType: 'blob'
    })
}

// 更新学生班级
export const updateStudentClass = (params) => {
    return request({
        url: '/student/update-class',
        method: 'post',
        data: params,
        headers: {
            'Content-Type': 'application/json;charset=UTF-8'
        }
    })
}

//获取班级邀请码
export const getInvitationCodes = (classId) => {
    return request({
        url: '/class/getInvitationCodes',
        method: 'get',
        params: { classId }
    });
}

// 获取班级统计数据
export const getClassStatistics = (classId) => {
  return request({
    url: '/class/statistics',
    method: 'get',
    params: classId ? { classId } : {}
  }).then(response => {
    // 确保这里返回的是整个响应对象
    return response;
  });
};