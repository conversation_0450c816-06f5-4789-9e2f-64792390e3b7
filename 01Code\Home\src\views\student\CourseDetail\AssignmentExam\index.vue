<template>
    <div class="task-exam-page">
        <!-- 顶部切换导航 -->
        <div class="top-tabs">
            <el-tabs v-model="activeTab" @tab-click="handleTabChange" stretch class="custom-tabs">
                <el-tab-pane label="作业" name="assignment" />
                <el-tab-pane label="考试" name="exam" />
            </el-tabs>
        </div>

        <!-- 二级导航栏 -->
        <div class="sub-bar-row">
            <div class="status-btn-group">
                <el-button v-for="item in statusList" :key="item.name" type="text" class="status-btn"
                    :class="{ active: statusFilter === item.name }" @click="onStatusFilterChange(item.name)">
                    {{ item.label }}
                </el-button>
            </div>

            <div class="sub-bar-right">
                <el-input v-model="searchKeyword" placeholder="搜索标题" clearable class="search-input" @clear="onSearchClear"
                    @input="onSearchInput">
                    <template #suffix>
                        <el-icon>
                            <Search />
                        </el-icon>
                    </template>
                </el-input>
            </div>
        </div>

        <!-- 主体内容区域 -->
        <div class="content-wrapper">
            <div class="content-area">
                <el-empty v-if="filteredList.length === 0" description="暂无数据" />

                <el-card v-for="item in filteredList" :key="item.id" class="task-card">
                    <div class="card-header">
                        <div class="left-section">
                            <span class="type-tag">{{ activeTab === 'assignment' ? '作业' : '考试' }}</span>
                            <h3 class="task-title">{{ item.title }}</h3>
                        </div>

                    </div>
                    <div class="right-section">
                        <span class="total-score">总分：{{ item.totalScore || '未知' }} 分</span>
                        <el-button class="action-button" @click.stop="handleActionClick(item)">
                            {{
                                item.submitted
                                ? '查看试卷'
                                : Date.now() < new Date(item.endTime).getTime() ? '开始答题' : '查看试卷' }} </el-button>
                    </div>

                    <div class="task-time-row">
                        <span>开始时间：{{ formatTime(item.startTime) }}</span>
                        <span>截止时间：{{ formatTime(item.endTime) }}</span>
                    </div>
                </el-card>
            </div>

            <!-- 分页条 -->
            <div class="pagination-container">
                <el-pagination background layout="prev, pager, next" :total="totalItems" :page-size="pageSize"
                    :current-page="currentPage" @current-change="handlePageChange" />
            </div>
        </div>
    </div>
</template>
  
<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { fetchAvailableList, checkIsSubmitted } from '@/api/student/exam-assignment/studentExam'

const router = useRouter()
const route = useRoute()
const courseId = route.params.courseId

const activeTab = ref('assignment')
const statusFilter = ref('all')
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(6)
const totalItems = ref(0)
const allList = ref([])

const statusList = [
    { name: 'all', label: '全部' },
    { name: 'uncompleted', label: '未完成' },
    { name: 'completed', label: '已完成' },
]

// 时间格式化
const formatTime = (timestamp) => {
    if (!timestamp) return ''
    const date = new Date(timestamp)
    const yyyy = date.getFullYear()
    const MM = String(date.getMonth() + 1).padStart(2, '0')
    const dd = String(date.getDate()).padStart(2, '0')
    const hh = String(date.getHours()).padStart(2, '0')
    const mm = String(date.getMinutes()).padStart(2, '0')
    return `${yyyy}/${MM}/${dd} ${hh}:${mm}`
}

const fetchList = async () => {
    console.log('当前课程ID:', courseId)
    console.log('当前标签:', activeTab.value)
    console.log('当前页码:', currentPage.value)

    try {
        const isExam = activeTab.value === 'exam'
        const res = await fetchAvailableList({
            courseId,
            page: currentPage.value,
            size: pageSize.value,
            isExam,
        })

        console.log('接口返回数据:', res)

        const records = res.result?.records || []

        const listWithStatus = await Promise.all(
            records.map(async (item) => {
                const submittedRes = await checkIsSubmitted({ id: item.id, isExam })
                return {
                    ...item,
                    submitted: submittedRes.result === true,
                    isPublishScore: item.isPublishScore === true,
                    startTime: item.startTime || item.startTimeStamp || '',
                    endTime: item.endTime || item.endTimeStamp || '',
                }
            })
        )

        allList.value = listWithStatus
        totalItems.value = res.result?.total || 0

        console.log('列表数据:', allList.value)
        console.log('总条数:', totalItems.value)
    } catch (err) {
        ElMessage.error('获取任务列表失败')
        console.error(err)
    }
}

const filteredList = computed(() => {
    let list = [...allList.value]

    if (statusFilter.value === 'completed') {
        list = list.filter((i) => i.submitted)
    } else if (statusFilter.value === 'uncompleted') {
        list = list.filter((i) => !i.submitted)
    }

    const keyword = searchKeyword.value.trim().toLowerCase()
    if (keyword) {
        list = list.filter((i) => i.title.toLowerCase().includes(keyword))
    }

    return list
})

const handleActionClick = (item) => {
    const isExam = activeTab.value === 'exam'
    const now = Date.now()
    const deadline = new Date(item.endTime).getTime()

    const goTo = item.submitted
        ? 'studentTaskDetail'
        : now < deadline
            ? 'doExam'
            : 'studentTaskDetail'

    const finalPath = `/student/course-detail/s-examAndWork/${goTo}`

    console.log('[跳转信息]', {
        id: item.id,
        isExam,
        title: item.title,
        isPublishScore: item.isPublishScore,
        routePath: finalPath
    })

    router.push({
        path: finalPath,
        query: {
            id: item.id,
            isExam: isExam.toString(),
            title: item.title,
            startTime: item.startTime,
            endTime: item.endTime,
            duration: item.durationMin, // 分钟数
            totalScore: item.totalScore,
            isPublishScore: item.isPublishScore?.toString() || 'false'
        }
    })
}


const handlePageChange = (val) => {
    currentPage.value = val
    fetchList()
}

const handleTabChange = (tab) => {
    activeTab.value = tab.props.name
    currentPage.value = 1
    fetchList()
}

const onStatusFilterChange = (val) => {
    statusFilter.value = val
    currentPage.value = 1
    fetchList()
}

const onSearchInput = () => {
    currentPage.value = 1
    fetchList()
}

const onSearchClear = () => {
    currentPage.value = 1
    fetchList()
}

onMounted(() => {
    fetchList()
})

watch([activeTab], () => {
    currentPage.value = 1
    fetchList()
})
</script>
  
<style scoped>
.task-exam-page {
    background-color: white;
    padding: 30px;
    min-height: 90vh;
    display: flex;
    flex-direction: column;
}

/* 分页条样式 */
.content-wrapper {
    display: flex;
    flex-direction: column;
    min-height: 65vh;
}

.content-area {
    flex: 1;
    padding: 0px;
}

.pagination-container {
    display: flex;
    justify-content: flex-end;
    padding: 0px 30px;
}

.top-tabs {
    width: 15%;
    /* 限定宽度20% */
    min-width: 240px;
    margin-bottom: 20px;
    margin-left: 0;
}

.el-tabs__item {
    font-size: 24px;
    font-weight: 600;
    padding: 10px 24px;
}

.sub-bar-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 16px;
    background-color: #f7f7f7db;
    padding: 20px 16px;
    border-radius: 10px;
}

.status-btn-group {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    flex: 1;
    min-width: 300px;
}

.status-btn {
    padding: 6px 20px;
    border-radius: 4px;
    font-weight: 500;
    color: #606266;
}

.status-btn:hover,
.status-btn.active {
    color: #409eff;
    border-color: #a0c4ff;
}


.sub-bar-right {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.search-input {
    width: 200px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.total-score {
    font-size: 14px;
    color: #606266;
}

.left-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.right-section {
    display: flex;
    margin-right: 15px;
    margin-bottom: 15px;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
}

.type-tag {
    background-color: #e0f0ff;
    color: #409eff;
    padding: 4px 10px;
    border-radius: 4px;
    font-size: 12px;
}

.task-title {
    margin-top: 8px;
    padding: 4px 10px;
    font-weight: 600;
    font-size: 18px;
}

.task-time-row {
    display: flex;
    gap: 20px;
    font-size: 14px;
    color: #888;
}

.task-card {
    position: relative;
    margin-bottom: 15px;
    min-height: 140px;
}

.action-button {
    width: 100px;
    height: 30px;
    background-color: #e0f0ff;
    border: 0px solid;
    color: #409eff;
    border-radius: 15px;
    transition: transform 0.2s ease;
}

.action-button:active {
    transform: scale(0.95);
}
</style>
  