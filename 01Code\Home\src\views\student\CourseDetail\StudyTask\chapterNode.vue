<!--src\views\student\CourseDetail\StudyTask\chapterNode.vue-->
<template>
  <div :id="anchorId" :class="['chapter-node', depth > 0 ? 'nested' : '']">
    <div 
      class="node-header"
      :class="{ 'top-level': depth === 0 }"
      @click="toggleExpanded"
    >
      <div>
        <span v-if="depth > 0" class="progress-pie" :style="progressStyle">
    <svg viewBox="0 0 36 36" class="pie-chart">
      <!-- 背景圆（始终全显示） -->
      <circle cx="18" cy="18" r="16" class="pie-bg" />
      
      <!-- 进度圆（通过clipPath裁剪显示部分） -->
      <g :clip-path="`url(#clip-${node.id})`">
        <circle cx="18" cy="18" r="16" class="pie-fill" />
      </g>
      
      <!-- 动态裁剪路径 -->
      <defs>
        <clipPath :id="`clip-${node.id}`">
          <path :d="getPiePath(progress)" />
        </clipPath>
      </defs>
    </svg>
  </span>
        <span class="chapter-number">
            {{ formattedChapterNumber }}
        </span>
        <span 
            class="title-text"
            :class="{
            'highlighted': isHighlighted,
            'large-text': depth === 0
            }"
        >
            {{ node.title }}
        </span>
      </div>
      <div>
        <span v-if="isExpandable" class="expand-icon" :class="{ 'low-opacity': !hasChildren && !hasVideos }">
          <svg v-if="isExpanded" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="6 9 12 15 18 9"></polyline>
          </svg>
          <svg v-else xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="9 18 15 12 9 6"></polyline>
          </svg>
        </span>
        <span v-else class="expand-placeholder"></span>
      </div>
    </div>
    
    <div v-if="isExpanded && hasChildren" class="children-container">
      <chapter-node
        v-for="(child, index) in node.children"
        :key="index"
        :node="child"
        :depth="depth + 1"
        :search-query="searchQuery"
        :course-id="courseId"
        @preview-resource="$emit('preview-resource', $event)"
        @video-clicked="$emit('video-clicked', $event)"
      />
    </div>
    
      <div v-if="isExpanded && videoResources.length > 0" class="resource-list">
    <div 
      v-for="(resource, index) in videoResources" 
      :key="resource.id"
      class="resource-item"
      :class="{ 
        'watched': resource.progress === 100,
        'in-progress': resource.progress > 0 && resource.progress < 100
      }"
    >
      <div class="resource-content">
        <span class="resource-type-tag" 
              :class="{
                'video-watched': resource.progress === 100,
                'video-in-progress': resource.progress > 0 && resource.progress < 100,
                'video-not-started': resource.progress === 0
              }"
              :title="`视频进度: ${resource.progress}%`">
          视频
        </span>
        <i class="el-icon-video-camera resource-icon"></i>
        <span 
          class="resource-title"
          @click="handleVideoClick(resource)"
        >
          {{ resource.videoTitle }}
        </span>
        <span class="video-duration">
          {{ formatDuration(resource.videoDuration) }}
        </span>
        <!-- <span class="video-progress" v-if="resource.progress > 0">
          {{ resource.progress }}%
        </span> -->
      </div>
    </div>
  </div>
  </div>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits, watch } from 'vue'
import { getVideoListByChapter, getVideoProgress  } from '@/api/student/course'

const props = defineProps({
  anchorId: String,
  node: {
    type: Object,
    required: true,
    validator: (node) => !!node?.id && !!node?.title
  },
  depth: { 
    type: Number, 
    default: 0 
  },
  searchQuery: String,
  courseId: {
    type: String,
    required: true,
    validator: (id) => {
      if (!id) {
        console.error('courseId 是必填参数')
        return false
      }
      return true
    }
  }
})

const emit = defineEmits(['preview-resource', 'video-clicked'])

const isExpanded = ref(false)
const videoResources = ref([])
const loading = ref(false)
const chapterProgress = ref(0)

const hasChildren = computed(() => props.node.children?.length > 0)
const hasVideos = computed(() => videoResources.value.length > 0)
const isExpandable = computed(() => hasChildren.value || props.node.id)
const isHighlighted = computed(() => 
  props.searchQuery && props.node.title.toLowerCase().includes(props.searchQuery.toLowerCase())
)
const formattedChapterNumber = computed(() => 
  props.depth === 0 ? `第${props.node.chapterNumber}章` : `${props.node.chapterNumber}.${props.node.sortOrder}`
)

const formatDuration = (seconds) => {
  if (!seconds) return '0:00'
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins}:${secs < 10 ? '0' : ''}${secs}`
}

const progress = computed(() => {
  return chapterProgress.value || 0
})
const getPiePath = (percent) => {
  if (percent >= 100) return ''
  const angle = (percent / 100) * 360
  const radians = (angle - 90) * Math.PI / 180
  const x = 18 + 16 * Math.cos(radians)
  const y = 18 + 16 * Math.sin(radians)
  const largeArcFlag = angle <= 180 ? 0 : 1
  
  return `M18,18 L18,2 A16,16 0 ${largeArcFlag},1 ${x},${y} Z`
}
const progressStyle = computed(() => {
  return {
    '--progress-color': progress.value === 100 ? '#4CAF50' : progress.value > 0 ? '#FFC107' : '#999'
  }
})

const toggleExpanded = async () => {
  if (!isExpandable.value) return
  
  if (!props.courseId) {
    console.error('无法展开: courseId 未定义', {
      component: 'ChapterNode',
      receivedProps: props
    })
    return
  }

  isExpanded.value = !isExpanded.value
  if (isExpanded.value && videoResources.value.length === 0) {
    await fetchVideoResources()
  }
}
const fetchVideoProgress = async (videoId) => {
  try {
    const res = await getVideoProgress(videoId)
    if (res.code === 200) {
      return res.result?.progress || 0
    }
    return 0
  } catch (error) {
    console.error('获取视频进度失败:', error)
    return 0
  }
}
const fetchVideoResources = async () => {
  if (!props.node.id) return;

  loading.value = true;
  try {
    // 1. 获取章节下的视频列表
    const videoRes = await getVideoListByChapter({ chapterId: props.node.id })
    
    if (videoRes.code === 200) {
      const videos = videoRes.result || []
      
      // 2. 为每个视频获取进度
      const videosWithProgress = await Promise.all(videos.map(async video => {
        const progress = await fetchVideoProgress(video.id)
        return {
          ...video,
          progress,
          isWatchCompleted: progress === 100 ? 1 : 0
        }
      }))

      videoResources.value = videosWithProgress

      // 3. 计算章节总进度
      const totalProgress = videosWithProgress.reduce((sum, video) => sum + video.progress, 0)
      const maxPossibleProgress = videosWithProgress.length * 100
      chapterProgress.value = maxPossibleProgress > 0 
        ? Math.round((totalProgress / maxPossibleProgress) * 100)
        : 0
    }
  } catch (error) {
    console.error('获取视频资源失败:', error)
  } finally {
    loading.value = false
  }
}
const calculateProgress = () => {
  if (videoResources.value.length === 0) return 0
  const completedCount = videoResources.value.filter(
    video => video.isWatchCompleted === 1
  ).length
  return Math.round((completedCount / videoResources.value.length) * 100)
}


// 辅助函数：仅保留原始标题（不做任何处理）
const normalizeTitle = (title) => title;
const handleVideoClick = (resource) => {
  emit('video-clicked', {
    id: resource.id,
    videoTitle: resource.videoTitle,
    videoUrl: resource.videoUrl,
    duration: resource.videoDuration,
    progress: resource.progress
  })
  emit('preview-resource', {
    id: resource.id,
    name: resource.videoTitle,
    type: 1,
    url: resource.videoUrl,
    duration: resource.videoDuration
  })
}

watch(() => props.courseId, (newVal) => {
  if (newVal && isExpanded.value) {
    fetchVideoResources()
  }
})
</script>

<style scoped>
.node-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background-color 200ms;
  margin-top: 10px;
}

.node-header > div {
  display: flex;
  align-items: center;
}

.node-header:hover {
  background-color: #f9fafb;
}

.node-header.top-level {
  background-color: #f5f5ff;
  padding: 15px 0 15px 15px;
  border-radius: 0.5rem;
}

.node-header.top-level:hover {
  background-color: #f1f1fd;
}

.progress-circle {
  margin-right: 0.5rem;
  width: 16px;
  height: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.circular-chart {
  display: block;
  width: 100%;
  height: 100%;
}

.circle-bg {
  fill: none;
  stroke: #eee;
  stroke-width: 2;
}

.circle-fill {
  fill: none;
  stroke: var(--progress-color);
  stroke-width: 2;
  stroke-linecap: round;
  animation: progress 1s ease-out forwards;
}

.children-container {
  margin-left: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.resource-list {
  margin-top: 5px;
  margin-left: 10px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.resource-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 10px;
  border-radius: 0.375rem;
  transition: all 200ms;
}

.resource-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.resource-type-tag {
  margin-right: 0.75rem;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-size: 0.85rem;
  font-weight: normal;
  color: #9e9e9e;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
}

.resource-type-tag.video-watched {
  color: #4CAF50;
  border-color: #4CAF50;
  background-color: #e8f5e9;
}
.resource-type-tag.video-in-progress {
  color: #FFC107;
  border-color: #FFC107;
  background-color: #fff8e1;
}
.resource-type-tag.video-not-started {
  color: #9e9e9e;
  border-color: #e0e0e0;
  background-color: #f5f5f5;
}
.resource-icon {
  margin-right: 8px;
  color: #9e9e9e;
}

.resource-title {
  cursor: pointer;
  flex-grow: 1;
}

.resource-title:hover {
  color: var(--primary-color);
}
.video-progress {
  margin-left: 8px;
  font-size: 0.85rem;
  color: #666;
}

.resource-item.watched .resource-icon,
.resource-item.watched .resource-title {
  color: #4CAF50;
}

.resource-item.in-progress .resource-icon,
.resource-item.in-progress .resource-title {
  color: #FFC107;
}
.progress-pie {
  margin-right: 0.5rem;
  width: 20px;
  height: 20px;
  display: inline-flex;
}

.pie-chart {
  width: 100%;
  height: 100%;
}

.pie-bg {
  fill: #eee; /* 背景色 */
}

.pie-fill {
  fill: var(--progress-color); /* 进度色 */
}

.video-duration {
  color: #6b7280;
  font-size: 0.85rem;
  margin-left: 8px;
}

.resource-item:hover {
  background-color: #f9fafb;
}

.resource-item.watched .resource-icon {
  color: #4CAF50;
}

.resource-item.watched .resource-title {
  color: #4CAF50;
}

@keyframes progress {
  0% {
    stroke-dasharray: 0, 100;
  }
}
</style>