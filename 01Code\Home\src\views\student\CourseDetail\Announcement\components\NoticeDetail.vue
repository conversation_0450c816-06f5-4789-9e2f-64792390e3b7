<template>
  <div v-if="visible" class="notice-detail-overlay" @click.self="closeDetail">
    <div class="notice-detail">
      <div class="detail-header">
        <h2 class="detail-title">{{ currentNotice.title }}</h2>
        <button class="close-btn" @click="closeDetail">&times;</button>
      </div>
      
      <div class="detail-meta">
        <span class="notice-type">{{ getNoticeTypeLabel(currentNotice.noticeType) }}</span>
        <span class="publish-time">{{ formatTime(currentNotice.publishTime) }}</span>
        <span v-if="currentNotice.readCount" class="read-count">阅读 {{ currentNotice.readCount }}</span>
      </div>
      
      <div class="detail-content">
        <div v-html="currentNotice.content" class="content-body"></div>
      </div>
      
      <!-- 附件资源 -->
      <div v-if="currentNotice.resources && currentNotice.resources.length > 0" class="detail-resources">
        <h4>附件资源</h4>
        <div class="resource-list">
          <div 
            v-for="resource in currentNotice.resources" 
            :key="resource.id"
            class="resource-item"
            @click="downloadResource(resource)"
          >
            <div class="resource-icon">
              <span>{{ getFileIcon(resource.fileExtension) }}</span>
            </div>
            <div class="resource-info">
              <div class="resource-name">{{ resource.originalName }}</div>
              <div class="resource-meta">
                <span class="file-size">{{ formatFileSize(resource.fileSize) }}</span>
                <span class="file-type">{{ resource.fileType }}</span>
              </div>
            </div>
            <div class="download-btn">
              <span>下载</span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="detail-footer">
        <button @click="closeDetail" class="back-btn">返回列表</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useStudentNoticeStore } from '@/stores/student/noticeStore'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close'])

const noticeStore = useStudentNoticeStore()

// 当前公告
const currentNotice = computed(() => noticeStore.selectedNotice || {})

// 获取公告类型标签
const getNoticeTypeLabel = (typeValue) => {
  return noticeStore.NOTICE_TYPES[noticeStore.NOTICE_TYPE_MAP[typeValue]]?.label || '公告'
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN')
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 获取文件图标
const getFileIcon = (extension) => {
  const iconMap = {
    pdf: '📄',
    doc: '📝',
    docx: '📝',
    xls: '📊',
    xlsx: '📊',
    ppt: '📽️',
    pptx: '📽️',
    txt: '📄',
    zip: '📦',
    rar: '📦',
    jpg: '🖼️',
    jpeg: '🖼️',
    png: '🖼️',
    gif: '🖼️',
    mp4: '🎥',
    mp3: '🎵',
    default: '📎'
  }
  return iconMap[extension?.toLowerCase()] || iconMap.default
}

// 下载资源
const downloadResource = (resource) => {
  if (resource.url) {
    window.open(resource.url, '_blank')
  }
}

// 关闭详情
const closeDetail = () => {
  noticeStore.clearSelectedNotice()
  emit('close')
}
</script>

<style lang="scss" scoped>
@use '@/styles/variables';

.notice-detail-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.notice-detail {
  background-color: #fff;
  border-radius: 8px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);

  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 24px 24px 16px;
    border-bottom: 1px solid #eee;

    .detail-title {
      font-size: 20px;
      font-weight: 600;
      color: #333;
      margin: 0;
      flex: 1;
      margin-right: 16px;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 24px;
      color: #999;
      cursor: pointer;
      padding: 0;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;

      &:hover {
        background-color: #f5f5f5;
        color: #666;
      }
    }
  }

  .detail-meta {
    display: flex;
    gap: 16px;
    align-items: center;
    padding: 16px 24px;
    background-color: #f9f9f9;
    font-size: 14px;

    .notice-type {
      background-color: $primary-color;
      color: white;
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
    }

    .publish-time, .read-count {
      color: #666;
    }
  }

  .detail-content {
    padding: 24px;

    .content-body {
      line-height: 1.8;
      color: #333;
      font-size: 15px;

      // 处理富文本内容样式
      :deep(p) {
        margin-bottom: 16px;
      }

      :deep(img) {
        max-width: 100%;
        height: auto;
        border-radius: 4px;
        margin: 16px 0;
      }

      :deep(ul), :deep(ol) {
        margin: 16px 0;
        padding-left: 24px;
      }

      :deep(blockquote) {
        border-left: 4px solid $primary-color;
        padding-left: 16px;
        margin: 16px 0;
        color: #666;
        background-color: #f9f9f9;
        padding: 16px;
        border-radius: 4px;
      }
    }
  }

  .detail-resources {
    padding: 0 24px 24px;
    border-top: 1px solid #eee;
    margin-top: 24px;

    h4 {
      margin: 16px 0;
      color: #333;
      font-size: 16px;
    }

    .resource-list {
      .resource-item {
        display: flex;
        align-items: center;
        padding: 12px;
        border: 1px solid #eee;
        border-radius: 6px;
        margin-bottom: 8px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          background-color: #f5f5f5;
          border-color: $primary-color;
        }

        .resource-icon {
          font-size: 24px;
          margin-right: 12px;
        }

        .resource-info {
          flex: 1;

          .resource-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
          }

          .resource-meta {
            font-size: 12px;
            color: #999;
            display: flex;
            gap: 12px;
          }
        }

        .download-btn {
          color: $primary-color;
          font-size: 14px;
          font-weight: 500;
        }
      }
    }
  }

  .detail-footer {
    padding: 16px 24px;
    border-top: 1px solid #eee;
    text-align: center;

    .back-btn {
      padding: 10px 24px;
      background-color: $primary-color;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s;

      &:hover {
        background-color: darken($primary-color, 10%);
      }
    }
  }
}
</style>
