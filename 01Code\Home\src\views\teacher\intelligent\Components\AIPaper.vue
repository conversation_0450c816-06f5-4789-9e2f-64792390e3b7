<template>
  <div class="paper-container">
    <!-- 文件上传和试卷设置 -->
    <div class="upload-settings" v-if="!generatedPaper">
      <h2>试卷生成</h2>
      <p>请上传课程相关文档，我将为您生成试卷</p>

      <!-- 试卷类型和网络题库选项 -->
      <div class="type-external-row">
        <!-- 试卷类型选择 -->
        <div class="paper-type-selector">
          <label for="paper-type">试卷类型:</label>
          <select id="paper-type" v-model="paperType">
            <option value="quiz">课堂测验</option>
            <option value="midterm">期中考试</option>
            <option value="final">期末考试</option>
          </select>
        </div>

        <!-- 网络题库选项 -->
        <div class="external-option">
          <input type="checkbox" id="search-external" v-model="searchExternal">
          <label for="search-external">结合网络题库出题</label>
        </div>
      </div>

      <!-- 题型数量设置 - 水平排列 -->
      <div class="question-type-selector">
        <div class="type-input">
          <label for="mcq-count">选择题数量:</label>
          <input type="number" id="mcq-count" v-model.number="mcqCount" min="0" max="20">
        </div>
        <div class="type-input">
          <label for="fill-count">填空题数量:</label>
          <input type="number" id="fill-count" v-model.number="fillCount" min="0" max="20">
        </div>
        <div class="type-input">
          <label for="essay-count">简答题数量:</label>
          <input type="number" id="essay-count" v-model.number="essayCount" min="0" max="10">
        </div>
      </div>

      <!-- 文件上传区域 -->
      <div class="upload-area" @dragover.prevent @drop.prevent @drop="handleDrop">
        <input type="file" @change="handleFileChange" accept=".doc,.docx" ref="fileInput" style="display: none" />
        <div @click="triggerFileUpload">
          <p>拖放文件到此处或点击选择文件</p>
          <p class="file-name" v-if="fileName">{{ fileName }}</p>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loader"></div>
      <p>正在生成试卷...</p>
    </div>

    <!-- 错误提示 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>

    <!-- 试卷生成结果 -->
    <div class="content-wrapper" v-if="generatedPaper">
      <div class="side-panel">
        <div class="panel-header">
          <h3>生成的试卷</h3>
<!--          <button @click="togglePanel">×</button>-->
        </div>
        <div class="panel-content">
          <pre>{{ formattedPaper }}</pre>
          <div class="actions">
            <button @click="copyPaper">复制试卷</button>
            <button @click="downloadPaper">下载试卷</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { generatePaper } from '../../../../api/teacher/intelligent/paper';
import * as mammoth from 'mammoth';

export default {
  data() {
    return {
      fileName: '',
      file: null,
      generatedPaper: null,
      isLoading: false,
      errorMessage: '',
      mcqCount: 1,
      fillCount: 1,
      essayCount: 1,
      paperType: 'quiz',
      searchExternal: false,
      showSidePanel: false
    };
  },
  computed: {
    formattedPaper() {
      if (!this.generatedPaper) return '';
      return this.generatedPaper;
    },
    questionCounts() {
      return {
        mcqCount: this.mcqCount,
        fillCount: this.fillCount,
        essayCount: this.essayCount
      };
    }
  },
  methods: {
    triggerFileUpload() {
      this.$refs.fileInput.click();
    },
    handleFileChange(event) {
      if (event.target.files.length > 0) {
        const file = event.target.files[0];
        // 检查文件类型
        if (!this.isValidFileType(file)) {
          this.errorMessage = '只支持 .docx 文件';
          return;
        }
        this.file = file;
        this.fileName = file.name;
        this.processDocument();
      }
    },
    handleDrop(event) {
      if (event.dataTransfer.files.length > 0) {
        const file = event.dataTransfer.files[0];
        // 检查文件类型
        if (!this.isValidFileType(file)) {
          this.errorMessage = '只支持 .docx 文件';
          return;
        }
        this.file = file;
        this.fileName = file.name;
        this.processDocument();
      }
    },
    isValidFileType(file) {
      // 只允许.docx文件
      return file.name.endsWith('.docx');
    },
    async processDocument() {
      if (!this.file) return;

      this.isLoading = true;
      this.errorMessage = '';
      this.generatedPaper = null;

      try {
        // 读取文件内容
        const fileContent = await this.readFileAsText(this.file);

        // 生成试卷
        this.generatedPaper = await generatePaper(
          fileContent,
          this.paperType,
          this.questionCounts,
          this.searchExternal
        );

        this.showSidePanel = true;
      } catch (error) {
        this.errorMessage = error.message || '试卷生成失败，请重试';
        console.error('处理文档时出错:', error);
      } finally {
        this.isLoading = false;
      }
    },
    readFileAsText(file) {
      return new Promise((resolve, reject) => {
        // 只处理.docx文件
        if (!file.name.endsWith('.docx')) {
          reject(new Error('不支持的文件格式'));
          return;
        }

        const reader = new FileReader();
        reader.onload = async (event) => {
          try {
            // 使用mammoth解析docx内容
            const result = await mammoth.extractRawText({
              arrayBuffer: event.target.result
            });
            resolve(result.value); // 返回解析后的纯文本
          } catch (error) {
            reject(error);
          }
        };
        reader.onerror = reject;
        reader.readAsArrayBuffer(file); // 读取为ArrayBuffer
      });
    },
    togglePanel() {
      this.showSidePanel = !this.showSidePanel;
    },
    copyPaper() {
      if (!this.formattedPaper) return;

      navigator.clipboard.writeText(this.formattedPaper)
        .then(() => alert('试卷已复制到剪贴板'))
        .catch(err => {
          console.error('复制失败:', err);
          this.errorMessage = '复制失败，请手动复制内容';
        });
    },
    downloadPaper() {
      if (!this.formattedPaper) return;

      const blob = new Blob([this.formattedPaper], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');

      let fileName = '试卷';
      if (this.paperType === 'quiz') fileName = '课堂测验';
      else if (this.paperType === 'midterm') fileName = '期中考试';
      else if (this.paperType === 'final') fileName = '期末考试';

      a.href = url;
      a.download = `${fileName}-${Date.now()}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      setTimeout(() => URL.revokeObjectURL(url), 100);
    }
  }
};
</script>

<style scoped>
.paper-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.upload-settings {
  margin-bottom: 20px;
  text-align: center;
}

/* 新增：试卷类型和网络题库在同一行 */
.type-external-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px 0;
}

.paper-type-selector {
  flex: 1;
  text-align: left;
  margin-left: 250px;
}

.external-option {
  flex: 1;
  text-align: right;
  margin-right: 250px;
}

/* 题型数量设置 - 水平排列 */
.question-type-selector {
  display: flex;
  justify-content: space-between;
  margin: 20px 0;
  gap: 15px;
}

.type-input {
  flex: 1;
  /*display: flex;*/
  /*flex-direction: column;*/
  align-items: center;
}

.type-input label {
  margin-bottom: 5px;
}

.upload-area {
  border: 2px dashed #ccc;
  padding: 30px;
  border-radius: 8px;
  cursor: pointer;
  transition: border-color 0.3s;
  margin: 20px 0;
}

.upload-area:hover {
  border-color: #956ef6;
}

.file-name {
  margin-top: 10px;
  color: #666;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loader {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #956ef6;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  padding: 10px;
  background-color: #ffebee;
  color: #c62828;
  border-radius: 4px;
  margin-bottom: 15px;
}

.content-wrapper {
  margin-top: 20px;
}

.side-panel {
  position: relative;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  border-bottom: 1px solid #eee;
  background: #f9f9f9;
}

.panel-content {
  padding: 15px;
  max-height: 400px;
  overflow-y: auto;
}

.actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.actions button {
  padding: 8px 15px;
  background-color: #956ef6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
</style>
