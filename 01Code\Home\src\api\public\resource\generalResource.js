import api from '@/api/service.js'

/**
 * 获取通用资源分类列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页码，默认1
 * @param {number} params.pageSize - 每页数量，默认10
 * @param {string} params.name - 分类名称（可选）
 * @returns {Promise} API响应
 */
export const getGeneralResourceCategoryList = (params = {}) => {
  // 默认参数
  const defaultParams = {
    pageNum: 1,
    pageSize: 10
  };
  
  // 过滤掉空值参数并合并默认参数
  const filteredParams = Object.fromEntries(
    Object.entries({
      ...defaultParams,
      ...params
    }).filter(([_, v]) => v !== '' && v !== undefined && v !== null)
  );
  
  return api({
    url: '/general/resource/category/list',
    method: 'get',
    params: filteredParams
  });
}

/**
 * 获取课程资料列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页码，默认1
 * @param {number} params.pageSize - 每页数量，默认10
 * @param {string} params.name - 资料名称（可选）
 * @param {string} params.categoryId - 分类ID（可选）
 * @returns {Promise} API响应
 */
export const getGeneralResourceProblemList = (params = {}) => {
  // 默认参数
  const defaultParams = {
    pageNum: 1,
    pageSize: 10
  };
  
  // 过滤掉空值参数并合并默认参数
  const filteredParams = Object.fromEntries(
    Object.entries({
      ...defaultParams,
      ...params
    }).filter(([_, v]) => v !== '' && v !== undefined && v !== null)
  );
  
  return api({
    url: '/general/resource/problem/list',
    method: 'get',
    params: filteredParams
  });
}

/**
 * 获取虚拟仿真软件资源列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页码，默认1
 * @param {number} params.pageSize - 每页数量，默认10
 * @param {string} params.name - 软件名称（可选）
 * @param {string} params.categoryId - 分类ID（可选）
 * @returns {Promise} API响应
 */
export const getGeneralResourceSoftwareList = (params = {}) => {
  // 默认参数
  const defaultParams = {
    pageNum: 1,
    pageSize: 10
  };
  
  // 过滤掉空值参数并合并默认参数
  const filteredParams = Object.fromEntries(
    Object.entries({
      ...defaultParams,
      ...params
    }).filter(([_, v]) => v !== '' && v !== undefined && v !== null)
  );
  
  return api({
    url: '/general/resource/software/list',
    method: 'get',
    params: filteredParams
  });
}
