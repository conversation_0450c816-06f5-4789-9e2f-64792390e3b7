<template>
    <div class="resource-association">
      <div class="resources-list">
        <ResourceList :items="resourceTree" v-model:selected="selectedIds" />
      </div>
      <ResourceFooter
        :selectedCount="selectedIds.length"
        @confirm="handleConfirm"
      />
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import ResourceList from './components/ResourceList.vue'
  import ResourceFooter from './components/ResourceFooter.vue'
  import { useKnowledgeGraphStore } from '@/stores/teacher/graphManager/knowledgeGraphStore'

  
  const router = useRouter()
  const route = useRoute()
  const store = useKnowledgeGraphStore()
  
  const nodeId = route.params.nodeId
  
  const resourceTree = ref([])
  
  const selectedIds = ref([])
  
const handleConfirm = async () => {
  const success = await store.associateResourcesWithNode(nodeId, selectedIds.value)
  if (success) {
    alert(`关联成功：${selectedIds.value.join(', ')}`)
    router.go(-1)
  } else {
    alert('关联失败，请重试')
  }
}
onMounted(async () => {
  console.log('当前节点ID:', nodeId)
  
  // 优先从路由参数获取，如果没有则从store获取
  let courseId = route.params.courseId
  if (!courseId) {
    courseId = store.courseId
  }
  
  console.log('课程ID:', courseId)
  
  if (!courseId) {
    console.error('缺少课程ID，无法获取资源树')
    return
  }
  
  resourceTree.value = await store.fetchResourceTree(courseId)

  // 获取节点详情并设置已选中的资源
  const nodeDetail = await store.fetchNodeDetail(nodeId);
  console.log('获取到的节点详情:', nodeDetail);

  if (nodeDetail && nodeDetail.resources) {
    const ids = nodeDetail.resources.map(r => r.resourceId);
    console.log('已关联的资源ID列表:', ids);
    selectedIds.value = ids;
  } else {
    console.log('节点详情中没有找到 resources 字段或详情为空');
  }
})
  </script>
  
  <style lang="scss" scoped>
  .resource-association {
    display: flex;
    flex-direction: column;
    height: 100vh;
  
    .resources-list {
      flex: 1;
      overflow-y: auto;
      padding: 40px 80px 80px 80px;
      background: #fff;
      border-top: 1px solid #eee;
    }
  }
  </style>
  
