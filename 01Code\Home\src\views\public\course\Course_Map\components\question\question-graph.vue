<!-- src\views\public\course\Course_Map\components\question\question-graph.vue -->
<template>
  <!-- 问题图谱容器 -->
  <div class="question-graph-container">
    <!-- 图谱头部 -->
    <div class="graph-header">
      <h3>{{ courseTitle }} - 问题图谱</h3>
      <!-- 标签列表 -->
      <!-- <div class="tab-list-box">
        <div
          class="tab-list"
          :class="{ active: activeTab === 'tree' }"
          @click="switchTab('tree')"
        >
          树图
        </div>
        <div
          class="tab-list"
          :class="{ active: activeTab === 'ring' }"
          @click="switchTab('ring')"
        >
          环图
        </div>
      </div> -->
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>加载问题图谱数据中...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <p>{{ error }}</p>
      <button @click="loadData" class="retry-btn">重试</button>
    </div>

    <!-- 图谱包装器 -->
    <div v-else class="graph-wrapper">
      <!-- 图谱内容 -->
      <div class="graph-content">
        <!-- 标签摘要包装器，仅在树图显示 -->
        <!-- <div class="tag-summary-wrapper" v-if="activeTab === 'tree'">
          <TagSummary
            :data="treeData"
            :tagCounts="tagCounts"
            @tag-click="handleTagClick"
          />
        </div> -->

        <!-- 树图 -->
        <TreeGraph
          v-if="activeTab === 'tree'"
          ref="treeGraph"
          :data="treeData"
          @node-click="handleNodeClick"
          class="tree-graph-container"
        />
        <!-- 环图 -->
        <!-- <RingGraph
          v-else-if="activeTab === 'ring' && ringGraphRendered"
          :graph-id="getGraphId()"
          @node-click="handleNodeClick"
        /> -->
      </div>
    </div>

    <!-- 右侧知识点弹窗 -->
    <questionPointDrawer
      :isOpen="drawerOpen"
      :nodeData="selectedNode"
      ref="drawerRef"
      @close="drawerOpen = false"
    />

    <!-- 弹窗侧边书签栏 -->
    <BookmarkBar :tabs="tabs" :isOpen="drawerOpen" @jump="handleBookmarkJump" />
  </div>
</template>

<script setup>
import { ref, nextTick, onMounted } from "vue";
import { useRoute } from "vue-router";
import TreeGraph from "./TreeGraph.vue";
import RingGraph from "../RingGraph.vue";
//import KnowledgePointDrawer from "../rightPopup/KnowledgePointDrawer.vue";
import questionPointDrawer from "./rightPopup/QuestionPointDrawer.vue";
import BookmarkBar from "./rightPopup/BookmarkBar.vue";
import TagSummary from "../TagSummary.vue";
import {
  getTreeView,
  //getTreeViewFromquestion,
  //getAllTags,
  //getTagCount,
} from "@/api/public/course/courseMap/questionGraph.js";

// 定义组件接收的属性
const props = defineProps({
  courseTitle: {
    type: String,
    default: "课程名称",
  },
  graphId: {
    type: String,
    default: null,
  },
});

// 获取路由信息
const route = useRoute();

// 当前激活的标签
const activeTab = ref("tree");
// 抽屉是否打开
const drawerOpen = ref(false);
// 选中的节点数据
const selectedNode = ref({});
// 抽屉引用
const drawerRef = ref(null);
// 树图引用
const treeGraph = ref(null);
// 环形图是否已渲染
const ringGraphRendered = ref(false);

// 数据状态
const loading = ref(true);
const error = ref(null);
const treeData = ref(null);
const allTags = ref([]);
const tagCounts = ref({});

// 书签栏标签
const tabs = [
  { key: "desc", label: "问题描述" },
  { key: "resource", label: "问题答案" },
  { key: "attr", label: "关联资源" },
];

// 获取图谱ID（优先使用props传入的，其次使用路由参数，最后使用默认值）
const getGraphId = () => {
  return props.graphId || route.params.graphId || "1389533344026333184"; // 默认图谱ID
};

// 创建标签ID到标签名称的映射
const createTagIdToNameMap = (tags) => {
  const map = {};
  if (tags && Array.isArray(tags)) {
    tags.forEach((tag) => {
      map[tag.tagId] = tag.tagName;
    });
  }
  return map;
};

// 转换标签统计数据，将tagId映射为tagName
const transformTagCounts = (counts, tagIdToNameMap) => {
  const transformed = {};
  Object.entries(counts).forEach(([tagId, count]) => {
    const tagName = tagIdToNameMap[tagId] || tagId;
    transformed[tagName] = count;
  });
  return transformed;
};

// 转换后端数据为前端需要的格式
const transformTreeData = (nodes) => {
  if (!nodes || !Array.isArray(nodes)) return null;

  const transformNode = (node) => {
    //console.log(node);
    // console.log(node.nodeId);
    // console.log(node.nodeName);
    // console.log(node.level);
    return {
      id: node.questionId,
      label: node.nodeName,
      //tags: node.tags ? node.tags.map((tag) => tag.tagName) : [],
      children: Array.isArray(node.children)
        ? node.children.map(transformNode)
        : [],
      // collapsed: node.level >= 1, // 默认折叠深层节点
    };
  };

  // 如果只有一个根节点，直接返回
  if (nodes.length === 1) {
    return transformNode(nodes[0]);
  }

  // 如果有多个节点，创建一个虚拟根节点
  return {
    id: "root",
    label: props.courseTitle,
    tags: [],
    children: nodes.map(transformNode),
    collapsed: false,
  };
};
// const transformTreeData = (apiResponse, courseTitle) => {
//   // 1. 验证API响应有效性
//   if (!apiResponse || apiResponse.code !== 200 || !Array.isArray(apiResponse.result)) {
//     return null;
//   }

//   // 2. 提取有效节点数据
//   const nodes = apiResponse.result;
//   // 输出节点数据
//   console.log(nodes);
//   // 3. 节点转换函数（适配后端字段名）
//   const transformNode = (node) => {
//     return {
//       id: node.questionId,   // 后端使用 questionId 作为节点ID
//       label: node.nodeName,  // 节点名称字段不变
//       children: Array.isArray(node.children) 
//         ? node.children.map(transformNode) 
//         : [],
//       collapsed: node.level >= 1, // 层级≥1的节点默认折叠
      
//       // 可选：保留原始数据中的其他字段
//       meta: {
//         graphId: node.graphId,
//         level: node.level,
//         parentId: node.parentId,
//         sortOrder: node.sortOrder
//       }
//     };
//   };

//   // 4. 处理根节点逻辑
//   if (nodes.length === 1) {
//     // 单根节点：直接转换第一个节点
//     return transformNode(nodes[0]);
//   } else if (nodes.length > 1) {
//     // 多根节点：创建虚拟根节点
//     return {
//       id: "virtual-root",
//       label: courseTitle || "课程知识图谱", // 使用传入的课程标题
//       children: nodes.map(transformNode),
//       collapsed: false, // 虚拟根节点默认展开
//       isVirtualRoot: true // 标记为虚拟根节点
//     };
//   }
  
//   // 5. 无有效节点时返回null
//   return null;
// };

// 加载数据
const loadData = async () => {
  try {
    loading.value = true;
    error.value = null;

    const graphId = getGraphId();

    // 并行加载数据
    //const [treeResponse, tagsResponse, countResponse] = await Promise.all([
    const [treeResponse] = await Promise.all([ 
      getTreeView(graphId),
      //getTreeViewFromquestion(graphId),
      //getAllTags(),
      //getTagCount(graphId),
    ]);

    // 检查响应状态
    if (treeResponse.code === 200) {
      treeData.value = transformTreeData(treeResponse.result);
    } else {
      throw new Error(treeResponse.msg || "获取树形图数据失败");
    }

    // if (tagsResponse.code === 200) {
    //   allTags.value = tagsResponse.result;
    // } else {
    //   throw new Error(tagsResponse.msg || "获取标签列表失败");
    // }

    // if (countResponse.code === 200) {
    //   // 创建标签ID到名称的映射
    //   const tagIdToNameMap = createTagIdToNameMap(allTags.value);
    //   // 转换标签统计数据
    //   tagCounts.value = transformTagCounts(
    //     countResponse.result,
    //     tagIdToNameMap
    //   );
    // } else {
    //   throw new Error(countResponse.msg || "获取标签统计失败");
    // }
  } catch (err) {
    //console.error("加问题图谱数据失败:", err);
    error.value = err.message || "加载数据失败，请稍后重试";
  } finally {
    loading.value = false;
  }
};

// 切换标签
// const switchTab = async (tab) => {
//   activeTab.value = tab;

//   // 如果切换到环形图，延迟渲染
//   if (tab === "ring") {
//     ringGraphRendered.value = false;
//     await nextTick();
//     await new Promise((resolve) => setTimeout(resolve, 100));
//     ringGraphRendered.value = true;
//   }
// };

// 处理节点点击事件
const handleNodeClick = (node) => {
  
  if (node.level === 2)
  {
    selectedNode.value = node;
    drawerOpen.value = true;
  }
  else
  {
    drawerOpen.value = false;
  }
};

// 处理书签跳转事件
const handleBookmarkJump = (key) => {
  if (!drawerOpen.value) {
    drawerOpen.value = true;
  }
  nextTick(() => {
    drawerRef.value.scrollTo(key);
  });
};

// 处理标签点击事件
// const handleTagClick = (label) => {
//   if (treeGraph.value) {
//     treeGraph.value.highlightByTag(label);
//   }
// };

// 组件挂载时加载数据
onMounted(() => {
  loadData();
});
</script>

<style lang="scss" scoped>
.question-graph-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 15px;

  .graph-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    h3 {
      color: #8a6de3;
      margin: 0;
      font-size: 18px;
    }

    .tab-list-box {
      display: flex;
      background: #f5f5f5;
      border-radius: 6px;
      padding: 4px;

      .tab-list {
        padding: 6px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.3s;

        &.active {
          background: white;
          color: #8a6de3;
          font-weight: 500;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        &:hover:not(.active) {
          background: rgba(255, 255, 255, 0.5);
        }
      }
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    color: #8a6de3;

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #8a6de3;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 16px;
    }

    p {
      margin: 0;
      font-size: 16px;
    }
  }

  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    color: #ff4d4f;

    p {
      margin: 0 0 16px 0;
      font-size: 16px;
    }

    .retry-btn {
      padding: 8px 16px;
      background: #8a6de3;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background 0.3s;

      &:hover {
        background: #7a5dd3;
      }
    }
  }

  .graph-wrapper {
    flex: 1;
    position: relative;
    min-height: 500px;

    .graph-content {
      position: relative;
      height: 100%;
      border: 1px solid #eee;
      border-radius: 8px;
      background-color: #f9f9f9;
      overflow: hidden;

      .tree-graph-container {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
      }

      .tag-summary-wrapper {
        position: absolute;
        top: 50%;
        left: 20px;
        transform: translateY(-50%);
        z-index: 2;
      }
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.ring-graph-panel,
.ring-graph-container {
  width: 100%;
  height: 100%;
  /* 或更大 */
  min-height: 300px;
}
</style>
