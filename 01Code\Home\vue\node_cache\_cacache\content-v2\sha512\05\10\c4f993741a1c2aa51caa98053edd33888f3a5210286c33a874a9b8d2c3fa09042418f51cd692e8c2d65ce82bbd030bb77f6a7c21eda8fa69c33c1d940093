{"_attachments": {}, "_id": "ms", "_rev": "581-61f144e0830fd08f52a204ab", "description": "Tiny millisecond conversion utility", "dist-tags": {"beta": "3.0.0-beta.2", "canary": "3.0.0-canary.1", "latest": "2.1.3"}, "license": "MIT", "maintainers": [{"name": "rauchg", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "zeit-bot", "email": "<EMAIL>"}, {"name": "vercel-release-bot", "email": "<EMAIL>"}, {"name": "leo", "email": "<EMAIL>"}, {"name": "matt.straka", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nick.tracey", "email": "<EMAIL>"}], "name": "ms", "readme": "# ms\n\n![CI](https://github.com/vercel/ms/workflows/CI/badge.svg)\n\nUse this package to easily convert various time formats to milliseconds.\n\n## Examples\n\n<!-- prettier-ignore -->\n```js\nms('2 days')  // 172800000\nms('1d')      // 86400000\nms('10h')     // 36000000\nms('2.5 hrs') // 9000000\nms('2h')      // 7200000\nms('1m')      // 60000\nms('5s')      // 5000\nms('1y')      // 31557600000\nms('100')     // 100\nms('-3 days') // -259200000\nms('-1h')     // -3600000\nms('-200')    // -200\n```\n\n### Convert from Milliseconds\n\n<!-- prettier-ignore -->\n```js\nms(60000)             // \"1m\"\nms(2 * 60000)         // \"2m\"\nms(-3 * 60000)        // \"-3m\"\nms(ms('10 hours'))    // \"10h\"\n```\n\n### Time Format Written-Out\n\n<!-- prettier-ignore -->\n```js\nms(60000, { long: true })             // \"1 minute\"\nms(2 * 60000, { long: true })         // \"2 minutes\"\nms(-3 * 60000, { long: true })        // \"-3 minutes\"\nms(ms('10 hours'), { long: true })    // \"10 hours\"\n```\n\n## Features\n\n- Works both in [Node.js](https://nodejs.org) and in the browser\n- If a number is supplied to `ms`, a string with a unit is returned\n- If a string that contains the number is supplied, it returns it as a number (e.g.: it returns `100` for `'100'`)\n- If you pass a string with a number and a valid unit, the number of equivalent milliseconds is returned\n\n## TypeScript support\n\nAs of v3.0, this package includes TypeScript definitions.\n\nFor added safety, we're using [Template Literal Types](https://www.typescriptlang.org/docs/handbook/2/template-literal-types.html) (added in [TypeScript 4.1](https://www.typescriptlang.org/docs/handbook/release-notes/typescript-4-1.html)). This ensures that you don't accidentally pass `ms` values that it can't process.\n\nThis won't require you to do anything special in most situations, but you can also import the `StringValue` type from `ms` if you need to use it.\n\n```ts\nimport ms, { StringValue } from 'ms';\n\n// Using the exported type.\nfunction example(value: StringValue) {\n  ms(value);\n}\n\n// This function will only accept a string compatible with `ms`.\nexample('1 h');\n```\n\nIn this example, we use a [Type Assertion](https://www.typescriptlang.org/docs/handbook/2/everyday-types.html#type-assertions) to coerce a `string`.\n\n```ts\nimport ms, { StringValue } from 'ms';\n\n// Type assertion with the exported type.\nfunction example(value: string) {\n  try {\n    // A string could be \"wider\" than the values accepted by `ms`, so we assert\n    // that our `value` is a `StringValue`.\n    //\n    // It's important to note that this can be dangerous (see below).\n    ms(value as StringValue);\n  } catch (error: Error) {\n    // Handle any errors from invalid vaues.\n    console.error(error);\n  }\n}\n\n// This function will accept any string, which may result in a bug.\nexample('any value');\n```\n\nYou may also create a custom Template Literal Type.\n\n```ts\nimport ms from 'ms';\n\ntype OnlyDaysAndWeeks = `${number} ${'days' | 'weeks'}`;\n\n// Using a custom Template Literal Type.\nfunction example(value: OnlyDaysAndWeeks) {\n  // The type of `value` is narrower than the values `ms` accepts, which is\n  // safe to use without coercion.\n  ms(value);\n}\n\n// This function will accept \"# days\" or \"# weeks\" only.\nexample('5.2 days');\n```\n\n## Related Packages\n\n- [ms.macro](https://github.com/knpwrs/ms.macro) - Run `ms` as a macro at build-time.\n\n## Caught a Bug?\n\n1. [Fork](https://help.github.com/articles/fork-a-repo/) this repository to your own GitHub account and then [clone](https://help.github.com/articles/cloning-a-repository/) it to your local device\n2. Link the package to the global module directory: `npm link`\n3. Within the module you want to test your local development instance of ms, just link it to the dependencies: `npm link ms`. Instead of the default one from npm, Node.js will now use your clone of ms!\n\nAs always, you can run the tests using: `npm test`\n", "time": {"created": "2022-01-26T12:56:00.811Z", "modified": "2025-06-22T02:25:34.961Z", "0.5.0": "2012-11-10T00:39:49.944Z", "3.0.0-canary.1": "2021-09-15T15:40:43.956Z", "3.0.0-canary.0": "2021-09-15T13:29:00.734Z", "3.0.0-beta.2": "2021-08-25T16:55:32.842Z", "3.0.0-beta.1": "2021-08-20T15:29:19.828Z", "3.0.0-beta.0": "2021-08-20T14:54:07.095Z", "2.1.3": "2020-12-08T13:54:35.223Z", "2.1.2": "2019-06-06T17:31:55.859Z", "2.1.1": "2017-11-30T18:30:16.876Z", "2.1.0": "2017-11-30T16:54:16.315Z", "2.0.0": "2017-05-16T12:26:06.610Z", "1.0.0": "2017-03-19T21:43:15.128Z", "0.7.3": "2017-03-08T21:59:28.048Z", "0.7.2": "2016-10-25T08:16:49.773Z", "0.7.1": "2015-04-20T23:38:57.957Z", "0.7.0": "2014-11-24T07:59:08.195Z", "0.6.2": "2013-12-05T15:57:45.292Z", "0.6.1": "2013-05-10T15:38:08.059Z", "0.6.0": "2013-03-15T15:26:35.127Z", "0.5.1": "2013-02-24T20:27:27.010Z", "0.4.0": "2012-10-22T17:01:26.046Z", "0.3.0": "2012-09-07T20:36:45.931Z", "0.2.0": "2012-09-03T20:33:06.093Z", "0.1.0": "2011-12-21T19:38:26.538Z"}, "versions": {"0.5.0": {"name": "ms", "version": "0.5.0", "description": "Tiny ms conversion utility", "main": "./index", "devDependencies": {"mocha": "*", "expect.js": "*", "serve": "*"}, "_id": "ms@0.5.0", "dist": {"tarball": "https://registry.npmmirror.com/ms/-/ms-0.5.0.tgz", "shasum": "8e52e7e1bf521f9cea30f726de958822eab0ee27", "size": 2304, "noattachment": false, "integrity": "sha512-l+4vT0spctuJn4dEuiTHFJg/o2Gu7lcPPVmoEkOvCJ7q6btdsvokZscv1rAj5rokCmiqZRWpA/apQSpgDv8ZSw=="}, "_npmVersion": "1.1.59", "_npmUser": {"name": "rauchg", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "bme<PERSON>", "email": "blake<PERSON><PERSON>@gmail.com"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "codybrouwers", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "dferber90", "email": "<EMAIL>"}, {"name": "ebb-tide", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "f3d0r", "email": "<EMAIL>"}, {"name": "falcoagustin", "email": "<EMAIL>"}, {"name": "gdborton", "email": "<EMAIL>"}, {"name": "geovanisouza92", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hungrybearstudio", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "kdy1", "email": "<EMAIL>"}, {"name": "kelly", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "msimulcik", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "southpolesteve", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1352507989944, "_hasShrinkwrap": false, "_cnpm_publish_time": 1352507989944, "_cnpmcore_publish_time": "2021-12-13T06:24:19.631Z"}, "3.0.0-canary.1": {"name": "ms", "version": "3.0.0-canary.1", "description": "Tiny millisecond conversion utility", "repository": {"type": "git", "url": "git+https://github.com/vercel/ms.git"}, "main": "./dist/index.cjs", "type": "module", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "sideEffects": false, "license": "MIT", "engines": {"node": ">=12.13"}, "scripts": {"test": "jest", "build": "scripts/build.js", "prepublishOnly": "npm run build", "eslint-check": "eslint --max-warnings=0 .", "prettier-check": "prettier --check .", "type-check": "tsc --noEmit", "precommit": "lint-staged", "prepare": "husky install"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"endOfLine": "lf", "tabWidth": 2, "printWidth": 80, "singleQuote": true, "trailingComma": "all"}, "lint-staged": {"*": ["prettier --ignore-unknown --write"], "*.{js,jsx,ts,tsx}": ["eslint --max-warnings=0 --fix"]}, "devDependencies": {"@types/jest": "27.0.1", "@typescript-eslint/eslint-plugin": "4.31.0", "@typescript-eslint/parser": "4.31.0", "eslint": "7.32.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-jest": "24.4.0", "eslint-plugin-tsdoc": "0.2.14", "husky": "7.0.2", "jest": "27.1.1", "lint-staged": "11.1.2", "prettier": "2.4.0", "ts-jest": "27.0.5", "typescript": "4.4.2"}, "gitHead": "1304f150b38027e0818cc122106b5c7322d68d0c", "bugs": {"url": "https://github.com/vercel/ms/issues"}, "homepage": "https://github.com/vercel/ms#readme", "_id": "ms@3.0.0-canary.1", "_nodeVersion": "14.16.0", "_npmVersion": "7.23.0", "dist": {"shasum": "c7b34fbce381492fd0b345d1cf56e14d67b77b80", "size": 4564, "noattachment": false, "tarball": "https://registry.npmmirror.com/ms/-/ms-3.0.0-canary.1.tgz", "integrity": "sha512-kh8ARjh8rMN7Du2igDRO9QJnqCb2xYTJxyQYK7vJJS4TvLLmsbyhiKpSW+t+y26gyOyMd0riphX0GeWKU3ky5g=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "lro<PERSON><EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "bme<PERSON>", "email": "blake<PERSON><PERSON>@gmail.com"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "codybrouwers", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "dferber90", "email": "<EMAIL>"}, {"name": "ebb-tide", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "f3d0r", "email": "<EMAIL>"}, {"name": "falcoagustin", "email": "<EMAIL>"}, {"name": "gdborton", "email": "<EMAIL>"}, {"name": "geovanisouza92", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hungrybearstudio", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "kdy1", "email": "<EMAIL>"}, {"name": "kelly", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "msimulcik", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "southpolesteve", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ms_3.0.0-canary.1_1631720443773_0.6805463476444127"}, "_hasShrinkwrap": false, "publish_time": 1631720443956, "_cnpm_publish_time": 1631720443956, "_cnpmcore_publish_time": "2021-12-13T06:24:11.716Z"}, "3.0.0-canary.0": {"name": "ms", "version": "3.0.0-canary.0", "description": "Tiny millisecond conversion utility", "repository": {"type": "git", "url": "git+https://github.com/vercel/ms.git"}, "main": "./dist/index.cjs", "type": "module", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "sideEffects": false, "license": "MIT", "engines": {"node": ">=12.13"}, "scripts": {"test": "jest", "build": "scripts/build.js", "prepublishOnly": "npm run build", "eslint-check": "eslint --max-warnings=0 .", "prettier-check": "prettier --check .", "type-check": "tsc --noEmit", "precommit": "lint-staged", "prepare": "husky install"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"endOfLine": "lf", "tabWidth": 2, "printWidth": 80, "singleQuote": true, "trailingComma": "all"}, "lint-staged": {"*": ["prettier --ignore-unknown --write"], "*.{js,jsx,ts,tsx}": ["eslint --max-warnings=0 --fix"]}, "devDependencies": {"@types/jest": "27.0.1", "@typescript-eslint/eslint-plugin": "4.31.0", "@typescript-eslint/parser": "4.31.0", "eslint": "7.32.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-jest": "24.4.0", "eslint-plugin-tsdoc": "0.2.14", "husky": "7.0.2", "jest": "27.1.1", "lint-staged": "11.1.2", "prettier": "2.4.0", "ts-jest": "27.0.5", "typescript": "4.4.2"}, "gitHead": "6dd3b72e9b0a920d5ca04b989390ce89b12f62bd", "bugs": {"url": "https://github.com/vercel/ms/issues"}, "homepage": "https://github.com/vercel/ms#readme", "_id": "ms@3.0.0-canary.0", "_nodeVersion": "14.16.0", "_npmVersion": "7.23.0", "dist": {"shasum": "f965adb7f3afccac672ec0b2cf6bdc5320b755c5", "size": 3042, "noattachment": false, "tarball": "https://registry.npmmirror.com/ms/-/ms-3.0.0-canary.0.tgz", "integrity": "sha512-FrdRKJ9G+nm9Bw6atUMrbIhGTJ2emtf0KRgtzgSrAW1nnV9RuQAXc7sbFWdr1RfOH3fcNk0MyK+ma4iihdLmyA=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "lro<PERSON><EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "bme<PERSON>", "email": "blake<PERSON><PERSON>@gmail.com"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "codybrouwers", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "dferber90", "email": "<EMAIL>"}, {"name": "ebb-tide", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "f3d0r", "email": "<EMAIL>"}, {"name": "falcoagustin", "email": "<EMAIL>"}, {"name": "gdborton", "email": "<EMAIL>"}, {"name": "geovanisouza92", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hungrybearstudio", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "kdy1", "email": "<EMAIL>"}, {"name": "kelly", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "msimulcik", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "southpolesteve", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ms_3.0.0-canary.0_1631712540574_0.010946113352426678"}, "_hasShrinkwrap": false, "publish_time": 1631712540734, "_cnpm_publish_time": 1631712540734, "_cnpmcore_publish_time": "2021-12-13T06:24:12.053Z"}, "3.0.0-beta.2": {"name": "ms", "version": "3.0.0-beta.2", "description": "Tiny millisecond conversion utility", "repository": {"type": "git", "url": "git+https://github.com/vercel/ms.git"}, "main": "./lib/index.cjs", "type": "module", "exports": {"import": "./lib/index.mjs", "require": "./lib/index.cjs"}, "module": "./lib/index.mjs", "types": "./lib/index.d.ts", "sideEffects": false, "license": "MIT", "engines": {"node": ">=12.13"}, "scripts": {"test": "jest", "build": "scripts/build.js", "prepublishOnly": "npm run build", "eslint-check": "eslint --max-warnings=0 .", "prettier-check": "prettier --check .", "type-check": "tsc --noEmit", "precommit": "lint-staged", "prepare": "husky install"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"endOfLine": "lf", "tabWidth": 2, "printWidth": 80, "singleQuote": true, "trailingComma": "all"}, "lint-staged": {"*": ["prettier --ignore-unknown --write"], "*.{js,jsx,ts,tsx}": ["eslint --max-warnings=0 --fix"]}, "devDependencies": {"@types/jest": "27.0.1", "@typescript-eslint/eslint-plugin": "4.29.2", "@typescript-eslint/parser": "4.29.2", "eslint": "7.32.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-jest": "24.4.0", "eslint-plugin-tsdoc": "0.2.14", "husky": "7.0.1", "jest": "27.0.6", "lint-staged": "11.1.2", "prettier": "2.3.2", "ts-jest": "27.0.5", "typescript": "4.3.5"}, "readmeFilename": "readme.md", "gitHead": "6d2221735dbe9ec197e0753c22af4ce406ac512b", "bugs": {"url": "https://github.com/vercel/ms/issues"}, "homepage": "https://github.com/vercel/ms#readme", "_id": "ms@3.0.0-beta.2", "_nodeVersion": "14.16.0", "_npmVersion": "6.14.11", "dist": {"shasum": "c1a586879b489759c44be2ac402ff1df7c314ed9", "size": 4581, "noattachment": false, "tarball": "https://registry.npmmirror.com/ms/-/ms-3.0.0-beta.2.tgz", "integrity": "sha512-G/4X0GjOFFpeGVj0D/yxd7plnMjizeLa2mBu2yNRPQlDlvmERfqZ2alTIijo9QNH91b9g1IlJAYsVV1g6GbWvg=="}, "_npmUser": {"name": "mrmckeb", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "bme<PERSON>", "email": "blake<PERSON><PERSON>@gmail.com"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "codybrouwers", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "dferber90", "email": "<EMAIL>"}, {"name": "ebb-tide", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "f3d0r", "email": "<EMAIL>"}, {"name": "falcoagustin", "email": "<EMAIL>"}, {"name": "gdborton", "email": "<EMAIL>"}, {"name": "geovanisouza92", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hungrybearstudio", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "kdy1", "email": "<EMAIL>"}, {"name": "kelly", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "msimulcik", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "southpolesteve", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ms_3.0.0-beta.2_1629910532683_0.9012556075786939"}, "_hasShrinkwrap": false, "publish_time": 1629910532842, "_cnpm_publish_time": 1629910532842, "_cnpmcore_publish_time": "2021-12-13T06:24:12.428Z"}, "3.0.0-beta.1": {"name": "ms", "version": "3.0.0-beta.1", "description": "Tiny millisecond conversion utility", "repository": {"type": "git", "url": "git+https://github.com/vercel/ms.git"}, "main": "./lib/index.cjs", "type": "module", "exports": {"import": "./lib/index.mjs", "require": "./lib/index.cjs"}, "module": "./lib/index.mjs", "types": "./lib/index.d.ts", "sideEffects": false, "license": "MIT", "engines": {"node": ">=12.13"}, "scripts": {"test": "jest", "build": "scripts/build.js", "prepublishOnly": "npm run build", "eslint-check": "eslint --max-warnings=0 .", "prettier-check": "prettier --check .", "type-check": "tsc --noEmit", "precommit": "lint-staged", "prepare": "husky install"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"endOfLine": "lf", "tabWidth": 2, "printWidth": 80, "singleQuote": true, "trailingComma": "all"}, "lint-staged": {"*": ["prettier --ignore-unknown --write"], "*.{js,jsx,ts,tsx}": ["eslint --max-warnings=0 --fix"]}, "devDependencies": {"@types/jest": "27.0.1", "@typescript-eslint/eslint-plugin": "4.29.2", "@typescript-eslint/parser": "4.29.2", "eslint": "7.32.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-jest": "24.4.0", "eslint-plugin-tsdoc": "0.2.14", "husky": "7.0.1", "jest": "27.0.6", "lint-staged": "11.1.2", "prettier": "2.3.2", "ts-jest": "27.0.5", "typescript": "4.3.5"}, "readmeFilename": "readme.md", "gitHead": "7068bb390311b2620e65f992cd3ad6ff19d13400", "bugs": {"url": "https://github.com/vercel/ms/issues"}, "homepage": "https://github.com/vercel/ms#readme", "_id": "ms@3.0.0-beta.1", "_nodeVersion": "14.16.0", "_npmVersion": "6.14.11", "dist": {"shasum": "1796d327b201d04705b8bf70b67442246c1e26e4", "size": 4596, "noattachment": false, "tarball": "https://registry.npmmirror.com/ms/-/ms-3.0.0-beta.1.tgz", "integrity": "sha512-72RBgCsIUfh6MtK1FyAqWVYjMhvYsU/5WbiTrAksNyIcv/uhR8r6g7wU5JEUIzhRYYI1uF9+I5S1vOb41NYxkw=="}, "_npmUser": {"name": "mrmckeb", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "bme<PERSON>", "email": "blake<PERSON><PERSON>@gmail.com"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "codybrouwers", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "dferber90", "email": "<EMAIL>"}, {"name": "ebb-tide", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "f3d0r", "email": "<EMAIL>"}, {"name": "falcoagustin", "email": "<EMAIL>"}, {"name": "gdborton", "email": "<EMAIL>"}, {"name": "geovanisouza92", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hungrybearstudio", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "kdy1", "email": "<EMAIL>"}, {"name": "kelly", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "msimulcik", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "southpolesteve", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ms_3.0.0-beta.1_1629473359666_0.4804227855905876"}, "_hasShrinkwrap": false, "publish_time": 1629473359828, "_cnpm_publish_time": 1629473359828, "_cnpmcore_publish_time": "2021-12-13T06:24:12.845Z"}, "3.0.0-beta.0": {"name": "ms", "version": "3.0.0-beta.0", "description": "Tiny millisecond conversion utility", "repository": {"type": "git", "url": "git+https://github.com/vercel/ms.git"}, "main": "./lib/index.cjs", "type": "module", "exports": {"import": "./lib/index.mjs", "require": "./lib/index.cjs"}, "module": "./lib/index.mjs", "types": "./lib/index.d.ts", "sideEffects": false, "license": "MIT", "engines": {"node": ">=12.13"}, "scripts": {"test": "jest", "build": "scripts/build.js", "prepublishOnly": "npm run build", "eslint-check": "eslint --max-warnings=0 .", "prettier-check": "prettier --check .", "type-check": "tsc --noEmit", "precommit": "lint-staged", "prepare": "husky install"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"endOfLine": "lf", "tabWidth": 2, "printWidth": 80, "singleQuote": true, "trailingComma": "all"}, "lint-staged": {"*": ["prettier --ignore-unknown --write"], "*.{js,jsx,ts,tsx}": ["eslint --max-warnings=0 --fix"]}, "devDependencies": {"@types/jest": "27.0.1", "@typescript-eslint/eslint-plugin": "4.29.2", "@typescript-eslint/parser": "4.29.2", "eslint": "7.32.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-jest": "24.4.0", "eslint-plugin-tsdoc": "0.2.14", "husky": "7.0.1", "jest": "27.0.6", "lint-staged": "11.1.2", "prettier": "2.3.2", "ts-jest": "27.0.5", "typescript": "4.3.5"}, "gitHead": "4059199878427d37197270ee7852d7c18206e92a", "readmeFilename": "readme.md", "bugs": {"url": "https://github.com/vercel/ms/issues"}, "homepage": "https://github.com/vercel/ms#readme", "_id": "ms@3.0.0-beta.0", "_nodeVersion": "14.16.0", "_npmVersion": "6.14.11", "dist": {"shasum": "d970e06f8b1e384befe5acae5c27209e9b93916f", "size": 4353, "noattachment": false, "tarball": "https://registry.npmmirror.com/ms/-/ms-3.0.0-beta.0.tgz", "integrity": "sha512-x620WtkfdGJZPaGRIJLeTEJcHiq6fHx0DR2KVfMgn4bLB3N60NUFrTTfuo7mcNPc5coqyu0ioK5m92CXnJKYGQ=="}, "_npmUser": {"name": "mrmckeb", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "bme<PERSON>", "email": "blake<PERSON><PERSON>@gmail.com"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "codybrouwers", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "dferber90", "email": "<EMAIL>"}, {"name": "ebb-tide", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "f3d0r", "email": "<EMAIL>"}, {"name": "falcoagustin", "email": "<EMAIL>"}, {"name": "gdborton", "email": "<EMAIL>"}, {"name": "geovanisouza92", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hungrybearstudio", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "kdy1", "email": "<EMAIL>"}, {"name": "kelly", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "msimulcik", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "southpolesteve", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ms_3.0.0-beta.0_1629471246957_0.5534162813430621"}, "_hasShrinkwrap": false, "publish_time": 1629471247095, "_cnpm_publish_time": 1629471247095, "_cnpmcore_publish_time": "2021-12-13T06:24:13.220Z"}, "2.1.3": {"name": "ms", "version": "2.1.3", "description": "Tiny millisecond conversion utility", "repository": {"type": "git", "url": "git+https://github.com/vercel/ms.git"}, "main": "./index", "scripts": {"precommit": "lint-staged", "lint": "eslint lib/* bin/*", "test": "mocha tests.js"}, "eslintConfig": {"extends": "eslint:recommended", "env": {"node": true, "es6": true}}, "lint-staged": {"*.js": ["npm run lint", "prettier --single-quote --write", "git add"]}, "license": "MIT", "devDependencies": {"eslint": "4.18.2", "expect.js": "0.3.1", "husky": "0.14.3", "lint-staged": "5.0.0", "mocha": "4.0.1", "prettier": "2.0.5"}, "gitHead": "1c6264b795492e8fdecbc82cb8802fcfbfc08d26", "bugs": {"url": "https://github.com/vercel/ms/issues"}, "homepage": "https://github.com/vercel/ms#readme", "_id": "ms@2.1.3", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.6", "dist": {"shasum": "574c8138ce1d2b5861f0b44579dbadd60c6615b2", "size": 2967, "noattachment": false, "tarball": "https://registry.npmmirror.com/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="}, "_npmUser": {"name": "styfle", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "bme<PERSON>", "email": "blake<PERSON><PERSON>@gmail.com"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "codybrouwers", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "dferber90", "email": "<EMAIL>"}, {"name": "ebb-tide", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "f3d0r", "email": "<EMAIL>"}, {"name": "falcoagustin", "email": "<EMAIL>"}, {"name": "gdborton", "email": "<EMAIL>"}, {"name": "geovanisouza92", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hungrybearstudio", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "kdy1", "email": "<EMAIL>"}, {"name": "kelly", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "msimulcik", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "southpolesteve", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ms_2.1.3_1607435675054_0.7617026089064693"}, "_hasShrinkwrap": false, "publish_time": 1607435675223, "_cnpm_publish_time": 1607435675223, "_cnpmcore_publish_time": "2021-12-13T06:24:13.517Z"}, "2.1.2": {"name": "ms", "version": "2.1.2", "description": "Tiny millisecond conversion utility", "repository": {"type": "git", "url": "git+https://github.com/zeit/ms.git"}, "main": "./index", "scripts": {"precommit": "lint-staged", "lint": "eslint lib/* bin/*", "test": "mocha tests.js"}, "eslintConfig": {"extends": "eslint:recommended", "env": {"node": true, "es6": true}}, "lint-staged": {"*.js": ["npm run lint", "prettier --single-quote --write", "git add"]}, "license": "MIT", "devDependencies": {"eslint": "4.12.1", "expect.js": "0.3.1", "husky": "0.14.3", "lint-staged": "5.0.0", "mocha": "4.0.1"}, "gitHead": "7920885eb232fbe7a5efdab956d3e7c507c92ddf", "bugs": {"url": "https://github.com/zeit/ms/issues"}, "homepage": "https://github.com/zeit/ms#readme", "_id": "ms@2.1.2", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.3", "_npmUser": {"name": "styfle", "email": "<EMAIL>"}, "dist": {"shasum": "d09d1f357b443f493382a8eb3ccd183872ae6009", "size": 3017, "noattachment": false, "tarball": "https://registry.npmmirror.com/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="}, "maintainers": [{"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "bme<PERSON>", "email": "blake<PERSON><PERSON>@gmail.com"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "codybrouwers", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "dferber90", "email": "<EMAIL>"}, {"name": "ebb-tide", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "f3d0r", "email": "<EMAIL>"}, {"name": "falcoagustin", "email": "<EMAIL>"}, {"name": "gdborton", "email": "<EMAIL>"}, {"name": "geovanisouza92", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hungrybearstudio", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "kdy1", "email": "<EMAIL>"}, {"name": "kelly", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "msimulcik", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "southpolesteve", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ms_2.1.2_1559842315767_0.4700607530567853"}, "_hasShrinkwrap": false, "publish_time": 1559842315859, "_cnpm_publish_time": 1559842315859, "_cnpmcore_publish_time": "2021-12-13T06:24:13.844Z"}, "2.1.1": {"name": "ms", "version": "2.1.1", "description": "Tiny millisecond conversion utility", "repository": {"type": "git", "url": "git+https://github.com/zeit/ms.git"}, "main": "./index", "files": ["index.js"], "scripts": {"precommit": "lint-staged", "lint": "eslint lib/* bin/*", "test": "mocha tests.js"}, "eslintConfig": {"extends": "eslint:recommended", "env": {"node": true, "es6": true}}, "lint-staged": {"*.js": ["npm run lint", "prettier --single-quote --write", "git add"]}, "license": "MIT", "devDependencies": {"eslint": "4.12.1", "expect.js": "0.3.1", "husky": "0.14.3", "lint-staged": "5.0.0", "mocha": "4.0.1"}, "gitHead": "fe0bae301a6c41f68a01595658a4f4f0dcba0e84", "bugs": {"url": "https://github.com/zeit/ms/issues"}, "homepage": "https://github.com/zeit/ms#readme", "_id": "ms@2.1.1", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "leo", "email": "<EMAIL>"}, "dist": {"shasum": "30a5864eb3ebb0a66f2ebe6d727af06a09d86e0a", "size": 3028, "noattachment": false, "tarball": "https://registry.npmmirror.com/ms/-/ms-2.1.1.tgz", "integrity": "sha512-tgp+dl5cGk28utYktBsrFqA7HKgrhgPsg6Z/EfhWI4gl1Hwq8B/GmY/0oXZ6nF8hDVesS/FpnYaD/kOWhYQvyg=="}, "maintainers": [{"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "bme<PERSON>", "email": "blake<PERSON><PERSON>@gmail.com"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "codybrouwers", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "dferber90", "email": "<EMAIL>"}, {"name": "ebb-tide", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "f3d0r", "email": "<EMAIL>"}, {"name": "falcoagustin", "email": "<EMAIL>"}, {"name": "gdborton", "email": "<EMAIL>"}, {"name": "geovanisouza92", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hungrybearstudio", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "kdy1", "email": "<EMAIL>"}, {"name": "kelly", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "msimulcik", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "southpolesteve", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ms-2.1.1.tgz_1512066615982_0.7117063472978771"}, "directories": {}, "publish_time": 1512066616876, "_hasShrinkwrap": false, "_cnpm_publish_time": 1512066616876, "_cnpmcore_publish_time": "2021-12-13T06:24:14.155Z"}, "2.1.0": {"name": "ms", "version": "2.1.0", "description": "Tiny millisecond conversion utility", "repository": {"type": "git", "url": "git+https://github.com/zeit/ms.git"}, "main": "./index", "files": ["index.js"], "scripts": {"precommit": "lint-staged", "lint": "eslint lib/* bin/*", "test": "mocha tests.js"}, "eslintConfig": {"extends": "eslint:recommended", "env": {"node": true, "es6": true}}, "lint-staged": {"*.js": ["npm run lint", "prettier --single-quote --write", "git add"]}, "license": "MIT", "devDependencies": {"eslint": "4.12.1", "expect.js": "0.3.1", "husky": "0.14.3", "lint-staged": "5.0.0", "mocha": "4.0.1"}, "gitHead": "845c302f155d955141d623a0276bbff3529ed626", "bugs": {"url": "https://github.com/zeit/ms/issues"}, "homepage": "https://github.com/zeit/ms#readme", "_id": "ms@2.1.0", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "leo", "email": "<EMAIL>"}, "dist": {"shasum": "9a345be8f6a4aadc6686d74d88a23c1b84720549", "size": 2964, "noattachment": false, "tarball": "https://registry.npmmirror.com/ms/-/ms-2.1.0.tgz", "integrity": "sha512-gVZHb22Z7YDyiiaoGld9LD4tUuDDxdkDJUEfTIej9LFePFqiE9JxI0qTFfu6tD7Wu03lg7skmVwTmA6XkeMlPQ=="}, "maintainers": [{"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "bme<PERSON>", "email": "blake<PERSON><PERSON>@gmail.com"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "codybrouwers", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "dferber90", "email": "<EMAIL>"}, {"name": "ebb-tide", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "f3d0r", "email": "<EMAIL>"}, {"name": "falcoagustin", "email": "<EMAIL>"}, {"name": "gdborton", "email": "<EMAIL>"}, {"name": "geovanisouza92", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hungrybearstudio", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "kdy1", "email": "<EMAIL>"}, {"name": "kelly", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "msimulcik", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "southpolesteve", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ms-2.1.0.tgz_1512060855394_0.6358025514055043"}, "directories": {}, "publish_time": 1512060856315, "_hasShrinkwrap": false, "_cnpm_publish_time": 1512060856315, "_cnpmcore_publish_time": "2021-12-13T06:24:14.551Z"}, "2.0.0": {"name": "ms", "version": "2.0.0", "description": "Tiny milisecond conversion utility", "repository": {"type": "git", "url": "git+https://github.com/zeit/ms.git"}, "main": "./index", "files": ["index.js"], "scripts": {"precommit": "lint-staged", "lint": "eslint lib/* bin/*", "test": "mocha tests.js"}, "eslintConfig": {"extends": "eslint:recommended", "env": {"node": true, "es6": true}}, "lint-staged": {"*.js": ["npm run lint", "prettier --single-quote --write", "git add"]}, "license": "MIT", "devDependencies": {"eslint": "3.19.0", "expect.js": "0.3.1", "husky": "0.13.3", "lint-staged": "3.4.1", "mocha": "3.4.1"}, "gitHead": "9b88d1568a52ec9bb67ecc8d2aa224fa38fd41f4", "bugs": {"url": "https://github.com/zeit/ms/issues"}, "homepage": "https://github.com/zeit/ms#readme", "_id": "ms@2.0.0", "_shasum": "5608aeadfc00be6c2901df5f9861788de0d597c8", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.8.0", "_npmUser": {"name": "leo", "email": "<EMAIL>"}, "dist": {"shasum": "5608aeadfc00be6c2901df5f9861788de0d597c8", "size": 2874, "noattachment": false, "tarball": "https://registry.npmmirror.com/ms/-/ms-2.0.0.tgz", "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A=="}, "maintainers": [{"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "bme<PERSON>", "email": "blake<PERSON><PERSON>@gmail.com"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "codybrouwers", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "dferber90", "email": "<EMAIL>"}, {"name": "ebb-tide", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "f3d0r", "email": "<EMAIL>"}, {"name": "falcoagustin", "email": "<EMAIL>"}, {"name": "gdborton", "email": "<EMAIL>"}, {"name": "geovanisouza92", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hungrybearstudio", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "kdy1", "email": "<EMAIL>"}, {"name": "kelly", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "msimulcik", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "southpolesteve", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/ms-2.0.0.tgz_1494937565215_0.34005374647676945"}, "directories": {}, "publish_time": 1494937566610, "_hasShrinkwrap": false, "_cnpm_publish_time": 1494937566610, "_cnpmcore_publish_time": "2021-12-13T06:24:14.896Z"}, "1.0.0": {"name": "ms", "version": "1.0.0", "description": "Tiny milisecond conversion utility", "repository": {"type": "git", "url": "git+https://github.com/zeit/ms.git"}, "main": "./index", "files": ["index.js"], "scripts": {"precommit": "lint-staged", "lint": "eslint lib/* bin/*", "test": "mocha tests.js"}, "eslintConfig": {"extends": "eslint:recommended", "env": {"node": true, "es6": true}}, "lint-staged": {"*.js": ["npm run lint", "prettier --single-quote --write", "git add"]}, "license": "MIT", "devDependencies": {"eslint": "3.18.0", "expect.js": "0.3.1", "husky": "0.13.2", "lint-staged": "3.4.0", "mocha": "3.0.2"}, "gitHead": "7daf984a9011e720cc3c165ed82c4506f3471b37", "bugs": {"url": "https://github.com/zeit/ms/issues"}, "homepage": "https://github.com/zeit/ms#readme", "_id": "ms@1.0.0", "_shasum": "59adcd22edc543f7b5381862d31387b1f4bc9473", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.3", "_npmUser": {"name": "leo", "email": "<EMAIL>"}, "dist": {"shasum": "59adcd22edc543f7b5381862d31387b1f4bc9473", "size": 2885, "noattachment": false, "tarball": "https://registry.npmmirror.com/ms/-/ms-1.0.0.tgz", "integrity": "sha512-85ytwCiGUnD84ui6ULG1KBFMaZgHW3jg5KPr9jt+ZPYt75+XK+JGbYddGrBQ+RSHXOhekCnCZwJywBoFvFl0kw=="}, "maintainers": [{"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "bme<PERSON>", "email": "blake<PERSON><PERSON>@gmail.com"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "codybrouwers", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "dferber90", "email": "<EMAIL>"}, {"name": "ebb-tide", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "f3d0r", "email": "<EMAIL>"}, {"name": "falcoagustin", "email": "<EMAIL>"}, {"name": "gdborton", "email": "<EMAIL>"}, {"name": "geovanisouza92", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hungrybearstudio", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "kdy1", "email": "<EMAIL>"}, {"name": "kelly", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "msimulcik", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "southpolesteve", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ms-1.0.0.tgz_1489959793252_0.42147551802918315"}, "directories": {}, "publish_time": 1489959795128, "_hasShrinkwrap": false, "_cnpm_publish_time": 1489959795128, "_cnpmcore_publish_time": "2021-12-13T06:24:15.334Z"}, "0.7.3": {"name": "ms", "version": "0.7.3", "description": "Tiny milisecond conversion utility", "repository": {"type": "git", "url": "git+https://github.com/zeit/ms.git"}, "main": "./index", "files": ["index.js"], "scripts": {"test": "xo && mocha test/index.js", "test-browser": "serve ./test"}, "license": "MIT", "devDependencies": {"expect.js": "0.3.1", "mocha": "3.0.2", "serve": "5.0.1", "xo": "0.17.0"}, "component": {"scripts": {"ms/index.js": "index.js"}}, "xo": {"space": true, "semicolon": false, "envs": ["mocha"], "rules": {"complexity": 0}}, "gitHead": "2006a7706041443fcf1f899b5752677bd7ae01a8", "bugs": {"url": "https://github.com/zeit/ms/issues"}, "homepage": "https://github.com/zeit/ms#readme", "_id": "ms@0.7.3", "_shasum": "708155a5e44e33f5fd0fc53e81d0d40a91be1fff", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.6.0", "_npmUser": {"name": "leo", "email": "<EMAIL>"}, "dist": {"shasum": "708155a5e44e33f5fd0fc53e81d0d40a91be1fff", "size": 2873, "noattachment": false, "tarball": "https://registry.npmmirror.com/ms/-/ms-0.7.3.tgz", "integrity": "sha512-lrKNzMWqQZgwJahtrtrM+9NgOoDUveDrVmm5aGXrf3BdtL0mq7X6IVzoZaw+TfNti29eHd1/8GI+h45K5cQ6/w=="}, "maintainers": [{"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "bme<PERSON>", "email": "blake<PERSON><PERSON>@gmail.com"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "codybrouwers", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "dferber90", "email": "<EMAIL>"}, {"name": "ebb-tide", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "f3d0r", "email": "<EMAIL>"}, {"name": "falcoagustin", "email": "<EMAIL>"}, {"name": "gdborton", "email": "<EMAIL>"}, {"name": "geovanisouza92", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hungrybearstudio", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "kdy1", "email": "<EMAIL>"}, {"name": "kelly", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "msimulcik", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "southpolesteve", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ms-0.7.3.tgz_1489010366101_0.14404030703008175"}, "directories": {}, "publish_time": 1489010368048, "_hasShrinkwrap": false, "_cnpm_publish_time": 1489010368048, "_cnpmcore_publish_time": "2021-12-13T06:24:15.746Z"}, "0.7.2": {"name": "ms", "version": "0.7.2", "description": "Tiny milisecond conversion utility", "repository": {"type": "git", "url": "git+https://github.com/zeit/ms.git"}, "main": "./index", "files": ["index.js"], "scripts": {"test": "xo && mocha test/index.js", "test-browser": "serve ./test"}, "license": "MIT", "devDependencies": {"expect.js": "^0.3.1", "mocha": "^3.0.2", "serve": "^1.4.0", "xo": "^0.17.0"}, "component": {"scripts": {"ms/index.js": "index.js"}}, "xo": {"space": true, "semicolon": false, "envs": ["mocha"], "rules": {"complexity": 0}}, "gitHead": "ac92a7e0790ba2622a74d9d60690ca0d2c070a45", "bugs": {"url": "https://github.com/zeit/ms/issues"}, "homepage": "https://github.com/zeit/ms#readme", "_id": "ms@0.7.2", "_shasum": "ae25cf2512b3885a1d95d7f037868d8431124765", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.8.0", "_npmUser": {"name": "leo", "email": "<EMAIL>"}, "dist": {"shasum": "ae25cf2512b3885a1d95d7f037868d8431124765", "size": 2876, "noattachment": false, "tarball": "https://registry.npmmirror.com/ms/-/ms-0.7.2.tgz", "integrity": "sha512-5NnE67nQSQDJHVahPJna1PQ/zCXMnQop3yUCxjKPNzCxuyPSKWTQ/5Gu5CZmjetwGLWRA+PzeF5thlbOdbQldA=="}, "maintainers": [{"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "bme<PERSON>", "email": "blake<PERSON><PERSON>@gmail.com"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "codybrouwers", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "dferber90", "email": "<EMAIL>"}, {"name": "ebb-tide", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "f3d0r", "email": "<EMAIL>"}, {"name": "falcoagustin", "email": "<EMAIL>"}, {"name": "gdborton", "email": "<EMAIL>"}, {"name": "geovanisouza92", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hungrybearstudio", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "kdy1", "email": "<EMAIL>"}, {"name": "kelly", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "msimulcik", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "southpolesteve", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/ms-0.7.2.tgz_1477383407940_0.4743474116548896"}, "directories": {}, "publish_time": 1477383409773, "_hasShrinkwrap": false, "_cnpm_publish_time": 1477383409773, "_cnpmcore_publish_time": "2021-12-13T06:24:16.184Z"}, "0.7.1": {"name": "ms", "version": "0.7.1", "description": "Tiny ms conversion utility", "repository": {"type": "git", "url": "git://github.com/guille/ms.js.git"}, "main": "./index", "devDependencies": {"mocha": "*", "expect.js": "*", "serve": "*"}, "component": {"scripts": {"ms/index.js": "index.js"}}, "gitHead": "713dcf26d9e6fd9dbc95affe7eff9783b7f1b909", "bugs": {"url": "https://github.com/guille/ms.js/issues"}, "homepage": "https://github.com/guille/ms.js", "_id": "ms@0.7.1", "scripts": {}, "_shasum": "9cd13c03adbff25b65effde7ce864ee952017098", "_from": ".", "_npmVersion": "2.7.5", "_nodeVersion": "0.12.2", "_npmUser": {"name": "rauchg", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "bme<PERSON>", "email": "blake<PERSON><PERSON>@gmail.com"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "codybrouwers", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "dferber90", "email": "<EMAIL>"}, {"name": "ebb-tide", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "f3d0r", "email": "<EMAIL>"}, {"name": "falcoagustin", "email": "<EMAIL>"}, {"name": "gdborton", "email": "<EMAIL>"}, {"name": "geovanisouza92", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hungrybearstudio", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "kdy1", "email": "<EMAIL>"}, {"name": "kelly", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "msimulcik", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "southpolesteve", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "dist": {"shasum": "9cd13c03adbff25b65effde7ce864ee952017098", "size": 2802, "noattachment": false, "tarball": "https://registry.npmmirror.com/ms/-/ms-0.7.1.tgz", "integrity": "sha512-lRLiIR9fSNpnP6TC4v8+4OU7oStC01esuNowdQ34L+Gk8e5Puoc88IqJ+XAY/B3Mn2ZKis8l8HX90oU8ivzUHg=="}, "directories": {}, "publish_time": 1429573137957, "_hasShrinkwrap": false, "_cnpm_publish_time": 1429573137957, "_cnpmcore_publish_time": "2021-12-13T06:24:16.638Z"}, "0.7.0": {"name": "ms", "version": "0.7.0", "description": "Tiny ms conversion utility", "repository": {"type": "git", "url": "git://github.com/guille/ms.js.git"}, "main": "./index", "devDependencies": {"mocha": "*", "expect.js": "*", "serve": "*"}, "component": {"scripts": {"ms/index.js": "index.js"}}, "gitHead": "1e9cd9b05ef0dc26f765434d2bfee42394376e52", "bugs": {"url": "https://github.com/guille/ms.js/issues"}, "homepage": "https://github.com/guille/ms.js", "_id": "ms@0.7.0", "scripts": {}, "_shasum": "865be94c2e7397ad8a57da6a633a6e2f30798b83", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "rauchg", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "bme<PERSON>", "email": "blake<PERSON><PERSON>@gmail.com"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "codybrouwers", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "dferber90", "email": "<EMAIL>"}, {"name": "ebb-tide", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "f3d0r", "email": "<EMAIL>"}, {"name": "falcoagustin", "email": "<EMAIL>"}, {"name": "gdborton", "email": "<EMAIL>"}, {"name": "geovanisouza92", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hungrybearstudio", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "kdy1", "email": "<EMAIL>"}, {"name": "kelly", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "msimulcik", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "southpolesteve", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "dist": {"shasum": "865be94c2e7397ad8a57da6a633a6e2f30798b83", "size": 2275, "noattachment": false, "tarball": "https://registry.npmmirror.com/ms/-/ms-0.7.0.tgz", "integrity": "sha512-YmuMMkfOZzzAftlHwiQxFepJx/5rDaYi9o9QanyBCk485BRAyM/vB9XoYlZvglxE/pmAWOiQgrdoE10watiK9w=="}, "directories": {}, "publish_time": 1416815948195, "_hasShrinkwrap": false, "_cnpm_publish_time": 1416815948195, "_cnpmcore_publish_time": "2021-12-13T06:24:17.082Z"}, "0.6.2": {"name": "ms", "version": "0.6.2", "description": "Tiny ms conversion utility", "repository": {"type": "git", "url": "git://github.com/guille/ms.js.git"}, "main": "./index", "devDependencies": {"mocha": "*", "expect.js": "*", "serve": "*"}, "component": {"scripts": {"ms/index.js": "index.js"}}, "readmeFilename": "README.md", "bugs": {"url": "https://github.com/guille/ms.js/issues"}, "_id": "ms@0.6.2", "dist": {"tarball": "https://registry.npmmirror.com/ms/-/ms-0.6.2.tgz", "shasum": "d89c2124c6fdc1353d65a8b77bf1aac4b193708c", "size": 1477, "noattachment": false, "integrity": "sha512-/pc3eh7TWorTtbvXg8je4GvrvEqCfH7PA3P7iW01yL2E53FKixzgMBaQi0NOPbMJqY34cBSvR0tZtmlTkdUG4A=="}, "_from": ".", "_npmVersion": "1.2.30", "_npmUser": {"name": "rauchg", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "bme<PERSON>", "email": "blake<PERSON><PERSON>@gmail.com"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "codybrouwers", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "dferber90", "email": "<EMAIL>"}, {"name": "ebb-tide", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "f3d0r", "email": "<EMAIL>"}, {"name": "falcoagustin", "email": "<EMAIL>"}, {"name": "gdborton", "email": "<EMAIL>"}, {"name": "geovanisouza92", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hungrybearstudio", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "kdy1", "email": "<EMAIL>"}, {"name": "kelly", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "msimulcik", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "southpolesteve", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1386259065292, "_hasShrinkwrap": false, "_cnpm_publish_time": 1386259065292, "_cnpmcore_publish_time": "2021-12-13T06:24:17.556Z"}, "0.6.1": {"name": "ms", "version": "0.6.1", "description": "Tiny ms conversion utility", "main": "./index", "devDependencies": {"mocha": "*", "expect.js": "*", "serve": "*"}, "component": {"scripts": {"ms/index.js": "index.js"}}, "readmeFilename": "README.md", "_id": "ms@0.6.1", "dist": {"tarball": "https://registry.npmmirror.com/ms/-/ms-0.6.1.tgz", "shasum": "ed57e5f3fc736e09afc85017c5c912a47bc59ab9", "size": 1444, "noattachment": false, "integrity": "sha512-TAjpu7RNwH/eBQfmrVg6eA6hClZfmhd3B2Ghp/Di5HMLjNBhd44KtO5lWjQj0EayygL1BsfZEJe3Y4sBHMQQEg=="}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "rauchg", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "bme<PERSON>", "email": "blake<PERSON><PERSON>@gmail.com"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "codybrouwers", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "dferber90", "email": "<EMAIL>"}, {"name": "ebb-tide", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "f3d0r", "email": "<EMAIL>"}, {"name": "falcoagustin", "email": "<EMAIL>"}, {"name": "gdborton", "email": "<EMAIL>"}, {"name": "geovanisouza92", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hungrybearstudio", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "kdy1", "email": "<EMAIL>"}, {"name": "kelly", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "msimulcik", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "southpolesteve", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1368200288059, "_hasShrinkwrap": false, "_cnpm_publish_time": 1368200288059, "_cnpmcore_publish_time": "2021-12-13T06:24:18.022Z"}, "0.6.0": {"name": "ms", "version": "0.6.0", "description": "Tiny ms conversion utility", "main": "./index", "devDependencies": {"mocha": "*", "expect.js": "*", "serve": "*"}, "component": {"scripts": {"ms/index.js": "index.js"}}, "readmeFilename": "README.md", "_id": "ms@0.6.0", "dist": {"tarball": "https://registry.npmmirror.com/ms/-/ms-0.6.0.tgz", "shasum": "21dc16a7d1dc2d8ed244dc0e6a71a5c2612b623b", "size": 1389, "noattachment": false, "integrity": "sha512-twVBDoonss/A6chyHOAQkx8Y+daAablgQy4khn8vYnrbcU4UvLLLFX2TCVhbGOXxTxJ4pqQtlTzjBErRyq/NDA=="}, "_from": ".", "_npmVersion": "1.2.10", "_npmUser": {"name": "rauchg", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "bme<PERSON>", "email": "blake<PERSON><PERSON>@gmail.com"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "codybrouwers", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "dferber90", "email": "<EMAIL>"}, {"name": "ebb-tide", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "f3d0r", "email": "<EMAIL>"}, {"name": "falcoagustin", "email": "<EMAIL>"}, {"name": "gdborton", "email": "<EMAIL>"}, {"name": "geovanisouza92", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hungrybearstudio", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "kdy1", "email": "<EMAIL>"}, {"name": "kelly", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "msimulcik", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "southpolesteve", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1363361195127, "_hasShrinkwrap": false, "_cnpm_publish_time": 1363361195127, "_cnpmcore_publish_time": "2021-12-13T06:24:18.504Z"}, "0.5.1": {"name": "ms", "version": "0.5.1", "description": "Tiny ms conversion utility", "main": "./index", "devDependencies": {"mocha": "*", "expect.js": "*", "serve": "*"}, "component": {"scripts": {"ms/index.js": "index.js"}}, "readmeFilename": "README.md", "_id": "ms@0.5.1", "dist": {"tarball": "https://registry.npmmirror.com/ms/-/ms-0.5.1.tgz", "shasum": "98058c8f9c64854d1703ab92bf3f1dcc8e713b4c", "size": 2360, "noattachment": false, "integrity": "sha512-DgU7MSi4T3XY43mZL/Lgk31wqwe2NB56QsyVMcY3m5rICuAp+/uY1/w3lnjhPSaTYVdx1vZQ+ppUlH4AlJ6UAA=="}, "_from": ".", "_npmVersion": "1.2.10", "_npmUser": {"name": "rauchg", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "bme<PERSON>", "email": "blake<PERSON><PERSON>@gmail.com"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "codybrouwers", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "dferber90", "email": "<EMAIL>"}, {"name": "ebb-tide", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "f3d0r", "email": "<EMAIL>"}, {"name": "falcoagustin", "email": "<EMAIL>"}, {"name": "gdborton", "email": "<EMAIL>"}, {"name": "geovanisouza92", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hungrybearstudio", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "kdy1", "email": "<EMAIL>"}, {"name": "kelly", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "msimulcik", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "southpolesteve", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1361737647010, "_hasShrinkwrap": false, "_cnpm_publish_time": 1361737647010, "_cnpmcore_publish_time": "2021-12-13T06:24:19.084Z"}, "0.4.0": {"name": "ms", "version": "0.4.0", "description": "Tiny ms conversion utility", "main": "./index", "devDependencies": {"mocha": "*", "expect.js": "*", "serve": "*"}, "_id": "ms@0.4.0", "dist": {"tarball": "https://registry.npmmirror.com/ms/-/ms-0.4.0.tgz", "shasum": "77ade5470b099bb2d83e232c25763c18cd6963f1", "size": 2013, "noattachment": false, "integrity": "sha512-64oIDtd4AvWd9+PXu3mS+e+83nD/4+vDjORXYUrMsUodlxSgxHt6okjkFO94XAG+zDoBz7GPkCYFXd5OD++kJg=="}, "_npmVersion": "1.1.59", "_npmUser": {"name": "rauchg", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "bme<PERSON>", "email": "blake<PERSON><PERSON>@gmail.com"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "codybrouwers", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "dferber90", "email": "<EMAIL>"}, {"name": "ebb-tide", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "f3d0r", "email": "<EMAIL>"}, {"name": "falcoagustin", "email": "<EMAIL>"}, {"name": "gdborton", "email": "<EMAIL>"}, {"name": "geovanisouza92", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hungrybearstudio", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "kdy1", "email": "<EMAIL>"}, {"name": "kelly", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "msimulcik", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "southpolesteve", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1350925286046, "_hasShrinkwrap": false, "_cnpm_publish_time": 1350925286046, "_cnpmcore_publish_time": "2021-12-13T06:24:20.118Z"}, "0.3.0": {"name": "ms", "version": "0.3.0", "description": "Tiny ms conversion utility", "main": "./ms", "devDependencies": {"mocha": "*", "expect.js": "*", "serve": "*"}, "_id": "ms@0.3.0", "dist": {"tarball": "https://registry.npmmirror.com/ms/-/ms-0.3.0.tgz", "shasum": "03edc348d613e66a56486cfdac53bcbe899cbd61", "size": 74687, "noattachment": false, "integrity": "sha512-25BVmSAdN4KRX7XeI6/gwQ9ewx6t9QB9/8X2fVJUUDpPc03qTRaEPgt5bTMZQ5T2l+XT+haSfqIkysOupDsSVQ=="}, "_npmVersion": "1.1.59", "_npmUser": {"name": "rauchg", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "bme<PERSON>", "email": "blake<PERSON><PERSON>@gmail.com"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "codybrouwers", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "dferber90", "email": "<EMAIL>"}, {"name": "ebb-tide", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "f3d0r", "email": "<EMAIL>"}, {"name": "falcoagustin", "email": "<EMAIL>"}, {"name": "gdborton", "email": "<EMAIL>"}, {"name": "geovanisouza92", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hungrybearstudio", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "kdy1", "email": "<EMAIL>"}, {"name": "kelly", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "msimulcik", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "southpolesteve", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1347050205931, "_hasShrinkwrap": false, "_cnpm_publish_time": 1347050205931, "_cnpmcore_publish_time": "2021-12-13T06:24:20.696Z"}, "0.2.0": {"name": "ms", "version": "0.2.0", "description": "Tiny ms conversion utility", "main": "./ms", "devDependencies": {"mocha": "*", "expect.js": "*", "serve": "*"}, "_id": "ms@0.2.0", "dist": {"tarball": "https://registry.npmmirror.com/ms/-/ms-0.2.0.tgz", "shasum": "6edfc5a063471f7bfd35a5831831c24275ce9dc5", "size": 74655, "noattachment": false, "integrity": "sha512-3hmNMG0TYmTiQD6+s+b9eKLYWYTbR+6AgZtOu60jiedzeu2JK9NS6Ih1vosLwxLutvG45slW7/fVaCM8WDXGRQ=="}, "_npmVersion": "1.1.59", "_npmUser": {"name": "rauchg", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "bme<PERSON>", "email": "blake<PERSON><PERSON>@gmail.com"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "codybrouwers", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "dferber90", "email": "<EMAIL>"}, {"name": "ebb-tide", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "f3d0r", "email": "<EMAIL>"}, {"name": "falcoagustin", "email": "<EMAIL>"}, {"name": "gdborton", "email": "<EMAIL>"}, {"name": "geovanisouza92", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hungrybearstudio", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "kdy1", "email": "<EMAIL>"}, {"name": "kelly", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "msimulcik", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "southpolesteve", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1346704386093, "_hasShrinkwrap": false, "_cnpm_publish_time": 1346704386093, "_cnpmcore_publish_time": "2021-12-13T06:24:21.297Z"}, "0.1.0": {"name": "ms", "version": "0.1.0", "description": "Tiny ms conversion utility", "main": "./ms", "devDependencies": {"mocha": "*", "expect.js": "*", "serve": "*"}, "_npmUser": {"name": "rauchg", "email": "<EMAIL>"}, "_id": "ms@0.1.0", "dependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/ms/-/ms-0.1.0.tgz", "shasum": "f21fac490daf1d7667fd180fe9077389cc9442b2", "size": 81920, "noattachment": false, "integrity": "sha512-7uwYj3Xip4rOFpe5dDy+C25Ad0nAXkT4yAVMSpuh1UYR2Z7tAswSh4wb/HghRa533wofFUsvg54OQ90Mu1dCJg=="}, "maintainers": [{"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "bme<PERSON>", "email": "blake<PERSON><PERSON>@gmail.com"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "codybrouwers", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "dferber90", "email": "<EMAIL>"}, {"name": "ebb-tide", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "f3d0r", "email": "<EMAIL>"}, {"name": "falcoagustin", "email": "<EMAIL>"}, {"name": "gdborton", "email": "<EMAIL>"}, {"name": "geovanisouza92", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hungrybearstudio", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "kdy1", "email": "<EMAIL>"}, {"name": "kelly", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "msimulcik", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "southpolesteve", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1324496306538, "_hasShrinkwrap": false, "_cnpm_publish_time": 1324496306538, "_cnpmcore_publish_time": "2021-12-13T06:24:21.867Z"}}, "bugs": {"url": "https://github.com/vercel/ms/issues"}, "homepage": "https://github.com/vercel/ms#readme", "repository": {"type": "git", "url": "git+https://github.com/vercel/ms.git"}, "_source_registry_name": "default"}