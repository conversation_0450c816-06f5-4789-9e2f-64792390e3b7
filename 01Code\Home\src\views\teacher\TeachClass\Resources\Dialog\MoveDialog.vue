<template>
  <div class="dialog-mask" @click.self="close">
    <div class="dialog">
      <div class="dialog-header">
        <span class="title">移动到 (已选择 {{ selectedItems.length }} 项)</span>
        <span class="close" @click="close">✕</span>
      </div>

      <div class="dialog-body">
        <ul class="group-list">
          <!-- 根文件夹 -->
          <li :class="{
            'selected-folder': selectedGroup.id === rootFolder.id,
            'all-group': true
          }" @click="selectGroup(rootFolder)">
            <span class="toggle-icon" @click.stop="toggleExpand(rootFolder)">
              {{ rootFolder.expanded ? '▼' : '▶' }}
            </span>
            {{ rootFolder.name }}
          </li>

          <!-- 递归渲染子文件夹 -->
          <template v-if="rootFolder.expanded && rootFolder.children">
            <template v-for="child in rootFolder.children" :key="child.id">
              <tree-node v-if="child.type === 'folder'" 
                        :node="child" 
                        :selected-group="selectedGroup" 
                        :depth="1"
                        @select="selectGroup" 
                        @toggle="toggleExpand" 
                        @create-folder="handleCreateFolder" />
            </template>
          </template>

          <!-- 新建文件夹输入框 -->
          <li v-if="isCreatingFolder" class="new-folder-item"
              :style="{ paddingLeft: `${createFolderDepth * 16 + 32}px` }">
            <div class="inline-input">
              <input ref="folderNameInput" v-model="newFolderName" placeholder="输入文件夹名称" v-autofocus
                     @keyup.enter="confirmCreate" />
              <button class="confirm-btn" @click="confirmCreate">✓</button>
              <button class="cancel-btn" @click="cancelCreate">✗</button>
            </div>
          </li>
        </ul>
      </div>

      <div class="dialog-footer">
        <button v-if="!isCreatingFolder" class="add-folder"
                @click="startCreateFolder(selectedGroup.value || rootFolder)">
          + 新建文件夹
        </button>
        <button class="cancel" @click="close">取消</button>
        <button class="confirm" @click="handleMoveConfirm" :disabled="!canMove || isMoving">
          {{ isMoving ? '移动中...' : '移动' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, watchEffect } from 'vue'
import { usePublicResourceStore } from '@/stores/public/teacher/publicResStore'
import { ElMessage } from 'element-plus'
import TreeNode from '@/components/resource/TreeNode.vue' 

const resourceStore = usePublicResourceStore()
const props = defineProps({
  currentFolder: {
    type: Object,
    required: true,
    default: () => ({
      id: 'root',
      name: '全部文档',
      type: 'folder',
      children: []
    })
  },
  selectedItems: {
    type: Array,
    default: () => [],
    validator: items => items.every(item => item.id && item.type)
  }
})

const emit = defineEmits(['close', 'confirm', 'folder-created', 'update:currentFolder'])

const selectedGroup = ref({ id: null })
const isCreatingFolder = ref(false)
const newFolderName = ref('')
const creatingParent = ref(null)
const createFolderDepth = ref(0)
const isMoving = ref(false)

const rootFolder = ref({
  id: 'root',
  name: '全部文档',
  type: 'folder',
  children: [],
  expanded: true
})


onMounted(async () => {
  await resourceStore.fetchFullFolderTree()
  
  // 递归设置所有文件夹的type属性
  const processFolders = (folders) => {
    return folders.map(folder => {
      // 显式设置type为folder
      const processedFolder = { 
        ...folder, 
        type: 'folder',
        // 确保expanded属性正确
        expanded: true,
        // 递归处理子文件夹
        children: folder.children ? processFolders(folder.children) : []
      }
      return processedFolder
    })
  }
  
  rootFolder.value.children = processFolders(resourceStore.fullFolderTree || [])
  
  selectGroup(props.currentFolder)
  expandAllFolders(rootFolder.value)
})

const vAutofocus = {
  mounted(el) {
    el.focus()
  }
}

const canMove = computed(() => {
  return selectedGroup.value.id && selectedGroup.value.id !== props.currentFolder.id
})

// 开始创建文件夹
const startCreateFolder = (parentFolder) => {
  isCreatingFolder.value = true
  newFolderName.value = ''
  creatingParent.value = parentFolder
  createFolderDepth.value = getFolderDepth(parentFolder)
  if (parentFolder && !parentFolder.expanded) {
    parentFolder.expanded = true
  }
}

// 获取文件夹的深度
const getFolderDepth = (folder, currentDepth = 0) => {
  if (!folder) return currentDepth
  if (folder.id === props.currentFolder.id) {
    return currentDepth
  }
  return currentDepth + 1
}

// 自动展开所有文件夹的函数
function expandAllFolders(folder) {
  folder.expanded = true
  if (folder.children) {
    folder.children.forEach(child => {
      if (child.type === 'folder') {
        expandAllFolders(child)
      }
    })
  }
}

// 监听 currentFolder 变化，自动展开所有文件夹
watchEffect(() => {
  if (props.currentFolder) {
    expandAllFolders(props.currentFolder)
  }
})

// 处理来自TreeNode的创建文件夹请求
const handleCreateFolder = (parentFolder) => {
  startCreateFolder(parentFolder)
}

// 确认创建
const confirmCreate = async () => {
  const name = newFolderName.value.trim()
  if (!name) {
    ElMessage.warning('请输入文件夹名称')
    return
  }

  try {
    const newFolder = await resourceStore.createFolder(
      name,
      creatingParent.value.id === 'root' ? null : creatingParent.value.id
    )

    if (!creatingParent.value.children) {
      creatingParent.value.children = []
    }
    creatingParent.value.children.unshift({
      id: newFolder.id,
      name: newFolder.name,
      type: 'folder',
      children: [],
      size: '--',
      modifiedTime: new Date(newFolder.createdTime).toLocaleString(),
      parent: creatingParent.value.id,
      expanded: false
    })

    ElMessage.success('文件夹创建成功')
    isCreatingFolder.value = false
    selectGroup(creatingParent.value.children[0])
    emit('folder-created', {
      parentId: creatingParent.value.id,
      newFolder: creatingParent.value.children[0]
    })
  } catch (error) {
    ElMessage.error(`文件夹创建失败: ${error.message}`)
  }
}

// 取消创建
const cancelCreate = () => {
  isCreatingFolder.value = false
  creatingParent.value = null
}

const handleMoveConfirm = async () => {
  if (!canMove.value) return;

  try {
    isMoving.value = true;

    for (const item of props.selectedItems) {
      if (item.type === 'folder') {
        // 文件夹移动
        await resourceStore.updateFolderInfo({
          id: item.id,
          parentId: selectedGroup.value.id
        });
      } else {
        // 文件移动
        await resourceStore.updateResourceLocationInfo({
          id: item.id,
          folderId: selectedGroup.value.id
        });
      }
      updateItemInFolderTree(props.currentFolder, item.id, {
        [item.type === 'folder' ? 'parentId' : 'folderId']: selectedGroup.value.id
      });
    }
    ElMessage.success(`移动成功`);
    emit('confirm');
    close();
  } catch (error) {
    ElMessage.error(`移动失败: ${error.message}`);
  } finally {
    isMoving.value = false;
  }
};

/**
 * 递归查找并更新文件夹树中的项目
 * @param {Object} folder 当前文件夹
 * @param {string} itemId 要更新的项目ID
 * @param {Object} updates 要应用的更新
 * @returns {boolean} 是否找到并更新了项目
 */
function updateItemInFolderTree(folder, itemId, updates) {
  if (!folder.children) return false

  for (let i = 0; i < folder.children.length; i++) {
    const child = folder.children[i]

    if (child.id === itemId) {
      folder.children[i] = { ...child, ...updates }
      return true
    }

    if (child.type === 'folder' && child.children) {
      const found = updateItemInFolderTree(child, itemId, updates)
      if (found) return true
    }
  }

  return false
}

// 根据ID查找文件夹
const findFolderById = (folder, id) => {
  if (folder.id === id) return folder
  if (!folder.children) return null

  for (const child of folder.children) {
    const found = findFolderById(child, id)
    if (found) return found
  }
  return null
}

// 查找父文件夹
const findParentFolder = (folder, childId, parent = null) => {
  if (folder.id === childId) return parent
  if (!folder.children) return null

  for (const child of folder.children) {
    const found = findParentFolder(child, childId, folder)
    if (found) return found
  }
  return null
}

function toggleExpand(folder) {
  folder.expanded = !folder.expanded
}

function selectGroup(folder) {
  if (folder.type !== 'folder') return
  selectedGroup.value = {
    id: folder.id,
    name: folder.name
  }
   console.log('当前选中:', selectedGroup.value) // 调试用
}

// 关闭对话框
const close = () => {
  isCreatingFolder.value = false
  creatingParent.value = null
  emit('close')
}
</script>

<style lang="scss" scoped>
.dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;
}

.dialog {
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 10px;
  width: 500px;
  height: 500px;
  padding: 0;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);
}

.dialog-header {
  padding: 16px;
  font-weight: bold;
  font-size: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;

  .close {
    cursor: pointer;
    font-size: 18px;
    color: #999;
  }
}

.dialog-body {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  max-height: calc(500px - 100px);
}

.group-list {
  list-style: none;
  padding: 0;
  margin: 0;

  li {
    padding: 10px 14px;
    margin-bottom: 6px;
    border-radius: 4px;
    cursor: pointer;
    background: none;
    border: none;
    align-items: center;
    transition: all 0.2s ease;

    &:hover {
      .node-content {
        background-color: #f0f5ff;
      }
    }

    &.selected-folder {
      .node-content {
        background-color: #e6eeff;
        border-left: 3px solid #4c7bff;
        font-weight: 500;
        color: #2c3e50;
      }
    }

    &.all-group {
      font-weight: 500;
    }
  }
}

.toggle-icon {
  display: inline-block;
  width: 20px;
  text-align: center;
  margin-right: 5px;
  font-size: 12px;
  cursor: pointer;
}

.new-folder-item {
  background-color: #f8faff;
  border-left: 3px dashed #4c7bff;
  margin: 8px 0;
}

.inline-input {
  display: flex;
  align-items: center;
  gap: 5px;
  width: 100%;

  input {
    flex: 1;
    padding: 6px 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    outline: none;

    &:focus {
      border-color: #409eff;
    }
  }
}

.confirm-btn,
.cancel-btn {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  background: transparent;
  transition: background 0.2s;
}

.confirm-btn {
  color: #67c23a;

  &:hover {
    background: #f0f9eb;
  }
}

.cancel-btn {
  color: #f56c6c;

  &:hover {
    background: #fef0f0;
  }
}

.dialog-footer {
  display: flex;
  flex-shrink: 0;
  justify-content: flex-end;
  gap: 10px;
  padding: 12px 16px;
  border-top: 1px solid #eee;

  button {
    padding: 6px 14px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .cancel {
    background: #f1f1f1;

    &:hover {
      background: #e0e0e0;
    }
  }

  .confirm {
    background: #4c7bff;
    color: white;

    &:hover:not(:disabled) {
      background: #3a6bef;
    }

    &:disabled {
      background: #cccccc;
      cursor: not-allowed;
    }
  }

  .add-folder {
    color: #4c7bff;
    font-weight: 500;
    margin-right: auto;
    background: transparent;

    &:hover {
      background-color: #e6eeff;
    }
  }
}
</style>