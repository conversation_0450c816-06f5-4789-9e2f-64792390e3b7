<!--src/components/teacher/AnswerList.vue-->
<template>
  <div class="answer-list">
    <h3 class="answer-list-title">
      <i class="iconfont icon-comment"></i>
      全部回答（共 {{ totalAnswers }} 条）
    </h3>
    
    <!-- 回答列表 -->
    <div class="answer-items">
      <AnswerItem 
        v-for="item in answerList" 
        :key="item.id"
        :answer="item"
        :comments-per-page="commentsPerPage"
        @like="handleLike"
        @toggle-comment="handleToggleComment"
        @submit-comment="handleSubmitComment"
        @reply-comment="handleReplyComment"
        @delete-comment="handleDeleteComment"
        @edit-comment="handleEditComment"
        @star="handleStar"
      />
    </div>
    
    <!-- 分页控件 -->
    <div class="answer-pagination" v-if="totalPages > 1">
      <button 
        class="page-btn" 
        :disabled="currentPage === 1"
        @click="goToPage(1)"
      >
        首页
      </button>
      <button 
        class="page-btn" 
        :disabled="currentPage === 1"
        @click="goToPage(currentPage - 1)"
      >
        上一页
      </button>
      
      <span class="page-info">
        第 {{ currentPage }} 页 / 共 {{ totalPages }} 页
      </span>
      
      <button 
        class="page-btn" 
        :disabled="currentPage === totalPages"
        @click="goToPage(currentPage + 1)"
      >
        下一页
      </button>
      <button 
        class="page-btn" 
        :disabled="currentPage === totalPages"
        @click="goToPage(totalPages)"
      >
        末页
      </button>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import AnswerItem from './AnswerItem.vue'

const props = defineProps({
  answerList: {
    type: Array,
    required: true,
    default: () => []
  },
  currentAnswerPage: {
    type: Number,
    default: 1
  },
  answersPerPage: {
    type: Number,
    default: 10
  },
  totalAnswers: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits([
  'like',
  'toggle-comment',
  'submit-comment',
  'reply-comment',
  'delete-comment',
  'edit-comment',
  'update:currentAnswerPage',
  'refresh-answers'
])

// 使用计算属性直接获取当前页码，确保与父组件同步
const currentPage = computed({
  get() {
    return props.currentAnswerPage
  },
  set(newPage) {
    // 确保页码在有效范围内
    const validPage = Math.max(1, Math.min(newPage, totalPages.value))
    emit('update:currentAnswerPage', validPage)
  }
})

const commentsPerPage = props.answersPerPage

// 计算属性
const totalPages = computed(() => Math.ceil(props.totalAnswers / props.answersPerPage))

// 分页方法
const goToPage = (page) => {
  currentPage.value = page
}

// 事件处理方法
const handleLike = (data) => {
  emit('like', data)
}

const handleToggleComment = (answer) => {
  emit('toggle-comment', answer)
}

// 更新事件处理逻辑
// 修改 handleSubmitComment 方法
const handleSubmitComment = ({ answer, newComment, error }) => {
  if (error) {
    console.error('评论提交错误:', error)
    // 直接传递错误信息
    emit('submit-comment', { answer, error })
    return
  }
  
  // 直接转发原始数据，不要修改结构
  emit('submit-comment', { answer, newComment })
}

const handleReplyComment = (answer, comment) => {
  emit('reply-comment', answer, comment)
}

const handleEditComment = (payload) => {
  // 直接转发给父组件
  emit('edit-comment', payload)
}

const handleDeleteComment = (payload) => {
  console.group('[AnswerList] 删除评论事件')
  console.log('收到的payload:', payload)
  console.log('当前answerList:', props.answerList)
  emit('delete-comment', payload)
  console.groupEnd()
  if (payload.success) {
    emit('refresh-answers')
  }
}
</script>


<style scoped lang="scss">
@use "@/styles/teacher/CourseDetailManager/AnswerList";
</style>