<template>
  <div class="sign-in-container">
    <!-- 顶部导航栏 -->
    <div class="header">
      <!-- 左侧标签页 -->
      <div class="tabs">
        <span v-for="(tab, index) in tabs" :key="index" :class="['tab', { active: activeTab === tab.value }]"
          @click="setActiveTab(tab)">
          {{ tab.label }}
        </span>
      </div>

      <!-- 右侧操作按钮 -->
      <div class="actions">
        <button class="export-btn" @click="exportSignIn">
          导出签到
        </button>
        <button class="create-btn" @click="showCreateAttendanceModal = true">
          创建签到
        </button>
      </div>
    </div>

    <!-- 内容区域 - 移除滚动功能 -->
    <div class="content">
      <!-- 签到列表内容 -->
      <SignInContent v-if="activeTab !== '签到统计'" :sign-ins="filteredSignIns" @edit-sign-in="editSignIn"
        @delete-sign-in="deleteSignIn" />

      <!-- 统计页面 -->
      <StatisticsView v-else />
    </div>
  </div>
  <CreateAttendanceDialog :visible="showCreateAttendanceModal" mode="create" @submit="loadAttendanceList"
    @cancel="showCreateAttendanceModal = false" />
  <CreateAttendanceDialog :visible="showEditDialog" mode="edit" :edit-data="currentEditData" @submit="loadAttendanceList"
    @cancel="showEditDialog = false" />
</template>

<script setup>
import { ref } from 'vue';
import SignInContent from './components/SignInContent.vue';
import StatisticsView from './components/StatisticsView.vue';
import CreateAttendanceDialog from './components/CreateAttendanceDialog.vue';
import { fetchAttendanceList, deleteAttendance } from '@/api/teacher/attendance'
import { formatTime } from '@/utils/dateUtils';

const signIns = ref([]); // 替换为从API获取的数据
const filteredSignIns = ref([]);

const showCreateAttendanceModal = ref(false);
const showEditDialog = ref(false);
const currentEditData = ref(null);

// 标签页配置
const tabs = [
  { label: '全部签到', value: '全部签到' },
  { label: '正在签到', value: '正在签到' },
  { label: '历史签到', value: '历史签到' },
  { label: '暂未开始', value: '暂未开始' },
  { label: '签到统计', value: '签到统计' }
];
const activeTab = ref('全部签到');


// 获取签到列表数据
const loadAttendanceList = async () => {
  try {
    const res = await fetchAttendanceList();
    signIns.value = res.result.map(item => ({
      id: item.id,
      name: item.name,
      status: getStatusName(item.status),
      statusClass: getStatusClass(item.status),
      progress: calculateProgress(item.checkedCount, item.totalCount),
      signedIn: item.checkedCount,
      total: item.totalCount,
      classIds: item.classIds,
      classNames: item.classNames,
      creator: item.teacherName,
      method: getMethodName(item.checkType),
      startTime: formatTime(item.startTime),
      endTime: formatTime(item.endTime)
    }));
    filterSignIns();
  } catch (error) {
    console.error('获取签到列表失败:', error);
  }
};

// 状态映射
const getStatusName = (status) => {
  const map = { 0: '未开始', 1: '进行中', 2: '已结束' };
  return map[status] || '未知状态';
};

const getStatusClass = (status) => {
  const map = { 0: 'status-pending', 1: 'status-active', 2: 'status-ended' };
  return map[status] || '';
};

// 签到方式映射
const getMethodName = (checkType) => {
  const map = { 0: '快捷签到', 1: '签到码签到' };
  return map[checkType] || '未知方式';
};

// 计算进度百分比
const calculateProgress = (checked, total) => {
  return total > 0 ? Math.round((checked / total) * 100) : 0;
};

// 设置当前标签页
const setActiveTab = (tab) => {
  activeTab.value = tab.value;
  filterSignIns();
};

// 根据标签筛选签到数据
const filterSignIns = () => {
  switch (activeTab.value) {
    case '全部签到':
      filteredSignIns.value = [...signIns.value];
      break;
    case '正在签到':
      filteredSignIns.value = signIns.value.filter(signIn => signIn.status === '进行中');
      break;
    case '历史签到':
      filteredSignIns.value = signIns.value.filter(signIn => signIn.status === '已结束');
      break;
    case '暂未开始':
      filteredSignIns.value = signIns.value.filter(signIn => signIn.status === '未开始');
      break;
    case '签到统计':
      filteredSignIns.value = [];
      break;
  }
};

// 编辑签到
const editSignIn = (signIn) => {
  currentEditData.value = signIn;
  showEditDialog.value = true;
};
const deleteSignIn = async (attendanceId) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除该签到吗？',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    await deleteAttendance(attendanceId);
    ElMessage.success('删除成功');
    await loadAttendanceList(); // 重新加载数据
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message);
    }
  }
};



const exportSignIn = () => {
  console.log('导出签到数据');
};




// 初始化数据
filterSignIns();

onMounted(() => {
  loadAttendanceList();
});
</script>

<style scoped lang="scss">
.sign-in-container {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  padding: 20px;
  margin: 0 auto;
  overflow: hidden;
  background-color: #ffffff;
  border-radius: 6px;
  height: 83vh;
  display: flex;
  flex-direction: column;
}

/* 顶部导航栏样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;

  .tabs {
    display: flex;
    gap: 6px;

    .tab {
      padding: 8px 16px;
      font-size: 14px;
      color: #666;
      cursor: pointer;
      border-radius: 4px;
      transition: all 0.3s;
      background: #f0f0f0;

      &:hover {
        color: #1890ff;
        background: #e6f7ff;
      }

      &.active {
        color: #ffffff;
        background: #1890ff;
        font-weight: 500;
      }
    }
  }

  .actions {
    display: flex;
    gap: 12px;

    button {
      padding: 8px 16px;
      font-size: 14px;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s;
      border: none;
    }

    .export-btn {
      background: #f5f5f5;
      color: #666;

      &:hover {
        background: #e8e8e8;
      }
    }

    .create-btn {
      background: #1890ff;
      color: white;

      &:hover {
        background: #40a9ff;
      }
    }
  }
}

/* 内容区域样式 */
.content {
  margin-top: 24px;
  overflow: hidden;
  flex: 1;
}
</style>