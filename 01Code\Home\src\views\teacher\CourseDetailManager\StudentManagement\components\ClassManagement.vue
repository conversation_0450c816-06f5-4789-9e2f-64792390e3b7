<template>
  <div class="class-management">
    <div class="class-search">
      <el-input
        :model-value="classSearchKeyword"
        @update:model-value="$emit('update:class-search-keyword', $event)"
        placeholder="请输入班级名称"
        class="class-search-input"
        suffix-icon="el-icon-search"
      ></el-input>
    </div>

    <div class="class-list">
      <div 
        class="class-item" 
        :class="{ selected: getClassName(classItem) === activeClass }"
        v-for="classItem in filteredClassNames" 
        :key="getClassName(classItem)"
        @click="selectClass(getClassName(classItem))"
      >
        <div class="class-item-content">
          {{ getClassName(classItem) }} ({{ getStudentCount(classItem) }})
          <el-popconfirm
            v-if="getClassName(classItem) !== '所有学生'"
            title="确定删除此班级吗？"
            @confirm="deleteClass(getClassName(classItem))"
          >
            <template #reference>
              <el-icon class="delete-icon" @click.stop>
                <Delete />
              </el-icon>
            </template>
          </el-popconfirm>
        </div>
      </div>
    </div>

    <div v-if="isNewClassMode">
      <div class="class-form">
        <input 
          type="text" 
          placeholder="请输入班级名称" 
          class="class-input" 
          :value="newClassName"
          @input="$emit('update:new-class-name', $event.target.value)"
        >
        <div class="button-group">
          <el-button type="primary" @click="saveClass">保存</el-button>
          <el-button @click="cancelClass">取消</el-button>
        </div>
      </div>
    </div>
    <div v-else>
      <el-button class="new-class-button" @click="toggleNewClassMode">+ 新建班级</el-button>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue';
import { ElInput, ElButton, ElPopconfirm, ElMessage } from 'element-plus';
import { Delete } from '@element-plus/icons-vue';
import { useStudentStore } from '@/stores/teacher/studentStore';

export default {
  components: {
    ElInput,
    ElButton,
    ElPopconfirm,
    Delete
  },
  props: {
    classSearchKeyword: {
      type: String,
      required: true
    },
    classNames: {
      type: Array,
      required: true,
      default: () => ['所有学生']
    },
    isNewClassMode: {
      type: Boolean,
      required: true
    },
    newClassName: {
      type: String,
      required: true
    },
    students: {
      type: Array,
      required: true
    },
    activeClass: {
      type: String,
      default: ''
    },
    activeTab: {
      type: String,
      default: '已入班'
    }
  },
  emits: [
    'update:class-search-keyword', 
    'toggle-new-class-mode', 
    'save-class', 
    'cancel-class', 
    'update:new-class-name', 
    'class-selected',
    'delete-class'
  ],
  setup(props, { emit }) {
    // 辅助方法：获取班级名称（兼容字符串和对象）
    const getClassName = (classItem) => {
      return typeof classItem === 'string' ? classItem : classItem.name;
    };

    // 过滤班级列表（支持搜索）
    const filteredClassNames = computed(() => {
      if (!props.classSearchKeyword) return props.classNames;
      return props.classNames.filter(classItem => {
        const className = getClassName(classItem);
        return className.includes(props.classSearchKeyword);
      });
    });

    // 获取班级学生数量
    const getStudentCount = (classItem) => {
      const className = getClassName(classItem);
      
      // 处理"所有学生"特殊情况
      if (className === '所有学生') {
        return props.students.filter(student => {
          return student.status === (
            props.activeTab === '已入班' ? 2 : 
            props.activeTab === '待审核' ? 1 : 
            props.activeTab === '待入班' ? 0 : 
            props.activeTab === '已拒绝/已移除' ? 3 : null
          );
        }).length;
      }
      
      // 处理具体班级（直接从班级对象获取studentCount）
      if (typeof classItem === 'object' && classItem.studentCount !== undefined) {
        return classItem.studentCount;
      }
      
      // 回退方案：通过students数据计算（兼容旧数据）
      return props.students.filter(student => 
        student.className === className && 
        student.status === (
          props.activeTab === '已入班' ? 2 : 
          props.activeTab === '待审核' ? 1 : 
          props.activeTab === '待入班' ? 0 : 
          props.activeTab === '已拒绝/已移除' ? 3 : null
        )
      ).length;
    };

    // 选择班级
    const selectClass = (className) => {
      emit('class-selected', className);
    };

    // 切换新建班级模式
    const toggleNewClassMode = () => {
      emit('toggle-new-class-mode');
    };

    // 保存班级
    const saveClass = () => {
      emit('save-class');
    };

    // 删除班级
    const deleteClass = (className) => {
      emit('delete-class', className);
    };

    // 取消新建
    const cancelClass = () => {
      emit('cancel-class');
    };

    return {
      getClassName,
      filteredClassNames,
      getStudentCount,
      selectClass,
      toggleNewClassMode,
      saveClass,
      deleteClass,
      cancelClass
    };
  }
};
</script>

<style scoped>
.class-management {
  width: 100%;
}

.class-search-input {
  width: 100%;
  margin-bottom: 15px;
}

.class-form {
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 15px;
}

.class-input {
  width: 100%;
  padding: 8px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  margin-bottom: 10px;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.class-list {
  margin-top: 15px;
}

.class-item {
  padding: 8px;
  margin-bottom: 5px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.class-item:hover {
  background-color: #f5f7fa;
}

.class-item.selected {
  color: #409EFF;
  font-weight: bold;
}

.new-class-button {
  border: 1px solid #409EFF; 
  color: #409EFF; 
}

.class-item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.delete-icon {
  color: #f56c6c;
  cursor: pointer;
  margin-left: 8px;
}

.delete-icon:hover {
  color: #ff0000;
}
</style>