<!-- ExamPreviewDialog.vue -->
<template>
    <div class="exam-preview">
        <h2>{{ examData.title }}</h2>
        <p style="color: #666;">{{ examData.description }}</p>

        <div style="margin: 10px 0;">
            <p v-if="examData.examStart">
                <strong>考试开始：</strong>{{ formatTime(examData.examStart) }}
            </p>
            <p v-if="examData.examEnd">
                <strong>考试结束：</strong>{{ formatTime(examData.examEnd) }}
            </p>
            <p v-if="examData.deadline">
                <strong>截止时间：</strong>{{ formatTime(examData.deadline) }}
            </p>
            <p v-if="examData.examDuration">
                <strong>考试时长：</strong>{{ examData.examDuration }} 分钟
            </p>
            <p>
                <strong>总分：</strong>{{ examData.totalScore }} 分
            </p>
        </div>

        <div v-for="(q, index) in examData.questions" :key="q.id || index"
            style="margin-bottom: 20px; padding-bottom: 12px; border-bottom: 1px solid #eee;">
            <p>
                <strong>{{ index + 1 }}. [{{ getQuestionType(q) }}]</strong>
                {{ q.content }}
            </p>

            <ul v-if="q.options" style="list-style: none; padding-left: 0; margin-left: 16px;">
                <li v-for="(val, key) in parseOptions(q.options)" :key="key">
                    {{ key }}：{{ val }}
                </li>
            </ul>

            <p v-if="q.correct"><strong>答案：</strong>{{ formatAnswer(q.correct) }}</p>
            <p v-if="q.answer !== undefined">
                <strong>答案：</strong>{{ q.answer ? '正确' : '错误' }}
            </p>
            <p v-if="q.answers"><strong>答案：</strong>{{ formatAnswer(q.answers) }}</p>
            <p v-if="q.sampleAnswer"><strong>参考答案：</strong>{{ q.sampleAnswer }}</p>
        </div>
    </div>
</template>
  
<script setup>
const props = defineProps({
    examData: Object,
});

const parseOptions = (options) => {
    try {
        return typeof options === 'string' ? JSON.parse(options || '{}') : options || {};
    } catch {
        return {};
    }
};

const getQuestionType = (q) => {
    if (q.options && typeof q.correct === 'string') return '单选题';
    if (q.options && q.correct?.startsWith('[')) return '多选题';
    if ('answer' in q) return '判断题';
    if ('answers' in q) return '填空题';
    if ('sampleAnswer' in q) return '问答题';
    return '未知';
};

const formatAnswer = (ans) => {
    if (!ans) return '';
    try {
        const parsed = JSON.parse(ans);
        if (Array.isArray(parsed)) {
            return parsed.join(', ');
        }
        return parsed;
    } catch {
        return ans;
    }
};

const formatTime = (ts) => {
    if (!ts) return '';
    return new Date(ts).toLocaleString();
};
</script>
  