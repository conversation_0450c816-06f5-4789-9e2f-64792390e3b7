<template>
  <ElDialog 
    v-model="dialogVisible"
    title="课程数据导出" 
    width="500px"
  >
    <div class="export-options">
      <p class="mb-4 text-gray-700">选择你要导出的数据类型</p>
      <ElCheckboxGroup v-model="selectedTypes">
        <ElCheckbox 
          v-for="option in exportOptions" 
          :key="option.value" 
          :label="option.value"
          class="mb-2 block"
        >
          {{ option.label }}
        </ElCheckbox>
      </ElCheckboxGroup>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <ElButton @click="closeDialog">取消</ElButton>
        <ElButton 
          type="primary" 
          :disabled="selectedTypes.length === 0"
          @click="handleConfirm"
        >
          确认导出
        </ElButton>
      </span>
    </template>
  </ElDialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emits = defineEmits(['update:visible', 'confirm'])

const selectedTypes = ref([])
const exportOptions = [
  { label: '课程运行数据', value: 'courseOperation' },
  { label: '成绩详情', value: 'scoreDetail' },
  { label: '作业情况', value: 'homeworkStatus' },
  { label: '考试详情', value: 'examDetail' },
  { label: '学习资源详情', value: 'resourceDetail' }
]

// 使用计算属性处理双向绑定
const dialogVisible = computed({
  get() {
    return props.visible
  },
  set(val) {
    emits('update:visible', val)
  }
})

const closeDialog = () => {
  dialogVisible.value = false
}

const handleConfirm = () => {
  emits('confirm', [...selectedTypes.value])
  selectedTypes.value = []
  closeDialog()
}

// 监听visible变化，清空选中状态
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    selectedTypes.value = []
  }
})
</script>

<style scoped>
:deep(.el-dialog) {
  z-index: 9999 !important;
  margin-top: 15vh !important;  /* 调整垂直位置 */
  display: flex;
  flex-direction: column;
  margin: auto !important;
}

/* 弹窗内容居中 */
:deep(.el-dialog__body) {
  text-align: center;
}
.export-options {
  padding: 20px 0;
}
</style>