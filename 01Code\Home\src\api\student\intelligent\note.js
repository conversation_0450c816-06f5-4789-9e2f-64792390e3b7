import OpenAI from 'openai';
import { getKey } from '../../key.js';

// 为代码生成中文注释
export const generateCodeComment = async (code, language) => {
  const deepseekApiKey = getKey('deepseek');
  const openai = new OpenAI({
    baseURL: 'https://api.deepseek.com',
    apiKey: deepseekApiKey,
    dangerouslyAllowBrowser: true
  });

  try {
    const completion = await openai.chat.completions.create({
      model: 'deepseek-chat',
      messages: [
        {
          role: 'system',
          content: `你是一个专业的代码注释工具，请为以下 ${language} 代码生成详细中文注释：
            - 使用清晰的中文注释
            - 注释需要简洁明了，解释代码功能
            - 根据代码复杂程度，分块注释关键逻辑
            - 注释风格符合 ${language} 语言的习惯
            - 对变量、函数、类等进行解释
            - 对代码输入输出进行说明
            - 对复杂逻辑提供简要说明
            - 不需要重复代码本身
            - 每行注释不要过长，保持阅读友好性
            - 不要添加示例外的代码
            - 使用 ${language === 'python' ? '# 注释内容' : '// 注释内容'} 注释格式`
        },
        {
          role: 'user',
          content: `请为以下代码生成注释：
            \`\`\`${language}
            ${code}
            \`\`\``,
        },
      ],
      temperature: 0.5,
      max_tokens: 1000,
    });

    return completion.choices[0].message.content;
  } catch (error) {
    console.error('注释生成失败:', error);
    throw error;
  }
};

export const embedCommentsIntoCode = (originalCode, comments) => {
  const commentLines = comments.split('\n').filter(line => line.trim() !== '');
  const codeLines = originalCode.split('\n');

  const combinedLines = [];
  let commentIndex = 0;

  for (let i = 0; i < codeLines.length; i++) {
    const codeLine = codeLines[i].trim();

    // 如果当前代码行非空，则添加注释
    if (codeLine && commentIndex < commentLines.length) {
      combinedLines.push(commentLines[commentIndex]);
      commentIndex++;
    }
    combinedLines.push(codeLines[i]);
  }

  // 如果注释还有剩余，直接追加到代码末尾
  if (commentIndex < commentLines.length) {
    combinedLines.push(...commentLines.slice(commentIndex));
  }
  return combinedLines.join('\n');
};

// 代码评价
export const getCodeEvaluation = async (code, language) => {
  const deepseekApiKey = getKey('deepseek');
  const openai = new OpenAI({
    baseURL: 'https://api.deepseek.com',
    apiKey: deepseekApiKey,
    dangerouslyAllowBrowser: true
  });

  try {
    const completion = await openai.chat.completions.create({
      model: 'deepseek-chat',
      messages: [
        {
          role: 'system',
          content: `你是一个专业的代码评审工具，请对以下 ${language} 代码进行多维度评价，包括：
            - 代码规范性
            - 代码风格
            - 代码复杂度
            - 代码安全性
            - 性能优化
            - 改进建议

            请以清晰的中文段落返回评价结果，使用以下结构：

            【代码规范性】
            评价内容...

            【代码风格】
            评价内容...

            【代码复杂度】
            评价内容...

            【代码安全性】
            评价内容...

            【性能优化】
            评价内容...

            【改进建议】
            建议内容...
            `
        },
        {
          role: 'user',
          content: `请对以下代码进行评价：
            \`\`\`${language}
            ${code}
            \`\`\``,
        },
      ],
      temperature: 0.5,
      max_tokens: 1000,
    });

    // 直接返回文本格式的评价结果
    return completion.choices[0].message.content;
  } catch (error) {
    console.error('代码评价失败:', error);
    throw error;
  }
};
