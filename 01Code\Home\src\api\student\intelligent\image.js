import axios from 'axios';
import { get<PERSON><PERSON> } from '../../key.js';

const aliyunApiKey = getKey('deepseek');

// 提交图像编辑任务
// export async function submitEditTask(baseImageUrl, prompt, functionType) {
//   try {
//     const response = await axios.post(
//       'https://dashscope.aliyuncs.com/api/v1/services/aigc/image2image/image-synthesis',
//       {
//         model: 'wanx2.1-imageedit',
//         input: {
//           function: functionType,
//           prompt: prompt,
//           base_image_url: baseImageUrl,
//         },
//         parameters: {
//           n: 1,
//         },
//       },
//       {
//         headers: {
//           'Content-Type': 'application/json',
//           'Authorization': `Bearer ${aliyunApiKey}`,
//           'X-DashScope-Async': 'enable',
//         },
//       }
//     );
//
//     return response.data.output.task_id;
//   } catch (error) {
//     console.error('提交任务失败:', error);
//     throw error;
//   }
// }
export async function submitEditTask(baseImageUrl, prompt, functionType) {
  try {
    console.log('准备提交阿里云任务:', { baseImageUrl, prompt, functionType });

    const payload = {
      model: 'wanx2.1-imageedit',
      input: {
        function: functionType,
        prompt: prompt,
        base_image_url: baseImageUrl,
      },
      parameters: {
        n: 1,
      },
    };

    console.log('请求载荷:', payload);

    const response = await axios.post(
      'https://dashscope.aliyuncs.com/api/v1/services/aigc/image2image/image-synthesis',
      payload,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${aliyunApiKey}`,
          'X-DashScope-Async': 'enable',
        },
      }
    );

    console.log('阿里云响应:', response.data);
    return response.data.output.task_id;
  } catch (error) {
    console.error('提交阿里云任务失败:', error);
    if (error.response) {
      console.error('阿里云错误响应:', error.response.data);
    }
    throw error;
  }
}
// 查询任务状态
export async function getTaskResult(taskId) {
  try {
    const response = await axios.get(
      `https://dashscope.aliyuncs.com/api/v1/tasks/${taskId}`,
      {
        headers: {
          'Authorization': `Bearer ${aliyunApiKey}`,
        },
      }
    );

    return response.data.output;
  } catch (error) {
    console.error('查询任务结果失败:', error);
    throw error;
  }
}

// 获取预设提示词
export function getPresetPrompt(operation) {
  const presets = {
    globalStyle: '',
    localStyle: '',
    edit: '',
    redraw: '',
    removeText: '',
    expand: '',
    colorize: '',
    sketch: ''
  };

  return presets[operation] || '';
}
