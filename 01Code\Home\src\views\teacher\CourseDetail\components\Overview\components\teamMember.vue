<template>
  <div class="person-info-container">
    <img :src="avatar" alt="人物头像" class="avatar" />
    <div class="info-wrapper">
      <div class="name-tags-container">
        <h3 class="name">{{ name }}</h3>
        <div class="tags">
          <span 
            v-for="(tag, index) in tags" 
            :key="index" 
            :class="['tag', `tag-${tag.type}`]"
          >
            {{ tag.text }}
          </span>
        </div>
      </div>
      <p class="title">{{ title }}</p>
      <p class="desc">{{ desc }}</p>
      <button class="more-btn" @click="handleShowMore">查看更多</button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
const props = defineProps({
  name: { type: String, required: true },
  avatar: { type: String, required: true },
  tags: { type: Array, default: () => [] },
  title: { type: String, required: true },
  desc: { type: String, required: true }
});

const handleShowMore = () => {
  console.log('查看更多逻辑触发');
};
</script>

<style scoped lang="scss">
// 定义SCSS变量
$primary-border: #eee;
$border-radius: 8px;
$padding-base: 16px;
$spacing-sm: 8px;
$spacing-md: 12px;
$spacing-lg: 16px;
$font-size-name: 18px;
$font-size-tag: 12px;
$font-size-title: 14px;
$font-size-desc: 13px;

.person-info-container {
  display: flex;
  flex-direction: column;
  padding: $padding-base;
  border: 1px solid $primary-border;
  border-radius: $border-radius;
  width: 300px;
  max-width: 100%;
  
  .avatar {
    width: 6vw;
    height: 8vw;
    border-radius: 0.5vw;
    margin: 0 auto $spacing-md;
  }
  
  .info-wrapper {
    width: 100%;
    
    // 姓名与标签容器（关键布局调整）
    .name-tags-container {
      display: flex;
      flex-wrap: nowrap; // 允许标签换行
      justify-content: center;
      
      
      .name {
        margin: 0 $spacing-md 0 0; // 右侧留间距容纳标签
        font-size: $font-size-name;
        font-weight: bold;
      }
      
      .tags {
        display: flex;
        flex-wrap: wrap; // 标签超出容器时换行
        gap: $spacing-sm;
        margin-bottom: $spacing-md;
        
        .tag {
          padding: 4px 8px;
          border-radius: 4px;
          color: #fff;
          font-size: $font-size-tag;
        }
      }
    }
    
    .title {
      margin: 0 0 $spacing-sm 0;
      color: #666;
      font-size: $font-size-title;
      text-align: center;
    }
    
    .desc {
      margin: 0 0 $spacing-lg 0;
      line-height: 1.5;
      font-size: $font-size-desc;
      text-align: left;
      
      // 简介最多显示6行
      display: -webkit-box;
      -webkit-line-clamp: 6;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    .more-btn {
      background-color: #007bff;
      color: #fff;
      border: none;
      padding: 6px 12px;
      border-radius: 4px;
      cursor: pointer;
      display: block;
      width: 100%;
      text-align: center;
      font-size: 14px;
      
      &:hover {
        background-color: #0056b3;
      }
    }
  }
}

// 标签类型样式
.tag-course { background-color: #c3e6cb; color: #216832; }
.tag-academic { background-color: #bee5eb; color: #1e5a69; }
.tag-teacher { background-color: #f8d7da; color: #721c24; }
</style>