<template>
  <div class="sign-in-list-container">
    <!-- 签到列表 -->
    <div class="sign-in-list">
      <div v-for="(signIn, index) in signIns" :key="index" class="sign-in-card" :class="signIn.statusClass"
        @click="viewSignInDetail(signIn)">
        <div class="card-header">
          
          <h3 class="title">{{ signIn.name }}</h3>
          <span class="status-badge" :class="signIn.statusClass">{{ signIn.status }}</span>
        </div>

        <div class="progress-container">
          <span class="progress-label">签到人数：</span>
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: signIn.progress + '%' }"></div>
          </div>
          <span class="progress-text">已签到{{ signIn.signedIn }} / 应签到{{ signIn.total }}</span>
        </div>

        <!-- 合并为一行的详情和操作区 -->
        <div class="card-info">
          <div class="card-details">
            <div class="detail-item">
              <span>发起人：{{ signIn.creator }}</span>
            </div>
            <div class="detail-item">
              <span>签到方式：{{ signIn.method }}</span>
            </div>
            <div class="detail-item">
              <span>{{ signIn.startTime }} - {{ signIn.endTime }}</span>
            </div>
          </div>

          <div class="card-actions">
            <button class="edit-btn"  v-if="signIn.statusClass === 'status-active'" @click.stop="$emit('edit-sign-in', signIn)">
              编辑
            </button>
            <button class="delete-btn" @click.stop="handleDelete(signIn)">
              删除
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>


<script setup>
import { defineProps, defineEmits } from 'vue';
import { useRouter } from 'vue-router'

const router = useRouter()

// 接收签到数据
const props = defineProps({
  signIns: {
    type: Array,
    required: true
  }
});

const viewSignInDetail = (signIn) => {
  router.push({
    name: 'SignInDetail',
    params: { id: signIn.id },  // 签到ID（必须）
    query: {
      signInName: signIn.name,  // 签到名称（可选）
    }
  })
}

const handleDelete = (signIn) => {
  emits('delete-sign-in', signIn.id);
};

// 定义事件
const emits = defineEmits(['edit-sign-in', 'delete-sign-in']);
</script>

<style scoped lang="scss">
// 签到列表外部方框容器
.sign-in-list-container {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  height: 100%;
  overflow: auto;
  /* 允许滚动 */
}

.sign-in-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.sign-in-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s, box-shadow 0.3s;
  white-space: nowrap;
  /* 强制卡片内容不换行 */

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;

  .title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin: 0;
  }

  .status-badge {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 10px;
    border: 1px solid transparent;
  }

  // 状态标签颜色
  .status-active {
    background: #f6ffed;
    color: #52c41a;
    border-color: #b7eb8f;
  }

  .status-ended {
    background: #fff1f0;
    color: #f5222d;
    border-color: #ffa39e;
  }

  .status-pending {
    background: #fffbe6;
    color: #faad14;
    border-color: #ffe58f;
  }
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;

  .progress-label {
    font-size: 13px;
    color: #666;
    white-space: nowrap;
  }

  .progress-bar {
    flex: 1;
    height: 8px;
    background: #f5f5f5;
    border-radius: 4px;
    overflow: hidden;
    max-width: 30%;
  }

  .progress-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.6s ease;
    background: #1890ff;
  }

  .progress-text {
    font-size: 12px;
    color: #888;
    white-space: nowrap;
  }
}

// 详情和操作区的容器
.card-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

// 详情区域横向排列
.card-details {
  display: flex;
  gap: 20px;
  width: auto;
  margin-bottom: 0;

  .detail-item {
    font-size: 13px;
    color: #666;
    white-space: nowrap;
    /* 禁止详情内容换行 */

    &:not(:last-child)::after {
      content: "|";
      margin-left: 20px;
      color: #e0e0e0;
    }
  }
}

// 操作按钮区域
.card-actions {
  display: flex;
  gap: 8px;
  margin-left: 10px;

  button {
    padding: 4px 12px;
    font-size: 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    border: none;
    white-space: nowrap;
    /* 禁止按钮文字换行 */
  }

  .edit-btn {
    background: #e6f7ff;
    color: #1890ff;

    &:hover {
      background: #bae7ff;
    }
  }

  .delete-btn {
    background: #fff1f0;
    color: #f5222d;

    &:hover {
      background: #ffccc7;
    }
  }
}
</style>