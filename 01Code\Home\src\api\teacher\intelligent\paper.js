import axios from 'axios';
import { getKey } from '../../key.js';


// 生成试卷
export const generatePaper = async (documentText, paperType, questionCounts, searchExternal = false) => {
  try {
    const deepseekApiKey = getKey('deepseek');

    const systemPrompt = createSystemPrompt(paperType, questionCounts, searchExternal);

    const response = await axios.post(
      'https://api.deepseek.com/v1/chat/completions',  // 修正后的URL
      {
        model: 'deepseek-chat',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: documentText }
        ],
        max_tokens: 1000,
        temperature: 0.5
      },
      {
        headers: {
          'Authorization': `Bearer ${deepseekApiKey}`,
          'Content-Type': 'application/json'
        }
      }
    );

    return processResponse(response.data.choices[0].message.content);
  } catch (error) {
    console.error('生成试卷失败:', error);
    throw error;
  }
};

// 创建系统提示
const createSystemPrompt = (paperType, questionCounts, searchExternal) => {
  const { mcqCount, fillCount, essayCount } = questionCounts;

  let systemPrompt = `你是一位经验丰富的IT教师，需要根据提供的内容创建一份${paperType}试卷。试卷要求如下：

1. 题型包括：
   - 选择题 ${mcqCount} 道
   - 填空题 ${fillCount} 道
   - 简答题 ${essayCount} 道

2. 难度分布合理，覆盖核心知识点

3. 题目表述清晰，避免歧义

4. 试卷出完后在末尾附上每道题的答案

5. 试卷格式整齐规范，适合打印，并且使用中文格式`;

  if (searchExternal) {
    systemPrompt += `
6. 你可以参考网络上相关的${paperType}试卷题库，但必须确保题目与提供的内容相关`;
  }

  return systemPrompt;
};

// 处理 API 响应
const processResponse = (content) => {
  // 1. 移除###这样的标记符号
  let processed = content
    .replace(/^#{1,3}\s*/gm, '')      // 移除 ### 标题
    .replace(/\*\*(.*?)\*\*/g, '$1'); // 移除 **加粗** 标记

  // 2. 确保参考答案部分完整
  const answerIndex = processed.indexOf('参考答案');
  if (answerIndex !== -1) {
    // 保留从"参考答案"开始到结尾的所有内容
    processed = processed.substring(0, answerIndex) +
      processed.substring(answerIndex);
  }

  // 3. 检查是否有截断并尝试修复
  if (processed.indexOf('...') === processed.length - 3) {
    processed = processed.substring(0, processed.length - 3);
  }

  return processed;
};
