<!--src\views\teacher\CourseDetail\components\Stat.vue-->
<template>
  <div class="stat-container">
    <StatCard title="学习人数" value="0" unit="人" />
    <StatCard title="学习资源" value="0" unit="个" />
    <StatCard title="问答次数" value="0" unit="人次" />
    <StatCard title="作业考试" value="0" unit="次" />
    <StatCard title="课堂互动" value="0" unit="次" />
  </div>
</template>

<script setup>
import StatCard from './StatCard.vue'; // 引入子组件，注意路径根据实际存放位置调整
</script>

<style scoped lang="scss">
.stat-container {
  display: flex;
  gap: 12px; 
  padding: 4vw 4vw;
    > * {
    flex: 1; /* 所有卡片等宽 */
    min-width: 0; /* 防止内容溢出 */
  }
}

</style>