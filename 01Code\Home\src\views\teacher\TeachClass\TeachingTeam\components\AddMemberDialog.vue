<template>
  <el-dialog
    title="添加成员"
    :visible.sync="showDialog"
    width="60%"
  >
    <el-form :model="searchForm" @submit.native.prevent="handleSearch">
      <el-form-item label="教师姓名">
        <el-input v-model="searchForm.teacherName" placeholder="请输入教师姓名"></el-input>
      </el-form-item>
      <el-form-item label="工号">
        <el-input v-model="searchForm.teaCode" placeholder="请输入工号"></el-input>
      </el-form-item>
      <el-form-item label="学院">
        <el-input v-model="searchForm.department" placeholder="请输入学院"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="teachers" style="width: 100%">
      <el-table-column prop="teacherName" label="教师姓名" width="180"></el-table-column>
      <el-table-column prop="teaCode" label="工号" width="180"></el-table-column>
      <el-table-column prop="department" label="学院" width="180"></el-table-column>
      <el-table-column label="操作" width="120">
        <template slot-scope="scope">
          <el-button @click="selectTeacher(scope.row)" type="primary" size="small">选择</el-button>
        </template>
      </el-table-column>
    </el-table>

    <span slot="footer" class="dialog-footer">
      <el-button @click="showDialog = false">取消</el-button>
      <el-button type="primary" @click="addMember">确认添加</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { ref } from 'vue';
import axios from 'axios';

export default {
  props: {
    team: {
      type: Object,
      required: true
    },
    isCreator: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { emit }) {
    const showDialog = ref(false);
    const searchForm = ref({
      teacherName: '',
      teaCode: '',
      department: ''
    });
    const teachers = ref([]);
    const selectedTeacher = ref(null);

    const handleSearch = () => {
      axios.get('/teacher/list', { params: searchForm.value })
        .then(response => {
          teachers.value = response.data.records;
        })
        .catch(error => {
          console.error('搜索教师失败:', error);
        });
    };

    const selectTeacher = (teacher) => {
      selectedTeacher.value = teacher;
    };

    const addMember = () => {
      if (!selectedTeacher.value) {
        alert('请选择一个教师');
        return;
      }

      const teamRole = prompt('请输入角色（1: 创建者, 2: 成员, 3: 助教）:');
      if (teamRole === null) return;

      axios.post('/courseTeacher/save', {
        courseId: props.team.courseId,
        teacherId: selectedTeacher.value.teacherId,
        teamRole: parseInt(teamRole, 10),
        createdTime: new Date().toISOString(),
      })
        .then(response => {
          alert('添加成功');
          showDialog.value = false;
          emit('add-member-success');
        })
        .catch(error => {
          console.error('添加成员失败:', error);
        });
    };

    return {
      showDialog,
      searchForm,
      teachers,
      selectedTeacher,
      handleSearch,
      selectTeacher,
      addMember
    };
  }
};
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>