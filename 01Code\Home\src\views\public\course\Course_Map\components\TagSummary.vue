<template>
  <!-- 标签摘要组件的根容器 -->
  <div class="tag-summary">
    <!-- 顶部按钮区域，用于展开或收起标签列表 -->
    <div class="tag-header tooltip-container top" @click="toggleExpand">
      <!-- 箭头图标，根据展开状态旋转 -->
      <svg class="icon-arrow" :class="{ rotated: isExpanded }" viewBox="0 0 1024 1024">
        <path d="M512 640L192 320h640z" fill="#8a6de3" />
      </svg>
      <!-- 鼠标悬停时显示的提示信息 -->
      <div class="tooltip">
        {{ isExpanded ? '收起全部标签' : '展开全部标签' }}
      </div>
    </div>

    <!-- 标签列表区域，用于显示标签及其计数 -->
    <div class="tag-list" ref="tagListRef">
      <!-- 遍历显示标签列表 -->
      <div v-for="(item, index) in displayTags" :key="index" class="tag-item" @click="handleTagClick(item.label)">
        <!-- 标签名称 -->
        <span class="tag-label">{{ item.label }}</span>
        <!-- 标签出现的次数 -->
        <span class="tag-count">{{ item.count }}</span>
      </div>
    </div>

    <!-- 底部按钮区域，用于向下滚动标签列表 -->
    <div class="tag-footer tooltip-container bottom" @click="scrollTags">
      <!-- 箭头图标 -->
      <svg class="icon-arrow" viewBox="0 0 1024 1024">
        <path d="M512 640L192 320h640z" fill="#333" />
      </svg>
      <!-- 鼠标悬停时显示的提示信息 -->
      <div class="tooltip">下移</div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// 定义组件接收的属性
const props = defineProps({
  // data 属性为一个对象，包含树形结构的数据，用于提取标签
  data: {
    type: Object,
    required: true
  },
  // tagCounts 属性为从API获取的标签统计数据
  tagCounts: {
    type: Object,
    default: () => ({})
  },
  // 新增：所有标签列表（包含树图有的和没有的标签）
  allTags: {
    type: Array,
    default: () => []
  }
})

// 记录标签列表是否展开的状态
const isExpanded = ref(false)
// 引用标签列表的 DOM 元素，用于滚动操作
const tagListRef = ref(null)

// 计算属性：树图实际包含的标签及其计数
const treeTags = computed(() => {
  const tagMap = new Map()
  const collectTags = (node) => {
    if (node.tags) {
      node.tags.forEach(tag => {
        tagMap.set(tag, (tagMap.get(tag) || 0) + 1)
      })
    }
    if (node.children) {
      node.children.forEach(collectTags)
    }
  }
  if (props.data) collectTags(props.data)
  return Array.from(tagMap).map(([label, count]) => ({ label, count }))
})

// 计算属性：全部标签（allTags），计数为树图中实际出现的数量，否则为0
const allTagsWithCount = computed(() => {
  // 构建树图标签计数Map
  const countMap = new Map()
  treeTags.value.forEach(item => {
    countMap.set(item.label, item.count)
  })
  // allTags为对象数组（如[{tagId, tagName}]），兼容字符串数组
  return props.allTags.map(tag => {
    const label = tag.tagName || tag // 兼容tag为对象或字符串
    return {
      label,
      count: countMap.get(label) || 0
    }
  })
})

// 计算属性：根据展开状态显示标签
const displayTags = computed(() => {
  if (isExpanded.value) {
    // 展开时显示所有标签（allTagsWithCount）
    return allTagsWithCount.value
  } else {
    // 收起时只显示树图实际包含的标签
    return treeTags.value
  }
})

// 切换标签列表展开状态的方法
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
}

// 滚动标签列表的方法
const scrollTags = () => {
  if (tagListRef.value) {
    // 平滑向下滚动 50 像素
    tagListRef.value.scrollBy({ top: 50, behavior: 'smooth' })
  }
}

// 定义组件触发的事件
const emit = defineEmits(['tag-click'])

// 处理标签点击事件的方法
const handleTagClick = (label) => {
  // 触发 tag-click 事件，并传递标签名称
  emit('tag-click', label)
}
</script>

<style lang="scss" scoped>
.tag-summary {
  width: 150px;
  background: #fff;
  box-shadow: 1px 4px 4px rgba(0, 0, 0, 0.2);
  border-radius: 20px;
  overflow: visible;
  display: flex;
  flex-direction: column;
  padding: 10px;

  // 提示框容器样式
  .tooltip-container {
    position: relative;
    display: inline-block;

    // 提示框样式
    .tooltip {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      background: #666;
      color: #fff;
      padding: 4px 8px;
      border-radius: 6px;
      font-size: 12px;
      white-space: nowrap;
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.2s;
      z-index: 999;
    }

    // 三角箭头样式
    .tooltip::after {
      content: '';
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      border-width: 5px;
      border-style: solid;
    }

    // 鼠标悬停时显示提示框
    &:hover .tooltip {
      opacity: 1;
      visibility: visible;
    }

    // 上方显示提示框
    &.top .tooltip {
      bottom: 100%;
      margin-bottom: 8px;
    }

    &.top .tooltip::after {
      top: 100%;
      border-color: #666 transparent transparent transparent;
    }

    // 下方显示提示框
    &.bottom .tooltip {
      top: 100%;
      margin-top: 8px;
    }

    &.bottom .tooltip::after {
      bottom: 100%;
      border-color: transparent transparent #666 transparent;
    }
  }

  // 顶部和底部按钮样式
  .tag-header,
  .tag-footer {
    background: #f0f0f0;
    padding: 2px;
    text-align: center;
    cursor: pointer;
    user-select: none;
    border-radius: 20px;
  }

  // 底部按钮样式微调
  .tag-footer {
    padding: 10px 0px 0px 0px;
    background: #fff;
  }

  // 箭头图标样式
  .icon-arrow {
    width: 20px;
    height: 20px;
    transition: transform 0.3s;

    // 展开状态下箭头旋转 180 度
    &.rotated {
      transform: rotate(180deg);
    }
  }

  // 标签列表样式
  .tag-list {
    max-height: 200px;
    overflow-y: auto;
    padding: 6px;
  }

  // 单个标签项样式
  .tag-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 13px;
    color: #666;
    padding: 8px 0;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
    border-radius: 10px;
    padding: 10px;

    // 鼠标悬停时的样式
    &:hover {
      background: #f5f0ff;
      color: #8a6de3;
    }

    // 标签名称样式
    .tag-label {
      flex: 1;
    }

    // 标签计数样式
    .tag-count {
      font-size: 15px;
      font-weight: 800;
      color: #8a6de3;
      margin-left: 4px;
    }
  }
}
</style>