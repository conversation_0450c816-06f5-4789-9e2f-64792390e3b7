<template>
    <div class="ability-list-panel">
        <!-- 新建能力目标按钮 -->
        <button class="new-ability-btn" @click="handleCreateNew">
            <span>+ 新建能力目标</span>
        </button>

        <!-- 能力目标列表标题 -->
        <div class="list-header">
            <img :src="abilityIcon" alt="能力目标" class="header-icon" />
            <span class="header-text">能力目标列表</span>
        </div>

        <!-- 能力目标下拉列表 -->
        <div class="ability-list">
            <div v-for="ability in abilityList" :key="ability.abilityId" class="ability-item">
                <div class="item-content" @click="selectMainAbility(ability)">
                    <!-- 修改箭头显示条件，根据subAbilityCount判断 -->
                    <span class="arrow" v-if="ability.subAbilityCount > 0" @click.stop="toggleExpand(ability)">
                        {{ ability.expanded ? '▼' : '▶' }}
                    </span>
                    <span class="ability-name">{{ ability.nodeName }}</span>
                </div>

                <!-- 子能力列表 -->
                <div v-if="ability.expanded && ability.subAbilities" class="sub-ability-list">
                    <div v-for="subAbility in ability.subAbilities" :key="subAbility.abilityId" class="sub-ability-item"
                        @click.stop="selectSubAbility(ability, subAbility)">
                        {{ subAbility.nodeName }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { useAbilityGraphStore } from '@/stores/teacher/graphManager/abilityGraphStore';
import abilityIcon from '@/assets/courseMap-icon/createAbGraph/abilityIcon.svg';

const route = useRoute();
const store = useAbilityGraphStore();

const emit = defineEmits(['create-new', 'select-main', 'select-sub']);

const abilityList = ref([]);
const selectedAbilityId = ref(null);
const isEditing = ref(false);

// 获取主能力列表
const fetchMainAbilities = async () => {
    try {
        if (!store.graphId) {
            await store.initAbilityGraph(route.params.courseId, route.params.graphId);
        }
        
        const res = await store.fetchMainAbilitiesWithDetail(store.graphId);
        abilityList.value = res.map(ability => ({
            ...ability,
            expanded: false,
            subAbilities: ability.subAbilities || []
        }));
    } catch (error) {
        console.error('获取主能力列表失败:', error);
        throw error; // 抛出错误以便外部捕获
    }
};

// 获取子能力列表
const fetchSubAbilities = async (mainAbilityId) => {
    try {
        // 使用store中的方法获取带描述的完整数据
        const res = await store.getMainAbilityDetail(mainAbilityId);
        return res.result?.subAbilities || [];
    } catch (error) {
        console.error('获取子能力列表失败:', error);
        return [];
    }
};



// 切换展开/收起状态
const toggleExpand = async (ability) => {
    // 如果没有子能力，则不处理
    if (ability.subAbilityCount <= 0) return;

    ability.expanded = !ability.expanded;

    // 如果展开且还没有加载子能力，则加载子能力
    if (ability.expanded && ability.subAbilities.length === 0) {
        ability.subAbilities = await fetchSubAbilities(ability.abilityId);
    }
};

// 选择主能力
const selectMainAbility = (ability) => {
    if (isEditing.value) {
        ElMessage.warning('请先保存或取消当前更改');
        return;
    }
    emit('select-main', ability);
};

// 选择子能力
const selectSubAbility = (mainAbility, subAbility) => {
    if (isEditing.value) {
        ElMessage.warning('请先保存或取消当前更改');
        return;
    }
    emit('select-sub', mainAbility, subAbility);
};

// 新建能力目标
const handleCreateNew = async () => {
    if (isEditing.value) {
        ElMessage.warning('请先保存或取消当前更改');
        return;
    }

    try {
        // 初始化能力图谱（如果未初始化）
        if (!store.graphId || !store.rootNodeId) {
            const success = await store.initAbilityGraph(
                route.params.courseId,
                route.params.graphId
            );
            if (!success) return;
        }

        // 触发父组件的编辑模式
        emit('create-new');

    } catch (error) {
        console.error('创建新能力目标失败:', error);
    }
};

// 刷新列表方法
const refreshList = async () => {
    try {
        await fetchMainAbilities();
        
        // 添加安全判断
        if (selectedAbilityId.value && abilityList.value.length > 0) {
            const stillExists = abilityList.value.some(a => a.abilityId === selectedAbilityId.value);
            if (!stillExists) {
                selectedAbilityId.value = null;
                emit('select-main', null);
            }
        }
    } catch (error) {
        console.error('刷新能力列表失败:', error);
        throw error;
    }
};

// 初始化时加载数据
onMounted(async () => {
    if (route.params.graphId) {
        await fetchMainAbilities();
    }
});

// 设置编辑状态
const setEditingState = (editing) => {
    isEditing.value = editing;
};

defineExpose({
    refreshList,
    abilityList,
    setEditingState
});
</script>

<style lang="scss" scoped>
.ability-list-panel {
    display: flex;
    flex-direction: column;
    min-height: 85vh;
    padding: 16px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.new-ability-btn {
    padding: 8px 16px;
    background-color: #4c7bff;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    margin-bottom: 20px;
    transition: background-color 0.2s;

    &:hover {
        background-color: #3a6ae0;
    }
}

.list-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e8e8e8;

    .header-icon {
        width: 20px;
        height: 20px;
        margin-right: 8px;
    }

    .header-text {
        font-size: 16px;
        font-weight: 600;
        color: #333;
    }
}

.ability-list {
    flex: 1;
    overflow-y: auto;
}

.ability-item {
    margin-bottom: 8px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;

    &:hover {
        background-color: #f5f5f5;
    }

    .item-content {
        display: flex;
        align-items: center;
        padding: 8px 12px;

        .arrow {
            font-size: 12px;
            width: 16px;
            text-align: center;
            cursor: pointer;
            color: #999;
            margin-right: 8px;
        }

        .ability-name {
            font-size: 14px;
            color: #333;
        }
    }
}

.sub-ability-list {
    padding-left: 32px;
    margin-top: 4px;

    .sub-ability-item {
        padding: 6px 12px;
        font-size: 13px;
        color: #666;
        border-radius: 4px;
        cursor: pointer;

        &:hover {
            background-color: #f0f0f0;
            color: #4c7bff;
        }
    }
}
</style>