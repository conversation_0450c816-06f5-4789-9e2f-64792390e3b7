<template>
    <div class="stats-bar">
      <div class="stat-item">
        <span class="count">{{ stats.knowledges }}</span>
        <span class="label">个知识点</span>
      </div>
      <div class="stat-item">
        <span class="count">{{ stats.resources }}</span>
        <span class="label">个资源</span>
      </div>
    </div>
  </template>
  
  <script setup>
  defineProps(['stats'])
  </script>
  
  <style lang="scss" scoped>
  .stats-bar {
    position: absolute;
    top: 80px;
    left: 40px;
    z-index: 10;
    display: flex;
    gap: 24px;
    background-color: white;
    padding: 6px 12px;
    border-radius: 6px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
  
    .stat-item {
      display: flex;
      align-items: baseline;
      font-size: 14px;
  
      .count {
        font-weight: 600;
        color: #000;
        font-size: 15px;
        margin-right: 4px;
      }
  
      .label {
        color: #999;
      }
    }
  }
  </style>
  