<!-- 路径： src\views\teacher\CourseDetailManager\WorkAndExam\TestDataBase\CourseTestDataBase.vue-->
<template>
    <div class="course-test-database">
        <!-- 顶部导航 -->
        <NavBar />
        <div class="navbar-space" />
        <div class="main">
            <!-- 侧边栏 -->
            <SideMenu />

            <!-- 右侧内容 -->
            <div class="content">
                <!-- Tabs -->
                <div class="tabs">
                    <el-tabs v-model="activeTab">
                        <el-tab-pane label="课程题库" name="course" />
                        <el-tab-pane label="课程试卷库" name="exam" />
                        <el-tab-pane label="课程实践项目" name="project" />
                    </el-tabs>
                </div>

                <!-- 顶部操作栏 -->
                <div class="action-row">
                    <div class="left-side">
                        <span class="course-info-text" v-if="selectedCourse">
                            当前课程：{{ selectedCourse.name }}（{{ selectedCourse.semester }}）
                        </span>
                        <el-select v-model="selectedCourseId" placeholder="选择课程" clearable style="width: 260px">
                            <el-option v-for="course in courseStore.courses" :key="course.id"
                                :label="`${course.name} ${course.semester}`" :value="course.id" />
                        </el-select>
                    </div>

                    <div class="right-side">
                        <el-input v-model="tabSearch" placeholder="搜索题目 / 项目" style="width: 220px; margin-right: 12px"
                            clearable />
                        <el-button type="primary" style="margin-right: 10px" @click="handleAddQuestion">
                            <el-icon>
                                <Plus />
                            </el-icon>新增试题
                        </el-button>
                    </div>
                </div>

                <!-- 筛选栏 -->
                <FilterBar v-model="filters" :questionTypes="questionTypes" :owners="owners" :total="filteredData.length"
                    @update="handleFilterChange" @batchMark="batchMark" @batchPreview="batchPreview"
                    @batchDelete="batchDelete" @batchUnlink="batchUnlink" @toggleSortDesc="toggleSortDesc" />

                <!-- 题库列表 -->
                <div class="question-list" v-loading="loading" element-loading-background="transparent">
                    <div v-for="(item, index) in paginatedData" :key="item.id" class="question-card"
                        @click="handlePreview(item)">
                        <el-checkbox v-model="item.checked" class="card-checkbox" @click.stop />
                        <div class="question-title">
                            <el-icon v-if="['实践项目', '整套试卷'].includes(item.type)" style="margin-right: 6px;">
                                <Folder />
                            </el-icon>
                            {{ item.title }}
                        </div>
                        <div class="question-meta">
                            创建时间: {{ item.createTime }} 更新时间: {{ item.updateTime }}
                        </div>
                    </div>
                    <!-- 提示消息 -->
                    <template v-if="!loading && filteredData.length === 0">
                        <el-empty :description="`当前${activeTabLabel}为空`" image-size="100" style="margin-top: 40px" />
                    </template>

                </div>

                <!-- 公共弹窗 -->
                <el-dialog v-model="previewVisible" :title="getDialogTitle()" width="800px">
                    <!-- 整套试卷 -->
                    <ExamPreviewDialog v-if="currentPreview?.type === '整套试卷'" :examData="currentPreview" :inline="true" />

                    <!-- 普通题型 -->
                    <template v-else-if="['单选题', '多选题'].includes(currentPreview?.type)">
                        <p><strong>题目：</strong>{{ currentPreview.content }}</p>
                        <ul style="list-style: none; padding-left: 0;">
                            <li v-for="(val, key) in currentPreview.options" :key="key">
                                {{ key }}：{{ val }}
                            </li>
                        </ul>
                        <p><strong>正确答案：</strong>{{ formatAnswer(currentPreview.correct) }}</p>
                    </template>

                    <template v-else-if="currentPreview?.type === '判断题'">
                        <p><strong>题目：</strong>{{ currentPreview.content }}</p>
                        <p><strong>答案：</strong>{{ currentPreview.answer }}</p>
                    </template>

                    <template v-else-if="currentPreview?.type === '填空题'">
                        <p><strong>题目：</strong>{{ currentPreview.content }}</p>
                        <p><strong>答案：</strong>{{ currentPreview.answers.join(', ') }}</p>
                    </template>

                    <template v-else-if="currentPreview?.type === '问答题'">
                        <p><strong>题目：</strong>{{ currentPreview.content }}</p>
                        <p><strong>参考答案：</strong>{{ currentPreview.sampleAnswer }}</p>
                    </template>

                    <!-- 实践项目 -->
                    <template v-else-if="currentPreview?.type === '实践项目'">
                        <div style="margin-bottom: 12px;">
                            <el-button type="primary" icon="el-icon-download"
                                @click="downloadFile(currentPreview.fileUrl)">下载文档</el-button>
                        </div>
                        <iframe :src="getOfficeOnlineUrl(currentPreview.fileUrl)" style="width: 100%; height: 600px;"
                            frameborder="0"></iframe>
                    </template>
                </el-dialog>

                <!-- 分页条 -->
                <div class="pagination">
                    <el-pagination v-model:current-page="currentPage" :page-size="pageSize" layout="prev, pager, next"
                        :total="filteredData.length" background />
                </div>

            </div>
        </div>
    </div>

    <div class="bottom-bar">
        <el-button @click="onCancel">取消</el-button>
        <div class="selected-count">已选 {{ selectedTests.length }} 道题</div>
        <el-button type="primary" @click="onConfirm">引用所选试题</el-button>
    </div>

    <input ref="fileInputRef" type="file" accept=".pdf,.docx,.zip" style="display: none" @change="handleFileChange" />

    <template>
        <div>
            <el-button type="primary" @click="showCreateDialog = true">新建试卷</el-button>

            <el-dialog title="请选择创建方式" :visible.sync="showCreateDialog" width="400px" :before-close="handleDialogClose">
                <div style="display: flex; flex-direction: column; gap: 15px; padding: 20px 0;">
                    <el-button type="success" @click="triggerFileUpload">上传整套试卷（Word）</el-button>
                    <el-button type="primary" @click="goToCreatePage">自己创建题目</el-button>
                </div>
            </el-dialog>
            <input ref="fileInputRef" type="file" style="display: none" @change="handleFileChange" />
        </div>
    </template>
    <!-- 新建试题弹窗 -->
    <NewTestTypeSelectDialog v-model="showNewTestDialog"
        @single="() => router.push('/new-test?courseId=' + selectedCourseId.value + '&isPublic=false')"
        @batch="triggerFileUpload" />
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import NavBar from '@/components/NavBar.vue'
import SideMenu from '../components/SideMenu.vue'
import FilterBar from '../components/FilterBar.vue'
import ExamPreviewDialog from '../components/ExamPreviewDialog.vue'
import { importQuestionFile } from '@/api/teacher/question'

// 用 ref 存一下，避免每次都重复 query 取值
const courseId = ref('')
const courseName = ref('')

// 新建题目弹窗
import NewTestTypeSelectDialog from '../components/NewTestTypeSelectDialog.vue'

const showNewTestDialog = ref(false)

const fileInputRef = ref(null)
const triggerFileUpload = () => {
    if (!selectedCourseId.value) {
        return ElMessage.warning('请先选择课程')
    }
    fileInputRef.value?.click()
}

// 渲染列表接口
import {
    getCourseQuestions,
    getExamPage,
    getPracticeProjectList,
} from '@/api/teacher/question'
import { Plus, Folder } from '@element-plus/icons-vue'

// 题目详情获取
import {
    getSingleChoiceDetail,
    getMultipleChoiceDetail,
    getTrueFalseDetail,
    getFillBlankDetail,
    getShortAnswerDetail,
    getExamDetail,
    getPracticeProjectDetail
} from '@/api/teacher/question'

// store
import { useCourseStore } from '@/stores/courseStore'
const courseStore = useCourseStore()

// 消息提示
const activeTabLabel = computed(() => {
    switch (activeTab.value) {
        case 'course':
            return '题库'
        case 'exam':
            return '试卷库'
        case 'project':
            return '实践项目'
        default:
            return ''
    }
})

const route = useRoute()
const router = useRouter()
const loading = ref(false)

const activeTab = ref('course') // 当前 tab：course / exam / project
const tabSearch = ref('')
const selectedCourseId = ref(null)
const tableData = ref([])

// 分页
const currentPage = ref(1)
const pageSize = 5
const paginatedData = computed(() => {
    const start = (currentPage.value - 1) * pageSize
    const end = currentPage.value * pageSize
    return filteredData.value.slice(start, end)
})

// 格式化时间
function formatDate(ts) {
    if (!ts) return '-'
    const date = new Date(ts)
    if (isNaN(date.getTime())) return '-'
    return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`
}

// 获取课程题库
async function fetchCourseQuestions() {
    loading.value = true
    const typeLabelMap = { 0: '单选题', 1: '多选题', 2: '判断题', 3: '填空题', 4: '问答题' }
    const all = []

    try {
        for (let type = 0; type <= 4; type++) {
            const params = { isPublic: false, type }
            if (selectedCourseId.value) params.courseId = selectedCourseId.value

            const res = await getCourseQuestions(params)
            if (res.code === 0 || res.code === 200) {
                const items = res.result.map(q => ({
                    id: q.id,
                    title: q.content?.slice(0, 30) + (q.content.length > 30 ? '...' : ''),
                    type: typeLabelMap[q.type] || '未知',
                    content: q.content || '',
                    createTime: formatDate(q.createdTime || Date.now()),
                    updateTime: formatDate(q.updatedTime || Date.now()),
                    owner: q.teacherName || '未知教师',
                    checked: false,
                }))
                all.push(...items)
            }
        }
    } catch (e) {
        console.error('题库加载失败', e)
    } finally {
        loading.value = false
        tableData.value = all
    }
}

// 获取课程试卷
async function fetchCourseExams() {
    loading.value = true
    try {
        const params = selectedCourseId.value
            ? { courseId: selectedCourseId.value }
            : { isPublic: false }

        const res = await getExamPage(params)

        if (res.code === 200) {
            tableData.value = (res.result.records || []).map(paper => ({
                id: paper.id,
                title: paper.title,
                type: '整套试卷',
                content: paper.title,
                createTime: formatDate(paper.createdTime),
                updateTime: formatDate(paper.updatedTime),
                owner: paper.teacherName || '未知',
                checked: false,
            }))
        }
    } catch (e) {
        console.error('试卷库加载失败', e)
    } finally {
        loading.value = false
    }
}

// 获取课程实践项目
async function fetchCourseProjects() {
    loading.value = true
    try {
        const params = {
            page: 1,
            size: 100
        }
        if (selectedCourseId.value) {
            params.courseId = selectedCourseId.value
        }

        const res = await getPracticeProjectList(params)
        if (res.code === 200) {
            const records = Array.isArray(res.result?.records)
                ? res.result.records
                : res.result

            tableData.value = records.map(proj => ({
                id: proj.id,
                title: proj.title,
                type: '实践项目',
                content: proj.title,
                createTime: formatDate(proj.createdTime),
                updateTime: formatDate(proj.updatedTime),
                owner: proj.teacherName || '未知',
                checked: false,
            }))
        } else {
            console.warn('实践项目加载失败：', res)
        }
    } catch (e) {
        console.error('实践项目加载失败', e)
    } finally {
        loading.value = false
    }
}

// 通用刷新方法
async function fetchDataByTab() {
    if (activeTab.value === 'course') {
        await fetchCourseQuestions()
    } else if (activeTab.value === 'exam') {
        await fetchCourseExams()
    } else if (activeTab.value === 'project') {
        await fetchCourseProjects()
    }
}

// 初始加载课程
onMounted(async () => {
    courseId.value = route.query.courseId || ''
    // courseName.value = route.query.courseName || ''
    // if (!courseId.value || !courseName.value) {
    //     ElMessage.error({ message: '课程信息缺失，无法返回创建页', offset: 90 })
    // }
    if (!courseId.value) {
        ElMessage.error({ message: '课程信息缺失，无法返回创建页', offset: 90 })
    }
    await courseStore.fetchTeacherCourses()
    await fetchDataByTab()
})

// tab切换或课程切换时重新加载
watch([activeTab, selectedCourseId], fetchDataByTab)

// 选中课程对象
const selectedCourse = computed(() =>
    courseStore.courses.find(c => c.id === selectedCourseId.value) || null
)

// 筛选项
const filters = ref({
    qType: 'all',
    owner: 'all',
    startTime: null,
    endTime: null,
    sortDesc: true,
})

// 题型映射
const tabQuestionTypes = {
    course: ['单选题', '多选题', '判断题', '填空题', '问答题'],
    exam: ['整套试卷'],
    project: ['实践项目'],
}
const questionTypes = computed(() => {
    const types = tabQuestionTypes[activeTab.value] || []
    return [{ label: '全部', value: 'all' }, ...types.map(t => ({ label: t, value: t }))]
})

// 前端筛选
const filteredData = computed(() => {
    let result = [...tableData.value]

    const allowedTypes = tabQuestionTypes[activeTab.value] || []
    result = result.filter(item => allowedTypes.includes(item.type))

    if (tabSearch.value.trim()) {
        const kw = tabSearch.value.trim().toLowerCase()
        result = result.filter(item => item.title.toLowerCase().includes(kw))
    }

    if (filters.value.qType !== 'all') {
        result = result.filter(item => item.type === filters.value.qType)
    }

    if (filters.value.startTime) {
        const start = new Date(filters.value.startTime).getTime()
        result = result.filter(i => new Date(i.createTime).getTime() >= start)
    }
    if (filters.value.endTime) {
        const end = new Date(filters.value.endTime).getTime()
        result = result.filter(i => new Date(i.createTime).getTime() <= end)
    }

    result = result.sort((a, b) => {
        const t1 = new Date(a.createTime).getTime()
        const t2 = new Date(b.createTime).getTime()
        return filters.value.sortDesc ? t2 - t1 : t1 - t2
    })

    return result
})

function getDialogTitle() {
    if (!currentPreview.value) return '查看'
    return currentPreview.value.title || '查看'
}

// 格式化答案：去除 ["A", "C"] 变成 A, C
const formatAnswer = (ans) => {
    if (!ans) return ''
    try {
        const parsed = JSON.parse(ans)
        if (Array.isArray(parsed)) {
            return parsed.join(', ')
        }
        return parsed
    } catch {
        return ans
    }
}
const previewVisible = ref(false)
const currentPreview = ref(null)

//预览功能
const handlePreview = async (item) => {
    try {
        const typeMap = {
            '单选题': getSingleChoiceDetail,
            '多选题': getMultipleChoiceDetail,
            '判断题': getTrueFalseDetail,
            '填空题': getFillBlankDetail,
            '问答题': getShortAnswerDetail,
            '整套试卷': getExamDetail,
            '实践项目': getPracticeProjectDetail,
        }

        const apiFn = typeMap[item.type]
        if (!apiFn) {
            return ElMessage.error({ message: '不支持的题型：' + item.type, offset: 90 })
        }

        // 实践项目接口特殊（需要拼 URL）
        const res = item.type === '实践项目'
            ? await apiFn(item.id)
            : await apiFn({ id: item.id })

        if (res.code !== 200 && res.code !== 0) {
            return ElMessage.error({ message: '预览失败：' + res.msg, offset: 90 })
        }

        let previewData = {}

        if (item.type === '单选题' || item.type === '多选题') {
            const opts = JSON.parse(res.result.options || '{}')
            previewData = {
                ...res.result,
                options: opts,
                type: item.type,
            }
        } else if (item.type === '判断题') {
            previewData = {
                ...res.result,
                answer: res.result.answer ? '正确' : '错误',
                type: item.type,
            }
        } else if (item.type === '填空题') {
            // 先去掉答案字符串的[]和""，拆成数组
            let answers = []
            try {
                const parsed = JSON.parse(res.result.answers || '[]')
                if (Array.isArray(parsed)) {
                    answers = parsed
                } else if (typeof parsed === 'string') {
                    answers = [parsed]
                }
            } catch {
                answers = (res.result.answers || '').split(',')
            }
            previewData = {
                ...res.result,
                answers,
                type: item.type,
            }
        } else if (item.type === '问答题') {
            previewData = {
                ...res.result,
                type: item.type,
            }
        } else if (item.type === '整套试卷') {
            // 给每道题都加上类型字段，方便组件显示
            const formatList = (list, type) =>
                (list || []).map(q => ({ ...q, type }))

            previewData = {
                title: res.result.title,
                totalScore: res.result.totalScore,
                questions: [
                    ...formatList(res.result.singleChoiceList, '单选题'),
                    ...formatList(res.result.multipleChoiceList, '多选题'),
                    ...formatList(res.result.trueFalseList, '判断题'),
                    ...formatList(res.result.fillInBlankList, '填空题'),
                    ...formatList(res.result.shortAnswerList, '问答题'),
                ],
                type: '整套试卷'
            }
        } else if (item.type === '实践项目') {
            previewData = {
                type: '实践项目',
                fileUrl: res.result.fileUrl || item.content || '', // 用后端返回的fileUrl
                title: res.result.title || item.title || '实践项目'
            }
        }

        currentPreview.value = previewData
        previewVisible.value = true
    } catch (err) {
        console.error('预览出错', err)
        ElMessage.error({ message: '预览失败：' + (err.message || '未知错误'), offset: 90 })
    }
}

//实践项目
const downloadFile = (url) => {
    if (!url) {
        ElMessage.error('无效的文件地址')
        return
    }
    const link = document.createElement('a')
    link.href = url
    const fileName = url.split('/').pop().split('?')[0]
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
}

const getOfficeOnlineUrl = (url) => {
    if (!url) return ''
    return `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(url)}`
}
// 引用 & 取消
import { useQuestionStore } from '@/stores/useQuestionStore'

const testStore = useQuestionStore()

const selectedTests = computed(() => tableData.value.filter(item => item.checked))

// 返回路径生成：统一回到创建页面第 2 步
const mode = computed(() => route.query.mode || 'assignment')

function buildReturnPath(mode) {
    const id = courseId.value
    const name = courseName.value
    return `/teacher/course-detail-manager/${encodeURIComponent(id)}/${encodeURIComponent(name)}/assignment-exam/create?courseId=${encodeURIComponent(id)}&courseName=${encodeURIComponent(name)}&mode=${encodeURIComponent(mode)}&step=1`
    // return `/teacher/course-detail-manager/${encodeURIComponent(id)}/assignment-exam/create?courseId=${encodeURIComponent(id)}&courseName=${encodeURIComponent(name)}&mode=${encodeURIComponent(mode)}&step=1`
}

function onCancel() {
    if (!courseId.value) {
        ElMessage.warning('课程信息缺失，无法返回')
        return
    }
    router.push(buildReturnPath(mode.value))
}

function onConfirm() {
    if (selectedTests.value.length === 0) {
        ElMessage.warning('请选择至少一个题目')
        return
    }

    testStore.addTests(selectedTests.value)
    router.push(buildReturnPath(mode.value))
}

// 新建试题/上传Excel文件
const handleAddQuestion = () => {
    if (!selectedCourseId.value) {
        return ElMessage.warning('请先选择课程')
    }
    if (activeTab.value === 'course') {
        showNewTestDialog.value = true
    } else if (activeTab.value === 'exam') {
        // 如果是考试（假设是组卷模式），传递 `paperMode=true` 和 `courseId`
        router.push({
            path: '/new-test',
            query: {
                paperMode: true,
                courseId: selectedCourseId.value // 这里传递课程 ID
            }
        })
    } else {
        fileInputRef.value?.click()
    }
}

async function handleFileChange(e) {
    const file = e.target.files?.[0]
    if (!file) return
    e.target.value = '' // 允许重复上传同一文件

    try {
        const res = await importQuestionFile(file)
        if (res.code === 200) {
            ElMessage.success({ message: '试题导入成功', offset: 90 })
            await fetchCourseQuestions() // 重新加载题库
        } else {
            ElMessage.error({ message: '导入失败：' + (res.msg || '未知错误'), offset: 90 })
        }
    } catch (err) {
        ElMessage.error({ message: '上传异常：' + (err.message || '未知错误'), offset: 90 })
    }
}
// 批量删除
function batchDelete() {
    const checked = selectedTests.value
    if (!checked.length) return ElMessage.warning({ message: '请先选择题目' })
    if (confirm(`确定删除选中的 ${checked.length} 道题目吗？`)) {
        tableData.value = tableData.value.filter(i => !i.checked)
    }
}

// 排序
const toggleSortDesc = () => {
    filters.value.sortDesc = !filters.value.sortDesc
}

// 筛选变更
const handleFilterChange = (newFilters) => {
    Object.assign(filters.value, newFilters)
    currentPage.value = 1
}
</script>

<style scoped>
.course-test-database {
    display: flex;
    flex-direction: column;
    min-height: 90vh;
}

.navbar-space {
    height: 78px;
    flex-shrink: 0;
}

.main {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Tabs 样式和公共题库一样 */
.content {
    flex: 1;
    padding: 24px;
    background-color: #f5f7fa;
}

.tabs {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 4px;
    margin-bottom: 12px;
}

.action-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.left-side,
.right-side {
    display: flex;
    align-items: center;
}

.course-info-text {
    font-weight: 600;
    font-size: 14px;
}

.question-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.question-card {
    background: white;
    padding: 16px;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    position: relative;
}

.card-checkbox {
    position: absolute;
    top: 16px;
    left: 16px;
}

.question-title {
    margin-left: 32px;
    font-weight: 600;
    font-size: 15px;
    color: #333;
    margin-bottom: 6px;
    display: flex;
    align-items: center;
}

.question-meta {
    margin-left: 32px;
    font-size: 12px;
    color: #888;
}

/* 底部操作栏 */
.bottom-bar {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 60px;
    padding: 0 20px;
    background: #fff;
    border-top: 1px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 999;
}

.selected-count {
    font-size: 14px;
    color: #666;
}

/* 分页条容器 */
.pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px;
    margin-bottom: 80px;
    /* 留出 bottom-bar 高度避免遮挡 */
}

/* 内容区域自动撑满剩余高度并允许滚动 */
.content {
    display: flex;
    flex-direction: column;
    max-height: calc(100vh - 78px);
    flex: 1;
}

/* 题目列表区域自动撑满内容空间 */
.question-list {
    flex: 1;
}
</style>
