<template>
  <div class="virtual-simulation-container">
    <!-- 搜索和筛选区域 -->
    <div class="filter-section">
      <div class="search-box">
        <input
          v-model="searchParams.name"
          type="text"
          placeholder="搜索仿真项目名称..."
          @keyup.enter="handleSearch"
          class="search-input"
        />
        <button @click="handleSearch" class="search-btn">
          <i class="fas fa-search"></i>
        </button>
      </div>

      <div class="filter-controls">
        <select v-model="searchParams.semester" @change="handleSearch" class="filter-select">
          <option value="">全部学期</option>
          <option value="2023春季">2023春季</option>
          <option value="2023秋季">2023秋季</option>
          <option value="2024春季">2024春季</option>
          <option value="2024秋季">2024秋季</option>
        </select>

        <select v-model="searchParams.subject" @change="handleSearch" class="filter-select">
          <option value="">全部学科</option>
          <option value="计算机科学">计算机科学</option>
          <option value="电子工程">电子工程</option>
          <option value="机械工程">机械工程</option>
          <option value="化学">化学</option>
          <option value="医学">医学</option>
        </select>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>正在加载虚拟仿真项目...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <div class="error-icon">⚠️</div>
      <p class="error-message">{{ error }}</p>
      <button @click="fetchSimulations" class="retry-btn">重试</button>
    </div>

    <!-- 无数据状态 -->
    <div v-else-if="resources.length === 0" class="empty-container">
      <div class="empty-icon">📚</div>
      <p>暂无虚拟仿真项目</p>
    </div>

    <!-- 项目列表 -->
    <div v-else class="resource-grid">
      <div
        v-for="item in resources"
        :key="item.simulationId"
        class="resource-card"
        :style="{ backgroundImage: `url(${item.coverImage})` }"
        @click="handleCardClick(item)"
      >
        <div class="card-overlay">
          <div class="card-content">
            <h3 class="card-title">{{ item.name }}</h3>
            <p class="card-description">{{ item.description }}</p>

            <div class="card-stats">
              <div class="stat-item">
                <i class="fas fa-eye"></i>
                <span>{{ item.visitCount }}</span>
              </div>
              <div class="stat-item">
                <i class="fas fa-user-graduate"></i>
                <span>{{ item.studentCount }}</span>
              </div>
              <div class="stat-item">
                <i class="fas fa-clock"></i>
                <span>{{ item.classHours }}课时</span>
              </div>
            </div>

            <div class="card-meta">
              <span class="semester">{{ item.semester }}</span>
              <span class="subject">{{ item.subject }}</span>
              <span class="leader">{{ item.leader }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页控件 -->
    <div v-if="resources.length > 0" class="pagination-container">
      <button
        @click="prevPage"
        :disabled="pagination.current === 1 || isLoading"
        class="page-btn"
      >
        上一页
      </button>

      <span class="page-info">
        第 {{ pagination.current }} 页 / 共 {{ pagination.pages }} 页
        (共 {{ pagination.total }} 项)
      </span>

      <button
        @click="nextPage"
        :disabled="pagination.current >= pagination.pages || isLoading"
        class="page-btn"
      >
        下一页
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { getSimulationList } from '@/api/public/resource/simulation';
import { ElMessage } from 'element-plus';

// 路由实例
const router = useRouter();

// 响应式数据
const resources = ref([]);
const isLoading = ref(false);
const error = ref(null);

// 搜索参数
const searchParams = reactive({
  name: '',
  semester: '',
  subject: ''
});

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0,
  pages: 1
});

// 获取虚拟仿真项目列表
const fetchSimulations = async (resetPage = false) => {
  if (resetPage) {
    pagination.current = 1;
  }

  isLoading.value = true;
  error.value = null;

  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchParams
    };

    // 过滤空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key];
      }
    });

    const response = await getSimulationList(params);

    if (response && response.result) {
      resources.value = response.result.records || [];
      pagination.total = response.result.total || 0;
      pagination.pages = response.result.pages || 1;
      pagination.current = response.result.current || 1;
    } else {
      throw new Error('响应数据格式错误');
    }

  } catch (err) {
    console.error('获取虚拟仿真项目失败:', err);
    error.value = err.response?.data?.msg || err.message || '获取数据失败，请重试';
    ElMessage.error(error.value);
  } finally {
    isLoading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  fetchSimulations(true);
};

// 分页处理
const prevPage = () => {
  if (pagination.current > 1) {
    pagination.current--;
    fetchSimulations();
  }
};

const nextPage = () => {
  if (pagination.current < pagination.pages) {
    pagination.current++;
    fetchSimulations();
  }
};

// 卡片点击处理
const handleCardClick = (item) => {
  console.log('点击了仿真项目:', item);
  // 跳转到虚拟项目详情页
  router.push({
    name: 'virtual-project',
    params: { simulationId: item.simulationId }
  });
};

// 组件挂载时获取数据
onMounted(() => {
  fetchSimulations();
});
</script>

<style scoped lang="scss">
.virtual-simulation-container {
  padding: 20px;

  .filter-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    gap: 20px;
    flex-wrap: wrap;

    .search-box {
      display: flex;
      align-items: center;
      flex: 1;
      max-width: 400px;

      .search-input {
        flex: 1;
        padding: 10px 15px;
        border: 1px solid #ddd;
        border-radius: 25px 0 0 25px;
        outline: none;
        font-size: 14px;

        &:focus {
          border-color: #8a6de3;
          box-shadow: 0 0 0 2px rgba(138, 109, 227, 0.2);
        }
      }

      .search-btn {
        padding: 10px 15px;
        background: #8a6de3;
        color: white;
        border: none;
        border-radius: 0 25px 25px 0;
        cursor: pointer;
        transition: background-color 0.3s;

        &:hover {
          background: #7a5dd3;
        }
      }
    }

    .filter-controls {
      display: flex;
      gap: 10px;

      .filter-select {
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 6px;
        outline: none;
        font-size: 14px;

        &:focus {
          border-color: #8a6de3;
        }
      }
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #8a6de3;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 15px;
    }

    p {
      color: #666;
      font-size: 16px;
    }
  }

  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;

    .error-icon {
      font-size: 48px;
      margin-bottom: 15px;
    }

    .error-message {
      color: #e74c3c;
      font-size: 16px;
      margin-bottom: 20px;
      text-align: center;
    }

    .retry-btn {
      padding: 10px 20px;
      background: #8a6de3;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.3s;

      &:hover {
        background: #7a5dd3;
      }
    }
  }

  .empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 15px;
    }

    p {
      color: #666;
      font-size: 16px;
    }
  }

  .resource-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
  }

  .resource-card {
    height: 280px;
    border-radius: 12px;
    overflow: hidden;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-color: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    position: relative;

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);

      .card-overlay {
        background: rgba(0, 0, 0, 0.7);
      }
    }

    .card-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: flex-end;
      padding: 20px;
      transition: background-color 0.3s ease;
    }

    .card-content {
      color: white;
      width: 100%;

      .card-title {
        font-size: 18px;
        font-weight: 600;
        margin: 0 0 8px 0;
        line-height: 1.3;
      }

      .card-description {
        font-size: 14px;
        margin: 0 0 12px 0;
        opacity: 0.9;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .card-stats {
        display: flex;
        gap: 15px;
        margin-bottom: 10px;

        .stat-item {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          opacity: 0.9;

          i {
            font-size: 12px;
          }
        }
      }

      .card-meta {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;

        span {
          background: rgba(255, 255, 255, 0.2);
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 11px;
          backdrop-filter: blur(10px);
        }
      }
    }
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    padding: 20px 0;

    .page-btn {
      padding: 8px 16px;
      background: #8a6de3;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s;

      &:hover:not(:disabled) {
        background: #7a5dd3;
        transform: translateY(-1px);
      }

      &:disabled {
        background: #ccc;
        cursor: not-allowed;
        transform: none;
      }
    }

    .page-info {
      color: #666;
      font-size: 14px;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 响应式设计
@media (max-width: 768px) {
  .virtual-simulation-container {
    padding: 15px;

    .filter-section {
      flex-direction: column;
      align-items: stretch;

      .search-box {
        max-width: none;
      }

      .filter-controls {
        justify-content: space-between;
      }
    }

    .resource-grid {
      grid-template-columns: 1fr;
    }

    .pagination-container {
      flex-direction: column;
      gap: 10px;
    }
  }
}
</style>