{"_attachments": {}, "_id": "webpack-virtual-modules", "_rev": "280087-61f1bc7905b5be8ed15822b4", "author": "SysGears INC", "description": "Webpack Virtual Modules", "dist-tags": {"latest": "0.6.2"}, "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}], "name": "webpack-virtual-modules", "readme": "# Webpack Virtual Modules\n\n[![npm version][npm-version-src]][npm-version-href]\n[![npm downloads][npm-downloads-src]][npm-downloads-href]\n[![License][license-src]][license-href]\n\n**Webpack Virtual Modules** is a plugin that allows for dynamical generation of in-memory virtual modules for JavaScript\nbuilds created with webpack. When virtual module is created all the parent virtual dirs that lead to the module filename are created too. This plugin supports watch mode meaning any write to a virtual module is seen by webpack as\nif a real file stored on disk has changed.\n\n## Installation\n\nUse NPM or Yarn to install Webpack Virtual Modules as a development dependency:\n\n```bash\n# with NPM\nnpm install webpack-virtual-modules --save-dev\n\n# with Yarn\nyarn add webpack-virtual-modules --dev\n```\n\n## Usage\n\nYou can use Webpack Virtual Modules with webpack 5, 4 and 3. The examples below show the usage with webpack 5 or 4. If you want to use our plugin with webpack 3, check out a dedicated doc:\n\n* [Webpack Virtual Modules with Webpack 3]\n\n### Generating static virtual modules\n\nRequire the plugin in the webpack configuration file, then create and add virtual modules in the `plugins` array in the\nwebpack configuration object:\n\n```js\nvar VirtualModulesPlugin = require('webpack-virtual-modules');\n\nvar virtualModules = new VirtualModulesPlugin({\n  'node_modules/module-foo.js': 'module.exports = { foo: \"foo\" };',\n  'node_modules/module-bar.js': 'module.exports = { bar: \"bar\" };'\n});\n\nmodule.exports = {\n  // ...\n  plugins: [\n    virtualModules\n  ]\n};\n```\n\nYou can now import your virtual modules anywhere in the application and use them:\n\n```js\nvar moduleFoo = require('module-foo');\n// You can now use moduleFoo\nconsole.log(moduleFoo.foo);\n```\n\n### Generating dynamic virtual modules\n\nYou can generate virtual modules **_dynamically_** with Webpack Virtual Modules.\n\nHere's an example of dynamic generation of a module. All you need to do is create new virtual modules using the plugin\nand add them to the `plugins` array. After that, you need to add a webpack hook. For using hooks, consult [webpack\ncompiler hook documentation].\n\n```js\nvar webpack = require('webpack');\nvar VirtualModulesPlugin = require('webpack-virtual-modules');\n\n// Create an empty set of virtual modules\nconst virtualModules = new VirtualModulesPlugin();\n\nvar compiler = webpack({\n  // ...\n  plugins: [\n    virtualModules\n  ]\n});\n\ncompiler.hooks.compilation.tap('MyPlugin', function(compilation) {\n  virtualModules.writeModule('node_modules/module-foo.js', '');\n});\n\ncompiler.watch();\n```\n\nIn other module or a Webpack plugin, you can write to the module `module-foo` whatever you need. After this write,\nwebpack will \"see\" that `module-foo.js` has changed and will restart compilation.\n\n```js\nvirtualModules.writeModule(\n  'node_modules/module-foo.js',\n  'module.exports = { foo: \"foo\" };'\n);\n```\n\n## More Examples\n\n  - [Swagger and JSDoc Example with Webpack 5]\n  - [Swagger and JSDoc Example with Webpack 4]\n  - [Swagger and JSDoc Example with Webpack 3]\n\n## API Reference\n\n  - [API Reference]\n\n## Inspiration\n\nThis project is inspired by [virtual-module-webpack-plugin].\n\n## License\n\nCopyright © 2017 [SysGears INC]. This source code is licensed under the [MIT] license.\n\n[webpack virtual modules with webpack 3]: https://github.com/sysgears/webpack-virtual-modules/tree/master/docs/webpack3.md\n[webpack compiler hook documentation]: https://webpack.js.org/api/compiler-hooks/\n[swagger and jsdoc example with webpack 3]: https://github.com/sysgears/webpack-virtual-modules/tree/master/examples/swagger-webpack3\n[swagger and jsdoc example with webpack 4]: https://github.com/sysgears/webpack-virtual-modules/tree/master/examples/swagger-webpack4\n[swagger and jsdoc example with webpack 5]: https://github.com/sysgears/webpack-virtual-modules/tree/master/examples/swagger-webpack5\n[api reference]: https://github.com/sysgears/webpack-virtual-modules/tree/master/docs/API%20Reference.md\n[virtual-module-webpack-plugin]: https://github.com/rmarscher/virtual-module-webpack-plugin\n[MIT]: LICENSE\n[SysGears INC]: http://sysgears.com\n\n<!-- Badges -->\n[npm-version-src]: https://img.shields.io/npm/v/webpack-virtual-modules?style=flat\n[npm-version-href]: https://npmjs.com/package/webpack-virtual-modules\n[npm-downloads-src]: https://img.shields.io/npm/dm/webpack-virtual-modules?style=flat\n[npm-downloads-href]: https://npmjs.com/package/webpack-virtual-modules\n[license-src]: https://img.shields.io/github/license/sysgears/webpack-virtual-modules.svg?style=flat\n[license-href]: https://github.com/sysgears/webpack-virtual-modules/blob/main/LICENSE\n", "time": {"created": "2022-01-26T21:26:17.950Z", "modified": "2024-06-11T15:21:14.435Z", "0.4.3": "2021-05-14T11:49:52.148Z", "0.4.2": "2021-01-16T07:57:07.933Z", "0.4.1": "2020-11-27T17:23:02.106Z", "0.3.2": "2020-10-15T14:41:39.252Z", "0.3.1": "2020-09-14T06:59:12.473Z", "0.3.0": "2020-09-05T07:27:54.022Z", "0.2.2": "2020-04-24T07:41:10.102Z", "0.2.1": "2019-11-28T15:31:16.275Z", "0.2.0": "2019-10-17T12:47:26.717Z", "0.1.12": "2019-10-01T04:25:56.724Z", "0.1.11": "2019-08-12T10:01:37.423Z", "0.1.10": "2018-04-25T15:21:08.253Z", "0.1.9": "2018-04-18T07:27:56.779Z", "0.1.8": "2017-08-27T08:39:10.151Z", "0.1.7": "2017-07-25T04:54:44.310Z", "0.1.5": "2017-03-31T12:46:47.748Z", "0.1.4": "2017-03-31T12:31:41.908Z", "0.1.3": "2017-03-30T16:56:28.576Z", "0.1.2": "2017-03-30T13:59:00.274Z", "0.1.1": "2017-03-30T13:36:13.794Z", "0.4.4": "2022-06-23T12:43:24.436Z", "0.4.5": "2022-09-17T06:24:00.119Z", "0.4.6": "2022-11-01T07:25:42.527Z", "0.5.0": "2022-12-11T19:46:54.836Z", "0.6.0": "2023-10-03T15:14:56.365Z", "0.6.1": "2023-11-22T09:58:47.101Z", "0.6.2": "2024-06-04T15:49:39.434Z"}, "versions": {"0.4.3": {"name": "webpack-virtual-modules", "version": "0.4.3", "description": "Webpack Virtual Modules", "main": "lib/index.js", "scripts": {"clean": "rm -rf ./lib", "build": "tsc -p tsconfig.build.json", "watch": "tsc -p tsconfig.build.json -w", "tests": "jest", "tests:watch": "jest --watch", "test": "yarn lint && yarn tests", "lint": "eslint --fix src/**/*.ts", "prepack": "yarn clean && yarn build"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "author": "SysGears INC", "license": "MIT", "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "devDependencies": {"@babel/core": "^7.4.5", "@babel/plugin-proposal-class-properties": "^7.4.4", "@babel/plugin-transform-modules-commonjs": "^7.4.4", "@babel/preset-typescript": "^7.3.3", "@babel/register": "^7.5.5", "@types/jest": "^24.0.6", "@types/node": "^11.11.3", "@types/tmp": "^0.1.0", "@types/webpack": "^4.32.1", "@typescript-eslint/eslint-plugin": "^1.13.0", "@typescript-eslint/parser": "^1.13.0", "babel-jest": "^26.6.1", "babel-plugin-replace-ts-export-assignment": "^0.0.2", "eslint": "^7.12.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-jest": "^24.1.0", "eslint-plugin-prettier": "^3.1.4", "husky": "^4.3.0", "jest": "^26.6.1", "lint-staged": "^10.5.0", "memory-fs": "^0.5.0", "prettier": "^2.1.2", "tmp": "^0.2.1", "typescript": "^4.0.5", "webpack": "^5.3.0"}, "publishConfig": {"main": "lib/index.js", "types": "lib/index.d.ts"}, "lint-staged": {"*.ts": ["eslint --fix -c tslint.json", "git add"]}, "prettier": {"printWidth": 120, "singleQuote": true, "parser": "typescript"}, "husky": {"pre-commit": "lint-staged"}, "_id": "webpack-virtual-modules@0.4.3", "dist": {"shasum": "cd597c6d51d5a5ecb473eea1983a58fa8a17ded9", "size": 16906, "noattachment": false, "tarball": "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.4.3.tgz", "integrity": "sha512-5NUqC2JquIL2pBAAo/VfBP6KuGkHIZQXW/lNKupLPfhViwh8wNsu0BObtl09yuKZszeEUfbXz8xhrHvSG16Nqw=="}, "_npmUser": {"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}, "directories": {}, "maintainers": [{"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/webpack-virtual-modules_0.4.3_1620992991563_0.5010501301864712"}, "_hasShrinkwrap": false, "publish_time": 1620992992148, "_cnpm_publish_time": 1620992992148, "_cnpmcore_publish_time": "2021-12-16T13:27:12.830Z"}, "0.4.2": {"name": "webpack-virtual-modules", "version": "0.4.2", "description": "Webpack Virtual Modules", "main": "lib/index.js", "scripts": {"clean": "rm -rf ./lib", "build": "tsc -p tsconfig.build.json", "watch": "tsc -p tsconfig.build.json -w", "tests": "jest", "tests:watch": "jest --watch", "test": "yarn lint && yarn tests", "lint": "eslint --fix src/**/*.ts", "prepack": "yarn clean && yarn build"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "author": "SysGears INC", "license": "MIT", "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "devDependencies": {"@babel/core": "^7.4.5", "@babel/plugin-proposal-class-properties": "^7.4.4", "@babel/plugin-transform-modules-commonjs": "^7.4.4", "@babel/preset-typescript": "^7.3.3", "@babel/register": "^7.5.5", "@types/jest": "^24.0.6", "@types/node": "^11.11.3", "@types/tmp": "^0.1.0", "@types/webpack": "^4.32.1", "@typescript-eslint/eslint-plugin": "^1.13.0", "@typescript-eslint/parser": "^1.13.0", "babel-jest": "^26.6.1", "babel-plugin-replace-ts-export-assignment": "^0.0.2", "eslint": "^7.12.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-jest": "^24.1.0", "eslint-plugin-prettier": "^3.1.4", "husky": "^4.3.0", "jest": "^26.6.1", "lint-staged": "^10.5.0", "memory-fs": "^0.5.0", "prettier": "^2.1.2", "tmp": "^0.2.1", "typescript": "^4.0.5", "webpack": "^5.3.0"}, "publishConfig": {"main": "lib/index.js", "types": "lib/index.d.ts"}, "lint-staged": {"*.ts": ["eslint --fix -c tslint.json", "git add"]}, "prettier": {"printWidth": 120, "singleQuote": true, "parser": "typescript"}, "husky": {"pre-commit": "lint-staged"}, "_id": "webpack-virtual-modules@0.4.2", "dist": {"shasum": "68ce4479df7334a491b7a3f3bead47fe382947d9", "size": 16902, "noattachment": false, "tarball": "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.4.2.tgz", "integrity": "sha512-OUsT1VZhArN8nY7g6mMlw91HWnXcNXsIQjsQ83WteF4ViZ6YXqF2sWKOTDIZ0H+PPiApQdszLdZIrD7NNlU0Yw=="}, "_npmUser": {"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}, "directories": {}, "maintainers": [{"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/webpack-virtual-modules_0.4.2_1610783827808_0.5052448821278468"}, "_hasShrinkwrap": false, "publish_time": 1610783827933, "_cnpm_publish_time": 1610783827933, "_cnpmcore_publish_time": "2021-12-16T13:27:13.085Z"}, "0.4.1": {"name": "webpack-virtual-modules", "version": "0.4.1", "description": "Webpack Virtual Modules", "main": "lib/index.js", "scripts": {"clean": "rm -rf ./lib", "build": "tsc -p tsconfig.build.json", "watch": "tsc -p tsconfig.build.json -w", "tests": "jest", "tests:watch": "jest --watch", "test": "yarn lint && yarn tests", "lint": "eslint --fix src/**/*.ts", "prepack": "yarn clean && yarn build"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "author": "SysGears INC", "license": "MIT", "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "devDependencies": {"@babel/core": "^7.4.5", "@babel/plugin-proposal-class-properties": "^7.4.4", "@babel/plugin-transform-modules-commonjs": "^7.4.4", "@babel/preset-typescript": "^7.3.3", "@babel/register": "^7.5.5", "@types/jest": "^24.0.6", "@types/node": "^11.11.3", "@types/tmp": "^0.1.0", "@types/webpack": "^4.32.1", "@typescript-eslint/eslint-plugin": "^1.13.0", "@typescript-eslint/parser": "^1.13.0", "babel-jest": "^26.6.1", "babel-plugin-replace-ts-export-assignment": "^0.0.2", "eslint": "^7.12.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-jest": "^24.1.0", "eslint-plugin-prettier": "^3.1.4", "husky": "^4.3.0", "jest": "^26.6.1", "lint-staged": "^10.5.0", "memory-fs": "^0.5.0", "prettier": "^2.1.2", "tmp": "^0.2.1", "typescript": "^4.0.5", "webpack": "^5.3.0"}, "publishConfig": {"main": "lib/index.js", "types": "lib/index.d.ts"}, "lint-staged": {"*.ts": ["eslint --fix -c tslint.json", "git add"]}, "prettier": {"printWidth": 120, "singleQuote": true, "parser": "typescript"}, "husky": {"pre-commit": "lint-staged"}, "_id": "webpack-virtual-modules@0.4.1", "dist": {"shasum": "cae5a7085d34331d077225f77037bea233dbfdad", "size": 16674, "noattachment": false, "tarball": "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.4.1.tgz", "integrity": "sha512-BH/RKOHk223WdBDLFqghztx3DF5AqR3CKg3ue1KN9S1SAaXP68Kj/4rF0lsdysxXaanzx7aWl1u0+lnfj7+OtQ=="}, "_npmUser": {"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}, "directories": {}, "maintainers": [{"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/webpack-virtual-modules_0.4.1_1606497781966_0.060842594815077033"}, "_hasShrinkwrap": false, "publish_time": 1606497782106, "_cnpm_publish_time": 1606497782106, "_cnpmcore_publish_time": "2021-12-16T13:27:13.473Z"}, "0.3.2": {"name": "webpack-virtual-modules", "version": "0.3.2", "description": "Webpack Virtual Modules", "main": "index.js", "scripts": {"lint": "eslint --fix .", "posttest": "npm run lint", "test": "NODE_ENV=coverage nyc --check-coverage --lines 85 --branches 60 npm run tests", "tests": "mocha", "tests:watch": "mocha -w"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "author": {"name": "SysGears INC"}, "license": "MIT", "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "devDependencies": {"chai": "^4.0.1", "eslint": "^4.1.0", "memory-fs": "^0.4.1", "mocha": "^6.2.0", "nyc": "^14.1.1", "webpack": "^5.0.0-beta.30"}, "dependencies": {"debug": "^3.0.0"}, "greenkeeper": {}, "gitHead": "d10e955511f81d7e1101ebd1ecca48fb8c46ecd8", "_id": "webpack-virtual-modules@0.3.2", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.6", "dist": {"shasum": "b7baa30971a22d99451f897db053af48ec29ad2c", "size": 5549, "noattachment": false, "tarball": "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.3.2.tgz", "integrity": "sha512-RXQXioY6MhzM4CNQwmBwKXYgBs6ulaiQ8bkNQEl2J6Z+V+s7lgl/wGvaI/I0dLnYKB8cKsxQc17QOAVIphPLDw=="}, "maintainers": [{"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}], "_npmUser": {"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/webpack-virtual-modules_0.3.2_1602772899133_0.5101695167924327"}, "_hasShrinkwrap": false, "publish_time": 1602772899252, "_cnpm_publish_time": 1602772899252, "_cnpmcore_publish_time": "2021-12-16T13:27:13.712Z"}, "0.3.1": {"name": "webpack-virtual-modules", "version": "0.3.1", "description": "Webpack Virtual Modules", "main": "index.js", "scripts": {"lint": "eslint --fix .", "posttest": "npm run lint", "test": "NODE_ENV=coverage nyc --check-coverage --lines 85 --branches 60 npm run tests", "tests": "mocha", "tests:watch": "mocha -w"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "author": {"name": "SysGears INC"}, "license": "MIT", "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "devDependencies": {"chai": "^4.0.1", "eslint": "^4.1.0", "memory-fs": "^0.4.1", "mocha": "^6.2.0", "nyc": "^14.1.1", "webpack": "^5.0.0-beta.29"}, "dependencies": {"debug": "^3.0.0"}, "greenkeeper": {}, "gitHead": "f302d6d8445c0061f690b9b35f142ec1d76ac279", "_id": "webpack-virtual-modules@0.3.1", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.6", "dist": {"shasum": "78cbf1a41a699890a706e789a682dd1120558bf4", "size": 5507, "noattachment": false, "tarball": "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.3.1.tgz", "integrity": "sha512-C9Zbb9rny/SaZJ7gTgJjyB2Qt4G4dbT5rVZywYpyk3L6qyf006RepODREXC4rcQCiTPdZnqnebRq5Chsxg+SgQ=="}, "maintainers": [{"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}], "_npmUser": {"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/webpack-virtual-modules_0.3.1_1600066752303_0.8105744824908354"}, "_hasShrinkwrap": false, "publish_time": 1600066752473, "_cnpm_publish_time": 1600066752473, "_cnpmcore_publish_time": "2021-12-16T13:27:13.916Z"}, "0.3.0": {"name": "webpack-virtual-modules", "version": "0.3.0", "description": "Webpack Virtual Modules", "main": "index.js", "scripts": {"lint": "eslint --fix .", "posttest": "npm run lint", "test": "NODE_ENV=coverage nyc --check-coverage --lines 85 --branches 60 npm run tests", "tests": "mocha", "tests:watch": "mocha -w"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "author": {"name": "SysGears INC"}, "license": "MIT", "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "devDependencies": {"chai": "^4.0.1", "eslint": "^4.1.0", "memory-fs": "^0.4.1", "mocha": "^6.2.0", "nyc": "^14.1.1", "webpack": "^5.0.0-beta.29"}, "dependencies": {"debug": "^3.0.0"}, "greenkeeper": {}, "gitHead": "a7f1e9bf695be2fc3df2042ca9b82ba555688f37", "_id": "webpack-virtual-modules@0.3.0", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.6", "dist": {"shasum": "5cf7b27b7085bf54c9e7272a70e6dac17e7afffa", "size": 5424, "noattachment": false, "tarball": "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.3.0.tgz", "integrity": "sha512-H<PERSON>lu0bZiojnQmwR+bPUZlqCcE1DG1ETk74lZFtuFBrcj7HKSNgXxHX68gZPkLfnQ3FCkqWFp3OfS0g6syYh3Zw=="}, "maintainers": [{"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}], "_npmUser": {"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/webpack-virtual-modules_0.3.0_1599290873881_0.5067313652732541"}, "_hasShrinkwrap": false, "publish_time": 1599290874022, "_cnpm_publish_time": 1599290874022, "_cnpmcore_publish_time": "2021-12-16T13:27:14.130Z"}, "0.2.2": {"name": "webpack-virtual-modules", "version": "0.2.2", "description": "Webpack Virtual Modules", "main": "index.js", "scripts": {"lint": "eslint --fix .", "posttest": "npm run lint", "test": "NODE_ENV=coverage nyc --check-coverage --lines 90 --branches 85 npm run tests", "tests": "mocha", "tests:watch": "mocha -w"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "author": {"name": "SysGears INC"}, "license": "MIT", "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "devDependencies": {"chai": "^4.0.1", "eslint": "^4.1.0", "memory-fs": "^0.4.1", "mocha": "^6.2.0", "nyc": "^14.1.1", "webpack": "^4.0.0"}, "dependencies": {"debug": "^3.0.0"}, "greenkeeper": {}, "licenseText": "MIT License\n\nCopyright (c) 2017 SysGears\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "_id": "webpack-virtual-modules@0.2.2", "dist": {"shasum": "20863dc3cb6bb2104729fff951fbe14b18bd0299", "size": 4956, "noattachment": false, "tarball": "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.2.2.tgz", "integrity": "sha512-kDUmfm3BZrei0y+1NTHJInejzxfhtU8eDj2M7OKb2IWrPFAeO1SOH2KuQ68MSZu9IGEHcxbkKKR1v18FrUSOmA=="}, "maintainers": [{"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}], "_npmUser": {"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/webpack-virtual-modules_0.2.2_1587714069941_0.2269836887935448"}, "_hasShrinkwrap": false, "publish_time": 1587714070102, "_cnpm_publish_time": 1587714070102, "_cnpmcore_publish_time": "2021-12-16T13:27:14.341Z"}, "0.2.1": {"name": "webpack-virtual-modules", "version": "0.2.1", "description": "Webpack Virtual Modules", "main": "index.js", "scripts": {"lint": "eslint --fix .", "posttest": "npm run lint", "test": "NODE_ENV=coverage nyc --check-coverage --lines 90 --branches 85 npm run tests", "tests": "mocha", "tests:watch": "mocha -w"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "author": {"name": "SysGears INC"}, "license": "MIT", "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "devDependencies": {"chai": "^4.0.1", "eslint": "^4.1.0", "memory-fs": "^0.4.1", "mocha": "^6.2.0", "nyc": "^14.1.1", "webpack": "^4.0.0"}, "dependencies": {"debug": "^3.0.0"}, "greenkeeper": {}, "gitHead": "665340a8c2ce240f1ed926d5ec706c7d83c2fca1", "_id": "webpack-virtual-modules@0.2.1", "_nodeVersion": "12.12.0", "_npmVersion": "6.11.3", "dist": {"shasum": "8ab73d4df0fd37ed27bb8d823bc60ea7266c8bf7", "size": 4870, "noattachment": false, "tarball": "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.2.1.tgz", "integrity": "sha512-0PWBlxyt4uGDofooIEanWhhyBOHdd+lr7QpYNDLC7/yc5lqJT8zlc04MTIBnKj+c2BlQNNuwE5er/Tg4wowHzA=="}, "maintainers": [{"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}], "_npmUser": {"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/webpack-virtual-modules_0.2.1_1574955076183_0.6662482056779389"}, "_hasShrinkwrap": false, "publish_time": 1574955076275, "_cnpm_publish_time": 1574955076275, "_cnpmcore_publish_time": "2021-12-16T13:27:14.533Z"}, "0.2.0": {"name": "webpack-virtual-modules", "version": "0.2.0", "description": "Webpack Virtual Modules", "main": "index.js", "scripts": {"lint": "eslint --fix .", "posttest": "npm run lint", "test": "NODE_ENV=coverage nyc --check-coverage --lines 90 --branches 85 npm run tests", "tests": "mocha", "tests:watch": "mocha -w"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "author": {"name": "SysGears INC"}, "license": "MIT", "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "devDependencies": {"chai": "^4.0.1", "eslint": "^4.1.0", "memory-fs": "^0.4.1", "mocha": "^6.2.0", "nyc": "^14.1.1", "webpack": "^4.0.0"}, "dependencies": {"debug": "^3.0.0"}, "greenkeeper": {}, "gitHead": "a73d53f8775ab3e5dd2bc57cc5e18d63408aefc8", "_id": "webpack-virtual-modules@0.2.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.3", "_npmUser": {"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}, "dist": {"shasum": "dd13e089898ee14a77717d37a9848b40a9b1068b", "size": 4838, "noattachment": false, "tarball": "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.2.0.tgz", "integrity": "sha512-Leo4r6Sjj0HkoeP9vtQzm1abUm6UJc6gK1KrqNakIer+03mCyFVivGOFy0I0NFpRrv+kQm3UKstjWg0Fe+JqUQ=="}, "maintainers": [{"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/webpack-virtual-modules_0.2.0_1571316446590_0.5131224699092212"}, "_hasShrinkwrap": false, "publish_time": 1571316446717, "_cnpm_publish_time": 1571316446717, "_cnpmcore_publish_time": "2021-12-16T13:27:14.764Z"}, "0.1.12": {"name": "webpack-virtual-modules", "version": "0.1.12", "description": "Webpack Virtual Modules", "main": "index.js", "scripts": {"lint": "eslint --fix .", "posttest": "npm run lint", "test": "NODE_ENV=coverage nyc --check-coverage --lines 90 --branches 90 npm run tests", "tests": "mocha", "tests:watch": "mocha -w"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "author": {"name": "SysGears INC"}, "license": "MIT", "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "devDependencies": {"chai": "^4.0.1", "eslint": "^4.1.0", "memory-fs": "^0.4.1", "mocha": "^6.2.0", "nyc": "^11.0.1", "webpack": "^4.0.0"}, "dependencies": {"debug": "^3.0.0"}, "greenkeeper": {}, "gitHead": "b20e909aa8ccfa58bbb9b4ab354132804539b2ac", "_id": "webpack-virtual-modules@0.1.12", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.3", "_npmUser": {"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}, "dist": {"shasum": "ff6430c1d6f514eff12bae773ec03e5043a81943", "size": 4456, "noattachment": false, "tarball": "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.1.12.tgz", "integrity": "sha512-uwVhcpmh7WykpP2fD4T//5DLdMuanPHGXQHCnIrKoRg10GgVNy5z2oAcn26HLF67a6HCSzgc9xar+SMtZhOnXw=="}, "maintainers": [{"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/webpack-virtual-modules_0.1.12_1569903956522_0.9383331018168242"}, "_hasShrinkwrap": false, "publish_time": 1569903956724, "_cnpm_publish_time": 1569903956724, "_cnpmcore_publish_time": "2021-12-16T13:27:14.977Z"}, "0.1.11": {"name": "webpack-virtual-modules", "version": "0.1.11", "description": "Webpack Virtual Modules", "main": "index.js", "scripts": {"lint": "eslint --fix .", "posttest": "npm run lint", "test": "NODE_ENV=coverage nyc --check-coverage --lines 90 --branches 90 npm run tests", "tests": "mocha", "tests:watch": "mocha -w"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "author": {"name": "SysGears INC"}, "license": "MIT", "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "devDependencies": {"chai": "^4.0.1", "eslint": "^4.1.0", "memory-fs": "^0.4.1", "mocha": "^6.0.0", "nyc": "^11.0.1", "webpack": "^4.0.0"}, "dependencies": {"debug": "^3.0.0"}, "greenkeeper": {}, "gitHead": "6bf5be3be36af26d608218eeba2425d6a4ababc8", "_id": "webpack-virtual-modules@0.1.11", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.3", "_npmUser": {"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}, "dist": {"shasum": "8f82c74c0cc8d1ea05a0d5ed40fbe2b9b00a3ef5", "size": 4264, "noattachment": false, "tarball": "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.1.11.tgz", "integrity": "sha512-cyaaKMkICVP333iPx+74f3azZ18qKWyvViLn0FnaU9xFki+jtdOfPlkxjQVKnSbE+0Hxz7GUEuDTU+boikWTvQ=="}, "maintainers": [{"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/webpack-virtual-modules_0.1.11_1565604097303_0.08228286087337167"}, "_hasShrinkwrap": false, "publish_time": 1565604097423, "_cnpm_publish_time": 1565604097423, "_cnpmcore_publish_time": "2021-12-16T13:27:15.211Z"}, "0.1.10": {"name": "webpack-virtual-modules", "version": "0.1.10", "description": "Webpack Virtual Modules", "main": "index.js", "scripts": {"lint": "eslint --fix .", "posttest": "npm run lint", "test": "NODE_ENV=coverage nyc --check-coverage --lines 90 --branches 90 npm run tests", "tests": "mocha", "tests:watch": "mocha -w"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "author": {"name": "SysGears INC"}, "license": "MIT", "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "devDependencies": {"chai": "^4.0.1", "eslint": "^4.1.0", "memory-fs": "^0.4.1", "mocha": "^3.2.0", "nyc": "^11.0.1", "webpack": "^3.10.0"}, "dependencies": {"debug": "^3.0.0"}, "files": ["*.js"], "greenkeeper": {}, "gitHead": "85208ef3c02e3ca419903484a93802c580d93e44", "_id": "webpack-virtual-modules@0.1.10", "_shasum": "2039529cbf1007e19f6e897c8d35721cc2c41f68", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.1", "_npmUser": {"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}, "dist": {"shasum": "2039529cbf1007e19f6e897c8d35721cc2c41f68", "size": 3693, "noattachment": false, "tarball": "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.1.10.tgz", "integrity": "sha512-JnPxh1bJpoixrrOng0fjOXMnJ8wevDL/xwQp+UEushoS7e7W7fVTwGKd/9eATswSq+I3pdLqouy26k0pLlWmCQ=="}, "maintainers": [{"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/webpack-virtual-modules_0.1.10_1524669668201_0.6349303519131537"}, "_hasShrinkwrap": false, "publish_time": 1524669668253, "_cnpm_publish_time": 1524669668253, "_cnpmcore_publish_time": "2021-12-16T13:27:15.434Z"}, "0.1.9": {"name": "webpack-virtual-modules", "version": "0.1.9", "description": "Webpack Virtual Modules", "main": "index.js", "scripts": {"lint": "eslint --fix .", "posttest": "npm run lint", "test": "NODE_ENV=coverage nyc --check-coverage --lines 90 --branches 90 npm run tests", "tests": "mocha", "tests:watch": "mocha -w"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "author": {"name": "SysGears INC"}, "license": "MIT", "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "devDependencies": {"chai": "^4.0.1", "eslint": "^4.1.0", "memory-fs": "^0.4.1", "mocha": "^3.2.0", "nyc": "^11.0.1", "webpack": "^3.10.0"}, "dependencies": {"debug": "^3.0.0"}, "files": ["*.js"], "greenkeeper": {}, "gitHead": "846aac31cc459050c14d50a4b4d8d70b9c5041df", "_id": "webpack-virtual-modules@0.1.9", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.0", "_npmUser": {"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}, "dist": {"shasum": "bddf5d5c1b9465a4c5e9a3c97aca8f481a440338", "size": 3696, "noattachment": false, "tarball": "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.1.9.tgz", "integrity": "sha512-O96nCE4hz7yuNwNG4QbZ2cL3bfh9dgFP1PGzZ4R0VvVGK+KXuKSN9bmCLsv8iMSXKt9yNoSmCCbbYVMSP8ZjKA=="}, "maintainers": [{"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/webpack-virtual-modules_0.1.9_1524036476680_0.3897163086865787"}, "_hasShrinkwrap": false, "publish_time": 1524036476779, "_cnpm_publish_time": 1524036476779, "_cnpmcore_publish_time": "2021-12-16T13:27:15.758Z"}, "0.1.8": {"name": "webpack-virtual-modules", "version": "0.1.8", "description": "Webpack Virtual Modules", "main": "index.js", "scripts": {"lint": "eslint --fix .", "posttest": "npm run lint", "test": "NODE_ENV=coverage nyc --check-coverage --lines 95 --branches 95 npm run tests", "tests": "mocha", "tests:watch": "mocha -w"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "author": {"name": "SysGears INC"}, "license": "MIT", "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "devDependencies": {"chai": "^4.0.1", "eslint": "^4.1.0", "memory-fs": "^0.4.1", "mocha": "^3.2.0", "nyc": "^11.0.1", "webpack": "^3.5.5"}, "dependencies": {"debug": "^2.6.8"}, "files": ["*.js"], "greenkeeper": {}, "gitHead": "b1b9c064b772dcfb32b358aae856826ff02a1830", "_id": "webpack-virtual-modules@0.1.8", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.2", "_npmUser": {"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}, "dist": {"shasum": "22f305a1e11954c47aef52be28aa1d658f3c041b", "size": 3614, "noattachment": false, "tarball": "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.1.8.tgz", "integrity": "sha512-+VPqsKyd8Ef/CNE4S3cNBshYWOr+gH6VCBCVyTurBWN42sBoVTaPKZlwCsRNG9VEFgl219RPkzezEmLqYYHV0A=="}, "maintainers": [{"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/webpack-virtual-modules-0.1.8.tgz_1503823149210_0.9512645227368921"}, "directories": {}, "publish_time": 1503823150151, "_hasShrinkwrap": false, "_cnpm_publish_time": 1503823150151, "_cnpmcore_publish_time": "2021-12-16T13:27:15.976Z"}, "0.1.7": {"name": "webpack-virtual-modules", "version": "0.1.7", "description": "Webpack Virtual Modules", "main": "index.js", "scripts": {"lint": "eslint --fix .", "posttest": "npm run lint", "test": "NODE_ENV=coverage nyc --check-coverage --lines 95 --branches 95 npm run tests", "tests": "mocha", "tests:watch": "mocha -w"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "author": {"name": "SysGears INC"}, "license": "MIT", "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "devDependencies": {"chai": "^4.0.1", "eslint": "^4.1.0", "memory-fs": "^0.4.1", "mocha": "^3.2.0", "nyc": "^11.0.1", "webpack": "^3.0.0"}, "dependencies": {"debug": "^2.6.8"}, "files": ["*.js"], "greenkeeper": {}, "gitHead": "81ec55b7b92d42167888522268c60e2170392f85", "_id": "webpack-virtual-modules@0.1.7", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.2", "_npmUser": {"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}, "dist": {"shasum": "b8b06c6b0d63183be5dc450f5d04d54d29908a53", "size": 3608, "noattachment": false, "tarball": "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.1.7.tgz", "integrity": "sha512-hciECsqaqxMXvu2Gd4mRTbnyCvyHLX9jd855Xmjs/8R61DEcGW+9Bkcu3aTXSyR7pkypNPUuawhtjRn0x83Ctg=="}, "maintainers": [{"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/webpack-virtual-modules-0.1.7.tgz_1500958483361_0.17163064936175942"}, "directories": {}, "publish_time": 1500958484310, "_hasShrinkwrap": false, "_cnpm_publish_time": 1500958484310, "_cnpmcore_publish_time": "2021-12-16T13:27:16.172Z"}, "0.1.5": {"name": "webpack-virtual-modules", "version": "0.1.5", "description": "Webpack Virtual Modules", "main": "index.js", "scripts": {"lint": "eslint --fix .", "posttest": "npm run lint", "test": "NODE_ENV=coverage nyc --check-coverage --lines 100 --branches 100 npm run tests", "tests": "mocha", "tests:watch": "mocha -w"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "author": {"name": "SysGears INC"}, "license": "MIT", "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "devDependencies": {"chai": "^3.5.0", "eslint": "^3.18.0", "memory-fs": "^0.4.1", "mocha": "^3.2.0", "nyc": "^10.2.0", "webpack": "^2.3.2"}, "dependencies": {}, "files": ["*.js"], "greenkeeper": {}, "gitHead": "3f7cdada881dc0dd585063b3c04fcf4c265c7f06", "_id": "webpack-virtual-modules@0.1.5", "_shasum": "88920964c0138820266ab325e5de5e4abbdec3ec", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.2", "_npmUser": {"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}, "dist": {"shasum": "88920964c0138820266ab325e5de5e4abbdec3ec", "size": 3484, "noattachment": false, "tarball": "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.1.5.tgz", "integrity": "sha512-27cY1HXjaSArK/3O3vWD8+IcV70hrGp4MLEmYrnbQmySi3JnXDlD3Tz4ppK+ucTnm7Y/OWemz6cfuacnFcm0bw=="}, "maintainers": [{"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/webpack-virtual-modules-0.1.5.tgz_1490964407090_0.9827194882091135"}, "directories": {}, "publish_time": 1490964407748, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490964407748, "_cnpmcore_publish_time": "2021-12-16T13:27:16.404Z"}, "0.1.4": {"name": "webpack-virtual-modules", "version": "0.1.4", "description": "Webpack Virtual Modules", "main": "index.js", "scripts": {"lint": "eslint --fix .", "posttest": "npm run lint", "test": "NODE_ENV=coverage nyc --check-coverage --lines 100 --branches 100 npm run tests", "tests": "mocha", "tests:watch": "mocha -w"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "author": {"name": "SysGears INC"}, "license": "MIT", "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "devDependencies": {"chai": "^3.5.0", "eslint": "^3.18.0", "memory-fs": "^0.4.1", "mocha": "^3.2.0", "nyc": "^10.2.0", "webpack": "^2.3.2"}, "dependencies": {}, "files": ["*.js"], "greenkeeper": {}, "gitHead": "bb5b05a4bf8fff3f9ab5b62d07c03f8cfcfc65fe", "_id": "webpack-virtual-modules@0.1.4", "_shasum": "22f4a1ea7e21c1366ae990fdea0fdd9e45baeabb", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.2", "_npmUser": {"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}, "dist": {"shasum": "22f4a1ea7e21c1366ae990fdea0fdd9e45baeabb", "size": 3463, "noattachment": false, "tarball": "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.1.4.tgz", "integrity": "sha512-g+ruD+g9jh0Ws2tEcvrPHLpwmN9f4W2t80L/e5s2XVwd6voXS11dwF9IDZJSnn+t7Lrao20vIlGdg3Pv5HXm/A=="}, "maintainers": [{"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/webpack-virtual-modules-0.1.4.tgz_1490963501338_0.5443547931499779"}, "directories": {}, "publish_time": 1490963501908, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490963501908, "_cnpmcore_publish_time": "2021-12-16T13:27:16.703Z"}, "0.1.3": {"name": "webpack-virtual-modules", "version": "0.1.3", "description": "Webpack Virtual Modules", "main": "index.js", "scripts": {"lint": "eslint --fix .", "posttest": "npm run lint", "test": "NODE_ENV=coverage nyc --check-coverage --lines 50 --branches 46 npm run tests", "tests": "mocha", "tests:watch": "mocha -w"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "author": {"name": "SysGears INC"}, "license": "MIT", "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "devDependencies": {"chai": "^3.5.0", "eslint": "^3.18.0", "mocha": "^3.2.0", "nyc": "^10.2.0", "webpack": "^2.3.2"}, "dependencies": {}, "files": ["*.js"], "greenkeeper": {}, "gitHead": "46e3517680f1f2fbfa19787e5ece654c64120da4", "_id": "webpack-virtual-modules@0.1.3", "_shasum": "5ed44dddb5ee088cd671d9cbe0a332da3c6812de", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.2", "_npmUser": {"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}, "dist": {"shasum": "5ed44dddb5ee088cd671d9cbe0a332da3c6812de", "size": 3469, "noattachment": false, "tarball": "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.1.3.tgz", "integrity": "sha512-p6Gk4Q58RDCqujlny4XOza1w5svIybQzuX6XLWXDjF99GLHuJWcIQcmwOqwCGz7zaY5VFTWR502u+FbOF+Fofw=="}, "maintainers": [{"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/webpack-virtual-modules-0.1.3.tgz_1490892988031_0.658678523497656"}, "directories": {}, "publish_time": 1490892988576, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490892988576, "_cnpmcore_publish_time": "2021-12-16T13:27:16.925Z"}, "0.1.2": {"name": "webpack-virtual-modules", "version": "0.1.2", "description": "Webpack Virtual Modules", "main": "index.js", "scripts": {"lint": "eslint --fix .", "posttest": "npm run lint", "test": "NODE_ENV=coverage nyc --check-coverage --lines 50 --branches 46 npm run tests", "tests": "mocha", "tests:watch": "mocha -w"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "author": {"name": "SysGears INC"}, "license": "MIT", "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "devDependencies": {"chai": "^3.5.0", "eslint": "^3.18.0", "mocha": "^3.2.0", "nyc": "^10.2.0", "webpack": "^2.3.2"}, "dependencies": {}, "files": ["*.js"], "greenkeeper": {}, "gitHead": "add9e2e2cfc49e11b78ead1a30e6cedc8f079e03", "_id": "webpack-virtual-modules@0.1.2", "_shasum": "b3a67f90bdc9e0b9cf0b7a3e76a6040bb03a2ed8", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.2", "_npmUser": {"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}, "dist": {"shasum": "b3a67f90bdc9e0b9cf0b7a3e76a6040bb03a2ed8", "size": 3800, "noattachment": false, "tarball": "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.1.2.tgz", "integrity": "sha512-ZyfFTvhjzAoWwaAWrzcg7jn2zzi2o5AbFcSPFSbWJ0T/F5Tv60wd8bq/BJoyZ2rmIR46Cnqqpvy41E+Em9SRYw=="}, "maintainers": [{"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/webpack-virtual-modules-0.1.2.tgz_1490882338464_0.40448667015880346"}, "directories": {}, "publish_time": 1490882340274, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490882340274, "_cnpmcore_publish_time": "2021-12-16T13:27:17.237Z"}, "0.1.1": {"name": "webpack-virtual-modules", "version": "0.1.1", "description": "Webpack Virtual Modules", "main": "src/index.js", "scripts": {"lint": "eslint --fix .", "posttest": "npm run lint", "test": "NODE_ENV=coverage nyc --check-coverage --lines 50 --branches 46 npm run tests", "tests": "mocha", "tests:watch": "mocha -w"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "author": {"name": "SysGears INC"}, "license": "MIT", "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "devDependencies": {"chai": "^3.5.0", "eslint": "^3.18.0", "mocha": "^3.2.0", "nyc": "^10.2.0", "webpack": "^2.3.2"}, "dependencies": {}, "greenkeeper": {}, "gitHead": "a1d713d5f181bc4204a625db8239dcf19da3c949", "_id": "webpack-virtual-modules@0.1.1", "_shasum": "54a51b3eeaf7b688feba8d809a86efc61f0d46a1", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.2", "_npmUser": {"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}, "dist": {"shasum": "54a51b3eeaf7b688feba8d809a86efc61f0d46a1", "size": 34646, "noattachment": false, "tarball": "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.1.1.tgz", "integrity": "sha512-belhnEb6NzcOId5Ak64I//1zxCfw9ZYHy7hSw4Uq/AYIvKs8i6Q9BOH5x4X3D6XKg+voYP4FXlmgnOl2d6kGjA=="}, "maintainers": [{"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/webpack-virtual-modules-0.1.1.tgz_1490880971918_0.9326021631713957"}, "directories": {}, "publish_time": 1490880973794, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490880973794, "_cnpmcore_publish_time": "2021-12-16T13:27:17.477Z"}, "0.4.4": {"name": "webpack-virtual-modules", "version": "0.4.4", "description": "Webpack Virtual Modules", "main": "lib/index.js", "scripts": {"clean": "rm -rf ./lib", "build": "tsc -p tsconfig.build.json", "watch": "tsc -p tsconfig.build.json -w", "tests": "jest", "tests:watch": "jest --watch", "test": "yarn lint && yarn tests", "lint": "eslint --fix src/**/*.ts", "prepack": "yarn clean && yarn build"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "author": "SysGears INC", "license": "MIT", "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "devDependencies": {"@babel/core": "^7.4.5", "@babel/plugin-proposal-class-properties": "^7.4.4", "@babel/plugin-transform-modules-commonjs": "^7.4.4", "@babel/preset-typescript": "^7.3.3", "@babel/register": "^7.5.5", "@types/jest": "^24.0.6", "@types/node": "^11.11.3", "@types/tmp": "^0.1.0", "@types/webpack": "^4.32.1", "@typescript-eslint/eslint-plugin": "^1.13.0", "@typescript-eslint/parser": "^1.13.0", "babel-jest": "^26.6.1", "babel-plugin-replace-ts-export-assignment": "^0.0.2", "eslint": "^7.12.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-jest": "^24.1.0", "eslint-plugin-prettier": "^3.1.4", "husky": "^4.3.0", "jest": "^26.6.1", "lint-staged": "^10.5.0", "memory-fs": "^0.5.0", "prettier": "^2.1.2", "tmp": "^0.2.1", "typescript": "^4.0.5", "webpack": "^5.3.0"}, "publishConfig": {"main": "lib/index.js", "types": "lib/index.d.ts"}, "lint-staged": {"*.ts": ["eslint --fix -c tslint.json", "git add"]}, "prettier": {"printWidth": 120, "singleQuote": true, "parser": "typescript"}, "husky": {"pre-commit": "lint-staged"}, "_id": "webpack-virtual-modules@0.4.4", "dist": {"shasum": "a19fcf371923c59c4712d63d7d194b1e4d8262cc", "integrity": "sha512-h9atBP/bsZohWpHnr+2sic8Iecb60GxftXsWNLLLSqewgIsGzByd2gcIID4nXcG+3tNe4GQG3dLcff3kXupdRA==", "tarball": "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.4.4.tgz", "fileCount": 22, "unpackedSize": 79389, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCb1tc/Y0+7Qmz5XLv8kg1+JVwrEvFuPk37lnkGoYbxswIhANVTVF/47eH3MDgIyjcnVEP8l2vlyq8OsaNF6UfnN4ON"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJitF/sACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqrORAAnK7QaJn8R9XvCVN8BoIs4EEnk8V+1F38UpmuGRwyr2vjwURW\r\nMTqGslW19YS3hnjHNc0e8dZ61hFWmqBgGMohOo4ehBPoNtwWrLr9qYYdmEHA\r\nMuynIoYYjpxgYjVr9qiPbx8MJCyP7oiLa5/aw7trfneDBv/LrPuPD/enx8fN\r\nieSLsUmC3PozrQrq59+eB8QRhWRRhRKiziipJnTz1uWgk5hYSARPugYYykHX\r\nJgW8UgYk0X6mmzjJbab4U+5kAC2/kJejYXRBbNQJXog6/KqmodkHT15J0R/g\r\nwCogM25EWRuqcwIHanWbJ+3C4w3kY3s/lP0BQQY+YAgirxKQVZxiLCyUqYBX\r\n6+SkIBtKwU2u8VWuvQOM0j7gWwPeR04HrJAz1cVGLXDXa9luk1A8NVeW6TBV\r\nuXWfob4iw1ohhDTykj92xRDdV8nYdsbIEsBR32uPB/qfqRtyoWaYHD2X+bWa\r\nWZDuyiOqXrwwvCbJGXGgA6X8jHNnHv5tiwwBC5LhdsLeODwSPgUZUnNbU41r\r\n7XlYhsf//JsCAnnb+jKVbXR1qoHh59vjFohc4lKdjexm8rKd1KNTC/5TWM7J\r\nJGNNDMVLyU3CE5BXvLdI+Ilj5OVgNYcaooaCmXQHsjoZlz2rCLk9YNaOyTgD\r\nueg+NMb5AqAIgCxAM31hwyTkrvxqKwDBOD8=\r\n=ZlgC\r\n-----END PGP SIGNATURE-----\r\n", "size": 16937}, "_npmUser": {"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}, "directories": {}, "maintainers": [{"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/webpack-virtual-modules_0.4.4_1655988204277_0.7596569864283327"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-23T14:15:00.712Z"}, "0.4.5": {"name": "webpack-virtual-modules", "version": "0.4.5", "description": "Webpack Virtual Modules", "main": "lib/index.js", "scripts": {"clean": "rm -rf ./lib", "build": "tsc -p tsconfig.build.json", "watch": "tsc -p tsconfig.build.json -w", "tests": "jest", "tests:watch": "jest --watch", "test": "yarn lint && yarn tests", "lint": "eslint --fix src/**/*.ts", "prepack": "yarn clean && yarn build"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "author": "SysGears INC", "license": "MIT", "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "devDependencies": {"@babel/core": "^7.4.5", "@babel/plugin-proposal-class-properties": "^7.4.4", "@babel/plugin-transform-modules-commonjs": "^7.4.4", "@babel/preset-typescript": "^7.3.3", "@babel/register": "^7.5.5", "@types/jest": "^24.0.6", "@types/node": "^11.11.3", "@types/tmp": "^0.1.0", "@types/webpack": "^4.32.1", "@typescript-eslint/eslint-plugin": "^5.26.0", "@typescript-eslint/parser": "^5.26.0", "babel-jest": "^29.0.3", "babel-plugin-replace-ts-export-assignment": "^0.0.2", "eslint": "^8.23.1", "eslint-config-prettier": "^8.5.0", "eslint-plugin-jest": "^27.0.4", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.1", "jest": "^29.0.3", "lint-staged": "^13.0.3", "memory-fs": "^0.5.0", "prettier": "^2.7.1", "tmp": "^0.2.1", "typescript": "^4.8.3", "webpack": "^5.74.0"}, "publishConfig": {"main": "lib/index.js", "types": "lib/index.d.ts"}, "lint-staged": {"*.ts": ["eslint --fix -c tslint.json", "git add"]}, "prettier": {"printWidth": 120, "singleQuote": true, "parser": "typescript"}, "husky": {"pre-commit": "lint-staged"}, "_id": "webpack-virtual-modules@0.4.5", "gitHead": "e22dd4459d3d78207a22e5a115c24f216dea7272", "dist": {"shasum": "e476842dab5eafb7beb844aa2f747fc12ebbf6ec", "integrity": "sha512-8bWq0Iluiv9lVf9YaqWQ9+liNgXSHICm+rg544yRgGYaR8yXZTVBaHZkINZSB2yZSWo4b0F6MIxqJezVfOEAlg==", "tarball": "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.4.5.tgz", "fileCount": 22, "unpackedSize": 79291, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCu5T0nW6en4Db1Xx6fqoXf8mrA3HrObgMmCrWeyeSIcgIhAJbtVIgUaDLJZseA9SK26exapWjcaAtef1PHFG0MiFZc"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjJWgAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqejxAAiX5nGO0l23osgGzwD3zL1w5fqR3ihPl8V9I+iyJuQExg4WmV\r\nkQaKOXm5T8E1PORqRiUhTc9YLkyXwbjixkB9WaQ1dGcGHejqd2PRqqLdF4pp\r\nEF7X0bOTNrcOIL4aNpA1U8YVFvSz8nxqLtA72zh/xY8MjQhmnlQOLF0tIDvt\r\n5h62c+RRoWDgKsyAqIAGTi8gsQDjNrV2I34qCfzUIHcnCDX4u5Q95+N5IO8H\r\nBmYhvmbYoQuWBgzIjPOFCgF/PYGvRky7lZI3FznljHSwN4F8nutIVeVkET+A\r\nsID+x0TURov+6NgnfLbuXwUAHB2Xbk/xt6tJBXVdRGDkaUBSz0cBURVEBgUo\r\nk2T4grnU9qvj2jJ6x0RsV/6Wgb0MGdM2LHbtI3uFK7xyipmyNmB/eL2U61YG\r\nz0xq5RraTgeF0lKMDO3uMWFsbHpDUjZhXvg+czvlfAChk6RyzMr62Vri87VP\r\nNyOD995b8/TbJBgUWHCmlWlDEDkQPUzOGMsrD6vJQtimg5tcREyYkQN5DbRY\r\noB3woiXksVE0lYB/BRueta2LMyQ9nNHxSl4cXoDlQUtvQTcmDPOOQEYWIz/t\r\nD9IjYs/FfTogA1CvYEomdAwBV/y33DtTiDtDIU8sVPPvVKQH0YlSHLI/vjST\r\njOVMtzK5DwBn4yyCUfRKy2MHgILD8rDJk4Y=\r\n=QRCl\r\n-----END PGP SIGNATURE-----\r\n", "size": 16934}, "_npmUser": {"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}, "directories": {}, "maintainers": [{"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/webpack-virtual-modules_0.4.5_1663395839936_0.36164635381294086"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-09-17T06:24:10.268Z"}, "0.4.6": {"name": "webpack-virtual-modules", "version": "0.4.6", "description": "Webpack Virtual Modules", "main": "lib/index.js", "scripts": {"clean": "rm -rf ./lib", "build": "tsc -p tsconfig.build.json", "watch": "tsc -p tsconfig.build.json -w", "tests": "jest", "tests:watch": "jest --watch", "test": "yarn lint && yarn tests", "lint": "eslint --fix src/**/*.ts", "prepack": "yarn clean && yarn build"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "author": "SysGears INC", "license": "MIT", "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "devDependencies": {"@babel/core": "^7.4.5", "@babel/plugin-proposal-class-properties": "^7.4.4", "@babel/plugin-transform-modules-commonjs": "^7.4.4", "@babel/preset-typescript": "^7.3.3", "@babel/register": "^7.5.5", "@types/jest": "^24.0.6", "@types/node": "^11.11.3", "@types/tmp": "^0.1.0", "@types/webpack": "^4.32.1", "@typescript-eslint/eslint-plugin": "^5.26.0", "@typescript-eslint/parser": "^5.26.0", "babel-jest": "^29.0.3", "babel-plugin-replace-ts-export-assignment": "^0.0.2", "eslint": "^8.23.1", "eslint-config-prettier": "^8.5.0", "eslint-plugin-jest": "^27.0.4", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.1", "jest": "^29.0.3", "lint-staged": "^13.0.3", "memory-fs": "^0.5.0", "prettier": "^2.7.1", "tmp": "^0.2.1", "typescript": "^4.8.3", "webpack": "5"}, "publishConfig": {"main": "lib/index.js", "types": "lib/index.d.ts"}, "lint-staged": {"*.ts": ["eslint --fix -c tslint.json", "git add"]}, "prettier": {"printWidth": 120, "singleQuote": true, "parser": "typescript"}, "husky": {"pre-commit": "lint-staged"}, "_id": "webpack-virtual-modules@0.4.6", "gitHead": "ea53626016db74de66b14401b7377cbc3fc31059", "dist": {"shasum": "3e4008230731f1db078d9cb6f68baf8571182b45", "integrity": "sha512-5tyDlKLqPfMqjT3Q9TAqf2YqjwmnUleZwzJi1A5qXnlBCdj2AtOJ6wAWdglTIDOPgOiOrXeBeFcsQ8+aGQ6QbA==", "tarball": "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.4.6.tgz", "fileCount": 11, "unpackedSize": 40787, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCGPBXLuTphJq3RQ5IuLdeWJHjXqQjVEN6i9eS/au6PlQIgZWiqJt51nUH7BaJXVmik9pXTykfix6UJpDocETiN/Ic="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYMn2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo5Fg/+K1aTbwbzf03ylkmPAw1ek4mr7U0RgM9W6LWx6amL6OcQJrbu\r\nCrKYilJGUUDQmQlxsFHvbzqzAlVOXqMrWY/MgPMZ6u9DLl3mBh521gYC1r5n\r\nm7Vwj02DGHMCpmD0QSweH0WjCn/ml/KSOV8+POeQBE4uOGAw7LMjYKIZ8Pp5\r\n7ue9MtYOQal7pJ7voaOuf7i5SPddwKZvsamSyebho5UMxYXYaZwpCo3XeUbx\r\nmSoWqZYCGNPmU3obntGOyqOqMUMUXizO1T/+neVsevoZRlL41d/fHWxvBsPR\r\nKphrGzWOC0fo4mlEDAo0y/I5uGDr5WVbjJ5ehBGBOpCfdHBK8hlcNSraqwwQ\r\nL5InDR/vlOmH0KqEJ0SLnbFGznJLrRcUBYvwtCuq6le/Xh9OViraOcJxjhHM\r\n1ajY42T0oh4Pz5CJOvB9qXKeZjIuFvHq5n6Q4uDQ/ssaiOjar+ANF3OEmyOJ\r\n64k/2uS/QM1Ck5QOouvwK2IGPIkAdaFvfwkTUG5rT/US1Ob4dNVsjQXi4i8B\r\nDde4J2fs9jqYGQ1UGy52J3Xet+p/b7eIa06gi1B0BZh5Yr+2kmTzlfQrwpna\r\nS+u1hLS9YuiZAqWCk8/4nV9tqr2OWVlkkTkDwdx/hTar8Olnc3hS9IJF26Rm\r\n4BeRQm6cCOI55evI5Nak0bDuJ/zn7o6BRlY=\r\n=9mal\r\n-----END PGP SIGNATURE-----\r\n", "size": 9660}, "_npmUser": {"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}, "directories": {}, "maintainers": [{"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/webpack-virtual-modules_0.4.6_1667287542373_0.5649805157475583"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-11-01T07:25:50.497Z"}, "0.5.0": {"name": "webpack-virtual-modules", "version": "0.5.0", "description": "Webpack Virtual Modules", "main": "lib/index.js", "scripts": {"clean": "rm -rf ./lib", "build": "tsc -p tsconfig.build.json", "watch": "tsc -p tsconfig.build.json -w", "tests": "jest", "tests:watch": "jest --watch", "test": "yarn lint && yarn tests", "lint": "eslint --fix src/**/*.ts", "prepack": "yarn clean && yarn build"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "author": "SysGears INC", "license": "MIT", "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "devDependencies": {"@babel/core": "^7.4.5", "@babel/plugin-proposal-class-properties": "^7.4.4", "@babel/plugin-transform-modules-commonjs": "^7.4.4", "@babel/preset-typescript": "^7.3.3", "@babel/register": "^7.5.5", "@types/jest": "^24.0.6", "@types/node": "^11.11.3", "@types/tmp": "^0.1.0", "@types/webpack": "^4.32.1", "@typescript-eslint/eslint-plugin": "^5.26.0", "@typescript-eslint/parser": "^5.26.0", "babel-jest": "^29.0.3", "babel-plugin-replace-ts-export-assignment": "^0.0.2", "eslint": "^8.23.1", "eslint-config-prettier": "^8.5.0", "eslint-plugin-jest": "^27.0.4", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.1", "jest": "^29.0.3", "lint-staged": "^13.0.3", "memory-fs": "^0.5.0", "prettier": "^2.7.1", "tmp": "^0.2.1", "typescript": "^4.8.3", "webpack": "5"}, "publishConfig": {"main": "lib/index.js", "types": "lib/index.d.ts"}, "lint-staged": {"*.ts": ["eslint --fix -c tslint.json", "git add"]}, "prettier": {"printWidth": 120, "singleQuote": true, "parser": "typescript"}, "husky": {"pre-commit": "lint-staged"}, "_id": "webpack-virtual-modules@0.5.0", "gitHead": "751796750396856e708bec5208c1e5a1048fda85", "dist": {"shasum": "362f14738a56dae107937ab98ea7062e8bdd3b6c", "integrity": "sha512-kyDivFZ7ZM0BVOUteVbDFhlRt7Ah/CSPwJdi8hBpkK7QLumUqdLtVfm/PX/hkcnrvr0i77fO5+TjZ94Pe+C9iw==", "tarball": "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.5.0.tgz", "fileCount": 11, "unpackedSize": 41797, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBbx8p1Lgh79Dfyb+NF78HFUu8dYwQ/93VbranKgs5y6AiEApjuRwPa9zygV2UF9gGPBkJChTre+sktFyePxOX4wkww="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjljOuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp8dA//XUaxKPfUmQ2HVWnYBFlFWm7Ys78XwkK0l/LYwC1j3eso/+/s\r\ndoQNbqtMtQ/zd/aS6DNMpliGkJ7MnS2QF0RAuUR8yOwPT4ByfZ2iyWTs142S\r\n3lRF1O65nJMYRxNDZbR18cadXKUqtkGDx/HmOI1RzQ/kSbZFe+6ZBcigPNCi\r\nlLhLXjhoYhXXo+dq1Go5AZpsQGZ3ewQZ1T50Z/VftrA41lKLllnBfFaLepAH\r\nTjQIZLzrl4IE7PsUcLbuVqESnVTzNRCHY/1/rkRYXmL6RTy7y0W2CO0MoQG8\r\nSyc/Tzd3MaItoqyMxNa1SmMMztnPZ7ETDyhiMaRlD3GIuzHAljPZ6KEX50XK\r\n9mrwLQRalm83RzEXqWC8BA43vFni9CxPZTgbpUJRU+Sug14v+EkQlXtQHJwD\r\ntocpRSptygwxcrRLFMH43XE8GWkAC/6jWhscoBoWtw9rFkKI0PpTWJjVcIb2\r\n6Z/dEi8yDgGtSlejyMe5qN4J2/DjDBY8HF2bOitXpBq9N4QTYelULeePuP66\r\nbK89reWylxS8Ywv1JXgyfnDYPkfmgoel8UpDn+8HDq4N5nzrvBK5YmJTfwTc\r\nfhsg5QKPRlokFBoO0zvV+O0ZSaUrUG6wG4aU9D0DGo6CeeU1tXVx5q0ZHHnm\r\nznEek+dvqS3C7WrB2VkYwhRRZ7zk4hFqUKw=\r\n=XfpD\r\n-----END PGP SIGNATURE-----\r\n", "size": 9928}, "_npmUser": {"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}, "directories": {}, "maintainers": [{"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/webpack-virtual-modules_0.5.0_1670788014673_0.6363565182372972"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-11T19:46:59.189Z"}, "0.6.0": {"name": "webpack-virtual-modules", "version": "0.6.0", "description": "Webpack Virtual Modules", "main": "lib/index.js", "scripts": {"clean": "rm -rf ./lib", "build": "tsc -p tsconfig.build.json", "watch": "tsc -p tsconfig.build.json -w", "tests": "jest", "tests:watch": "jest --watch", "test": "yarn lint && yarn tests", "lint": "eslint --fix src/**/*.ts", "prepack": "yarn clean && yarn build"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "author": "SysGears INC", "license": "MIT", "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "devDependencies": {"@babel/core": "^7.4.5", "@babel/plugin-proposal-class-properties": "^7.4.4", "@babel/plugin-transform-modules-commonjs": "^7.4.4", "@babel/preset-typescript": "^7.3.3", "@babel/register": "^7.5.5", "@types/jest": "^24.0.6", "@types/node": "^11.11.3", "@types/tmp": "^0.1.0", "@types/webpack": "^4.32.1", "@typescript-eslint/eslint-plugin": "^5.26.0", "@typescript-eslint/parser": "^5.26.0", "babel-jest": "^29.0.3", "babel-plugin-replace-ts-export-assignment": "^0.0.2", "eslint": "^8.23.1", "eslint-config-prettier": "^8.5.0", "eslint-plugin-jest": "^27.0.4", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.1", "jest": "^29.0.3", "lint-staged": "^13.0.3", "memory-fs": "^0.5.0", "prettier": "^2.7.1", "tmp": "^0.2.1", "typescript": "^4.8.3", "webpack": "5"}, "publishConfig": {"main": "lib/index.js", "types": "lib/index.d.ts"}, "lint-staged": {"*.ts": ["eslint --fix -c tslint.json", "git add"]}, "prettier": {"printWidth": 120, "singleQuote": true, "parser": "typescript"}, "husky": {"pre-commit": "lint-staged"}, "_id": "webpack-virtual-modules@0.6.0", "gitHead": "85fc6f64695a7e845a6403f50f4f11a921d0e2f0", "dist": {"shasum": "95eb2cb160a8cb84a73bdc2b5c806693f690ad35", "integrity": "sha512-KnaMTE6EItz/f2q4Gwg5/rmeKVi79OR58NoYnwDJqCk9ywMtTGbBnBcfoBtN4QbYu0lWXvyMoH2Owxuhe4qI6Q==", "tarball": "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.6.0.tgz", "fileCount": 11, "unpackedSize": 44912, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCd3eR9qZaVVZZgtsCw62uoEfYe5HAtOVzxw/BZUjCMowIgeaT641nPwg/OKgWHY5/Vo/RWyG9iQA/tbmf7/H6yZ9A="}], "size": 10595}, "_npmUser": {"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}, "directories": {}, "maintainers": [{"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/webpack-virtual-modules_0.6.0_1696346096173_0.5411284023036183"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-03T15:14:56.365Z", "publish_time": 1696346096365, "_source_registry_name": "default"}, "0.6.1": {"name": "webpack-virtual-modules", "version": "0.6.1", "description": "Webpack Virtual Modules", "main": "lib/index.js", "scripts": {"clean": "rm -rf ./lib", "build": "tsc -p tsconfig.build.json", "watch": "tsc -p tsconfig.build.json -w", "tests": "jest", "tests:watch": "jest --watch", "test": "yarn lint && yarn tests", "lint": "eslint --fix src/**/*.ts", "prepack": "yarn clean && yarn build"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "author": "SysGears INC", "license": "MIT", "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "devDependencies": {"@babel/core": "^7.4.5", "@babel/plugin-proposal-class-properties": "^7.4.4", "@babel/plugin-transform-modules-commonjs": "^7.4.4", "@babel/preset-typescript": "^7.3.3", "@babel/register": "^7.5.5", "@types/jest": "^24.0.6", "@types/node": "^11.11.3", "@types/tmp": "^0.1.0", "@types/webpack": "^4.32.1", "@typescript-eslint/eslint-plugin": "^5.26.0", "@typescript-eslint/parser": "^5.26.0", "babel-jest": "^29.0.3", "babel-plugin-replace-ts-export-assignment": "^0.0.2", "eslint": "^8.23.1", "eslint-config-prettier": "^8.5.0", "eslint-plugin-jest": "^27.0.4", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.1", "jest": "^29.0.3", "lint-staged": "^13.0.3", "memory-fs": "^0.5.0", "prettier": "^2.7.1", "tmp": "^0.2.1", "typescript": "^4.8.3", "webpack": "5"}, "publishConfig": {"main": "lib/index.js", "types": "lib/index.d.ts"}, "lint-staged": {"*.ts": ["eslint --fix -c tslint.json", "git add"]}, "prettier": {"printWidth": 120, "singleQuote": true, "parser": "typescript"}, "husky": {"pre-commit": "lint-staged"}, "_id": "webpack-virtual-modules@0.6.1", "gitHead": "dd54b944be432c5e9f3a05a6f8585058203d09ef", "dist": {"shasum": "ac6fdb9c5adb8caecd82ec241c9631b7a3681b6f", "integrity": "sha512-poXpCylU7ExuvZK8z+On3kX+S8o/2dQ/SVYueKA0D4WEMXROXgY8Ez50/bQEUmvoSMMrWcrJqCHuhAbsiwg7Dg==", "tarball": "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.6.1.tgz", "fileCount": 11, "unpackedSize": 45419, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBGyEuFagXsgDtwDH45i5e5ZaWPEZ5XrR9ypNROq2bLUAiB1z9CilbGcayzYVKTqYr9FgR2uCHVOE07KBviqs9lOUw=="}], "size": 10672}, "_npmUser": {"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}, "directories": {}, "maintainers": [{"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/webpack-virtual-modules_0.6.1_1700647126926_0.9577271080226235"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-22T09:58:47.101Z", "publish_time": 1700647127101, "_source_registry_name": "default"}, "0.6.2": {"name": "webpack-virtual-modules", "version": "0.6.2", "description": "Webpack Virtual Modules", "main": "lib/index.js", "scripts": {"clean": "rm -rf ./lib", "build": "tsc -p tsconfig.build.json", "watch": "tsc -p tsconfig.build.json -w", "tests": "jest", "tests:watch": "jest --watch", "test": "yarn lint && yarn tests", "lint": "eslint --fix src/**/*.ts", "prepack": "yarn clean && yarn build"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "author": "SysGears INC", "license": "MIT", "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "devDependencies": {"@babel/core": "^7.4.5", "@babel/plugin-proposal-class-properties": "^7.4.4", "@babel/plugin-transform-modules-commonjs": "^7.4.4", "@babel/preset-typescript": "^7.3.3", "@babel/register": "^7.5.5", "@types/jest": "^24.0.6", "@types/node": "^11.11.3", "@types/tmp": "^0.1.0", "@types/webpack": "^4.32.1", "@typescript-eslint/eslint-plugin": "^5.26.0", "@typescript-eslint/parser": "^5.26.0", "babel-jest": "^29.0.3", "babel-plugin-replace-ts-export-assignment": "^0.0.2", "eslint": "^8.23.1", "eslint-config-prettier": "^8.5.0", "eslint-plugin-jest": "^27.0.4", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.1", "jest": "^29.0.3", "lint-staged": "^13.0.3", "memory-fs": "^0.5.0", "prettier": "^2.7.1", "tmp": "^0.2.1", "typescript": "^4.8.3", "webpack": "5"}, "publishConfig": {"main": "lib/index.js", "types": "lib/index.d.ts"}, "lint-staged": {"*.ts": ["eslint --fix -c tslint.json", "git add"]}, "prettier": {"printWidth": 120, "singleQuote": true, "parser": "typescript"}, "husky": {"pre-commit": "lint-staged"}, "_id": "webpack-virtual-modules@0.6.2", "gitHead": "04350c6780c0929776706f46a6983b00514aced0", "dist": {"shasum": "057faa9065c8acf48f24cb57ac0e77739ab9a7e8", "integrity": "sha512-66/V2i5hQanC51vBQKPH4aI8NMAcBW59FVBs+rC7eGHupMyfn34q7rZIE+ETlJ+XTevqfUhVVBgSUNSW2flEUQ==", "tarball": "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.6.2.tgz", "fileCount": 11, "unpackedSize": 46451, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF/0k89TNWD7mViosFNnSrfkbriGPkMk0x4p6y8q0Gp2AiEAh6tkp+vPhfNJvyT4hs89deDtFqJY2qZYcVbUE3X8BAo="}], "size": 10846}, "_npmUser": {"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}, "directories": {}, "maintainers": [{"name": "vic<PERSON><PERSON>", "email": "victor.v<PERSON><PERSON>@sysgears.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/webpack-virtual-modules_0.6.2_1717516179265_0.29987307223524096"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-04T15:49:39.434Z", "publish_time": 1717516179434, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "_source_registry_name": "default"}