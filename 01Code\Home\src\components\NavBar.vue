<template>
  <nav class="navbar" :class="{ 'scrolled': isScrolled,'force-white': forceWhiteNavbar }">
    <div class="navbar-left">
      <p>数媒灵犀</p>
    </div>
    <div class="navbar-center">
      <!-- 导航链接保持不变 -->
      <router-link class="nav-link" :to="{ name: 'Home' }">首页</router-link>
      <router-link class="nav-link" :to="{ name: 'pro-content' }">专业内容</router-link>
      <router-link class="nav-link" :to="{ name: 'course-center' }">课程中心</router-link>
      <router-link class="nav-link" :to="{ name: 'course-map' }">课程图谱</router-link>
      <router-link class="nav-link" :to="{ name: 'db-resource' }">数媒资源</router-link>
      <router-link
          class="nav-link"
          :to="authStore.isTeacher ? { name: 'intelligent2' } : { name: 'intelligent' }"
      >
        数媒智能广场
      </router-link>
    </div>
    <div class="navbar-right">
      <template v-if="authStore.isAuthenticated">
        <div class="user-dropdown" @mouseenter="showDropdown = true" @mouseleave="showDropdown = false">
          <div class="user-profile">
            <!-- 修改头像显示，使用后端返回的avatar，没有则使用默认头像 -->
            <img 
              :src="authStore.user.avatar || defaultAvatar" 
              alt="用户头像" 
              class="user-avatar"
            >
            <span class="user-info">
              <!-- 修改用户名显示，优先使用name，没有则使用account -->
              <span class="user-name">{{ authStore.user.name || authStore.user.account }}</span>
              <!-- 修改身份显示，使用计算属性获取角色名称 -->
              <span class="user-role">{{ roleDisplayName }}</span>
            </span>
          </div>
          
          <transition name="dropdown">
            <div v-show="showDropdown" class="dropdown-menu">
              <!-- 根据角色显示不同菜单项 -->
              <router-link 
                v-if="authStore.isTeacher"
                :to="{ name: 'TeachClass' }" 
                class="dropdown-item"
              >
                教学课堂
              </router-link>
              <router-link 
                v-else-if="authStore.isStudent"
                :to="{ name: 'StudentClass' }" 
                class="dropdown-item"
              >
                我的课堂
              </router-link>
              
              <router-link 
                :to="{ name: authStore.isTeacher ? 'TeacherAccount' : 'StudentAccount' }" 
                class="dropdown-item"
              >
                账号管理
              </router-link>
              <div class="dropdown-divider"></div>
              <a @click.prevent="handleLogout" class="dropdown-item">
                安全退出
              </a>
            </div>
          </transition>
        </div>
      </template>
      <template v-else>
        <router-link class="login-btn" to="/login">登录</router-link>
        <router-link class="register-btn" to="/register">注册</router-link>
      </template>
    </div>
  </nav>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/auth/auth.js'
import { useRoute, useRouter } from 'vue-router'
import defaultAvatar from '@/assets/img/Teacher/avatar-male.png'

const authStore = useAuthStore()
const router = useRouter()
const isScrolled = ref(false)
const showDropdown = ref(false)
const route = useRoute()

// 修改角色显示计算属性，确保与后端返回的role字段一致
const roleDisplayName = computed(() => {
  switch(authStore.user?.role) {
    case 1: return '学生'
    case 2: return '教师'
    case 0: return '管理员'
    default: return '未知角色'
  }
})

const forceWhiteNavbar = computed(() => {
  return route.matched.some(routeRecord => routeRecord.meta.forceNavbarWhite)
})

const handleScroll = () => {
  isScrolled.value = window.scrollY > 10
}

const handleLogout = () => {
  authStore.logout()
  router.push('/')
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
  handleScroll()
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style lang="scss" scoped>
@use '@/styles/navbar.scss';
</style>