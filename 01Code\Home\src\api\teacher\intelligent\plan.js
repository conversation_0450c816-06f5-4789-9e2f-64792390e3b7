import OpenAI from 'openai';
import { getKey } from '../../key.js';

// 生成教案内容
export const generateLessonPlan = async (title, style, keyPoints, studentType) => {
  const deepseekApiKey = getKey('deepseek');
  const openai = new OpenAI({
    baseURL: 'https://api.deepseek.com',
    apiKey: deepseekApiKey,
    dangerouslyAllowBrowser: true
  });

  try {
    const completion = await openai.chat.completions.create({
      model: 'deepseek-chat',
      messages: [
        {
          role: "system",
          content: `你是一位经验丰富的IT讲师，需要为《${title}》课程准备详细教案。请根据以下要求：
          1. 提供具体可操作的教学内容，不要只是框架
          2. 包含实际的技术要点和示例
          3. 针对${studentType}学生调整内容深度
          4. 采用${style}教学风格
          5. 核心知识点必须包含: ${keyPoints}

          输出格式要求：
          - 纯文本格式，不要用任何Markdown符号
          - 每个教学环节都要有具体内容
          - 关键技术点要给出示例说明`
        },
        {
          role: "user",
          content: `请为《${title}》课程生成详细教案，需要包含：
          1. 实际要讲解的技术内容
          2. 具体的操作步骤演示
          3. 适合学生水平的示例
          4. 可实施的课堂练习`
        }
      ],
      temperature: 0.7,
      max_tokens: 2000
    });

    // 后处理：确保内容充实度
    let content = completion.choices[0].message.content;
    if(content.split('\n').length < 15) {
      throw new Error("生成内容过于简略，请扩充");
    }
    return content;
  } catch (error) {
    console.error('教案生成失败:', error);
    throw error;
  }
};
