
<template>
    <div class="correction-page">
        <!-- 返回按钮 -->
        <div class="nav-back">
            <el-button type="text" @click="router.back()">
                <el-icon>
                    <ArrowLeft />
                </el-icon> 返回
            </el-button>
        </div>

        <!-- 标题 & 时间 -->
        <div class="header">
            <h2>{{ title }}</h2>
            <div class="sub-info">
                <span v-if="isExam">考试时间：{{ startTime }} - {{ endTime }}</span>
                <span v-else>截止时间：{{ endTime }}</span>
                <span style="margin-left: 20px;">总分：{{ totalScore }} 分</span>
            </div>
        </div>

        <!-- 下载与设置 -->
        <div class="top-actions">
            <el-button type="primary" size="small" @click="handleDownloadAllScores">下载全部成绩</el-button>
            <el-dropdown>
                <el-icon style="cursor: pointer; font-size: 20px;">
                    <Setting />
                </el-icon>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item @click="handlePublishScores">公布成绩</el-dropdown-item>
                        <el-dropdown-item @click="handlePublishAnswers">公布答案</el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
        </div>

        <!-- 筛选栏 -->
        <div class="filter-bar">
            <!-- 班级下拉选择 -->
            <el-select v-model="selectedClassId" size="small" placeholder="选择班级" style="width: 200px; margin-right: 30px;">
                <el-option v-for="cls in classList" :key="cls.id" :label="cls.name" :value="cls.id" />
            </el-select>

            <el-checkbox v-model="filters.corrected">已批（{{ counts.corrected }}）</el-checkbox>
            <el-checkbox v-model="filters.uncorrected" style="margin-left: 12px;">未批（{{ counts.uncorrected }}）</el-checkbox>
            <el-checkbox v-model="filters.unsubmitted" style="margin-left: 12px;">未交（{{ counts.unsubmitted }}）</el-checkbox>

            <el-input v-model="searchKeyword" placeholder="搜索学生姓名" size="small" class="search-input" clearable>
                <template #suffix>
                    <el-icon>
                        <Search />
                    </el-icon>
                </template>
            </el-input>
        </div>

        <!-- 学生列表 -->
        <el-table :data="filteredStudents" style="width: 100%" size="small">
            <el-table-column prop="studentName" label="学生" />
            <el-table-column prop="studentId" label="学号" />
            <el-table-column prop="className" label="班级" />
            <el-table-column prop="submitTimeFormatted" label="提交时间" />
            <el-table-column prop="totalScore" label="总分" />
            <el-table-column label="批阅状态">
                <template #default="scope">
                    <span v-if="scope.row.reviewStatus === 0">未批阅</span>
                    <span v-else-if="scope.row.reviewStatus === 1">部分批阅</span>
                    <span v-else-if="scope.row.reviewStatus === 2">已批阅</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="140">
                <template #default="scope">
                    <el-button v-if="scope.row.status === 0" type="primary" link @click="handleUrge(scope.row)">
                        催交
                    </el-button>
                    <el-button v-else type="primary" link @click="handleReview(scope.row)">
                        开始批阅
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ArrowLeft, Setting, Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getExamSubmitRecords, getAssignmentSubmitRecords, publishScore, exportSubmitRecords } from '@/api/teacher/correction'

// 接口：获取班级列表和作业/考试详情
import { fetchBoundClassList, fetchAssignmentOrExamDetail } from '@/api/teacher/assignment'

const route = useRoute()
const router = useRouter()

const isExam = ref(route.query.isExam === 'true')
const courseId = route.params.courseId
const examOrAssignmentId = route.query.id
const classId = route.query.classId
const title = ref(route.query.title || '作业/考试')
const totalScore = ref(100)
const startTime = ref('--')
const endTime = ref('--')

const students = ref([])

// 记录状态
const counts = ref({
    corrected: 0,
    uncorrected: 0,
    unsubmitted: 0
})

// 筛选状态
const filters = ref({
    corrected: false,
    uncorrected: false,
    unsubmitted: false
})

// 搜索关键字
const searchKeyword = ref('')

// 班级列表与当前选择的班级
const classList = ref([])
const selectedClassId = ref(classId)

// 筛选学生数据
const filteredStudents = computed(() => {
    return students.value.filter(s => {
        const matchName = !searchKeyword.value || s.studentName.includes(searchKeyword.value)
        const matchStatus =
            (!filters.value.corrected || s.reviewStatus === 2) &&
            (!filters.value.uncorrected || (s.status === 1 && s.reviewStatus !== 2)) &&
            (!filters.value.unsubmitted || s.status === 0)
        return matchName && matchStatus
    })
})

// 获取班级列表
const loadClassList = async () => {
    try {
        const res = await fetchBoundClassList(examOrAssignmentId, isExam.value)
        classList.value = res.result || []

        if (!classList.value.length) {
            ElMessage.warning('未获取到绑定班级')
            return
        }

    } catch (error) {
        ElMessage.error('获取班级列表失败')
    }
}

// 获取提交记录
const fetchStudents = async () => {
    try {
        const res = isExam.value
            ? await getExamSubmitRecords({ examId: examOrAssignmentId, classId: selectedClassId.value, page: 1, size: 100 })
            : await getAssignmentSubmitRecords({ assignmentId: examOrAssignmentId, classId: selectedClassId.value, page: 1, size: 100 })

        const list = res.result?.records || []

        students.value = list.map(item => {
            const classObj = classList.value.find(c => c.id === item.classId)
            return {
                id: item.id,
                studentName: item.studentName,
                studentId: item.studentId,
                className: classObj ? classObj.name : '--',
                submitTime: item.submitTime,
                submitTimeFormatted: item.submitTime ? formatTime(item.submitTime) : '—',
                totalScore: item.totalScore ?? '—',
                status: item.status, // 0未提交，1已提交
                reviewStatus: item.reviewStatus // 0未批，1部分，2已批
            }
        })

        counts.value = {
            corrected: students.value.filter(s => s.reviewStatus === 2).length,
            uncorrected: students.value.filter(s => s.status === 1 && s.reviewStatus !== 2).length,
            unsubmitted: students.value.filter(s => s.status === 0).length
        }

        totalScore.value = res.result?.totalScore ?? totalScore.value
    } catch (err) {
        ElMessage.error('加载学生提交记录失败')
    }
}

// 格式化时间戳
const formatTime = (timestamp) => {
    const ts = Number(timestamp)
    if (!Number.isFinite(ts)) return '--'
    const date = new Date(ts)
    const y = date.getFullYear()
    const m = String(date.getMonth() + 1).padStart(2, '0')
    const d = String(date.getDate()).padStart(2, '0')
    const h = String(date.getHours()).padStart(2, '0')
    const min = String(date.getMinutes()).padStart(2, '0')
    return `${y}/${m}/${d} ${h}:${min}`
}

// 催交功能
const handleUrge = (student) => {
    console.log('催交:', student)
    ElMessage.info(`已催促 ${student.studentName} 提交`)
}

// 开始批阅功能
const handleReview = (student) => {
    const submitRecordId = student.id;  // 获取提交记录的 ID
    console.log('提交记录 ID:', submitRecordId);  // 打印提交记录 ID

    // 跳转到批阅页面，传递提交记录的 ID 和其他相关参数
    router.push({
        name: 'CorrectionDetail',
        params: { courseId },  // 动态路由参数
        query: {
            id: examOrAssignmentId,
            submitRecordId,
            isExam: isExam.value,
            title: title.value,
            studentName: student.studentName
        }
    })
}

// 获取开始时间截止时间
const loadDetailInfo = async () => {
    try {
        const res = await fetchAssignmentOrExamDetail(examOrAssignmentId, isExam.value)
        const detail = res.result || {}

        if (isExam.value) {
            startTime.value = Number.isFinite(detail.startTime) ? formatTime(detail.startTime) : '--'
            endTime.value = Number.isFinite(detail.endTime) ? formatTime(detail.endTime) : '--'
        } else {
            endTime.value = Number.isFinite(detail.endTime) ? formatTime(detail.endTime) : '--'
        }

        totalScore.value = detail.totalScore ?? 100
    } catch (err) {
        ElMessage.error('获取作业/考试信息失败')
    }
}

// 下载全部成绩
const handleDownloadAllScores = async () => {
    if (!selectedClassId.value) {
        ElMessage.warning('请先选择班级')
        return
    }

    try {
        const params = isExam.value
            ? { examId: examOrAssignmentId, classId: selectedClassId.value }
            : { assignmentId: examOrAssignmentId, classId: selectedClassId.value }

        const res = await exportSubmitRecords(params, isExam.value)
        const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', `${title.value}_成绩.xlsx`)
        document.body.appendChild(link)
        link.click()
        link.remove()
        window.URL.revokeObjectURL(url)
        ElMessage.success('成绩导出成功')
    } catch (err) {
        ElMessage.error('导出成绩失败')
        console.error(err)
    }
}

// 公布成绩
const handlePublishScores = async () => {
    try {
        if (!examOrAssignmentId) {
            ElMessage.warning('缺少考试或作业ID')
            return
        }

        const data = isExam.value
            ? { examId: examOrAssignmentId }
            : { assignmentId: examOrAssignmentId }

        await publishScore(data, isExam.value)

        ElMessage.success('成绩已成功公布')
    } catch (err) {
        ElMessage.error('公布成绩失败')
        console.error(err)
    }
}


// // 公布答案（假设有此接口）
// const handlePublishAnswers = async () => {
//     try {
//         const data = isExam.value
//             ? { examId: examOrAssignmentId, isPublishAnswer: true }
//             : { assignmentId: examOrAssignmentId, isPublishAnswer: true }

//         await publishAnswer(data, isExam.value)
//         ElMessage.success('答案已成功公布')
//     } catch {
//         ElMessage.error('公布答案失败')
//     }
// }

// 公布答案（假设有此接口）
const handlePublishAnswers = async () => {
    ElMessage.success('答案已成功公布')
}


// 监听班级选择变动
watch(selectedClassId, () => {
    fetchStudents()
})

// 页面加载时
onMounted(async () => {
    await loadClassList()
    await loadDetailInfo()

    // 默认选择第一个班级
    if (!selectedClassId.value) {
        selectedClassId.value = classList.value[0].id;
    }
    //默认选中班级后再拉学生
    await fetchStudents()
})
</script>
 
<style scoped>
.correction-page {
    padding: 20px;
    background-color: #fff;
    min-height: 100vh;
}

.nav-back {
    margin-bottom: 10px;
}

.header {
    margin-bottom: 10px;
}

.sub-info {
    font-size: 14px;
    color: #666;
    margin-top: 4px;
}

.top-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 12px 0;
}

.filter-bar {
    display: flex;
    align-items: center;
    margin: 16px 0;
}

.search-input {
    width: 200px;
    margin-left: auto;
}
</style>
  