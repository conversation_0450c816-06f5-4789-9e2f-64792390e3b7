<template>
  <div class="auth-info">
    <!-- 实名认证 -->
    <div class="auth-section">
      <h3>实名认证</h3>
      <el-tag :type="isRealNameAuthenticated ? 'success' : 'danger'" circle>
        {{ isRealNameAuthenticated ? '已认证' : '未认证' }}
      </el-tag>
      <el-button type="primary" @click="startRealNameAuth" v-if="!isRealNameAuthenticated">立即认证</el-button>
    </div>

    <!-- 职业认证 -->
    <div class="auth-section">
      <h3>职业认证</h3>
      <el-tag :type="isJobAuthenticated ? 'success' : 'danger'" circle>
        {{ isJobAuthenticated ? '已认证' : '未认证' }}
      </el-tag>
      <el-button type="primary" @click="startJobAuth" v-if="!isJobAuthenticated">立即认证</el-button>
    </div>

    <!-- 实名认证弹窗 -->
    <el-dialog v-model="realNameDialogVisible" title="实名认证">
      <el-form :model="realNameAuthForm" ref="realNameAuthForm" label-width="120px">
        <el-form-item label="姓名" :required="true">
          <el-input v-model="realNameAuthForm.name" placeholder="请输入您的姓名"></el-input>
        </el-form-item>
        <el-form-item label="性别">
          <el-radio-group v-model="realNameAuthForm.gender">
            <el-radio label="男">男</el-radio>
            <el-radio label="女">女</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="证件上传">
          <el-upload
            class="upload-demo"
            drag
            action="#"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :file-list="fileList"
            :auto-upload="false">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传jpg/png文件，且不超过2M</div>
          </el-upload>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitRealNameAuth">提交</el-button>
          <el-button @click="realNameDialogVisible = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 职业认证弹窗 -->
    <el-dialog v-model="jobDialogVisible" title="职业认证">
      <el-form :model="jobAuthForm" ref="jobAuthForm" label-width="120px">
        <el-form-item label="学校/单位">
          <el-input v-model="jobAuthForm.organization" placeholder="请输入您的学校或单位"></el-input>
        </el-form-item>
        <el-form-item label="院系/部门">
          <el-input v-model="jobAuthForm.department" placeholder="请输入您的院系或部门"></el-input>
        </el-form-item>
        <el-form-item label="职业证上传">
          <el-upload
            class="upload-demo"
            drag
            action="#"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :file-list="fileList"
            :auto-upload="false">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传jpg/png文件，且不超过2M</div>
          </el-upload>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitJobAuth">提交</el-button>
          <el-button @click="jobDialogVisible = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const isRealNameAuthenticated = ref(false); // 实名认证状态
const isJobAuthenticated = ref(false); // 职业认证状态
const realNameDialogVisible = ref(false);
const jobDialogVisible = ref(false);
const realNameAuthForm = ref({
  name: '',
  gender: '',
  idNumber: ''
});
const jobAuthForm = ref({
  organization: '',
  department: ''
});
const fileList = ref([]);

const startRealNameAuth = () => {
  realNameDialogVisible.value = true;
};

const submitRealNameAuth = () => {
  console.log('提交实名认证信息:', realNameAuthForm.value);
  // 在这里添加提交实名认证逻辑
  isRealNameAuthenticated.value = true; // 假设认证成功
  realNameDialogVisible.value = false;
};

const startJobAuth = () => {
  jobDialogVisible.value = true;
};

const submitJobAuth = () => {
  console.log('提交职业认证信息:', jobAuthForm.value);
  // 在这里添加提交职业认证逻辑
  isJobAuthenticated.value = true; // 假设认证成功
  jobDialogVisible.value = false;
};

const handlePreview = (file) => {
  console.log('Preview:', file);
};

const handleRemove = (file, fileList) => {
  console.log('Remove:', file, fileList);
};
</script>

<style scoped>
.auth-info {
  padding: 20px;
}

.auth-section {
  margin-bottom: 20px;
}

.upload-demo {
  width: 100%;
}
</style>