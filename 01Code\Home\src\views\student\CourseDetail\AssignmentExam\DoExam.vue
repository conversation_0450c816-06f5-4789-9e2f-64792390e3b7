<template>
    <div class="submit-homework-page">
        <!-- 顶部导航 -->
        <div class="top-bar">
            <el-button type="text" icon="el-icon-arrow-left" @click="goBack">返回</el-button>
        </div>

        <div class="content-area">
            <!-- 左侧题目区 -->
            <div class="left-area" v-if="currentQuestion">
                <div class="question-section">
                    <h3>{{ currentSectionTitle }}</h3>
                    <div class="question-item active">
                        <div class="question-header">
                            <span>{{ currentQuestion.index }}. {{ currentQuestion.content }}</span>
                            <span class="question-type">【{{ currentQuestion.typeText }}】</span>
                            <span class="score">({{ currentQuestion.score }}分)</span>
                        </div>

                        <!-- 多选题 -->
                        <el-checkbox-group v-if="currentQuestion.type === 'multiple'" v-model="answers[currentQuestion.id]"
                            class="vertical-options">
                            <div class="option-line" v-for="opt in currentQuestion.options" :key="opt.key">
                                <el-checkbox :label="opt.key">{{ opt.key }} . {{ opt.text }}</el-checkbox>
                            </div>
                        </el-checkbox-group>

                        <!-- 单选题 -->
                        <el-radio-group v-else-if="currentQuestion.type === 'single'" v-model="answers[currentQuestion.id]"
                            class="vertical-options">
                            <div class="option-line" v-for="opt in currentQuestion.options" :key="opt.key">
                                <el-radio :label="opt.key">{{ opt.key }} . {{ opt.text }}</el-radio>
                            </div>
                        </el-radio-group>

                        <!-- 判断题 -->
                        <div v-else-if="currentQuestion.type === 'truefalse'">
                            <el-radio-group v-model="answers[currentQuestion.id]" class="vertical-options">
                                <el-radio label="A">正确</el-radio>
                                <el-radio label="B">错误</el-radio>
                            </el-radio-group>
                        </div>

                        <!-- 填空题 -->
                        <div v-else-if="currentQuestion.type === 'blank'">
                            <el-input v-model="answers[currentQuestion.id]" placeholder="请填写答案" clearable
                                style="width: 300px;" />
                        </div>

                        <!-- 简答题 -->
                        <div v-else-if="currentQuestion.type === 'short'">
                            <el-input type="textarea" v-model="answers[currentQuestion.id]" placeholder="请填写简答题答案" rows="4"
                                style="width: 400px;" />
                        </div>
                    </div>

                    <!-- 底部按钮 -->
                    <div class="bottom-btns">
                        <el-button type="warning" @click="toggleMarkCurrent">标记此题</el-button>
                        <div class="nav-buttons">
                            <el-button :disabled="isFirstQuestion" @click="prevQuestion">上一题</el-button>
                            <el-button type="primary" :disabled="isLastQuestion" @click="nextQuestion">下一题</el-button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧信息区 -->
            <div class="right-area">
                <div class="info-card">
                    <h3>{{ homework.title }}</h3>
                    <div class="info-row">
                        <div class="info-block">
                            <div class="label">总分</div>
                            <div class="value">{{ totalScore }}</div>
                        </div>
                        <div class="info-block">
                            <div class="label">题目数</div>
                            <div class="value">{{ totalQuestions }}</div>
                        </div>
                    </div>
                    <div class="deadline">截止时间 {{ homework.deadline }}</div>
                </div>

                <div class="answer-card">
                    <div class="answer-card-title">答题卡</div>
                    <div v-for="section in questionSections" :key="section.title" class="answer-section">
                        <div class="answer-section-title">{{ section.title }}</div>
                        <el-button v-for="q in section.questions" :key="q.id"
                            :type="isCurrentQuestion(q) ? 'success' : 'default'" @click="goToQuestion(q)" size="small"
                            class="answer-btn">
                            {{ q.index }}
                        </el-button>
                    </div>
                    <el-button type="success" class="submit-btn" @click="submitHomework">提交作业</el-button>
                </div>
            </div>
        </div>
    </div>
    <!-- 确认提交弹窗 -->
    <el-dialog v-model="showConfirm" title="确认提交" width="400px" :close-on-click-modal="false" :draggable="true">
        <div>是否确认提交所有答案？提交后将无法修改。</div>
        <template #footer>
            <el-button @click="showConfirm = false">取消</el-button>
            <el-button type="primary" @click="handleConfirm">确认提交</el-button>
        </template>
    </el-dialog>
</template>
  
<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { fetchDetail, submitAnswers } from '@/api/student/exam-assignment/studentExam'

const showConfirm = ref(false) // 控制弹窗显示
const confirmCallback = ref(null) // 提交回调


// 路由参数
const route = useRoute()
const assignmentOrExamId = route.query.id
const isExam = route.query.isExam === 'true'

// 作业/考试基础信息
const homework = reactive({
    title: '',
    deadline: ''
})

// 所有题目结构化分组
const questionSections = reactive([])

// 答案记录
const answers = reactive({})

// 当前题目 id
const currentQuestionId = ref(null)

// 标记题目集合
const markedQuestions = reactive(new Set())

// 转换时间戳
function formatTime(timestamp) {
    if (!timestamp) return ''
    const date = new Date(timestamp)
    const y = date.getFullYear()
    const m = String(date.getMonth() + 1).padStart(2, '0')
    const d = String(date.getDate()).padStart(2, '0')
    const h = String(date.getHours()).padStart(2, '0')
    const min = String(date.getMinutes()).padStart(2, '0')
    return `${y}-${m}-${d} ${h}:${min}`
}

// 获取并处理作业/考试详情
async function loadHomeworkDetail() {
    console.log('传入的 ID:', assignmentOrExamId, '| 是否考试:', isExam)

    if (!assignmentOrExamId || assignmentOrExamId === 'undefined' || assignmentOrExamId === 'null') {
        ElMessage.error('缺少作业或考试 ID')
        return
    }

    try {
        const res = await fetchDetail({ id: assignmentOrExamId, isExam })
        const detail = res.result || res

        console.log('获取详情成功:', detail)

        homework.title = detail.title || ''
        // 赋值时使用格式化
        homework.deadline = formatTime(detail.endTime)

        let index = 1
        questionSections.length = 0 // 清空旧数据

        // 公共处理函数：解析题目并分类
        const pushSection = (list, type, typeText, defaultScore = 10) => {
            if (!list?.length) return
            questionSections.push({
                title: `${typeText}题`,
                questions: list.map((item) => {
                    const q = item.question
                    return {
                        id: q.id,
                        index: index++,
                        type,
                        typeText,
                        content: q.content,
                        score: item.score || defaultScore,
                        options: q.options
                            ? (() => {
                                try {
                                    const obj = typeof q.options === 'string' ? JSON.parse(q.options) : q.options;
                                    return Object.entries(obj).map(([key, text]) => ({ key, text }));
                                } catch (e) {
                                    console.warn('选项解析失败', e);
                                    return [];
                                }
                            })()
                            : []
                    }
                })
            })
        }

        // 将题目分组推入
        pushSection(detail.singleChoiceList, 'single', '单选')
        pushSection(detail.multipleChoiceList, 'multiple', '多选')
        pushSection(detail.trueFalseList, 'truefalse', '判断')
        pushSection(detail.fillInBlankList, 'blank', '填空', 20)
        pushSection(detail.shortAnswerList, 'short', '简答', 20)

        console.log('渲染后的题组:', questionSections)

        // 设置当前题目为第一题
        currentQuestionId.value = questionSections[0]?.questions[0]?.id || null
    } catch (err) {
        console.error('获取作业/考试详情失败:', err)
        ElMessage.error('加载作业/考试失败')
    }
}

// 生命周期加载
onMounted(() => {
    loadHomeworkDetail()
})

/* ----------- 题目相关计算属性 ----------- */

// 当前题目对象
const currentQuestion = computed(() =>
    questionSections.flatMap((s) => s.questions).find((q) => q.id === currentQuestionId.value)
)

// 当前题目所在的组名
const currentSectionTitle = computed(() =>
    questionSections.find((sec) => sec.questions.find((q) => q.id === currentQuestionId.value))?.title || ''
)

// 扁平化题目列表
const questionFlatList = computed(() => questionSections.flatMap((sec) => sec.questions))

// 当前题目索引及边界
const currentQuestionIndex = computed(() =>
    questionFlatList.value.findIndex((q) => q.id === currentQuestionId.value)
)
const isFirstQuestion = computed(() => currentQuestionIndex.value <= 0)
const isLastQuestion = computed(() => currentQuestionIndex.value >= questionFlatList.value.length - 1)

/* ----------- 控制切换题目 ----------- */
function prevQuestion() {
    if (!isFirstQuestion.value) {
        currentQuestionId.value = questionFlatList.value[currentQuestionIndex.value - 1].id
    }
}
function nextQuestion() {
    if (!isLastQuestion.value) {
        currentQuestionId.value = questionFlatList.value[currentQuestionIndex.value + 1].id
    }
}
function goToQuestion(q) {
    currentQuestionId.value = q.id
}

/* ----------- 标记题目 ----------- */
function toggleMarkCurrent() {
    if (markedQuestions.has(currentQuestionId.value)) {
        markedQuestions.delete(currentQuestionId.value)
    } else {
        markedQuestions.add(currentQuestionId.value)
    }
}
function isCurrentQuestion(q) {
    return q.id === currentQuestionId.value
}

/* ----------- 提交 ----------- */
async function submitHomework() {
    const flatQuestions = questionSections.flatMap(sec => sec.questions)

    const unansweredCount = flatQuestions.reduce((count, q) => {
        const ans = answers[q.id]
        return count + (isAnswerEmpty(ans) ? 1 : 0)
    }, 0)

    if (unansweredCount > 0) {
        ElMessage.warning({
            message: `还有${unansweredCount}题未作答`,
            offset: 90
        })
        return
    }

    const payload = flatQuestions.map(q => {
        const ans = answers[q.id]
        let formattedAnswer
        if (q.type === 'multiple' || q.type === 'blank') {
            formattedAnswer = JSON.stringify(Array.isArray(ans) ? ans : [ans])
        } else {
            formattedAnswer = Array.isArray(ans) ? ans.join(',') : ans
        }

        return {
            questionType: Number(mapTypeToCode(q.type)),
            questionId: q.id,
            answer: formattedAnswer
        }
    })

    confirmCallback.value = async () => {
        try {
            console.log('提交 assignmentOrExamId:', assignmentOrExamId)
            console.log('提交 payload:', payload)

            await submitAnswers({
                id: assignmentOrExamId,
                isExam,
                answers: payload
            })

            ElMessage.success({
                message: '提交成功！',
                offset: 90
            })

            goBack()
        } catch (err) {
            console.error('提交失败:', err)
            ElMessage.error({
                message: '提交失败，请稍后重试',
                offset: 90
            })
        }
    }

    showConfirm.value = true
}

function mapTypeToCode(type) {
    switch (type) {
        case 'single': return 0
        case 'multiple': return 1
        case 'truefalse': return 2
        case 'blank': return 3
        case 'short': return 4
        default: return -1
    }
}

// 查验空答案
function isAnswerEmpty(ans) {
    if (ans === undefined || ans === null) return true
    if (typeof ans === 'string' && ans.trim() === '') return true
    if (Array.isArray(ans) && ans.length === 0) return true
    if (typeof ans === 'object' && !Array.isArray(ans) && Object.keys(ans).length === 0) return true
    return false
}

// 确认提交
function handleConfirm() {
    showConfirm.value = false
    confirmCallback.value?.()
}

/* ----------- 返回 ----------- */
function goBack() {
    window.history.back()
}

/* ----------- 统计信息 ----------- */
const totalQuestions = computed(() =>
    questionSections.reduce((sum, sec) => sum + sec.questions.length, 0)
)
const totalScore = computed(() =>
    questionSections.reduce((sum, sec) => sum + sec.questions.reduce((s, q) => s + q.score, 0), 0)
)


</script>

<style scoped>
.submit-homework-page {
    display: flex;
    flex-direction: column;
    height: 90vh;
    background: #f5f7fa;
}

.top-bar {
    height: 56px;
    background: #2c3e50;
    color: white;
    display: flex;
    align-items: center;
    padding: 0 20px;
}

.top-bar .el-button {
    color: white;
}

.content-area {
    flex: 1;
    display: flex;
    padding: 20px;
    gap: 20px;
}

.question-section h3 {
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    font-weight: 600;
    margin-bottom: 20px;
}

.question-item {
    margin-bottom: 30px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.question-item.active {
    background: #f0f9eb;
    border-left: 4px solid #67c23a;
    padding-left: 16px;
}

.question-header {
    font-weight: 600;
    margin-bottom: 8px;
}

.question-type {
    color: #409eff;
    margin-left: 8px;
}

.score {
    margin-left: 8px;
    color: #999;
}

.bottom-btns {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 12px;
    border-top: 1px solid #eee;
}

.nav-buttons>.el-button+.el-button {
    margin-left: 8px;
}

.right-area {
    flex: 0.6;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.info-card,
.answer-card {
    background: white;
    border-radius: 6px;
    padding: 20px;
    box-shadow: 0 0 8px rgb(0 0 0 / 0.05);
}

.info-card h3 {
    margin-bottom: 12px;
}

.info-row {
    display: flex;
    gap: 16px;
}

.info-block {
    flex: 1;
    background: #f0f9eb;
    border-radius: 4px;
    padding: 10px;
    text-align: center;
}

.label {
    font-size: 14px;
    color: #666;
    margin-bottom: 6px;
}

.value {
    font-size: 28px;
    font-weight: 700;
    color: #67c23a;
}

.deadline {
    margin-top: 12px;
    font-size: 14px;
    color: #999;
}

.answer-card-title {
    border-left: 3px solid #67c23a;
    padding-left: 6px;
    font-weight: 600;
    margin-bottom: 12px;
}

.answer-section {
    margin-bottom: 12px;
}

.answer-section-title {
    font-weight: 600;
    margin-bottom: 8px;
}

.answer-btn {
    margin: 4px 6px 4px 0;
    width: 36px;
    height: 36px;
    font-weight: 600;
    user-select: none;
}

.submit-btn {
    margin-top: auto;
    width: 100%;
}

.left-area {
    flex: 1.6;
    background: white;
    border-radius: 6px;
    padding: 20px;
    overflow-y: auto;
    position: relative;
    /* 为固定底部按钮提供定位上下文 */
    padding-bottom: 70px;
    /* 给底部按钮腾出空间 */
}

/* 底部按钮固定 */
.bottom-btns {
    position: absolute;
    bottom: 10px;
    left: 20px;
    right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 12px;
    border-top: 1px solid #eee;
    background: white;
    /* 避免遮挡内容 */
}

/* 让按钮组间隔 */
.nav-buttons>.el-button+.el-button {
    margin-left: 8px;
}

/* 每个选项独占一行 */
.option-line {
    margin: 6px;
    display: block;
}

/* 垂直排列单选、多选、判断 */
.vertical-options {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.vertical-options .el-radio,
.vertical-options .el-checkbox {
    display: block;
    margin: 8px 0;
}
</style>
