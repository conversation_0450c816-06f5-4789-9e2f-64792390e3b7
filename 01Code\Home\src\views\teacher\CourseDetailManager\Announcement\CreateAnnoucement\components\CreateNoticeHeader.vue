<!-- NoticeHeader.vue -->
<template>
  <div class="notice-header">

    <div class="title-wrapper">
      <input
        type="text"
        class="title-input"
        placeholder="请在此输入标题，最多60个字符"
        v-model="localTitle"
        maxlength="60"
        :disabled="isLoading"
      />
      <span class="char-count">{{ localTitle.length }} / 60</span>
    </div>
  </div>
</template>

<script setup>
import { ref, watch,defineEmits } from 'vue'


const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  isLoading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])
const localTitle = ref(props.modelValue)

// 双向绑定同步
watch(localTitle, (val) => emit('update:modelValue', val))
watch(() => props.modelValue, (val) => {
  if (val !== localTitle.value) localTitle.value = val
})


</script>

<style scoped>
.notice-header {
  padding: 12px;
  background-color: #fff;
  border-bottom: 1px solid #eee;
}



.title-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-input {
  flex: 1;
  padding: 8px;
  font-size: 16px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.char-count {
  margin-left: 10px;
  font-size: 13px;
  color: #888;
}
</style>
