<!-- <template>
  <div class="course-resources">
    <div class="top-tabs">
      <button :class="{ active: activeTopTab === 'theme' }" @click="activeTopTab = 'theme'">
        主题模式
      </button>
      <button :class="{ active: activeTopTab === 'chapter' }" @click="activeTopTab = 'chapter'">
        章节模式
      </button>
    </div>

    <div class="content-wrapper">
      
      <LeftNav :tabs="leftTabs" v-model="activeLeftTab" />

      
      <div class="content-area">
        <ThemeModel v-if="activeTopTab === 'theme'" :currentTab="activeLeftTab" />
        <ChapterModel v-else :currentTab="activeLeftTab" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import LeftNav from './components/LeftNav.vue'
import ThemeModel from './components/ThemeModle.vue'
import ChapterModel from './components/ChapterModle.vue'

const activeTopTab = ref('theme') // 'theme' 或 'chapter'
const activeLeftTab = ref('各具千秋的茶叶分类')

const leftTabs = [
  '各具千秋的茶叶分类',
  '追根溯源识茶貌',
  '项目中的茶树之本',
  '发乎神农的中国饮茶',
  '茶为比屋之饮闻于天下'
]
</script>

<style scoped lang="scss">
// 定义SCSS变量
$border-color: #E9E9E9;
$text-color: #333;
$active-bg: $primary-color;
$active-text: #fff;

.course-resources {
  display: flex;
  flex-direction: column;
 

  .top-tabs {
    width: 19.16667vw;
    margin: 2.08333vw auto 1.25vw;
    height: 2.60417vw;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: .83333vw;
    border: 1px solid $border-color;
    background: #fff;

    button {
      display: flex;
      width: 9.375vw;
      height: 2.1875vw;
      padding: .41667vw 1.25vw;
      justify-content: center;
      align-items: center;
      background: #fff;
      color: $text-color;
      border: none;
      font-family: "Alibaba PuHuiTi 3.0";
      font-size: .9375vw;
      font-style: normal;
      font-weight: 400;
      line-height: 1.35417vw;
      cursor: pointer;

      &.active {
        border-radius: .625vw;
        background: $active-bg;
        color: $active-text;
        font-family: AlibabaPuHuiTi-Heavy;
      }
    }
  }

  .content-wrapper {
    display: flex;
    flex: 1;
    overflow: hidden;

    .content-area {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
    }
  }
}
</style> -->

<template>
  <div class="course-resources">
    <div class="content-wrapper">
      <!-- 左侧导航栏 -->
      <LeftNav :tabs="leftTabs" v-model="activeLeftTab" />

      <!-- 右侧内容区 -->
      <div class="content-area">
        <ChapterModel :currentTab="activeLeftTab" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import LeftNav from './components/LeftNav.vue'
import ChapterModel from './components/ChapterModle.vue'

const activeLeftTab = ref('各具千秋的茶叶分类')

const leftTabs = [
  '各具千秋的茶叶分类',
  '追根溯源识茶貌',
  '项目中的茶树之本',
  '发乎神农的中国饮茶',
  '茶为比屋之饮闻于天下'
]
</script>

<style scoped lang="scss">
// 定义SCSS变量
$border-color: #E9E9E9;
$text-color: #333;
$active-bg: $primary-color;
$active-text: #fff;

.course-resources {
  display: flex;
  flex-direction: column;
  height: 100%;

  .content-wrapper {
    display: flex;
    flex: 1;
    overflow: hidden;

    .content-area {
      flex: 1;
      padding: 1.042vw; // 20px → 1.042vw
      overflow-y: auto;
    }
  }
}
</style>