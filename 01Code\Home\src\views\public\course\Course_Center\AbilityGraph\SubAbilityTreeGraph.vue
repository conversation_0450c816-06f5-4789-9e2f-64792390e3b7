<template>
  <div class="sub-ability-tree-graph-container">
    <!-- 主能力导航 -->
    <div class="main-ability-nav" v-if="mainAbilities.length > 0">
      <div class="nav-dropdown">
        <label class="nav-label">选择主能力：</label>
        <select v-model="selectedMainAbilityId" @change="onMainAbilityChange" class="nav-select">
          <option value="">请选择主能力</option>
          <option 
            v-for="ability in mainAbilities" 
            :key="ability.abilityId" 
            :value="ability.abilityId"
          >
            {{ ability.nodeName }}
          </option>
        </select>
      </div>
      
      <!-- 子能力列表 -->
      <div class="sub-abilities-list" v-if="subAbilities.length > 0">
        <h4 class="sub-title">子能力列表</h4>
        <div class="sub-abilities-grid">
          <div 
            v-for="subAbility in subAbilities" 
            :key="subAbility.abilityId"
            class="sub-ability-item"
            @click="onSubAbilityClick(subAbility)"
          >
            <div class="sub-ability-name">{{ subAbility.nodeName }}</div>
            <div class="sub-ability-desc" v-if="subAbility.nodeDesc">
              {{ subAbility.nodeDesc }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 树形图 -->
    <div ref="graphContainer" class="sub-ability-tree-graph"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick, watch, defineProps, defineEmits } from 'vue'
import * as G6 from '@antv/g6'
import { getMainAbilities, getSubAbilitiesLite, getRelatedKnowledgeTree } from '@/api/public/course/courseMap/abilityGraph'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({
      id: 'root',
      label: '子能力知识点',
      tags: [],
      children: []
    })
  },
  graphId: {
    type: String,
    default: ''
  },
  abilityId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['subAbilityClick'])

// 主能力相关数据
const mainAbilities = ref([])
const subAbilities = ref([])
const selectedMainAbilityId = ref('')
const loading = ref(false)
const error = ref('')

// 知识点树形数据
const knowledgeTreeData = ref(null)

const graphContainer = ref(null)
let graph = null

const staticData = {
  id: 'root',
  label: '子能力知识点',
  tags: ['核心'],
  children: [
    {
      id: 'k1',
      label: '知识点A',
      tags: ['基础'],
      children: [
        { id: 'k1-1', label: '知识点A-1', tags: ['扩展'] },
        { id: 'k1-2', label: '知识点A-2', tags: [] }
      ]
    },
    {
      id: 'k2',
      label: '知识点B',
      tags: ['进阶'],
      children: [
        { id: 'k2-1', label: '知识点B-1', tags: [] }
      ]
    }
  ]
}

// 分支颜色配置（参考TreeGraph.vue）
const branchColorsArr = [
  '#ff4d4f', // 红
  '#3b50f9', // 靛蓝
  '#feca57', // 黄
  '#f93b7c', // 粉
]

// 递归为每个节点分配 branchKey，实现一级节点分配彩色
function assignBranchKey(node, branchKey = null, level = 0, branchIdx = 0) {
  node.level = level
  if (level === 0) {
    node.branchKey = null // 只有根节点深色
  } else if (level === 1) {
    node.branchKey = branchIdx // 一级节点分配彩色
  } else {
    node.branchKey = branchKey // 子孙节点继承一级节点色
  }
  if (node.children) {
    node.children.forEach((child, idx) => assignBranchKey(child, node.branchKey, level + 1, idx))
  }
}

// 获取知识点树形数据
const fetchKnowledgeTree = async (abilityId) => {
  if (!abilityId) {
    console.warn('没有能力ID，跳过获取知识点树形数据')
    return
  }

  try {
    console.log('获取知识点树形数据，abilityId:', abilityId)
    const response = await getRelatedKnowledgeTree(abilityId)
    if (response.code === 200) {
      knowledgeTreeData.value = response.result || response.data
      console.log('获取到知识点树形数据:', knowledgeTreeData.value)
      
      // 更新图表数据
      if (graph) {
        const processedData = processTreeData(knowledgeTreeData.value)
        graph.changeData(processedData)
        graph.fitCenter()
        graph.zoom(0.9)
      }
    } else {
      console.error('获取知识点树形数据失败:', response.msg || response.message)
    }
  } catch (error) {
    console.error('获取知识点树形数据失败:', error)
    // 使用模拟数据
    knowledgeTreeData.value = staticData
  }
}

// 处理树形数据格式
const processTreeData = (data) => {
  if (!data || !Array.isArray(data)) {
    return staticData
  }
  
  // 将后端数据转换为G6需要的格式
  const convertNode = (node) => {
    const tagNames = node.tags && Array.isArray(node.tags) 
      ? node.tags.map(tag => tag.tagName).filter(tagName => tagName)
      : []
    return {
      id: node.nodeId || node.id,
      label: node.nodeName || node.label,
      tags: tagNames, // 使用提取的tagName数组
      children: node.children ? node.children.map(convertNode) : []
    }
  }
  
  let treeData
  if (data.length === 1) {
    treeData = convertNode(data[0])
  } else {
    treeData = {
      id: 'virtual-root',
      label: '知识点',
      tags: [],
      children: data.map(convertNode)
    }
  }
  // 分配分支色
  assignBranchKey(treeData)
  return treeData
}

// 获取主能力列表
const fetchMainAbilities = async () => {
  if (!props.graphId) {
    console.warn('没有图谱ID，跳过获取主能力列表')
    return
  }

  loading.value = true
  error.value = ''

  try {
    const response = await getMainAbilities(props.graphId)
    if (response.code === 200) {
      mainAbilities.value = response.result || response.data
      console.log('获取到主能力列表:', mainAbilities.value)
    } else {
      error.value = response.msg || response.message || '获取主能力列表失败'
    }
  } catch (err) {
    console.error('获取主能力列表失败:', err)
    error.value = '获取主能力列表失败'
  } finally {
    loading.value = false
  }
}

// 获取子能力列表
const fetchSubAbilities = async (parentId) => {
  if (!parentId) {
    subAbilities.value = []
    return
  }

  loading.value = true
  error.value = ''

  try {
    const response = await getSubAbilitiesLite(parentId)
    if (response.code === 200) {
      subAbilities.value = response.result || response.data
      console.log('获取到子能力列表:', subAbilities.value)
    } else {
      error.value = response.msg || response.message || '获取子能力列表失败'
    }
  } catch (err) {
    console.error('获取子能力列表失败:', err)
    error.value = '获取子能力列表失败'
  } finally {
    loading.value = false
  }
}

// 主能力选择变化处理
const onMainAbilityChange = async () => {
  if (selectedMainAbilityId.value) {
    console.log('选择主能力:', selectedMainAbilityId.value)
    await fetchSubAbilities(selectedMainAbilityId.value)
  } else {
    subAbilities.value = []
  }
}

// 子能力点击处理
const onSubAbilityClick = (subAbility) => {
  console.log('点击子能力:', subAbility)
  emit('subAbilityClick', subAbility)
}

onMounted(async () => {
  await nextTick()
  initGraph()
  window.addEventListener('resize', handleResize)
  
  // 获取主能力列表
  await fetchMainAbilities()
})

onBeforeUnmount(() => {
  if (graph) graph.destroy()
  window.removeEventListener('resize', handleResize)
})

watch(() => props.data, (newData) => {
  if (newData && graph) {
    const processedData = JSON.parse(JSON.stringify(newData))
    graph.changeData(processedData)
    graph.fitCenter()
    graph.zoom(0.9)
  }
}, { deep: true })

// 监听abilityId变化，获取对应的知识点树形数据
watch(() => props.abilityId, (newAbilityId) => {
  if (newAbilityId) {
    console.log('abilityId变化，获取知识点树形数据:', newAbilityId)
    fetchKnowledgeTree(newAbilityId)
  }
}, { immediate: true })

const handleResize = () => {
  if (graph && graphContainer.value) {
    graph.changeSize(
      graphContainer.value.clientWidth,
      graphContainer.value.clientHeight
    )
    graph.fitCenter()
  }
}

const initGraph = () => {
  if (!graphContainer.value) return
  if (graph) graph.destroy()

  // 注册自定义节点，支持分支配色
  G6.registerNode('sub-custom-node', {
    draw(cfg, group) {
      // 分支色逻辑
      const isRoot = (cfg.level === 0 || cfg.branchKey === null)
      const color = isRoot
        ? '#24285e'
        : branchColorsArr[cfg.branchKey % branchColorsArr.length] || '#8a6de3'
      const fontSize = 15
      const paddingX = 18
      const ctx = document.createElement('canvas').getContext('2d')
      ctx.font = `${fontSize}px sans-serif`
      const labelWidth = ctx.measureText(cfg.label).width
      const tags = cfg.tags || []
      let tagsWidth = 0
      const tagPadding = 14
      tags.forEach(tag => {
        tagsWidth += ctx.measureText(tag).width + tagPadding + 8
      })
      tagsWidth = Math.max(tagsWidth - 8, 0)
      const contentWidth = Math.max(labelWidth, tagsWidth)
      const nodeWidth = contentWidth + paddingX * 2
      const nodeHeight = 44
      const rect = group.addShape('rect', {
        attrs: {
          x: 0,
          y: 0,
          width: nodeWidth,
          height: nodeHeight,
          fill: color,
          radius: 14,
          shadowColor: '#ccc',
          shadowBlur: 4,
          cursor: 'pointer'
        }
      })
      group.addShape('text', {
        attrs: {
          x: nodeWidth / 2,
          y: nodeHeight / 2,
          text: cfg.label,
          fill: '#fff',
          fontSize,
          fontWeight: 'bold',
          textAlign: 'center',
          textBaseline: 'middle'
        }
      })
      if (tags.length) {
        let totalWidth = tagsWidth
        let startX = (nodeWidth - totalWidth) / 2
        const startY = nodeHeight + 8
        tags.forEach(tag => {
          const tagWidth = ctx.measureText(tag).width + tagPadding
          group.addShape('rect', {
            attrs: {
              x: startX,
              y: startY,
              width: tagWidth,
              height: 20,
              fill: '#fff',
              radius: 10
            }
          })
          group.addShape('text', {
            attrs: {
              x: startX + tagWidth / 2,
              y: startY + 10,
              text: tag,
              fill: '#666',
              fontSize: 11,
              textAlign: 'center',
              textBaseline: 'middle'
            }
          })
          startX += tagWidth + 8
        })
      }
      if (cfg.children && cfg.children.length > 0) {
        group.addShape('marker', {
          attrs: {
            x: nodeWidth + 8,
            y: nodeHeight / 2,
            r: 5,
            symbol: cfg.collapsed ? G6.Marker.expand : G6.Marker.collapse,
            fill: '#fff',
            lineWidth: 1.5,
            stroke: color
          }
        })
      }
      return rect
    }
  }, 'rect')

  // 注册自定义边，边颜色与source节点一致，紧凑L型折线
  G6.registerEdge('sub-custom-edge', {
    draw(cfg, group) {
      const { startPoint, endPoint } = cfg
      // 紧凑L型折线：水平延伸offset，再垂直到终点y，再水平到终点x
      const offset = 40
      const midX = startPoint.x + offset
      const path = [
        ['M', startPoint.x, startPoint.y],
        ['L', midX, startPoint.y],
        ['L', midX, endPoint.y],
        ['L', endPoint.x, endPoint.y]
      ]
      // 只用更深的灰色，不再判断sourceNode
      const color = '#8c8c8c'
      return group.addShape('path', {
        attrs: {
          path,
          stroke: color,
          lineWidth: 2,
          lineJoin: 'round',
          lineCap: 'round'
        }
      })
    }
  })

  graph = new G6.TreeGraph({
    container: graphContainer.value,
    width: graphContainer.value.clientWidth,
    height: graphContainer.value.clientHeight,
    modes: { default: ['drag-canvas', 'zoom-canvas', 'collapse-expand'] },
    defaultNode: {
      type: 'sub-custom-node',
      anchorPoints: [[0, 0.5], [1, 0.5]]
    },
    defaultEdge: {
      type: 'sub-custom-edge',
      style: { lineWidth: 2 }
    },
    layout: {
      type: 'compactBox',
      direction: 'LR',
      getId: d => d.id,
      getHeight: () => 60,
      getWidth: () => 120,
      getVGap: () => 24,
      getHGap: () => 40
    }
  })

  // 渲染时确保分支色分配
  let treeData = props.data && props.data.children && props.data.children.length ? props.data : staticData
  assignBranchKey(treeData)
  graph.data(treeData)
  graph.render()
  graph.fitCenter()
  graph.zoom(0.9)
}
</script>

<style scoped>
.sub-ability-tree-graph {
  width: 100%;
  height: 400px;
  background: #f0f2f5;
  border-radius: 8px;
}
</style> 