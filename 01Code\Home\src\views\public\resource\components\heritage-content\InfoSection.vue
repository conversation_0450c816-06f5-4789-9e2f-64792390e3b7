<template>
  <div class="info-section">
    <h2 class="info-title">简介</h2>
    <div class="info-content">
      <div class="introduction-text">
        <p>{{ heritageData.introduction }}</p>
      </div>
      
      <div class="detail-info">
        <div class="info-item" v-for="item in heritageData.details" :key="item.label">
          <span class="info-label">{{ item.label }}：</span>
          <span class="info-value">{{ item.value }}</span>
        </div>
      </div>
      
      <div class="heritage-features">
        <h3>文化特色</h3>
        <ul class="features-list">
          <li v-for="feature in heritageData.features" :key="feature">{{ feature }}</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const props = defineProps({
  heritageData: {
    type: Object,
    required: true
  }
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.info-section {
  padding: 40px 20px;
  
  .info-title {
    font-size: 24px;
    font-weight: 600;
    color: $purple-primary;
    text-align: center;
    margin: 0 0 32px 0;
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      bottom: -8px;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 3px;
      background: $purple-primary;
      border-radius: 2px;
    }
  }
  
  .info-content {
    max-width: 1000px;
    margin: 0 auto;
    border: 2px solid $purple-primary;
    border-radius: 12px;
    background: white;
    padding: 32px;
    box-shadow: 0 4px 20px rgba($purple-primary, 0.1);
    
    .introduction-text {
      margin-bottom: 32px;
      
      p {
        font-size: 16px;
        line-height: 1.8;
        color: $text-color;
        margin: 0;
        text-align: justify;
        text-indent: 2em;
      }
    }
    
    .detail-info {
      margin-bottom: 32px;
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 16px;
      
      .info-item {
        display: flex;
        padding: 12px;
        background: rgba($purple-primary, 0.05);
        border-radius: 6px;
        
        .info-label {
          font-weight: 600;
          color: $purple-primary;
          min-width: 100px;
        }
        
        .info-value {
          color: $text-color;
          flex: 1;
        }
      }
    }
    
    .heritage-features {
      h3 {
        font-size: 18px;
        font-weight: 600;
        color: $purple-primary;
        margin: 0 0 16px 0;
      }
      
      .features-list {
        list-style: none;
        padding: 0;
        margin: 0;
        
        li {
          font-size: 14px;
          line-height: 1.6;
          color: $text-color;
          margin-bottom: 12px;
          padding-left: 24px;
          position: relative;
          
          &::before {
            content: '•';
            position: absolute;
            left: 0;
            color: $purple-primary;
            font-weight: bold;
            font-size: 16px;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .info-section {
    padding: 24px 16px;
    
    .info-content {
      padding: 20px;
      
      .detail-info {
        grid-template-columns: 1fr;
      }
    }
  }
}
</style>