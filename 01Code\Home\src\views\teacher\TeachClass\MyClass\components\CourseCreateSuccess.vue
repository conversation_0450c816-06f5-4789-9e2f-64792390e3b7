<template>
  <div class="success-popup" v-if="isOpen">
    <div class="popup-content">
      <!--       <div class="checkmark">
        <svg viewBox="0 0 52 52">
          <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none"/>
          <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8"/>
        </svg>
      </div> -->
      <p class="success-message">恭喜您，课程创建成功！</p>
      <p class="sub-message">已为您创建好课程班群，快通过课程号邀请学生加入吧！</p>
      <div class="course-info">
        <p class="course-number">课程号：<span>{{ courseNumber }}</span></p>
        <p>
          邀请码：<span class="verification-code">{{ invitationCode }}</span>
          <button @click="copyCode">复制邀请码</button>
        </p>
      </div>
      <button @click="closePopup" class="ok-button">我知道了</button>
    </div>
  </div>
</template>

<script setup>
import { ref ,computed,defineProps,defineExpose} from 'vue';
import { useClipboard } from '@vueuse/core';
import { useCourseStore } from '@/stores/courseStore'
import { storeToRefs } from 'pinia'
import { useRouter } from 'vue-router'


const courseStore = useCourseStore()
const router = useRouter(); 
const isOpen = ref(false);
const { currentCourseId } = storeToRefs(courseStore) 

const props = defineProps({
  invitationCode: {
    type: String,
    default: ''
  },
  courseNumber:{
    type:String,
    default:''
  }
});

const { copy } = useClipboard();// 复制功能

const copyCode = () => {
  copy(props.invitationCode);// 复制邀请码

};

const closePopup = () => {// 关闭弹窗
  isOpen.value = false;
};

const openPopup = () => {// 打开弹窗
  isOpen.value = true;
};
const handleOk = () => {
  closePopup(); // 关闭弹窗
  /* router.push('/course-detail-manager');  */
    router.push({ 
    name: 'CourseDetailManager',
    params: { courseId: currentCourseId.value }
  });
};

defineExpose({
  openPopup,
  closePopup
});
</script>

<style lang="scss" scoped>
$border-radius: 3px;

.success-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.popup-content {
  background-color: white;
  padding: 30px;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  width: 600px;
  height: 300px;

  /*   .checkmark {
    margin-bottom: 20px;
  } */

  .success-message {
    font-size: 20px;
    margin-bottom: 10px;

  }

  .sub-message {
    color: #666;
    margin-bottom: 20px;
  }

  .course-info {
    text-align: center;
    padding: 10px;
    border-radius: 4px;

    .course-number {
      font-size: 30px;
      margin-bottom: 10px;
    }

    span {
      font-weight: bold;
    }

    .verification-code {
      vertical-align: middle;
      padding: 3px 20px;
      border-radius: $border-radius;
      background-color: #efecec;
      color: #54d2d6;
    }

    button {
      vertical-align: middle;
      background: $primary-color;
      padding: 8px;
      border: none;
      border-radius: $border-radius;
      font-size: 8px;
      color: #ffffff;
      cursor: pointer;
      margin-left: 10px;
    }
  }

  .ok-button {
    background-color: #007BFF;
    color: white;
    border: none;
    font-size: 12px;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;

    &:hover {
      background-color: #0056b3;
    }
  }
}
</style>