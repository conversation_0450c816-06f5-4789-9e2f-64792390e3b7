import OpenAI from 'openai';
import * as mammoth from 'mammoth';
import { getKey } from '../../key.js';

// 处理文件并生成新题目
export const processDocumentAndGenerateQuestions = async (file, counts) => {
  try {
    // 1. 读取文件为 ArrayBuffer
    const arrayBuffer = await readFileAsArrayBuffer(file);
    // 2. 将 DOCX 转换为 HTML
    const { value: html } = await mammoth.convertToHtml({ arrayBuffer });
    // 3. 从 HTML 中提取纯文本
    const textContent = extractTextFromHTML(html);
    // 4. 创建系统提示
    const systemPrompt = createSystemPrompt(counts);
    // 5. 调用 API 生成新题目
    const newQuestions = await callDeepSeekAPI(textContent, systemPrompt);
    return newQuestions;
  } catch (error) {
    console.error('文档处理失败:', error);
    throw new Error(`处理文档时出错: ${error.message}`);
  }
};

// 创建系统提示 - 使用对象参数确保属性存在
const createSystemPrompt = (counts) => {
  // 确保有默认值
  const mcqCount = counts.mcqCount || 1;
  const fillCount = counts.fillCount || 1;
  const essayCount = counts.essayCount || 1;

  return `你是一个专业的教育助手，请完成以下任务：

1. 从提供的文档中提取所有知识点，并列出
2. 根据提取的知识点，再度进行扩展相似的知识点，生成新的习题，题目不要和原题一样，也不能和原题只变一个是或否
3. 新习题包括
- 选择题 ${mcqCount} 道
- 填空题 ${fillCount} 道
- 简答题 ${essayCount} 道
4. 新习题应紧密围绕提取的知识点，不能偏离
5. 不要在题目中包含答案，答案统一放在最后，答案不要采用文档里面的，要AI思考

输出格式：
知识点列表：
- 知识点1
- 知识点2
...

新习题：
【选择题】
1. 题目内容1
2. 题目内容2
...

【填空题】
1. 题目内容1
2. 题目内容2
...

【简答题】
1. 题目内容1
2. 题目内容2
...

答案：
【选择题答案】
1. A
2. B
...
【填空题答案】
1. 答案1
2. 答案2
...
【简答题答案】
1. 详细答案1
2. 详细答案2
...`;
};

// 调用 DeepSeek API
const callDeepSeekAPI = async (textContent, systemPrompt) => {
  const deepseekApiKey = getKey('deepseek');
  const openai = new OpenAI({
    baseURL: 'https://api.deepseek.com',
    apiKey: deepseekApiKey,
    dangerouslyAllowBrowser: true
  });

  const response = await openai.chat.completions.create({
    model: 'deepseek-chat',
    messages: [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: textContent }
    ],
    max_tokens: 2000,
    temperature: 0.5
  });

  if (!response.choices?.[0]?.message?.content) {
    throw new Error('API 返回了无效的响应格式');
  }

  return response.choices[0].message.content;
};

// 从 HTML 中提取纯文本
const extractTextFromHTML = (html) => {
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, 'text/html');
  return doc.body.textContent || '';
};

// 将文件读取为 ArrayBuffer
const readFileAsArrayBuffer = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (event) => resolve(event.target.result);
    reader.onerror = (error) => reject(error);
    reader.readAsArrayBuffer(file);
  });
};
