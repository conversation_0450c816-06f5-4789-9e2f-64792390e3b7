//src\views\teacher\Dashboard.vue
.teacher-dashboard {
  padding: 20px;

  .teacher-actions {
    display: flex;
    gap: 10px;
    margin: 20px 0;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;

    .stat-card {
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 15px;
      text-align: center;

      h3 {
        margin: 0 0 10px 0;
        font-size: 1rem;
      }

      p {
        font-size: 1.5rem;
        font-weight: bold;
        margin: 0;
      }
    }
  }
}