<template>
    <aside class="side-toolbar">
      <button title="全屏" @click="handleFullscreen">
        <img :src="fullscreenIcon" alt="全屏" class="icon" />
      </button>
      <button title="展开/收起节点" @click="toggleExpandAll">
        <img :src="toggleExpandIcon" alt="展开收起" class="icon" />
      </button>
      <button title="定位到根节点" @click="centerRoot">
        <img :src="centerIcon" alt="居中" class="icon" />
      </button>
      <button title="放大" @click="zoomIn">
        <img :src="zoomInIcon" alt="放大" class="icon" />
      </button>
      <button title="缩小" @click="zoomOut">
        <img :src="zoomOutIcon" alt="缩小" class="icon" />
      </button>
    </aside>
  </template>
  
  <script setup>
  import { ref, inject, onMounted, onUnmounted } from 'vue'
  import { useKnowledgeGraphStore } from '@/stores/teacher/graphManager/knowledgeGraphStore'
  
  import fullscreenIcon from '@/assets/courseMap-icon/createkgGraph/sideToolbar/fullscreen.svg'
  import toggleExpandIcon from '@/assets/courseMap-icon/createkgGraph/sideToolbar/toggle-expand.svg'
  import centerIcon from '@/assets/courseMap-icon/createkgGraph/sideToolbar/center.svg'
  import zoomInIcon from '@/assets/courseMap-icon/createkgGraph/sideToolbar/zoom-in.svg'
  import zoomOutIcon from '@/assets/courseMap-icon/createkgGraph/sideToolbar/zoom-out.svg'
  
  const graphRef = inject('graphRef')
  const graphData = inject('graphData')
  const zoomLevel = ref(1)
  
  const store = useKnowledgeGraphStore()
  
  // 全屏功能
  const handleFullscreen = () => {
    const elem = document.querySelector('.knowledge-graph-page')
    if (!document.fullscreenElement) {
      elem.requestFullscreen().catch(err => {
        console.error(`全屏错误: ${err.message}`)
      })
    } else {
      document.exitFullscreen()
    }
  }
  
  // 展开/收起所有节点
  const toggleExpandAll = () => {
    if (!graphRef.value || !graphData.value) return
    
    const toggleCollapsed = (node) => {
      if (node.children && node.children.length > 0) {
        node.collapsed = !node.collapsed
        node.children.forEach(toggleCollapsed)
      }
    }
    
    toggleCollapsed(graphData.value)
    
    const matrix = graphRef.value.getGroup().getMatrix()
    graphRef.value.changeData(graphData.value)
    if (matrix) graphRef.value.getGroup().setMatrix(matrix)
  }
  
  // 定位到根节点
  const centerRoot = () => {
    if (!graphRef.value) return
    
    // 使用与KnowledgeDetailDrawer.vue相同的根节点判断逻辑
    const rootNodeId = store.rootNodeId || 'root'
    
    graphRef.value.focusItem(rootNodeId, true, {
      duration: 300,
      easing: 'easeCubic'
    })
  }
  
  // 放大功能
  const zoomIn = () => {
    if (!graphRef.value) return
    
    const currentZoom = graphRef.value.getZoom()
    if (currentZoom > 5) { // 最大放大5倍
      return
    }
  
    // 获取当前视口中心点
    const width = graphRef.value.getWidth()
    const height = graphRef.value.getHeight()
    const center = {
      x: width / 2,
      y: height / 2
    }
    
    // 基于当前视口中心点放大
    graphRef.value.zoom(1.2, center, {
      duration: 300
    })
  }
  
  // 缩小功能
  const zoomOut = () => {
    if (!graphRef.value) return
    
    const currentZoom = graphRef.value.getZoom()
    if (currentZoom < 0.2) { // 最小缩小到20%
      return
    }
  
    // 获取当前视口中心点
    const width = graphRef.value.getWidth()
    const height = graphRef.value.getHeight()
    const center = {
      x: width / 2,
      y: height / 2
    }
    
    // 基于当前视口中心点缩小
    graphRef.value.zoom(0.8, center, {
      duration: 300
    })
  }
  
  const updateZoomLevel = () => {
    if (!graphRef.value) return
    
    // 获取当前图表的缩放比例
    const currentZoom = graphRef.value.getZoom()
    
    // 更新本地状态
    zoomLevel.value = currentZoom
  }
  
  // 监听缩放事件
  onMounted(() => {
    if (graphRef.value) {
      graphRef.value.on('wheelzoom', updateZoomLevel)
    }
  })
  
  onUnmounted(() => {
    if (graphRef.value) {
      graphRef.value.off('wheelzoom', updateZoomLevel)
    }
  })
  </script>
  
  <style lang="scss" scoped>
  .side-toolbar {
    position: absolute;
    top: 100px;
    left: 40px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 10;
  
    button {
      width: 36px;
      height: 36px;
      border: 1px solid #ccc;
      background-color: #fff;
      border-radius: 6px;
      cursor: pointer;
      padding: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s;
  
      &:hover {
        background-color: #f0f0f0;
        transform: scale(1.05);
      }
  
      &:active {
        transform: scale(0.95);
      }
    }
  
    .icon {
      width: 20px;
      height: 20px;
    }
  }
  </style>
  