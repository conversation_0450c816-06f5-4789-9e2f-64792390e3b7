<template>
  <el-dialog
    v-model="dialogVisible"
    title="平时权重高级设置"
    width="600px"
    :before-close="handleBeforeClose"
    :close-on-click-modal="false"
  >
    <div class="dialog-content-wrapper">
      <div class="daily-weight-settings-container">
        <div class="description">
          平时权重{{ totalWeight }}分 = 学习进度 ({{ learningProgressFullScore }}分) + 课堂互动 ({{ classInteractionFullScore }}分)
        </div>

        <el-divider />

        <div class="section">
          <div class="section-header">
            <div class="indicator"></div>
            <h3>学习进度 <span class="sub-label">(仅算必学)</span></h3>
            <span class="formula">学习进度分=学习进度*学习进度总分</span>
          </div>
          <div class="form-item">
            <label>满分</label>
            <el-input-number
              v-model="learningProgressFullScore"
              :min="0"
              :max="maxLearningScore"
              controls-position="right"
              @change="handleLearningProgressChange"
            />
            <span>分</span>
          </div>
          <div class="form-item">
            <label>高于</label>
            <el-input-number
              v-model="learningProgressStartScore"
              :min="0"
              :max="100"
              controls-position="right"
            />
            <span>% 开始得分 (学习进度0分)</span>
          </div>
        </div>

        <div class="section">
          <div class="section-header">
            <div class="indicator"></div>
            <h3>课堂互动</h3>
            <span class="formula">课堂互动分=课堂表现分+课堂参与分</span>
          </div>
          <div class="form-item">
            <label>满分</label>
            <el-input-number
              v-model="classInteractionFullScore"
              :min="0"
              :max="maxInteractionScore"
              controls-position="right"
              @change="handleInteractionChange"
            />
            <span>分</span>
          </div>
        </div>

        <div class="section">
          <div class="section-header">
            <div class="indicator"></div>
            <h3>课堂表现分</h3>
          </div>
          <div class="info-text">
            教师对学生在【投票、点名、抢答、打分】等互动中的表现评分（0分/1分/2分）
          </div>
        </div>

        <div class="section">
          <div class="section-header">
            <div class="indicator"></div>
            <h3>课堂参与分 <span class="sub-label">(学生参与互动得分)</span></h3>
          </div>
        </div>

        <div class="footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleConfirm">确定</el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

const props = defineProps({
  totalWeight: {
    type: Number,
    default: 30,
    required: true
  },
  initialData: {
    type: Object,
    default: () => ({
      learningProgressFullScore: 15,
      learningProgressStartScore: 0,
      classInteractionFullScore: 15
    }),
    required: true
  }
});

const emit = defineEmits(['confirm', 'cancel']);

// 弹窗显示状态
const dialogVisible = ref(false);

// 表单数据
const learningProgressFullScore = ref(props.initialData.learningProgressFullScore);
const learningProgressStartScore = ref(props.initialData.learningProgressStartScore);
const classInteractionFullScore = ref(props.initialData.classInteractionFullScore);

// 监听初始数据变化
watch(() => props.initialData, (newVal) => {
  learningProgressFullScore.value = newVal.learningProgressFullScore;
  learningProgressStartScore.value = newVal.learningProgressStartScore;
  classInteractionFullScore.value = newVal.classInteractionFullScore;
}, { deep: true });

// 计算属性
const maxLearningScore = computed(() => props.totalWeight);
const maxInteractionScore = computed(() => props.totalWeight);

// 处理学习进度分数变化
const handleLearningProgressChange = (value) => {
  // 确保学习进度和互动分数总和不超过平时权重
  if (value + classInteractionFullScore.value > props.totalWeight) {
    ElMessage.warning(`学习进度和课堂互动分数总和不能超过${props.totalWeight}分`);
    learningProgressFullScore.value = props.totalWeight - classInteractionFullScore.value;
  }
};

// 处理互动分数变化
const handleInteractionChange = (value) => {
  // 确保学习进度和互动分数总和不超过平时权重
  if (value + learningProgressFullScore.value > props.totalWeight) {
    ElMessage.warning(`学习进度和课堂互动分数总和不能超过${props.totalWeight}分`);
    classInteractionFullScore.value = props.totalWeight - learningProgressFullScore.value;
  }
};

// 数据验证
const validateScores = () => {
  return learningProgressFullScore.value + classInteractionFullScore.value <= props.totalWeight;
};

// 暴露给父组件的方法
const open = () => {
  dialogVisible.value = true;
};

const close = () => {
  dialogVisible.value = false;
};

const handleConfirm = () => {
  if (!validateScores()) {
    ElMessage.warning(`学习进度和课堂互动分数总和不能超过${props.totalWeight}分！`);
    return;
  }

  emit('confirm', {
    learningProgressFullScore: learningProgressFullScore.value,
    learningProgressStartScore: learningProgressStartScore.value,
    classInteractionFullScore: classInteractionFullScore.value
  });
  close();
};

const handleCancel = () => {
  // 恢复原始数据
  learningProgressFullScore.value = props.initialData.learningProgressFullScore;
  learningProgressStartScore.value = props.initialData.learningProgressStartScore;
  classInteractionFullScore.value = props.initialData.classInteractionFullScore;
  emit('cancel');
  close();
};

const handleBeforeClose = (done) => {
  ElMessageBox.confirm('确定要关闭吗？未保存的修改将会丢失', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    handleCancel();
    done();
  }).catch(() => {});
};

defineExpose({ open, close });
</script>

<style lang="scss" scoped>
.dialog-content-wrapper {
  padding: 10px;

  .daily-weight-settings-container {
    padding: 0 10px;

    .description {
      font-size: 14px;
      color: #666;
      margin-bottom: 15px;
      font-weight: bold;
    }

    .section {
      margin-bottom: 20px;

      .section-header {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        .indicator {
          width: 4px;
          height: 16px;
          background-color: #409eff;
          border-radius: 2px;
          margin-right: 8px;
        }

        h3 {
          font-size: 15px;
          margin: 0;
          font-weight: bold;
        }

        .sub-label {
          font-size: 12px;
          color: #999;
          margin-left: 6px;
        }

        .formula {
          font-size: 12px;
          color: #999;
          margin-left: 20px;
        }
      }

      .form-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        padding-left: 12px;

        label {
          min-width: 40px;
          margin-right: 8px;
          font-size: 14px;
        }

        span {
          font-size: 14px;
          margin-left: 5px;
        }
      }

      .info-text {
        font-size: 14px;
        color: #666;
        line-height: 1.5;
        padding-left: 16px;
      }
    }

    .footer {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
      padding-top: 15px;
      border-top: 1px solid #ebeef5;
      gap: 10px;
    }
  }
}

/* 调整弹窗内部样式 */
:deep(.el-dialog__body) {
  padding: 20px !important;
}
</style>