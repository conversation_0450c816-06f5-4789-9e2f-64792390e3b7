<!--src\views\public\threeDirection\fullStack\index.vue-->
<template>
  <div class="fullStack">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>全栈开发</h1>
      <div class="page-header-desc">
        <div class="data"></div>
        <div class="part">
            <div>
                <h1>{{allNum }}</h1>
                <p>门</p>
            </div>
            <p>全部课程</p>
        </div>
        <div class="part">
            <div>
                <h1>{{smartNum }}</h1>
                <p>门</p>
            </div>
            <p>已建设知识图谱</p>
        </div>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="course-container">
      <!-- 左侧导航标签 -->
      <div class="course-nav">
        <h2>课程类型</h2>
        <button
          v-for="tab in tabs"
          :key="tab.title"
          @click="switchTab(tab)"
          :class="{ active: activeTab.title === tab.title }"
        >
          {{ tab.title }}
        </button>
      </div>

      <!-- 右侧内容区 -->
      <div class="course-content">
        <CourseDisplay 
          :course-type="activeTab.title" 
          :all-courses="coursesData"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import CourseDisplay from '@/views/public/course/components/CourseDisplay.vue';

// 静态导入所有图片资源（绝对路径）
import cover1 from '/src/assets/img/Course/courseCover/cover1.png';
import cover2 from '/src/assets/img/Course/courseCover/cover2.png';

const allNum = ref(53); 
const smartNum = ref(28); 
const coursesData = ref([]);

const tabs = [
  { title: '全部' },
  { title: '学科基础课程' },
  { title: '专业教育课程' },
  { title: '实践教育课程' },
  { title: '多元化教育课程' }
];
const activeTab = ref(tabs[0]);

function switchTab(tab) {
  activeTab.value = tab;
}

onMounted(() => {
  coursesData.value = [
    {
      id: 1,
      title: '计算机科学导论',
      type: '学科基础课程',
      semester: '1',
      cover: cover1,
      properties: ['必修', '计算机类'],
      hasKnowledgeGraph: true
    },
    {
      id: 2,
      title: 'Web前端开发实战',
      type: '实践教育课程', 
      semester: '2',
      cover: cover2,
      properties: ['选修', '前端开发', '项目实践'],
      hasKnowledgeGraph: true
    },
    {
      id: 3,
      title: '数据结构与算法',
      type: '学科基础课程',
      semester: '1',
      cover: cover1,
      properties: ['必修', '计算机类'],
      hasKnowledgeGraph: false
    },
    {
      id: 4,
      title: '人工智能基础',
      type: '专业教育课程', 
      semester: '3',
      cover: cover2,
      properties: ['选修', '人工智能', '理论'],
      hasKnowledgeGraph: true
    }
  ];
  
  // 计算知识图谱课程数量
  smartNum.value = coursesData.value.filter(course => course.hasKnowledgeGraph).length;
});

// 监听课程数据变化（可用于后续调试）
watch(coursesData, (newVal) => {
  if (newVal.length > 0) {
    console.log('课程数据已更新，第一个课程cover路径:', newVal[0].cover);
  }
});
</script>

<style lang="scss" scoped>
/* 页面整体样式 */
.fullStack {
  position: relative;
  padding-top: 10vw;
  padding-left: 3vw;
  padding-right: 3vw;
  
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('@/assets/img/Home/ic_home_bg.png') top/cover no-repeat;
    z-index: -1;
  }
  
  .page-header {
    color: white;
    margin-bottom: 2vw;
    display: flex;
    justify-content: space-between; 
    
    h1 { 
      font-size: 3rem; 
      font-weight: 400;
      margin: 0; 
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); 
    }
    
    .page-header-desc {
      display: flex;
      margin-top: 5vw;
      gap: 2vw;
      
      .part {
        display: flex;
        flex-direction: column;
        align-items: end;
        gap: 1vw;
        
        div {
          display: flex;
          align-items: flex-end;
          justify-content: center;
          h1 {
            margin: 0; 
            line-height: 1;
            font-size: 3rem; 
          }
          
          p {
            margin: 0 0 0 0.2em;
            line-height: 1; 
            align-self: flex-end; 
            font-size: 1rem; 
          }
        }
      }
    }
  }
}

/* 课程容器样式 */
.course-container {
  display: flex;
  gap: 1.5vw;
  min-height: 70vh;
  background-color: white;
  border-radius: 1vw 1vw 0 0;
  overflow: hidden;
  padding: 2vw 1vw;
  margin-top: 10vw;
  
  .course-nav {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.8vw;
    
    h2 {
      font-size: 1.2rem;
      font-weight: 700;
    }
    
    button {
      padding: 0.8vw 1.8vw;
      border: $course-tabs-solid solid 1px;
      background-color:white;
      cursor: pointer;
      border-radius: 2vw;
      font-size: 1vw;
      transition: all 0.3s ease;
      text-align: left;
      
      &:hover {
        background-color: $course-tabs;
        color: $primary-color;
        border: $primary-color solid 1px;
      }
      
      &.active {
        background-color: $course-tabs;
        color: $primary-color;
        border: $primary-color solid 1px;
        font-weight: bold;
      }
    }
  }
  
  .course-content {
    flex: 4;
    padding: 1vw;
    border-radius: 0.3vw;
  }
}
</style>