<template>
  <div class="notice-footer">
    <div class="selection-section">
      <div class="publish-options">
        <label>
          <input type="radio" value="course" v-model="selectedType" @change="handleTypeChange" />
          课程公告
        </label>
        <label>
          <input type="radio" value="homework" v-model="selectedType" @change="handleTypeChange" />
          作业通知
        </label>
        <label>
          <input type="radio" value="exam" v-model="selectedType" @change="handleTypeChange" />
          考试通知
        </label>
      </div>

      <div class="class-selection" v-if="showClassSelection">
        <label>选择班级：</label>
        <div class="class-options">
          <label>
            <input type="checkbox" :value="null" v-model="selectedClasses" @change="handleSelectAll" />
            全部班级
          </label>
          <label v-for="classItem in classList" :key="classItem.id">
            <input type="checkbox" :value="classItem.id" v-model="selectedClasses" :disabled="isAllSelected"
              @change="handleClassSelect" />
            {{ classItem.name }}
          </label>
        </div>
      </div>
    </div>

    <div class="footer-actions">
      <button @click="$emit('cancel')">取消</button>
      <button class="submit-btn" @click="submit"
        :disabled="isLoading || (showClassSelection && selectedClasses.length === 0)">
        {{ isEditMode ? '确认修改' : '发布' }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue'
import { fetchClassList } from '@/api/teacher/class'

const props = defineProps({
  isLoading: Boolean,
  isEditMode: Boolean,
  initialClasses: {
    type: Array,
    default: () => []
  },
  initialNoticeType: {
    type: String,
    default: 'course'
  },
  courseId: {
    type: String,
    required: true
  }
})

const selectedType = ref(props.initialNoticeType || 'course')
const selectedClasses = ref([])
const classList = ref([])
const loadingClasses = ref(false)
const showClassSelection = ref(selectedType.value !== 'course')

// 计算是否选择了"全部班级"
const isAllSelected = computed(() => selectedClasses.value.includes(null))

// 获取班级列表
const getClassList = async () => {
  try {
    loadingClasses.value = true
    const res = await fetchClassList({ courseId: props.courseId })
    if (res.code === 200) {
      classList.value = res.result || []
      if (props.isEditMode && props.initialClasses.length > 0) {
        if (props.initialClasses.includes(null)) {
          selectedClasses.value = [null]
        } else {
          selectedClasses.value = [...props.initialClasses]
        }
      }
    }
  } finally {
    loadingClasses.value = false
  }
}

// 处理类型变化
const handleTypeChange = () => {
  showClassSelection.value = selectedType.value !== 'course'
  if (!showClassSelection.value) {
    selectedClasses.value = []
  }
}

// 处理选择"全部班级"
const handleSelectAll = (e) => {
  if (e.target.checked) {
    selectedClasses.value = [null] // 选择全部时只保留null
  } else {
    selectedClasses.value = [] // 取消选择时清空
  }
}
// 处理选择具体班级
const handleClassSelect = () => {
  selectedClasses.value = selectedClasses.value.filter(id => id !== null)
}

// 初始化
onMounted(() => {
  getClassList()

  selectedType.value = props.initialNoticeType
  showClassSelection.value = selectedType.value !== 'course'
  if (props.isEditMode && props.initialClasses.length > 0) {
    selectedClasses.value = [...props.initialClasses]
  }
})

// 监听courseId变化
watch(() => props.courseId, (newVal) => {
  if (newVal) {
    getClassList()
    selectedClasses.value = []
  }
})

watch(() => props.initialNoticeType, (newVal) => {
  if (newVal) {
    selectedType.value = newVal
    showClassSelection.value = newVal !== 'course'
  }
})

watch(() => props.initialClasses, (newVal) => {
  if (props.isEditMode && newVal && newVal.length > 0) {
    if (newVal.includes(null)) {
      selectedClasses.value = [null]
    } else {
      selectedClasses.value = [...newVal]
    }
  }
}, { immediate: true })

const emit = defineEmits(['submit', 'cancel'])

const submit = () => {
  let classesToSubmit = []

  if (selectedType.value === 'course') {
    classesToSubmit = [null]
  } else if (isAllSelected.value) {
    classesToSubmit = classList.value.map(item => item.id)
  } else {
    classesToSubmit = [...selectedClasses.value]
  }

  emit('submit', {
    type: selectedType.value,
    classes: classesToSubmit,
    isAllSelected: isAllSelected.value
  })
}
</script>

<style scoped>
.notice-footer {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 20px;
  padding-top: 12px;
  border-top: 1px solid #ddd;
}

.selection-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.publish-options,
.class-selection {
  display: flex;
  align-items: flex-start;
}

.publish-options {
  gap: 16px;
}

.class-selection {
  gap: 8px;
}

.class-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.publish-options label,
.class-options label {
  margin-right: 0;
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
}

.publish-options input,
.class-options input {
  margin-right: 4px;
}

.footer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.footer-actions button {
  padding: 6px 14px;
  font-size: 14px;
  cursor: pointer;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fff;
}

.footer-actions .submit-btn {
  background-color: #1677ff;
  color: white;
  border: none;
}

.submit-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.class-options input:disabled+span {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>