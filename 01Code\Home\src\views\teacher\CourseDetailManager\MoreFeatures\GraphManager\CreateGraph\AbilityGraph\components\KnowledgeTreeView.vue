<template>
    <div class="knowledge-tree-view">
        <!-- 添加加载状态提示 -->
        <div v-if="!treeData" class="data-loading">
            <span v-if="store.loading">知识树加载中...</span>
            <span v-else>无法加载知识树数据</span>
        </div>

        <div v-else>
            <div class="tree-toggle" @click="toggleTree" v-if="!isExpanded">
                <span>点击展开知识模块与知识点树</span>
                <span class="toggle-icon">▶</span>
            </div>

            <div class="tree-container" v-show="isExpanded">
                <div class="tree-header">
                    <span>知识模块与知识点</span>
                    <button class="close-btn" @click="toggleTree">×</button>
                </div>

                <!-- G6树图容器 -->
                <div id="g6-container" ref="g6Container">
                    <div v-if="isLoading" class="loading-overlay">加载中...</div>
                    <div v-if="error" class="error-message">{{ error }}</div>
                </div>

                <div class="tree-footer">
                    <button class="confirm-btn" @click="confirmSelection" v-if="isEditing">确认选择</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import * as G6 from '@antv/g6';
import { useAbilityGraphStore } from '@/stores/teacher/graphManager/abilityGraphStore';

const props = defineProps({
    isEditing: Boolean,
    initialData: Array,
    subAbilityIndex: Number, 
    mainAbilityId: String    
});


const emit = defineEmits(['update']);
const store = useAbilityGraphStore();

const isExpanded = ref(false);
const g6Container = ref(null);
const graph = ref(null);
const selectedNodes = ref([]);

const isLoading = ref(false);
const error = ref(null);

// 节点样式配置
const NODE_STYLE = {
    normal: {
        fill: '#f5f5f5',
        stroke: '#666666',
        textFill: '#333'
    },
    selected: {
        fill: '#e6f4ff',
        stroke: '#1890ff',
        textFill: '#1890ff'
    }
};

// 常量定义
const CHECKBOX_PADDING = 20; // 复选框与文字的间距
const CHECKBOX_SIZE = 20;    // 复选框大小
const NODE_PADDING = 20;     // 节点内边距
const NODE_HEIGHT = 50;      // 节点高度
const FONT_SIZE = 16;        // 字体大小

// 添加一个计算属性来处理初始选中的节点
const initialSelectedNodes = computed(() => {
  return props.initialData || [];
});

// 添加一个 watcher 来响应 initialSelectedNodes 的变化
watch(initialSelectedNodes, (newVal) => {
  selectedNodes.value = [...newVal];
  if (graph.value) {
    updateGraphCheckedState();
    expandSelectedNodes(newVal);
  }
}, { immediate: true });

// 从后端获取数据
const treeData = computed(() => {
  console.log('store.knowledgeTreeData:', store.knowledgeTreeData); // 添加调试
  
  if (!store.knowledgeTreeData) {
    console.warn('No knowledge tree data available');
    return null;
  }
  
  // 确保数据是数组且不为空
  if (!Array.isArray(store.knowledgeTreeData)) {
    console.warn('Knowledge tree data is not an array');
    return null;
  }
  
  if (store.knowledgeTreeData.length === 0) {
    console.warn('Knowledge tree data is empty');
    return null;
  }

  // 处理第一个根节点
  const rootNode = store.knowledgeTreeData[0];
  if (!rootNode) {
    console.warn('No root node found');
    return null;
  }

  const processNode = (node) => {
    return {
      id: node.nodeId,
      label: node.nodeName,
      tags: node.tags?.map(tag => tag.tagName) || [],
      children: node.children?.map(processNode) || [],
      level: node.level,
      parentId: node.parentId,
      isLeaf: node.isLeaf
    };
  };

  return processNode(rootNode);
});

// 递归折叠节点
const collapseDeepNodes = (node, level = 0) => {
    if (node.children) {
        node.collapsed = level >= 1;
        node.children.forEach(child => collapseDeepNodes(child, level + 1));
    }
};

// 初始化选中节点
watch(() => props.initialData, (newVal) => {
    selectedNodes.value = [...newVal];
    if (graph.value) {
        updateGraphCheckedState();
        // 确保展开所有选中的节点及其父节点
        expandSelectedNodes(newVal);
    }
}, { immediate: true });

// 初始化 G6 图表
const initGraph = () => {
    console.log('Initializing graph...');
    console.log('Container:', g6Container.value);
    console.log('Container size:', g6Container.value?.clientWidth, g6Container.value?.clientHeight);
    console.log('Tree data:', treeData.value);
    if (!g6Container.value || !treeData.value) {
        console.log('Init aborted - missing container or data');
        return;
    }
    if (graph.value) graph.value.destroy();

    // 注册自定义节点
    G6.registerNode('custom-node', {
        draw(cfg, group) {
            const isSelected = selectedNodes.value.includes(cfg.id);
            const style = isSelected ? NODE_STYLE.selected : NODE_STYLE.normal;

            // 计算文本宽度
            const ctx = document.createElement('canvas').getContext('2d');
            ctx.font = `${FONT_SIZE}px sans-serif`;
            const labelWidth = ctx.measureText(cfg.label).width;
            const tagsWidth = (cfg.tags || []).reduce((total, tag) => {
                return total + ctx.measureText(tag).width + 24;
            }, 0);

            // 计算节点宽度（考虑复选框区域）
            const hasCheckbox = props.isEditing;
            const checkboxAreaWidth = hasCheckbox ? CHECKBOX_PADDING + CHECKBOX_SIZE : 0;
            const contentWidth = Math.max(labelWidth, tagsWidth) + checkboxAreaWidth;
            const nodeWidth = contentWidth + NODE_PADDING * 2;

            // 存储节点尺寸
            cfg.width = nodeWidth;
            cfg.height = NODE_HEIGHT;

            // 绘制节点矩形
            const rect = group.addShape('rect', {
                attrs: {
                    x: 0,
                    y: 0,
                    width: nodeWidth,
                    height: NODE_HEIGHT,
                    fill: style.fill,
                    stroke: style.stroke,
                    lineWidth: 1,
                    radius: 8,
                    shadowColor: '#ccc',
                    shadowBlur: 6,
                    cursor: 'pointer'
                }
            });

            // 文字位置（考虑复选框）
            const textX = hasCheckbox ? NODE_PADDING : nodeWidth / 2;
            const textAlign = hasCheckbox ? 'left' : 'center';

            // 绘制节点文字
            group.addShape('text', {
                attrs: {
                    x: textX,
                    y: NODE_HEIGHT / 2,
                    text: cfg.label,
                    fill: style.textFill,
                    fontSize: FONT_SIZE,
                    fontWeight: 'bold',
                    textAlign,
                    textBaseline: 'middle'
                }
            });

            // 绘制复选框（如果有）
            if (hasCheckbox) {
                const checkboxX = nodeWidth - NODE_PADDING - CHECKBOX_SIZE;
                const checkboxY = (NODE_HEIGHT - CHECKBOX_SIZE) / 2;

                // 复选框背景
                const checkboxRect = group.addShape('rect', {
                    attrs: {
                        x: checkboxX,
                        y: checkboxY,
                        width: CHECKBOX_SIZE,
                        height: CHECKBOX_SIZE,
                        fill: '#fff',
                        stroke: isSelected ? '#4c7bff' : '#d9d9d9',
                        radius: 2,
                        zIndex: 10
                    },
                    name: 'checkbox-rect'
                });

                // 选中标记
                if (isSelected) {
                    group.addShape('path', {
                        attrs: {
                            path: [
                                ['M', checkboxX + 4, checkboxY + 8],
                                ['L', checkboxX + 8, checkboxY + 12],
                                ['L', checkboxX + 12, checkboxY + 4]
                            ],
                            stroke: '#4c7bff',
                            lineWidth: 2,
                            zIndex: 11
                        },
                        name: 'checkbox-check'
                    });
                }
            }

            // 绘制标签（tags）
            if (cfg.tags && cfg.tags.length) {
                const tagY = NODE_HEIGHT + 10;
                let tagX = (nodeWidth - tagsWidth) / 2;

                cfg.tags.forEach(tag => {
                    const tagWidth = ctx.measureText(tag).width + 12;
                    group.addShape('rect', {
                        attrs: {
                            x: tagX,
                            y: tagY,
                            width: tagWidth,
                            height: 24,
                            fill: '#f0f0f0',
                            radius: 12
                        }
                    });
                    group.addShape('text', {
                        attrs: {
                            x: tagX + tagWidth / 2,
                            y: tagY + 12,
                            text: tag,
                            fill: '#666',
                            fontSize: 12,
                            textAlign: 'center',
                            textBaseline: 'middle'
                        }
                    });
                    tagX += tagWidth + 8;
                });
            }

            // 绘制折叠图标（如果有子节点）
            if (cfg.children && cfg.children.length > 0) {
                group.addShape('marker', {
                    attrs: {
                        x: nodeWidth + 10,
                        y: NODE_HEIGHT / 2,
                        r: 6,
                        symbol: cfg.collapsed ? G6.Marker.expand : G6.Marker.collapse,
                        fill: '#fff',
                        stroke: '#666',
                        cursor: 'pointer'
                    },
                    name: 'collapse-icon'
                });
            }

            return rect;
        },

        update(cfg, node) {
            const group = node.getContainer();
            const model = node.getModel();
            const isSelected = selectedNodes.value.includes(model.id);
            const style = isSelected ? NODE_STYLE.selected : NODE_STYLE.normal;

            const CHECKBOX_PADDING = 30;
            const CHECKBOX_SIZE = 20;
            const NODE_PADDING = 20;
            const NODE_HEIGHT = 50;
            const hasCheckbox = props.isEditing;

            const nodeWidth = model.width || 100;
            const checkboxX = nodeWidth - NODE_PADDING - CHECKBOX_SIZE;
            const checkboxY = (NODE_HEIGHT - CHECKBOX_SIZE) / 2;

            // 更新节点矩形样式
            const rect = group.find(ele => ele.get('type') === 'rect' && ele.get('name') !== 'checkbox-rect');
            if (rect) {
                rect.attr({ fill: style.fill, stroke: style.stroke });
            }

            // 更新节点文字位置和样式
            const text = group.find(ele => ele.get('type') === 'text' && ele.get('attrs').text === model.label);
            if (text) {
                const textX = hasCheckbox ? NODE_PADDING : nodeWidth / 2;
                text.attr({
                    x: textX,
                    fill: style.textFill,
                    textAlign: hasCheckbox ? 'left' : 'center'
                });
            }

            // 更新复选框（如果有）
            if (hasCheckbox) {
                let checkboxRect = group.find(ele => ele.get('name') === 'checkbox-rect');
                let checkboxCheck = group.find(ele => ele.get('name') === 'checkbox-check');

                if (!checkboxRect) {
                    checkboxRect = group.addShape('rect', {
                        attrs: {
                            x: checkboxX,
                            y: checkboxY,
                            width: CHECKBOX_SIZE,
                            height: CHECKBOX_SIZE,
                            fill: '#fff',
                            stroke: isSelected ? '#4c7bff' : '#d9d9d9',
                            radius: 2,
                            zIndex: 10
                        },
                        name: 'checkbox-rect'
                    });
                } else {
                    checkboxRect.attr({
                        x: checkboxX,
                        y: checkboxY,
                        stroke: isSelected ? '#4c7bff' : '#d9d9d9'
                    });
                }

                if (isSelected) {
                    if (!checkboxCheck) {
                        checkboxCheck = group.addShape('path', {
                            attrs: {
                                path: [
                                    ['M', checkboxX + 4, checkboxY + 8],
                                    ['L', checkboxX + 8, checkboxY + 12],
                                    ['L', checkboxX + 12, checkboxY + 4]
                                ],
                                stroke: '#4c7bff',
                                lineWidth: 2,
                                zIndex: 11
                            },
                            name: 'checkbox-check'
                        });
                    }
                } else {
                    if (checkboxCheck) checkboxCheck.remove();
                }
            }

            // 更新折叠图标位置
            const collapseIcon = group.find(ele => ele.get('name') === 'collapse-icon');
            if (collapseIcon) {
                collapseIcon.attr({
                    x: nodeWidth + 10,
                    y: NODE_HEIGHT / 2
                });
            }
        },

        getAnchorPoints() {
            return [[0, 0.5], [1, 0.5]];
        }
    });

    // 注册自定义边
    G6.registerEdge('custom-edge', {
        draw(cfg, group) {
            const path = [
                ['M', cfg.startPoint.x, cfg.startPoint.y],
                ['C',
                    (cfg.startPoint.x + cfg.endPoint.x) / 2, cfg.startPoint.y,
                    (cfg.startPoint.x + cfg.endPoint.x) / 2, cfg.endPoint.y,
                    cfg.endPoint.x, cfg.endPoint.y
                ]
            ];
            const color = selectedNodes.value.includes(cfg.targetNode.getModel().id) ? '#1890ff' : '#666666';

            return group.addShape('path', {
                attrs: { path, stroke: color, lineWidth: 2 }
            });
        }
    });

    // 初始化图实例
    graph.value = new G6.TreeGraph({
        container: g6Container.value,
        width: g6Container.value.clientWidth,
        height: 500,
        minZoom: 0.5,
        maxZoom: 2,
        modes: {
            default: ['drag-canvas', 'zoom-canvas', {
                type: 'fit-view',
                options: {
                    padding: [20, 40, 20, 40],
                    animate: true
                }
            }]
        },
        defaultNode: { type: 'custom-node' },
        defaultEdge: { type: 'custom-edge' },
        nodeStateStyles: {
            highlight: { shadowColor: '#f88c77', shadowBlur: 20, lineWidth: 2 },
            dimmed: { opacity: 0.3 }
        },
        layout: {
            type: 'compactBox',
            direction: 'LR',
            getId(d) { return d.id; },
            getHeight() { return 50; },
            getWidth(d) {
                const ctx = document.createElement('canvas').getContext('2d');
                ctx.font = '16px sans-serif';
                const labelWidth = ctx.measureText(d.label).width;
                const tagsWidth = (d.tags || []).reduce((total, tag) => total + ctx.measureText(tag).width + 24, 0);
                return Math.max(labelWidth, tagsWidth) + 40;
            },
            getVGap() { return 40; },
            getHGap() { return 120; },
        }
    });

    // 监听节点点击
    graph.value.on('node:click', (evt) => {
        const { item, target } = evt;
        const model = item.getModel();
        const targetName = target.get('name');

        if (targetName === 'collapse-icon') {
            graph.value.updateItem(item, { collapsed: !model.collapsed });
            graph.value.layout();
        } else if (props.isEditing && targetName !== 'checkbox-rect' && targetName !== 'checkbox-check') {
            const checked = !selectedNodes.value.includes(model.id);
            updateNodeSelection(model.id, checked);
        }
    });

    // 监听复选框点击
    graph.value.on('node:click', (evt) => {
        if (!props.isEditing) return;
        const { item, target } = evt;
        const targetName = target.get('name');
        if (targetName === 'checkbox-rect' || targetName === 'checkbox-check') {
            const model = item.getModel();
            const checked = !selectedNodes.value.includes(model.id);
            updateNodeSelection(model.id, checked);
        }
    });

    // 加载数据
    graph.value.data(treeData.value);
    graph.value.render();

    // 使用setTimeout确保布局完成后再缩放
    setTimeout(() => {
        graph.value.fitView({
            padding: [20, 40, 20, 40],
            animate: true
        });
        graph.value.zoomTo(1, {
            x: graph.value.getWidth() / 2,
            y: graph.value.getHeight() / 2
        });
    }, 300);
};

// 更新节点选中状态
const updateNodeSelection = (nodeId, checked) => {
    // 更新当前节点选中状态
    if (checked) {
        if (!selectedNodes.value.includes(nodeId)) {
            selectedNodes.value.push(nodeId);
        }
    } else {
        selectedNodes.value = selectedNodes.value.filter(id => id !== nodeId);
    }

    // 递归更新祖先节点
    updateAncestors(nodeId, checked);
    
    // 递归更新后代节点
    updateDescendants(nodeId, checked);
    
    // 更新图表显示
    updateGraphCheckedState();
};

// 更新祖先节点选中状态
const updateAncestors = (nodeId, checked) => {
  const node = findNodeById(treeData.value, nodeId);
  if (!node) return;
  
  // 查找父节点
  const parent = findParent(treeData.value, nodeId);
  if (!parent) return; // 没有父节点则停止递归
  
  if (checked) {
    // 如果选中当前节点，确保父节点也被选中
    if (!selectedNodes.value.includes(parent.id)) {
      selectedNodes.value.push(parent.id);
    }
  } else {
    // 如果取消选中当前节点，检查父节点是否还有其他子节点被选中
    const siblings = parent.children || [];
    const hasSelectedSibling = siblings.some(sibling => 
      sibling.id !== nodeId && selectedNodes.value.includes(sibling.id)
    );
    
    // 如果没有其他子节点被选中，取消选中父节点
    if (!hasSelectedSibling) {
      selectedNodes.value = selectedNodes.value.filter(id => id !== parent.id);
    }
  }
  
  // 递归处理父节点
  updateAncestors(parent.id, checked);
};

// 更新后代节点选中状态
const updateDescendants = (nodeId, checked) => {
  const node = findNodeById(treeData.value, nodeId);
  if (!node || !node.children) return;
  
  // 处理所有子节点
  node.children.forEach(child => {
    if (checked) {
      // 如果选中当前节点，确保所有子节点也被选中
      if (!selectedNodes.value.includes(child.id)) {
        selectedNodes.value.push(child.id);
      }
    } else {
      // 如果取消选中当前节点，确保所有子节点也被取消选中
      selectedNodes.value = selectedNodes.value.filter(id => id !== child.id);
    }
    
    // 递归处理子节点
    updateDescendants(child.id, checked);
  });
};

// 根据 ID 查找节点
const findNodeById = (tree, id) => {
  if (!tree) return null;
  if (tree.id === id) return tree;
  if (tree.children) {
    for (const child of tree.children) {
      const found = findNodeById(child, id);
      if (found) return found;
    }
  }
  return null;
};

// 查找父节点
const findParent = (tree, childId, parent = null) => {
  if (!tree) return null;
  if (tree.id === childId) return parent;
  if (tree.children) {
    for (const child of tree.children) {
      const found = findParent(child, childId, tree);
      if (found) return found;
    }
  }
  return null;
};

// 更新图表选中状态
const updateGraphCheckedState = () => {
  if (!graph.value) return;

  // 更新节点样式
  graph.value.getNodes().forEach(node => {
    const model = node.getModel();
    const isSelected = selectedNodes.value.includes(model.id);
    
    // 更新节点样式
    graph.value.updateItem(node, {
      style: {
        fill: isSelected ? '#e6f4ff' : '#f5f5f5',
        stroke: isSelected ? '#1890ff' : '#666666'
      }
    });
    
    // 更新复选框状态
    const checkboxRect = node.getContainer().find(ele => ele.get('name') === 'checkbox-rect');
    if (checkboxRect) {
      checkboxRect.attr('stroke', isSelected ? '#4c7bff' : '#d9d9d9');
    }
    
    const checkboxCheck = node.getContainer().find(ele => ele.get('name') === 'checkbox-check');
    if (checkboxCheck) {
      if (isSelected && !checkboxCheck) {
        // 添加选中标记
        node.getContainer().addShape('path', {
          attrs: {
            path: [
              ['M', model.width - 30 + 4, 25 - 10 + 8],
              ['L', model.width - 30 + 8, 25 - 10 + 12],
              ['L', model.width - 30 + 12, 25 - 10 + 4]
            ],
            stroke: '#4c7bff',
            lineWidth: 2,
            zIndex: 11
          },
          name: 'checkbox-check'
        });
      } else if (!isSelected && checkboxCheck) {
        // 移除选中标记
        checkboxCheck.remove();
      }
    }
  });

  // 更新边样式
  graph.value.getEdges().forEach(edge => {
    const targetModel = edge.getTarget()?.getModel();
    if (!targetModel) return;
    
    const edgeShape = edge.get('shape');
    if (!edgeShape) return;
    
    const color = selectedNodes.value.includes(targetModel.id) ? '#1890ff' : '#666666';
    edgeShape.attr('stroke', color);
  });

  graph.value.refresh();
};

// 展开/收起图谱
const toggleTree = () => {
    if (props.isEditing) {
        isExpanded.value = !isExpanded.value;

        // 如果是展开操作且容器已挂载但图表未初始化，则初始化
        if (isExpanded.value && containerMounted.value && !graph.value) {
            setTimeout(() => {
                initGraph();
            }, 50); // 给 DOM 渲染留出时间
        }
    }
};

// 确认选择
const confirmSelection = async () => {
    if (!props.isEditing || props.subAbilityIndex === undefined) return;

    try {
        // 过滤掉可能的根节点ID
        const selectedKnowledgeIds = selectedNodes.value.filter(id => id !== 'root');
        
        // 通过emit事件将选中的知识点ID数组传递给父组件
        emit('update', selectedKnowledgeIds);
        isExpanded.value = false;
    } catch (error) {
        console.error('更新关联知识点失败:', error);
    }
}

watch(() => props.initialData, (newVal) => {
    selectedNodes.value = [...newVal];
    if (graph.value) {
        updateGraphCheckedState();
        // 确保展开所有选中的节点及其父节点
        expandSelectedNodes(newVal);
    }
}, { immediate: true });

// 添加展开选中节点的方法
const expandSelectedNodes = (selectedIds) => {
  if (!graph.value || !selectedIds?.length) return;
  
  // 先收起所有节点
  graph.value.getNodes().forEach(node => {
    const model = node.getModel();
    if (model.children && model.children.length > 0) {
      graph.value.updateItem(node, { collapsed: true });
    }
  });
  
  // 展开选中的节点及其父节点
  selectedIds.forEach(id => {
    let node = graph.value.findById(id);
    while (node) {
      const model = node.getModel();
      if (model.children && model.children.length > 0) {
        graph.value.updateItem(node, { collapsed: false });
      }
      node = model.parentId ? graph.value.findById(model.parentId) : null;
    }
  });
  
  graph.value.layout();
};

watch(treeData, (newVal) => {
  console.log('Tree data updated:', newVal); // 添加这行
  if (newVal && isExpanded.value && !graph.value) {
    initGraph();
  }
}, { immediate: true });

// 新增一个变量跟踪容器是否已挂载
const containerMounted = ref(false);

// 监听容器挂载状态
const observeContainer = () => {
    if (!g6Container.value) return;

    // 创建一个 ResizeObserver 监听容器尺寸变化
    const observer = new ResizeObserver(() => {
        if (containerMounted.value) return;

        // 当容器有尺寸时，表示已挂载
        if (g6Container.value.clientWidth > 0 && g6Container.value.clientHeight > 0) {
            containerMounted.value = true;
            observer.disconnect();

            // 延迟初始化以确保 DOM 完全稳定
            setTimeout(() => {
                if (isExpanded.value && !graph.value) {
                    initGraph();
                }
            }, 100);
        }
    });

    observer.observe(g6Container.value);
};

// 生命周期钩子
onMounted(() => {
    observeContainer();
    window.addEventListener('resize', handleResize);
});

onBeforeUnmount(() => {
    if (graph.value) graph.value.destroy();
    window.removeEventListener('resize', handleResize);
});

// 处理窗口大小变化
const handleResize = () => {
    if (graph.value && g6Container.value) {
        const { clientWidth } = g6Container.value;
        graph.value.changeSize(clientWidth, 500);
        // 修改为直接设置缩放级别
        graph.value.zoomTo(1, {
            x: clientWidth / 2,
            y: 250
        });
        graph.value.refreshPositions();
    }
};
</script>

<style lang="scss" scoped>
.knowledge-tree-view {
    margin-top: 8px;
}

.tree-toggle {
    padding: 8px 12px;
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: #666;

    &:hover {
        background-color: #e8e8e8;
    }

    .toggle-icon {
        font-size: 12px;
    }
}

.tree-container {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.tree-header {
    padding: 8px 12px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;

    .close-btn {
        background: none;
        border: none;
        font-size: 16px;
        cursor: pointer;
        color: #999;

        &:hover {
            color: #666;
        }
    }
}

#g6-container {
    width: 100%;
    height: 500px;
    overflow: hidden;
    background: #f5f7fa;
    position: relative;
    min-height: 500px;
}

.tree-footer {
    padding: 8px;
    border-top: 1px solid #e0e0e0;
    text-align: right;

    .confirm-btn {
        padding: 6px 12px;
        background-color: #4c7bff;
        color: white;
        border: none;
        border-radius: 4px;
        font-size: 13px;
        cursor: pointer;

        &:hover {
            background-color: #3a6ae0;
        }
    }
}

.loading-overlay, .error-message {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
}
.error-message {
  color: red;
}

.data-loading {
  padding: 20px;
  text-align: center;
  color: #666;
  background: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 10px;
}
</style>