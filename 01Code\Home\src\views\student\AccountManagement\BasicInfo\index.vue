<template>
  <div class="basic-info">
    <div v-if="viewMode === 'view'" class="view-mode">
      <div class="info-header">
        <div class="avatar-container" v-if="userInfo.studentCard">
          <img :src="userInfo.studentCard" alt="证件照" class="avatar">
        </div>
        <h3>{{ userInfo.realName }}</h3>
        <p>{{ userInfo.major }}</p>
      </div>
      
      <div class="info-content">
        <div class="info-item">
          <span>学号：</span>{{ userInfo.studentCode }}
        </div>
        <div class="info-item">
          <span>院系：</span>{{ userInfo.departName }}
        </div>
        <div class="info-item">
          <span>专业：</span>{{ userInfo.major }}
        </div>
        <div class="info-item">
          <span>性别：</span>{{ userInfo.gender }}
        </div>
        <div class="info-item">
          <span>学历：</span>{{ userInfo.education }}
        </div>
        <div class="info-item bio-item">
          <span>简介：</span>{{ userInfo.introduction || '暂无简介' }}
        </div>
      </div>
      
      <el-button type="primary" @click="editMode" :loading="loading">修改信息</el-button>
    </div>
    
    <div v-else class="edit-mode">
      <el-form :model="editForm" label-width="120px" @submit.native.prevent="saveChanges">
        <el-form-item label="姓名">
          <el-input v-model="editForm.realName" disabled></el-input>
        </el-form-item>
        <el-form-item label="学号">
          <el-input v-model="editForm.studentCode" disabled></el-input>
        </el-form-item>
        <el-form-item label="院系">
          <el-input v-model="editForm.departName" disabled></el-input>
        </el-form-item>
        <el-form-item label="专业" prop="major">
          <el-input v-model="editForm.major"></el-input>
        </el-form-item>
        <el-form-item label="性别">
          <el-radio-group v-model="editForm.gender">
            <el-radio label="男">男</el-radio>
            <el-radio label="女">女</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="学历">
          <el-select v-model="editForm.education" placeholder="请选择学历">
            <el-option label="高中及以下" value="高中及以下"></el-option>
            <el-option label="专科" value="专科"></el-option>
            <el-option label="本科" value="本科"></el-option>
            <el-option label="硕士" value="硕士"></el-option>
            <el-option label="博士" value="博士"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="证件照">
          <el-upload
            action="#"
            list-type="picture-card"
            :on-change="handleUpload"
            :on-preview="handlePictureCardPreview"
            :on-remove="handleRemove"
            :file-list="fileList"
            :auto-upload="false"
            :limit="1"
          >
            <i class="el-icon-plus"></i>
            <div slot="tip" class="el-upload__tip">支持 JPG/PNG 格式，大小不超过 2M</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="简介">
          <el-input type="textarea" v-model="editForm.introduction" :rows="4"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="saveChanges" :loading="loading">保存</el-button>
          <el-button @click="cancelEdit">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getStudentInfo, updateStudentInfo, uploadStudentPhoto } from '@/api/student/personalManager'
import { ElMessage } from 'element-plus'

const viewMode = ref('view') // 'view' 或 'edit'
const fileList = ref([])
const loading = ref(false)

// 展示用的学生信息
const userInfo = ref({
  userId: '',
  realName: '',
  studentCode: '',
  departName: '',
  major: '',
  gender: '',
  education: '',
  introduction: '',
  studentCard: ''
})

// 编辑表单数据
const editForm = ref({ ...userInfo.value })

// 加载学生数据
const loadStudentData = async () => {
  try {
    loading.value = true
    const response = await getStudentInfo()
    
    if (response.code === 200 && response.result) {
      const apiData = response.result
      
      // 映射后端字段到前端结构
      userInfo.value = {
        userId: apiData.userId,
        realName: apiData.realName || '未设置',
        studentCode: apiData.code || '未设置',
        departName: apiData.department || '未设置',
        major: apiData.major || '未设置',
        gender: apiData.gender || '男',
        education: apiData.education || '未设置',
        introduction: apiData.introduction || '暂无简介',
        studentCard: apiData.studentCard || ''
      }

      editForm.value = { ...userInfo.value }
      
      // 初始化证件照文件列表
      fileList.value = apiData.studentCard 
        ? [{ name: '证件照', url: apiData.studentCard }] 
        : []
    }
  } catch (error) {
    console.error('加载学生数据失败:', error)
    ElMessage.error('加载信息失败')
  } finally {
    loading.value = false
  }
}

// 进入编辑模式
const editMode = () => {
  viewMode.value = 'edit'
}

// 保存修改
const saveChanges = async () => {
  try {
    loading.value = true
    
    // 构造符合后端API的字段结构
    const updateData = {
      major: editForm.value.major,
      name: editForm.value.realName,
      gender: editForm.value.gender,
      education: editForm.value.education,
      introduction: editForm.value.introduction
    }

    // 提交修改到后端
    const response = await updateStudentInfo(updateData)
    if (response.code !== 200) {
      throw new Error(response.msg || '保存失败')
    }

    // 重新加载最新数据
    await loadStudentData()
    viewMode.value = 'view'
    ElMessage.success('信息已更新')
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error(error.message || '保存失败')
  } finally {
    loading.value = false
  }
}

// 取消编辑
const cancelEdit = () => {
  viewMode.value = 'view'
  editForm.value = { ...userInfo.value }
}

// 上传证件照
const handleUpload = async (file) => {
  try {
    const validTypes = ['image/jpeg', 'image/png']
    const isLt2M = file.size / 1024 / 1024 < 2
    
    if (!validTypes.includes(file.raw?.type || file.type)) {
      ElMessage.error('只能上传JPG/PNG格式图片')
      return false
    }
    if (!isLt2M) {
      ElMessage.error('图片大小不能超过2MB')
      return false
    }

    file.status = 'uploading'
    fileList.value = [file]

    const response = await uploadStudentPhoto(file)
    if (response.code === 200) {
      userInfo.value.studentCard = response.result || ''
      editForm.value.studentCard = response.result || ''
      file.status = 'success'
      ElMessage.success('上传成功')
    } else {
      throw new Error(response.msg || '上传失败')
    }
  } catch (error) {
    console.error('上传失败:', error)
    file.status = 'fail'
    ElMessage.error(error.message || '上传失败')
  }
  return false
}

// 移除证件照
const handleRemove = () => {
  userInfo.value.studentCard = ''
  editForm.value.studentCard = ''
}

// 初始化加载数据
onMounted(() => {
  loadStudentData()
})
</script>

<style scoped>
/* 保持原有样式不变 */
.basic-info {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.info-header {
  text-align: center;
  margin-bottom: 20px;
}

.avatar-container {
  width: 120px;
  height: 120px;
  margin: 0 auto 15px;
  border-radius: 50%;
  overflow: hidden;
  border: 1px solid #eee;
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.info-content {
  margin-bottom: 20px;
}

.info-item {
  margin-bottom: 10px;
  font-size: 14px;
  color: #333;
  line-height: 1.6;
}

.bio-item {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.edit-mode {
  margin-top: 20px;
}
</style>