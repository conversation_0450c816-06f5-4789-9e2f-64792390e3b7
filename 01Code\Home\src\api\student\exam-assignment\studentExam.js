// src\api\student\exam-assignment\studentExam.js
import request from '@/api/service'

// 获取当前学生可见的考试列表
export function getAvailableExams(params) {
    return request.get('/student/exam/available', { params })
}

// 获取考试详细信息
export function getExamDetail(params) {
    return request.get('/student/exam/detail', { params })
}

// 获取课程信息（课程所属学期）
export function getCourseById(params) {
    return request.get('/course/get', { params })
}


// 判断是否提交过（作业/考试）
export function checkIsSubmitted({ id, isExam }) {
    const url = isExam ? '/student/exam/is-submitted' : '/student/assignment/is-submitted'
    const params = isExam ? { examId: id } : { assignmentId: id }
    return request.get(url, { params })
}

// 分页获取可见作业/考试
export function fetchAvailableList({ page = 1, size = 10, courseId, isExam }) {
    const url = isExam ? '/student/exam/available' : '/student/assignment/available'
    return request.get(url, {
        params: {
            page,
            size,
            courseId,
        },
    })
}

// 获取作业/考试详情（含题目）
export function fetchDetail({ id, isExam }) {
    const url = isExam ? '/student/exam/detail' : '/student/assignment/detail'
    return request.get(url, { params: { id } }) // 统一使用 id
}

// 提交作业/考试
export function submitAnswers({ id, isExam, answers }) {
    const url = isExam ? '/student/exam/submit' : '/student/assignment/submit'
    const data = isExam ? { examId: id, answers } : { assignmentId: id, answers }
    return request.post(url, data)
}