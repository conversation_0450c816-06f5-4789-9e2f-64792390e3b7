<!--src\views\student\StudentClass\index.vue-->
<template>
  <div class="teaching-class">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>我的课堂</h1>
    </div>

    <!-- 主内容区 -->
    <div class="classroom-container course-detail-wrapper">
      <!-- 左侧导航标签 -->
      <div class="classroom-nav">
        <button v-for="tab in tabs" :key="tab.title" @click="switchTab(tab)"
          :class="{ active: activeTab.title === tab.title }">
          {{ tab.title }}
        </button>
      </div>

      <!-- 右侧内容区 -->
      <div class="classroom-content">
        <!-- 动态加载的主要内容 -->
        <div class="main-content">
          <component :is="activeTab.component" />
        </div>
        <!-- 图片衔接在内容下方 -->
        <div class="Seeker-footer">
          <img src="@/assets/img/General/Ai.png">
        </div>
      </div>
    </div>

  </div>
  <!-- <MyClass />-->
</template>

<script setup>
import { ref } from 'vue';

// 导入外部组件  
import MyCourse from '@/views/student/MyCourse/index.vue';
import MyGrade from '@/views/student/MyGrade/index.vue';
import MyMessage from '@/views/student/MyMessage/index.vue';
import MyExam from '@/views/student/MyExam/index.vue';
import MyCollection from '@/views/student/MyCollection/index.vue';

const tabs = [
  {
    title: '我的课程',
    component: MyCourse
  },
  {
    title: '我的成绩',
    component: MyGrade
  },
  {
    title: '我的消息',
    component: MyMessage
  },
  {
    title: '我的考试',
    component: MyExam
  },
  {
    title: '我的收藏',
    component: MyCollection
  }
];

// 默认激活第一个标签
const activeTab = ref(tabs[0]);

function switchTab(tab) {
  activeTab.value = tab;
}
</script>

<style lang="scss" scoped>
/* 保持你原有的样式不变 */
.teaching-class {
  position: relative; // 为伪元素定位提供基准
  padding-top: 10vw;
  padding-left: 3vw;
  padding-right: 3vw;

  &::before {
    content: '';
    position: fixed; // 固定背景
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('@/assets/img/Home/ic_home_bg.png') top/cover no-repeat;
    z-index: -1; // 确保背景在内容下方
  }

  .page-header {
    color: white;
    margin-bottom: 2vw; // 增加一些底部间距

    h1 {
      // 注意这里改为 h1 而不是 .h1
      font-size: 3rem; // 调整为更大的字号
      font-weight: 400; // 加粗
      margin: 0; // 移除默认边距
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); // 可选：添加文字阴影增强可读性
    }
  }
}

.classroom-container {
  display: flex;
  gap: 1.5vw;
  min-height: 70vh;
  background-color: white;
  border-radius: 1vw 1vw 0 0; // 只设置左上和右上圆角
  overflow: hidden; // 确保子元素不会破坏圆角效果
  padding: 2vw;
  margin-top: 10vw;

  .classroom-nav {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1vw;
    margin-top: 1.5vw;

    button {
      padding: 0.8vw 2vw;
      border: $course-tabs-solid solid 1px;
      background-color: white;
      cursor: pointer;
      border-radius: 2vw;
      font-size: 1.2rem;
      transition: all 0.3s ease;
      text-align: left;

      &:hover {
        background-color: $course-tabs;
        color: $primary-color;
        border: $primary-color solid 1px;
      }

      &.active {
        background-color: $course-tabs;
        color: $primary-color;
        border: $primary-color solid 1px;
        font-weight: bold;
      }
    }
  }

  .classroom-content {
    flex: 4;
    display: flex;
    flex-direction: column; // 垂直排列
    gap: 1vw; // 内容与图片的间距
    padding: 1vw;

    .main-content {
      flex: 1; // 主要内容占据剩余空间
    }

    .Seeker-footer {
      align-self: flex-end; // 图片靠右
      padding: 1vw 0; // 上下边距

      img {
        width: 80px; // 控制图片大小
        height: auto;
      }
    }
  }
}
</style>
