{"_attachments": {}, "_id": "stylus", "_rev": "5899-61f153b8a920628a7b70f7c5", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "description": "Robust, expressive, and feature-rich CSS superset", "dist-tags": {"latest": "0.64.0"}, "license": "MIT", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}, {"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}, {"name": "xdan", "email": "<EMAIL>"}], "name": "stylus", "readme": "<p align=\"center\"><a href=\"https://stylus-lang.com\" target=\"_blank\" rel=\"noopener noreferrer\"><img width=\"150\" src=\"https://raw.githubusercontent.com/stylus/stylus/dev/graphics/Logos/stylus.png\" alt=\"Stylus logo\"></a></p>\n\n[![Build Status](https://github.com/stylus/stylus/actions/workflows/ci.yml/badge.svg?branch=dev)](https://github.com/stylus/stylus/actions?query=branch%3Adev)\n[![Maintenance](https://img.shields.io/badge/Maintained%3F-yes-brightgreen.svg)](https://github.com/stylus/stylus/pulse)\n[![npm version](https://img.shields.io/npm/v/stylus?color=brightgreen)](https://www.npmjs.com/package/stylus)\n[![npm](https://img.shields.io/npm/dm/stylus.svg)](https://www.npmjs.com/package/stylus)\n[![Join the community on github discussion](https://img.shields.io/badge/Join%20the%20community-on%20discussions-%23754ffb?logo=googlechat&logoColor=white)](https://github.com/stylus/stylus/discussions)\n\nStylus is a revolutionary new language, providing an efficient, dynamic, and expressive way to generate CSS. Supporting both an indented syntax and regular CSS style.\n\n## Sponsors\n\nYou can sponsor stylus ongoing development via [opencollective](https://opencollective.com/stylus/) or [paypal](https://www.paypal.com/paypalme/iChenLei/) !\n\n<table>\n  <tbody>\n    <tr>\n      <td width=\"50%\" align=\"center\">\n        <a href=\"https://automattic.com/\">\n          <img alt=\"automattic\" src=\"https://user-images.githubusercontent.com/14012511/*********-40ca295f-a0cf-45a6-b24a-303496552499.svg\" />\n        </a>\n      </td>\n      <td width=\"50%\" align=\"center\">\n        <a href=\"https://www.mojotech.com/\">\n          <img alt=\"mojotech\" src=\"https://user-images.githubusercontent.com/14012511/*********-0a090b42-89f8-4651-9506-b6aefac57c66.png\" />\n        </a>\n      </td>\n    </tr>\n    <tr>\n      <td width=\"50%\" align=\"center\">\n         <p style=\"font-size: 30px\">Your Logo</p>\n      </td>\n      <td width=\"50%\" align=\"center\">\n        <a href=\"https://www.paypal.com/paypalme/iChenLei/\">\n          <img alt=\"Paypal stylus\" src=\"https://www.paypalobjects.com/digitalassets/c/website/marketing/apac/C2/logos-buttons/optimize/Full_Online_Tray_RGB.png\" />\n        </a>\n      </td>\n    </tr>\n  </tbody>\n</table>\n\n### Backers\n[![Backers](https://opencollective.com/stylus/individuals.svg)](https://opencollective.com/stylus/)\n\n## Installation\n\n```bash\n$ npm install stylus -g\n```\n\n## Basic Usage\nWatch and compile a stylus file from command line with \n```bash\nstylus -w style.styl -o style.css\n```\nYou can also [try all stylus features on stylus-lang.com](http://stylus-lang.com/try.html), build something with stylus on [codepen](http://codepen.io) or [RunKit](https://npm.runkit.com/stylus)\n\n### Community modules\n\n  - https://github.com/stylus/stylus/wiki\n\n### Stylus cheatsheet\n\n  - [Stylus cheatsheet](https://devhints.io/stylus), very useful stylus syntax code snippet for you\n\n### Code of Conduct\n\nPlease note that this project is released with a [Contributor Code of Conduct](Code_of_Conduct.md). By participating in this project you agree to abide by its terms.\n\n### Contribution\n\nPlease read our [Contribution Guide](Contributing.md) before making any pull requests to the project. Included are directions for opening issues, workflows, and coding standards.\n\nThank you to all the people who already contributed to Stylus!\n\n<a href=\"https://github.com/stylus/stylus/graphs/contributors\"><img src=\"https://opencollective.com/stylus/contributors.svg?width=890\" /></a>\n\n## License \n\n[MIT](https://github.com/stylus/stylus/blob/dev/LICENSE)\n\nCopyright (c) 2010-present [TJ](https://github.com/tj) and [Stylus maintainers](https://github.com/orgs/stylus/people)\n", "time": {"created": "2022-01-26T13:59:20.927Z", "modified": "2025-06-21T10:20:44.533Z", "0.55.0": "2021-09-04T07:17:22.051Z", "0.54.8": "2020-07-16T12:07:56.864Z", "0.54.7": "2019-08-21T11:40:25.172Z", "0.54.6": "2019-08-20T10:42:11.894Z", "0.54.5": "2016-04-27T22:43:06.844Z", "0.54.4": "2016-04-27T17:11:03.771Z", "0.54.3": "2016-04-26T20:11:56.278Z", "0.54.2": "2016-03-11T21:26:51.061Z", "0.54.0": "2016-03-05T16:12:12.151Z", "0.53.0": "2015-12-14T13:54:33.275Z", "0.52.4": "2015-09-04T06:10:44.513Z", "0.52.3": "2015-09-03T21:15:23.479Z", "0.52.2": "2015-09-03T16:14:29.287Z", "0.52.1": "2015-09-03T12:01:48.333Z", "0.52.0": "2015-07-19T17:26:47.395Z", "0.52.0-alpha": "2015-07-15T13:31:09.120Z", "0.51.1": "2015-04-28T07:28:48.258Z", "0.51.0": "2015-04-23T14:06:49.706Z", "0.50.0": "2015-02-05T08:47:54.130Z", "0.49.3": "2014-11-06T00:12:31.572Z", "0.49.2": "2014-10-14T12:08:29.597Z", "0.49.1": "2014-09-24T09:54:45.087Z", "0.49.0": "2014-09-22T06:39:58.683Z", "0.48.1": "2014-08-21T11:27:29.305Z", "0.48.0": "2014-08-20T18:28:20.224Z", "0.47.3": "2014-07-22T13:09:36.577Z", "0.47.2": "2014-07-19T13:18:53.466Z", "0.47.1": "2014-07-02T16:28:38.051Z", "0.47.0": "2014-07-01T17:30:12.731Z", "0.46.3": "2014-06-09T15:47:22.699Z", "0.46.2": "2014-06-04T07:17:08.661Z", "0.46.1": "2014-06-03T21:45:35.568Z", "0.46.0": "2014-06-03T08:33:32.333Z", "0.45.1": "2014-05-16T00:24:52.933Z", "0.45.0": "2014-05-11T19:20:26.505Z", "0.44.0": "2014-04-23T12:01:14.802Z", "0.44.0-beta3": "2014-04-14T06:05:57.568Z", "0.44.0-beta2": "2014-04-07T13:18:11.849Z", "0.43.1": "2014-04-07T12:00:24.030Z", "0.44.0-beta": "2014-04-06T17:48:22.179Z", "0.43.0": "2014-04-05T19:19:12.929Z", "0.42.3": "2014-03-03T06:24:18.211Z", "0.42.2": "2014-01-30T11:44:28.411Z", "0.42.1": "2014-01-18T20:07:39.831Z", "0.42.0": "2014-01-06T19:25:13.708Z", "0.41.3": "2013-12-12T18:47:46.524Z", "0.41.2": "2013-12-10T09:43:40.593Z", "0.41.1": "2013-12-08T19:47:55.088Z", "0.41.0": "2013-11-30T19:05:19.877Z", "0.40.3": "2013-11-16T16:40:13.825Z", "0.40.2": "2013-11-12T13:06:42.851Z", "0.40.1": "2013-11-12T11:08:14.379Z", "0.40.0": "2013-11-05T07:37:25.321Z", "0.39.4": "2013-11-03T20:19:22.960Z", "0.39.3": "2013-11-01T12:37:28.893Z", "0.39.2": "2013-10-31T07:28:38.811Z", "0.39.1": "2013-10-30T15:40:43.748Z", "0.39.0": "2013-10-30T05:40:17.857Z", "0.38.0": "2013-09-24T13:14:42.222Z", "0.37.0": "2013-08-18T20:04:13.997Z", "0.36.1": "2013-08-06T10:56:35.447Z", "0.36.0": "2013-08-01T16:13:05.798Z", "0.35.1": "2013-07-29T17:39:10.995Z", "0.35.0": "2013-07-28T21:32:46.974Z", "0.34.1": "2013-07-12T13:40:18.904Z", "0.34.0": "2013-07-12T10:15:06.757Z", "0.33.1": "2013-06-30T16:59:07.546Z", "0.33.0": "2013-06-30T16:47:23.732Z", "0.32.1": "2013-02-27T23:22:55.618Z", "0.32.0": "2013-01-04T16:32:07.278Z", "0.31.0": "2012-11-24T18:12:13.619Z", "0.30.1": "2012-10-17T18:49:49.394Z", "0.30.0": "2012-10-16T04:59:42.380Z", "0.29.0": "2012-08-15T15:56:56.327Z", "0.28.2": "2012-07-15T18:49:20.878Z", "0.28.1": "2012-07-07T15:41:37.712Z", "0.28.0": "2012-07-06T23:31:10.125Z", "0.27.2": "2012-06-19T23:56:32.124Z", "0.27.1": "2012-05-28T16:13:55.768Z", "0.27.0": "2012-05-10T19:07:15.837Z", "0.26.1": "2012-05-07T16:25:51.580Z", "0.26.0": "2012-04-27T17:39:56.982Z", "0.25.0": "2012-04-04T01:29:34.803Z", "0.24.0": "2012-02-17T00:06:18.726Z", "0.23.0": "2012-02-02T16:43:17.272Z", "0.22.6": "2012-01-20T16:47:02.172Z", "0.22.5": "2012-01-17T04:13:43.876Z", "0.22.4": "2012-01-11T21:27:01.259Z", "0.22.3": "2012-01-11T16:52:02.470Z", "0.22.2": "2012-01-08T20:25:15.846Z", "0.22.1": "2012-01-08T13:59:39.626Z", "0.22.0": "2012-01-05T00:24:05.383Z", "0.21.2": "2011-12-22T17:27:40.011Z", "0.21.1": "2011-12-20T16:51:26.901Z", "0.21.0": "2011-12-17T18:32:32.383Z", "0.20.1": "2011-12-16T17:31:54.513Z", "0.20.0": "2011-12-11T15:23:46.350Z", "0.19.8": "2011-12-01T18:51:09.548Z", "0.19.7": "2011-11-30T18:37:33.771Z", "0.19.6": "2011-11-30T17:29:02.874Z", "0.19.5": "2011-11-28T18:32:58.864Z", "0.19.4": "2011-11-28T17:22:33.465Z", "0.19.3": "2011-11-17T22:28:18.518Z", "0.19.2": "2011-11-09T18:11:51.128Z", "0.19.1": "2011-11-08T16:03:39.616Z", "0.19.0": "2011-10-26T18:41:17.028Z", "0.18.0": "2011-10-21T16:39:39.975Z", "0.17.0": "2011-09-30T19:09:17.621Z", "0.16.0": "2011-09-26T18:44:13.086Z", "0.15.4": "2011-09-14T22:09:30.777Z", "0.15.3": "2011-09-14T16:02:12.784Z", "0.15.2": "2011-09-07T00:27:17.880Z", "0.15.1": "2011-08-18T17:48:31.922Z", "0.15.0": "2011-08-16T04:41:11.745Z", "0.14.0": "2011-08-10T15:59:28.376Z", "0.13.9": "2011-08-04T23:06:41.335Z", "0.13.8": "2011-08-01T16:00:48.314Z", "0.13.7": "2011-07-15T18:10:10.183Z", "0.13.6": "2011-07-12T18:41:01.577Z", "0.13.5": "2011-06-27T17:40:49.858Z", "0.13.4": "2011-06-22T16:09:34.637Z", "0.13.3": "2011-06-01T22:22:54.429Z", "0.13.2": "2011-05-31T21:54:08.613Z", "0.13.1": "2011-05-30T17:51:10.925Z", "0.13.0": "2011-05-17T18:11:51.309Z", "0.12.4": "2011-05-12T18:04:58.189Z", "0.12.3": "2011-05-08T17:29:25.795Z", "0.12.2": "2011-05-03T20:45:32.180Z", "0.12.1": "2011-04-29T23:31:00.911Z", "0.12.0": "2011-04-29T21:41:58.390Z", "0.11.12": "2011-04-28T01:54:37.091Z", "0.11.11": "2011-04-25T01:58:51.175Z", "0.11.10": "2011-04-17T23:04:02.268Z", "0.11.9": "2011-04-15T21:15:21.703Z", "0.11.8": "2011-04-15T17:12:00.736Z", "0.11.7": "2011-04-12T21:49:27.287Z", "0.11.6": "2011-04-12T18:22:53.874Z", "0.11.5": "2011-04-12T14:39:59.565Z", "0.11.4": "2011-04-10T19:05:58.786Z", "0.11.3": "2011-04-08T23:37:54.020Z", "0.11.2": "2011-04-06T22:16:50.594Z", "0.11.1": "2011-04-02T00:05:20.252Z", "0.11.0": "2011-04-01T22:38:33.701Z", "0.10.0": "2011-03-29T22:49:17.956Z", "0.9.2": "2011-03-21T18:08:38.694Z", "0.9.1": "2011-03-19T00:00:02.730Z", "0.9.0": "2011-03-18T18:46:27.156Z", "0.8.0": "2011-03-14T16:14:08.768Z", "0.7.4": "2011-03-11T00:05:29.814Z", "0.7.3": "2011-03-09T17:43:43.337Z", "0.7.2": "2011-03-09T02:34:45.795Z", "0.7.1": "2011-03-08T02:52:21.828Z", "0.7.0": "2011-03-02T08:12:01.183Z", "0.6.7": "2011-03-02T04:13:44.622Z", "0.6.6": "2011-03-02T03:34:11.784Z", "0.6.5": "2011-02-25T07:03:49.817Z", "0.6.4": "2011-02-24T17:10:04.320Z", "0.6.3": "2011-02-22T19:24:18.863Z", "0.6.2": "2011-02-21T22:28:20.839Z", "0.6.0": "2011-02-18T17:03:48.821Z", "0.5.3": "2011-02-17T22:28:58.396Z", "0.5.2": "2011-02-15T20:47:56.977Z", "0.5.1": "2011-02-11T23:31:47.118Z", "0.5.0": "2011-02-09T21:30:15.589Z", "0.4.1": "2011-02-09T18:35:08.711Z", "0.4.0": "2011-02-07T19:28:21.209Z", "0.3.1": "2011-02-04T18:13:38.616Z", "0.3.0": "2011-02-04T17:40:16.656Z", "0.2.1": "2011-02-02T17:09:29.991Z", "0.2.0": "2011-02-01T19:48:31.360Z", "0.1.0": "2011-02-01T17:39:42.414Z", "0.0.2": "2011-01-31T20:14:16.038Z", "0.0.1": "2011-01-31T18:22:10.089Z", "0.56.0": "2021-12-18T02:17:23.044Z", "0.57.0": "2022-02-19T08:33:36.884Z", "0.58.0": "2022-05-28T07:51:27.957Z", "0.58.1": "2022-05-31T06:57:48.694Z", "0.59.0": "2022-08-13T03:21:46.090Z", "0.60.0": "2023-08-30T04:59:31.328Z", "0.61.0": "2023-11-04T07:35:21.179Z", "0.62.0": "2023-11-18T04:44:53.874Z", "0.63.0": "2024-03-05T05:05:22.258Z", "0.64.0": "2024-10-20T07:12:10.493Z"}, "versions": {"0.55.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.55.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus.git"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require chai --bail --check-leaks --reporter dot", "test-cov": "mocha test/ test/middleware/ --require chai --bail --reporter html-cov > coverage.html"}, "dependencies": {"css": "^3.0.0", "debug": "~3.1.0", "glob": "^7.1.6", "mkdirp": "~1.0.4", "safer-buffer": "^2.1.2", "sax": "~1.2.4", "semver": "^6.3.0", "source-map": "^0.7.3"}, "devDependencies": {"chai": "^4.3.4", "jscoverage": "~0.6.0", "mocha": "^9.0.3"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "gitHead": "9cb7635af60bf918ed8c2a990efd251e2e825975", "_id": "stylus@0.55.0", "_nodeVersion": "14.17.4", "_npmVersion": "6.14.14", "dist": {"shasum": "bd404a36dd93fa87744a9dd2d2b1b8450345e5fc", "size": 106848, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.55.0.tgz", "integrity": "sha512-MuzIIVRSbc8XxHH7FjkvWqkIcr1BvoMZoR/oFuAJDlh7VSaNJzrB4uJ38GRQa+mWjLXODAMzeDe0xi9GYbGwnw=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/stylus_0.55.0_1630739841468_0.8592832464821087"}, "_hasShrinkwrap": false, "publish_time": 1630739842051, "_cnpm_publish_time": 1630739842051, "_cnpmcore_publish_time": "2021-12-13T13:28:55.954Z"}, "0.54.8": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.54.8", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus.git"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --check-leaks --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "~2.0.0", "debug": "~3.1.0", "glob": "^7.1.6", "mkdirp": "~1.0.4", "safer-buffer": "^2.1.2", "sax": "~1.2.4", "semver": "^6.3.0", "source-map": "^0.7.3"}, "devDependencies": {"jscoverage": "~0.6.0", "mocha": "^8.0.1", "should": "^13.2.3"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "gitHead": "0087d4b977190fb83f075b8a5b0bd2a0bc3cf631", "_id": "stylus@0.54.8", "_nodeVersion": "13.9.0", "_npmVersion": "6.14.5", "dist": {"shasum": "3da3e65966bc567a7b044bfe0eece653e099d147", "size": 104913, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.54.8.tgz", "integrity": "sha512-vr54Or4BZ7pJafo2mpf0ZcwA74rpuYCZbxrHBsH8kbcXOwSfvBFwsRfpGO5OD5fhG5HDCFW737PKaawI7OqEAg=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_npmUser": {"name": "xdan", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/stylus_0.54.8_1594901276651_0.15631931239008923"}, "_hasShrinkwrap": false, "publish_time": 1594901276864, "_cnpm_publish_time": 1594901276864, "_cnpmcore_publish_time": "2021-12-13T13:28:56.433Z"}, "0.54.7": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.54.7", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus.git"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --check-leaks --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "~2.0.0", "debug": "~3.1.0", "glob": "^7.1.3", "mkdirp": "~0.5.x", "safer-buffer": "^2.1.2", "sax": "~1.2.4", "semver": "^6.0.0", "source-map": "^0.7.3"}, "devDependencies": {"jscoverage": "~0.6.0", "mocha": "^5.2.0", "should": "^13.2.3"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "gitHead": "97bf57dd9439f33f2bda20c36be82b7e0d4e3da9", "_id": "stylus@0.54.7", "_nodeVersion": "11.6.0", "_npmVersion": "6.10.1", "_npmUser": {"name": "xdan", "email": "<EMAIL>"}, "dist": {"shasum": "c6ce4793965ee538bcebe50f31537bfc04d88cd2", "size": 110074, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.54.7.tgz", "integrity": "sha512-Yw3WMTzVwevT6ZTrLCYNHAFmanMxdylelL3hkWNgPMeTCpMwpV3nXjpOHuBXtFv7aiO2xRuQS6OoAdgkNcSNug=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/stylus_0.54.7_1566387624892_0.6224406881342215"}, "_hasShrinkwrap": false, "publish_time": 1566387625172, "_cnpm_publish_time": 1566387625172, "_cnpmcore_publish_time": "2021-12-13T13:28:56.908Z"}, "0.54.6": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.54.6", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus.git"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --check-leaks --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "~2.0.0", "debug": "~3.1.0", "glob": "^7.1.3", "mkdirp": "~0.5.x", "safer-buffer": "^2.1.2", "sax": "~1.2.4", "semver": "^6.0.0", "source-map": "^0.7.3"}, "devDependencies": {"jscoverage": "~0.6.0", "mocha": "^5.2.0", "should": "^13.2.3"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "gitHead": "1b434aba52e9477412b7d98fd2d3a872a85b3d49", "_id": "stylus@0.54.6", "_nodeVersion": "11.6.0", "_npmVersion": "6.10.1", "_npmUser": {"name": "xdan", "email": "<EMAIL>"}, "dist": {"shasum": "e8d0c540efd748075c763763f955946ab36f0f66", "size": 109160, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.54.6.tgz", "integrity": "sha512-6s7jEoHKSdAaEtfOnGCNJWy7rcgxPf/pdSUUCusnfV6qO28OmmOLAUYeIJIgJlXWfWTwDO9ytRdd8KhxIdB4EQ=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/stylus_0.54.6_1566297731733_0.20898580020757085"}, "_hasShrinkwrap": false, "publish_time": 1566297731894, "_cnpm_publish_time": 1566297731894, "_cnpmcore_publish_time": "2021-12-13T13:28:57.337Z"}, "0.54.5": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.54.5", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus.git"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --check-leaks --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.5.x", "debug": "*", "sax": "0.5.x", "glob": "7.0.x", "source-map": "0.1.x"}, "devDependencies": {"should": "8.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "gitHead": "5aff5ed0e3f482ed48f30110e24fccad7f5559ab", "_id": "stylus@0.54.5", "_shasum": "42b9560931ca7090ce8515a798ba9e6aa3d6dc79", "_from": ".", "_npmVersion": "3.8.3", "_nodeVersion": "5.10.1", "_npmUser": {"name": "<PERSON>ya", "email": "<EMAIL>"}, "dist": {"shasum": "42b9560931ca7090ce8515a798ba9e6aa3d6dc79", "size": 98760, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.54.5.tgz", "integrity": "sha512-4Yzg9aqLf3f4sDvO3x+Fbp2V634j9ikFGCFokIPYi+7Y4IG/nxAiPUs95MRlo+lPdTsxAs9wCzEclmPccItISA=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/stylus-0.54.5.tgz_1461796984140_0.8051077802665532"}, "publish_time": 1461796986844, "_hasShrinkwrap": false, "_cnpm_publish_time": 1461796986844, "_cnpmcore_publish_time": "2021-12-13T13:28:57.799Z"}, "0.54.4": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.54.4", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus.git"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --check-leaks --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.5.x", "debug": "*", "sax": "0.5.x", "glob": "7.0.x", "source-map": "0.1.x"}, "devDependencies": {"should": "8.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "gitHead": "9d341936eaa56e3758a6b915d5e195423342625b", "_id": "stylus@0.54.4", "_shasum": "b0b89201a20b653029223b5a896664e590dd9a1f", "_from": ".", "_npmVersion": "3.7.1", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON>ya", "email": "<EMAIL>"}, "dist": {"shasum": "b0b89201a20b653029223b5a896664e590dd9a1f", "size": 98732, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.54.4.tgz", "integrity": "sha512-zjyUaJ+okzUlEsEpcMMK9BBNQo4qlXoL5lmquuOnNBs3LPbjSFf/245AOAgMbQnBQxDlteanpDDrY9R75EBmmg=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/stylus-0.54.4.tgz_1461777061500_0.7129299163352698"}, "publish_time": 1461777063771, "_hasShrinkwrap": false, "_cnpm_publish_time": 1461777063771, "_cnpmcore_publish_time": "2021-12-13T13:28:58.287Z"}, "0.54.3": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.54.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus.git"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --check-leaks --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.5.x", "debug": "*", "sax": "0.5.x", "glob": "7.0.x", "source-map": "0.1.x"}, "devDependencies": {"should": "8.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "gitHead": "91f280af8ee1312c62cad87acd4e27fa76456146", "_id": "stylus@0.54.3", "_shasum": "6343fee541fae60531a8da459dc9437700f0f4af", "_from": ".", "_npmVersion": "3.8.3", "_nodeVersion": "5.10.1", "_npmUser": {"name": "<PERSON>ya", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "dist": {"shasum": "6343fee541fae60531a8da459dc9437700f0f4af", "size": 98621, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.54.3.tgz", "integrity": "sha512-4eb8eGPKZLFVpMY+l03IhFUJFwGbNUJ2WxQZdEhKkb8l3R3uzcuWstcvfpPcGhnG+1Fm9NfxOW0W/GtBt9bCmw=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/stylus-0.54.3.tgz_1461701513747_0.8110062405467033"}, "publish_time": 1461701516278, "_hasShrinkwrap": false, "_cnpm_publish_time": 1461701516278, "_cnpmcore_publish_time": "2021-12-13T13:28:58.777Z"}, "0.54.2": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.54.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus.git"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --check-leaks --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.5.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x", "source-map": "0.1.x"}, "devDependencies": {"should": "8.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "gitHead": "0ac8abe202160172c3b1bf8cd28cbf17eedfbefd", "_id": "stylus@0.54.2", "_shasum": "77f0da93cde5a55ab917496850b7ab5214474a0b", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.8.0", "_npmUser": {"name": "<PERSON>ya", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "dist": {"shasum": "77f0da93cde5a55ab917496850b7ab5214474a0b", "size": 97750, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.54.2.tgz", "integrity": "sha512-tlymppNSjDAvf0ySJ9Weg2EyyUDzShfh0TGLbLWk4Sd8FmuP+QUlSvuFxy3CU2YlxnDQVoA76tIs8ZdzZPIk1Q=="}, "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/stylus-0.54.2.tgz_1457731608401_0.5718708764761686"}, "publish_time": 1457731611061, "_hasShrinkwrap": false, "_cnpm_publish_time": 1457731611061, "_cnpmcore_publish_time": "2021-12-13T13:28:59.217Z"}, "0.54.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.54.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus.git"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --check-leaks --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.5.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x", "source-map": "0.1.x"}, "devDependencies": {"should": "8.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "gitHead": "26e04d862a9dfbbec9a39bc4747d9c66d5eadf19", "_id": "stylus@0.54.0", "_shasum": "f28bd6c49cc9f93632355853817c28ef3e7c756e", "_from": ".", "_npmVersion": "3.3.5", "_nodeVersion": "0.10.28", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "dist": {"shasum": "f28bd6c49cc9f93632355853817c28ef3e7c756e", "size": 114497, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.54.0.tgz", "integrity": "sha512-mJjvdHo2UauH54Rui9/Xly7kY2Twyxvt1tVJA0bLfKEJ8BaTSvznHQYdsE+wXIPOm5rSj0DHyoAhLZLmgIAElg=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/stylus-0.54.0.tgz_1457194328491_0.033713654382154346"}, "publish_time": 1457194332151, "_hasShrinkwrap": false, "_cnpm_publish_time": 1457194332151, "_cnpmcore_publish_time": "2021-12-13T13:28:59.794Z"}, "0.53.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.53.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus.git"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.5.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x", "source-map": "0.1.x"}, "devDependencies": {"should": "2.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "gitHead": "a0b7cc61feb8abf361a1660710f8f96aa8aba309", "_id": "stylus@0.53.0", "_shasum": "6b51e7665097f8dd4a6965e14ceea5e4b9fd724a", "_from": ".", "_npmVersion": "3.3.5", "_nodeVersion": "0.10.28", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "dist": {"shasum": "6b51e7665097f8dd4a6965e14ceea5e4b9fd724a", "size": 116409, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.53.0.tgz", "integrity": "sha512-it1ZZSCdSeohZJGWCfFWuKbI9KrBl8g4VlV61d9gyl8yf3COoOk7L7S87yWZrLbczFAPS3FJdZsYiR6SPwhsog=="}, "publish_time": 1450101273275, "_hasShrinkwrap": false, "_cnpm_publish_time": 1450101273275, "_cnpmcore_publish_time": "2021-12-13T13:29:00.370Z"}, "0.52.4": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.52.4", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.5.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x", "source-map": "0.1.x"}, "devDependencies": {"should": "2.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "_id": "stylus@0.52.4", "_shasum": "6551b5f0bfdcf29ee7f0fe0a59b7eb6ff26d2539", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "dist": {"shasum": "6551b5f0bfdcf29ee7f0fe0a59b7eb6ff26d2539", "size": 88269, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.52.4.tgz", "integrity": "sha512-m7qTTjX0/AQsTF9j16JPfZTzS2nTTBVVfP/naU5pZS1IVsbIY0LVBsi1GRG+h3rJx3FsmFMp43eB5+e00coH1Q=="}, "publish_time": 1441347044513, "_hasShrinkwrap": false, "_cnpm_publish_time": 1441347044513, "_cnpmcore_publish_time": "2021-12-13T13:29:00.792Z"}, "0.52.3": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.52.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.5.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x", "source-map": "0.1.x"}, "devDependencies": {"should": "2.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "_id": "stylus@0.52.3", "_shasum": "f24e6c2f7def49301a0dd7e893a4cfab3b88ad9f", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "dist": {"shasum": "f24e6c2f7def49301a0dd7e893a4cfab3b88ad9f", "size": 88171, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.52.3.tgz", "integrity": "sha512-bR534+ptkyAkQLh4rVlDBmlyZO7NiQ206Z8UQNVXpquPWfWmiP+uf7v/H//4E3TFZ3IzjoPTGl7jw9Vp8C9JJw=="}, "publish_time": 1441314923479, "_hasShrinkwrap": false, "_cnpm_publish_time": 1441314923479, "_cnpmcore_publish_time": "2021-12-13T13:29:01.275Z"}, "0.52.2": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.52.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.5.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x", "source-map": "0.1.x"}, "devDependencies": {"should": "2.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "_id": "stylus@0.52.2", "_shasum": "963ecd7feb165306ca10a7fc849a2d018a991510", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "dist": {"shasum": "963ecd7feb165306ca10a7fc849a2d018a991510", "size": 88125, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.52.2.tgz", "integrity": "sha512-hJFI0JW3UzKFDeqm8+h93TJ9wTsSEsIzirBoSHAWedtrmZJlPKDzaG/QXzM9SxfbdWMQFxsJEONBGttvhSDU6g=="}, "publish_time": 1441296869287, "_hasShrinkwrap": false, "_cnpm_publish_time": 1441296869287, "_cnpmcore_publish_time": "2021-12-13T13:29:01.737Z"}, "0.52.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.52.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.5.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x", "source-map": "0.1.x"}, "devDependencies": {"should": "2.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "_id": "stylus@0.52.1", "_shasum": "8c847864dcfb63cf60c81768e31dff54dd897ccc", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "dist": {"shasum": "8c847864dcfb63cf60c81768e31dff54dd897ccc", "size": 88129, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.52.1.tgz", "integrity": "sha512-EWStYomRIa5aK8KSrKOdtoBzB+k/+fW2cDw/pPnKmhGVghb2h74xOoGtrATUNBZ+g4jgXwXR/yhAVX4g9aFzYg=="}, "publish_time": 1441281708333, "_hasShrinkwrap": false, "_cnpm_publish_time": 1441281708333, "_cnpmcore_publish_time": "2021-12-13T13:29:02.287Z"}, "0.52.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.52.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.5.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x", "source-map": "0.1.x"}, "devDependencies": {"should": "2.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "_id": "stylus@0.52.0", "_shasum": "46cdcc0d3a12b703a1c459464b0fc8921c48dd00", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "dist": {"shasum": "46cdcc0d3a12b703a1c459464b0fc8921c48dd00", "size": 88159, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.52.0.tgz", "integrity": "sha512-QYDZxV4qp0+cv4IBsnuifelsBYls34/dVSLp4nxbtZ4pBGQGzRye9zKdBtztrHlCXuh2tNjABPAhb4E9OrTQaw=="}, "publish_time": 1437326807395, "_hasShrinkwrap": false, "_cnpm_publish_time": 1437326807395, "_cnpmcore_publish_time": "2021-12-13T13:29:02.853Z"}, "0.52.0-alpha": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.52.0-alpha", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x", "source-map": "0.1.x"}, "devDependencies": {"should": "2.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "_id": "stylus@0.52.0-alpha", "_shasum": "811bd7862602de3ae620fe7520fb6f24ddd3ddc2", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "dist": {"shasum": "811bd7862602de3ae620fe7520fb6f24ddd3ddc2", "size": 87936, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.52.0-alpha.tgz", "integrity": "sha512-BW709qNUq32HfLQM+2j8/OAfZzld9ST74nNh2iT95NZBS+uh+IuGaDwEKjFQIp/84drQnxS9wgN2kldqdNWaew=="}, "publish_time": 1436967069120, "_hasShrinkwrap": false, "_cnpm_publish_time": 1436967069120, "_cnpmcore_publish_time": "2021-12-13T13:29:03.344Z"}, "0.51.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.51.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x", "source-map": "0.1.x"}, "devDependencies": {"should": "2.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "_id": "stylus@0.51.1", "_shasum": "d75c405c1d87d5e00ca5758d311cd79360f0fb99", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "dist": {"shasum": "d75c405c1d87d5e00ca5758d311cd79360f0fb99", "size": 87909, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.51.1.tgz", "integrity": "sha512-PVtonRerc0BBjcDga8EM6g2Fsp6gcj4LzR8TjuQSd6+yjz63Ik+KhsrjjagO+HdN9/Acoi9FQMvB//IjDwEWhg=="}, "publish_time": 1430206128258, "_hasShrinkwrap": false, "_cnpm_publish_time": 1430206128258, "_cnpmcore_publish_time": "2021-12-13T13:29:03.831Z"}, "0.51.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.51.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x", "source-map": "0.1.x"}, "devDependencies": {"should": "2.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "_id": "stylus@0.51.0", "_shasum": "1c7466ec305ef0141ba9980a2fc7d2b0b76d48e6", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "dist": {"shasum": "1c7466ec305ef0141ba9980a2fc7d2b0b76d48e6", "size": 87798, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.51.0.tgz", "integrity": "sha512-ndFueqdVhg+U5gDY/6xI+RV8Ygj6DQO+29AHrxl/pnRnPKL00zofmXDsF776SvIT+ZgG2SQ/y1kZybSNTE8zZg=="}, "publish_time": 1429798009706, "_hasShrinkwrap": false, "_cnpm_publish_time": 1429798009706, "_cnpmcore_publish_time": "2021-12-13T13:29:04.503Z"}, "0.50.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.50.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x", "source-map": "0.1.x"}, "devDependencies": {"should": "2.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "homepage": "https://github.com/LearnBoost/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "_id": "stylus@0.50.0", "_shasum": "2391f0df1ce1dde55a5a8df26b6906a9425ced05", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "dist": {"shasum": "2391f0df1ce1dde55a5a8df26b6906a9425ced05", "size": 87727, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.50.0.tgz", "integrity": "sha512-dSlDuUxGknqfsIvnK1XeMMYaurIDsUPC3YVw83mEiAij3TxmQr7pZInM53khp8YkZLfpx24zNYKjweQYpo+2yw=="}, "publish_time": 1423126074130, "_hasShrinkwrap": false, "_cnpm_publish_time": 1423126074130, "_cnpmcore_publish_time": "2021-12-13T13:29:05.069Z"}, "0.49.3": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.49.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x", "source-map": "0.1.x"}, "devDependencies": {"should": "2.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "homepage": "https://github.com/LearnBoost/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "_id": "stylus@0.49.3", "_shasum": "1fbdabe479ed460872c71a6252a67f95040ba511", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "dist": {"shasum": "1fbdabe479ed460872c71a6252a67f95040ba511", "size": 86650, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.49.3.tgz", "integrity": "sha512-HPoTajgP1PNFFXW7IjTS10XiY6TMlRe3aippyI9npFMjuZLAdzMSqnrKC2OfiBPLsROhazOlkYIvN73HhX1cVg=="}, "publish_time": 1415232751572, "_hasShrinkwrap": false, "_cnpm_publish_time": 1415232751572, "_cnpmcore_publish_time": "2021-12-13T13:29:05.667Z"}, "0.49.2": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.49.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x", "source-map": "0.1.x"}, "devDependencies": {"should": "2.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "homepage": "https://github.com/LearnBoost/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "_id": "stylus@0.49.2", "_shasum": "c72a9ea9d904d24bb07c8fd609e6abc28620000a", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "dist": {"shasum": "c72a9ea9d904d24bb07c8fd609e6abc28620000a", "size": 86563, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.49.2.tgz", "integrity": "sha512-2tkKOIbqHR+4ZCXFVVSJp9Ha1RoJdTkqZft70M1Y/ZnP11Vf6siSmdAZPY9t4YCVbC6KNooZjLojh+hiZauU/Q=="}, "publish_time": 1413288509597, "_hasShrinkwrap": false, "_cnpm_publish_time": 1413288509597, "_cnpmcore_publish_time": "2021-12-13T13:29:06.237Z"}, "0.49.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.49.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x", "source-map": "0.1.x"}, "devDependencies": {"should": "2.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "homepage": "https://github.com/LearnBoost/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "_id": "stylus@0.49.1", "_shasum": "c915495675ca77847a7d4285dd6d6572bae34448", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "dist": {"shasum": "c915495675ca77847a7d4285dd6d6572bae34448", "size": 86342, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.49.1.tgz", "integrity": "sha512-9QseIO3I0piYooYcbCV+cuQZiOPf5TRpF0t8id+gycPeSq7qDr4DJofvbay8DZDh7wamlTqzxywSxqcF3U3IcA=="}, "publish_time": 1411552485087, "_hasShrinkwrap": false, "_cnpm_publish_time": 1411552485087, "_cnpmcore_publish_time": "2021-12-13T13:29:06.890Z"}, "0.49.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.49.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x", "source-map": "0.1.x"}, "devDependencies": {"should": "2.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "homepage": "https://github.com/LearnBoost/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "_id": "stylus@0.49.0", "_shasum": "d1b03b319f59acface0e489e6424a155b05fbc63", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "dist": {"shasum": "d1b03b319f59acface0e489e6424a155b05fbc63", "size": 86293, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.49.0.tgz", "integrity": "sha512-NdKmzdW0obNXnpeRScwe/DMNl4E4JbabfCnwwu0Qp+4l3RaybQAHMjFa3NA5igrbhWKE3pigUhPxo8hWpO2ZeQ=="}, "publish_time": 1411367998683, "_hasShrinkwrap": false, "_cnpm_publish_time": 1411367998683, "_cnpmcore_publish_time": "2021-12-13T13:29:07.514Z"}, "0.48.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.48.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x", "source-map": "0.1.x"}, "devDependencies": {"should": "2.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "homepage": "https://github.com/LearnBoost/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "_id": "stylus@0.48.1", "_shasum": "612c1b8806ce2c35f37cd3a57ad932c883f7ff10", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "dist": {"shasum": "612c1b8806ce2c35f37cd3a57ad932c883f7ff10", "size": 84718, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.48.1.tgz", "integrity": "sha512-U1+gtuQPLgBoqvte6DUkYx0vEfNDszzE9FDq+JphE7F5FZDbx8amp1CJT0PnHV+odcArOP37ZuHJzr9XREUq1A=="}, "publish_time": 1408620449305, "_hasShrinkwrap": false, "_cnpm_publish_time": 1408620449305, "_cnpmcore_publish_time": "2021-12-13T13:29:08.280Z"}, "0.48.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.48.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x", "source-map": "0.1.x"}, "devDependencies": {"should": "2.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "homepage": "https://github.com/LearnBoost/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "_id": "stylus@0.48.0", "_shasum": "a8de8341b1cd89efb9161050bf87a72d65485795", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "dist": {"shasum": "a8de8341b1cd89efb9161050bf87a72d65485795", "size": 84689, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.48.0.tgz", "integrity": "sha512-SlqWSo252UkFVsg8jQ9BEfFwPzbOtcwnvaaMR8IvckspJ1umQFpKD7q0BZPo3R3Uvusqsh635H/s9pyTalGLrA=="}, "publish_time": 1408559300224, "_hasShrinkwrap": false, "_cnpm_publish_time": 1408559300224, "_cnpmcore_publish_time": "2021-12-13T13:29:08.949Z"}, "0.47.3": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.47.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x"}, "devDependencies": {"should": "2.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "homepage": "https://github.com/LearnBoost/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "_id": "stylus@0.47.3", "dist": {"shasum": "1bd905c6701f80653be1fd15d14807cc462be9f5", "size": 81629, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.47.3.tgz", "integrity": "sha512-zRzjghf+l0KRPe/l/fJ4d+yA/h0XiulucE3WjzuzF1KY72MwoBNwhbH9OnWgzQQ4mjvwy5LkeMyav5Nqjcfm/w=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "publish_time": 1406034576577, "_hasShrinkwrap": false, "_cnpm_publish_time": 1406034576577, "_cnpmcore_publish_time": "2021-12-13T13:29:09.720Z"}, "0.47.2": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.47.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x"}, "devDependencies": {"should": "2.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "homepage": "https://github.com/LearnBoost/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "_id": "stylus@0.47.2", "dist": {"shasum": "41267ec953d2cec34f71b2aed899218ca913cf58", "size": 81696, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.47.2.tgz", "integrity": "sha512-aSM92mvO3d0HMZmF5/itoqGsrqcjY43jedaTRTWKT37ELQaElkfinNeQz0Na6QZSkp+ewsinfkSa1gMxFwKtGg=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "publish_time": 1405775933466, "_hasShrinkwrap": false, "_cnpm_publish_time": 1405775933466, "_cnpmcore_publish_time": "2021-12-13T13:29:10.572Z"}, "0.47.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.47.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x"}, "devDependencies": {"should": "2.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "homepage": "https://github.com/LearnBoost/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "_id": "stylus@0.47.1", "dist": {"shasum": "2822873555f8dcf920a4cad5b9e2813c4de68f6a", "size": 81584, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.47.1.tgz", "integrity": "sha512-h5u5mbC19tQZMP818UhYPs4IlqHaXR7eUHZpQYUkoVQeFiLyImEtf/GzeUavS/LieXj2rvPddppr6OA4qCW1rw=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "publish_time": 1404318518051, "_hasShrinkwrap": false, "_cnpm_publish_time": 1404318518051, "_cnpmcore_publish_time": "2021-12-13T13:29:11.318Z"}, "0.47.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.47.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x"}, "devDependencies": {"should": "2.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "homepage": "https://github.com/LearnBoost/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "_id": "stylus@0.47.0", "dist": {"shasum": "8cbffb7fd9453799d9864f0f27b0d14722f0647f", "size": 81539, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.47.0.tgz", "integrity": "sha512-w95B4vTA15u1roLqnGUMeTAGTl2XxcVwTfSTJ35LrC7+leb8p6xiS9wtd+Awg/Zu+jv71jFBQJCTDDILgSOffQ=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "publish_time": 1404235812731, "_hasShrinkwrap": false, "_cnpm_publish_time": 1404235812731, "_cnpmcore_publish_time": "2021-12-13T13:29:12.060Z"}, "0.46.3": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.46.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x"}, "devDependencies": {"should": "2.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "_id": "stylus@0.46.3", "dist": {"shasum": "0bb5d602f63fa8088ad3f8affba2c1eb925c4d70", "size": 78373, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.46.3.tgz", "integrity": "sha512-iMcg/luUhMUAdXn3hb0lPgE1Qd/s4uTiub/7mKsa/5utazrGq5V6LrzLFu1KHgjqSAw2VPn1e6UfY5fPql31Vw=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1402328842699, "_hasShrinkwrap": false, "_cnpm_publish_time": 1402328842699, "_cnpmcore_publish_time": "2021-12-13T13:29:12.829Z"}, "0.46.2": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.46.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x"}, "devDependencies": {"should": "2.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "_id": "stylus@0.46.2", "dist": {"shasum": "9e6723aac7b582a9de6a0d1eacef20967c02e70b", "size": 78485, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.46.2.tgz", "integrity": "sha512-rODJI/FWxu41vsIwPcV9ghhBR7y8CWMSWkY5bRJ+0cUErEiU/PrceZBmLXuLoCm0g0AeKIvCWfV8VBYW1tc5yw=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1401866228661, "_hasShrinkwrap": false, "_cnpm_publish_time": 1401866228661, "_cnpmcore_publish_time": "2021-12-13T13:29:13.581Z"}, "0.46.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.46.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x"}, "devDependencies": {"should": "2.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "_id": "stylus@0.46.1", "dist": {"shasum": "72abb57e674056f073e134793192a619c4f46b5c", "size": 78459, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.46.1.tgz", "integrity": "sha512-S99VfYQhMgTcz2qQzzLA4jKv5vBG/dUmOgp/0t0XIVETnhCc8ziplzFHXdFc/Nj9LfjQSE9EJVVdkMb1t7RxCw=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1401831935568, "_hasShrinkwrap": false, "_cnpm_publish_time": 1401831935568, "_cnpmcore_publish_time": "2021-12-13T13:29:14.312Z"}, "0.46.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.46.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x"}, "devDependencies": {"should": "2.x", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "_id": "stylus@0.46.0", "dist": {"shasum": "44483046a4051007e56406e4306aa8ebd5ea33b8", "size": 78432, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.46.0.tgz", "integrity": "sha512-4svKA1Q5NVg8VZ21VfLDDw3BpK4LtyEmZo4Ln2gf/QCtRH36dmhvbgdYZV97gYsxeqbmo/lwHm/rFWaa4BiIwQ=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1401784412333, "_hasShrinkwrap": false, "_cnpm_publish_time": 1401784412333, "_cnpmcore_publish_time": "2021-12-13T13:29:15.109Z"}, "0.45.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.45.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x"}, "devDependencies": {"should": "*", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "homepage": "https://github.com/LearnBoost/stylus", "_id": "stylus@0.45.1", "_shasum": "ef69f62c984a480adc0c9d4aaafb2382a389e453", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "dist": {"shasum": "ef69f62c984a480adc0c9d4aaafb2382a389e453", "size": 78044, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.45.1.tgz", "integrity": "sha512-hESfrPjsbRdLp6bQd5wmv4STeZbzgzGb2KpU3z6vkZCc5sa+TfjaKc6QBA6E21eDAH2wZJdAmw5e66xeASGl5g=="}, "directories": {}, "publish_time": 1400199892933, "_hasShrinkwrap": false, "_cnpm_publish_time": 1400199892933, "_cnpmcore_publish_time": "2021-12-13T13:29:15.971Z"}, "0.45.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.45.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x"}, "devDependencies": {"should": "*", "mocha": "*", "jscoverage": "0.3.8"}, "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "homepage": "https://github.com/LearnBoost/stylus", "_id": "stylus@0.45.0", "_shasum": "e6c6ea624a14332931fc7c6fc0219b8cbde9fa76", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "dist": {"shasum": "e6c6ea624a14332931fc7c6fc0219b8cbde9fa76", "size": 78420, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.45.0.tgz", "integrity": "sha512-cNZKmnChEdXfXwWlQq6qOI3GFTh6mL8mOYwekLUL5FdFXjE5pESVzSYCOGGYzj/l05d9wM1J4/gHjbfKzucIEg=="}, "directories": {}, "publish_time": 1399836026505, "_hasShrinkwrap": false, "_cnpm_publish_time": 1399836026505, "_cnpmcore_publish_time": "2021-12-13T13:29:16.833Z"}, "0.44.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.44.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "homepage": "https://github.com/LearnBoost/stylus", "_id": "stylus@0.44.0", "dist": {"shasum": "c6544d75290377986822b5aeb71899dbf756fd72", "size": 76752, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.44.0.tgz", "integrity": "sha512-g5lno2VktKDwGvjTw/iGswS2m/844bxtJCL1CcKJybWRckTtnBB6fYI+BegXsJVoA5wuu7FzwRdZZA7PJiZkpQ=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1398254474802, "_hasShrinkwrap": false, "_cnpm_publish_time": 1398254474802, "_cnpmcore_publish_time": "2021-12-13T13:29:17.624Z"}, "0.44.0-beta3": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.44.0-beta3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x", "circular-json": "0.1.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "homepage": "https://github.com/LearnBoost/stylus", "_id": "stylus@0.44.0-beta3", "dist": {"shasum": "a602b30f57f1692cf0f1fb664e60a14e26102cff", "size": 75528, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.44.0-beta3.tgz", "integrity": "sha512-wfBIU275/ZkqcVWYOOEMck2FCn2DYuBOJ/kfKUeVxZIJpVvfx16pdd6KFyHNNC0zvyDep0KQ3ZljzzdaMSs1vA=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1397455557568, "_hasShrinkwrap": false, "_cnpm_publish_time": 1397455557568, "_cnpmcore_publish_time": "2021-12-13T13:29:18.356Z"}, "0.44.0-beta2": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.44.0-beta2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x", "circular-json": "0.1.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "homepage": "https://github.com/LearnBoost/stylus", "_id": "stylus@0.44.0-beta2", "dist": {"shasum": "fdfa244abf38d1d077c6a4fe729bb20ee928f513", "size": 75509, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.44.0-beta2.tgz", "integrity": "sha512-Y+sisIeUf9RGsoE+qG8ILjthNnhWjDUvjMEEnAQGzgAnUs1aQ98r3Z5q3PZy40zDbBNvWtVhTVdhIBX4LCRdkw=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1396876691849, "_hasShrinkwrap": false, "_cnpm_publish_time": 1396876691849, "_cnpmcore_publish_time": "2021-12-13T13:29:19.266Z"}, "0.43.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.43.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "homepage": "https://github.com/LearnBoost/stylus", "_id": "stylus@0.43.1", "dist": {"shasum": "8ebff63d41aba631d6451cea5618c7e982891da9", "size": 73940, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.43.1.tgz", "integrity": "sha512-sryVUx1q7FJTUSVLGxpCQ9af+5hPL/wsfD6MJfLnx4Ksy6Dnfe6YaLtak5PiVlimKLOfU0VklIlbxSNijijwpQ=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1396872024030, "_hasShrinkwrap": false, "_cnpm_publish_time": 1396872024030, "_cnpmcore_publish_time": "2021-12-13T13:29:20.071Z"}, "0.44.0-beta": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.44.0-beta", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x", "circular-json": "0.1.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "homepage": "https://github.com/LearnBoost/stylus", "_id": "stylus@0.44.0-beta", "dist": {"shasum": "a4ab58e8f0b973e1561d023494a0847b0b25d5f4", "size": 75522, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.44.0-beta.tgz", "integrity": "sha512-cZo/n8qW/kjtfFSIQPPvQCpeVvOhCdkCVUnZeyB0Cnj2t3VxMC4MKju4OPsa46aSWs6jKVtRTvM6gSE8eMTz3w=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1396806502179, "_hasShrinkwrap": false, "_cnpm_publish_time": 1396806502179, "_cnpmcore_publish_time": "2021-12-13T13:29:21.013Z"}, "0.43.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.43.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "homepage": "https://github.com/LearnBoost/stylus", "_id": "stylus@0.43.0", "dist": {"shasum": "1d59cbd9d2bc90dc54e3e61de24234710c8cf43f", "size": 73923, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.43.0.tgz", "integrity": "sha512-tX2kl2MuDCOkCDqzF9aOx5BUYrk/wysTGfz3PwjeWwB6xza0p869dAOF2O2iUBU5QqnQIsSyVgRrIKTe5sgFiQ=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1396725552929, "_hasShrinkwrap": false, "_cnpm_publish_time": 1396725552929, "_cnpmcore_publish_time": "2021-12-13T13:29:21.898Z"}, "0.42.3": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.42.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "_id": "stylus@0.42.3", "dist": {"shasum": "23e8b3e71eb7f60bc4a657de498c8feb9c366c32", "size": 70825, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.42.3.tgz", "integrity": "sha512-1eoVC0Nf3daJHrNf0xW7CrFBXBfx2MoDIeHe5EIzeqfc0dTWCjDKFMWzVcy7pMl7PQTjYVX3refUPIOFbcEw0Q=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1393827858211, "_hasShrinkwrap": false, "_cnpm_publish_time": 1393827858211, "_cnpmcore_publish_time": "2021-12-13T13:29:22.859Z"}, "0.42.2": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.42.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "_id": "stylus@0.42.2", "dist": {"shasum": "bed29107803129bed1983efc4c7e33f4fd34fee7", "size": 192760, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.42.2.tgz", "integrity": "sha512-pB4+/jtRyNyfeMNRNLwy9uSkcROupmv8vLF+Z58gg3Hvh2soryxeMOittCYynGjUa2J72WwtZLlymHkYRGr1HQ=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1391082268411, "_hasShrinkwrap": false, "_cnpm_publish_time": 1391082268411, "_cnpmcore_publish_time": "2021-12-13T13:29:23.914Z"}, "0.42.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.42.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "readmeFilename": "Readme.md", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "_id": "stylus@0.42.1", "dist": {"shasum": "8e5bfec922e8d0037748cd05281d53ca602b6e4f", "size": 192758, "noattachment": false, "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.42.1.tgz", "integrity": "sha512-Vjr9zeNY6OSQzf2hYPF5mZeCOJqpGkP3SGc1ZpQqRodSG4P35hml0nURxOeYrSs8kS3B5lOyAzeVzNXNg/paMQ=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1390075659831, "_hasShrinkwrap": false, "_cnpm_publish_time": 1390075659831, "_cnpmcore_publish_time": "2021-12-13T13:29:24.931Z"}, "0.42.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.42.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"css-parse": "1.7.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x", "glob": "3.2.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "readmeFilename": "Readme.md", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "_id": "stylus@0.42.0", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.42.0.tgz", "shasum": "5dc4d50dd205dbb7e6af6bf7f19546da24148e44", "size": 70192, "noattachment": false, "integrity": "sha512-8/EdUPr45fomGeDF/KRKekE8MwaPHYE+UIiCosmGtSHKw169PqyzDVY5s6uub8cdH0BSyYcZ00XzbC8azVIg7g=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1389036313708, "_hasShrinkwrap": false, "_cnpm_publish_time": 1389036313708, "_cnpmcore_publish_time": "2021-12-13T13:29:25.942Z"}, "0.41.3": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.41.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "readmeFilename": "Readme.md", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "_id": "stylus@0.41.3", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.41.3.tgz", "shasum": "0a49c3f2f4f6b6719e7cca823052b9df87acaeb7", "size": 67988, "noattachment": false, "integrity": "sha512-fJGtHOBOsq1qY2Lv1BiJOfDk+iXoO0sKDCCDIX1VmU3vJ1ObVHjuTqBGiihR5uBb4Y9IvyMqZw3r+Tkf90kdSg=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1386874066524, "_hasShrinkwrap": false, "_cnpm_publish_time": 1386874066524, "_cnpmcore_publish_time": "2021-12-13T13:29:26.925Z"}, "0.41.2": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.41.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "readmeFilename": "Readme.md", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "_id": "stylus@0.41.2", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.41.2.tgz", "shasum": "68bca2b317d32c85e7cdf7afe7ee044da9afe983", "size": 68009, "noattachment": false, "integrity": "sha512-13Yr1eAHNioVcknfTSFULX4y0tG3OEz+6uC9ct7Av5zRyU5JNO70Kxb5HT91fzK61kG8O35GsvXvpgGckbbo8A=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1386668620593, "_hasShrinkwrap": false, "_cnpm_publish_time": 1386668620593, "_cnpmcore_publish_time": "2021-12-13T13:29:27.861Z"}, "0.41.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.41.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "readmeFilename": "Readme.md", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "_id": "stylus@0.41.1", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.41.1.tgz", "shasum": "6737ccec7e397b913a54996d024ce00a9ac13185", "size": 68010, "noattachment": false, "integrity": "sha512-DkVwaqr78AbqHRvoPielLyjFR/b9Kv49Hivle8iz4Nyu0TXpXH2boRZcC/umhwBdVap3ZQTIqzu7xE3T6i6IPQ=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1386532075088, "_hasShrinkwrap": false, "_cnpm_publish_time": 1386532075088, "_cnpmcore_publish_time": "2021-12-13T13:29:28.772Z"}, "0.41.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.41.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "readmeFilename": "Readme.md", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "_id": "stylus@0.41.0", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.41.0.tgz", "shasum": "b9609d5663c9624d11b28952f3e6b23cde10ac28", "size": 67774, "noattachment": false, "integrity": "sha512-W+1UEXUCGb5Aew9LRWsc0zAvbwnyXO8yLyn2X8d2nTyzQRHLCP6AXkplAIC2bL2KhIRsiPn7EHYjLTQBxjdVaw=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1385838319877, "_hasShrinkwrap": false, "_cnpm_publish_time": 1385838319877, "_cnpmcore_publish_time": "2021-12-13T13:29:29.640Z"}, "0.40.3": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.40.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "readmeFilename": "Readme.md", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "_id": "stylus@0.40.3", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.40.3.tgz", "shasum": "17e566274ebd6e56f23a8928fa38626c4260ded3", "size": 66729, "noattachment": false, "integrity": "sha512-ryduhCPEb8aEArV/ofe8BBTU1ibLeb4ytqBjG9iyLsrTn3X8uQp3CNMLu7/1TnOHge5/pBO2sC+Ej7tIiCKpqw=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1384620013825, "_hasShrinkwrap": false, "_cnpm_publish_time": 1384620013825, "_cnpmcore_publish_time": "2021-12-13T13:29:30.725Z"}, "0.40.2": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.40.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "readmeFilename": "Readme.md", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "_id": "stylus@0.40.2", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.40.2.tgz", "shasum": "4b4f7a2d8a6aeecfe0a62ef0e2d8a45a78b1e2bd", "size": 66660, "noattachment": false, "integrity": "sha512-G4XsxOsoilMEx1UXeVyfmXU2SbGrWUCUQ0LdA55YePujkIshySn9dCeGZEN3YzNgObDQ07rF8gF6uljAK+pF2g=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1384261602851, "_hasShrinkwrap": false, "_cnpm_publish_time": 1384261602851, "_cnpmcore_publish_time": "2021-12-13T13:29:31.785Z"}, "0.40.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.40.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "readmeFilename": "Readme.md", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "_id": "stylus@0.40.1", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.40.1.tgz", "shasum": "6b7508b1e22710a8ed9e483da871ac0bdb4a01ea", "size": 66754, "noattachment": false, "integrity": "sha512-MzNS0hKBKQFc4kd6l84xWWk2bWGMfkBZzTOuTE4o8qPWHKadX3ULXWlqmMUhn5FM2DeCxqhzELnVMUvoc7jFbA=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1384254494379, "_hasShrinkwrap": false, "_cnpm_publish_time": 1384254494379, "_cnpmcore_publish_time": "2021-12-13T13:29:32.736Z"}, "0.40.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.40.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "readmeFilename": "Readme.md", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "_id": "stylus@0.40.0", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.40.0.tgz", "shasum": "497b6242f3952417482acad65627a6988125b634", "size": 66603, "noattachment": false, "integrity": "sha512-wzHV45kpjEQpghbxIvfVBjPaf8OglxLSW8FyMxYDONgbXTQl0O+/yMje+nAfDc1NngTJwsBbfgfv0UpKOLstjQ=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1383637045321, "_hasShrinkwrap": false, "_cnpm_publish_time": 1383637045321, "_cnpmcore_publish_time": "2021-12-13T13:29:33.691Z"}, "0.39.4": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.39.4", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "readmeFilename": "Readme.md", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "_id": "stylus@0.39.4", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.39.4.tgz", "shasum": "a062854b51497675bb246f976d68b61c8297d6c7", "size": 66349, "noattachment": false, "integrity": "sha512-XUBXIY+77H8qCpqXjoaj/prVVj2E23gxEK3X0kNvhuwGq/Bma1NtQcTmyj3yPruQMXlk2l580yZPuouWx1i9Fw=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1383509962960, "_hasShrinkwrap": false, "_cnpm_publish_time": 1383509962960, "_cnpmcore_publish_time": "2021-12-13T13:29:34.857Z"}, "0.39.3": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.39.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "readmeFilename": "Readme.md", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "_id": "stylus@0.39.3", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.39.3.tgz", "shasum": "45af2cb47935f1736a7ef905b86508309c6ad16a", "size": 66334, "noattachment": false, "integrity": "sha512-t3CXwroz9jEMOm/ynat95xf+lUo9L0YmAzi16P8lHRGHP3ze63euiX8zkqWfE26fB5ot55mU2OIjZi2dJZl4Bg=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1383309448893, "_hasShrinkwrap": false, "_cnpm_publish_time": 1383309448893, "_cnpmcore_publish_time": "2021-12-13T13:29:36.134Z"}, "0.39.2": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.39.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "readmeFilename": "Readme.md", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "_id": "stylus@0.39.2", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.39.2.tgz", "shasum": "af39ca47ad666314ba3171155499826f5b387c90", "size": 66346, "noattachment": false, "integrity": "sha512-zqLiNmDyPcnaS56QepzG79Mn49rF36H2e6xAspkGEvAusGIq82l0b8a2Fk3odmvHzYn9LJ2tAZf792RQbsESOQ=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1383204518811, "_hasShrinkwrap": false, "_cnpm_publish_time": 1383204518811, "_cnpmcore_publish_time": "2021-12-13T13:29:37.272Z"}, "0.39.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.39.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "readmeFilename": "Readme.md", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "_id": "stylus@0.39.1", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.39.1.tgz", "shasum": "3eec9681781b483fd8b133a89b9fdcad6a3f1c6b", "size": 66237, "noattachment": false, "integrity": "sha512-E0Fo4M8eB464rm1lOyC4esf2tMqUQrGaWOG9lOwji7ImvSjP6/lYbTlrsJ8B8+AYJPhTK4yOZc5ErUzlF1YWZw=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1383147643748, "_hasShrinkwrap": false, "_cnpm_publish_time": 1383147643748, "_cnpmcore_publish_time": "2021-12-13T13:29:38.333Z"}, "0.39.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.39.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "readmeFilename": "Readme.md", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "_id": "stylus@0.39.0", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.39.0.tgz", "shasum": "d2f3665bb4aa12a10e10fb04936d2ee80deda544", "size": 66149, "noattachment": false, "integrity": "sha512-KvHJSbwFbmhpzW+k8EiQsvpWJxrTYEY1pd39z7VglLYY+Y/lGwtFDtA7QfVHDvZApuaQ/WlYqQesxehGA40Bzg=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1383111617857, "_hasShrinkwrap": false, "_cnpm_publish_time": 1383111617857, "_cnpmcore_publish_time": "2021-12-13T13:29:39.502Z"}, "0.38.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.38.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "readmeFilename": "Readme.md", "bugs": {"url": "https://github.com/LearnBoost/stylus/issues"}, "_id": "stylus@0.38.0", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.38.0.tgz", "shasum": "6bd0581db0ee0491251639d338685f7232ca0610", "size": 64294, "noattachment": false, "integrity": "sha512-+LZa5qjRMlrM3QbUMC9FXiudhEYhZVazXc9gXxSPIAl8nBXUoVrlG282IAxVNv4KC3DtSpW7X7lUMsT8vP/CZw=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1380028482222, "_hasShrinkwrap": false, "_cnpm_publish_time": 1380028482222, "_cnpmcore_publish_time": "2021-12-13T13:29:40.667Z"}, "0.37.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.37.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "_id": "stylus@0.37.0", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.37.0.tgz", "shasum": "3f18ba693960e3408e2951b4a22e337c33d239d8", "size": 58138, "noattachment": false, "integrity": "sha512-OUxmQGwyVdjW2p+Xtz1zNlevRhgDVhmStG/95Zhf3MLnx+vBLaMZq/X8D9/ZLjVSk3D0vT/dlIPCy0mnH3Hh8g=="}, "_npmVersion": "1.1.61", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1376856253997, "_hasShrinkwrap": false, "_cnpm_publish_time": 1376856253997, "_cnpmcore_publish_time": "2021-12-13T13:29:41.666Z"}, "0.36.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.36.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "readmeFilename": "Readme.md", "_id": "stylus@0.36.1", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.36.1.tgz", "shasum": "64e493933eb5f7347e941b37046f5ba533d3d262", "size": 57710, "noattachment": false, "integrity": "sha512-2sww6SLdwl0qvoxu+tDcaF+xVyZNqx9wrTMRmgEMBTujOPbVoYHwNmEnpt8MbjoklUNYtnKglLNtFkrSMzjfiQ=="}, "_from": ".", "_npmVersion": "1.2.11", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1375786595447, "_hasShrinkwrap": false, "_cnpm_publish_time": 1375786595447, "_cnpmcore_publish_time": "2021-12-13T13:29:42.817Z"}, "0.36.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.36.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "readmeFilename": "Readme.md", "_id": "stylus@0.36.0", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.36.0.tgz", "shasum": "733e5ed75336c386da42209eda6f4ec814360789", "size": 57690, "noattachment": false, "integrity": "sha512-ispkGa+zAtSDXr8KRYIqzWzuvedre/ZgnbRzxRA/5NYMSAyTc6OsBPl9Wlp1MBvTSPFeoteo0qmpkMqKlSsXUg=="}, "_from": ".", "_npmVersion": "1.2.11", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1375373585798, "_hasShrinkwrap": false, "_cnpm_publish_time": 1375373585798, "_cnpmcore_publish_time": "2021-12-13T13:29:44.225Z"}, "0.35.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.35.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "_id": "stylus@0.35.1", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.35.1.tgz", "shasum": "ed4f4cf378c2f883b04633f01e5c76ffe976084f", "size": 56903, "noattachment": false, "integrity": "sha512-3uU5tf7b3AsbgXoGfaPoj3DeqNbCgMhX5o1iMTRFizGM7vP8T27DeWomfOfHgzjPS/t/cSdcsW5RifGtUjHXdQ=="}, "_npmVersion": "1.1.61", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1375119550995, "_hasShrinkwrap": false, "_cnpm_publish_time": 1375119550995, "_cnpmcore_publish_time": "2021-12-13T13:29:45.500Z"}, "0.35.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.35.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*", "sax": "0.5.x"}, "devDependencies": {"should": "*", "mocha": "*"}, "_id": "stylus@0.35.0", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.35.0.tgz", "shasum": "4ecc12e592bc6e04b381f72ee4c2c7a7b96320c7", "size": 56870, "noattachment": false, "integrity": "sha512-lVnB+ekv3DkiTXmkfXco/Tlw7P4+x5gj1KQZSXMktNtpBd3ZWmudmU6zIRSf8sQJFXAubx5HYf5mdkZYx1EqeQ=="}, "_npmVersion": "1.1.61", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1375047166974, "_hasShrinkwrap": false, "_cnpm_publish_time": 1375047166974, "_cnpmcore_publish_time": "2021-12-13T13:29:46.784Z"}, "0.34.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.34.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*"}, "devDependencies": {"should": "*", "mocha": "*"}, "readmeFilename": "Readme.md", "_id": "stylus@0.34.1", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.34.1.tgz", "shasum": "937d8502a3be4e617d5ad493f204c70a93d95b14", "size": 55342, "noattachment": false, "integrity": "sha512-iN9lVTf04KoMydURjWoTcB6s5d5qtP7yfOLdHrffiD6aIuqGUqKyO+CsH/NCN+8a3cKn5d8IUHzk81hNoMlrtw=="}, "_from": ".", "_npmVersion": "1.2.11", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1373636418904, "_hasShrinkwrap": false, "_cnpm_publish_time": 1373636418904, "_cnpmcore_publish_time": "2021-12-13T13:29:47.957Z"}, "0.34.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.34.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*"}, "devDependencies": {"should": "*", "mocha": "*"}, "readmeFilename": "Readme.md", "_id": "stylus@0.34.0", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.34.0.tgz", "shasum": "1299d1ac3e3e34659437903a724f6aefaef2561e", "size": 56086, "noattachment": false, "integrity": "sha512-OqA8AbQQRrSMyxeRHZNOvVJbFp/prQWg80QRMqXw6otopuWR+65RX0vvrrcv+liEiqQzb7R8LJ5opvibJ6cFxA=="}, "_from": ".", "_npmVersion": "1.2.11", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1373624106757, "_hasShrinkwrap": false, "_cnpm_publish_time": 1373624106757, "_cnpmcore_publish_time": "2021-12-13T13:29:49.190Z"}, "0.33.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.33.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*"}, "devDependencies": {"should": "*", "mocha": "*"}, "_id": "stylus@0.33.1", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.33.1.tgz", "shasum": "10aad6776b66952304fb27e162f74de29a40ffa4", "size": 55519, "noattachment": false, "integrity": "sha512-+TMmKIpaP3CLtU98439R1xLxsVwVCfBKJPsuUtcrhBNbSa4I8ZUQaDsBfDrwxs5qjD8GcQAD1t9tfp7S5W0/nA=="}, "_npmVersion": "1.1.61", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1372611547546, "_hasShrinkwrap": false, "_cnpm_publish_time": 1372611547546, "_cnpmcore_publish_time": "2021-12-13T13:29:50.538Z"}, "0.33.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.33.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/LearnBoost/stylus"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*"}, "devDependencies": {"should": "*", "mocha": "*"}, "_id": "stylus@0.33.0", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.33.0.tgz", "shasum": "60fd9bb5eef822430402a03fa11cb86816add1ab", "size": 55508, "noattachment": false, "integrity": "sha512-6U02Ks7+3/cJUvhnRzF5e7SeUSc9VvbY1/7n7XMmbyq/uKENWai1afcerxBe64AEYFbS6/ie/mP6tLhDX/6a3w=="}, "_npmVersion": "1.1.61", "_npmUser": {"name": "kizu", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1372610843732, "_hasShrinkwrap": false, "_cnpm_publish_time": 1372610843732, "_cnpmcore_publish_time": "2021-12-13T13:29:51.800Z"}, "0.32.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.32.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus"}, "main": "./index.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*"}, "devDependencies": {"should": "*", "mocha": "*"}, "readmeFilename": "Readme.md", "_id": "stylus@0.32.1", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.32.1.tgz", "shasum": "3d28275bd9f181085f8dd0fa52a8559696978212", "size": 55240, "noattachment": false, "integrity": "sha512-QAUlDBLxjrNpf4dT991DOaXcxBKCJuICYAso29Q0RV9aI+REZIER4aRMfE5DIkV1meSiVccO27dA1Uhu+5gzEQ=="}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1362007375618, "_hasShrinkwrap": false, "_cnpm_publish_time": 1362007375618, "_cnpmcore_publish_time": "2021-12-13T13:29:53.078Z"}, "0.32.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.32.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus"}, "main": "./index.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*"}, "devDependencies": {"should": "*", "mocha": "*"}, "readmeFilename": "Readme.md", "_id": "stylus@0.32.0", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.32.0.tgz", "shasum": "ea1d489d2beb3b92d08c9bcd94dcac8e509762f7", "size": 54985, "noattachment": false, "integrity": "sha512-38ha6MVBpvwO1zjbfJIKIAMdegs8zoxFzFFfOGYQ8HV3aCymk4C6SaiL5QMHondKi2ZvwP1CUc2OZZGEkcV18Q=="}, "_npmVersion": "1.1.66", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1357317127278, "_hasShrinkwrap": false, "_cnpm_publish_time": 1357317127278, "_cnpmcore_publish_time": "2021-12-13T13:29:54.489Z"}, "0.31.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.31.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus"}, "main": "./index.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*"}, "devDependencies": {"should": "*", "mocha": "*"}, "readmeFilename": "Readme.md", "_id": "stylus@0.31.0", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.31.0.tgz", "shasum": "b4f807aa067f071ab707f2c8ae143ab3211e51b2", "size": 54894, "noattachment": false, "integrity": "sha512-BJ7iU9iUNOj/HlTuUF9SHh994ooIrOr/yYxzzQzpsweulT0HPyL4vtel9/QJcvq+xwy3ff7sf9Nv25kATr2qww=="}, "_npmVersion": "1.1.65", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1353780733619, "_hasShrinkwrap": false, "_cnpm_publish_time": 1353780733619, "_cnpmcore_publish_time": "2021-12-13T13:29:55.828Z"}, "0.30.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.30.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus"}, "main": "./index.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*"}, "devDependencies": {"should": "*", "mocha": "*"}, "_id": "stylus@0.30.1", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.30.1.tgz", "shasum": "8fce4fb3940f4556e1dca1c43ce8be2876c80325", "size": 55371, "noattachment": false, "integrity": "sha512-Ar293Pn/IyYacuNEA8kDe0/aBmBKUCN7cvxL6sSDM/fiscGdvXo2qypwLNtoAyp+UTOHJz2/049Z0Ej3FVpi2A=="}, "_npmVersion": "1.1.61", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1350499789394, "_hasShrinkwrap": false, "_cnpm_publish_time": 1350499789394, "_cnpmcore_publish_time": "2021-12-13T13:29:57.047Z"}, "0.30.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.30.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus"}, "main": "./index.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*"}, "devDependencies": {"should": "*", "mocha": "*"}, "_id": "stylus@0.30.0", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.30.0.tgz", "shasum": "17be0fe69c0690ea6d221988d6481a5fd7bf37fc", "size": 55351, "noattachment": false, "integrity": "sha512-hHrGqvaHNCoGvc3DLHc1Sf8RmQIfP+eDFFsfI3lpR35L2ot6EjwocZyzcIz2RWOAiRma0s8icwojJyfYv0uOyQ=="}, "_npmVersion": "1.1.61", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1350363582380, "_hasShrinkwrap": false, "_cnpm_publish_time": 1350363582380, "_cnpmcore_publish_time": "2021-12-13T13:29:58.377Z"}, "0.29.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.29.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus"}, "main": "./index.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*"}, "devDependencies": {"should": "*", "mocha": "*"}, "_id": "stylus@0.29.0", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.29.0.tgz", "shasum": "508a8d350a270e23e1900c20ac5604b9a190b611", "size": 54940, "noattachment": false, "integrity": "sha512-bvhqBONMi+nFyyjONXIs4OJygsxyY/bF6KjYNgz9SUZF7lnJOdBWCNOaXwOkFRcWrAp0vx00dSiwkGt2Muv7Dw=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1345046216327, "_hasShrinkwrap": false, "_cnpm_publish_time": 1345046216327, "_cnpmcore_publish_time": "2021-12-13T13:29:59.700Z"}, "0.28.2": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.28.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus"}, "main": "./index.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*"}, "devDependencies": {"should": "*", "mocha": "*"}, "_id": "stylus@0.28.2", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.28.2.tgz", "shasum": "d4c67c69e1b21e139143cdecf27f205b3e69e587", "size": 54167, "noattachment": false, "integrity": "sha512-nGord4MkvdV9Xo+I3cuHrhm+D7pP/S8el92jd7QGdewgV4Id/F/ji2rI9nhyRY9NA648npP7acuuvCaNGCF/Uw=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1342378160878, "_hasShrinkwrap": false, "_cnpm_publish_time": 1342378160878, "_cnpmcore_publish_time": "2021-12-13T13:30:00.885Z"}, "0.28.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.28.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus"}, "main": "./index.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*"}, "devDependencies": {"should": "*", "mocha": "*"}, "_id": "stylus@0.28.1", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.28.1.tgz", "shasum": "093d4a9d9508ecb33aac4eb802bca8a594733091", "size": 52209, "noattachment": false, "integrity": "sha512-weUCjC8Z5SmG7TXQ3I44aksIO9arGpgxiGzZES6NSMe9MoSI8QsWH8rdqlU6ouqCIg5I41h96dzWYPXg1zoNaw=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1341675697712, "_hasShrinkwrap": false, "_cnpm_publish_time": 1341675697712, "_cnpmcore_publish_time": "2021-12-13T13:30:02.126Z"}, "0.28.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.28.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus"}, "main": "./index.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*"}, "devDependencies": {"should": "*", "mocha": "*"}, "_id": "stylus@0.28.0", "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.28.0.tgz", "shasum": "8e82b4b1c70ad87aabe9c8992a5c1f68469c69d0", "size": 52219, "noattachment": false, "integrity": "sha512-9G8QY6aM5qZxKVjk0+viTt8Me9Aoxk624CiLen2ekON7RBvXMQ32U152cfJRSwSYFX/vkaxcNdDSZi5mqRWKKA=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1341617470125, "_hasShrinkwrap": false, "_cnpm_publish_time": 1341617470125, "_cnpmcore_publish_time": "2021-12-13T13:30:03.293Z"}, "0.27.2": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.27.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*"}, "devDependencies": {"should": "*", "mocha": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.27.2", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.19", "_nodeVersion": "v0.6.16", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.27.2.tgz", "shasum": "1121f7f8cd152b0f8a4aa6a24a9adea10c825117", "size": 51975, "noattachment": false, "integrity": "sha512-A09OMYaAcFsJK/Vk7ZBd6oVAQT3cckz5ErTBxhJinbkkJ0MFO788cgjwznA8tGKhAP6yswJci8+adWTalaJisw=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1340150192124, "_hasShrinkwrap": false, "_cnpm_publish_time": 1340150192124, "_cnpmcore_publish_time": "2021-12-13T13:30:04.683Z"}, "0.27.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.27.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*"}, "devDependencies": {"should": "*", "mocha": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.27.1", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0-3", "_nodeVersion": "v0.6.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.27.1.tgz", "shasum": "123b3a95255e423f5404826bfcff4ac1ca5be298", "size": 52141, "noattachment": false, "integrity": "sha512-OgZmsFr5mmVg9udhromDW84c9nzwyGA2in2LlmNBd+EPX8S4tnE8wqJCxa6a65sVIHEClKdWFAEjYlpNazn02A=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1338221635768, "_hasShrinkwrap": false, "_cnpm_publish_time": 1338221635768, "_cnpmcore_publish_time": "2021-12-13T13:30:06.099Z"}, "0.27.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.27.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*"}, "devDependencies": {"should": "*", "mocha": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.27.0", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0-3", "_nodeVersion": "v0.6.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.27.0.tgz", "shasum": "f0eff1158f4fcd484933cf0c9ac753f0f84f1219", "size": 51776, "noattachment": false, "integrity": "sha512-vNgjLltadUCtB/pGX1HCBdu1i0ZPXLkX7BsNIS+16wJAkGibb6CbA3RudBhLrZpTY4NFcVXmWSQaNc2M0FuJug=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1336676835837, "_hasShrinkwrap": false, "_cnpm_publish_time": 1336676835837, "_cnpmcore_publish_time": "2021-12-13T13:30:07.604Z"}, "0.26.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.26.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "*"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*"}, "devDependencies": {"should": "*", "mocha": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.26.1", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0-3", "_nodeVersion": "v0.6.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.26.1.tgz", "shasum": "9738d76f477d644c5fffee4367e7231de5bb0af5", "size": 53034, "noattachment": false, "integrity": "sha512-33J3iBM2Ueh/wDFzkQXmjHSDxNRWQ7J2I2dqiInAKkGR4j+3hkojRRSbv3ITodxJBIodVfv0l10CHZhJoi0Ubw=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1336407951580, "_hasShrinkwrap": false, "_cnpm_publish_time": 1336407951580, "_cnpmcore_publish_time": "2021-12-13T13:30:08.958Z"}, "0.26.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.26.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*"}, "devDependencies": {"should": "*", "mocha": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.26.0", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0-3", "_nodeVersion": "v0.6.9", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.26.0.tgz", "shasum": "cffddc827c091fdb278f03209e4dfe09e51e6eba", "size": 53124, "noattachment": false, "integrity": "sha512-IXH5ymwZXvCan/G+Rxg0aI93lJlYUvHXYhPOgUBrebzyZ2GJyh/XxpYipO1X8FhU5RqmhGcdQy0J2+UBQTVwMw=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1335548396982, "_hasShrinkwrap": false, "_cnpm_publish_time": 1335548396982, "_cnpmcore_publish_time": "2021-12-13T13:30:10.614Z"}, "0.25.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.25.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.x", "mkdirp": "0.3.x", "debug": "*"}, "devDependencies": {"should": "*", "mocha": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.25.0", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.9", "_nodeVersion": "v0.6.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.25.0.tgz", "shasum": "2ce3846bf5492f05156d25331073b7e5c8471bfa", "size": 52745, "noattachment": false, "integrity": "sha512-oVwh0haj9sqvx9amf+0lv8zkLN445sHvZFOruH1PK2VTNIxIQLKLo/L5lhP/SVYUGV8xLEoaMUKCdM6daBjEcw=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1333502974803, "_hasShrinkwrap": false, "_cnpm_publish_time": 1333502974803, "_cnpmcore_publish_time": "2021-12-13T13:30:12.158Z"}, "0.24.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.24.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "make test"}, "dependencies": {"cssom": "0.2.2", "growl": "1.4.x", "mkdirp": "0.3.x", "debug": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.24.0", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.6.6", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.24.0.tgz", "shasum": "bc3cdb7b1ec89ba5b50f785c30a295b46bb3558d", "size": 61440, "noattachment": false, "integrity": "sha512-ltQPJQ77Qu95sT22wtv6Eh9vbZPiTO//nRXQQLlmD/rvaiW/KMC1a8R9F9bA9Wt+m4dXDqh9moYG4lKrn5/u1A=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1329437178726, "_hasShrinkwrap": false, "_cnpm_publish_time": 1329437178726, "_cnpmcore_publish_time": "2021-12-13T13:30:13.628Z"}, "0.23.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.23.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.1", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.23.0", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.23.0.tgz", "shasum": "f87bd28a290defdd16a5e24d873a63547345a749", "size": 358400, "noattachment": false, "integrity": "sha512-Mc9cuDKklCuHMuZYV7s8ONmaHrjrJ5w6X0e48MDZH/zzb21+Z6iD6Jvoc4uaZMUzk8YCjxSKDDmtuoCHpAw6Wg=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1328200997272, "_hasShrinkwrap": false, "_cnpm_publish_time": 1328200997272, "_cnpmcore_publish_time": "2021-12-13T13:30:15.323Z"}, "0.22.6": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.22.6", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.1", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.22.6", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.22.6.tgz", "shasum": "392c7c16ceee48eec0404e4adda43969a0865ae5", "size": 358400, "noattachment": false, "integrity": "sha512-RDBIydAJb4eAF7nCIb4n5j/jP4TwPCQBlHSt3tWsemFOEc7dDnsTPqVUnsRZ1V84RViQ+IA2MIWwyxTV9MkRaA=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1327078022172, "_hasShrinkwrap": false, "_cnpm_publish_time": 1327078022172, "_cnpmcore_publish_time": "2021-12-13T13:30:16.723Z"}, "0.22.5": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.22.5", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.1", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.22.5", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.22.5.tgz", "shasum": "8ec2168edaf9e9d8d002fe34311ec30c57097fb0", "size": 358400, "noattachment": false, "integrity": "sha512-/sNKmZrFPERJzQnTqI2KwV1TEug+NEn63qOVLfPI7ICmhQiaFk3fHea/5rAxnSXs+hAyrdffhaq6jcp6oyePfg=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1326773623876, "_hasShrinkwrap": false, "_cnpm_publish_time": 1326773623876, "_cnpmcore_publish_time": "2021-12-13T13:30:18.562Z"}, "0.22.4": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.22.4", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.1", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.22.4", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.22.4.tgz", "shasum": "a180455854790b25d3be42ea698b4b1d8af5a8c0", "size": 358400, "noattachment": false, "integrity": "sha512-uUAe4ZHdt3GQqbfeX4iYAcXy7fmDkW5xRsBx8bAEFnuZsdIaImQhbZkC23grC+OA6j/gaiNI412WnM2MtK6eOQ=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1326317221259, "_hasShrinkwrap": false, "_cnpm_publish_time": 1326317221259, "_cnpmcore_publish_time": "2021-12-13T13:30:20.315Z"}, "0.22.3": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.22.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.1", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.22.3", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.22.3.tgz", "shasum": "192f21ae054049c9356ccc5104b2d75aa1c8d3f5", "size": 358400, "noattachment": false, "integrity": "sha512-CKjcbR+Yr0ftip4iCNEO1iqOg0ep01p6J3n7z0Dt02AebtyUS8E2spNLXNzfvR8Au5gmWqcmcyLUcfAXxYMByg=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1326300722470, "_hasShrinkwrap": false, "_cnpm_publish_time": 1326300722470, "_cnpmcore_publish_time": "2021-12-13T13:30:22.165Z"}, "0.22.2": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.22.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.1", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.22.2", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.22.2.tgz", "shasum": "e001a07e400ea9c3137988f32ed2102da325f219", "size": 358400, "noattachment": false, "integrity": "sha512-70uQl8DMSJJiV16/m46GCWsRrWX+Tye3z6flPyC3mcI4yB3fSZxyxzKxUrXL68edw+rK+RAj8wTw0ZVhyN4ZiQ=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1326054315846, "_hasShrinkwrap": false, "_cnpm_publish_time": 1326054315846, "_cnpmcore_publish_time": "2021-12-13T13:30:23.925Z"}, "0.22.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.22.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.1", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.22.1", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.6.6", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.22.1.tgz", "shasum": "f95ea8da05dac52eaac109d35a90f2974c6bfb47", "size": 358400, "noattachment": false, "integrity": "sha512-52LJ63MxBKtGU77VSCRrN7xcgK6qjirOfKRc4yWguCzDJdwf6lBszXX21zsCXB0+wKH4OgsWAsTK4bBWD27pKw=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1326031179626, "_hasShrinkwrap": false, "_cnpm_publish_time": 1326031179626, "_cnpmcore_publish_time": "2021-12-13T13:30:25.597Z"}, "0.22.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.22.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.1", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.22.0", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.22.0.tgz", "shasum": "d0e73245116414fabd605a41539a186d47d94ad8", "size": 368640, "noattachment": false, "integrity": "sha512-FzuxGbXQ/KMPN6xPAe081ATWEjKiZ85WeTkDfaw+qUb6iPcw99Q2Y/s2SrNCTtQkwAZjAW+Fg6n3oqFYiLNdIQ=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1325723045383, "_hasShrinkwrap": false, "_cnpm_publish_time": 1325723045383, "_cnpmcore_publish_time": "2021-12-13T13:30:27.403Z"}, "0.21.2": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.21.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.21.2", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.21.2.tgz", "shasum": "3f4560f70a3d310dd09fd5b16b670e12f0159e64", "size": 358400, "noattachment": false, "integrity": "sha512-tBVGHa+tFHRqLEji5EMUhUgOIUCClDrc1y2Q2TxJhCQgDc0Uk0JH6NbI3dfiWevnDfcz7KtGLpd4CZQQLOdx7w=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1324574860011, "_hasShrinkwrap": false, "_cnpm_publish_time": 1324574860011, "_cnpmcore_publish_time": "2021-12-13T13:30:29.195Z"}, "0.21.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.21.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.21.1", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.21.1.tgz", "shasum": "395b3b6843f5bcd2418e869c151ddc8d7edfd5ba", "size": 358400, "noattachment": false, "integrity": "sha512-pUgb1lOUwIwp88DcIe/9ZMeeLwyHDjSwJVZsPz2ey1KBmylZGNvmb3IJibSwTJYmqxfmkTUWVvAj6QFpor02Ag=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1324399886901, "_hasShrinkwrap": false, "_cnpm_publish_time": 1324399886901, "_cnpmcore_publish_time": "2021-12-13T13:30:31.040Z"}, "0.21.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.21.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.21.0", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.6.4", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.21.0.tgz", "shasum": "7c857d4c4d34e669bbe6a3ff3a6946a3a96e51e5", "size": 358400, "noattachment": false, "integrity": "sha512-clrVbMg0ewIpZ9RC0OnPJTlhgC2W496NTFNx4quXa9OON4h1o8mwprgUay5W58UdJHchs1tfCXwzhyf5UqDV0w=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1324146752383, "_hasShrinkwrap": false, "_cnpm_publish_time": 1324146752383, "_cnpmcore_publish_time": "2021-12-13T13:30:32.931Z"}, "0.20.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.20.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.20.1", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.6.5", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.20.1.tgz", "shasum": "79e739091db75b72972ac34d69d51fdc74ebd62c", "size": 358400, "noattachment": false, "integrity": "sha512-H2nXzfMX6bGZ9oA+zhDtYQ1M0SDLWZVJ7Kpyeq6IvkdDrgaLi3SnB/hM8XZesW+YqMsb6qvsP4Uc5IHBVqx5KA=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1324056714513, "_hasShrinkwrap": false, "_cnpm_publish_time": 1324056714513, "_cnpmcore_publish_time": "2021-12-13T13:30:34.822Z"}, "0.20.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.20.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.20.0", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.6.4", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.20.0.tgz", "shasum": "3793c88361dff3dd98b440b8e4c93fa38e442c10", "size": 358400, "noattachment": false, "integrity": "sha512-yNdTgHkGoUHC3o89QDTkAUgTP2r/WdV6fnc653+3mG5030ql4KVjOUMEy7gfV0EOfipXnNbYNTn7cO/iaRHqfQ=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1323617026350, "_hasShrinkwrap": false, "_cnpm_publish_time": 1323617026350, "_cnpmcore_publish_time": "2021-12-13T13:30:36.747Z"}, "0.19.8": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.19.8", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.19.8", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.104", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.19.8.tgz", "shasum": "07bd6940d66043dacdd5ef5a7b4423ea753f1254", "size": 358400, "noattachment": false, "integrity": "sha512-xQnGG9Rl36Bc586WDA967uFfRrMOCp/wtyY/zz912KhcJn/pANaNWDyksVN/9nHUcxZrguBRur+ep/3EcsRHtg=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1322765469548, "_hasShrinkwrap": false, "_cnpm_publish_time": 1322765469548, "_cnpmcore_publish_time": "2021-12-13T13:30:38.359Z"}, "0.19.7": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.19.7", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.19.7", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.104", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.19.7.tgz", "shasum": "1f4f9798be218cf57035d110f83ee6b0d1c712e6", "size": 358400, "noattachment": false, "integrity": "sha512-7cnZOXmfayq4n4WrCBxgcyR4qmkA71tQdtYNITkl8AbAiVpoX2uxNYalqVsoGvZFFVwHzgySqRSQQSLtPr+jVg=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1322678253771, "_hasShrinkwrap": false, "_cnpm_publish_time": 1322678253771, "_cnpmcore_publish_time": "2021-12-13T13:30:40.395Z"}, "0.19.6": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.19.6", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.19.6", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.104", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.19.6.tgz", "shasum": "e227d788df628aeb0bb3e855c534f1cdfe7cc7c7", "size": 358400, "noattachment": false, "integrity": "sha512-lEAEiw9xF42KSxHSSg0JX4c/TU31M570286E/1RuEejJ80c4mPt45FOKsMTU36ivg26aeApz7zkOMXIaHLLZ/A=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1322674142874, "_hasShrinkwrap": false, "_cnpm_publish_time": 1322674142874, "_cnpmcore_publish_time": "2021-12-13T13:30:42.396Z"}, "0.19.5": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.19.5", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.19.5", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.104", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.19.5.tgz", "shasum": "0a2ac440736dbfe17c2f78a3293ca8553e0b24cb", "size": 358400, "noattachment": false, "integrity": "sha512-vMdC0ZybAY9gIDwnO1IAD6gQn+jA9NjNulLgdoNirCiEATVYXjeHIAUPatw8U/+lxPqnZlURzfj3VjvdPkA17w=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1322505178864, "_hasShrinkwrap": false, "_cnpm_publish_time": 1322505178864, "_cnpmcore_publish_time": "2021-12-13T13:30:44.675Z"}, "0.19.4": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.19.4", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.19.4", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.104", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.19.4.tgz", "shasum": "213f285e3052d3c5164dfb64fe9b55872d514d36", "size": 358400, "noattachment": false, "integrity": "sha512-Id31m1WuJzqkz4YBN8juyi+U/VzilvFnsJ2v1w7qAHU7lsLKuv6HzVGkXxc38LSG30Z2/ffqXEnINUOX/CBiLg=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1322500953465, "_hasShrinkwrap": false, "_cnpm_publish_time": 1322500953465, "_cnpmcore_publish_time": "2021-12-13T13:30:46.738Z"}, "0.19.3": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.19.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.19.3", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.104", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.19.3.tgz", "shasum": "ecb1e81d57e5b322adba59c75349145c3534e9b2", "size": 358400, "noattachment": false, "integrity": "sha512-5lHZYMK+x4GHliqDXSgWxTsLxx1/263Q9cagxCVtNnGIHpqSIcKO0UiN8hYhdR09etsvatseuAFX5bsaa6EXVg=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1321568898518, "_hasShrinkwrap": false, "_cnpm_publish_time": 1321568898518, "_cnpmcore_publish_time": "2021-12-13T13:30:48.567Z"}, "0.19.2": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.19.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.19.2", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.104", "_nodeVersion": "v0.6.0", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.19.2.tgz", "shasum": "b27c23e82697aa01e08ebc7449880a3655a9a1b9", "size": 358400, "noattachment": false, "integrity": "sha512-253YK5dzvOZRMnz5dxsG8bKoyTsIVc5a3LIHTVW1Ocipks11ZIX/Yk69+4NxQLM7RTLyxmKq9xC9FUfLMx6pRw=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1320862311128, "_hasShrinkwrap": false, "_cnpm_publish_time": 1320862311128, "_cnpmcore_publish_time": "2021-12-13T13:30:50.337Z"}, "0.19.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.19.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.19.1", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.104", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.19.1.tgz", "shasum": "467122490c9c49a1e79b662476a7ef7242a6a1e0", "size": 358400, "noattachment": false, "integrity": "sha512-q5Pt2+w02Z+73Lpc5lD8sijvEXa11Wfar3Pp81ugzUTtNE7GxdrBrk1qL4uWT2k+EWrXMT95Ka7ubnmPoctihA=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1320768219616, "_hasShrinkwrap": false, "_cnpm_publish_time": 1320768219616, "_cnpmcore_publish_time": "2021-12-13T13:30:51.924Z"}, "0.19.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.19.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": "> 0.4.x < 0.7.0"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "stylus@0.19.0", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.102", "_nodeVersion": "v0.5.10", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.19.0.tgz", "shasum": "a0368d41ca68c79eb9a59ea9bb05ce59fde8c214", "size": 358400, "noattachment": false, "integrity": "sha512-oT3kWuZmMZPBabOVWbJmFkh+NvLTDRtBWReumrhZFAW2McusjDeeCDrC7YYTcZ9HfoxRrNKDHxeHd49TzX5FQA=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1319654477028, "_hasShrinkwrap": false, "_cnpm_publish_time": 1319654477028, "_cnpmcore_publish_time": "2021-12-13T13:30:53.695Z"}, "0.18.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.18.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.18.0/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "stylus@0.18.0", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.24", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.18.0.tgz", "shasum": "0e181844911cea1af80181fca35c49534e58b5c1", "size": 349430, "noattachment": false, "integrity": "sha512-R1XzU0lm/qHS+IOsdA1sWoI4LKDJvyTSAxxI2swoDs7fhjIVtdEMA4u8BkugANIzPhli7BKuBBP5y05tcG70FQ=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1319215179975, "_hasShrinkwrap": false, "_cnpm_publish_time": 1319215179975, "_cnpmcore_publish_time": "2021-12-13T13:30:56.050Z"}, "0.17.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.17.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.17.0/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "stylus@0.17.0", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.24", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.17.0.tgz", "shasum": "85e43dc681fda7329350f412c6de2192121c7f79", "size": 348861, "noattachment": false, "integrity": "sha512-iwGFoYAIZHvXCWjsV7C0t0eGABJY3gOQcRA9Bh7L27kTLqMzjyhEcfxfsrqe+My1CUasjhgfCp7IOQEIwd7NqA=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1317409757621, "_hasShrinkwrap": false, "_cnpm_publish_time": 1317409757621, "_cnpmcore_publish_time": "2021-12-13T13:30:58.058Z"}, "0.16.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.16.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0", "mkdirp": "0.0.7"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.16.0/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "stylus@0.16.0", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.24", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.16.0.tgz", "shasum": "8bf10acd782d9375dc6a6786e95b20f211c2b6ef", "size": 346942, "noattachment": false, "integrity": "sha512-jISZoUIIp4vbbRVf2BkzVQSlJiCogNsuXm9pZtKsJGuewaZA+4KTeNlqIqqvP2gwfeocrPajprA2tT7e+veyOQ=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1317062653086, "_hasShrinkwrap": false, "_cnpm_publish_time": 1317062653086, "_cnpmcore_publish_time": "2021-12-13T13:31:00.242Z"}, "0.15.4": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.15.4", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.15.4/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "stylus@0.15.4", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.24", "_nodeVersion": "v0.4.11", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.15.4.tgz", "shasum": "63ae5b3eb72edeacbb23265c034eb56c1cee07df", "size": 346435, "noattachment": false, "integrity": "sha512-KqmADhkWXlmu8QKU2vQzJx7Tnj/rcu8XJEyJvMoR+HT5qDW/J/IWDF2UMuPHDtHDUMgIt7nbhzbU678WMvL+5w=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1316038170777, "_hasShrinkwrap": false, "_cnpm_publish_time": 1316038170777, "_cnpmcore_publish_time": "2021-12-13T13:31:02.332Z"}, "0.15.3": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.15.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.15.3/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "stylus@0.15.3", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.24", "_nodeVersion": "v0.4.11", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.15.3.tgz", "shasum": "6f09fdd69638945e12bb76b387c37cbece7c1b1f", "size": 346323, "noattachment": false, "integrity": "sha512-yQwS7V9wo4OFepB/mSKI98n0fSaVe1LlvSB7RDFOcQBveepCZZjysKfu6pDfmQVYXEBcQgRU3y8c8vpbhp2tGA=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1316016132784, "_hasShrinkwrap": false, "_cnpm_publish_time": 1316016132784, "_cnpmcore_publish_time": "2021-12-13T13:31:04.413Z"}, "0.15.2": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.15.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.15.2/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "stylus@0.15.2", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.24", "_nodeVersion": "v0.4.11", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.15.2.tgz", "shasum": "af315fe3bce7d1ec97b82f62d1bb84d771647447", "size": 344835, "noattachment": false, "integrity": "sha512-sG6L9PNkTbNWseGE3ViLlYKzWW8DwZgriN3GIqz19NUd7s5A4J4yQgRRXZftE3DVCpFsFqrt/+urHUkk7fBBOQ=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1315355237880, "_hasShrinkwrap": false, "_cnpm_publish_time": 1315355237880, "_cnpmcore_publish_time": "2021-12-13T13:31:06.371Z"}, "0.15.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.15.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.15.1/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "stylus@0.15.1", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.24", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.15.1.tgz", "shasum": "671556400c58386d7e94149fe8012735334268ec", "size": 344535, "noattachment": false, "integrity": "sha512-+bchftj4XYDqFO0YsQW2yLSexxaSd+tuIciBRiGebe5wEJUkEbXv173eXudYodRDlax8IgK2O7CAZUp9QQje9w=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1313689711922, "_hasShrinkwrap": false, "_cnpm_publish_time": 1313689711922, "_cnpmcore_publish_time": "2021-12-13T13:31:08.451Z"}, "0.15.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.15.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.15.0/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "stylus@0.15.0", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.24", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.15.0.tgz", "shasum": "c0a608a0da676dbaf4653c741fd1c8b033794501", "size": 342137, "noattachment": false, "integrity": "sha512-Si3RUUiTAcLiL6F0rM9zahrLMJQlyj9Ue5Jh9e9vOG1r7aSD1ectAmc/X0ogxCpewtLvR7Pd7/Aw6JqghTsZjA=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1313469671745, "_hasShrinkwrap": false, "_cnpm_publish_time": 1313469671745, "_cnpmcore_publish_time": "2021-12-13T13:31:10.507Z"}, "0.14.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.14.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.14.0/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "stylus@0.14.0", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.14", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.14.0.tgz", "shasum": "f5c21b8f96e9c579d1e2bcc4f0d4b887c414a8e7", "size": 414827, "noattachment": false, "integrity": "sha512-cCtWwEvuw/K1hY8H+sHP42RpTzUw+hVAInF46XHRKZ6p66JJlDHBRvLSE7vC6H9Ka6xD0sp/KYazqDZgGBLTgw=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1312991968376, "_hasShrinkwrap": false, "_cnpm_publish_time": 1312991968376, "_cnpmcore_publish_time": "2021-12-13T13:31:12.375Z"}, "0.13.9": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.13.9", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.13.9/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "stylus@0.13.9", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.14", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.13.9.tgz", "shasum": "7472e4d753849833583ece0a13d3352400a53547", "size": 412373, "noattachment": false, "integrity": "sha512-lgok3FwQUjTd1yrQMKgm+JuEMyH4/qyGRfoH2sVwTOU2ISZgFccl1FrOsL1QfT4hqIb03Qb00fYSPeEjCONa+g=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1312499201335, "_hasShrinkwrap": false, "_cnpm_publish_time": 1312499201335, "_cnpmcore_publish_time": "2021-12-13T13:31:14.319Z"}, "0.13.8": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.13.8", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.13.8/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "stylus@0.13.8", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.14", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.13.8.tgz", "shasum": "555ead6c018c9dd4763eaf1af7c183968703599a", "size": 411972, "noattachment": false, "integrity": "sha512-bf8lCFCuWemd/vtjIKvRyuit9/IMXRbFraCWosOg/nHTt88WCCe9XBFbBJWn6j+BqrZtob0e8cvOqhlSDfN0vw=="}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1312214448314, "_hasShrinkwrap": false, "_cnpm_publish_time": 1312214448314, "_cnpmcore_publish_time": "2021-12-13T13:31:16.228Z"}, "0.13.7": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.13.7", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.13.7/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "stylus@0.13.7", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.14", "_nodeVersion": "v0.4.9", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.13.7.tgz", "shasum": "1f69251854de2e41164db4f2b1f5ab92aef31fa5", "size": 411917, "noattachment": false, "integrity": "sha512-V+GEzTTTF3nq+IWRV24MrbZfgVZwr+75KjpxTWVz2GZs4TZjeN0tlOh0Hz3If7f+yPpJbmTRW7v+U1Ki12Aj3Q=="}, "directories": {}, "publish_time": 1310753410183, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1310753410183, "_cnpmcore_publish_time": "2021-12-13T13:31:17.920Z"}, "0.13.6": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.13.6", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.13.6/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "stylus@0.13.6", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.14", "_nodeVersion": "v0.4.9", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.13.6.tgz", "shasum": "0b42e5cd7e7fd79809fcf5f5b521ac2a8754b345", "size": 407806, "noattachment": false, "integrity": "sha512-GxeQhe7fQR4Rb3h0g3pVlFSh+60Pb9Mz94/uNW4Ozc9GbAGHi34qgtetUl4ZFuHk7WpHGEMv65fupUH52lCwjg=="}, "directories": {}, "publish_time": 1310496061577, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1310496061577, "_cnpmcore_publish_time": "2021-12-13T13:31:19.969Z"}, "0.13.5": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.13.5", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.13.5/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "stylus@0.13.5", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.14", "_nodeVersion": "v0.4.8", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.13.5.tgz", "shasum": "214590b38388ea0ead05c7b9f1bb469aa31cc9cf", "size": 404018, "noattachment": false, "integrity": "sha512-vXBQTdf14gGVe3pyXDFhkMEL4W3EPUiO4EMcCULd9/e/z8hiM9p5QUnrlCMDRcIxlUL+PQ7Zg0XD7X8sSMFj6w=="}, "directories": {}, "publish_time": 1309196449858, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1309196449858, "_cnpmcore_publish_time": "2021-12-13T13:31:22.064Z"}, "0.13.4": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.13.4", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stylus/0.13.4/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "stylus@0.13.4", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.14", "_nodeVersion": "v0.4.8", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.13.4.tgz", "shasum": "08a679c40456b19642429b6a99eaa2a2045147ea", "size": 403952, "noattachment": false, "integrity": "sha512-gSxy0IpERLZjUkWqUMOHo67mhg5pCmkFfaLqnYg9ON3BgzvBZDfOLYf8FsP2VHrhZEkauTuQgCSdZp+qIlvRcw=="}, "directories": {}, "publish_time": 1308758974637, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1308758974637, "_cnpmcore_publish_time": "2021-12-13T13:31:24.414Z"}, "0.13.3": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.13.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "scripts": {"prepublish": "npm prune"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "devDependencies": {}, "_id": "stylus@0.13.3", "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.4.8", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.13.3.tgz", "shasum": "5930eb29f4b148bcc6d5432582f10f0bf4ead75a", "size": 49099, "noattachment": false, "integrity": "sha512-rCR+p3lhaPHq4sBl7e0cx3FdE8w9m6PdE8y00FXZt0Qq5Fds137i4OmXPhC339VIvKodI6r2pG15SgMn8s0Kbw=="}, "directories": {}, "publish_time": 1306966974429, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1306966974429, "_cnpmcore_publish_time": "2021-12-13T13:31:26.286Z"}, "0.13.2": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.13.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "devDependencies": {}, "_id": "stylus@0.13.2", "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.4.8", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.13.2.tgz", "shasum": "52afd1860ed017b47c6b91c5052bf98562df2768", "size": 71942, "noattachment": false, "integrity": "sha512-GQRgjn+ViXvMN495kelUgXy0VIKPVQi98nAtVxNz1i/O/mIwLjTEWI7qGz9TA5aFspnXMPGQBmK+m9kA8uy+/A=="}, "scripts": {}, "directories": {}, "publish_time": 1306878848613, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1306878848613, "_cnpmcore_publish_time": "2021-12-13T13:31:28.326Z"}, "0.13.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.13.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "devDependencies": {}, "_id": "stylus@0.13.1", "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.4.8", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.13.1.tgz", "shasum": "32274037e1ba4f73ec50dfa0e70526052a41dda1", "size": 48782, "noattachment": false, "integrity": "sha512-ik6k6KDGVONegWLUXTzqHY/X/XfuvdAXqmdes27SX8ZxEipg+ejlyfi89qJZQIrLKm01w67INBrexrBbWCrMTg=="}, "scripts": {}, "directories": {}, "publish_time": 1306777870925, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1306777870925, "_cnpmcore_publish_time": "2021-12-13T13:31:30.537Z"}, "0.13.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.13.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "devDependencies": {}, "_id": "stylus@0.13.0", "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.4.7", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.13.0.tgz", "shasum": "37b69909544a0aba26a2914cf18fc6d80988c7b8", "size": 48723, "noattachment": false, "integrity": "sha512-ue0TiH2m3PXZzhiLSmp+ieMMHTLvry62DNVE16mYZtemozHvnVJJH9kJ7af7RonoADXwhX/PD9RscRORAVl5WQ=="}, "scripts": {}, "directories": {}, "publish_time": 1305655911309, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1305655911309, "_cnpmcore_publish_time": "2021-12-13T13:31:32.334Z"}, "0.12.4": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.12.4", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "devDependencies": {}, "_id": "stylus@0.12.4", "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.4.7", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.12.4.tgz", "shasum": "ed2dadb8f40a238fc3d7b8e691ee02ac5ba36e5d", "size": 48507, "noattachment": false, "integrity": "sha512-QNUOhhcJ1r+SVlfPKoN1vL9JA/hRqYcO23fHtJTknLXELSFljUlFfV/DfSCGrGFU5B1IRps/o7LVNdra0jP1zQ=="}, "scripts": {}, "directories": {}, "publish_time": 1305223498189, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1305223498189, "_cnpmcore_publish_time": "2021-12-13T13:31:34.268Z"}, "0.12.3": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.12.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "devDependencies": {}, "_id": "stylus@0.12.3", "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.4.7", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.12.3.tgz", "shasum": "165aac072d2ba06bf8cee4046a7aeb373a0b810e", "size": 48444, "noattachment": false, "integrity": "sha512-HhbjiKiNReutWvCxlUPzS6S/Xtuw5ku03KIVD5ZmfwdyXchco5XBRivgv/Jh7faMVYPYAVK7ORYXjFrkcOFV1g=="}, "scripts": {}, "directories": {}, "publish_time": 1304875765795, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1304875765795, "_cnpmcore_publish_time": "2021-12-13T13:31:36.435Z"}, "0.12.2": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.12.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "devDependencies": {}, "_id": "stylus@0.12.2", "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.4.7", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.12.2.tgz", "shasum": "52987d4a416fdd390c3070ff89a4e66ebadf8fc5", "size": 48400, "noattachment": false, "integrity": "sha512-ry2MpDF6ZOjLJUeBA2pHXR4Kp+CnGCKlAqKcqZm87xRa0Hk/cmv4SrXJBulVGbXYIu/p8zxlKXxrJs0dTaMM8Q=="}, "scripts": {}, "directories": {}, "publish_time": 1304455532180, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1304455532180, "_cnpmcore_publish_time": "2021-12-13T13:31:38.800Z"}, "0.12.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.12.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_id": "stylus@0.12.1", "_engineSupported": true, "_npmVersion": "0.3.18", "_nodeVersion": "v0.4.6", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.12.1.tgz", "shasum": "9409351cccf304513ac9db87c86277032e54092a", "size": 49147, "noattachment": false, "integrity": "sha512-94C0YXArMRNrRKK0A8VeyzwYbuL2OGj/G6IduzY4DHWpHptXlyMDaBRr82UvO8GL0lmD2GsVhLCizLcaSsG//Q=="}, "publish_time": 1304119860911, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1304119860911, "_cnpmcore_publish_time": "2021-12-13T13:31:40.537Z"}, "0.12.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.12.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_id": "stylus@0.12.0", "_engineSupported": true, "_npmVersion": "0.3.18", "_nodeVersion": "v0.4.6", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.12.0.tgz", "shasum": "e1b938486fd4fabaeddc23378c1b33219d0ea63b", "size": 49018, "noattachment": false, "integrity": "sha512-/BAl6G0ryr3zZPekMhWqPkCshJjNK/Dk+jpNXkMSwdMwzEo2t6ddxpMBkGuAwEl/fX0RSYPaZQzb2q416SiUFw=="}, "publish_time": 1304113318390, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1304113318390, "_cnpmcore_publish_time": "2021-12-13T13:31:42.475Z"}, "0.11.12": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.11.12", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_id": "stylus@0.11.12", "_engineSupported": true, "_npmVersion": "0.3.18", "_nodeVersion": "v0.4.6", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.11.12.tgz", "shasum": "2bd059d75df5e37b02b692527b8f9635f45b62a6", "size": 48458, "noattachment": false, "integrity": "sha512-l5/q8xy4AyE/vfYyLSsgBHruXCd60RMqR0f+b/Lup3plpeejpgsbR+P90nepBijdfn81v60VRdTG71PwTOZl6g=="}, "publish_time": 1303955677091, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1303955677091, "_cnpmcore_publish_time": "2021-12-13T13:31:44.452Z"}, "0.11.11": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.11.11", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_id": "stylus@0.11.11", "_engineSupported": true, "_npmVersion": "0.3.18", "_nodeVersion": "v0.4.6", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.11.11.tgz", "shasum": "cd2d4cbdfa0b77817c1a58b2614e04b8917ddcf2", "size": 48148, "noattachment": false, "integrity": "sha512-kRlsDfwXaGiBcImR77g8zFKpfStbwxUlML6wAuQPuuWATO+nEKMMfPETX3KZc2ghsMbFKNOf6EtQkw7KuvKS2Q=="}, "publish_time": 1303696731175, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1303696731175, "_cnpmcore_publish_time": "2021-12-13T13:31:46.490Z"}, "0.11.10": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.11.10", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_id": "stylus@0.11.10", "_engineSupported": true, "_npmVersion": "0.3.18", "_nodeVersion": "v0.4.6", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.11.10.tgz", "shasum": "6903bd0205a800b93b6e445072d801475680f59a", "size": 48112, "noattachment": false, "integrity": "sha512-J/UIdRdhKmwEK3uCcUTQJgMnTRNl2ZMQs2ygMPSBiaYjVGx2O01u5dsqRyJhTSNOT69B98SIYQybLnZTmvEf6A=="}, "publish_time": 1303081442268, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1303081442268, "_cnpmcore_publish_time": "2021-12-13T13:31:48.782Z"}, "0.11.9": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.11.9", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_id": "stylus@0.11.9", "_engineSupported": true, "_npmVersion": "0.3.18", "_nodeVersion": "v0.4.6", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.11.9.tgz", "shasum": "e11085c54394dddfd1ecc458b31f36a1511889cd", "size": 48068, "noattachment": false, "integrity": "sha512-oKqmH6Z5wGQVbeMpMHRpm10hflegA0zEwF/24OtGzu3/chZBa9B+3CTE5DDdlGVfedNY7p+/OMhAJrj/jrM6hA=="}, "publish_time": 1302902121703, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1302902121703, "_cnpmcore_publish_time": "2021-12-13T13:31:50.865Z"}, "0.11.8": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.11.8", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_id": "stylus@0.11.8", "_engineSupported": true, "_npmVersion": "0.3.18", "_nodeVersion": "v0.4.6", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.11.8.tgz", "shasum": "2910f56c092b1802863ba77a51196dd3f62121e1", "size": 48015, "noattachment": false, "integrity": "sha512-vGEw2QcP3oIabxqeAn2zMiL2lgvHTNRx2vbv5ivgZEb0XECbBcMYYRiFaPQ8Qq7dWZ4VWLsTcma4AiXuJXlArA=="}, "publish_time": 1302887520736, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1302887520736, "_cnpmcore_publish_time": "2021-12-13T13:31:52.824Z"}, "0.11.7": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.11.7", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_id": "stylus@0.11.7", "_engineSupported": true, "_npmVersion": "0.3.18", "_nodeVersion": "v0.4.5", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.11.7.tgz", "shasum": "7955acb22b9253a617f2a015717281cec300e294", "size": 47928, "noattachment": false, "integrity": "sha512-qTYWHr/i/Fxi3UILDPxx1zBYreUEUA+566EpeUZgzLEsIUv01cC6EjC0QfKSjBiigL+Rl3ZkkhuW1bEg+/4Gvg=="}, "publish_time": 1302644967287, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1302644967287, "_cnpmcore_publish_time": "2021-12-13T13:31:54.552Z"}, "0.11.6": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.11.6", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_id": "stylus@0.11.6", "_engineSupported": true, "_npmVersion": "0.3.15", "_nodeVersion": "v0.4.5", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.11.6.tgz", "shasum": "b2802c7b64daed3c5319bd480d988b3ead42aade", "size": 47790, "noattachment": false, "integrity": "sha512-lpFHulieyPa97sVBW9Ct2FPfDSPTgD7xpzK2/7kcHew8BQmsEwirOHdhS/uEbVOXg5OUJzN3+7LP9XE/hFnXow=="}, "publish_time": 1302632573874, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1302632573874, "_cnpmcore_publish_time": "2021-12-13T13:31:56.774Z"}, "0.11.5": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.11.5", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_id": "stylus@0.11.5", "_engineSupported": true, "_npmVersion": "0.3.15", "_nodeVersion": "v0.4.5", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.11.5.tgz", "shasum": "eb9576473d32208186d9bc06438b756e892eb856", "size": 47752, "noattachment": false, "integrity": "sha512-jQ2LeM3D0Dk+bKDLfhvWufKrv1QHyz4e8UwS4F1JvHc0b2ZghvjBCcDCCBRa7lwetrXrvOGU11d5JwGg69Fi3A=="}, "publish_time": 1302619199565, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1302619199565, "_cnpmcore_publish_time": "2021-12-13T13:31:58.849Z"}, "0.11.4": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.11.4", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_id": "stylus@0.11.4", "_engineSupported": true, "_npmVersion": "0.3.15", "_nodeVersion": "v0.4.5", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.11.4.tgz", "shasum": "149b8480cdc5da7a82388f1dc8f169b26b84b094", "size": 47615, "noattachment": false, "integrity": "sha512-IIUCUXYNm3ZJFZU6FS6A3IsznDMFYjsGp9rYyWIjyCLp3ks6Hm2Tzv3/M5mr3f1eMP7rkcZSf+Nbo5lDSDA/eg=="}, "publish_time": 1302462358786, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1302462358786, "_cnpmcore_publish_time": "2021-12-13T13:32:01.237Z"}, "0.11.3": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.11.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_id": "stylus@0.11.3", "_engineSupported": true, "_npmVersion": "0.3.15", "_nodeVersion": "v0.4.5", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.11.3.tgz", "shasum": "b3d13e11d5bd1a207dc77d687beddd1d732911df", "size": 47478, "noattachment": false, "integrity": "sha512-UIGU5pw+g/itFAv1Xi3zdO/lt08ye+MTQyDzXJqsxgGj28HLEXrqwQT1a/ZicSYCxEnEOQwy+yrUx8WWQVMrNA=="}, "publish_time": 1302305874020, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1302305874020, "_cnpmcore_publish_time": "2021-12-13T13:32:03.521Z"}, "0.11.2": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.11.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_id": "stylus@0.11.2", "_engineSupported": true, "_npmVersion": "0.3.15", "_nodeVersion": "v0.4.4", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.11.2.tgz", "shasum": "602dd4aaa878975cff1a07d54330bf5ab80c4d48", "size": 47395, "noattachment": false, "integrity": "sha512-c5yBvVJagxMAsREz299uaCIe65EP929MPI8ScDkNa7dR1+TzLboMZcqJjlqzcTNelxa5duKngiurKLPvc+HLKw=="}, "publish_time": 1302128210594, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1302128210594, "_cnpmcore_publish_time": "2021-12-13T13:32:05.636Z"}, "0.11.1": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.11.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_id": "stylus@0.11.1", "_engineSupported": true, "_npmVersion": "0.3.15", "_nodeVersion": "v0.4.4", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.11.1.tgz", "shasum": "8aa94a8e82f88b955ed828c5b93715b61072a6d5", "size": 47478, "noattachment": false, "integrity": "sha512-jIxI0M5FpmRDcDNTBsBfmSkKvdoSHrdU/PDwO/do+kPuCmeqvJUFIMxVX81dJWqiQCnofWZPp2e02ylxk2Kjng=="}, "publish_time": 1301702720252, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1301702720252, "_cnpmcore_publish_time": "2021-12-13T13:32:08.063Z"}, "0.11.0": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.11.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_id": "stylus@0.11.0", "_engineSupported": true, "_npmVersion": "0.3.15", "_nodeVersion": "v0.4.3", "directories": {"bin": "./bin", "lib": "./lib"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.11.0.tgz", "shasum": "a48653a64f3d2d34123d5d0cf27f2a7010c8bc66", "size": 47452, "noattachment": false, "integrity": "sha512-TWIR9f//Y2oYwB6yXtOtmKuX3Un7lcZFNWEPKvzeVee0HpvWFiEFYdTQ4/3p3WuMMSLg7eUgn03klrWhXr3JCw=="}, "publish_time": 1301697513701, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1301697513701, "_cnpmcore_publish_time": "2021-12-13T13:32:10.201Z"}, "0.10.0": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.10.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/learnboost/stylus.git"}, "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_id": "stylus@0.10.0", "_engineSupported": true, "_npmVersion": "0.3.15", "_nodeVersion": "v0.4.3", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.10.0.tgz", "shasum": "814f106cf6ff6a37418fbce8a39e9ca7d1d2bce7", "size": 45335, "noattachment": false, "integrity": "sha512-KOiH0lpXqRkddJ1fIx/3+movjtugXwkTQW/7jCTl6zGejs1Ki8toXPuvnF9g7Ql8dfQblgbBdpoeS//DGPj1Cg=="}, "publish_time": 1301438957956, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1301438957956, "_cnpmcore_publish_time": "2021-12-13T13:32:12.237Z"}, "0.9.2": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.9.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_id": "stylus@0.9.2", "_engineSupported": true, "_npmVersion": "0.3.15", "_nodeVersion": "v0.4.3", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.9.2.tgz", "shasum": "ee7c1a7fb74d6211bd64eca1003e3a9c1c3fc019", "size": 46080, "noattachment": false, "integrity": "sha512-3qSXQTJAS4JDUBXo2pUsfpcX9hvcbYTiSje2+8Teh73TlZ1eQ0WWsrWlN8d+6S8KS5yi1gngQvI9Fjhu+dyG8g=="}, "publish_time": 1300730918694, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1300730918694, "_cnpmcore_publish_time": "2021-12-13T13:32:14.206Z"}, "0.9.1": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.9.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_id": "stylus@0.9.1", "_engineSupported": true, "_npmVersion": "0.3.15", "_nodeVersion": "v0.4.3", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.9.1.tgz", "shasum": "1ee4831c2f76c4d4b0e0c6971d6d90420e896292", "size": 46074, "noattachment": false, "integrity": "sha512-MO8G7yW3TsoAxEAw8H9+w7RVJz52Ek6WiUVFLPawJyqyMy1vbN4UZgD+szt3zoKjfAUe39B2WVhwbIS+p1b9BA=="}, "publish_time": 1300492802730, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1300492802730, "_cnpmcore_publish_time": "2021-12-13T13:32:16.270Z"}, "0.9.0": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.9.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": "0.2.0", "growl": "1.1.0"}, "_id": "stylus@0.9.0", "_engineSupported": true, "_npmVersion": "0.3.15", "_nodeVersion": "v0.4.2", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.9.0.tgz", "shasum": "3662b65d87fbf7fa3cb63334ce915a4d00a1c719", "size": 45741, "noattachment": false, "integrity": "sha512-pirtM8M3uFYo88E3NAzYnyJMfbSL2StP56q9u2cXEwlkRq91O3TDp1mD/72XlTpi6iHBYasOwisBCZallBayxQ=="}, "publish_time": 1300473987156, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1300473987156, "_cnpmcore_publish_time": "2021-12-13T13:32:18.380Z"}, "0.8.0": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.8.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": "0.2.0", "growl": "1.0.2"}, "_id": "stylus@0.8.0", "_engineSupported": true, "_npmVersion": "0.3.15", "_nodeVersion": "v0.4.2", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.8.0.tgz", "shasum": "72e08462108320ac05d36a6d2b2749fe13d23857", "size": 44649, "noattachment": false, "integrity": "sha512-So29rdYq6japdXVCXy21PHH/77kn5tGPygKh1r+hLjcWqrBLkvr9BFE2O3eDOGmcN0BEV/MKBX8L8VL7Cqvw7w=="}, "publish_time": 1300119248768, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1300119248768, "_cnpmcore_publish_time": "2021-12-13T13:32:20.359Z"}, "0.7.4": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.7.4", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": "0.2.0", "growl": "1.0.2"}, "_id": "stylus@0.7.4", "_engineSupported": true, "_npmVersion": "0.3.15", "_nodeVersion": "v0.4.2", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.7.4.tgz", "shasum": "a7a85477fe906bc9367aec4c9d11d6371433a85e", "size": 44070, "noattachment": false, "integrity": "sha512-D4iPGPsw4bBk/T55aSeCE9ro5R+am2yqn3AAJHBZ47/+UKCb2P28jQX8USYMTEzeV1XhSSbfkNLg3Qfs0E1Yaw=="}, "publish_time": 1299801929814, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1299801929814, "_cnpmcore_publish_time": "2021-12-13T13:32:22.561Z"}, "0.7.3": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.7.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": "0.2.0", "growl": "1.0.2"}, "_id": "stylus@0.7.3", "_engineSupported": true, "_npmVersion": "0.3.15", "_nodeVersion": "v0.4.2", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.7.3.tgz", "shasum": "1f11ca1bbb5771c10d657e38da2e30a4d8918904", "size": 43330, "noattachment": false, "integrity": "sha512-s7gZ419Wd8eC64LD42s0wTz0JoZ4D7aGZ+q0zALqaFKp7El2qe6gG+M78ByqNdPQddKyrExbKynbKWrv6n6fqg=="}, "publish_time": 1299692623337, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1299692623337, "_cnpmcore_publish_time": "2021-12-13T13:32:25.105Z"}, "0.7.2": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.7.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": "0.2.0", "growl": "1.0.2"}, "_id": "stylus@0.7.2", "_engineSupported": true, "_npmVersion": "0.3.15", "_nodeVersion": "v0.4.2", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.7.2.tgz", "shasum": "dde792c217af509f41db888e6e72bd55059ec1c6", "size": 43302, "noattachment": false, "integrity": "sha512-BZUTR9e6Vpd79D+fkxUCA06aSd5NspCvskPF7+ft5I0KRW0uIlq8oLCfwfbQkK97JVRwstxYjppXniptbWQzzw=="}, "publish_time": 1299638085795, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1299638085795, "_cnpmcore_publish_time": "2021-12-13T13:32:28.200Z"}, "0.7.1": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.7.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": "0.2.0", "growl": "1.0.2"}, "_id": "stylus@0.7.1", "_engineSupported": true, "_npmVersion": "0.3.15", "_nodeVersion": "v0.4.2", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.7.1.tgz", "shasum": "c83a5e834bf336d75d127a6402a66b177d23715f", "size": 42688, "noattachment": false, "integrity": "sha512-HRINll3zJZYbJlUvy3MIaptv/a9Toawf1I86SuMBhlwsPvh9uuIuIS6iU/T9MRVq2mxsSTXDqLaFYekHss1MRA=="}, "publish_time": 1299552741828, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1299552741828, "_cnpmcore_publish_time": "2021-12-13T13:32:30.047Z"}, "0.7.0": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.7.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": "0.2.0", "growl": "1.0.2"}, "_id": "stylus@0.7.0", "_engineSupported": true, "_npmVersion": "0.3.11", "_nodeVersion": "v0.4.1", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.7.0.tgz", "shasum": "f8269356cf59f8e5f0100f626afb20f6ceff06b2", "size": 45311, "noattachment": false, "integrity": "sha512-IeuVAvswejHD2sz6JuUgPC3xQXVmA2x5Tr1PR9gZBSrgQgw35xS4nUGZspUCRbmemnSALXbEzv9RF7PYAJkvsA=="}, "publish_time": 1299053521183, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1299053521183, "_cnpmcore_publish_time": "2021-12-13T13:32:32.547Z"}, "0.6.7": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.6.7", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": "0.2.0", "growl": "1.0.2"}, "_id": "stylus@0.6.7", "_engineSupported": true, "_npmVersion": "0.3.11", "_nodeVersion": "v0.4.1", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.6.7.tgz", "shasum": "7be1c6ee01e326abc7b98cde6f36802d4a7494e2", "size": 43677, "noattachment": false, "integrity": "sha512-mFGRZvdk5ZXfqxDh8Rg+zhNXZmbJkJd2Q4GFDsB1X3nORDZFcE6BwGtMxIc6u1I/IiKLz/5O1pp6QhsLX/zJbg=="}, "publish_time": 1299039224622, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1299039224622, "_cnpmcore_publish_time": "2021-12-13T13:32:34.752Z"}, "0.6.6": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.6.6", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index.js", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": "0.2.0", "growl": "1.0.2"}, "_id": "stylus@0.6.6", "_engineSupported": true, "_npmVersion": "0.3.11", "_nodeVersion": "v0.4.1", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.6.6.tgz", "shasum": "5a15f7c378de4a646719df80ae61a796dbccc10d", "size": 43623, "noattachment": false, "integrity": "sha512-ah4/SByoRSSSrsjVJSwym3gxxBjjT/1M8ArTQIcdDPiSAC4AzgqL53Pw+vKvmesJKwNTASNl+v95j5sixuvA9w=="}, "publish_time": 1299036851784, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1299036851784, "_cnpmcore_publish_time": "2021-12-13T13:32:36.930Z"}, "0.6.5": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.6.5", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": "0.2.0", "growl": "1.0.2"}, "_id": "stylus@0.6.5", "_engineSupported": true, "_npmVersion": "0.2.18", "_nodeVersion": "v0.4.1", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.6.5.tgz", "shasum": "5554f6b058cd4874a393d5e8b936b0d6b7bb5d23", "size": 42712, "noattachment": false, "integrity": "sha512-uQBtCzhZbsyedgAGjSUH9JPMJWZ+AKDKwBjx+fUJSVO3+axwZ30Mu9Z6IO1xQSQKeuazzFjaHUSNgPdr/4EKgg=="}, "publish_time": 1298617429817, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1298617429817, "_cnpmcore_publish_time": "2021-12-13T13:32:39.220Z"}, "0.6.4": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.6.4", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": "0.2.0", "growl": "1.0.2"}, "_id": "stylus@0.6.4", "_engineSupported": true, "_npmVersion": "0.2.18", "_nodeVersion": "v0.4.1", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.6.4.tgz", "shasum": "96925628e5b4712397677b8c36fcc488fd0ef31e", "size": 42719, "noattachment": false, "integrity": "sha512-e7q/RLHuAB8pvf6k4GcuC1LHgH2AhOsbkzQpuEUc6WkfxNFrnb5DYuHF+Bmce0ZEXGKhD+O1z8XpDdMK87jX/A=="}, "publish_time": 1298567404320, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1298567404320, "_cnpmcore_publish_time": "2021-12-13T13:32:41.290Z"}, "0.6.3": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.6.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": "0.2.0", "growl": "1.0.2"}, "_id": "stylus@0.6.3", "_engineSupported": true, "_npmVersion": "0.2.18", "_nodeVersion": "v0.4.1", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.6.3.tgz", "shasum": "bb6dad18bb94fd0b8c490c88d0de2edfb29ca3c2", "size": 42609, "noattachment": false, "integrity": "sha512-8LFgGPNGIOJMswt5KBqhrHLkLrJUQCoILhGX0ndKboEWtsnDoiWWyq4QAv6lDn7xSdRd+zTqgSojkHdaZM3svQ=="}, "publish_time": 1298402658863, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1298402658863, "_cnpmcore_publish_time": "2021-12-13T13:32:43.426Z"}, "0.6.2": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.6.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": "0.2.0", "growl": "1.0.2"}, "_id": "stylus@0.6.2", "_engineSupported": true, "_npmVersion": "0.2.18", "_nodeVersion": "v0.2.6", "directories": {"bin": "./bin", "lib": "./lib"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.6.2.tgz", "shasum": "29830c2c52b16003663998a9fe52b33db5dfc49f", "size": 42437, "noattachment": false, "integrity": "sha512-Qq/XM4/lKTpTYjaRHehFVdH4d5fAUDVNhk0jHC3Z6/ElzobGwxbGEtWoAAKd5k8r+kiqIEjG18zsxW200Y90xg=="}, "publish_time": 1298327300839, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1298327300839, "_cnpmcore_publish_time": "2021-12-13T13:32:46.047Z"}, "0.6.0": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.6.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": ">= 0.2.0"}, "_id": "stylus@0.6.0", "_engineSupported": true, "_npmVersion": "0.2.18", "_nodeVersion": "v0.4.0", "directories": {"lib": "./lib", "bin": "./bin"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.6.0.tgz", "shasum": "fce868e480a5bc9bfc424931046c06f363736af8", "size": 41548, "noattachment": false, "integrity": "sha512-4StLSnOSSUHiX39iVy3tumDaoLU7T9D5tUs1T/a8G5tlgNs5l/mnvjKbtvfc0QjjA/AZzXAQQeUJF3rVYqNpYg=="}, "publish_time": 1298048628821, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1298048628821, "_cnpmcore_publish_time": "2021-12-13T13:32:48.292Z"}, "0.5.3": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.5.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": ">= 0.2.0"}, "_id": "stylus@0.5.3", "_engineSupported": true, "_npmVersion": "0.2.18", "_nodeVersion": "v0.4.0", "directories": {"bin": "./bin", "lib": "./lib"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.5.3.tgz", "shasum": "c147f6c0df57c95767d64d26173b7d6ac50c8a36", "size": 41513, "noattachment": false, "integrity": "sha512-0bLC2WXVemQLQ2lj0jnoHe/bRSleAVPGxC0RGYXEva/59+n37Cx5vmuJ/7L0PRWaXmXVemlKN0fxk3ILNcIMBg=="}, "publish_time": 1297981738396, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1297981738396, "_cnpmcore_publish_time": "2021-12-13T13:32:50.430Z"}, "0.5.2": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.5.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": ">= 0.2.0"}, "_id": "stylus@0.5.2", "_engineSupported": true, "_npmVersion": "0.2.16", "_nodeVersion": "v0.4.0", "directories": {"lib": "./lib", "bin": "./bin"}, "modules": {"colors.js": "lib/colors.js", "lexer.js": "lib/lexer.js", "middleware.js": "lib/middleware.js", "parser.js": "lib/parser.js", "renderer.js": "lib/renderer.js", "stylus.js": "lib/stylus.js", "token.js": "lib/token.js", "utils.js": "lib/utils.js", "convert/css.js": "lib/convert/css.js", "functions/index.js": "lib/functions/index.js", "functions/image.js": "lib/functions/image.js", "functions/url.js": "lib/functions/url.js", "nodes/binop.js": "lib/nodes/binop.js", "nodes/block.js": "lib/nodes/block.js", "nodes/boolean.js": "lib/nodes/boolean.js", "nodes/call.js": "lib/nodes/call.js", "nodes/charset.js": "lib/nodes/charset.js", "nodes/color.js": "lib/nodes/color.js", "nodes/each.js": "lib/nodes/each.js", "nodes/expression.js": "lib/nodes/expression.js", "nodes/function.js": "lib/nodes/function.js", "nodes/group.js": "lib/nodes/group.js", "nodes/hsla.js": "lib/nodes/hsla.js", "nodes/ident.js": "lib/nodes/ident.js", "nodes/if.js": "lib/nodes/if.js", "nodes/import.js": "lib/nodes/import.js", "nodes/index.js": "lib/nodes/index.js", "nodes/keyframes.js": "lib/nodes/keyframes.js", "nodes/literal.js": "lib/nodes/literal.js", "nodes/media.js": "lib/nodes/media.js", "nodes/node.js": "lib/nodes/node.js", "nodes/null.js": "lib/nodes/null.js", "nodes/page.js": "lib/nodes/page.js", "nodes/params.js": "lib/nodes/params.js", "nodes/property.js": "lib/nodes/property.js", "nodes/return.js": "lib/nodes/return.js", "nodes/root.js": "lib/nodes/root.js", "nodes/selector.js": "lib/nodes/selector.js", "nodes/string.js": "lib/nodes/string.js", "nodes/ternary.js": "lib/nodes/ternary.js", "nodes/unaryop.js": "lib/nodes/unaryop.js", "nodes/unit.js": "lib/nodes/unit.js", "stack/frame.js": "lib/stack/frame.js", "stack/index.js": "lib/stack/index.js", "stack/scope.js": "lib/stack/scope.js", "visitor/compiler.js": "lib/visitor/compiler.js", "visitor/evaluator.js": "lib/visitor/evaluator.js", "visitor/index.js": "lib/visitor/index.js"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.5.2.tgz", "shasum": "c737a5a8540ed1725f1878eadb58994ef89e2b44", "size": 40515, "noattachment": false, "integrity": "sha512-Djp+csQ1/KSOhmS8b2bCtoMdHVb+Qeqqiz+nj0HXEe8is7a2XU1BsTsCf6LYDs8/tghInqVRO3heosEvS2/r2g=="}, "publish_time": 1297802876977, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1297802876977, "_cnpmcore_publish_time": "2021-12-13T13:32:53.092Z"}, "0.5.1": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.5.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": ">= 0.2.0"}, "_id": "stylus@0.5.1", "_engineSupported": true, "_npmVersion": "0.2.16", "_nodeVersion": "v0.4.0", "directories": {"lib": "./lib", "bin": "./bin"}, "modules": {"colors.js": "lib/colors.js", "lexer.js": "lib/lexer.js", "middleware.js": "lib/middleware.js", "parser.js": "lib/parser.js", "renderer.js": "lib/renderer.js", "stylus.js": "lib/stylus.js", "token.js": "lib/token.js", "utils.js": "lib/utils.js", "convert/css.js": "lib/convert/css.js", "functions/image.js": "lib/functions/image.js", "functions/index.js": "lib/functions/index.js", "functions/url.js": "lib/functions/url.js", "nodes/binop.js": "lib/nodes/binop.js", "nodes/block.js": "lib/nodes/block.js", "nodes/boolean.js": "lib/nodes/boolean.js", "nodes/call.js": "lib/nodes/call.js", "nodes/charset.js": "lib/nodes/charset.js", "nodes/color.js": "lib/nodes/color.js", "nodes/each.js": "lib/nodes/each.js", "nodes/expression.js": "lib/nodes/expression.js", "nodes/function.js": "lib/nodes/function.js", "nodes/group.js": "lib/nodes/group.js", "nodes/hsla.js": "lib/nodes/hsla.js", "nodes/ident.js": "lib/nodes/ident.js", "nodes/if.js": "lib/nodes/if.js", "nodes/import.js": "lib/nodes/import.js", "nodes/index.js": "lib/nodes/index.js", "nodes/keyframes.js": "lib/nodes/keyframes.js", "nodes/literal.js": "lib/nodes/literal.js", "nodes/media.js": "lib/nodes/media.js", "nodes/node.js": "lib/nodes/node.js", "nodes/null.js": "lib/nodes/null.js", "nodes/page.js": "lib/nodes/page.js", "nodes/params.js": "lib/nodes/params.js", "nodes/property.js": "lib/nodes/property.js", "nodes/return.js": "lib/nodes/return.js", "nodes/root.js": "lib/nodes/root.js", "nodes/selector.js": "lib/nodes/selector.js", "nodes/string.js": "lib/nodes/string.js", "nodes/ternary.js": "lib/nodes/ternary.js", "nodes/unaryop.js": "lib/nodes/unaryop.js", "nodes/unit.js": "lib/nodes/unit.js", "stack/frame.js": "lib/stack/frame.js", "stack/index.js": "lib/stack/index.js", "stack/scope.js": "lib/stack/scope.js", "visitor/compiler.js": "lib/visitor/compiler.js", "visitor/evaluator.js": "lib/visitor/evaluator.js", "visitor/index.js": "lib/visitor/index.js"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.5.1.tgz", "shasum": "52f3c59990b51203c80706a42371407fd09f020f", "size": 40479, "noattachment": false, "integrity": "sha512-Qho6j6JrOM/+SSAtTDde8HsqSzkAIkCRCkAE7pswFjylqQbmCM/mISWKEjx66PD+pRW4qX9eUk51Qnvj7wzCBg=="}, "publish_time": 1297467107118, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1297467107118, "_cnpmcore_publish_time": "2021-12-13T13:32:55.276Z"}, "0.5.0": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.5.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": ">= 0.2.0"}, "_id": "stylus@0.5.0", "_engineSupported": true, "_npmVersion": "0.2.16", "_nodeVersion": "v0.3.8", "directories": {"lib": "./lib", "bin": "./bin"}, "modules": {"colors.js": "lib/colors.js", "lexer.js": "lib/lexer.js", "middleware.js": "lib/middleware.js", "parser.js": "lib/parser.js", "renderer.js": "lib/renderer.js", "stylus.js": "lib/stylus.js", "token.js": "lib/token.js", "utils.js": "lib/utils.js", "convert/css.js": "lib/convert/css.js", "functions/image.js": "lib/functions/image.js", "functions/index.js": "lib/functions/index.js", "functions/url.js": "lib/functions/url.js", "nodes/binop.js": "lib/nodes/binop.js", "nodes/block.js": "lib/nodes/block.js", "nodes/boolean.js": "lib/nodes/boolean.js", "nodes/call.js": "lib/nodes/call.js", "nodes/charset.js": "lib/nodes/charset.js", "nodes/color.js": "lib/nodes/color.js", "nodes/each.js": "lib/nodes/each.js", "nodes/expression.js": "lib/nodes/expression.js", "nodes/function.js": "lib/nodes/function.js", "nodes/group.js": "lib/nodes/group.js", "nodes/hsla.js": "lib/nodes/hsla.js", "nodes/ident.js": "lib/nodes/ident.js", "nodes/if.js": "lib/nodes/if.js", "nodes/import.js": "lib/nodes/import.js", "nodes/index.js": "lib/nodes/index.js", "nodes/keyframes.js": "lib/nodes/keyframes.js", "nodes/literal.js": "lib/nodes/literal.js", "nodes/media.js": "lib/nodes/media.js", "nodes/node.js": "lib/nodes/node.js", "nodes/null.js": "lib/nodes/null.js", "nodes/page.js": "lib/nodes/page.js", "nodes/params.js": "lib/nodes/params.js", "nodes/property.js": "lib/nodes/property.js", "nodes/return.js": "lib/nodes/return.js", "nodes/root.js": "lib/nodes/root.js", "nodes/selector.js": "lib/nodes/selector.js", "nodes/string.js": "lib/nodes/string.js", "nodes/ternary.js": "lib/nodes/ternary.js", "nodes/unaryop.js": "lib/nodes/unaryop.js", "nodes/unit.js": "lib/nodes/unit.js", "stack/frame.js": "lib/stack/frame.js", "stack/index.js": "lib/stack/index.js", "stack/scope.js": "lib/stack/scope.js", "visitor/compiler.js": "lib/visitor/compiler.js", "visitor/evaluator.js": "lib/visitor/evaluator.js", "visitor/index.js": "lib/visitor/index.js"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.5.0.tgz", "shasum": "4db4b72288181fc8e0dc996defaeb80c4340cd55", "size": 40923, "noattachment": false, "integrity": "sha512-Bj0HatM/a44awBATOdDtJWXK3yyORHXeTZPBqMInSjGDl0EppdWfyK+WJh4JYX2qS5ysiZmBn607L2dE90LzoA=="}, "publish_time": 1297287015589, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1297287015589, "_cnpmcore_publish_time": "2021-12-13T13:32:57.393Z"}, "0.4.1": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.4.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": ">= 0.2.0"}, "_id": "stylus@0.4.1", "_engineSupported": true, "_npmVersion": "0.2.16", "_nodeVersion": "v0.3.8", "directories": {"lib": "./lib", "bin": "./bin"}, "modules": {"colors.js": "lib/colors.js", "lexer.js": "lib/lexer.js", "middleware.js": "lib/middleware.js", "parser.js": "lib/parser.js", "renderer.js": "lib/renderer.js", "stylus.js": "lib/stylus.js", "token.js": "lib/token.js", "utils.js": "lib/utils.js", "convert/css.js": "lib/convert/css.js", "functions/image.js": "lib/functions/image.js", "functions/index.js": "lib/functions/index.js", "functions/url.js": "lib/functions/url.js", "stack/frame.js": "lib/stack/frame.js", "stack/index.js": "lib/stack/index.js", "stack/scope.js": "lib/stack/scope.js", "nodes/binop.js": "lib/nodes/binop.js", "nodes/block.js": "lib/nodes/block.js", "nodes/boolean.js": "lib/nodes/boolean.js", "nodes/call.js": "lib/nodes/call.js", "nodes/charset.js": "lib/nodes/charset.js", "nodes/color.js": "lib/nodes/color.js", "nodes/each.js": "lib/nodes/each.js", "nodes/expression.js": "lib/nodes/expression.js", "nodes/function.js": "lib/nodes/function.js", "nodes/group.js": "lib/nodes/group.js", "nodes/hsla.js": "lib/nodes/hsla.js", "nodes/ident.js": "lib/nodes/ident.js", "nodes/if.js": "lib/nodes/if.js", "nodes/import.js": "lib/nodes/import.js", "nodes/index.js": "lib/nodes/index.js", "nodes/keyframes.js": "lib/nodes/keyframes.js", "nodes/literal.js": "lib/nodes/literal.js", "nodes/media.js": "lib/nodes/media.js", "nodes/node.js": "lib/nodes/node.js", "nodes/null.js": "lib/nodes/null.js", "nodes/page.js": "lib/nodes/page.js", "nodes/params.js": "lib/nodes/params.js", "nodes/property.js": "lib/nodes/property.js", "nodes/return.js": "lib/nodes/return.js", "nodes/root.js": "lib/nodes/root.js", "nodes/selector.js": "lib/nodes/selector.js", "nodes/string.js": "lib/nodes/string.js", "nodes/ternary.js": "lib/nodes/ternary.js", "nodes/unaryop.js": "lib/nodes/unaryop.js", "nodes/unit.js": "lib/nodes/unit.js", "visitor/compiler.js": "lib/visitor/compiler.js", "visitor/evaluator.js": "lib/visitor/evaluator.js", "visitor/index.js": "lib/visitor/index.js"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.4.1.tgz", "shasum": "93efcd1db687bcf950054ed86f5c063805c83113", "size": 40606, "noattachment": false, "integrity": "sha512-A9+PWEp1JBmXQSkb2diZQQ5f4DFGk/iS3MfzaVWIiR+/HZr9uj0my3IjUI3vy3nhfokqHu4ihdA8FWkkaJddkw=="}, "publish_time": 1297276508711, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1297276508711, "_cnpmcore_publish_time": "2021-12-13T13:32:59.932Z"}, "0.4.0": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.4.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": ">= 0.2.0"}, "_id": "stylus@0.4.0", "_engineSupported": true, "_npmVersion": "0.2.16", "_nodeVersion": "v0.2.6", "directories": {"lib": "./lib", "bin": "./bin"}, "modules": {"colors.js": "lib/colors.js", "lexer.js": "lib/lexer.js", "middleware.js": "lib/middleware.js", "parser.js": "lib/parser.js", "renderer.js": "lib/renderer.js", "stylus.js": "lib/stylus.js", "token.js": "lib/token.js", "utils.js": "lib/utils.js", "convert/css.js": "lib/convert/css.js", "nodes/binop.js": "lib/nodes/binop.js", "nodes/block.js": "lib/nodes/block.js", "nodes/boolean.js": "lib/nodes/boolean.js", "nodes/call.js": "lib/nodes/call.js", "nodes/charset.js": "lib/nodes/charset.js", "nodes/color.js": "lib/nodes/color.js", "nodes/each.js": "lib/nodes/each.js", "nodes/expression.js": "lib/nodes/expression.js", "nodes/function.js": "lib/nodes/function.js", "nodes/group.js": "lib/nodes/group.js", "nodes/hsla.js": "lib/nodes/hsla.js", "nodes/ident.js": "lib/nodes/ident.js", "nodes/if.js": "lib/nodes/if.js", "nodes/import.js": "lib/nodes/import.js", "nodes/index.js": "lib/nodes/index.js", "nodes/keyframes.js": "lib/nodes/keyframes.js", "nodes/literal.js": "lib/nodes/literal.js", "nodes/media.js": "lib/nodes/media.js", "nodes/node.js": "lib/nodes/node.js", "nodes/null.js": "lib/nodes/null.js", "nodes/page.js": "lib/nodes/page.js", "nodes/params.js": "lib/nodes/params.js", "nodes/property.js": "lib/nodes/property.js", "nodes/return.js": "lib/nodes/return.js", "nodes/root.js": "lib/nodes/root.js", "nodes/selector.js": "lib/nodes/selector.js", "nodes/string.js": "lib/nodes/string.js", "nodes/ternary.js": "lib/nodes/ternary.js", "nodes/unaryop.js": "lib/nodes/unaryop.js", "nodes/unit.js": "lib/nodes/unit.js", "functions/image.js": "lib/functions/image.js", "functions/index.js": "lib/functions/index.js", "functions/url.js": "lib/functions/url.js", "stack/frame.js": "lib/stack/frame.js", "stack/index.js": "lib/stack/index.js", "stack/scope.js": "lib/stack/scope.js", "visitor/compiler.js": "lib/visitor/compiler.js", "visitor/evaluator.js": "lib/visitor/evaluator.js", "visitor/index.js": "lib/visitor/index.js"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.4.0.tgz", "shasum": "52c9ad5d45643bb54bf2549ee9e1871e521575c1", "size": 39930, "noattachment": false, "integrity": "sha512-KT5bj20GYB5/h328ciDeAIbiTvb7KYHBaIlv/9XhcHnpS4idFExaW+lfydMzb8x3PYwQrXKaUWKYoKD9H2PUCg=="}, "publish_time": 1297106901209, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1297106901209, "_cnpmcore_publish_time": "2021-12-13T13:33:02.095Z"}, "0.3.1": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.3.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": ">= 0.2.0"}, "_id": "stylus@0.3.1", "_engineSupported": true, "_npmVersion": "0.2.16", "_nodeVersion": "v0.3.7", "directories": {"lib": "./lib", "bin": "./bin"}, "modules": {"colors.js": "lib/colors.js", "lexer.js": "lib/lexer.js", "middleware.js": "lib/middleware.js", "parser.js": "lib/parser.js", "renderer.js": "lib/renderer.js", "stylus.js": "lib/stylus.js", "token.js": "lib/token.js", "utils.js": "lib/utils.js", "convert/css.js": "lib/convert/css.js", "functions/image.js": "lib/functions/image.js", "functions/index.js": "lib/functions/index.js", "functions/url.js": "lib/functions/url.js", "nodes/binop.js": "lib/nodes/binop.js", "nodes/block.js": "lib/nodes/block.js", "nodes/boolean.js": "lib/nodes/boolean.js", "nodes/call.js": "lib/nodes/call.js", "nodes/charset.js": "lib/nodes/charset.js", "nodes/color.js": "lib/nodes/color.js", "nodes/each.js": "lib/nodes/each.js", "nodes/expression.js": "lib/nodes/expression.js", "nodes/function.js": "lib/nodes/function.js", "nodes/group.js": "lib/nodes/group.js", "nodes/hsla.js": "lib/nodes/hsla.js", "nodes/ident.js": "lib/nodes/ident.js", "nodes/if.js": "lib/nodes/if.js", "nodes/import.js": "lib/nodes/import.js", "nodes/index.js": "lib/nodes/index.js", "nodes/keyframes.js": "lib/nodes/keyframes.js", "nodes/literal.js": "lib/nodes/literal.js", "nodes/media.js": "lib/nodes/media.js", "nodes/node.js": "lib/nodes/node.js", "nodes/null.js": "lib/nodes/null.js", "nodes/page.js": "lib/nodes/page.js", "nodes/params.js": "lib/nodes/params.js", "nodes/property.js": "lib/nodes/property.js", "nodes/return.js": "lib/nodes/return.js", "nodes/root.js": "lib/nodes/root.js", "nodes/selector.js": "lib/nodes/selector.js", "nodes/string.js": "lib/nodes/string.js", "nodes/ternary.js": "lib/nodes/ternary.js", "nodes/unaryop.js": "lib/nodes/unaryop.js", "nodes/unit.js": "lib/nodes/unit.js", "stack/frame.js": "lib/stack/frame.js", "stack/index.js": "lib/stack/index.js", "stack/scope.js": "lib/stack/scope.js", "visitor/compiler.js": "lib/visitor/compiler.js", "visitor/evaluator.js": "lib/visitor/evaluator.js", "visitor/index.js": "lib/visitor/index.js"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.3.1.tgz", "shasum": "89eae9842b9e47a6c6b2b47756cd645275f2d4e2", "size": 39705, "noattachment": false, "integrity": "sha512-MTSro0eMLD9AOlptWldDWTD8zP64QPPjoZcQoxWc83SaQrld2KxRFRWJ8Xmdf8VcO4eGhsw0c0PIjB1g8lhFpA=="}, "publish_time": 1296843218616, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1296843218616, "_cnpmcore_publish_time": "2021-12-13T13:33:04.832Z"}, "0.3.0": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.3.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": ">= 0.2.0"}, "_id": "stylus@0.3.0", "_engineSupported": true, "_npmVersion": "0.2.16", "_nodeVersion": "v0.3.7", "directories": {"lib": "./lib", "bin": "./bin"}, "modules": {"colors.js": "lib/colors.js", "lexer.js": "lib/lexer.js", "middleware.js": "lib/middleware.js", "parser.js": "lib/parser.js", "renderer.js": "lib/renderer.js", "stylus.js": "lib/stylus.js", "token.js": "lib/token.js", "utils.js": "lib/utils.js", "convert/css.js": "lib/convert/css.js", "functions/image.js": "lib/functions/image.js", "functions/index.js": "lib/functions/index.js", "functions/url.js": "lib/functions/url.js", "nodes/binop.js": "lib/nodes/binop.js", "nodes/block.js": "lib/nodes/block.js", "nodes/boolean.js": "lib/nodes/boolean.js", "nodes/call.js": "lib/nodes/call.js", "nodes/charset.js": "lib/nodes/charset.js", "nodes/color.js": "lib/nodes/color.js", "nodes/each.js": "lib/nodes/each.js", "nodes/expression.js": "lib/nodes/expression.js", "nodes/function.js": "lib/nodes/function.js", "nodes/group.js": "lib/nodes/group.js", "nodes/hsla.js": "lib/nodes/hsla.js", "nodes/ident.js": "lib/nodes/ident.js", "nodes/if.js": "lib/nodes/if.js", "nodes/import.js": "lib/nodes/import.js", "nodes/index.js": "lib/nodes/index.js", "nodes/keyframes.js": "lib/nodes/keyframes.js", "nodes/literal.js": "lib/nodes/literal.js", "nodes/media.js": "lib/nodes/media.js", "nodes/node.js": "lib/nodes/node.js", "nodes/null.js": "lib/nodes/null.js", "nodes/page.js": "lib/nodes/page.js", "nodes/params.js": "lib/nodes/params.js", "nodes/property.js": "lib/nodes/property.js", "nodes/return.js": "lib/nodes/return.js", "nodes/root.js": "lib/nodes/root.js", "nodes/selector.js": "lib/nodes/selector.js", "nodes/string.js": "lib/nodes/string.js", "nodes/ternary.js": "lib/nodes/ternary.js", "nodes/unaryop.js": "lib/nodes/unaryop.js", "nodes/unit.js": "lib/nodes/unit.js", "stack/frame.js": "lib/stack/frame.js", "stack/index.js": "lib/stack/index.js", "stack/scope.js": "lib/stack/scope.js", "visitor/compiler.js": "lib/visitor/compiler.js", "visitor/evaluator.js": "lib/visitor/evaluator.js", "visitor/index.js": "lib/visitor/index.js"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.3.0.tgz", "shasum": "a616c476de59cfa4e66c3d53ff07d604232bd3c7", "size": 39352, "noattachment": false, "integrity": "sha512-Gp6foZoZPtdQ4+qgTk+AUQ3gaYk3rRUOtMqn5RwoxYr5+d0DpBBWC5qP7nIvLIOpabn0wPSClJGbnejWUCxiCg=="}, "publish_time": 1296841216656, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1296841216656, "_cnpmcore_publish_time": "2021-12-13T13:33:07.507Z"}, "0.2.1": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.2.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": ">= 0.2.0"}, "_id": "stylus@0.2.1", "_engineSupported": true, "_npmVersion": "0.2.16", "_nodeVersion": "v0.2.6", "directories": {"bin": "./bin", "lib": "./lib"}, "modules": {"colors.js": "lib/colors.js", "lexer.js": "lib/lexer.js", "middleware.js": "lib/middleware.js", "parser.js": "lib/parser.js", "renderer.js": "lib/renderer.js", "stylus.js": "lib/stylus.js", "token.js": "lib/token.js", "utils.js": "lib/utils.js", "convert/css.js": "lib/convert/css.js", "functions/image.js": "lib/functions/image.js", "functions/index.js": "lib/functions/index.js", "functions/url.js": "lib/functions/url.js", "nodes/binop.js": "lib/nodes/binop.js", "nodes/block.js": "lib/nodes/block.js", "nodes/boolean.js": "lib/nodes/boolean.js", "nodes/call.js": "lib/nodes/call.js", "nodes/charset.js": "lib/nodes/charset.js", "nodes/color.js": "lib/nodes/color.js", "nodes/each.js": "lib/nodes/each.js", "nodes/expression.js": "lib/nodes/expression.js", "nodes/function.js": "lib/nodes/function.js", "nodes/group.js": "lib/nodes/group.js", "nodes/hsla.js": "lib/nodes/hsla.js", "nodes/ident.js": "lib/nodes/ident.js", "nodes/if.js": "lib/nodes/if.js", "nodes/import.js": "lib/nodes/import.js", "nodes/index.js": "lib/nodes/index.js", "nodes/keyframes.js": "lib/nodes/keyframes.js", "nodes/literal.js": "lib/nodes/literal.js", "nodes/media.js": "lib/nodes/media.js", "nodes/node.js": "lib/nodes/node.js", "nodes/null.js": "lib/nodes/null.js", "nodes/page.js": "lib/nodes/page.js", "nodes/params.js": "lib/nodes/params.js", "nodes/property.js": "lib/nodes/property.js", "nodes/return.js": "lib/nodes/return.js", "nodes/root.js": "lib/nodes/root.js", "nodes/selector.js": "lib/nodes/selector.js", "nodes/string.js": "lib/nodes/string.js", "nodes/ternary.js": "lib/nodes/ternary.js", "nodes/unaryop.js": "lib/nodes/unaryop.js", "nodes/unit.js": "lib/nodes/unit.js", "stack/frame.js": "lib/stack/frame.js", "stack/index.js": "lib/stack/index.js", "stack/scope.js": "lib/stack/scope.js", "visitor/compiler.js": "lib/visitor/compiler.js", "visitor/evaluator.js": "lib/visitor/evaluator.js", "visitor/index.js": "lib/visitor/index.js"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.2.1.tgz", "shasum": "b3c1e51208fdc83ebe782fe4ad8b0ce65891f1ea", "size": 39740, "noattachment": false, "integrity": "sha512-OoSl+K+lFtUjfHG0i8PtCSaELiqJABmCisvUCRi27r9wVxayFmySqViddM8XCRhTXO1WgSnggIBayg3WxX9Reg=="}, "publish_time": 1296666569991, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1296666569991, "_cnpmcore_publish_time": "2021-12-13T13:33:10.559Z"}, "0.2.0": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.2.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": ">= 0.2.0"}, "_id": "stylus@0.2.0", "_engineSupported": true, "_npmVersion": "0.2.15", "_nodeVersion": "v0.2.6", "directories": {"lib": "./lib", "bin": "./bin"}, "modules": {"colors.js": "lib/colors.js", "lexer.js": "lib/lexer.js", "middleware.js": "lib/middleware.js", "parser.js": "lib/parser.js", "renderer.js": "lib/renderer.js", "stylus.js": "lib/stylus.js", "token.js": "lib/token.js", "utils.js": "lib/utils.js", "convert/css.js": "lib/convert/css.js", "functions/image.js": "lib/functions/image.js", "functions/index.js": "lib/functions/index.js", "functions/url.js": "lib/functions/url.js", "nodes/binop.js": "lib/nodes/binop.js", "nodes/block.js": "lib/nodes/block.js", "nodes/boolean.js": "lib/nodes/boolean.js", "nodes/call.js": "lib/nodes/call.js", "nodes/charset.js": "lib/nodes/charset.js", "nodes/color.js": "lib/nodes/color.js", "nodes/each.js": "lib/nodes/each.js", "nodes/expression.js": "lib/nodes/expression.js", "nodes/function.js": "lib/nodes/function.js", "nodes/group.js": "lib/nodes/group.js", "nodes/hsla.js": "lib/nodes/hsla.js", "nodes/ident.js": "lib/nodes/ident.js", "nodes/if.js": "lib/nodes/if.js", "nodes/import.js": "lib/nodes/import.js", "nodes/index.js": "lib/nodes/index.js", "nodes/keyframes.js": "lib/nodes/keyframes.js", "nodes/literal.js": "lib/nodes/literal.js", "nodes/media.js": "lib/nodes/media.js", "nodes/node.js": "lib/nodes/node.js", "nodes/null.js": "lib/nodes/null.js", "nodes/page.js": "lib/nodes/page.js", "nodes/params.js": "lib/nodes/params.js", "nodes/property.js": "lib/nodes/property.js", "nodes/return.js": "lib/nodes/return.js", "nodes/root.js": "lib/nodes/root.js", "nodes/selector.js": "lib/nodes/selector.js", "nodes/string.js": "lib/nodes/string.js", "nodes/ternary.js": "lib/nodes/ternary.js", "nodes/unaryop.js": "lib/nodes/unaryop.js", "nodes/unit.js": "lib/nodes/unit.js", "stack/frame.js": "lib/stack/frame.js", "stack/index.js": "lib/stack/index.js", "stack/scope.js": "lib/stack/scope.js", "visitor/compiler.js": "lib/visitor/compiler.js", "visitor/evaluator.js": "lib/visitor/evaluator.js", "visitor/index.js": "lib/visitor/index.js"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.2.0.tgz", "shasum": "d60a2475260e053cf87b277cbf073cd4e1da55df", "size": 39001, "noattachment": false, "integrity": "sha512-iAkDEw7cwqt8a4AUMOVPN1WhPm9rlA9k+ckSIhkgnDq8+GBLfxRqN7J2SHCvsdHWQlZ3o5wjSfCMbI1sHx4PWQ=="}, "publish_time": 1296589711360, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1296589711360, "_cnpmcore_publish_time": "2021-12-13T13:33:12.977Z"}, "0.1.0": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.1.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": ">= 0.2.0"}, "_id": "stylus@0.1.0", "_engineSupported": true, "_npmVersion": "0.2.15", "_nodeVersion": "v0.2.6", "directories": {"lib": "./lib", "bin": "./bin"}, "modules": {"colors.js": "lib/colors.js", "lexer.js": "lib/lexer.js", "middleware.js": "lib/middleware.js", "parser.js": "lib/parser.js", "renderer.js": "lib/renderer.js", "stylus.js": "lib/stylus.js", "token.js": "lib/token.js", "utils.js": "lib/utils.js", "convert/css.js": "lib/convert/css.js", "functions/image.js": "lib/functions/image.js", "functions/index.js": "lib/functions/index.js", "functions/url.js": "lib/functions/url.js", "nodes/binop.js": "lib/nodes/binop.js", "nodes/block.js": "lib/nodes/block.js", "nodes/boolean.js": "lib/nodes/boolean.js", "nodes/call.js": "lib/nodes/call.js", "nodes/charset.js": "lib/nodes/charset.js", "nodes/color.js": "lib/nodes/color.js", "nodes/each.js": "lib/nodes/each.js", "nodes/expression.js": "lib/nodes/expression.js", "nodes/function.js": "lib/nodes/function.js", "nodes/group.js": "lib/nodes/group.js", "nodes/hsla.js": "lib/nodes/hsla.js", "nodes/ident.js": "lib/nodes/ident.js", "nodes/if.js": "lib/nodes/if.js", "nodes/import.js": "lib/nodes/import.js", "nodes/index.js": "lib/nodes/index.js", "nodes/keyframes.js": "lib/nodes/keyframes.js", "nodes/literal.js": "lib/nodes/literal.js", "nodes/media.js": "lib/nodes/media.js", "nodes/node.js": "lib/nodes/node.js", "nodes/null.js": "lib/nodes/null.js", "nodes/page.js": "lib/nodes/page.js", "nodes/params.js": "lib/nodes/params.js", "nodes/property.js": "lib/nodes/property.js", "nodes/return.js": "lib/nodes/return.js", "nodes/root.js": "lib/nodes/root.js", "nodes/selector.js": "lib/nodes/selector.js", "nodes/string.js": "lib/nodes/string.js", "nodes/ternary.js": "lib/nodes/ternary.js", "nodes/unaryop.js": "lib/nodes/unaryop.js", "nodes/unit.js": "lib/nodes/unit.js", "stack/frame.js": "lib/stack/frame.js", "stack/index.js": "lib/stack/index.js", "stack/scope.js": "lib/stack/scope.js", "visitor/compiler.js": "lib/visitor/compiler.js", "visitor/evaluator.js": "lib/visitor/evaluator.js", "visitor/index.js": "lib/visitor/index.js"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.1.0.tgz", "shasum": "7986729d88a3fb4585a866f4e03c068c44728833", "size": 39463, "noattachment": false, "integrity": "sha512-ew6NK8/Y1aNtmjh2fD72oTau5T50+XcQq12egJL52hmQuBGBhL26kjQC+W5VBDxc0TzrNN5DDrcIAT/DSdAKjw=="}, "publish_time": 1296581982414, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1296581982414, "_cnpmcore_publish_time": "2021-12-13T13:33:15.731Z"}, "0.0.2": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.0.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": ">= 0.2.0"}, "_id": "stylus@0.0.2", "_engineSupported": true, "_npmVersion": "0.2.15", "_nodeVersion": "v0.2.6", "directories": {"lib": "./lib", "bin": "./bin"}, "modules": {"colors.js": "lib/colors.js", "lexer.js": "lib/lexer.js", "middleware.js": "lib/middleware.js", "parser.js": "lib/parser.js", "renderer.js": "lib/renderer.js", "stylus.js": "lib/stylus.js", "token.js": "lib/token.js", "utils.js": "lib/utils.js", "convert/css.js": "lib/convert/css.js", "functions/index.js": "lib/functions/index.js", "functions/url.js": "lib/functions/url.js", "nodes/binop.js": "lib/nodes/binop.js", "nodes/block.js": "lib/nodes/block.js", "nodes/boolean.js": "lib/nodes/boolean.js", "nodes/call.js": "lib/nodes/call.js", "nodes/charset.js": "lib/nodes/charset.js", "nodes/color.js": "lib/nodes/color.js", "nodes/each.js": "lib/nodes/each.js", "nodes/expression.js": "lib/nodes/expression.js", "nodes/function.js": "lib/nodes/function.js", "nodes/group.js": "lib/nodes/group.js", "nodes/hsla.js": "lib/nodes/hsla.js", "nodes/ident.js": "lib/nodes/ident.js", "nodes/if.js": "lib/nodes/if.js", "nodes/import.js": "lib/nodes/import.js", "nodes/index.js": "lib/nodes/index.js", "nodes/keyframes.js": "lib/nodes/keyframes.js", "nodes/literal.js": "lib/nodes/literal.js", "nodes/media.js": "lib/nodes/media.js", "nodes/node.js": "lib/nodes/node.js", "nodes/null.js": "lib/nodes/null.js", "nodes/page.js": "lib/nodes/page.js", "nodes/params.js": "lib/nodes/params.js", "nodes/property.js": "lib/nodes/property.js", "nodes/return.js": "lib/nodes/return.js", "nodes/root.js": "lib/nodes/root.js", "nodes/selector.js": "lib/nodes/selector.js", "nodes/string.js": "lib/nodes/string.js", "nodes/ternary.js": "lib/nodes/ternary.js", "nodes/unaryop.js": "lib/nodes/unaryop.js", "nodes/unit.js": "lib/nodes/unit.js", "stack/frame.js": "lib/stack/frame.js", "stack/index.js": "lib/stack/index.js", "stack/scope.js": "lib/stack/scope.js", "visitor/compiler.js": "lib/visitor/compiler.js", "visitor/evaluator.js": "lib/visitor/evaluator.js", "visitor/index.js": "lib/visitor/index.js"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.0.2.tgz", "shasum": "11b930e2dc00268062cb9e46ba1bfa04b2c7d5ad", "size": 36991, "noattachment": false, "integrity": "sha512-OlgaCGoVB3xcCdKNT+QQiJUM5bh8PahDGRh4B9mOrOnAxUX089TrFdMdDFX254c+qB5Zm+c9R4L1th4xZfAuJw=="}, "publish_time": 1296504856038, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1296504856038, "_cnpmcore_publish_time": "2021-12-13T13:33:18.110Z"}, "0.0.1": {"name": "stylus", "description": "Robust, expressive language which compiles to CSS", "version": "0.0.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "main": "./index", "engines": {"node": ">= 0.2.4"}, "bin": {"stylus": "./bin/stylus", "stylus-tutorial": "./bin/stylus-tutorial"}, "dependencies": {"cssom": ">= 0.2.0"}, "_id": "stylus@0.0.1", "_engineSupported": true, "_npmVersion": "0.2.15", "_nodeVersion": "v0.2.6", "directories": {"bin": "./bin", "lib": "./lib"}, "modules": {"colors.js": "lib/colors.js", "lexer.js": "lib/lexer.js", "middleware.js": "lib/middleware.js", "parser.js": "lib/parser.js", "renderer.js": "lib/renderer.js", "stylus.js": "lib/stylus.js", "token.js": "lib/token.js", "utils.js": "lib/utils.js", "convert/css.js": "lib/convert/css.js", "functions/index.js": "lib/functions/index.js", "functions/url.js": "lib/functions/url.js", "nodes/binop.js": "lib/nodes/binop.js", "nodes/block.js": "lib/nodes/block.js", "nodes/boolean.js": "lib/nodes/boolean.js", "nodes/call.js": "lib/nodes/call.js", "nodes/charset.js": "lib/nodes/charset.js", "nodes/color.js": "lib/nodes/color.js", "nodes/each.js": "lib/nodes/each.js", "nodes/expression.js": "lib/nodes/expression.js", "nodes/function.js": "lib/nodes/function.js", "nodes/group.js": "lib/nodes/group.js", "nodes/hsla.js": "lib/nodes/hsla.js", "nodes/ident.js": "lib/nodes/ident.js", "nodes/if.js": "lib/nodes/if.js", "nodes/import.js": "lib/nodes/import.js", "nodes/index.js": "lib/nodes/index.js", "nodes/keyframes.js": "lib/nodes/keyframes.js", "nodes/literal.js": "lib/nodes/literal.js", "nodes/media.js": "lib/nodes/media.js", "nodes/node.js": "lib/nodes/node.js", "nodes/null.js": "lib/nodes/null.js", "nodes/page.js": "lib/nodes/page.js", "nodes/params.js": "lib/nodes/params.js", "nodes/property.js": "lib/nodes/property.js", "nodes/return.js": "lib/nodes/return.js", "nodes/root.js": "lib/nodes/root.js", "nodes/selector.js": "lib/nodes/selector.js", "nodes/string.js": "lib/nodes/string.js", "nodes/ternary.js": "lib/nodes/ternary.js", "nodes/unaryop.js": "lib/nodes/unaryop.js", "nodes/unit.js": "lib/nodes/unit.js", "stack/frame.js": "lib/stack/frame.js", "stack/index.js": "lib/stack/index.js", "stack/scope.js": "lib/stack/scope.js", "visitor/compiler.js": "lib/visitor/compiler.js", "visitor/evaluator.js": "lib/visitor/evaluator.js", "visitor/index.js": "lib/visitor/index.js"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.0.1.tgz", "shasum": "102c5826838e7f8cc6e582cf990baa72c33fc7df", "size": 37083, "noattachment": false, "integrity": "sha512-FA5IBXw76vp5Vo3w5RmhRDydoZN4XJbhRq03hMsu5kBp2EnieFZkTCimtH10WqmSOpRUGv4+Z5AelfFEi8lkHQ=="}, "publish_time": 1296498130089, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1296498130089, "_cnpmcore_publish_time": "2021-12-13T13:33:20.669Z"}, "0.56.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.56.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus.git"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require chai --bail --check-leaks --reporter dot", "test-cov": "mocha test/ test/middleware/ --require chai --bail --reporter html-cov > coverage.html"}, "dependencies": {"css": "^3.0.0", "debug": "^4.3.2", "glob": "^7.1.6", "safer-buffer": "^2.1.2", "sax": "~1.2.4", "source-map": "^0.7.3"}, "devDependencies": {"chai": "^4.3.4", "jscoverage": "~0.6.0", "mocha": "^9.0.3"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "gitHead": "fc2e6306a2fcfad6f1213693b26254bee3f1de6b", "_id": "stylus@0.56.0", "_nodeVersion": "14.17.4", "_npmVersion": "6.14.14", "dist": {"integrity": "sha512-Ev3fOb4bUElwWu4F9P9WjnnaSpc8XB9OFHSFZSKMFL1CE1oM+oFXWEgAqPmmZIyhBihuqIQlFsVTypiiS9RxeA==", "shasum": "13fc85c48082db483c90d2530942fe8b0be988eb", "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.56.0.tgz", "fileCount": 144, "unpackedSize": 425578, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhvUSzCRA9TVsSAnZWagAAgRIQAIIMP0Iwu/P1lDZb2Ok1\n/aMDZ304PyUKRChwoQb12Y5TjQMj7vJ+vZamSvxIDBzcdv576NU2WLOKc1NO\nOK4aXN6VZQ/rk5oDGKB4aXy669jH2V3QAth5tQ96Yr9/aFj1S63Ih/Irr6q+\nCX5QA6RB9DckXLhQbf4EMYOr5bdp/7wmZSbOZzAV5o1eQm1WgoLTjoZIS8n5\n7WxfAfYMC4zByErp/fBuEB6Iq5NS/qPR3tkptPCuYPuRYatzMPWyGrwxTSrl\n6lKy5fv4KEZjDWmolQsCirluLg3+tcMiOix2FsBqtgGS7LIK0/scQhFPCwPm\nF9eFbZYCsSoAaXhdN/WxfdZg9mZ6V8tuQLtlhOWUCZrL9bkDoBIOqOHtiG0m\nWjotNQZL0MpMicpp0QB0w6mJHcAR2YQNZ2FNPz0/g9ACu+XFpSQVk52/nP+g\nTMjlqgaaZQIJxEJNDmfmyh8Tl4kUFFmLsdl/aeVylM2ayBoboKMU1KMgNQyr\nNPl8fm1bVWFm28VF23oDlpRVPSoZqXg0IXBFS7kEwIiLeh6kjBv525z23rHZ\nR13RxmEh25i7Df+3ZmxNT06/vbE6M5GG7WVEZT4yVKaA8x8ij+2o+/MY9Xqv\ncpMl8QcDkLkOabTrOUDPHCuP41GBBwWfdWDWwIghOgqT8zy7iWuMXTLMvcsk\nMo5f\r\n=fF00\r\n-----END PGP SIGNATURE-----\r\n", "size": 106873, "noattachment": false}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/stylus_0.56.0_1639793842844_0.7308729074233775"}, "_hasShrinkwrap": false, "publish_time": 1639793843044, "_cnpm_publish_time": 1639793843044, "_cnpmcore_publish_time": "2021-12-18T14:32:51.010Z"}, "0.57.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.57.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus.git"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require chai --bail --check-leaks --reporter dot", "test-cov": "mocha test/ test/middleware/ --require chai --bail --reporter html-cov > coverage.html"}, "dependencies": {"css": "^3.0.0", "debug": "^4.3.2", "glob": "^7.1.6", "safer-buffer": "^2.1.2", "sax": "~1.2.4", "source-map": "^0.7.3"}, "devDependencies": {"chai": "^4.3.6", "mocha": "^9.2.0"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "readmeFilename": "Readme.md", "gitHead": "bc1404aa1f6c03341bd76529c8cf4beb4f3d99f7", "_id": "stylus@0.57.0", "_nodeVersion": "14.17.4", "_npmVersion": "6.14.14", "dist": {"integrity": "sha512-yOI6G8WYfr0q8v8rRvE91wbxFU+rJPo760Va4MF6K0I6BZjO4r+xSynkvyPBP9tV1CIEUeRsiidjIs2rzb1CnQ==", "shasum": "a46f04f426c19ceef54abb1a9d189fd4e886df41", "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.57.0.tgz", "fileCount": 144, "unpackedSize": 425627, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiEKtgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoGcw/+M0Iq0yEPjZWWyijRlxtVEwWQfOWlAbSub/PTjGaqKZimFWJ6\r\ni1F/ohGvXFX+YuLkpE+7f8Rhz1lNsZsVsaOPCKyYu3B3KQK+EsgQKPuGB7kG\r\nxI02fIbEzSmZZfcXpsirHpO9+SBcdF5hd3KilQ9k4khoH0g7Mtn6V+LZw7kS\r\nGbwdYfvSPiKsxrLoudcvYSPXb9ey/PGvdi2rTp3c27LJV8+WqtfmisOrUpMO\r\n+NB0ekH4Ix1KAUW+RCmBA92x7bP7/Nv31QNrwHkEqrbrPPwoYWKZZ3O9AY/t\r\ngdj5v3QF+C6FZ+0EVar8hwGJ3vh3Ppa/EnL5Ihxw+85usxyiUMrRNAIi6t7f\r\no8WTpdqgio4mV13H5RBGdjG+1wtLhwhQ5kBPExgITtiy8bIi2VscuFUhETZG\r\nFhZy62XufYyjJnh0BUDBXafzLKM22au5HEFkWO4/HhfEOTDXw8qvcSJuQvNY\r\ncM78+HsxmSjgyxwcqBhSgSIJI67M8HyptaPDUPA3YSo0DCkyCpCsfO4rEh/f\r\ncqjvQu2su5MR8CT1MNTQubeq96vQgZykDyFrFMfYhZb8AAfxkKga8wNaL6b2\r\nf80O79R4+nfIodMIm3C0iQeunNx4gKfS7y4aoqJPgsFH4/sMieceyu7D4X1f\r\nyqI3ZJ6GU5L5vC7n4koPMek0joT993b05R4=\r\n=atv0\r\n-----END PGP SIGNATURE-----\r\n", "size": 106870}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}, {"name": "xdan", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/stylus_0.57.0_1645259616701_0.5520700629008688"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-02-19T08:33:45.722Z"}, "0.58.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.58.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus.git"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require chai --bail --check-leaks --reporter dot"}, "dependencies": {"css": "^3.0.0", "debug": "^4.3.2", "glob": "^7.1.6", "sax": "~1.2.4", "source-map": "^0.7.3"}, "devDependencies": {"chai": "^4.3.6", "mocha": "^9.2.0"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "gitHead": "914505ff73b457eef80713344dc072170ae8337a", "_id": "stylus@0.58.0", "_nodeVersion": "14.15.3", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-q27Pof0PINInv/gTO1AH+6C1VkoedEOPp38CB/DFWdSgu4fBCwnxM8cZEm4M1zU+Jr6rmZ75hkmqk2QyDze10g==", "shasum": "1125d8381173356b22c675f165c17da3ec81f5ff", "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.58.0.tgz", "fileCount": 144, "unpackedSize": 445017, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH1nw1mrd3PYPvdDHkbACJi0mMzAYdwyvSLGD7sdUwLiAiAzi6ouHyhHGUiyAW33i1+iumU80ZH56aWsC7BsKnE6KQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikdSAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrOjBAAl5HdPIj7R8d5PDYKsnTc1lb5VlUHhpNAVjLFXx874Cp9HHEY\r\nWpIFIc7JVmaXhUlvUIRF86OlHd09grAN4JHgjGiwXTruMSG1bPXTBotOGqko\r\n9UHIdMN+tkOVVtXX8MyxsrJHjFwPLL0+aGoZgBCPNr+32AjtFGYnyDeBaBO1\r\na9M4K29I92Gj/twAVUZBYADfSi6NRZtDePvK7jyfYONA6t4q0pqDp1n99CqC\r\n5Np3/pU7nd9OdobCgD1g/mdDSiX7r3Q4mR63nvd3Pkglnf6oZaYzwmbUt2+G\r\n+Mn7vsDDFVFfleehLXfCA0oEnGHJER+MOgyfstWNGTwXrUPzKeOsoyELk/1A\r\n7z/+3muvT58mriD26g145TOZ5zkrybBm95X1J8DIQu18I/gHiEAAH3x0VIei\r\ndk/wgDrQn82quh83Y/b/oOnXj5XKLLBq40tApocJhwsTY92Tli0lJVJZ706l\r\n2/WAPnOEclgNi7kMiehKsCbUB2GOIEgS4G2jB/KPkrOPzEc0zKivV4x1GKYA\r\nkuc+qBrOx+I9Qqze8uolSpfMOwU5HU9q9l54edHvZoHEymXyJLjyatVfUsnx\r\nZA7Rc0QfYy3RoUL4n8c8WwdvaovewFMyV5mNMBiKtNhHAYyMDRNZlVBd3QQb\r\neT+qXPrG630fYt1i+ut9xluZs/FS38UNtHg=\r\n=SZBg\r\n-----END PGP SIGNATURE-----\r\n", "size": 108193}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}, {"name": "xdan", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/stylus_0.58.0_1653724287754_0.12240211397907674"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-05-28T07:52:59.819Z"}, "0.58.1": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.58.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus.git"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require chai --bail --check-leaks --reporter dot"}, "dependencies": {"css": "^3.0.0", "debug": "^4.3.2", "glob": "^7.1.6", "sax": "~1.2.4", "source-map": "^0.7.3"}, "devDependencies": {"chai": "^4.3.6", "mocha": "^9.2.0"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "gitHead": "52db72cdfa57f6e3545d68a10976ba334c4ce851", "_id": "stylus@0.58.1", "_nodeVersion": "14.15.3", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-AYiCHm5ogczdCPMfe9aeQa4NklB2gcf4D/IhzYPddJjTgPc+k4D/EVE0yfQbZD43MHP3lPy+8NZ9fcFxkrgs/w==", "shasum": "7e425bb493c10dde94cf427a138d3eae875a3b44", "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.58.1.tgz", "fileCount": 144, "unpackedSize": 426443, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBUREBADn5+PLvcRChdSrGY8760f2sfrQkecEhX8JaVZAiEAiUoU8S9bL+cSEm6iEBzSqUGWm4QiAMuSmwif5KhiekM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJilbxsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNtA//bCtg/Lp7n8arc+aKtiA3E2eRznI/ZCWZBLtQgMBDJ12nTUpt\r\nxjfhtuWAmG3UlhSikmuc4iWQtmcZNG+lzYptUP4CLqa+zOaE73EMkLa5j6lh\r\n2eAcU4+u3AEbZaWJzJ+ItzasxcIOlQ4u74nBWwsLSfdPFZ8yREXMIEK4naMv\r\n68o1yAiyboGmIlBNWPO8h5ivjH4cJiYZrKu873Krz4+kGWOB22+QifbA4Zuc\r\nfit5yc6lEhUiz215c4+cCha<PERSON>Z3EwZwRhgMg5d7u+xNyoMBzwI4ybu2BRbyGt\r\n2A+OD/QtVOlP4VjTJO4UO6bq/KC/9NngE9pQPHlnLc6Zop/pDSn11vpKsbuc\r\nVl7rtS4pvSmog8QcliV1OQgYKF2+DhOfNi0fhCJmwPDJXYCnIPw2hD37mLxf\r\nwFXhCD1PbHuowwoALVJeRKRbWfbJGR8X957BDjta4U2ONvSHTaEbigAuyUKV\r\nfn6pErFQ6sNswo60x+edn93G26baFsLlPRCi4yxOFuDZIQfpPscwHbPNHqVM\r\nFzE90SO0mEmZ5sFa13ZutaygWgPDu9jAaqAg9xBxvIx2o2IxLYrqxMVDt+nR\r\nSuEh9fjZXj9LV9uGOjCh5L1y1J/USGuhzxoUH7I6vppfJmGQtiBMxG/tIoeh\r\nv5nIRg+ujpfsCf6wWKxUKQAlFuAi9FihbRs=\r\n=qySn\r\n-----END PGP SIGNATURE-----\r\n", "size": 107176}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}, {"name": "xdan", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/stylus_0.58.1_1653980268522_0.5937306415525998"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-05-31T09:48:42.152Z"}, "0.59.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.59.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus.git"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require chai --bail --check-leaks --reporter dot"}, "dependencies": {"@adobe/css-tools": "^4.0.1", "debug": "^4.3.2", "glob": "^7.1.6", "sax": "~1.2.4", "source-map": "^0.7.3"}, "devDependencies": {"chai": "^4.3.6", "mocha": "^9.2.0"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "funding": "https://opencollective.com/stylus", "gitHead": "8e29ff39fb563f91ef4eaff9b2c9f29e2a1910ec", "_id": "stylus@0.59.0", "_nodeVersion": "14.15.3", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-lQ9w/XIOH5ZHVNuNbWW8D822r+/wBSO/d6XvtyHLF7LW4KaCIDeVbvn5DF8fGCJAUCwVhVi/h6J0NUcnylUEjg==", "shasum": "a344d5932787142a141946536d6e24e6a6be7aa6", "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.59.0.tgz", "fileCount": 144, "unpackedSize": 421343, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBDeiQsaeoucR9d27DpPF2pDt7BqMESzdIcti0LshDc6AiBCh2DMptvL6SL1iUnOQJQNl7ziB/D52357DpgtsPYNlw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi9xjKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoZIQ/+O9dOMsA0JYe11eKWr1SQ7VrdDu6WxIRdBpc1rxLAlcdyT3dy\r\nnjkXH+nFeSIScpbSu+MEVX5LJWbnLEweZ3GgZ+niRF1qz6QdmrnG0D2vGChL\r\nk5aLEARFf9/aoEiqrL/bMUQ2kBA7NyeaoawLBmuPHDhmvjjT19myXZJPQDOJ\r\nVXFX+61qUa14ZVkvpnPK7NYVnFd5PKb2JRVYGy8V1/+wRLeFgj1p8jtNoLmB\r\ncLd1J6/qPYIR9vwR6KdF0s/SHuQPif8ZaMtVVbilTakw9OTUerhoA5dhXH4a\r\nDE51f4zG/5vc8RAv73Nvd0I6HzAhsQqV3ErYvsiAhapAv8DD8RKObIT8w47t\r\nN6bEcrgbgp6GTxj5TJz4NSHrtu5jgW7WCxzNAB6lwjZAk0rUM98PnktVTnYR\r\n5x5jzHbDd9OEnJG+EMh9VfJRauF2DLcURFED1utjzysNx5Z//3gqZzcNwXdz\r\njdyYbIkXBn4Qe72kRRRCa6QiK+zLp+Upb4Qy1cAHSbGZdqwU/7WfurSn0XSO\r\nt/Mwy69mJozM9F/pOVEy4tlVv+tLk8kH7o1g1p7ujQGUw/WeaxcbTztacb8Q\r\n4RmOnEbRi1PlxF9JBK+0PIT8p3zIDlYRFe7ay+goOyLfcDSYXpqf6w6F3aZi\r\nqi3qWf+R9a33BFL/RCKSI0ebhmnK+p/EfAs=\r\n=lk9r\r\n-----END PGP SIGNATURE-----\r\n", "size": 105455}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}, {"name": "xdan", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/stylus_0.59.0_1660360905846_0.3704175935070799"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-08-13T03:22:23.687Z"}, "0.60.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.60.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus.git"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require chai --bail --check-leaks --reporter dot"}, "dependencies": {"@adobe/css-tools": "~4.2.0", "debug": "^4.3.2", "glob": "^7.1.6", "sax": "~1.2.4", "source-map": "^0.7.3"}, "devDependencies": {"chai": "^4.3.6", "mocha": "^9.2.0"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "funding": "https://opencollective.com/stylus", "_id": "stylus@0.60.0", "gitHead": "74efabf971b7fec4fccd93eb6fb9caac1d621bcf", "_nodeVersion": "20.5.1", "_npmVersion": "9.8.0", "dist": {"integrity": "sha512-j2pBgEwzCu05yCuY4cmyp0FtPQQFBBAGB7TY7QaNl7eztiHwkxzwvIp5vjZJND/a1JNOka+ZW9ewVPFZpI3pcA==", "shasum": "0d75f3772929185d580d164d9394b2dcbed21083", "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.60.0.tgz", "fileCount": 144, "unpackedSize": 366495, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCNtKlNu9VIw31oQQplP6GlpEN+pGvNRcTq5FmCyo4D4QIhANMylwv3llKh9dr/laK7g15vNSS/9kpw2AKE2zhK7SaC"}], "size": 87428}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}, {"name": "xdan", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/stylus_0.60.0_1693371571127_0.2438799278555832"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-30T04:59:31.328Z", "publish_time": 1693371571328, "_source_registry_name": "default"}, "0.61.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.61.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus.git"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require chai --bail --check-leaks --reporter dot"}, "dependencies": {"@adobe/css-tools": "~4.3.1", "debug": "^4.3.2", "glob": "^7.1.6", "sax": "~1.3.0", "source-map": "^0.7.3"}, "devDependencies": {"chai": "^4.3.6", "mocha": "^9.2.0"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "funding": "https://opencollective.com/stylus", "_id": "stylus@0.61.0", "gitHead": "566a9f2062e6ee58e1c8510d3feb13eaa3d1d9aa", "_nodeVersion": "20.5.1", "_npmVersion": "9.8.0", "dist": {"integrity": "sha512-oaV9T4sRBiQfChXE0av9SrLD+ovEdQiWzPJ5kwIeYvMhjUDJnZtdubAG6lSSbaR4sCnoT6sw411IOl5Akcht4Q==", "shasum": "d90a4334486a1622cc6d3d5e878087376dc478a6", "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.61.0.tgz", "fileCount": 143, "unpackedSize": 365817, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCYOEWOjeLlue92sFOuWEZh/xVE3OYhT6uL5RMsV6D0IAIgMiSUrbA6cdsrdeUozhpUeqfKrQR4h+/HfSZSUzHZoqc="}], "size": 87121}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}, {"name": "xdan", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/stylus_0.61.0_1699083321003_0.8483383100287085"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-04T07:35:21.179Z", "publish_time": 1699083321179, "_source_registry_name": "default"}, "0.62.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.62.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus.git"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require chai --bail --check-leaks --reporter dot"}, "dependencies": {"@adobe/css-tools": "~4.3.1", "debug": "^4.3.2", "glob": "^7.1.6", "sax": "~1.3.0", "source-map": "^0.7.3"}, "devDependencies": {"chai": "^4.3.6", "mocha": "^9.2.0"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "funding": "https://opencollective.com/stylus", "_id": "stylus@0.62.0", "gitHead": "49e32667d5e1cf84fb28635dbaff0cda156939d3", "_nodeVersion": "20.5.1", "_npmVersion": "9.8.0", "dist": {"integrity": "sha512-v3YCf31atbwJQIMtPNX8hcQ+okD4NQaTuKGUWfII8eaqn+3otrbttGL1zSMZAAtiPsBztQnujVBugg/cXFUpyg==", "shasum": "648a020e2bf90ed87587ab9c2f012757e977bb5d", "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.62.0.tgz", "fileCount": 143, "unpackedSize": 365792, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICjy995vbVp0RnHY5UnOYeIE0S2r3kBUFfvi1vs7zJ98AiBYpkqjFw9pe0WxFkTHJfOUvnkz33/8q2yhJC6kAfJJxw=="}], "size": 87110}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}, {"name": "xdan", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/stylus_0.62.0_1700282693608_0.8212811458059563"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-18T04:44:53.874Z", "publish_time": 1700282693874, "_source_registry_name": "default"}, "0.63.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.63.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus.git"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": "*"}, "bin": {"stylus": "bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require chai --bail --check-leaks --reporter dot"}, "dependencies": {"@adobe/css-tools": "~4.3.3", "debug": "^4.3.2", "glob": "^7.1.6", "sax": "~1.3.0", "source-map": "^0.7.3"}, "devDependencies": {"chai": "^4.3.6", "mocha": "^9.2.0"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "funding": "https://opencollective.com/stylus", "_id": "stylus@0.63.0", "gitHead": "c5b0b90476d102ef730118099707b9fa026a4313", "_nodeVersion": "20.5.1", "_npmVersion": "9.8.0", "dist": {"integrity": "sha512-OMlgrTCPzE/ibtRMoeLVhOY0RcNuNWh0rhAVqeKnk/QwcuUKQbnqhZ1kg2vzD8VU/6h3FoPTq4RJPHgLBvX6Bw==", "shasum": "511e8d56f2005b09010fbc1f62561c7b6f72a490", "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.63.0.tgz", "fileCount": 143, "unpackedSize": 365596, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDklwQYE2TUEnibSXcZfS7IA+oBm9fXl0FaCYaWrzXsLQIhAOaLPplDJG7CjLMHO69j4PnFwWt5JEqYyGrYweY4drEf"}], "size": 87068}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}, {"name": "xdan", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/stylus_0.63.0_1709615122044_0.6494594714976967"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-05T05:05:22.258Z", "publish_time": 1709615122258, "_source_registry_name": "default"}, "0.64.0": {"name": "stylus", "description": "Robust, expressive, and feature-rich CSS superset", "version": "0.64.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus.git"}, "main": "./index.js", "browserify": "./lib/browserify.js", "engines": {"node": ">=16"}, "bin": {"stylus": "bin/stylus"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require chai --bail --check-leaks --reporter dot"}, "dependencies": {"@adobe/css-tools": "~4.3.3", "debug": "^4.3.2", "glob": "^10.4.5", "sax": "~1.4.1", "source-map": "^0.7.3"}, "devDependencies": {"chai": "^4.3.6", "mocha": "^10.4.0"}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "directories": {"doc": "docs", "example": "examples", "test": "test"}, "license": "MIT", "funding": "https://opencollective.com/stylus", "_id": "stylus@0.64.0", "gitHead": "d6f55c19bc0181eaab472752af2504dc2448a44d", "_nodeVersion": "20.11.1", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-ZIdT8eUv8tegmqy1tTIdJv9We2DumkNZFdCF5mz/Kpq3OcTaxSuCAYZge6HKK2CmNC02G1eJig2RV7XTw5hQrA==", "shasum": "af99253f1254c851528c44eddc3ccf1f831942f1", "tarball": "https://registry.npmmirror.com/stylus/-/stylus-0.64.0.tgz", "fileCount": 143, "unpackedSize": 365731, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDhKM2v75DlRJcOeZRzjZnMm3ir/VJaP9QEfzdvUuKGJQIgQwSK+MJX/V2rpTxeANAlsHOqXIik1MgQpT4K4PuBF4I="}], "size": 87381}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ya", "email": "<EMAIL>"}, {"name": "kizu", "email": "<EMAIL>"}, {"name": "xdan", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>i", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/stylus_0.64.0_1729408330302_0.05604398839014957"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-20T07:12:10.493Z", "publish_time": 1729408330493, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "homepage": "https://github.com/stylus/stylus", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "repository": {"type": "git", "url": "git://github.com/stylus/stylus.git"}, "_source_registry_name": "default"}