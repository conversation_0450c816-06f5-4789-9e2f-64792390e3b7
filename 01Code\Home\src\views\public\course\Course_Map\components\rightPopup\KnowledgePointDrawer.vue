<!-- src\views\public\course\Course_Map\components\rightPopup\KnowledgePointDrawer.vue -->
<template>
  <!-- 知识点弹窗 -->
  <div class="drawer" :class="{ open: isOpen }">
    <!-- 弹窗头部 -->
    <div class="drawer-header">
      <!-- 头部信息 -->
      <div class="header-info">
        <!-- 节点标签 -->
        <div class="node-tags">
          <span v-for="(tag, index) in nodeData.tags || []" :key="index" class="node-tag">
            {{ tag }}
          </span>
        </div>
        <!-- 节点标题 -->
        <h3 class="node-title">{{ nodeData.label }}</h3>
      </div>
      <!-- 关闭按钮 -->
      <button class="close-btn" @click="closeDrawer">×</button>
    </div>

    <!-- 弹窗内容区 -->
    <div class="drawer-content" ref="contentRef">
      <!-- 知识点描述 -->
      <section ref="descRef" class="content-block">
        <h4 class="content-title">
          <img :src="descIcon" alt="desc" class="icon" />
          <span class="title-text">知识点描述</span>
          <span class="count">· {{ nodeData.descCount || 1 }}</span>
        </h4>
        <div class="attr-desc-content-wrapper">
          <p
            class="attr-desc-content"
            :class="{ 'collapsed': !mainDescExpanded, 'show-gradient': showMainDescToggle && !mainDescExpanded }"
            :ref="el => { if(el) mainDescRef = el }"
          >
            <template v-if="descLoading">加载中...</template>
            <template v-else-if="descError">{{ descError }}</template>
            <template v-else>{{ nodeDesc || '暂无描述' }}</template>
          </p>
          <div v-if="showMainDescToggle" class="desc-toggle-bar">
            <button class="desc-toggle-btn" @click="toggleMainDescExpand">
              <span v-if="!mainDescExpanded">展开全部 <svg class='toggle-icon' width='16' height='16' viewBox='0 0 16 16'><path d='M4 6l4 4 4-4' stroke='#8a6de3' stroke-width='2' fill='none' stroke-linecap='round'/></svg></span>
              <span v-else>收起 <svg class='toggle-icon' width='16' height='16' viewBox='0 0 16 16'><path d='M4 10l4-4 4 4' stroke='#8a6de3' stroke-width='2' fill='none' stroke-linecap='round'/></svg></span>
            </button>
          </div>
        </div>
      </section>

      <!-- 知识点资源 -->
      <section ref="resourceRef" class="content-block">
        <h4 class="content-title">
          <img :src="resourceIcon" alt="resource" class="icon" />
          <span class="title-text">知识点资源</span>
          <span class="count">·
            <template v-if="resourceCountLoading">...</template>
            <template v-else-if="resourceCountError">?</template>
            <template v-else>{{ resourceCount }}</template>
          </span>
        </h4>
        <ul class="resource-list">
          <template v-if="resourceLoading">
            <li class="resource-item">加载中...</li>
          </template>
          <template v-else-if="resourceError">
            <li class="resource-item">{{ resourceError }}</li>
          </template>
          <template v-else-if="resourceList.length === 0">
            <li class="resource-item">暂无资源</li>
          </template>
          <template v-else>
            <li v-for="item in resourceList" :key="item.id || item.resourceType || item.resourceName" class="resource-item">
              <button class="resource-btn" type="button">
                <img :src="getResourceIcon(item.resourceType)" :alt="item.resourceName || '资源'" class="resource-type-icon" />
                <span class="resource-type-name">{{ item.resourceName || getTypeName(item.resourceType) || '资源' }}</span>
              </button>
            </li>
          </template>
        </ul>
      </section>

      <!-- 知识点属性 -->
      <section ref="attrRef" class="content-block">
        <h4 class="content-title">
          <img :src="attrIcon" alt="attr" class="icon" />
          <span class="title-text">知识点属性</span>
          <span class="count">·
            <template v-if="childCountLoading">...</template>
            <template v-else-if="childCountError">?</template>
            <template v-else>{{ childCount }}</template>
          </span>
        </h4>
        <ul class="attr-desc-list">
          <template v-if="childDescLoading">
            <li class="attr-desc-item">加载中...</li>
          </template>
          <template v-else-if="childDescError">
            <li class="attr-desc-item">{{ childDescError }}</li>
          </template>
          <template v-else-if="childDescList.length === 0">
            <li class="attr-desc-item">暂无子节点</li>
          </template>
          <template v-else>
            <li v-for="(item, idx) in childDescList" :key="item.id + '-' + idx" class="attr-desc-item">
              <div class="attr-desc-header">
                <div class="attr-desc-title">{{ item.label }}</div>
                <div class="attr-desc-tags">
                  <span v-for="tag in item.tags" :key="tag.tagId || tag" class="attr-desc-tag">{{ tag.tagName || tag }}</span>
                </div>
              </div>
              <div class="attr-desc-content-wrapper">
                <div
                  class="attr-desc-content"
                  :class="{ 'collapsed': !expandedMap[idx], 'show-gradient': showToggleMap[idx] && !expandedMap[idx] }"
                  :ref="el => { if(el) descRefs[idx] = el }"
                >
                  {{ item.desc }}
                </div>
                <div
                  v-if="showToggleMap[idx]"
                  class="desc-toggle-bar"
                >
                  <button class="desc-toggle-btn" @click="toggleExpand(idx)">
                    <span v-if="!expandedMap[idx]">展开全部 <svg class='toggle-icon' width='16' height='16' viewBox='0 0 16 16'><path d='M4 6l4 4 4-4' stroke='#8a6de3' stroke-width='2' fill='none' stroke-linecap='round'/></svg></span>
                    <span v-else>收起 <svg class='toggle-icon' width='16' height='16' viewBox='0 0 16 16'><path d='M4 10l4-4 4 4' stroke='#8a6de3' stroke-width='2' fill='none' stroke-linecap='round'/></svg></span>
                  </button>
                </div>
              </div>
            </li>
          </template>
        </ul>
      </section>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, ref, watch, defineExpose, onMounted, onUpdated, nextTick } from 'vue'
import descIcon from '@/assets/courseMap-icon/descIcon.svg'
import resourceIcon from '@/assets/courseMap-icon/resourceIcon.svg'
import attrIcon from '@/assets/courseMap-icon/attrIcon.svg'
import videoIcon from '@/assets/courseMap-icon/resourceType/videoIcon.svg'
import bookIcon from '@/assets/courseMap-icon/resourceType/bookIcon.svg'
import pptIcon from '@/assets/courseMap-icon/resourceType/pptIcon.svg'
import wordIcon from '@/assets/courseMap-icon/resourceType/wordIcon.svg'
import externalIcon from '@/assets/courseMap-icon/resourceType/externalIcon.svg'
import otherIcon from '@/assets/courseMap-icon/resourceType/otherIcon.svg'
import { getNodeDetail, getNodeResources, countNodeResources, countChildNodes } from '@/api/teacher/graphManager/knowledgeGraph.js'

// 定义组件接收的属性
const props = defineProps({
  isOpen: Boolean,
  nodeData: Object
})

// 定义组件触发的事件
const emit = defineEmits(['close'])

// 当前激活的标签
const activeTab = ref('desc')

// 内容区域引用
const contentRef = ref(null)
// 描述区域引用
const descRef = ref(null)
// 资源区域引用
const resourceRef = ref(null)
// 属性区域引用
const attrRef = ref(null)

const mainDescExpanded = ref(false)
const showMainDescToggle = ref(false)
const mainDescRef = ref(null)

const expandedMap = ref({})
const showToggleMap = ref({})
const descRefs = ref([])

const nodeDesc = ref('')
const descLoading = ref(false)
const descError = ref('')

const resourceList = ref([])
const resourceLoading = ref(false)
const resourceError = ref('')
const resourceCount = ref(0)
const resourceCountLoading = ref(false)
const resourceCountError = ref('')

const childCount = ref(0)
const childCountLoading = ref(false)
const childCountError = ref('')

const childDescList = ref([])
const childDescLoading = ref(false)
const childDescError = ref('')

const checkOverflow = () => {
  // Check main description
  if (mainDescRef.value && !showMainDescToggle.value) {
    const lineHeight = 1.6 * 14 // font-size * line-height
    const maxLines = 4
    if (mainDescRef.value.scrollHeight > lineHeight * maxLines) {
      showMainDescToggle.value = true
    }
  }
  // Check attribute list descriptions
  descRefs.value.forEach((el, idx) => {
    if (el && showToggleMap.value[idx] === undefined) {
      const lineHeight = 1.6 * 14 // font-size * line-height
      const maxLines = 4
      if (el.scrollHeight > lineHeight * maxLines) {
        showToggleMap.value = { ...showToggleMap.value, [idx]: true }
      }
    }
  })
}

// 监听节点数据变化
watch(
  () => props.nodeData,
  async () => {
    activeTab.value = 'desc'

    // Reset main description states
    mainDescExpanded.value = false
    showMainDescToggle.value = false
    mainDescRef.value = null

    // Reset attribute list states
    expandedMap.value = {}
    showToggleMap.value = {}
    descRefs.value = []
    nextTick(checkOverflow)

    // 获取知识点描述和子节点属性
nodeDesc.value = ''
descError.value = ''
childDescList.value = []
childDescError.value = ''

if (props.nodeData && props.nodeData.id) {
  descLoading.value = true
  childDescLoading.value = true
  try {
    const res = await getNodeDetail(props.nodeData.id)
    if (res && res.code === 200 && res.result) {
      nodeDesc.value = res.result.nodeDesc || ''

      // ✅ 子节点属性合并处理（避免重复卡片）
      if (Array.isArray(res.result.children)) {
  const childMap = new Map()

  res.result.children.forEach(child => {
    const id = child.nodeId  // ✅ 修复点
    const tagList = Array.isArray(child.tags) ? child.tags : []

    if (!childMap.has(id)) {
      childMap.set(id, {
        id,
        label: child.nodeName,
        tags: [...tagList],
        desc: child.nodeDesc || ''
      })
    } else {
      const existing = childMap.get(id)
      const existingTagsSet = new Set(existing.tags.map(t => t.tagName || t))
      tagList.forEach(tag => {
        const tagValue = tag.tagName || tag
        if (!existingTagsSet.has(tagValue)) {
          existing.tags.push(tag)
        }
      })
    }
  })



        childDescList.value = Array.from(childMap.values())
      } else {
        childDescList.value = []
      }
    } else {
      descError.value = res?.msg || '获取描述失败'
      childDescError.value = res?.msg || '获取子节点属性失败'
    }
  } catch (e) {
    descError.value = '获取描述失败'
    childDescError.value = '获取子节点属性失败'
  } finally {
    descLoading.value = false
    childDescLoading.value = false
  }
}


    // 获取知识点资源数量
    resourceCount.value = 0
    resourceCountError.value = ''
    if (props.nodeData && props.nodeData.id) {
      resourceCountLoading.value = true
      try {
        const res = await countNodeResources(props.nodeData.id)
        if (res && res.code === 200 && typeof res.result === 'number') {
          resourceCount.value = res.result
        } else {
          resourceCountError.value = res?.msg || '获取资源数量失败'
        }
      } catch (e) {
        resourceCountError.value = '获取资源数量失败'
      } finally {
        resourceCountLoading.value = false
      }
    }

    // 获取知识点资源列表
    resourceList.value = []
    resourceError.value = ''
    if (props.nodeData && props.nodeData.id) {
      resourceLoading.value = true
      try {
        const res = await getNodeResources(props.nodeData.id)
        if (res && res.code === 200 && Array.isArray(res.result)) {
          resourceList.value = res.result
        } else {
          resourceError.value = res?.msg || '获取资源失败'
        }
      } catch (e) {
        resourceError.value = '获取资源失败'
      } finally {
        resourceLoading.value = false
      }
    }

    // 获取子节点数量
    childCount.value = 0
    childCountError.value = ''
    if (props.nodeData && props.nodeData.id) {
      childCountLoading.value = true
      try {
        const res = await countChildNodes(props.nodeData.id)
        if (res && res.code === 200 && typeof res.result === 'number') {
          childCount.value = res.result
        } else {
          childCountError.value = res?.msg || '获取子节点数量失败'
        }
      } catch (e) {
        childCountError.value = '获取子节点数量失败'
      } finally {
        childCountLoading.value = false
      }
    }
  },
  { deep: true }
)

onMounted(() => {
  nextTick(checkOverflow)
})

onUpdated(() => {
  checkOverflow()
})

function toggleMainDescExpand() {
  mainDescExpanded.value = !mainDescExpanded.value
}

function toggleExpand(idx) {
  expandedMap.value[idx] = !expandedMap.value[idx]
}

// 关闭抽屉
const closeDrawer = () => {
  emit('close')
}

// 滚动到指定区域
const scrollTo = (key) => {
  activeTab.value = key
  let target = null
  if (key === 'desc') target = descRef.value
  if (key === 'resource') target = resourceRef.value
  if (key === 'attr') target = attrRef.value

  if (target && contentRef.value) {
    target.scrollIntoView({ behavior: 'smooth', block: 'start' })
  }
}

// 对外暴露方法
defineExpose({ scrollTo })

function getResourceIcon(type) {
  switch (type) {
    case 1: return videoIcon
    case 2: return bookIcon
    case 3: return pptIcon
    case 4: return wordIcon
    case 5: return externalIcon
    case 6: return otherIcon
    default: return otherIcon
  }
}

function getTypeName(type) {
  switch (type) {
    case 1: return '视频资源'
    case 2: return '教材资源'
    case 3: return 'PPT资源'
    case 4: return 'Word资源'
    case 5: return '外部资源'
    case 6: return '其他资源'
    default: return '资源'
  }
}
</script>

<style scoped>
.drawer {
  position: fixed;
  top: 85px;
  right: -40%;
  width: 25%;
  height: calc(100% - 100px);
  background: #fff;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  transition: right 0.3s ease;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.drawer.open {
  right: 40px;
}

.drawer-header {
  padding: 30px 40px 5px 40px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-info {
  display: flex;
  flex-direction: column;
}

.node-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 10px;
}

.node-tag {
  background: #ece8f4;
  color: #8a6de3;
  font-size: 10px;
  padding: 4px 10px;
  border-radius: 12px;
  font-weight: 500;
}

.node-title {
  margin: 0;
  font-size: 22px;
  color: #111;
  font-weight: 888;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  line-height: 1;
}

.drawer-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.content-block {
  background: #f6f3fd;
  border-radius: 8px;
  padding: 22px 22px 16px 22px;
  margin-bottom: 24px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
}

.content-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 14px;
}

.content-title .icon {
  width: 18px;
  height: 18px;
}

.content-title .title-text {
  color: #222;
  font-size: 16px;
  font-weight: 500;
}

.content-title .count {
  color: #8a6de3;
  font-size: 16px;
  margin-left: 4px;
}

.content-block p {
  font-size: 14px;
  line-height: 1.7;
  color: #333;
}

.resource-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 14px;
}
.resource-item {
  padding: 0;
  margin: 0;
}
.resource-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  background: #fff;
  border: 1.5px solid #ece8f4;
  border-radius: 7px;
  padding: 10px 14px;
  cursor: pointer;
  transition: box-shadow 0.18s, border-color 0.18s, background 0.18s;
  font: inherit;
  outline: none;
  box-shadow: 0 1px 2px rgba(140, 120, 200, 0.04);
}
.resource-btn:hover, .resource-btn:focus {
  border-color: #8a6de3;
  background: #f5f5f5;
  box-shadow: 0 2px 8px rgba(140, 120, 200, 0.10);
}
.resource-btn:active {
  background: #ece8f4;
  box-shadow: 0 1px 2px rgba(140, 120, 200, 0.04);
}
.resource-type-icon {
  width: 22px;
  height: 22px;
  flex-shrink: 0;
}
.resource-type-name {
  font-size: 15px;
  color: #333;
  font-weight: 500;
}

.attr-desc-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.attr-desc-item {
  background: #fff;
  border: 1.5px solid #ece8f4;
  border-radius: 8px;
  padding: 16px 16px 12px 16px;
  box-shadow: 0 1px 2px rgba(140, 120, 200, 0.04);
}
.attr-desc-header {
  margin-bottom: 12px;
}
.attr-desc-title {
  font-size: 16px;
  font-weight: 600;
  color: #222;
  margin-bottom: 8px;
}
.attr-desc-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}
.attr-desc-tag {
  background: #ece8f4;
  color: #8a6de3;
  font-size: 10px;
  padding: 3px 8px;
  border-radius: 10px;
  font-weight: 500;
}
.attr-desc-content-wrapper {
  position: relative;
}
.attr-desc-content {
  font-size: 14px;
  line-height: 1.6;
  color: #555;
  text-align: justify;
  transition: max-height 0.22s cubic-bezier(.4,0,.2,1);
  overflow: hidden;
  max-height: none;
}
.attr-desc-content.collapsed {
  max-height: 6.4em; /* 4 lines * 1.6 line-height */
  position: relative;
}
.attr-desc-content.collapsed.show-gradient::after {
  content: '';
  position: absolute;
  left: 0; right: 0; bottom: 0;
  height: 1.8em; /* Gradient height, covers approx. the 4th line */
  background: linear-gradient(to bottom, rgba(255,255,255,0) 0%, #fff 100%);
  pointer-events: none;
}
/* Custom gradient for the main description block */
.content-block:first-child .attr-desc-content.collapsed.show-gradient::after {
  background: linear-gradient(to bottom, rgba(246,243,253,0) 0%, #f6f3fd 100%);
}
.desc-toggle-bar {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
}
.desc-toggle-btn {
  background: none;
  border: none;
  color: #8a6de3;
  font-size: 13px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 2px;
  padding: 0;
  transition: color 0.15s;
}
.desc-toggle-btn:hover {
  color: #5f3dc4;
}
.toggle-icon {
  vertical-align: middle;
}
</style>
