<template>
    <div class="knowledge-detail-drawer" :class="{ open: isOpen }">
        <div class="drawer-content">
            <template v-if="isRootNode">
                <div class="empty-state warning">
                    该节点不可添加详情
                </div>
            </template>
            <template v-else-if="resolvedCurrentNode">
                <div class="section">
                    <h2>节点信息</h2>
                    <div class="node-info">
                        <div class="form-group">
                            <label>节点名称</label>
                            <input v-model="editableNode.nodeName" type="text" />
                        </div>
                        <div class="form-group">
                            <label>节点描述</label>
                            <textarea v-model="editableNode.nodeDesc" placeholder="请输入节点描述"></textarea>
                        </div>
                        <div class="form-group">
                            <label>节点标签</label>
                            <div class="tag-options">
                                <div v-for="tag in tagList" :key="tag.tagId"
                                    :class="['tag', { selected: isTagSelected(tag) }]" @click="toggleTag(tag)">
                                    {{ tag.tagName }}
                                </div>
                            </div>
                        </div>
                        <div class="form-actions">
                            <button class="save-btn" @click="saveNodeInfo">保存修改</button>
                            <button class="cancel-btn" @click="resetEditableNode">取消</button>
                        </div>
                    </div>
                </div>
                <div class="section">
                    <div class="section-header">
                        <h2>学习资源({{ stats.resources }})</h2>
                        <button class="link-btn" @click="openResourceAssociation">关联资源</button>
                    </div>
                    <div class="empty-state error" v-if="stats.resources === 0">
                        知识点尚未绑定资源，请点击「关联资源」选择绑定资源
                    </div>
                    <div class="resource-list" v-if="resourceList.length">
                        <div class="resource-item" v-for="res in resourceList" :key="res.resourceId">
                            <img :src="getResourceIcon(res.resourceType)" class="resource-icon" />
                            <span class="resource-name">{{ res.resourceName }}</span>
                            <button class="close-btn" @click.stop="handleRemove(res)" title="取消关联">×</button>
                        </div>
                    </div>
                    <div v-else-if="stats.resources > 0" class="empty-state error">知识点尚未绑定资源，请点击「关联资源」选择绑定资源</div>
                </div>
            </template>
            <template v-else>
                <div class="empty-state">
                    请选中节点来添加详情
                </div>
            </template>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, computed, watch, watchEffect } from 'vue'
import { useRoute } from 'vue-router'
import { useRouter } from 'vue-router'
import { useKnowledgeGraphStore } from '@/stores/teacher/graphManager/knowledgeGraphStore'

import videoIcon from '@/assets/courseMap-icon/resourceType/videoIcon.svg'
import bookIcon from '@/assets/courseMap-icon/resourceType/bookIcon.svg'
import pptIcon from '@/assets/courseMap-icon/resourceType/pptIcon.svg'
import wordIcon from '@/assets/courseMap-icon/resourceType/wordIcon.svg'
import externalIcon from '@/assets/courseMap-icon/resourceType/externalIcon.svg'
import otherIcon from '@/assets/courseMap-icon/resourceType/otherIcon.svg'

const emit = defineEmits(['update-node'])
const store = useKnowledgeGraphStore()
const route = useRoute()
const router = useRouter()
const resolvedCurrentNode = ref(null)

const props = defineProps({
    isOpen: Boolean,
    currentNode: [Object, String, Promise],
    stats: Object,
    editableNode: Object
})

// 处理 Promise 类型的 props
watch(() => props.currentNode, async (newVal) => {
    if (newVal instanceof Promise) {
        resolvedCurrentNode.value = await newVal
    } else {
        resolvedCurrentNode.value = newVal
    }
}, { immediate: true })

// 计算属性：判断当前是否是根节点
const isRootNode = computed(() => {
    if (!resolvedCurrentNode.value) return false
    return resolvedCurrentNode.value === 'root' ||
        resolvedCurrentNode.value?.id === store.rootNodeId
})

//标签列表（动态获取）
const tagList = ref([])

// 判断标签是否选中
const isTagSelected = (tag) => {
    return props.editableNode.tags?.some(t =>
        (typeof t === 'object' ? t.tagId : t) === tag.tagId
    ) || false
}

// 获取标签列表（添加错误处理）
const fetchTagList = async () => {
    try {
        // 直接从store获取而不是调用API
        if (store.tagList && store.tagList.length > 0) {
            tagList.value = store.tagList
        } else {
            // 如果store中没有，尝试获取一次
            await store.fetchAllTags()
            tagList.value = store.tagList || []
        }
    } catch (error) {
        console.error('加载标签列表失败:', error)
        tagList.value = [] // 确保有默认值
    }
}

// 切换标签选择
const toggleTag = (tag) => {
    if (!props.editableNode.tags) {
        props.editableNode.tags = []
    }

    const index = props.editableNode.tags.findIndex(t =>
        (typeof t === 'object' ? t.tagId : t) === tag.tagId
    )

    if (index === -1) {
        // 存储完整的标签对象而不是仅存储ID
        props.editableNode.tags.push(tag)
    } else {
        props.editableNode.tags.splice(index, 1)
    }
}

// 在组件挂载时获取标签
onMounted(() => {
    fetchTagList()
})

// 保存节点信息
const saveNodeInfo = async () => {
    if (!props.editableNode.nodeId) return;

    try {
        // 转换标签格式：确保只传递tagId数组到后端
        const tagIds = props.editableNode.tags?.map(tag =>
            typeof tag === 'object' ? tag.tagId : tag
        ) || [];

        // 更新节点基本信息
        const updateResult = await store.updateNodeInfo({
            nodeId: props.editableNode.nodeId,
            nodeName: props.editableNode.nodeName,
            nodeDesc: props.editableNode.nodeDesc
        });

        if (updateResult) {
            // 更新标签关联
            const tagResult = await store.associateTagsToNode({
                nodeId: props.editableNode.nodeId,
                tagIds: tagIds
            });

            if (tagResult) {
                // 刷新节点树
                await store.fetchGraphTree(route.params.graphId);

                // 通知父组件更新 - 确保传递标签名称而不是ID
                emit('update-node', {
                    label: props.editableNode.nodeName,
                    description: props.editableNode.nodeDesc,
                    tags: props.editableNode.tags.map(tag =>
                        typeof tag === 'object' ? tag.tagName : tag
                    )
                });
            }
        }
    } catch (error) {
        console.error('保存节点信息失败:', error);
    }
}

const handleNodeUpdate = () => {
    if (!props.currentNode || props.currentNode === 'root') return
    emit('update-node', {
        label: props.currentNode.label,
        description: props.currentNode.description
    })
}

const openResourceAssociation = async () => {
    if (!props.currentNode || props.currentNode === 'root') return

    // 从store获取courseId
    const courseId = store.courseId
    const currentNode = await props.currentNode;

    // 添加调试信息
    console.log('当前路由列表:', router.getRoutes().map(r => ({ name: r.name, path: r.path })))
    console.log('ResourceAssociation路由是否存在:', router.hasRoute('ResourceAssociation'))

    router.push({
        name: 'ResourceAssociation',
        params: {
            courseId: courseId,
            nodeId: currentNode.id || currentNode.nodeId // 根据实际结构调整
        }
    });
}

// 新增：资源列表响应式变量
const resourceList = ref([])

// 监听 currentNode 变化，获取资源列表
watchEffect(async () => {
    try {
        // 处理 Promise 类型的 currentNode
        const currentNode = props.currentNode instanceof Promise
            ? await props.currentNode
            : props.currentNode;

        if (!currentNode || currentNode === 'root') {
            resourceList.value = [];
            return;
        }

        // 获取节点ID - 兼容不同格式
        const nodeId = currentNode.id || currentNode.nodeId;
        if (nodeId) {
            resourceList.value = await store.fetchNodeResources(nodeId);
        } else {
            resourceList.value = [];
        }
    } catch (error) {
        console.error('获取资源列表失败:', error);
        resourceList.value = [];
    }
});

function getResourceIcon(type) {
    switch (type) {
        case 1: return videoIcon;
        case 2: return bookIcon;
        case 3: return pptIcon;
        case 4: return wordIcon;
        case 5: return externalIcon;
        case 6: return otherIcon;
        default: return otherIcon;
    }
}

async function handleRemove(res) {
    try {
        // 正确处理可能为Promise的currentNode
        const currentNode = props.currentNode instanceof Promise
            ? await props.currentNode
            : props.currentNode;

        // 获取节点ID - 兼容不同格式
        const nodeId = currentNode?.id || currentNode?.nodeId;
        if (!nodeId) {
            console.error('无法获取节点ID');
            return;
        }

        const success = await store.unlinkResourceFromNode(nodeId, res.resourceId);
        if (success) {
            alert('取消关联成功');
            // 重新获取资源列表
            const list = await store.fetchNodeResources(nodeId);
            resourceList.value = list;
        } else {
            alert('取消关联失败');
        }
    } catch (error) {
        console.error('取消关联操作出错:', error);
        alert('操作失败，请重试');
    }
}
</script>

<style lang="scss" scoped>
$drawer-width: 400px;
$transition-duration: 0.3s;

.knowledge-detail-drawer {
    position: fixed;
    will-change: transform;
    top: 60px;
    right: -$drawer-width;
    bottom: 0;
    width: $drawer-width;
    background-color: #fff;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    transition: right $transition-duration ease;
    z-index: 100;
    overflow-y: auto;
    border-left: 1px solid #eee;

    &.open {
        right: 0;
    }

    .drawer-content {
        padding: 20px;

        .section {
            margin-bottom: 30px;

            .section-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;

                h2 {
                    font-size: 16px;
                    font-weight: 600;
                    color: #333;
                    margin: 0;
                }

                .link-btn {
                    background-color: #4c7bff;
                    color: #fff;
                    padding: 6px 12px;
                    font-size: 14px;
                    border: none;
                    border-radius: 6px;
                    cursor: pointer;
                    transition: background 0.2s;

                    &:hover {
                        background-color: #3b6fe0;
                    }

                    &:active {
                        background-color: #345fd6;
                    }
                }
            }

            .empty-state {
                padding: 15px;
                background-color: #f9f9f9;
                border-radius: 6px;
                color: #666;
                text-align: center;
                font-size: 14px;

                &.error {
                    background-color: #fff0f0;
                    color: #ff4d4f;
                }
            }
        }
    }
}

.node-info {
    padding: 10px 0;
}

.form-group {
    margin-bottom: 15px;

    label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        font-size: 14px;
        color: #555;
    }

    input,
    textarea {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    }

    textarea {
        min-height: 80px;
        resize: vertical;
    }
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;

    button {
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s;
    }

    .save-btn {
        background-color: #4c7bff;
        color: white;
        border: none;

        &:hover {
            background-color: #3a6ae0;
        }
    }

    .cancel-btn {
        background-color: #fff;
        border: 1px solid #ccc;
        color: #666;

        &:hover {
            background-color: #f5f5f5;
        }
    }
}

.tag-options {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
}

.tag {
    background-color: #eef2ff;
    color: #4c7bff;
    font-size: 12px;
    padding: 4px 10px;
    border-radius: 999px;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
        background-color: #d6e0ff;
    }
}

.tag.selected {
    background-color: #4c7bff;
    color: white;
}

.empty-state {
    padding: 20px;
    text-align: center;
    color: #666;
    background-color: #f9f9f9;
    border-radius: 6px;
    margin-top: 10px;

    &.warning {
        background-color: #fff8e6;
        color: #faad14;
    }

    &.error {
        background-color: #fff0f0;
        color: #ff4d4f;
    }
}

.resource-list {
    margin-top: 10px;
    display: flex;
    flex-direction: column;
    gap: 14px;
}

.resource-item {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 15px;
    color: #333;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(140, 120, 200, 0.08);
    padding: 14px 18px;
    transition: box-shadow 0.18s, background 0.18s;
    cursor: pointer;
    border: 1.5px solid #ece8f4;
    position: relative;
}

.resource-item:hover {
    background: #f6f3fd;
    box-shadow: 0 4px 16px rgba(140, 120, 200, 0.16);
    border-color: #8a6de3;
}

.resource-icon {
    width: 22px;
    height: 22px;
}

.resource-name {
    font-size: 15px;
    flex: 1;
    word-break: break-all;
}

.close-btn {
    display: none;
    position: absolute;
    right: 14px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #1dbec7;
    font-size: 14px;
    cursor: pointer;
    padding: 0 4px;
    border-radius: 50%;
    transition: background 0.15s, color 0.15s;
}

.resource-item:hover .close-btn {
    display: block;
    color: #ffffff;
    background: #af9ed6;
}

.close-btn:hover {
    background: #ffeaea;
    color: #d9363e;
}
</style>