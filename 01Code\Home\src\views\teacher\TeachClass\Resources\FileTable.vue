<template>
  <div>
    <!-- 文件表格容器 -->
    <div class="file-grid-container">
      <!-- 表格表头 -->
      <div class="file-grid-header">
        <div class="checkbox-column">
          <input type="checkbox" @change="toggleAll"
            :checked="selectedItems.length > 0 && selectedItems.length === fileList.length" />
        </div>
        <div class="name-column">
          <div class="name-header-content">
            <span>文件名</span>
            <!-- 批量操作组件，选中文件后显示 -->
            <FileBatchActions v-if="selectedItems.length > 0" :selected-items="selectedItems" :file-list="fileList"
              @download="$emit('download-selected')" @delete-selected="$emit('delete-selected', $event)"
              @start-rename="handleStartRename" @move-selected="$emit('move-selected', $event)" />
          </div>
        </div>
        <div class="size-column">大小</div>
        <div class="time-column">修改时间</div>
      </div>

      <!-- 表格内容区域 -->
      <div class="file-grid-rows">
        <!-- 有文件时显示文件列表 -->
        <template v-if="fileList.length > 0">
          <div v-for="file in fileList" :key="file.id" class="file-grid-row"
            :class="{ 'bg-selected': selectedItems.includes(file.id) }">
            <div class="checkbox-column">
              <input type="checkbox" :checked="selectedItems.includes(file.id)" @change="toggleItem(file.id)" />
            </div>
            <div class="name-column">
              <!-- 新建文件夹时的输入框 -->
              <template v-if="file.isNew">
                <div class="inline-input">
                  <input v-model="file.name" placeholder="请输入文件夹名称" />
                  <button @click="confirmCreate(file.id)">√</button>
                  <button @click="$emit('cancel-create')">×</button>
                </div>
              </template>
              <!-- 重命名时的输入框（动态聚焦） -->
              <template v-else-if="String(renamingId) === String(file.id)">
                <div class="inline-input">
                  <input v-model="renamingName" 
                         @keyup.enter="confirmRename" 
                         @blur="cancelRename" 
                         placeholder="请输入新名称"
                         v-autofocus />
                  <button @mousedown.prevent @click.stop="confirmRename">√</button>
                  <button @mousedown.prevent @click.stop="cancelRename">×</button>
                </div>
              </template>
              <!-- 普通文件/文件夹显示 -->
              <template v-else>
                <span class="file-name"
                  @click="file.type === 'folder' ? $emit('navigate-to-folder', file.id) : openPreview(file)"
                  :class="{ 'folder-link': file.type === 'folder' }">
                  {{ file.name }}
                </span>
              </template>
            </div>
            <div class="size-column">{{ file.size }}</div>
            <div class="time-column">{{ file.modifiedTime }}</div>
          </div>
        </template>
        <!-- 无文件时的占位提示 -->
        <template v-else>
          <div class="empty-placeholder">
            <div class="empty-content">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
                <polyline points="14 2 14 8 20 8"></polyline>
                <line x1="12" y1="18" x2="12" y2="12"></line>
                <line x1="9" y1="15" x2="15" y2="15"></line>
              </svg>
              <p>此处为空，请上传文件</p>
            </div>
          </div>
        </template>
      </div>
    </div>

    <!-- 预览模态框 -->
    <PreviewModal v-if="showModal" :file="previewFile" @close="showModal = false" />
  </div>
</template>

<script setup>
import { ref, nextTick } from 'vue';
import FileBatchActions from './FileBatchActions.vue';
import PreviewModal from './PreviewModal.vue';

// 自定义自动聚焦指令
const vAutofocus = {
  mounted(el) {
    el.focus();
  }
};

// 定义组件接收的属性
const props = defineProps({
  fileList: { type: Array, default: () => [] },        // 文件列表
  isCreating: { type: Boolean, default: false },       // 是否处于创建状态
  selectedItems: { type: Array, default: () => [] }    // 已选文件ID列表
});

// 定义组件抛出的事件
const emit = defineEmits([
  'confirm-create',      // 确认创建文件夹
  'cancel-create',       // 取消创建文件夹
  'update-selection',    // 更新选择状态
  'download-selected',   // 下载选中文件
  'delete-selected',     // 删除选中文件
  'rename-selected',     // 重命名文件
  'move-selected',       // 移动选中文件
  'navigate-to-folder'   // 导航到文件夹
]);

// 重命名相关状态
const renamingId = ref(null);         // 正在重命名的文件ID
const renamingName = ref('');         // 重命名的新名称
const showModal = ref(false);         // 预览模态框显示状态
const previewFile = ref(null);        // 预览的文件数据

// 打开文件预览
const openPreview = (file) => {
  // 确保传递完整的文件对象
  previewFile.value = {
    ...file,
    fileExtension: file.fileExtension || file.name.split('.').pop(),
    stats: file.stats || { downloads: 0, views: 0, selfRefs: 0 },
    activities: file.activities || [
      { time: new Date().toLocaleString(), desc: '上传了文件' }
    ]
  };
  showModal.value = true;
};

// 切换单个文件的选中状态
const toggleItem = (id) => {
  const newSelection = [...props.selectedItems];
  const idx = newSelection.indexOf(id);
  if (idx >= 0) newSelection.splice(idx, 1);
  else newSelection.push(id);
  emit('update-selection', newSelection);
};

// 全选/取消全选
const toggleAll = (e) => {
  if (e.target.checked) {
    emit('update-selection', props.fileList.map((f) => f.id));
  } else {
    emit('update-selection', []);
  }
};

// 确认创建文件夹
const confirmCreate = (id) => {
  const file = props.fileList.find((i) => i.id === id);
  if (file && file.name.trim()) {
    emit('confirm-create', id, file.name);
  } else {
    alert('文件夹名称不能为空');
  }
};

// 开始重命名
const startRename = (file) => {
  renamingId.value = null; // 先清空状态避免冲突
  nextTick(() => {
    renamingId.value = String(file.id);
    renamingName.value = file.name;
  });
};

// 处理开始重命名的事件
const handleStartRename = (file) => {
  if (!file.id) {
    console.error('文件ID不存在，无法重命名');
    return;
  }
  startRename(file);
};



// 确认重命名（触发事件并传递新名称）
const confirmRename = () => {
  const newName = renamingName.value.trim();
  
  if (!newName) {
    alert('名称不能为空');
    return;
  }
  
  const file = props.fileList.find(f => f.id === renamingId.value);
  if (!file) {
    console.error('未找到文件，取消重命名');
    cancelRename();
    return;
  }
  
  // 保留文件扩展名（仅文件类型）
  const finalName = file.type === 'file'
    ? keepFileExt(file.name, newName)
    : newName;
    
  emit('rename-selected', { id: renamingId.value, name: finalName });
  renamingId.value = null; // 重置状态
};

// 保留文件扩展名的工具函数
const keepFileExt = (oldName, newName) => {
  if (newName.includes('.')) return newName;
  const ext = oldName.split('.').pop();
  return ext ? `${newName}.${ext}` : newName;
};

// 取消重命名
const cancelRename = () => {
  renamingId.value = null;
};
</script>

<style lang="scss" scoped>
.file-name {
  cursor: pointer;

  &:hover {
    color: #409eff;
    text-decoration: underline;
  }
}

/* 表格容器样式 */
.file-grid-container {
  overflow-x: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  background-color: white;
  margin-bottom: 16px;
}

/* 表头样式 */
.file-grid-header {
  display: grid;
  grid-template-columns: 50px 45% 20% 25%;
  padding: 0 16px;
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
  
  >div {
    height: 48px;
    line-height: 48px;
    text-align: left;
    box-sizing: border-box;
  }
}

/* 表格行样式 */
.file-grid-rows {
  display: flex;
  flex-direction: column;
  margin-top: 10px;
}

.file-grid-row {
  display: grid;
  grid-template-columns: 50px 45% 20% 25%;
  padding: 0 16px;
  border-bottom: 1px solid #f3f4f6;
  height: 48px;
  line-height: 48px;
  transition: background-color 0.2s;
  box-sizing: border-box;
  background-color: #ffffff;
  
  &:last-child {
    border-radius: 0 0 8px 8px;
    border-bottom: none;
  }
  
  &:hover {
    background-color: rgba(142, 191, 255, 0.08);
  }
  
  &.bg-selected {
    background-color: rgba(59, 130, 246, 0.1);
  }
  
  >div {
    text-align: left;
    box-sizing: border-box;
  }
}

/* 复选框列样式 */
.checkbox-column {
  text-align: center !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 表头内容样式 */
.name-header-content {
  display: flex;
  align-items: center;
  gap: 12px;
  
  >span {
    flex-shrink: 0;
    font-weight: 500;
  }
}

/* 内联输入框样式（新建/重命名时使用） */
.inline-input {
  display: flex;
  align-items: center;
  gap: 6px;
  height: 100%;
  
  input {
    flex: 1;
    min-width: 120px;
    padding: 4px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 14px;
    box-sizing: border-box;
    height: 32px;
    line-height: normal;
  }
  
  button {
    padding: 0 8px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 32px;
  }
  
  button:first-of-type {
    background-color: #10b981;
    color: white;
  }
  
  button:last-of-type {
    background-color: #ef4444;
    color: white;
  }
}

/* 空状态占位样式 */
.empty-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6b7280;
  border-bottom: 1px solid #f3f4f6;
  
  .empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    
    svg {
      width: 40px;
      height: 40px;
      color: #9ca3af;
    }
    
    p {
      margin: 0;
      font-size: 14px;
    }
  }
}
</style>