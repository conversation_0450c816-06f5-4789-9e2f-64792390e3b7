{"_attachments": {}, "_id": "warning", "_rev": "703-61f1451e830fd08f52a2166f", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.berkeleytrue.com"}, "description": "A mirror of Facebook's Warning", "dist-tags": {"latest": "4.0.3"}, "license": "MIT", "maintainers": [{"name": "berkeleytrue", "email": "<EMAIL>"}], "name": "warning", "readme": "# Warning [![npm version](https://badge.fury.io/js/warning.svg)](https://badge.fury.io/js/warning)\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/BerkeleyTrue/warning.svg)](https://greenkeeper.io/)\nA mirror of Facebook's [Warning](https://github.com/facebook/fbjs/blob/master/packages/fbjs/src/__forks__/warning.js)\n\n\n## Usage\n```\nnpm install warning\n```\n\n```\n// some script\nvar warning = require('warning');\n\nvar ShouldBeTrue = false;\n\nwarning(\n  ShouldBeTrue,\n  'This thing should be true but you set to false. No soup for you!'\n);\n//  'This thing should be true but you set to false. No soup for you!'\n```\n\nSimilar to Facebook's (FB) invariant but only logs a warning if the condition is not met.\nThis can be used to log issues in development environments in critical\npaths. Removing the logging code for production environments will keep the\nsame logic and follow the same code paths.\n\n## FAQ (READ before opening an issue)\n\n> Why do you use `console.error` instead of `console.warn` ?\n\nThis is a mirror of Facebook's (FB) [warning](https://github.com/facebook/fbjs/blob/master/packages/fbjs/src/__forks__/warning.js) module used within React's source code (and other FB software).\nAs such this module will mirror their code as much as possible. \n\nThe descision to use `error` over `warn` was made a long time ago by the FB team and isn't going to change anytime soon.\n\nThe source can be found here: https://github.com/facebook/fbjs/blob/master/packages/fbjs/src/__forks__/warning.js\nThe reasoning can be found here and elsewhere: https://github.com/facebook/fbjs/pull/94#issuecomment-168332326\n\n> Can I add X feature?\n\nThis is a mirror of Facebook's (FB) [warning](https://github.com/facebook/fbjs/blob/master/packages/fbjs/src/__forks__/warning.js) and as such the source and signature will mirror that module.\n\nIf you believe a feature is missing than please open a feature request [there](https://github.com/facebook/fbjs).\nIf it is approved and merged in that this module will be updated to reflect that change, otherwise this module will not change.\n\n## Use in Production\n\nIt is recommended to add [babel-plugin-dev-expression](https://github.com/4Catalyzer/babel-plugin-dev-expression) with this module to remove warning messages in production.\n<br>\n<br>\n<br>\n<br>\n<br>\n<br>\n<br>\n<br>\n<br>\n<br>\n<br>\n<br>\n<br>\n<br>\n<br>\n<br>\n<br>\n<small>Don't Forget To Be Awesome</small>\n", "time": {"created": "2022-01-26T12:57:02.517Z", "modified": "2023-07-28T00:32:43.149Z", "4.0.3": "2019-02-09T21:08:28.900Z", "4.0.2": "2018-08-17T13:34:28.837Z", "4.0.1": "2018-05-30T03:03:56.386Z", "4.0.0": "2018-05-22T04:20:13.216Z", "3.0.0": "2016-06-03T02:54:03.287Z", "2.1.0": "2015-10-04T21:26:55.529Z", "2.0.0": "2015-07-12T00:41:56.055Z", "1.0.2": "2015-05-30T07:09:47.232Z", "1.0.1": "2015-04-09T14:49:19.325Z", "1.0.0": "2015-04-01T00:23:13.847Z"}, "versions": {"4.0.3": {"name": "warning", "version": "4.0.3", "description": "A mirror of Facebook's Warning", "main": "warning.js", "scripts": {"test": "npm run test:dev && npm run test:prod", "test:dev": "NODE_ENV=development jest", "test:prod": "NODE_ENV=production jest", "commit": "git cz", "commitmsg": "commitlint -e $GIT_PARAMS"}, "dependencies": {"loose-envify": "^1.0.0"}, "devDependencies": {"@commitlint/cli": "^6.2.0", "@commitlint/config-conventional": "^6.1.3", "browserify": "^16.2.2", "commitizen": "^2.10.1", "cz-conventional-changelog": "^2.1.0", "husky": "^0.14.3", "jest": "^23.1.0", "uglify-js": "^3.3.25"}, "repository": {"type": "git", "url": "git+https://github.com/BerkeleyTrue/warning.git"}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "browserify": {"transform": ["loose-envify"]}, "keywords": ["warning", "facebook", "react", "invariant"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.berkeleytrue.com"}, "license": "MIT", "bugs": {"url": "https://github.com/BerkeleyTrue/warning/issues"}, "homepage": "https://github.com/BerkeleyTrue/warning", "gitHead": "3c44be700a0e37e972c1a7e87446ba007f39cac0", "_id": "warning@4.0.3", "_npmVersion": "6.5.0", "_nodeVersion": "10.15.0", "_npmUser": {"name": "berkeleytrue", "email": "<EMAIL>"}, "dist": {"shasum": "16e9e077eb8a86d6af7d64aa1e05fd85b4678ca3", "size": 3750, "noattachment": false, "tarball": "https://registry.npmmirror.com/warning/-/warning-4.0.3.tgz", "integrity": "sha512-rpJyN222KWIvHJ/F53XSZv0Zl/accqHR8et1kpaMTD/fLCRxtV8iX8czMzY7sVZupTI3zcUTg8eycS2kNF9l6w=="}, "maintainers": [{"name": "berkeleytrue", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/warning_4.0.3_1549746508792_0.39167921585706167"}, "_hasShrinkwrap": false, "publish_time": 1549746508900, "_cnpm_publish_time": 1549746508900, "_cnpmcore_publish_time": "2021-12-15T16:50:52.921Z"}, "4.0.2": {"name": "warning", "version": "4.0.2", "description": "A mirror of Facebook's Warning", "main": "warning.js", "scripts": {"test": "npm run test:dev && npm run test:prod", "test:dev": "NODE_ENV=development jest", "test:prod": "NODE_ENV=production jest", "commit": "git cz", "commitmsg": "commitlint -e $GIT_PARAMS"}, "dependencies": {"loose-envify": "^1.0.0"}, "devDependencies": {"@commitlint/cli": "^6.2.0", "@commitlint/config-conventional": "^6.1.3", "browserify": "^16.2.2", "commitizen": "^2.10.1", "cz-conventional-changelog": "^2.1.0", "husky": "^0.14.3", "jest": "^23.1.0", "uglify-js": "^3.3.25"}, "repository": {"type": "git", "url": "git+https://github.com/BerkeleyTrue/warning.git"}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "browserify": {"transform": ["loose-envify"]}, "files": ["warning.js"], "keywords": ["warning", "facebook", "react", "invariant"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.berkeleytrue.com"}, "license": "MIT", "bugs": {"url": "https://github.com/BerkeleyTrue/warning/issues"}, "homepage": "https://github.com/BerkeleyTrue/warning", "gitHead": "e4806228c076429774878522cafdf9321b7ff71c", "_id": "warning@4.0.2", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "berkeleytrue", "email": "<EMAIL>"}, "dist": {"shasum": "aa6876480872116fa3e11d434b0d0d8d91e44607", "size": 3681, "noattachment": false, "tarball": "https://registry.npmmirror.com/warning/-/warning-4.0.2.tgz", "integrity": "sha512-wbTp09q/9C+jJn4KKJfJfoS6VleK/Dti0yqWSm6KMvJ4MRCXFQNapHuJXutJIrWV0Cf4AhTdeIe4qdKHR1+Hug=="}, "maintainers": [{"name": "berkeleytrue", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/warning_4.0.2_1534512868704_0.995670660882549"}, "_hasShrinkwrap": false, "publish_time": 1534512868837, "_cnpm_publish_time": 1534512868837, "_cnpmcore_publish_time": "2021-12-15T16:50:53.121Z"}, "4.0.1": {"name": "warning", "version": "4.0.1", "description": "A mirror of Facebook's Warning", "main": "warning.js", "browserify": {"transform": ["loose-envify"]}, "files": ["warning.js"], "scripts": {"test": "NODE_ENV=production tap test/*.js && NODE_ENV=development tap test/*.js"}, "dependencies": {"loose-envify": "^1.0.0"}, "devDependencies": {"browserify": "^11.0.1", "tap": "^1.4.0", "uglify-js": "^3.3.25"}, "repository": {"type": "git", "url": "git+https://github.com/BerkeleyTrue/warning.git"}, "keywords": ["warning", "facebook", "react", "invariant"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.berkeleytrue.com"}, "license": "MIT", "bugs": {"url": "https://github.com/BerkeleyTrue/warning/issues"}, "homepage": "https://github.com/BerkeleyTrue/warning", "gitHead": "19d17bd3dc716072aec2f1680345b089e413d3c5", "_id": "warning@4.0.1", "_npmVersion": "6.0.1", "_nodeVersion": "8.9.4", "_npmUser": {"name": "berkeleytrue", "email": "<EMAIL>"}, "dist": {"shasum": "66ce376b7fbfe8a887c22bdf0e7349d73d397745", "size": 3411, "noattachment": false, "tarball": "https://registry.npmmirror.com/warning/-/warning-4.0.1.tgz", "integrity": "sha512-rAVtTNZw+cQPjvGp1ox0XC5Q2IBFyqoqh+QII4J/oguyu83Bax1apbo2eqB8bHRS+fqYUBagys6lqUoVwKSmXQ=="}, "maintainers": [{"name": "berkeleytrue", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/warning_4.0.1_1527649436327_0.8098921246506208"}, "_hasShrinkwrap": false, "publish_time": 1527649436386, "_cnpm_publish_time": 1527649436386, "_cnpmcore_publish_time": "2021-12-15T16:50:53.400Z"}, "4.0.0": {"name": "warning", "version": "4.0.0", "description": "A mirror of Facebook's Warning", "main": "warning.js", "browserify": {"transform": ["loose-envify"]}, "files": ["warning.js"], "scripts": {"test": "NODE_ENV=production tap test/*.js && NODE_ENV=development tap test/*.js"}, "dependencies": {"loose-envify": "^1.0.0"}, "devDependencies": {"browserify": "^11.0.1", "tap": "^1.4.0", "uglify-js": "^3.3.25"}, "repository": {"type": "git", "url": "git+https://github.com/BerkeleyTrue/warning.git"}, "keywords": ["warning", "facebook", "react", "invariant"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.berkeleytrue.com"}, "license": "MIT", "bugs": {"url": "https://github.com/BerkeleyTrue/warning/issues"}, "homepage": "https://github.com/BerkeleyTrue/warning", "gitHead": "14967a756ea77cea27edfd7f0b2b3910404d7e46", "_id": "warning@4.0.0", "_npmVersion": "6.0.1", "_nodeVersion": "8.9.4", "_npmUser": {"name": "berkeleytrue", "email": "<EMAIL>"}, "dist": {"shasum": "311a464f62c0986f4d2d2f3a46d471b3ba02f782", "size": 3334, "noattachment": false, "tarball": "https://registry.npmmirror.com/warning/-/warning-4.0.0.tgz", "integrity": "sha512-JQ0tNxaiZyKqW2PQvsABcdh+W8DHc8fH3jhROE3Sf/NPFXZtjxWysD6X93OQSgJfSBY9CKNRzd5e3p4jHkp2NQ=="}, "maintainers": [{"name": "berkeleytrue", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/warning_4.0.0_1526962813097_0.20211087836391273"}, "_hasShrinkwrap": false, "publish_time": 1526962813216, "_cnpm_publish_time": 1526962813216, "_cnpmcore_publish_time": "2021-12-15T16:50:53.689Z"}, "3.0.0": {"name": "warning", "version": "3.0.0", "description": "A mirror of Facebook's Warning", "main": "warning.js", "browser": "browser.js", "browserify": {"transform": ["loose-envify"]}, "files": ["browser.js", "warning.js"], "scripts": {"test": "NODE_ENV=production tap test/*.js && NODE_ENV=development tap test/*.js"}, "dependencies": {"loose-envify": "^1.0.0"}, "devDependencies": {"browserify": "^11.0.1", "tap": "^1.4.0"}, "repository": {"type": "git", "url": "git+https://github.com/BerkeleyTrue/warning.git"}, "keywords": ["warning", "facebook", "react", "invariant"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.freecodecamp.com"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/BerkeleyTrue/warning/issues"}, "homepage": "https://github.com/BerkeleyTrue/warning", "gitHead": "6ef2da6b3da76eb6fcc735eaa52160fec2ed6078", "_id": "warning@3.0.0", "_shasum": "32e5377cb572de4ab04753bdf8821c01ed605b7c", "_from": ".", "_npmVersion": "3.8.7", "_nodeVersion": "4.2.4", "_npmUser": {"name": "berkeleytrue", "email": "<EMAIL>"}, "maintainers": [{"name": "berkeleytrue", "email": "<EMAIL>"}], "dist": {"shasum": "32e5377cb572de4ab04753bdf8821c01ed605b7c", "size": 2955, "noattachment": false, "tarball": "https://registry.npmmirror.com/warning/-/warning-3.0.0.tgz", "integrity": "sha512-jMBt6pUrKn5I+OGgtQ4YZLdhIeJmObddh6CsibPxyQ5yPZm1XExSyzC1LCNX7BzhxWgiHmizBWJTHJIjMjTQYQ=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/warning-3.0.0.tgz_1464922442833_0.31551201664842665"}, "directories": {}, "publish_time": 1464922443287, "_hasShrinkwrap": false, "_cnpm_publish_time": 1464922443287, "_cnpmcore_publish_time": "2021-12-15T16:50:53.873Z"}, "2.1.0": {"name": "warning", "version": "2.1.0", "description": "A mirror of Facebook's Warning", "main": "warning.js", "browser": "browser.js", "browserify": {"transform": ["loose-envify"]}, "files": ["browser.js", "warning.js"], "scripts": {"test": "NODE_ENV=production tap test/*.js && NODE_ENV=development tap test/*.js"}, "dependencies": {"loose-envify": "^1.0.0"}, "devDependencies": {"browserify": "^11.0.1", "tap": "^1.4.0"}, "repository": {"type": "git", "url": "git+https://github.com/r3dm/warning.git"}, "keywords": ["warning", "facebook", "react", "invariant"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://r3dm.com"}, "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/r3dm/warning/issues"}, "homepage": "https://github.com/r3dm/warning", "gitHead": "b7d56e3f6c21c3ee05af617222d16c7d80118443", "_id": "warning@2.1.0", "_shasum": "21220d9c63afc77a8c92111e011af705ce0c6901", "_from": ".", "_npmVersion": "2.11.1", "_nodeVersion": "1.6.4", "_npmUser": {"name": "berkeleytrue", "email": "<EMAIL>"}, "maintainers": [{"name": "berkeleytrue", "email": "<EMAIL>"}], "dist": {"shasum": "21220d9c63afc77a8c92111e011af705ce0c6901", "size": 2861, "noattachment": false, "tarball": "https://registry.npmmirror.com/warning/-/warning-2.1.0.tgz", "integrity": "sha512-O9pvum8nlCqIT5pRGo2WRQJPRG2bW/ZBeCzl7/8CWREjUW693juZpGup7zbRtuVcSKyGiRAIZLYsh3C0vq7FAg=="}, "directories": {}, "publish_time": 1443994015529, "_hasShrinkwrap": false, "_cnpm_publish_time": 1443994015529, "_cnpmcore_publish_time": "2021-12-15T16:50:54.069Z"}, "2.0.0": {"name": "warning", "version": "2.0.0", "description": "A mirror of Facebook's Warning", "main": "warning.js", "browser": "browser.js", "browserify": {"transform": ["envify"]}, "scripts": {"test": "echo 'you've been warned'"}, "dependencies": {"envify": "^3.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/r3dm/warning.git"}, "keywords": ["warning", "facebook", "react", "invariant"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://r3dm.com"}, "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/r3dm/warning/issues"}, "homepage": "https://github.com/r3dm/warning", "gitHead": "0c3fa99bf47c0cd12b95db2b674c3da2d6115319", "_id": "warning@2.0.0", "_shasum": "34235ed1061fbe75fff52fd9be23d730d9b4b6e0", "_from": ".", "_npmVersion": "2.11.1", "_nodeVersion": "1.6.4", "_npmUser": {"name": "berkeleytrue", "email": "<EMAIL>"}, "maintainers": [{"name": "berkeleytrue", "email": "<EMAIL>"}], "dist": {"shasum": "34235ed1061fbe75fff52fd9be23d730d9b4b6e0", "size": 2867, "noattachment": false, "tarball": "https://registry.npmmirror.com/warning/-/warning-2.0.0.tgz", "integrity": "sha512-8EMbsQwyRcydNZ+uX8logEEtzOJtF3fqtG2MM8sdvgPQqTOd5nYkhFa7WiQsxgoo5MgOgintG98zwoIiasumLQ=="}, "directories": {}, "publish_time": 1436661716055, "_hasShrinkwrap": false, "_cnpm_publish_time": 1436661716055, "_cnpmcore_publish_time": "2021-12-15T16:50:54.305Z"}, "1.0.2": {"name": "warning", "version": "1.0.2", "description": "A mirror of Facebook's Warning", "main": "warning.js", "scripts": {"test": "echo 'you've been warned'"}, "repository": {"type": "git", "url": "git+https://github.com/r3dm/warning.git"}, "keywords": ["warning", "facebook", "react", "invariant"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://r3dm.com"}, "license": "BSD", "bugs": {"url": "https://github.com/r3dm/warning/issues"}, "homepage": "https://github.com/r3dm/warning", "gitHead": "f6de4ea58115d650d6fda9e59d45063d7ea743ac", "_id": "warning@1.0.2", "_shasum": "5716cfb37147b4534b3e164480afffa6a255f0d9", "_from": ".", "_npmVersion": "2.9.1", "_nodeVersion": "1.6.4", "_npmUser": {"name": "berkeleytrue", "email": "<EMAIL>"}, "maintainers": [{"name": "berkeleytrue", "email": "<EMAIL>"}], "dist": {"shasum": "5716cfb37147b4534b3e164480afffa6a255f0d9", "size": 2372, "noattachment": false, "tarball": "https://registry.npmmirror.com/warning/-/warning-1.0.2.tgz", "integrity": "sha512-Lm7+cS4bdIQ66pkUdaWoLoO8lB/NgjF/+F7jTHaj4I1JxlA5AWs6P8rlLq2SAj9XOEC0sYMZiPL77Gu2Q+scDw=="}, "directories": {}, "publish_time": 1432969787232, "_hasShrinkwrap": false, "_cnpm_publish_time": 1432969787232, "_cnpmcore_publish_time": "2021-12-15T16:50:54.504Z"}, "1.0.1": {"name": "warning", "version": "1.0.1", "description": "A mirror of Facebook's Warning", "main": "warning.js", "scripts": {"test": "echo 'you've been warned'"}, "repository": {"type": "git", "url": "https://github.com/r3dm/warning.git"}, "keywords": ["warning", "facebook", "react", "invariant"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://r3dm.com"}, "license": "BSD", "bugs": {"url": "https://github.com/r3dm/warning/issues"}, "homepage": "https://github.com/r3dm/warning", "gitHead": "2acb7e49b0b9aa1ac52ff0761bbf3ca3db1b7a34", "_id": "warning@1.0.1", "_shasum": "58e9b758c0c79c10f8b13984a43c9195ec3e6f50", "_from": ".", "_npmVersion": "2.7.5", "_nodeVersion": "1.6.4", "_npmUser": {"name": "berkeleytrue", "email": "<EMAIL>"}, "maintainers": [{"name": "berkeleytrue", "email": "<EMAIL>"}], "dist": {"shasum": "58e9b758c0c79c10f8b13984a43c9195ec3e6f50", "size": 2287, "noattachment": false, "tarball": "https://registry.npmmirror.com/warning/-/warning-1.0.1.tgz", "integrity": "sha512-tRoxvvR0Vb0GskLTKRRQU+VihzKkmtqnzxlGC9QhBm/lOmP0Ox8O7/DXtP1Aseazx3ynsLutk9Zrvby47rGghQ=="}, "directories": {}, "publish_time": 1428590959325, "_hasShrinkwrap": false, "_cnpm_publish_time": 1428590959325, "_cnpmcore_publish_time": "2021-12-15T16:50:54.694Z"}, "1.0.0": {"name": "warning", "version": "1.0.0", "description": "A mirror of Facebook's Warning", "main": "warning.js", "scripts": {"test": "echo 'you've been warned'"}, "repository": {"type": "git", "url": "https://github.com/r3dm/warning.git"}, "keywords": ["warning", "facebook", "react", "invariant"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://r3dm.com"}, "license": "BSD", "bugs": {"url": "https://github.com/r3dm/warning/issues"}, "homepage": "https://github.com/r3dm/warning", "gitHead": "d2785e5680c88143bba459e1db1c85e4ecca5086", "_id": "warning@1.0.0", "_shasum": "fdb5867eac00ce17214686a4efe6617d5c8e2aa8", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "1.6.3", "_npmUser": {"name": "berkeleytrue", "email": "<EMAIL>"}, "maintainers": [{"name": "berkeleytrue", "email": "<EMAIL>"}], "dist": {"shasum": "fdb5867eac00ce17214686a4efe6617d5c8e2aa8", "size": 2193, "noattachment": false, "tarball": "https://registry.npmmirror.com/warning/-/warning-1.0.0.tgz", "integrity": "sha512-<PERSON><PERSON><PERSON><PERSON>vSJ8VDSeoNLZBkhMr1JkN5IgEOrWMwd5Okk0vtGtJLoOywpOiHQ8YOk7myOQqedIBlZD0tR/eqrQIZihw=="}, "directories": {}, "publish_time": 1427847793847, "_hasShrinkwrap": false, "_cnpm_publish_time": 1427847793847, "_cnpmcore_publish_time": "2021-12-15T16:50:54.908Z"}}, "bugs": {"url": "https://github.com/BerkeleyTrue/warning/issues"}, "homepage": "https://github.com/BerkeleyTrue/warning", "keywords": ["warning", "facebook", "react", "invariant"], "repository": {"type": "git", "url": "git+https://github.com/BerkeleyTrue/warning.git"}, "_source_registry_name": "default"}