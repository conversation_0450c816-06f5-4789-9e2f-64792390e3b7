<!--src\views\teacher\CourseDetailManager\StudentManagement\components\StudentTable.vue-->
<template>
  <div class="student-table-container">
    <el-table
      :data="filteredStudents"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        width="55"
      ></el-table-column>
      <el-table-column
        prop="studentId"
        label="学号"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="studentName"
        label="姓名"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="school"
        label="学校"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="department"
        label="院系"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="className"
        label="教学班"
        width="180"
      ></el-table-column>
    </el-table>
    
    <div v-if="!filteredStudents.length" class="empty-state">
      <div class="empty-image">
        <img src="" alt="班级暂无学生" style="width: 100%; height: 100%; object-fit: cover;">
      </div>
      <div class="empty-text">班级暂无学生，请添加学生</div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue';
import { ElTable, ElTag } from 'element-plus';

export default {
  components: {
    ElTable,
    ElTag
  },
  props: {
    filteredStudents: {
      type: Array,
      default: () => []
    },
    selectAll: {
      type: Boolean,
      default: false
    },
    selectedStudents: {
      type: Array,
      default: () => []
    }
  },
  emits: ['handle-selection-change'],
  setup(props, { emit }) {
    // 处理选择变化
    const handleSelectionChange = (selection) => {
      emit('handle-selection-change', selection);
    };

    return {
      handleSelectionChange
    };
  }
};
</script>

<style scoped>
.student-table-container {
  width: 100%;
  height: 70%;
  overflow-y: auto;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #909399;
}

.empty-image {
  width: 180px;
  height: 180px;
  border-radius: 50%;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 14px;
}
</style>