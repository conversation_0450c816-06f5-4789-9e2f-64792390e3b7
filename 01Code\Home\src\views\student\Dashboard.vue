//src\views\student\Dashboard.vue
<template>
  <div class="student-dashboard">
    <h1>学生学习中心</h1>
    
    <!-- 学生专属功能 -->
    <section class="student-actions">
      <button @click="viewCourses">我的课程</button>
      <button @click="submitAssignment">提交作业</button>
      <button @click="viewGrades">查看成绩</button>
    </section>

    <!-- 学习进度 -->
    <div class="progress-section">
      <h3>当前课程进度</h3>
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: progress + '%' }"></div>
      </div>
      <p>{{ progress }}% 已完成</p>
    </div>

    <!-- 待完成任务 -->
    <div class="todo-list">
      <h3>待完成任务</h3>
      <ul>
        <li v-for="task in pendingTasks" :key="task.id">
          {{ task.name }} - 截止: {{ task.deadline }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useAuthStore } from '@/stores/auth/auth.js'

const authStore = useAuthStore()
const progress = ref(65)
const pendingTasks = ref([
  { id: 1, name: '数学作业', deadline: '2023-11-15' },
  { id: 2, name: '物理实验报告', deadline: '2023-11-18' }
])

// 模拟学生专属方法
const viewCourses = () => {
  console.log('查看课程逻辑...')
}

const submitAssignment = () => {
  console.log('提交作业逻辑...')
}

const viewGrades = () => {
  console.log('查看成绩逻辑...')
}
</script>

<style scoped lang="scss">
@use '@/styles/student/dashboard.scss';
</style>
