//src\router\studentRoutes.js学生专属路由，登录后只有学生身份才可访问

const studentRoutes = [
  {
    path: '/student',
    component: () => import('@/views/DefaultLayout.vue'),
    children: [
      {
        path: 'dashboard',
        name: 'StudentDashboard',
        //component: () => import('@/views/student/Dashboard.vue'),
        //meta: { requiresAuth: true, role: '1', navTitle: '学生中心' }
        component: () => import('@/views/student/MyCourse/index.vue'),
        meta: { requiresAuth: true, role: '1' }
      },
      // 添加学生账号管理路由
      {
        path: 'account',
        name: 'StudentAccount',
        component: () => import('@/views/student/AccountManagement/index.vue'),
        meta: { requiresAuth: true, role: '1' }
      },
      {
        path: 'student-class',
        name: 'StudentClass',
        component: () => import('@/views/student/StudentClass/index.vue'),
        meta: { requiresAuth: true, role: '1' }
      },
      {
        path: 'my-course',
        name: 'MyCourse',
        component: () => import('@/views/student/MyCourse/index.vue'),
        meta: { requiresAuth: true, role: '1' }
      },
      {
        path: 'my-grade',
        name: 'MyGrade',
        component: () => import('@/views/student/MyGrade/index.vue'),
        meta: { requiresAuth: true, role: '1' }
      },
      {
        path: 'my-message',
        name: 'MyMessage',
        component: () => import('@/views/student/MyMessage/index.vue'),
        meta: { requiresAuth: true, role: '1' }
      },
      {
        path: 'my-exam',
        name: 'MyExam',
        component: () => import('@/views/student/MyExam/index.vue'),
        meta: { requiresAuth: true, role: '1' }
      },
      {
        path: 'my-collection',
        name: 'MyCollection',
        component: () => import('@/views/student/MyCollection/index.vue'),
        meta: { requiresAuth: true, role: '1' }
      },
      {
        //path: 'course-detail/:courseId',
        path: 'course-detail/:courseId',
        name: 'StudentCourseDetail',
        component: () => import('@/views/student/CourseDetail/index.vue'),
        redirect: { name: 'S-Task' },
        meta: { requiresAuth: true, role: '1', forceNavbarWhite: true },
        children: [
          {
            path: 's-courseCenter',
            name: 'S-CourseCenter',
            component: () => import('@/views/student/CourseDetail/CourseCenter/index.vue')
          },
          {
            path: 's-task',
            name: 'S-Task',
            component: () => import('@/views/student/CourseDetail/StudyTask/index.vue')
          },
          {
            path: 's-announcement',
            name: 'S-Announcement',
            component: () => import('@/views/student/CourseDetail/Announcement/index.vue')
          },
          {
            path: 's-examAndWork',
            name: 'S-ExamAndWork',
            component: () => import('@/views/student/CourseDetail/AssignmentExam/index.vue'),
          },
          {
            path: 's-grade',
            name: 'S-Grade',
            component: () => import('@/views/student/CourseDetail/CourseCenter/index.vue')
          },
          // 考试作业详情：
          {
            path: 'doExam',
            name: 'DoExam',
            component: () => import('@/views/student/CourseDetail/AssignmentExam/DoExam.vue')
          },
          {
            path: 'studentTaskDetail',
            name: 'StudentTaskDetail',
            component: () => import('@/views/student/CourseDetail/AssignmentExam/StudentTaskDetail.vue')
          },
          {
            path: 'answerDetail',
            name: 'AnswerDetail',
            component: () => import('@/views/student/CourseDetail/AssignmentExam/AnswerDetail.vue')
          }
        ]
      }
    ]
  }
]

export default studentRoutes