import axios from 'axios';
import {getKey} from '../../key.js';

const API_URL = 'https://open.bigmodel.cn/api/paas/v4/videos/generations';
const RESULT_QUERY_URL = 'https://open.bigmodel.cn/api/paas/v4/async-result/';
let currentRequestCancelToken = null;

// 视频生成
export async function generateVideo(prompt) {
  try {
    const apiKey = getKey('zhipu');

    // 取消之前的请求（如果有）
    if (currentRequestCancelToken) {
      currentRequestCancelToken.cancel('取消上一个视频生成请求');
    }
    currentRequestCancelToken = axios.CancelToken.source();

    // console.log('[Video Generation] Starting with prompt:', prompt);

    // 发起视频生成请求
    const generationResponse = await axios.post(API_URL, {
      model: 'cogvideox-flash',
      prompt: prompt,
      with_audio: true,
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': apiKey
      },
      cancelToken: currentRequestCancelToken.token
    });

    // console.log('[Video Generation] Initial response:', generationResponse.data);

    // 检查任务状态
    if (generationResponse.data.task_status === 'FAIL') {
      throw new Error('视频生成任务失败');
    }

    let videoData;

    // 如果任务仍在处理中，轮询查询结果
    if (generationResponse.data.task_status === 'PROCESSING') {
      const taskId = generationResponse.data.id;
      // console.log(`[Video Generation] Polling task ${taskId}`);
      videoData = await pollForResult(taskId, apiKey);
    }
    // 如果任务已经成功，直接返回结果
    else if (generationResponse.data.task_status === 'SUCCESS') {
      if (!generationResponse.data.video_result?.length) {
        throw new Error('API返回的数据结构不符合预期');
      }
      videoData = {
        url: generationResponse.data.video_result[0].url,
        cover_image_url: generationResponse.data.video_result[0].cover_image_url
      };
    } else {
      throw new Error(`未知的任务状态: ${generationResponse.data.task_status}`);
    }

    console.log('[Video Generation] Completed:', {
      url: videoData.url,
      prompt: prompt,
      timestamp: Date.now()
    });

    return {
      ...videoData,
      prompt: prompt,
      timestamp: Date.now() // 添加时间戳确保唯一性
    };

  } catch (error) {
    if (axios.isCancel(error)) {
      // console.log('[Video Generation] Request canceled:', error.message);
      throw new Error('视频生成已取消');
    }
    // console.error('[Video Generation] Failed:', error);
    throw error;
  } finally {
    currentRequestCancelToken = null;
  }
}

// 轮询查询任务结果
async function pollForResult(taskId, apiKey) {
  let attempts = 0;
  const maxAttempts = 30; // 最大尝试次数
  const interval = 3000; // 轮询间隔3秒
  const startTime = Date.now();

  // console.log(`[Polling] Starting polling for task ${taskId}`);

  while (attempts < maxAttempts) {
    try {
      const resultResponse = await axios.get(`${RESULT_QUERY_URL}${taskId}`, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': apiKey
        },
        params: {
          _t: Date.now() // 防止缓存
        }
      });

      // console.log(`[Polling] Attempt ${attempts + 1}:`, resultResponse.data);

      if (resultResponse.data.task_status === 'SUCCESS') {
        if (!resultResponse.data.video_result?.length) {
          throw new Error('API返回的数据结构不符合预期');
        }
        // console.log(`[Polling] Completed in ${attempts * 3}s`);
        return {
          url: resultResponse.data.video_result[0].url,
          cover_image_url: resultResponse.data.video_result[0].cover_image_url
        };
      }

      if (resultResponse.data.task_status === 'FAIL') {
        throw new Error(resultResponse.data.message || '视频生成任务失败');
      }

      attempts++;
      await new Promise(resolve => setTimeout(resolve, interval));

    } catch (error) {
      // console.error(`[Polling] Attempt ${attempts + 1} failed:`, error);
      if (attempts >= maxAttempts - 1) break;
      attempts++;
      await new Promise(resolve => setTimeout(resolve, interval));
    }
  }

  throw new Error(`视频生成任务超时，${maxAttempts * 3 / 60}分钟后仍未完成`);
}
