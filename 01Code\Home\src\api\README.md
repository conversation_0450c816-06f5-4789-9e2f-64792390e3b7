# 知识图谱API接口文档

## 概述

本文档描述了学生端知识图谱相关的前端API接口。这些接口都是公开的，不需要API认证。

## 接口列表

### 1. 获取树形图数据

**接口地址:** `/knowledge-graph/tree-view`

**请求方法:** GET

**请求参数:**
- `graphId` (string, 必填): 图谱ID

**响应格式:**
```json
{
  "code": 200,
  "msg": "操作成功",
  "result": [
    {
      "nodeId": "1388283778895450112",
      "graphId": "1388283778706706432",
      "nodeName": "全栈开发",
      "level": 0,
      "parentId": null,
      "isLeaf": 0,
      "sortOrder": 0,
      "offsetX": null,
      "offsetY": null,
      "tags": [],
      "children": [...]
    }
  ]
}
```

### 2. 获取环形图数据

**接口地址:** `/knowledge-graph/ring-view`

**请求方法:** GET

**请求参数:**
- `graphId` (string, 必填): 图谱ID
- `maxLevel` (number, 可选): 最大显示层级

**响应格式:**
```json
{
  "code": 200,
  "msg": "操作成功",
  "result": [
    {
      "nodeId": "1388283778895450112",
      "nodeName": "全栈开发",
      "level": 0,
      "offsetX": null,
      "offsetY": null,
      "parentId": null,
      "isLeaf": false
    },
    {
      "nodeId": "1388284092075741184",
      "nodeName": "test节点1-1",
      "level": 1,
      "offsetX": null,
      "offsetY": null,
      "parentId": "1388283778895450112",
      "isLeaf": false
    }
  ]
}
```

### 3. 获取所有标签

**接口地址:** `/knowledge-graph/tags/all`

**请求方法:** GET

**请求参数:** 无

**响应格式:**
```json
{
  "code": 200,
  "msg": "操作成功",
  "result": [
    {
      "tagId": "1387888120652894208",
      "tagName": "重点",
      "sortOrder": 0
    }
  ]
}
```

### 4. 获取标签统计

**接口地址:** `/knowledge-graph/tags/count`

**请求方法:** GET

**请求参数:**
- `graphId` (string, 必填): 图谱ID

**响应格式:**
```json
{
  "code": 200,
  "msg": "操作成功",
  "result": {
    "1387888177355689984": 1,
    "1387888233592918016": 2,
    "1387888120652894208": 3
  }
}
```

## 前端使用示例

### 导入API函数

```javascript
import { getTreeView, getRingView, getAllTags, getTagCount } from '@/api/teacher/graphManager/knowledgeGraph.js'
```

### 获取树形图数据

```javascript
const loadTreeData = async (graphId) => {
  try {
    const response = await getTreeView(graphId)
    if (response.code === 200) {
      console.log('树形图数据:', response.result)
      return response.result
    } else {
      throw new Error(response.msg)
    }
  } catch (error) {
    console.error('获取树形图数据失败:', error)
  }
}
```

### 获取环形图数据

```javascript
const loadRingData = async (graphId, maxLevel = null) => {
  try {
    const response = await getRingView(graphId, maxLevel)
    if (response.code === 200) {
      console.log('环形图数据:', response.result)
      return response.result
    } else {
      throw new Error(response.msg)
    }
  } catch (error) {
    console.error('获取环形图数据失败:', error)
  }
}
```

### 获取所有标签

```javascript
const loadAllTags = async () => {
  try {
    const response = await getAllTags()
    if (response.code === 200) {
      console.log('所有标签:', response.result)
      return response.result
    } else {
      throw new Error(response.msg)
    }
  } catch (error) {
    console.error('获取标签列表失败:', error)
  }
}
```

### 获取标签统计

```javascript
const loadTagCounts = async (graphId) => {
  try {
    const response = await getTagCount(graphId)
    if (response.code === 200) {
      console.log('标签统计:', response.result)
      return response.result
    } else {
      throw new Error(response.msg)
    }
  } catch (error) {
    console.error('获取标签统计失败:', error)
  }
}
```

### 并行加载数据

```javascript
const loadAllData = async (graphId, maxLevel = null) => {
  try {
    const [treeResponse, ringResponse, tagsResponse, countResponse] = await Promise.all([
      getTreeView(graphId),
      getRingView(graphId, maxLevel),
      getAllTags(),
      getTagCount(graphId)
    ])
    
    // 处理响应数据
    if (treeResponse.code === 200 && ringResponse.code === 200 && 
        tagsResponse.code === 200 && countResponse.code === 200) {
      return {
        treeData: treeResponse.result,
        ringData: ringResponse.result,
        allTags: tagsResponse.result,
        tagCounts: countResponse.result
      }
    }
  } catch (error) {
    console.error('加载数据失败:', error)
  }
}
```

## 数据转换

### 后端数据转前端格式

后端返回的树形数据需要转换为前端G6图表库需要的格式：

```javascript
const transformTreeData = (nodes) => {
  if (!nodes || !Array.isArray(nodes)) return null
  
  const transformNode = (node) => {
    return {
      id: node.nodeId,
      label: node.nodeName,
      tags: node.tags ? node.tags.map(tag => tag.tagName) : [],
      children: node.children ? node.children.map(transformNode) : null,
      collapsed: node.level >= 1
    }
  }
  
  if (nodes.length === 1) {
    return transformNode(nodes[0])
  }
  
  return {
    id: 'root',
    label: '课程名称',
    tags: [],
    children: nodes.map(transformNode),
    collapsed: false
  }
}
```

### 环形图数据转换

环形图数据需要转换为树形结构：

```javascript
const transformRingData = (data) => {
  if (!data || !Array.isArray(data)) return []
  
  // 构建树形结构
  const nodeMap = new Map()
  const rootNodes = []
  
  // 首先创建所有节点
  data.forEach(item => {
    nodeMap.set(item.nodeId, {
      id: item.nodeId,
      label: item.nodeName,
      level: item.level,
      parentId: item.parentId,
      isLeaf: item.isLeaf,
      children: []
    })
  })
  
  // 构建父子关系
  data.forEach(item => {
    const node = nodeMap.get(item.nodeId)
    if (item.parentId && nodeMap.has(item.parentId)) {
      const parent = nodeMap.get(item.parentId)
      parent.children.push(node)
    } else {
      rootNodes.push(node)
    }
  })
  
  return rootNodes
}
```

### 标签ID映射

标签统计数据中的tagId需要映射为tagName：

```javascript
const createTagIdToNameMap = (tags) => {
  const map = {}
  if (tags && Array.isArray(tags)) {
    tags.forEach(tag => {
      map[tag.tagId] = tag.tagName
    })
  }
  return map
}

const transformTagCounts = (counts, tagIdToNameMap) => {
  const transformed = {}
  Object.entries(counts).forEach(([tagId, count]) => {
    const tagName = tagIdToNameMap[tagId] || tagId
    transformed[tagName] = count
  })
  return transformed
}
```

## 错误处理

所有API接口都应该包含适当的错误处理：

```javascript
const handleApiError = (error, operation) => {
  console.error(`${operation}失败:`, error)
  // 可以显示用户友好的错误消息
  // 可以记录错误日志
  // 可以重试机制
}
```

## 测试

可以使用提供的API测试页面来验证接口是否正常工作：

访问 `/api-test` 路由来测试所有API接口。

## 注意事项

1. 所有接口都是公开的，不需要认证
2. 确保传入的graphId参数不为空
3. maxLevel参数为可选，不传时返回全部层级数据
4. 处理网络错误和服务器错误
5. 考虑添加加载状态和错误状态
6. 可以添加数据缓存机制提高性能 