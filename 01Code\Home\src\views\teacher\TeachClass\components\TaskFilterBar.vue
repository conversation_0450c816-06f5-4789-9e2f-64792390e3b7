<!--src\views\teacher\TeachClass\components\TaskFilterBar.vue-->
<template>
  <div class="task-filter-bar">
    <!-- 第一行：分类筛选(左) + 搜索框(右) -->
    <div class="first-row">
      <!-- 分类筛选 -->
      <div class="categories-container">
        <div class="filter-group categories-group" ref="categoriesGroup">
          <span 
            v-for="(category, index) in categories" 
            :key="index"
            :class="{ 'active': activeCategory === index }"
            @click="handleCategoryClick(index)"
            ref="categoryItems"
          >{{ category }}</span>
        </div>
        <!-- 滑动指示器 -->
        <div class="indicator" :style="{ transform: `translateX(${indicatorPosition}px)`, width: `${indicatorWidth}px` }"></div>
      </div>
      
      <!-- 搜索框 -->
      <div class="search-box">
        <input 
          v-model="searchQuery" 
          type="text" 
          placeholder="请输入搜索名称"
          @keyup.enter="handleSearch"
        >
        <button @click="handleSearch">
          <img 
            src="@/assets/img/Teacher/icon-seacher-normal.png" 
            alt="搜索"
            class="search-icon"
          >
        </button>
      </div>
    </div>
    
    <!-- 分隔线 -->
    <div class="divider"></div>
    
    <!-- 第二行：状态筛选 -->
    <div class="filter-group status-group">
      <span class="group-label">状态</span>
      <span 
        v-for="(status, index) in statuses" 
        :key="index"
        :class="{ 'active': activeStatus === index }"
        @click="handleStatusClick(index)"
      >{{ status }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue';

// 定义筛选数据
const categories = ['全部', '审核通过', '审核未通过', '未审核'];
const statuses = ['全部', '正在进行', '已结束'];

// 定义状态
const activeCategory = ref(0);
const activeStatus = ref(0);
const searchQuery = ref('');

// 指示器状态
const indicatorPosition = ref(0);
const indicatorWidth = ref(0);
const categoriesGroup = ref(null);
const categoryItems = ref([]);

const emit = defineEmits(['filter-change']);

// 更新指示器位置
const updateIndicatorPosition = (index) => {
  nextTick(() => {
    if (categoryItems.value && categoryItems.value[index]) {
      const element = categoryItems.value[index];
      const containerRect = categoriesGroup.value.getBoundingClientRect();
      const elementRect = element.getBoundingClientRect();
      
      indicatorPosition.value = elementRect.left - containerRect.left;
      indicatorWidth.value = elementRect.width;
    }
  });
};

// 处理分类点击
const handleCategoryClick = (index) => {
  activeCategory.value = index;
  updateIndicatorPosition(index);
  emit('filter-change', { 
    category: index,
    status: activeStatus.value,
    query: searchQuery.value
  });
};

// 处理状态点击
const handleStatusClick = (index) => {
  activeStatus.value = index;
  emit('filter-change', { 
    category: activeCategory.value,
    status: index,
    query: searchQuery.value
  });
};

// 搜索处理函数
const handleSearch = () => {
  emit('filter-change', { 
    category: activeCategory.value,
    status: activeStatus.value,
    query: searchQuery.value
  });
};

onMounted(() => {
  // 初始化指示器位置
  nextTick(() => {
    updateIndicatorPosition(activeCategory.value);
    
    // 监听窗口大小变化，更新指示器
    window.addEventListener('resize', () => {
      updateIndicatorPosition(activeCategory.value);
    });
  });
});

defineExpose({
  activeCategory,
  activeStatus,
  searchQuery
});
</script>

<style lang="scss" scoped>
@use '@/styles/teacher/TaskFilterBar';
// 定义间距变量
$spacing-sm: 8px;
$spacing-md: 12px;
$spacing-lg: 16px;

// 定义圆角
$border-radius: 4px;

.task-filter-bar {
  display: flex;
  flex-direction: column;
  padding: $spacing-md $spacing-lg;
  border-radius: $border-radius;
  
  // 第一行布局：分类筛选 + 搜索框
  .first-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative; // 为指示器提供定位参考
    
    // 分类筛选容器
    .categories-container {
      position: relative;
      overflow: hidden; // 防止内容溢出影响布局计算
      
      // 分类筛选组
      .categories-group {
        display: flex;
        position: relative; // 为子元素定位提供参考
        
        span {
          padding: $spacing-sm $spacing-md;
          margin-right: $spacing-sm;
          border-radius: $border-radius;
          cursor: pointer;
          transition: all 0.2s;
          white-space: nowrap;
          position: relative; // 确保元素有定位属性
          
          &:hover {
            background-color: $hover-color;
          }
          
          &.active {
            font-weight: 500;
          }
        }
      }
      
      // 滑动指示器
      .indicator {
        position: absolute;
        bottom: 0;
        height: 2px;
        background-color: $primary-color;
        transition: transform 0.3s ease, width 0.3s ease;
        will-change: transform, width; // 优化动画性能
      }
    }
    
    // 搜索框
    .search-box {
      display: flex;
      align-items: center;
      min-width: 200px;
      max-width: 300px;
      width: 30%;
      
      input {
        flex: 1;
        padding: $spacing-sm 32px $spacing-sm $spacing-md;
        border: 1px solid $border-color;
        border-radius: $border-radius;
        outline: none;
        transition: border-color 0.2s;
        
        &:focus {
          border-color: $primary-color;
        }
      }
      
      button {
        position: relative;
        right: $spacing-sm;
        background: none;
        border: none;
        padding: 0;
        cursor: pointer;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: -20px;
        
        .search-icon {
          width: 100%;
          height: 100%;
          filter: invert(100%); /* 反色效果 */
          transition: filter 0.2s;
        }
        
        &:hover .search-icon {
          filter: invert(50%) sepia(100%) saturate(1000%) hue-rotate(200deg); /* 悬停时变色效果 */
        }
      }
    }
  }
  
  // 分隔线
  .divider {
    height: 1px;
    background-color: $border-color;
    margin: $spacing-sm 0;
  }
  
  // 状态筛选组
  .status-group {
    display: flex;
    flex-wrap: wrap;
    
    .group-label {
      margin-right: $spacing-md;
      font-weight: 500;
      color: #333;
    }
    
    span {
      padding: $spacing-sm $spacing-md;
      margin-right: $spacing-sm;
      margin-bottom: $spacing-sm;
      border-radius: $border-radius;
      cursor: pointer;
      transition: all 0.2s;
      
      &:hover {
        background-color: $hover-color;
      }
      
      &.active {
        background-color: $primary-color;
        color: white;
      }
    }
  }
}
</style>