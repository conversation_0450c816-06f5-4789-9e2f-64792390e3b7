import { defineStore } from 'pinia'
import {
    getRootNodeId,
    createMainAbility,
    createSubAbility,
    getGraphsByCourse,
    getMainAbilities,
    getSubAbilitiesLite,
    getMainAbilityDetail,
    updateAbility,
    deleteAbility,
    getKnowledgeTree,
    getRelatedKnowledgeTree,
    updateRelatedKnowledge,
} from '@/api/teacher/graphManager/abilityGraph'

export const useAbilityGraphStore = defineStore('abilityGraph', {
    state: () => ({
        courseId: null,
        graphId: null,
        rootNodeId: null,
        knowledgeGraphId: null,
        loading: false,
        error: null,
        abilityDetailsCache: {},
        knowledgeTreeData: null,
        relatedKnowledgeCache: {},
        currentSubAbilityId: null,
    }),

    actions: {
        setCurrentSubAbility(id) {
            this.currentSubAbilityId = id;
        },

        // 初始化能力图谱
        async initAbilityGraph(courseId, graphId) {
            this.courseId = courseId;
            this.graphId = graphId;

            try {
                // 获取根节点ID
                const rootRes = await getRootNodeId(graphId);
                this.rootNodeId = rootRes.result;

                // 获取课程的知识图谱ID
                const graphsRes = await getGraphsByCourse(courseId);
                const knowledgeGraph = graphsRes.result.find(g => g.graphType === 0);
                if (knowledgeGraph) {
                    this.knowledgeGraphId = knowledgeGraph.graphId;
                    // 主动获取知识树数据
                    await this.fetchKnowledgeTree(this.knowledgeGraphId);
                }

                return true;
            } catch (error) {
                this.error = error.message || '初始化能力图谱失败';
                return false;
            }
        },

        // 创建主能力
        async createMainAbility(createVO) {
            if (!this.graphId || !this.rootNodeId) {
                throw new Error('图谱未初始化')
            }

            try {
                this.loading = true
                const res = await createMainAbility(createVO)

                // 处理重复名称错误
                if (res.code === 400) {
                    throw new Error(res.msg || '创建主能力失败: 名称重复')
                }

                return res.result
            } catch (error) {
                this.error = error.message || '创建主能力失败'
                throw error
            } finally {
                this.loading = false
            }
        },

        // 创建子能力
        async createSubAbility(data) {
            if (!this.graphId) {
                throw new Error('图谱未初始化')
            }

            try {
                this.loading = true
                const res = await createSubAbility(data)

                // 处理重复名称错误
                if (res.code === 400) {
                    throw new Error(res.msg || '创建子能力失败: 名称重复')
                }

                return res.result
            } catch (error) {
                this.error = error.message || '创建子能力失败'
                throw error
            } finally {
                this.loading = false
            }
        },

        // 获取主能力列表
        async fetchMainAbilities(graphId) {
            try {
                const res = await getMainAbilities(graphId);
                // 明确返回result字段，添加默认值
                return res?.result || []; 
            } catch (error) {
                console.error('获取主能力列表失败:', error);
                return []; // 确保始终返回数组
            }
        },

        // 获取子能力列表
        async fetchSubAbilities(parentId) {
            try {
                this.loading = true

                // 先检查缓存中是否有完整数据
                if (this.abilityDetailsCache[parentId]?.subAbilities) {
                    const cachedSubAbilities = this.abilityDetailsCache[parentId].subAbilities || []
                    const liteRes = await getSubAbilitiesLite(parentId)
                    const liteList = liteRes.result || []

                    return liteList.map(liteItem => {
                        const cachedItem = cachedSubAbilities.find(c => c.abilityId === liteItem.abilityId) || {}
                        return {
                            ...liteItem,
                            nodeDesc: cachedItem.nodeDesc || ''
                        }
                    })
                }

                // 如果没有缓存，先获取精简列表
                const liteRes = await getSubAbilitiesLite(parentId)
                const liteList = liteRes.result || []

                // 然后获取完整详情来补充描述
                const detailRes = await this.getMainAbilityDetail(parentId)
                const fullList = detailRes.result?.subAbilities || []

                // 合并数据
                const mergedList = liteList.map(item => {
                    const fullItem = fullList.find(f => f.abilityId === item.abilityId) || {}
                    return {
                        ...item,
                        nodeDesc: fullItem.nodeDesc || ''
                    }
                })

                // 缓存完整数据
                if (detailRes.result) {
                    this.abilityDetailsCache[parentId] = detailRes.result
                }

                return mergedList
            } catch (error) {
                this.error = error.message || '获取子能力列表失败'
                return []
            } finally {
                this.loading = false
            }
        },

        // 获取主能力详情（包含子能力数量）
        async fetchMainAbilitiesWithDetail(graphId) {
            try {
                this.loading = true;
                const response = await this.fetchMainAbilities(graphId);
                const mainAbilities = Array.isArray(response) ? response : [];
                
                if (mainAbilities.length === 0) {
                    return [];
                }
                
                const abilitiesWithDetail = await Promise.all(
                    mainAbilities.map(async ability => {
                        try {
                            const { result: detail = {} } = await this.getMainAbilityDetail(ability.abilityId);
                            return {
                                ...ability,
                                subAbilityCount: detail.subAbilityCount || 0,
                                subAbilities: detail.subAbilities || []
                            };
                        } catch (error) {
                            console.error(`处理能力 ${ability.abilityId} 失败:`, error);
                            return { ...ability, subAbilityCount: 0, subAbilities: [] };
                        }
                    })
                );
                
                return abilitiesWithDetail;
            } catch (error) {
                console.error('获取增强能力列表失败:', error);
                return [];
            } finally {
                this.loading = false;
            }
        },

        // 获取主能力详情并缓存
        async getMainAbilityDetail(abilityId) {
            try {
                // 先检查缓存
                if (this.abilityDetailsCache[abilityId]) {
                    return { result: this.abilityDetailsCache[abilityId] }
                }

                // 调用API获取详情
                const res = await getMainAbilityDetail(abilityId)
                if (res.result) {
                    this.abilityDetailsCache[abilityId] = res.result
                }
                return res
            } catch (error) {
                this.error = error.message || '获取主能力详情失败'
                throw error
            }
        },

        // 更新能力节点信息
        async updateAbilityInfo(updateVO) {
            try {
                this.loading = true
                const res = await updateAbility(updateVO)

                // 处理重复名称错误
                if (res.code === 400) {
                    throw new Error(res.msg || '更新能力节点失败: 名称重复')
                }

                // 清除相关缓存
                if (updateVO.nodeId && this.abilityDetailsCache[updateVO.nodeId]) {
                    delete this.abilityDetailsCache[updateVO.nodeId]
                }

                return res.result
            } catch (error) {
                this.error = error.message || '更新能力节点失败'
                throw error
            } finally {
                this.loading = false
            }
        },

        // 删除能力节点
        async deleteAbilityNode(abilityId) {
            try {
                this.loading = true
                const res = await deleteAbility(abilityId)

                // 清除缓存
                if (this.abilityDetailsCache[abilityId]) {
                    delete this.abilityDetailsCache[abilityId]
                }

                // 清除父能力的缓存（如果删除的是子能力）
                Object.keys(this.abilityDetailsCache).forEach(key => {
                    if (this.abilityDetailsCache[key].subAbilities?.some(sub => sub.abilityId === abilityId)) {
                        delete this.abilityDetailsCache[key]
                    }
                })

                return res.result
            } catch (error) {
                this.error = error.message || '删除能力节点失败'
                throw error
            } finally {
                this.loading = false
            }
        },

        // 获取知识树数据
        async fetchKnowledgeTree(graphId) {
            console.log('Fetching knowledge tree for graphId:', graphId);
            try {
                this.loading = true;
                const res = await getKnowledgeTree(graphId);
                console.log('Knowledge tree response:', res);
                this.knowledgeTreeData = res.result;
                return res.result;
            } catch (error) {
                console.error('获取知识树失败:', error);
                return null;
            } finally {
                this.loading = false;
            }
        },

        // 获取关联的知识树
        async fetchRelatedKnowledgeTree(abilityId) {
            try {
                this.loading = true;
                const res = await getRelatedKnowledgeTree(abilityId);
                // 确保返回的是知识点ID数组
                if (res.result) {
                    // 如果返回的是树形结构，需要展平提取nodeId
                    const flattenNodes = (nodes) => {
                        let result = [];
                        nodes.forEach(node => {
                            result.push(node.nodeId);
                            if (node.children) {
                                result = result.concat(flattenNodes(node.children));
                            }
                        });
                        return result;
                    };

                    return Array.isArray(res.result) ? flattenNodes(res.result) : res.result;
                }
                return [];
            } catch (error) {
                this.error = error.message || '获取关联知识树失败';
                return [];
            } finally {
                this.loading = false;
            }
        },

        // 批量获取所有子能力的关联知识点
        async fetchAllSubAbilitiesKnowledgeTree(subAbilities) {
            try {
                this.loading = true;

                // 并行获取所有子能力的知识点
                const promises = subAbilities.map(async (subAbility) => {
                    try {
                        const res = await getRelatedKnowledgeTree(subAbility.abilityId);
                        // 使用与fetchRelatedKnowledgeTree相同的处理逻辑
                        let knowledgeNodes = [];
                        if (res.result) {
                            const flattenNodes = (nodes) => {
                                let result = [];
                                nodes.forEach(node => {
                                    result.push(node.nodeId);
                                    if (node.children) {
                                        result = result.concat(flattenNodes(node.children));
                                    }
                                });
                                return result;
                            };
                            knowledgeNodes = Array.isArray(res.result) ? flattenNodes(res.result) : res.result;
                        }

                        return {
                            ...subAbility,
                            knowledgeNodes: knowledgeNodes || []
                        };
                    } catch (error) {
                        console.error(`获取子能力 ${subAbility.abilityId} 的知识点失败:`, error);
                        return {
                            ...subAbility,
                            knowledgeNodes: []
                        };
                    }
                });

                const subAbilitiesWithKnowledge = await Promise.all(promises);
                return subAbilitiesWithKnowledge;
            } catch (error) {
                console.error('批量获取子能力知识点失败:', error);
                this.error = error.message;
                return subAbilities.map(sub => ({ ...sub, knowledgeNodes: [] }));
            } finally {
                this.loading = false;
            }
        },

        // 更新关联知识点
        async updateRelatedKnowledgeNodes(abilityId, knowledgeIds) {
            try {
                this.loading = true;
                const res = await updateRelatedKnowledge({
                    abilityId,
                    knowledgeIds
                });

                // 清除缓存
                if (this.relatedKnowledgeCache[abilityId]) {
                    delete this.relatedKnowledgeCache[abilityId];
                }

                return res.result;
            } catch (error) {
                this.error = error.message || '更新关联知识点失败';
                throw error;
            } finally {
                this.loading = false;
            }
        },
    }
})