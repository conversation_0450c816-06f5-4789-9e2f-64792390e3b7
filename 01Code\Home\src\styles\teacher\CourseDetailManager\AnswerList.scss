//src\styles\teacher\CourseDetailManager\AnswerItem.scss
@use "@/styles/variables.scss" as *;

.answer-list {
  background-color: #fff;
  border-radius: 8px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .answer-list-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
    color: #333;

    .iconfont {
      margin-right: 4px;
      color: #1677ff; 
    }
  }
}

.answer-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 24px;
  gap: 8px;
  
  .page-btn {
    padding: 6px 12px;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    font-size: 14px;
    color: #666;
    background-color: #fff;
    cursor: pointer;
    
    &:hover:not(.disabled) {
      background-color: #f5f5f5;
    }
    
    &.disabled {
      color: #ccc;
      cursor: not-allowed;
      border-color: #eee;
      background-color: #f9f9f9;
      opacity: 0.5;
    }
  }
  
  .page-info {
    font-size: 14px;
    color: #666;
  }
}