<template>
  <el-dialog
    title="选择班级"
    :model-value="visible"
    width="30%"
    @update:model-value="$emit('update:visible', $event)"
    @close="handleClose"
  >
    <el-select 
      v-model="selectedClass" 
      placeholder="请选择班级" 
      style="width: 100%"
      loading-text="加载班级中..."
      :loading="loading"
    >
      <el-option
        v-for="cls in classes"
        :key="cls.id"
        :label="cls.name"
        :value="cls"
      />
      <template #empty>
        <div class="empty-classes">暂无班级数据</div>
      </template>
    </el-select>
    
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button 
        type="primary" 
        @click="handleConfirm"
        :disabled="!selectedClass"
      >
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script>
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';

export default {
  props: {
    visible: Boolean,
    classes: Array
  },
  emits: ['update:visible', 'close', 'confirm'],
  setup(props, { emit }) {
    const selectedClass = ref(null);
    const loading = ref(false);

    watch(() => props.visible, (visible) => {
      if (visible) {
        selectedClass.value = null;
        if (props.classes.length === 0) {
          ElMessage.warning('没有可用的班级，请先创建班级');
        }
      }
    });

    const handleClose = () => {
      selectedClass.value = null;
      emit('update:visible', false);
      emit('close');
    };

    const handleConfirm = () => {
      if (!selectedClass.value) {
        ElMessage.warning('请先选择班级');
        return;
      }
      emit('confirm', selectedClass.value);
      handleClose();
    };

    return {
      selectedClass,
      loading,
      handleClose,
      handleConfirm
    };
  }
};
</script>

<style scoped>
.empty-classes {
  padding: 10px;
  color: #999;
  text-align: center;
}
</style>