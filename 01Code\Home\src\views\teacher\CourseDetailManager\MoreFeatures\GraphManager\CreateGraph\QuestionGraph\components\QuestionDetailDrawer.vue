<template>
  <div class="knowledge-detail-drawer" :class="{ open: isOpen }">
    <div class="drawer-content">
      <template v-if="isRootNode">
        <div class="empty-state warning">该节点不可添加详情</div>
      </template>
      <template v-else-if="resolvedCurrentNode">
        <div class="section">
          <h2>{{ isQuestionNode ? "问题节点信息" : "章节节点信息" }}</h2>
          <div class="node-info">
            <div class="form-group">
              <label>{{
                isQuestionNode ? "问题节点名称" : "章节节点名称"
              }}</label>
              <input v-model="editableNode.nodeName" type="text" />
            </div>
            <div class="form-group">
              <label>{{
                isQuestionNode ? "问题节点描述" : "章节节点描述"
              }}</label>
              <textarea
                v-model="editableNode.nodeDesc"
                placeholder="请输入节点描述"></textarea>
            </div>
            <!-- 问题节点显示答案字段 -->
            <div class="form-group" v-if="isQuestionNode">
              <label>问题答案</label>
              <textarea
                v-model="editableNode.answer"
                placeholder="请输入问题答案"></textarea>
            </div>

            <div class="form-actions">
              <button class="save-btn" @click="saveNodeInfo">保存修改</button>
              <button class="cancel-btn" @click="resetEditableNode">
                取消
              </button>
            </div>
          </div>
        </div>
        <!-- 学习资源部分 - 只有问题节点显示 -->
        <div class="section" v-if="showResources">
          <div class="section-header">
            <h2>学习资源({{ stats.resources }})</h2>
            <button class="link-btn" @click="openResourceAssociation">
              关联资源
            </button>
          </div>
          <div class="empty-state error" v-if="stats.resources === 0">
            问题尚未绑定资源，请点击「关联资源」选择绑定资源
          </div>
          <div class="resource-list" v-if="resourceList.length">
            <div
              class="resource-item"
              v-for="res in resourceList"
              :key="res.resourceId">
              <img
                :src="getResourceIcon(res.resourceType)"
                class="resource-icon" />
              <span class="resource-name">{{ res.resourceName }}</span>
              <button
                class="close-btn"
                @click.stop="handleRemove(res)"
                title="取消关联">
                ×
              </button>
            </div>
          </div>
          <div v-else-if="stats.resources > 0" class="empty-state error">
            问题尚未绑定资源，请点击「关联资源」选择绑定资源
          </div>
        </div>

        <!-- 章节节点的提示信息 -->
        <div class="section" v-if="isChapterNode">
          <!-- <div class="empty-state warning">
            章节节点用于组织问题结构，不支持关联学习资源和练习题
          </div> -->
        </div>
      </template>
      <template v-else>
        <div class="empty-state">请选中节点来添加详情</div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, watchEffect } from "vue";
import { useRoute } from "vue-router";
import { useRouter } from "vue-router";
import { useQuestionGraphStore } from "@/stores/teacher/graphManager/questionGraphStore";

import videoIcon from "@/assets/courseMap-icon/resourceType/videoIcon.svg";
import bookIcon from "@/assets/courseMap-icon/resourceType/bookIcon.svg";
import pptIcon from "@/assets/courseMap-icon/resourceType/pptIcon.svg";
import wordIcon from "@/assets/courseMap-icon/resourceType/wordIcon.svg";
import externalIcon from "@/assets/courseMap-icon/resourceType/externalIcon.svg";
import otherIcon from "@/assets/courseMap-icon/resourceType/otherIcon.svg";

const emit = defineEmits(["update-node"]);
const store = useQuestionGraphStore();
const route = useRoute();
const router = useRouter();
const resolvedCurrentNode = ref(null);

const props = defineProps({
  isOpen: Boolean,
  currentNode: [Object, String, Promise],
  stats: Object,
  editableNode: Object,
});

// 处理 Promise 类型的 props
watch(
  () => props.currentNode,
  async (newVal) => {
    if (newVal instanceof Promise) {
      resolvedCurrentNode.value = await newVal;
    } else {
      resolvedCurrentNode.value = newVal;
    }
  },
  { immediate: true }
);

// 计算属性：判断当前是否是根节点
const isRootNode = computed(() => {
  if (!resolvedCurrentNode.value) return false;
  return (
    resolvedCurrentNode.value === "root" ||
    resolvedCurrentNode.value?.id === store.rootNodeId
  );
});

// 计算属性：判断当前节点类型
const currentNodeType = computed(() => {
  if (!resolvedCurrentNode.value) return null;
  if (isRootNode.value) return "ROOT";
  const nodeType = resolvedCurrentNode.value?.nodeType || null;
  console.log(
    "当前节点类型:",
    nodeType,
    "节点数据:",
    resolvedCurrentNode.value
  );
  return nodeType;
});

// 计算属性：判断是否为章节节点
const isChapterNode = computed(() => {
  return currentNodeType.value === "CHAPTER";
});

// 计算属性：判断是否为问题节点
const isQuestionNode = computed(() => {
  return currentNodeType.value === "QUESTION";
});

// 计算属性：是否显示学习资源
const showResources = computed(() => {
  const shouldShow = isQuestionNode.value;
  console.log(
    "是否显示资源:",
    shouldShow,
    "是否为问题节点:",
    isQuestionNode.value
  );
  return shouldShow; // 只有问题节点显示学习资源
});

// 计算属性：是否显示练习题
const showExercises = computed(() => {
  return false; // 问题图谱中都不显示练习题
});

// 保存节点信息
const saveNodeInfo = async () => {
  if (!props.editableNode.nodeId) return;

  try {
    // 更新节点基本信息（包含答案字段）
    const updateResult = await store.updateNodeInfo({
      nodeId: props.editableNode.nodeId,
      nodeName: props.editableNode.nodeName,
      nodeDesc: props.editableNode.nodeDesc,
      answer: props.editableNode.answer,
    });

    if (updateResult) {
      // 刷新节点树
      await store.fetchGraphTree(route.params.graphId);

      // 通知父组件更新
      emit("update-node", {
        label: props.editableNode.nodeName,
        description: props.editableNode.nodeDesc,
      });
    } else {
      alert("节点信息更新失败");
    }
  } catch (error) {
    console.error("保存节点信息失败:", error);
    // 根据后端错误类型显示不同提示
    if (error.message && error.message.includes("已存在相同名称")) {
      alert("同一图谱内已存在相同名称的节点，请修改节点名称");
    } else if (error.message && error.message.includes("不能为空")) {
      alert("节点名称不能为空");
    } else {
      alert("保存失败：" + (error.message || "未知错误"));
    }
  }
};

// 重置编辑节点数据
const resetEditableNode = () => {
  if (props.editableNode) {
    // 重置为原始数据
    const originalNode = resolvedCurrentNode.value;
    if (originalNode && originalNode !== "root") {
      props.editableNode.nodeName = originalNode.label || "";
      props.editableNode.nodeDesc = originalNode.description || "";
      props.editableNode.answer = originalNode.answer || "";
    }
  }
};

const openResourceAssociation = async () => {
  if (!props.currentNode || props.currentNode === "root") return;

  // 从store获取courseId
  const courseId = store.courseId;
  const currentNode = await props.currentNode;

  // 添加调试信息
  console.log(
    "当前路由列表:",
    router.getRoutes().map((r) => ({ name: r.name, path: r.path }))
  );
  console.log(
    "ResourceAssociation路由是否存在:",
    router.hasRoute("ResourceAssociation")
  );

  router.push({
    name: "QuestionResourceAssociation",
    params: {
      courseId: courseId,
      nodeId: currentNode.id || currentNode.nodeId, // 根据实际结构调整
    },
  });
};

// 新增：资源列表响应式变量
const resourceList = ref([]);

// 监听 currentNode 变化，获取资源列表
watchEffect(async () => {
  try {
    // 处理 Promise 类型的 currentNode
    const currentNode =
      props.currentNode instanceof Promise
        ? await props.currentNode
        : props.currentNode;

    if (!currentNode || currentNode === "root") {
      resourceList.value = [];
      return;
    }

    // 获取节点ID - 兼容不同格式
    const nodeId = currentNode.id || currentNode.nodeId;
    console.log("获取资源列表 - 节点ID:", nodeId, "节点数据:", currentNode);
    if (nodeId) {
      const resources = await store.fetchNodeResources(nodeId);
      resourceList.value = resources;
      console.log("获取到的资源列表:", resources);
    } else {
      console.log("节点ID为空，清空资源列表");
      resourceList.value = [];
    }
  } catch (error) {
    console.error("获取资源列表失败:", error);
    resourceList.value = [];
  }
});

function getResourceIcon(type) {
  switch (type) {
    case 1:
      return videoIcon;
    case 2:
      return bookIcon;
    case 3:
      return pptIcon;
    case 4:
      return wordIcon;
    case 5:
      return externalIcon;
    case 6:
      return otherIcon;
    default:
      return otherIcon;
  }
}

async function handleRemove(res) {
  // 确认删除操作
  const confirmed = confirm(`确定要取消关联资源"${res.resourceName}"吗？`);
  if (!confirmed) return;

  try {
    // 正确处理可能为Promise的currentNode
    const currentNode =
      props.currentNode instanceof Promise
        ? await props.currentNode
        : props.currentNode;

    // 获取节点ID - 兼容不同格式
    const nodeId = currentNode?.id || currentNode?.nodeId;
    if (!nodeId) {
      console.error("无法获取节点ID");
      return;
    }

    console.log(
      `开始取消资源关联 [nodeId: ${nodeId}, resourceId: ${res.resourceId}]`
    );
    const success = await store.unlinkResourceFromNode(nodeId, res.resourceId);
    if (success) {
      console.log("取消资源关联成功，重新获取资源列表");
      // 静默更新，不显示成功提示（用户体验更好）
      // 重新获取资源列表（后端会自动重新排序）
      const list = await store.fetchNodeResources(nodeId);
      resourceList.value = list;

      // 更新统计数据
      if (props.stats) {
        props.stats.resources = list.length;
      }
      console.log(`资源列表已更新，当前资源数量: ${list.length}`);
    } else {
      console.error("取消资源关联失败");
      alert("取消关联失败，请重试");
    }
  } catch (error) {
    console.error("取消关联操作出错:", error);
    if (error.message && error.message.includes("为空")) {
      alert("参数错误，请刷新页面后重试");
    } else {
      alert("操作失败：" + (error.message || "未知错误"));
    }
  }
}
</script>

<style lang="scss" scoped>
$drawer-width: 400px;
$transition-duration: 0.3s;

.knowledge-detail-drawer {
  position: fixed;
  will-change: transform;
  top: 60px;
  right: -$drawer-width;
  bottom: 0;
  width: $drawer-width;
  background-color: #fff;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  transition: right $transition-duration ease;
  z-index: 100;
  overflow-y: auto;
  border-left: 1px solid #eee;

  &.open {
    right: 0;
  }

  .drawer-content {
    padding: 20px;

    .section {
      margin-bottom: 30px;

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;

        h2 {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          margin: 0;
        }

        .link-btn {
          background-color: #4c7bff;
          color: #fff;
          padding: 6px 12px;
          font-size: 14px;
          border: none;
          border-radius: 6px;
          cursor: pointer;
          transition: background 0.2s;

          &:hover {
            background-color: #3b6fe0;
          }

          &:active {
            background-color: #345fd6;
          }
        }
      }

      .empty-state {
        padding: 15px;
        background-color: #f9f9f9;
        border-radius: 6px;
        color: #666;
        text-align: center;
        font-size: 14px;

        &.error {
          background-color: #fff0f0;
          color: #ff4d4f;
        }
      }
    }
  }
}

.node-info {
  padding: 10px 0;
}

.form-group {
  margin-bottom: 15px;

  label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    font-size: 14px;
    color: #555;
  }

  input,
  textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
  }

  textarea {
    min-height: 80px;
    resize: vertical;
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;

  button {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .save-btn {
    background-color: #4c7bff;
    color: white;
    border: none;

    &:hover {
      background-color: #3a6ae0;
    }
  }

  .cancel-btn {
    background-color: #fff;
    border: 1px solid #ccc;
    color: #666;

    &:hover {
      background-color: #f5f5f5;
    }
  }
}

.tag-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.tag {
  background-color: #eef2ff;
  color: #4c7bff;
  font-size: 12px;
  padding: 4px 10px;
  border-radius: 999px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: #d6e0ff;
  }
}

.tag.selected {
  background-color: #4c7bff;
  color: white;
}

.empty-state {
  padding: 20px;
  text-align: center;
  color: #666;
  background-color: #f9f9f9;
  border-radius: 6px;
  margin-top: 10px;

  &.warning {
    background-color: #fff8e6;
    color: #faad14;
  }

  &.error {
    background-color: #fff0f0;
    color: #ff4d4f;
  }
}

.resource-list {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  gap: 14px;
}

.resource-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 15px;
  color: #333;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(140, 120, 200, 0.08);
  padding: 14px 18px;
  transition: box-shadow 0.18s, background 0.18s;
  cursor: pointer;
  border: 1.5px solid #ece8f4;
  position: relative;
}

.resource-item:hover {
  background: #f6f3fd;
  box-shadow: 0 4px 16px rgba(140, 120, 200, 0.16);
  border-color: #8a6de3;
}

.resource-icon {
  width: 22px;
  height: 22px;
}

.resource-name {
  font-size: 15px;
  flex: 1;
  word-break: break-all;
}

.close-btn {
  display: none;
  position: absolute;
  right: 14px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #1dbec7;
  font-size: 14px;
  cursor: pointer;
  padding: 0 4px;
  border-radius: 50%;
  transition: background 0.15s, color 0.15s;
}

.resource-item:hover .close-btn {
  display: block;
  color: #ffffff;
  background: #af9ed6;
}

.close-btn:hover {
  background: #ffeaea;
  color: #d9363e;
}
</style>
