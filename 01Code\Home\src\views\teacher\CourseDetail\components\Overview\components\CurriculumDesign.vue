<template>
  <div class="course-system-attachment">
    <div class="course-design">
      <h3>课程体系设计</h3>
    </div>
    
    <!-- 上传文件区域 -->
    <div class="file-upload-area">
      <h3>附件上传</h3>
      <div class="upload-box">
        <div class="upload-tip">
          <img src="https://cdn-icons-png.flaticon.com/512/869/869869.png" alt="上传图标" class="upload-icon">
          <p class="upload-restriction">
            仅支持上传 Word/PDF/PPT/Excel/图片文件，文件大小不超过 2G
          </p>
          <el-upload
            class="upload-button"
            action="#" 
            :before-upload="beforeUpload"
            :on-change="handleFileChange"
            :file-list="fileList"
            accept=".doc,.docx,.pdf,.ppt,.pptx,.xls,.xlsx,.png,.jpg,.jpeg"
          >
            <el-button type="primary">
              <span class="upload-icon-text">📁</span>
              <span class="upload-text">添加附件</span>
            </el-button>
          </el-upload>
        </div>
        
        <div class="uploaded-files" v-if="fileList.length > 0">
          <p>已选文件：</p>
          <ul>
            <li v-for="(file, index) in fileList" :key="index">
              {{ file.name }}
              <el-button 
                size="mini" 
                type="text" 
                @click="removeFile(index)"
                class="remove-btn"
              >
                移除
              </el-button>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CourseSystemAttachment',
  data() {
    return {
      fileList: []
    }
  },
  methods: {
    beforeUpload(file) {
      // 文件大小限制为2G（即2048MB）
      const isLimit = file.size / 1024 / 1024 <= 2048;
      if (!isLimit) {
        this.$message.error('文件大小不能超过 2G！');
      }
      return isLimit;
    },
    
    handleFileChange(file, fileList) {
      this.fileList = fileList;
    },
    
    removeFile(index) {
      this.fileList.splice(index, 1);
    }
  }
}
</script>

<style scoped>
.course-system-attachment {
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.file-upload-area {
  margin-top: 20px;
  padding: 20px;
  border: 2px dashed #e4e7ed;
  border-radius: 8px;
  background-color: #fafafa;
  transition: all 0.3s;
}

.file-upload-area:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1);
}

.upload-box {
  text-align: center;
  padding: 30px 0;
}

.upload-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 15px;
  opacity: 0.8;
}

.upload-restriction {
  color: #909399;
  font-size: 14px;
  margin-bottom: 20px;
}

.upload-button {
  margin-top: 15px;
}

.upload-icon-text {
  margin-right: 8px;
  font-size: 18px;
}

.upload-text {
  font-size: 15px;
}

.uploaded-files {
  text-align: left;
  margin-top: 25px;
}

.uploaded-files p {
  font-weight: bold;
  margin-bottom: 10px;
  color: #606266;
}

.uploaded-files ul {
  list-style: none;
  padding: 0;
}

.uploaded-files li {
  margin-bottom: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.remove-btn {
  margin-left: 10px;
  color: #f56c6c;
  font-size: 14px;
}
</style>