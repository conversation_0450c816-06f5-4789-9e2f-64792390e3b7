<template>
  <div class="attendance-container">
    <!-- 顶部控制栏 -->
    <div class="control-bar">
      <div class="class-selector">
        <label for="class-select">班级：</label>
        <select id="class-select" v-model="selectedClass" @change="filterByClass">
          <option value="">全部班级</option>
          <option v-for="classItem in classes" :key="classItem.id" :value="classItem.id">
            {{ classItem.name }}
          </option>
        </select>
      </div>
      <div class="search-box">
        <input
          type="text"
          placeholder="请输入学生姓名或学号"
          v-model="searchQuery"
          @input="filterStudents"
        />
        <span class="search-icon">🔍</span>
      </div>
    </div>

    <!-- 考勤表格 -->
    <div class="table-container">
      <table class="attendance-table">
        <thead>
          <tr>
            <th @click="sortBy('id')">
              学号
              <span class="sort-icon" :class="{ active: sortField === 'id' }">
                {{ sortOrder === 'asc' ? '↑' : '↓' }}
              </span>
            </th>
            <th>姓名</th>
            <th>出勤率</th>
            <th v-for="(sign, index) in attendanceDates" :key="index">
              第{{ index + 1 }}次签到<br>
              <span class="sign-date">{{ formatDate(sign.date) }}</span>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="student in filteredStudents" :key="student.id">
            <td>{{ student.id }}</td>
            <td>{{ student.name }}</td>
            <td :class="getAttendanceRateClass(student.attendanceRate)">
              {{ student.attendanceRate }}%
            </td>
            <td v-for="(sign, index) in attendanceDates" :key="index">
              <span v-if="student.attendance[index]" class="present">✔</span>
              <span v-else class="absent">✘</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 模拟数据
const classes = ref([
  { id: 1, name: '数字媒体技术111班(1)' },
  { id: 2, name: '数字媒体技术111班(2)' },
  { id: 3, name: '数字媒体技术112班(1)' }
])

// 修改学生数据，添加classId字段
const students = ref([
  {
    id: '2300470107',
    name: '潘颖珊',
    classId: 1, // 添加班级ID
    attendanceRate: 100,
    attendance: [true, true, false, true]
  },
  {
    id: '2300470108',
    name: '张三',
    classId: 1, // 添加班级ID
    attendanceRate: 75,
    attendance: [true, false, true, true]
  },
  {
    id: '2300470109',
    name: '李四',
    classId: 2, // 添加班级ID
    attendanceRate: 50,
    attendance: [true, false, false, true]
  },
  {
    id: '2300470110',
    name: '王五',
    classId: 3, // 添加班级ID
    attendanceRate: 80,
    attendance: [true, true, true, false]
  }
])

const attendanceDates = ref([
  { date: '2023-09-01' },
  { date: '2023-09-08' },
  { date: '2023-09-15' },
  { date: '2023-09-22' }
])

// 状态管理
const selectedClass = ref('') // 默认为空，显示全部班级
const searchQuery = ref('')
const sortField = ref('id')
const sortOrder = ref('asc')

// 过滤和排序学生数据
const filteredStudents = computed(() => {
  let result = [...students.value]
  
  // 班级筛选
  if (selectedClass.value) {
    result = result.filter(s => s.classId === selectedClass.value)
  }
  
  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(s => 
      s.name.toLowerCase().includes(query) || 
      s.id.toLowerCase().includes(query)
    )
  }
  
  // 排序
  if (sortField.value) {
    result.sort((a, b) => {
      let comparison = 0
      if (a[sortField.value] > b[sortField.value]) {
        comparison = 1
      } else if (a[sortField.value] < b[sortField.value]) {
        comparison = -1
      }
      return sortOrder.value === 'asc' ? comparison : -comparison
    })
  }
  
  return result
})

// 方法
const sortBy = (field) => {
  if (sortField.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortField.value = field
    sortOrder.value = 'asc'
  }
}

const filterStudents = () => {
  // 搜索功能已经在计算属性中实现
}

const filterByClass = () => {
  // 班级筛选功能已经在计算属性中实现
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return `${date.getMonth() + 1}/${date.getDate()}`
}

const getAttendanceRateClass = (rate) => {
  if (rate >= 90) return 'high-rate'
  if (rate >= 70) return 'medium-rate'
  return 'low-rate'
}

// 初始化
onMounted(() => {
  // 这里可以添加API调用获取初始数据
})
</script>

<style lang="scss" scoped>
.attendance-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  font-family: 'Microsoft YaHei', sans-serif;
}

.control-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px 0;
  
  .class-selector {
    select {
      padding: 8px 12px;
      border-radius: 4px;
      border: 1px solid #ddd;
      font-size: 14px;
      min-width: 180px;
      
      &:focus {
        outline: none;
        border-color: #409eff;
      }
    }
  }
  
  .search-box {
    position: relative;
    
    input {
      padding: 8px 12px 8px 32px;
      border-radius: 4px;
      border: 1px solid #ddd;
      width: 250px;
      font-size: 14px;
    }
    
    .search-icon {
      position: absolute;
      left: 10px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}

.table-container {
  width: 100%;
  overflow-x: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.attendance-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 800px;
  
  th, td {
    padding: 12px 15px;
    text-align: center;
    border-bottom: 1px solid #eee;
  }
  
  th {
    background-color: #f8f9fa;
    font-weight: 600;
    white-space: nowrap;
    cursor: pointer;
    
    &:first-child {
      text-align: left;
    }
    
    .sort-icon {
      opacity: 0;
      margin-left: 5px;
      transition: opacity 0.2s;
      
      &.active {
        opacity: 1;
      }
    }
    
    &:hover .sort-icon {
      opacity: 0.5;
    }
  }
  
  td {
    &:first-child {
      text-align: left;
    }
  }
  
  .sign-date {
    font-size: 0.8em;
    color: #666;
  }
  
  .present {
    color: #28a745;
    font-weight: bold;
  }
  
  .absent {
    color: #dc3545;
    font-weight: bold;
  }
  
  .high-rate {
    color: #28a745;
    font-weight: bold;
  }
  
  .medium-rate {
    color: #ffc107;
    font-weight: bold;
  }
  
  .low-rate {
    color: #dc3545;
    font-weight: bold;
  }
  
  tr:hover {
    background-color: #f8f9fa;
  }
}
</style>