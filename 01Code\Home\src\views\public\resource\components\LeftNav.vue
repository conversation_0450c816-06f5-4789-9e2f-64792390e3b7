<template>
  <nav class="nav-container">
    <h2>资源类型</h2>
    <button
      v-for="(item, index) in navItems"
      :key="item.key"
      :class="{ active: activeIndex === index }"
      @click="handleNavClick(index, item.key)"
    >
      {{ item.label }}
    </button>
  </nav>
</template>

<script setup>
import { ref } from 'vue'

const emit = defineEmits(['nav-click'])
const activeIndex = ref(0)

const navItems = [
  { key: 'digital-media', label: '数媒资源' },
  { key: 'virtual-simulation', label: '虚拟仿真资源' },
  { key: 'open-education', label: '开放教育资源' },
  { key: 'cultural-heritage', label: '非遗传承' },
  { key: 'graduation-works', label: '毕业作品' }
]

const handleNavClick = (index, tabKey) => {
  activeIndex.value = index
  emit('nav-click', tabKey)
}
</script>

<style lang="scss" scoped>
.nav-container {
  display: flex;
  flex-direction: column;
  gap: 1vw;

  h2 {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 1vw;
  }

  button {
    width: 100%;
    padding: 0.8vw 2vw;
    border: $course-tabs-solid solid 1px;
    background-color: white;
    cursor: pointer;
    border-radius: 2vw;
    font-size: 1rem;
    transition: all 0.3s ease;
    text-align: left;

    &:hover {
      background-color: $course-tabs;
      color: $primary-color;
      border: $primary-color solid 1px;
    }

    &.active {
      background-color: $course-tabs;
      color: $primary-color;
      border: $primary-color solid 1px;
      font-weight: bold;
    }
  }
}
</style>