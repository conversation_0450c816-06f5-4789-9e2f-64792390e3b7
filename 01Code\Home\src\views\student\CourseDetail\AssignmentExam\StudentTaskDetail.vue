<template>
    <div class="container" v-if="!loading">
        <!-- 顶部栏 -->
        <el-header class="header">
            <el-button class="back-button" link @click="goBack">返回</el-button>
            <div class="header-title">{{ isExam ? '我的考试' : '我的作业' }}</div>
            <div style="width: 60px"></div>
        </el-header>

        <!-- 主体内容 -->
        <el-main class="main">
            <el-card class="card">
                <!-- 试卷/作业信息 -->
                <div class="title">{{ title }}</div>

                <div class="deadline" v-if="isExam">
                    考试时间：{{ formatTime(startTime) }} - {{ formatTime(endTime) }}
                </div>
                <div class="deadline" v-else>
                    截止时间：{{ formatTime(endTime) }}
                </div>

                <div class="deadline" v-if="isExam">
                    考试时长：{{ duration }} 分钟
                </div>

                <div class="deadline">总分：{{ totalScore }} 分</div>

                <!-- 状态判断 -->
                <div class="status">
                    <template v-if="submitted">
                        <template v-if="published" style="align-items:center">
                            <!-- 已提交 & 已公布成绩 -->
                            <!-- <el-button type="primary" @click="goToAnswerDetail">
                                查看我的答题情况
                            </el-button> -->
                            <p>得分：{{ totalScore }}</p>
                        </template>
                        <template v-else>
                            <!-- 提交了但成绩未公布 -->
                            <el-alert title="提交成功，等待老师批阅" type="warning" :closable="false" show-icon />
                        </template>
                    </template>
                    <template v-else>
                        <!-- 未提交 -->
                        <el-alert :title="`未参加${isExam ? '考试' : '作业'}`" type="error" :closable="false" show-icon />
                    </template>
                </div>
            </el-card>
        </el-main>
    </div>
</template>
  
<script>
import dayjs from "dayjs";
import { checkIsSubmitted } from "@/api/student/exam-assignment/studentExam";

export default {
    data() {
        return {
            title: this.$route.query.title || "未命名",
            isExam: this.$route.query.isExam === "true",
            startTime: Number(this.$route.query.startTime) || 0,
            endTime: Number(this.$route.query.endTime) || 0,
            duration: Number(this.$route.query.duration) || 0, // 单位：分钟
            totalScore: this.$route.query.totalScore || "--",
            published: this.$route.query.isPublishScore === "true",
            id: this.$route.query.id || "",

            submitted: false, // 是否提交
            loading: true
        };
    },
    async mounted() {
        try {
            const res = await checkIsSubmitted({
                id: this.id,
                isExam: this.isExam
            });
            this.submitted = res?.result === true;
        } catch (e) {
            console.error("检查提交状态失败", e);
        } finally {
            this.loading = false;
        }
    },
    methods: {
        goBack() {
            this.$router.go(-1);
        },
        goToAnswerDetail() {
            this.$router.push({
                path: "/student/answer-detail",
                query: {
                    id: this.id,
                    title: this.title,
                    isExam: this.isExam
                }
            });
        },
        formatTime(timestamp) {
            if (!timestamp) return "--";
            return dayjs(timestamp).format("YYYY.MM.DD HH:mm");
        }
    }
};
</script>
  
<style scoped>
.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.header {
    background-color: #2c3e50;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    font-size: 16px;
}

.back-button {
    color: #fff;
    font-weight: 500;
    padding: 6px 10px;
    display: flex;
    align-items: center;
}

.header-title {
    flex: 1;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    color: white;
}

.main {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    overflow-y: auto;
}

.card {
    width: 100%;
    max-width: 800px;
    margin-bottom: 20px;
    padding: 20px;
}

.title {
    font-size: 22px;
    font-weight: bold;
    margin-bottom: 10px;
}

.deadline {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
}

.status {
    margin-top: 20px;
}
</style>
  