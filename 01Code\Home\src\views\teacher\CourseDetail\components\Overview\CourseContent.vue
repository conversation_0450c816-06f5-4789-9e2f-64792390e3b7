<!--src\views\teacher\CourseDetail\components\Overview\CourseContent.vue-->
<template>
  <div class="course-content">
    <div class="content-body">
      <!-- 动态组件占位符 -->
      <component :is="currentComponent" :course="course" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

// 接收父组件传递的当前组件和课程数据
const props = defineProps({
  currentComponent: {
    type: Object,
    required: true
  },
  course: {
    type: Object,
    default: () => ({})
  }
});

</script>

<style scoped>
.course-content {
  flex: 1;
  padding: 0 3vw;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
</style>