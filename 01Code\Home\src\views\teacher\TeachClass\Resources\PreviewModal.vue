<!-- <template>
  <div class="preview-modal">
    <div class="modal-mask" @click.self="$emit('close')">
      <div class="modal-container">
        <div class="modal-header">
          <h3 class="modal-title">文件预览</h3>
          <button class="close-btn" @click="$emit('close')">×</button>
        </div>

        <div class="modal-body">
          <div class="left-section">
            <div class="file-header">
              <div class="filename">{{ file.name }}</div>
              <button class="download-btn" @click="downloadFile">下载</button>
            </div>
            <div class="preview-area">
              <iframe 
                v-if="file.previewUrl" 
                :src="file.previewUrl" 
                frameborder="0" 
                class="preview-frame"
                allow="fullscreen"
              ></iframe>
              <div v-else class="no-preview">暂无预览内容</div>
            </div>
          </div>

          <div class="right-section">
            <div class="info-section">
              <h4>历史数据统计</h4>
              <p>下载 <strong>{{ file.stats.downloads }}</strong> 次</p>
              <p>查看 <strong>{{ file.stats.views }}</strong> 次</p>
            </div>

            <div class="info-section">
              <h4>历史引用次数</h4>
              <p>自己引用 <strong>{{ file.stats.selfRefs }}</strong> 次</p>
            </div>

            <div class="info-section">
              <h4>所有动态</h4>
              <ul class="activities">
                <li v-for="(a, i) in file.activities" :key="i">
                  <span class="time">• {{ a.time }}</span>
                  <span class="desc">{{ a.desc }}</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  file: {
    type: Object,
    required: true,
  },
})

const downloadFile = () => {
  alert(`下载：${props.file.name}`)
}
</script>

<style lang="scss" scoped>
.preview-modal {
  .modal-mask {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .modal-container {
    width: 90%;
    max-width: 1200px;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: #f8f8f8;
    border-bottom: 1px solid #eee;

    .modal-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .close-btn {
      background: transparent;
      border: none;
      font-size: 28px;
      line-height: 1;
      cursor: pointer;
      color: #999;
      padding: 0;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: color 0.2s;

      &:hover {
        color: #333;
      }
    }
  }

  .modal-body {
    display: flex;
    height: 80vh;
    max-height: 700px;
    overflow: hidden;
  }

  .left-section {
    flex: 3;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #eee;

    .file-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid #eee;

      .filename {
        font-weight: 600;
        font-size: 16px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 70%;
      }

      .download-btn {
        background: #409eff;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background 0.2s;

        &:hover {
          background: #66b1ff;
        }
      }
    }

    .preview-area {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #f5f5f5;
      overflow: hidden;

      .preview-frame {
        width: 100%;
        height: 100%;
        border: none;
        background: white;
      }

      .no-preview {
        color: #999;
        font-size: 14px;
      }
    }
  }

  .right-section {
    flex: 1;
    min-width: 280px;
    padding: 20px;
    overflow-y: auto;
    background: #fdfdfd;

    .info-section {
      margin-bottom: 24px;

      h4 {
        font-size: 15px;
        font-weight: 600;
        margin-bottom: 12px;
        color: #333;
        padding-bottom: 6px;
        border-bottom: 1px solid #eee;
      }

      p {
        margin: 8px 0;
        font-size: 14px;
        color: #666;
        
        strong {
          color: #333;
          font-weight: 500;
        }
      }

      .activities {
        list-style: none;
        padding: 0;
        margin: 0;

        li {
          font-size: 13px;
          margin-bottom: 10px;
          line-height: 1.4;
          display: flex;

          .time {
            color: #409eff;
            margin-right: 8px;
            flex-shrink: 0;
          }

          .desc {
            color: #555;
            word-break: break-word;
          }
        }
      }
    }
  }
}
</style> -->


<template>
  <div class="preview-modal">
    <div class="modal-mask" @click.self="$emit('close')">
      <div class="modal-container">
        <!-- 顶部标题栏 -->
        <div class="modal-header">
          <h3 class="modal-title">文件预览</h3>
          <button class="close-btn" @click="$emit('close')">×</button>
        </div>

        <!-- 主要内容区 -->
        <div class="modal-body">
          <!-- 左侧预览区 -->
          <div class="left-section">
            <!-- 文件头部信息 -->
            <div class="file-header">
              <div class="filename">{{ file.name }}</div>
              <button class="download-btn" @click="downloadFile">下载</button>
            </div>

            <!-- 预览内容区域 -->
            <div class="preview-area">
              <!-- 图片预览 -->
              <img v-if="isImage" :src="file.url" class="preview-content" alt="图片预览" />

              <!-- PDF预览 -->
              <iframe v-else-if="isPdf"
                :src="`https://docs.google.com/gview?url=${encodeURIComponent(file.url)}&embedded=true`"
                class="preview-frame" frameborder="0"></iframe>

              <!-- 视频预览 -->
              <video v-else-if="isVideo" controls class="preview-content">
                <source :src="file.url" :type="videoType">
                您的浏览器不支持视频播放
              </video>

              <!-- 音频预览 -->
              <audio v-else-if="isAudio" controls class="preview-content">
                <source :src="file.url" :type="audioType">
                您的浏览器不支持音频播放
              </audio>

              <!-- 文本文件预览 -->
              <iframe v-else-if="isText" :src="file.url" class="preview-frame" frameborder="0"></iframe>

              <!-- Office文档预览 -->
              <iframe v-else-if="isOffice"
                :src="`https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(file.url)}`"
                class="preview-frame" frameborder="0"></iframe>

              <!-- 不支持预览的文件类型 -->
              <div v-else class="no-preview">
                <div class="file-icon">
                  <span class="icon-text">{{ fileExtension }}</span>
                </div>
                <p>不支持在线预览此文件类型</p>
                <button @click="downloadFile" class="download-btn-large">
                  <i class="icon-download"></i> 下载文件
                </button>
              </div>
            </div>
          </div>

          <!-- 右侧信息区 -->
          <div class="right-section">
            <div class="info-section">
              <h4>文件信息</h4>
              <div class="file-info-item">
                <span class="label">文件类型：</span>
                <span class="value">{{ fileExtension.toUpperCase() }}文件</span>
              </div>
              <div class="file-info-item" v-if="file.size">
                <span class="label">文件大小：</span>
                <span class="value">{{ formatFileSize(file.size) }}</span>
              </div>
              <div class="file-info-item">
                <span class="label">上传时间：</span>
                <span class="value">{{ file.modifiedTime || '未知' }}</span>
              </div>
            </div>

            <div class="info-section">
              <h4>历史数据统计</h4>
              <div class="file-info-item">
                <span class="label">下载次数：</span>
                <span class="value">{{ file.stats?.downloads || 0 }}</span>
              </div>
              <div class="file-info-item">
                <span class="label">查看次数：</span>
                <span class="value">{{ file.stats?.views || 0 }}</span>
              </div>
            </div>

            <div class="info-section">
              <h4>所有动态</h4>
              <ul class="activities">
                <li v-for="(activity, index) in fileActivities" :key="index">
                  <span class="time">• {{ activity.time }}</span>
                  <span class="desc">{{ activity.desc }}</span>
                </li>
                <li v-if="fileActivities.length === 0">
                  <span class="no-activity">暂无动态记录</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  file: {
    type: Object,
    required: true,
    default: () => ({
      url: '',
      name: '',
      fileExtension: '',
      size: 0,
      modifiedTime: '',
      stats: {
        downloads: 0,
        views: 0,
        selfRefs: 0
      },
      activities: []
    })
  }
});




const emit = defineEmits(['close']);

// 计算属性
const fileExtension = computed(() => {
  return props.file.fileExtension?.toLowerCase() ||
    props.file.name?.split('.').pop()?.toLowerCase() ||
    'file';
});

const fileActivities = computed(() => {
  return props.file.activities?.length > 0
    ? props.file.activities
    : [{ time: new Date().toLocaleString(), desc: '上传了文件' }];
});

// 文件类型判断
const isImage = computed(() => {
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
  return imageExtensions.includes(fileExtension.value);
});

const isPdf = computed(() => fileExtension.value === 'pdf');

const isVideo = computed(() => {
  const videoExtensions = ['mp4', 'webm', 'ogg', 'mov', 'avi', 'm3u8'];
  return videoExtensions.includes(fileExtension.value);
});

const isAudio = computed(() => {
  const audioExtensions = ['mp3', 'wav', 'ogg', 'm4a', 'aac'];
  return audioExtensions.includes(fileExtension.value);
});

const isText = computed(() => {
  const textExtensions = ['txt', 'csv', 'json', 'xml', 'md', 'js', 'html', 'css'];
  return textExtensions.includes(fileExtension.value);
});

const isOffice = computed(() => {
  const officeExtensions = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];
  return officeExtensions.includes(fileExtension.value);
});

// 获取视频/音频类型
const videoType = computed(() => {
  const ext = fileExtension.value;
  if (ext === 'mp4') return 'video/mp4';
  if (ext === 'webm') return 'video/webm';
  if (ext === 'ogg') return 'video/ogg';
  return 'video/*';
});

const audioType = computed(() => {
  const ext = fileExtension.value;
  if (ext === 'mp3') return 'audio/mpeg';
  if (ext === 'wav') return 'audio/wav';
  if (ext === 'ogg') return 'audio/ogg';
  return 'audio/*';
});

// 方法

const downloadFile = () => {
  const link = document.createElement('a');
  link.href = props.file.url;
  link.download = props.file.name || 'file'; // Ensure the file has a proper name
  link.target = '_blank';  // Optionally open in a new tab
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  console.log(props.file.url);

};

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
</script>

<style lang="scss" scoped>
.preview-modal {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;

  .modal-mask {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .modal-container {
    width: 90%;
    max-width: 1000px;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    max-height: 80vh;
    display: flex;
    flex-direction: column;
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: #f8f8f8;
    border-bottom: 1px solid #eee;
    flex-shrink: 0;

    .modal-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .close-btn {
      background: transparent;
      border: none;
      font-size: 24px;
      line-height: 1;
      cursor: pointer;
      color: #999;
      padding: 0;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s;
      border-radius: 4px;

      &:hover {
        color: #333;
        background: rgba(0, 0, 0, 0.05);
      }
    }
  }

  .modal-body {
    display: flex;
    height: calc(90vh - 65px);
    overflow: hidden;
  }

  .left-section {
    flex: 3;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #eee;
    min-width: 0;

    .file-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid #eee;
      flex-shrink: 0;

      .filename {
        font-weight: 600;
        font-size: 16px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 70%;
      }

      .download-btn {
        background: #409eff;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background 0.2s;
        display: flex;
        align-items: center;
        gap: 6px;

        &:hover {
          background: #66b1ff;
        }
      }
    }

    .preview-area {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #f5f5f5;
      overflow: hidden;
      padding: 20px;
      position: relative;

      .preview-content {
        max-width: 100%;
        max-height: 100%;
        display: block;
        margin: 0 auto;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .preview-frame {
        width: 100%;
        height: 100%;
        border: none;
        background: white;
      }

      .no-preview {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #666;
        text-align: center;
        padding: 20px;

        .file-icon {
          width: 80px;
          height: 80px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f0f2f5;
          color: #409eff;
          border-radius: 50%;
          margin-bottom: 16px;
          font-size: 24px;
          font-weight: bold;

          .icon-text {
            transform: scale(1.2);
          }
        }

        p {
          margin: 8px 0 16px;
          font-size: 15px;
          color: #666;
        }

        .download-btn-large {
          margin-top: 16px;
          padding: 10px 20px;
          background: #409eff;
          color: white;
          border: none;
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
          display: flex;
          align-items: center;
          gap: 8px;
          transition: all 0.2s;

          &:hover {
            background: #66b1ff;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
          }

          .icon-download {
            font-size: 16px;
          }
        }
      }
    }
  }

  .right-section {
    flex: 1;
    min-width: 280px;
    max-width: 350px;
    padding: 20px;
    overflow-y: auto;
    background: #fdfdfd;

    .info-section {
      margin-bottom: 24px;

      h4 {
        font-size: 15px;
        font-weight: 600;
        margin-bottom: 16px;
        color: #333;
        padding-bottom: 8px;
        border-bottom: 1px solid #eee;
      }

      .file-info-item {
        display: flex;
        margin-bottom: 12px;
        font-size: 14px;

        .label {
          color: #666;
          min-width: 70px;
        }

        .value {
          color: #333;
          font-weight: 500;
          word-break: break-word;
        }
      }

      .activities {
        list-style: none;
        padding: 0;
        margin: 0;
        font-size: 13px;

        li {
          margin-bottom: 12px;
          line-height: 1.4;
          display: flex;
          flex-wrap: wrap;

          .time {
            color: #409eff;
            margin-right: 8px;
            flex-shrink: 0;
          }

          .desc {
            color: #555;
            word-break: break-word;
            flex: 1;
          }

          .no-activity {
            color: #999;
            font-style: italic;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .preview-modal {
    .modal-container {
      width: 95%;
      max-height: 95vh;
    }

    .modal-body {
      flex-direction: column;
      height: auto;
      max-height: calc(95vh - 65px);
    }

    .left-section {
      border-right: none;
      border-bottom: 1px solid #eee;
    }

    .right-section {
      max-width: 100%;
    }
  }
}
</style>