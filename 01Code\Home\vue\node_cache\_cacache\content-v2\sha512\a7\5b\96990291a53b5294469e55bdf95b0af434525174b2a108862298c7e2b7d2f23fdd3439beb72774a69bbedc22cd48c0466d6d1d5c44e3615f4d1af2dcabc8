{"_attachments": {}, "_id": "ya<PERSON>", "_rev": "272010-61f1b5661d755a05a20adcb0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "description": "Yet Another EventTarget Implementation", "dist-tags": {"latest": "1.0.3"}, "license": "MIT", "maintainers": [{"name": "ibc", "email": "<EMAIL>"}], "name": "ya<PERSON>", "readme": "# yaeti\n\nYet Another [EventTarget](https://developer.mozilla.org/es/docs/Web/API/EventTarget) Implementation.\n\nThe library exposes both the [EventTarget](https://developer.mozilla.org/es/docs/Web/API/EventTarget) interface and the [Event](https://developer.mozilla.org/en-US/docs/Web/API/Event) interface.\n\n\n## Installation\n\n```bash\n$ npm install yaeti --save\n```\n\n\n## Usage\n\n```javascript\nvar yaeti = require('yaeti');\n\n// Custom class we want to make an EventTarget.\nfunction Foo() {\n    // Call EventTarget constructor\n    yaeti.EventTarget.call(this);\n}\n// Inherit EventTarget prototype\nFoo.prototype = Object.create(yaeti.EventTarget.prototype);\nFoo.prototype.constructor = Foo;\n\n// Create an instance.\nvar foo = new Foo();\n\nfunction listener1() {\n    console.log('listener1');\n}\n\nfunction listener2() {\n    console.log('listener2');\n}\n \nfoo.addEventListener('bar', listener1);\nfoo.addEventListener('bar', listener2);\nfoo.removeEventListener('bar', listener1);\n\nvar event = new yaeti.Event('bar');\n\nfoo.dispatchEvent(event);\n\n// Output:\n// => \"listener2\"\n```\n\n\n## API\n\n\n### `yaeti.EventTarget` interface\n\nImplementation of the [EventTarget](https://developer.mozilla.org/es/docs/Web/API/EventTarget) interface.\n\n#### ES5\n```javascript\nfunction Foo() {\n    yaeti.EventTarget.call(this);\n}\nFoo.prototype = Object.create(yaeti.EventTarget.prototype);\nFoo.prototype.constructor = Foo;\n```\n\n#### ES6\n```javascript\nclass Foo extends EventTarget () {\n    constructor () {\n        super();\n    }\n}\n```\n\nThe interface implements the `addEventListener`, `removeEventListener` and `dispatchEvent` methods as defined by the W3C.\n\n\n##### `listeners` read-only property\n\nReturns an object whose keys are configured event types (String) and whose values are an array of listeners (functions) for those event types.\n\n\n### `yaeti.Event` interface\n\nImplementation of the [Event](https://developer.mozilla.org/en-US/docs/Web/API/Event) interface.\n\n*NOTE:* Just useful in Node (the browser already exposes the native `Event` interface).\n\n```javascript\nvar event = new yaeti.Event('bar');\n```\n\n\n## Author\n\n[Iñaki Baz Castillo](https://inakibaz.me)\n\n\n## License\n\n[MIT](./LICENSE)\n", "time": {"created": "2022-01-26T20:56:06.037Z", "modified": "2025-03-11T02:40:53.999Z", "1.0.2": "2018-02-23T22:22:41.992Z", "1.0.1": "2017-04-25T10:07:33.856Z", "1.0.0": "2017-04-20T17:29:38.182Z", "0.0.6": "2016-03-17T22:40:17.919Z", "0.0.5": "2016-01-28T09:51:21.949Z", "0.0.4": "2015-07-22T11:44:10.870Z", "0.0.3": "2015-07-22T10:36:43.041Z", "0.0.2": "2015-07-22T10:30:33.869Z", "0.0.1": "2015-07-20T21:52:50.671Z", "1.0.3": "2022-01-10T11:41:17.047Z"}, "versions": {"1.0.2": {"name": "ya<PERSON>", "version": "1.0.2", "description": "Yet Another EventTarget Implementation", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "main": "index.js", "browser": {"./lib/Event.js": "./lib/Event.browser.js"}, "repository": {"type": "git", "url": "git+https://github.com/ibc/yaeti.git"}, "devDependencies": {"gulp": "git+https://github.com/gulpjs/gulp.git#4.0", "gulp-jscs": "^4.0.0", "gulp-jscs-stylish": "^1.4.0", "gulp-jshint": "^2.0.4", "jshint": "^2.9.4", "jshint-stylish": "^2.2.1"}, "engines": {"node": ">=4.0.0"}, "gitHead": "2563b323b138721f1d49aaa00a74304eabcbfa2a", "bugs": {"url": "https://github.com/ibc/yaeti/issues"}, "homepage": "https://github.com/ibc/yaeti#readme", "_id": "yaeti@1.0.2", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "ibc", "email": "<EMAIL>"}, "dist": {"shasum": "eb4a978c77189e9649bcbf78dbc1dd18c72050b3", "size": 3471, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaeti/-/yaeti-1.0.2.tgz", "integrity": "sha512-sc1JByruVRqL6GYdIKbcvYw8PRmYeuwtSd376fM13DNE+JjBh37qIlKjCtqg9mKV2N2+xCfyil3Hd6BXN9W1uQ=="}, "maintainers": [{"name": "ibc", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaeti_1.0.2_1519424561943_0.15618930837825507"}, "_hasShrinkwrap": false, "publish_time": 1519424561992, "_cnpm_publish_time": 1519424561992, "_cnpmcore_publish_time": "2021-12-16T21:10:38.774Z", "deprecated": "Package no longer supported. Contact Support at https://www.npmjs.com/support for more info."}, "1.0.1": {"name": "ya<PERSON>", "version": "1.0.1", "description": "Yet Another EventTarget Implementation", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "main": "index.js", "browser": {"./lib/Event.js": "./lib/Event.browser.js"}, "repository": {"type": "git", "url": "git+https://github.com/ibc/yaeti.git"}, "devDependencies": {"gulp": "git+https://github.com/gulpjs/gulp.git#4.0", "gulp-jscs": "^4.0.0", "gulp-jscs-stylish": "^1.4.0", "gulp-jshint": "^2.0.4", "jshint": "^2.9.4", "jshint-stylish": "^2.2.1"}, "engines": {"node": ">=4.0.0"}, "gitHead": "cc0c1b0deaf3ebcb1ae3ed8e5abe4d4afecc8f86", "bugs": {"url": "https://github.com/ibc/yaeti/issues"}, "homepage": "https://github.com/ibc/yaeti#readme", "_id": "yaeti@1.0.1", "scripts": {}, "_shasum": "217d04bbcdcbbd86cc478e4655aa4c28c493debf", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.6.0", "_npmUser": {"name": "ibc", "email": "<EMAIL>"}, "dist": {"shasum": "217d04bbcdcbbd86cc478e4655aa4c28c493debf", "size": 3474, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaeti/-/yaeti-1.0.1.tgz", "integrity": "sha512-kapgTD+vUxjmw55Q/wVgVD7erlCW5IE07Ct0k8op8Y9jKHWOZ3Bmsgz7CCASwbJZNU4ud+4ZOfQfRxKOgliYAg=="}, "maintainers": [{"name": "ibc", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/yaeti-1.0.1.tgz_1493114851623_0.6291333651170135"}, "directories": {}, "publish_time": 1493114853856, "_hasShrinkwrap": false, "_cnpm_publish_time": 1493114853856, "_cnpmcore_publish_time": "2021-12-16T21:10:39.047Z", "deprecated": "Package no longer supported. Contact Support at https://www.npmjs.com/support for more info."}, "1.0.0": {"name": "ya<PERSON>", "version": "1.0.0", "description": "Yet Another EventTarget Implementation", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "main": "index.js", "browser": {"./lib/Event.js": "./lib/Event.browser.js"}, "repository": {"type": "git", "url": "git+https://github.com/ibc/yaeti.git"}, "devDependencies": {"gulp": "git+https://github.com/gulpjs/gulp.git#4.0", "gulp-jscs": "^4.0.0", "gulp-jscs-stylish": "^1.4.0", "gulp-jshint": "^2.0.4", "jshint": "^2.9.4", "jshint-stylish": "^2.2.1"}, "engines": {"node": ">=4.0.0"}, "gitHead": "f586a3e02834f42489a68444c7b4cfd8084bd688", "bugs": {"url": "https://github.com/ibc/yaeti/issues"}, "homepage": "https://github.com/ibc/yaeti#readme", "_id": "yaeti@1.0.0", "scripts": {}, "_shasum": "18b2580317fecb9d08eb91ddc818061424466c32", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.6.0", "_npmUser": {"name": "ibc", "email": "<EMAIL>"}, "dist": {"shasum": "18b2580317fecb9d08eb91ddc818061424466c32", "size": 3504, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaeti/-/yaeti-1.0.0.tgz", "integrity": "sha512-2iIelD70Fv+sw3Q1UdlddI/LYFf5Qqpha/FUPkN+NlmR2ZlF+k4pgdJ6od9BxwgTkJsOjWOSJYBIfZo0/Gae5A=="}, "maintainers": [{"name": "ibc", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/yaeti-1.0.0.tgz_1492709377646_0.6074129950720817"}, "directories": {}, "publish_time": 1492709378182, "_hasShrinkwrap": false, "_cnpm_publish_time": 1492709378182, "_cnpmcore_publish_time": "2021-12-16T21:10:39.280Z", "deprecated": "Package no longer supported. Contact Support at https://www.npmjs.com/support for more info."}, "0.0.6": {"name": "ya<PERSON>", "version": "0.0.6", "description": "Yet Another EventTarget Implementation", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "main": "index.js", "browser": {"./lib/Event.js": "./lib/Event.browser.js"}, "repository": {"type": "git", "url": "https://github.com/ibc/yaeti.git"}, "devDependencies": {"gulp": "git+https://github.com/gulpjs/gulp.git#4.0", "gulp-jscs": "^1.6.0", "gulp-jscs-stylish": "^1.1.0", "gulp-jshint": "^1.11.2", "jshint-stylish": "~1.0.2"}, "engines": {"node": ">=0.10.32"}, "gitHead": "5b838a23410b9ed0eb1b74bc3a266c1af204b8f2", "bugs": {"url": "https://github.com/ibc/yaeti/issues"}, "homepage": "https://github.com/ibc/yaeti", "_id": "yaeti@0.0.6", "scripts": {}, "_shasum": "f26f484d72684cf42bedfb76970aa1608fbf9577", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "ibc", "email": "<EMAIL>"}, "dist": {"shasum": "f26f484d72684cf42bedfb76970aa1608fbf9577", "size": 3418, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaeti/-/yaeti-0.0.6.tgz", "integrity": "sha512-MvQa//+KcZCUkBTIC9blM+CU9J2GzuTytsOUwf2lidtvkx/6gnEp1QvJv34t9vdjhFmha/mUiNDbN0D0mJWdug=="}, "maintainers": [{"name": "ibc", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/yaeti-0.0.6.tgz_1458254413403_0.9036164651624858"}, "directories": {}, "publish_time": 1458254417919, "_hasShrinkwrap": false, "_cnpm_publish_time": 1458254417919, "_cnpmcore_publish_time": "2021-12-16T21:10:39.469Z", "deprecated": "Package no longer supported. Contact Support at https://www.npmjs.com/support for more info."}, "0.0.5": {"name": "ya<PERSON>", "version": "0.0.5", "description": "Yet Another EventTarget Implementation", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "main": "index.js", "browser": {"./lib/Event.js": "./lib/Event.browser.js"}, "repository": {"type": "git", "url": "https://github.com/ibc/yaeti.git"}, "devDependencies": {"gulp": "git+https://github.com/gulpjs/gulp.git#4.0", "gulp-jscs": "^1.6.0", "gulp-jscs-stylish": "^1.1.0", "gulp-jshint": "^1.11.2", "jshint-stylish": "~1.0.2"}, "engines": {"node": ">=0.10.32"}, "gitHead": "7b77f5ba5f30e846cb0d71dc10b3bb789d95dcaa", "bugs": {"url": "https://github.com/ibc/yaeti/issues"}, "homepage": "https://github.com/ibc/yaeti", "_id": "yaeti@0.0.5", "scripts": {}, "_shasum": "1d88a1d17bb4e5c5e4d3fad5d3fd5eba864358c7", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "ibc", "email": "<EMAIL>"}, "dist": {"shasum": "1d88a1d17bb4e5c5e4d3fad5d3fd5eba864358c7", "size": 3414, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaeti/-/yaeti-0.0.5.tgz", "integrity": "sha512-kMgeAHsBOxWk+PtDfoE5N7DuKahjWFWmXT4lb7dKyDeux1EUKOlKuKWQLMqzM7ABdyYSTfm0rdypGP5iDonYQw=="}, "maintainers": [{"name": "ibc", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1453974681949, "_hasShrinkwrap": false, "_cnpm_publish_time": 1453974681949, "_cnpmcore_publish_time": "2021-12-16T21:10:39.685Z", "deprecated": "Package no longer supported. Contact Support at https://www.npmjs.com/support for more info."}, "0.0.4": {"name": "ya<PERSON>", "version": "0.0.4", "description": "Yet Another EventTarget Implementation", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "main": "index.js", "browser": {"./lib/Event.js": "./lib/Event.browser.js"}, "repository": {"type": "git", "url": "https://github.com/ibc/yaeti.git"}, "devDependencies": {"gulp": "git+https://github.com/gulpjs/gulp.git#4.0", "gulp-jscs": "^1.6.0", "gulp-jscs-stylish": "^1.1.0", "gulp-jshint": "^1.11.2", "jshint-stylish": "~1.0.2"}, "engines": {"node": ">=0.10.32"}, "gitHead": "c28b35176dbbe10e9452fccf12e4cca850a611ae", "bugs": {"url": "https://github.com/ibc/yaeti/issues"}, "homepage": "https://github.com/ibc/yaeti", "_id": "yaeti@0.0.4", "scripts": {}, "_shasum": "89fe739c45ac4491028973193262a837693a66b6", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "ibc", "email": "<EMAIL>"}, "dist": {"shasum": "89fe739c45ac4491028973193262a837693a66b6", "size": 3348, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaeti/-/yaeti-0.0.4.tgz", "integrity": "sha512-gjoIyQHRknnlDIkNShiqiehrjZrIuWrf+PIcqE5objLW1AXC8nFLSUu3H6D1sTjEIKibxKitmpsUjzICGuRrmw=="}, "maintainers": [{"name": "ibc", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1437565450870, "_hasShrinkwrap": false, "_cnpm_publish_time": 1437565450870, "_cnpmcore_publish_time": "2021-12-16T21:10:39.917Z", "deprecated": "Package no longer supported. Contact Support at https://www.npmjs.com/support for more info."}, "0.0.3": {"name": "ya<PERSON>", "version": "0.0.3", "description": "Yet Another EventTarget Implementation", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "main": "index.js", "browser": {"./lib/Event.js": "./lib/Event.browser.js"}, "repository": {"type": "git", "url": "https://github.com/ibc/yaeti.git"}, "devDependencies": {"gulp": "git+https://github.com/gulpjs/gulp.git#4.0", "gulp-jscs": "^1.6.0", "gulp-jscs-stylish": "^1.1.0", "gulp-jshint": "^1.11.2", "jshint-stylish": "~1.0.2"}, "engines": {"node": ">=0.10.32"}, "gitHead": "7e3a6010f9032bbb7e11bdc3897ac7d04301cb15", "bugs": {"url": "https://github.com/ibc/yaeti/issues"}, "homepage": "https://github.com/ibc/yaeti", "_id": "yaeti@0.0.3", "scripts": {}, "_shasum": "fbc762885c40f1c7fd9b0f06d07992289655778f", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "ibc", "email": "<EMAIL>"}, "dist": {"shasum": "fbc762885c40f1c7fd9b0f06d07992289655778f", "size": 3348, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaeti/-/yaeti-0.0.3.tgz", "integrity": "sha512-eqljxoZ7UI7KGdAJC9JkfC87RoRT87Vot3VVz1a+iA4BYY33yklVjF2aKLx7+cp/7DF9+id7hlNIiV6PVYU4+g=="}, "maintainers": [{"name": "ibc", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1437561403041, "_hasShrinkwrap": false, "_cnpm_publish_time": 1437561403041, "_cnpmcore_publish_time": "2021-12-16T21:10:40.130Z", "deprecated": "Package no longer supported. Contact Support at https://www.npmjs.com/support for more info."}, "0.0.2": {"name": "ya<PERSON>", "version": "0.0.2", "description": "Yet Another EventTarget Implementation", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "main": "index.js", "browser": {"./lib/Event.js": "./lib/Event.browser.js"}, "devDependencies": {"gulp": "git+https://github.com/gulpjs/gulp.git#4.0", "gulp-jscs": "^1.6.0", "gulp-jscs-stylish": "^1.1.0", "gulp-jshint": "^1.11.2", "jshint-stylish": "~1.0.2"}, "engines": {"node": ">=0.10.32"}, "gitHead": "06dd809c4f24f701e3b9523c5d38b662a9cc1006", "_id": "yaeti@0.0.2", "scripts": {}, "_shasum": "22eceb3c68c3681e09579f80e8b2f4c016f77fcd", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "ibc", "email": "<EMAIL>"}, "dist": {"shasum": "22eceb3c68c3681e09579f80e8b2f4c016f77fcd", "size": 3319, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaeti/-/yaeti-0.0.2.tgz", "integrity": "sha512-siij5d60MPXZVPYZ6kQfTmj9FAbxb0y6bl3l1NUwxnttCn1AH3MWpaBobVamXJJojebA4lWKd8NWZcZ9Y87ziA=="}, "maintainers": [{"name": "ibc", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1437561033869, "_hasShrinkwrap": false, "_cnpm_publish_time": 1437561033869, "_cnpmcore_publish_time": "2021-12-16T21:10:40.343Z", "deprecated": "Package no longer supported. Contact Support at https://www.npmjs.com/support for more info."}, "0.0.1": {"name": "ya<PERSON>", "version": "0.0.1", "description": "Yet Another EventTarget Implementation", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "main": "index.js", "browser": {"./lib/Event.js": "./lib/Event.browser.js"}, "devDependencies": {"gulp": "git+https://github.com/gulpjs/gulp.git#4.0", "gulp-jscs": "^1.6.0", "gulp-jscs-stylish": "^1.1.0", "gulp-jshint": "^1.11.2", "jshint-stylish": "~1.0.2"}, "engines": {"node": ">=0.10.32"}, "gitHead": "fd8f2f7da46f2d8dbf49186594da1723210b1250", "_id": "yaeti@0.0.1", "scripts": {}, "_shasum": "02d63b22eed5b669fa88a4c6843062295732428f", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "ibc", "email": "<EMAIL>"}, "dist": {"shasum": "02d63b22eed5b669fa88a4c6843062295732428f", "size": 3164, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaeti/-/yaeti-0.0.1.tgz", "integrity": "sha512-WFKV9jHS2eNIjAkjDqruTdWh13LTVJH/3ul/PA/HoMeybdzYVfjksk2aHoL/jVKgSwXPNDB6+5jme0JlI+fQWw=="}, "maintainers": [{"name": "ibc", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1437429170671, "_hasShrinkwrap": false, "_cnpm_publish_time": 1437429170671, "_cnpmcore_publish_time": "2021-12-16T21:10:40.566Z", "deprecated": "Package no longer supported. Contact Support at https://www.npmjs.com/support for more info."}, "1.0.3": {"name": "ya<PERSON>", "version": "1.0.3", "description": "Yet Another EventTarget Implementation", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "main": "index.js", "browser": {"./lib/Event.js": "./lib/Event.browser.js"}, "repository": {"type": "git", "url": "git+https://github.com/ibc/yaeti.git"}, "devDependencies": {"gulp": "^4.0.0", "gulp-jscs": "^4.0.0", "gulp-jscs-stylish": "^1.4.0", "gulp-jshint": "^2.0.4", "jshint": "^2.9.4", "jshint-stylish": "^2.2.1"}, "engines": {"node": ">=4.0.0"}, "gitHead": "bc5e14e2b0feb50a1fc371dda00dfa511ae7d6a9", "bugs": {"url": "https://github.com/ibc/yaeti/issues"}, "homepage": "https://github.com/ibc/yaeti#readme", "_id": "yaeti@1.0.3", "_nodeVersion": "16.0.0", "_npmVersion": "7.20.5", "dist": {"integrity": "sha512-XGlihp7jseP/hc/NqYNrNQAs/vgSoMbp40Kd9Zceg6wXws0fncwKWgFyulzUDGNVqlEBAWvvHtcgF9v2N/8LMA==", "shasum": "992d1a9b9632ac2e7af41ae24e6ee9e05da3ae89", "tarball": "https://registry.npmmirror.com/yaeti/-/yaeti-1.0.3.tgz", "fileCount": 10, "unpackedSize": 8147, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3BtdCRA9TVsSAnZWagAA77kP/jfSNuwe5JqDkefcDK6+\nYowTsXPO1pEel1lkJNgXupBuX9Wd+5XpJaL6QkdHus4JRGIFLYla4Y5Na3G2\nVzBgzBdfsDkTkM/d/FkH8fPQ0tZpfYW7hGUFZMd3frgeqAzGVNrYxc2TjA64\nKxgTI4y0dfMZtuEGWSsiPkJZ+7BybKdnisJHZygKSfRrR0isBuMDV7JBhoNj\nXsofghBAqbwClBzxISW8p38PvMlpyFGlhqmBDP7NzF3SJJigz7/NjXQePTc5\nE0ZjD9knImqIouieKUpNoKHnhyw2RVsvbrsWQghI5NcTt4RliWqLwQ1vKdlW\nfJq4K9gAMnhzjzN61aXjibqpI94rTpy4oFeS+RoHmLIO5nYFsToIx7mEvwb5\nBK4yWplMIm9cGp90jlkHHFflksmoJeo8BgXLWSuBQt5iH4uWWrfCqO4b+s9M\n3T2kPFX5HwQuQTMyGzkaQefOF3EC5fFvR1HG9KB9LctbGc2cBXx6r1Jgt2F6\ni2DmdOHF5KifhhVzLY32twvPBfvWVPO42CIpjdiwbXU8eXXS7m5LcCDBFy/M\nLGtGD446vpYR7C/veWRae2OGXdMAZ5Z5yQfZpA9Y+IwxEPaHS4SRYx6BZXb7\nhRKDQX9sJkNN6MBZBTq/IeUQVhWkkzjgTN7GVp5hQ8NVgkVTzeKeYC6IZg+L\nasGK\r\n=BMJO\r\n-----END PGP SIGNATURE-----\r\n", "size": 3366}, "_npmUser": {"name": "ibc", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ibc", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaeti_1.0.3_1641814876920_0.03185740213786947"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-10T12:10:30.389Z", "deprecated": "Package no longer supported. Contact Support at https://www.npmjs.com/support for more info."}}, "bugs": {"url": "https://github.com/ibc/yaeti/issues"}, "homepage": "https://github.com/ibc/yaeti#readme", "repository": {"type": "git", "url": "git+https://github.com/ibc/yaeti.git"}, "_source_registry_name": "default"}