<!--src\views\teacher\CourseDetail\components\StatCard.vue-->
<template>
  <div class="stat-card">
    <p class="stat-title">{{ title }}<span class="help-icon"><img src="@/assets/img/Teacher/icon-help.png" alt="help" /></span></p>
    <p class="stat-value">{{ value }}<span class="unit">{{ unit }}</span></p>
  </div>
</template>

<script setup>
import { defineProps } from 'vue';

// 定义组件接收的属性
defineProps({
  title: {
    type: String,
    required: true
  },
  value: {
    type: [String, Number],
    required: true
  },
  unit: {
    type: String,
    default: ''
  }
});
</script>

<style scoped lang="scss">
.stat-card {
  background-color: #d3c2f5; /* 浅紫色背景，可根据实际需求调整 */
  padding: 16px;
  border-radius: 8px;
  margin-right: 12px;
  min-width: 120px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-title {
  font-size: 1.3rem;
  color: #fff;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
}

.help-icon {
  font-size: 12px;
  margin-left: 4px;
  color: #eee;
  cursor: help;
}

.stat-value {
  font-size: 20px;
  color: #fff;
  margin: 0;
  display: flex;
  align-items: center;
}

.unit {
  font-size: 14px;
  margin-left: 4px;
  color: #eee;
}
</style>