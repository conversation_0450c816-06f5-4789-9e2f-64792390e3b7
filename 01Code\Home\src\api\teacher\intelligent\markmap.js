import OpenAI from 'openai';
import { getKey } from '../../key.js';

const openai = new OpenAI({
  baseURL: 'https://api.deepseek.com',
  apiKey: getKey('deepseek'),
  dangerouslyAllowBrowser: true
});

export const generateMarkdownCode = async (prompt) => {
  if (!prompt?.trim()) throw new Error('输入内容不能为空');

  try {
    const completion = await openai.chat.completions.create({
      model: 'deepseek-chat',
      messages: [
        {
          role: 'system',
          content: `生成Markdown格式的思维导图，使用无序列表(-)表示层级，确保有3级结构。示例：
# 中心主题
- 分支1
  - 子分支1
- 分支2`
        },
        {
          role: 'user',
          content: `转换为Markdown思维导图：${prompt}`
        }
      ],
      temperature: 0.5,
      max_tokens: 1000
    });

    let markdown = completion.choices[0]?.message?.content || '';
    markdown = markdown.replace(/```markdown?/g, '').replace(/```/g, '').trim();

    // 验证最少有3行有效内容
    const lines = markdown.split('\n').filter(l => l.trim().length > 0);
    if (lines.length < 3) {
      throw new Error('生成的Markdown结构过于简单');
    }

    return markdown;
  } catch (error) {
    console.error('API请求失败:', error);
    throw new Error(`生成Markdown失败: ${error.message}`);
  }
};
