// src/stores/teacher/graphManager/knowledgeGraphStore.js
import { defineStore } from 'pinia';
import {
    createGraph,
    getGraphListByCourse,
    deleteGraph,
    getGraphTree,
    createNode,
    deleteNode,
    updateNodeInfo,
    getAllTags,
    associateTagsToNode,
    getChildCount,
    getCourseResourceFolderTree,
    batchUpdateNodeResources,
    getNodeDetail,
    countNodeResources,
    getNodeResources,
    unlinkResourceFromNode
} from '@/api/teacher/graphManager/knowledgeGraph';

export const useKnowledgeGraphStore = defineStore('knowledgeGraph', {
    state: () => ({
        graphData: null,        // 当前创建的图谱数据（graphId）
        graphList: [],          // 图谱列表
        isLoading: false,       // 加载状态
        error: null,            // 错误信息
        courseId: null,         // 当前课程 ID，用于后续刷新
        rootNodeId: null,       // 存储根节点ID
        currentNodeTree: null,  // 存储当前图谱的树结构
        nodeCreationPending: false, // 节点创建加载状态
        nodeDetail: null,       // 存储节点详细信息
    }),

    actions: {
        // 设置当前图谱树
        setCurrentNodeTree(newTree) {
            this.currentNodeTree = newTree;
        },

        // 设置根节点 ID
        setRootNodeId(id) {
            this.rootNodeId = id || 'root';
        },

        // 创建图谱
        async createGraphAction(data) {
            this.isLoading = true;
            try {
                const response = await createGraph(data);
                if (response.code === 200) {
                    this.graphData = response.result; // 返回图谱ID
                    this.error = null;
                } else {
                    this.error = response.msg;
                }
            } catch (error) {
                this.error = '请求失败，请稍后再试';
            } finally {
                this.isLoading = false;
            }
        },

        // 获取该课程的所有图谱
        async fetchGraphListByCourse(courseId) {
            this.courseId = courseId; // ✅ 保存当前课程ID供后续操作使用
            this.isLoading = true;
            try {
                const res = await getGraphListByCourse(courseId);
                if (res.code === 200) {
                    this.graphList = res.result;
                    this.error = null;
                } else {
                    this.error = res.msg;
                }
            } catch (e) {
                this.error = '获取图谱列表失败';
            } finally {
                this.isLoading = false;
            }
        },

        // 根据 graphType 获取对应的路由名称
        getRouteNameByGraphType(graphType) {
            const routeMap = {
                0: 'CreateKnowledgeGraph', // 知识图谱
                1: 'CreateQuestionGraph',   // 问题图谱
                2: 'CreateAbilityGraph'    // 能力图谱
            };
            return routeMap[graphType] || 'CreateKnowledgeGraph'; // 默认跳转到知识图谱
        },

        // 删除图谱
        async deleteGraphById(graphId) {
            this.isLoading = true;
            try {
                const res = await deleteGraph(graphId);
                if (res.code === 200) {
                    this.error = null;
                    return true;
                } else {
                    this.error = res.msg;
                    return false;
                }
            } catch (e) {
                this.error = '删除图谱失败';
                return false;
            } finally {
                this.isLoading = false;
            }
        },

        // 获取图谱树结构
        async fetchGraphTree(graphId) {
            this.isLoading = true;
            try {
                const res = await getGraphTree(graphId);
                if (res.code === 200) {
                    // 获取返回的数据
                    console.log('获取到的树数据:', res.result);

                    // 假设返回的是一个数组，取第一个节点
                    const rootNode = res.result[0]; // 获取数组中的第一个元素

                    // 使用 nodeId 作为根节点 ID
                    const rootNodeId = rootNode.nodeId || 'root';  // 确保取 nodeId

                    // 更新 store 数据
                    this.setCurrentNodeTree(res.result);
                    this.setRootNodeId(rootNodeId);  // 通过 action 更新 rootNodeId

                    this.error = null;
                    return res.result;
                } else {
                    this.error = res.msg;
                    return null;
                }
            } catch (e) {
                this.error = '获取图谱结构失败';
                return null;
            } finally {
                this.isLoading = false;
            }
        },

        // 创建节点
        async createNodeAction(payload) {
            this.nodeCreationPending = true;
            try {
                const res = await createNode({
                    graphId: payload.graphId,
                    parentId: payload.parentId,
                    nodeName: payload.name,
                    nodeDesc: payload.description
                });

                if (res.code === 200) {
                    // 成功创建后返回新节点信息
                    return {
                        success: true,
                        node: {
                            id: res.result.nodeId,
                            label: payload.name,
                            description: payload.description,
                            children: []
                        }
                    };
                } else {
                    this.error = res.msg;
                    return { success: false, error: res.msg };
                }
            } catch (error) {
                this.error = '创建节点失败';
                return { success: false, error: '网络错误' };
            } finally {
                this.nodeCreationPending = false;
            }
        },

        // 刷新节点树
        async refreshGraphTree(graphId) {
            await this.fetchGraphTree(graphId);
            return this.currentNodeTree;
        },

        // 删除节点
        async deleteNodeAction(nodeId) {
            this.isLoading = true;
            try {
                const res = await deleteNode(nodeId);
                if (res.code === 200) {
                    this.error = null;
                    return true;
                } else {
                    this.error = res.msg;
                    return false;
                }
            } catch (error) {
                this.error = '删除节点失败';
                return false;
            } finally {
                this.isLoading = false;
            }
        },

        // 更新节点信息
        async updateNodeInfo(data) {
            try {
                const res = await updateNodeInfo(data);
                if (res.code === 200) {
                    return true;
                } else {
                    this.error = res.msg;
                    return false;
                }
            } catch (error) {
                this.error = '更新节点信息失败';
                return false;
            }
        },

        // 获取所有标签
        async fetchAllTags() {
            try {
                const res = await getAllTags();
                this.tagList = res.code === 200 ? res.result : [];
                return this.tagList;
            } catch (error) {
                console.error('获取标签失败:', error);
                this.tagList = [];
                return [];
            }
        },

        // 关联标签到节点
        async associateTagsToNode(data) {
            try {
                const res = await associateTagsToNode({
                    nodeId: data.nodeId,
                    tagIds: data.tagIds || []  // 确保总是传递数组
                });
                if (res.code === 200) {
                    return true;
                } else {
                    this.error = res.msg;
                    return false;
                }
            } catch (error) {
                this.error = '关联标签失败';
                return false;
            }
        },

        // 辅助方法：根据标签名获取标签ID
        getTagIdByName(tagName) {
            const tagMap = {
                '知识模块': 'tag_001',
                '知识点': 'tag_002',
                '概念性知识': 'tag_003',
                '重点': 'tag_004',
                '考点': 'tag_005',
                '概述': 'tag_006',
                // 其他标签...
            };
            return tagMap[tagName];
        },

        //获取子节点数量
        async fetchChildCount(nodeId) {
            try {
                const res = await getChildCount(nodeId);
                if (res.code === 200) {
                    return res.result || 0; // 确保有默认值
                } else {
                    this.error = res.msg;
                    return 0;
                }
            } catch (error) {
                this.error = '获取子节点数量失败';
                return 0;
            }
        },

        // 计算图谱中所有节点数量（不包括根节点）
        async countAllNodes(graphId) {
            try {
                const res = await getGraphTree(graphId);
                if (res.code === 200 && res.result && res.result.length > 0) {
                    const rootNode = res.result[0];
                    // 递归计算所有子节点数量
                    const countChildren = (node) => {
                        if (!node.children || node.children.length === 0) return 0;
                        return node.children.reduce((total, child) => {
                            return total + 1 + countChildren(child);
                        }, 0);
                    };
                    return countChildren(rootNode);
                }
                return 0;
            } catch (error) {
                console.error('计算节点数量失败:', error);
                return 0;
            }
        },

        // 获取资源树结构
        async fetchResourceTree(courseId) {
            try {
                const res = await getCourseResourceFolderTree(courseId);
                if (res.code === 200) {
                    return res.result;
                } else {
                    this.error = res.msg;
                    return [];
                }
            } catch (e) {
                this.error = '获取资源树失败';
                return [];
            }
        },

        // 关联资源
        async associateResourcesWithNode(nodeId, resourceIds) {
            try {
                const res = await batchUpdateNodeResources({
                    nodeId,
                    resourceIds,
                });
                return res.code === 200;
            } catch (e) {
                this.error = '资源关联失败';
                return false;
            }
        },

        // 获取节点详情
        async fetchNodeDetail(nodeId) {
            try {
                const res = await getNodeDetail(nodeId);
                if (res.code === 200) {
                    return res.result;
                } else {
                    this.error = res.msg;
                    return null;
                }
            } catch (e) {
                this.error = '获取节点详情失败';
                return null;
            }
        },

        // 统计节点资源
        async countNodeResources(nodeId) {
            try {
                const res = await countNodeResources(nodeId);
                if (res.code === 200) {
                    return res.result;
                } else {
                    this.error = res.msg;
                    return 0; // 出错时返回0
                }
            } catch (e) {
                this.error = '统计资源数量失败';
                return 0; // 异常时返回0
            }
        },

        // 获取节点已关联的资源列表
        async fetchNodeResources(nodeId) {
            try {
                const res = await getNodeResources(nodeId);
                if (res.code === 200) {
                    return res.result;
                } else {
                    this.error = res.msg;
                    return [];
                }
            } catch (e) {
                this.error = '获取资源列表失败';
                return [];
            }
        },

        // 取消资源关联
        async unlinkResourceFromNode(nodeId, resourceId) {
            try {
                const res = await unlinkResourceFromNode(nodeId, resourceId);
                if (res.code === 200) {
                    return res.result;
                } else {
                    this.error = res.msg;
                    return false;
                }
            } catch (e) {
                this.error = '取消资源关联失败';
                return false;
            }
        },
    }
});
