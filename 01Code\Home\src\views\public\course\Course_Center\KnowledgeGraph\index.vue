<template>
  <div class="knowledge-graph-wrapper">
    <div class="course-map-container">
      <KnowledgeGraph v-if="graphId" :graph-id="graphId" :course-title="courseTitle" />
      <div v-else class="no-graph">
        <p>该课程暂无知识图谱</p>
      </div>
    </div>
  </div>

</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import KnowledgeGraph from '@/views/public/course/Course_Map/components/knowledge-graph.vue'
import { getGraphsByCourse } from '@/api/public/course/courseMap/knowledgeGraph.js'

const route = useRoute()
const graphId = ref(null)
const courseTitle = ref('课程知识图谱')

onMounted(async () => {
  try {
    const res = await getGraphsByCourse(route.params.courseId)
    if (res.code === 200 && res.result.length) {
      // 查找类型为0的知识图谱
      const kg = res.result.find(g => g.graphType === 0)
      if (kg) {
        graphId.value = kg.graphId
        courseTitle.value = kg.graphName || courseTitle.value
      }
    }
  } catch (error) {
    console.error('获取知识图谱失败:', error)
  }
})
</script>

<style scoped>
.knowledge-graph-wrapper {
  background: white;
  border-radius: 8px;
  padding: 0px;
  min-height: 70vh;
  height: calc(100vh);
  z-index: 999;
  overflow: hidden;
  margin: -10px;
}

.course-map-container {
  padding: 20px;
  height: 100%;
}

.no-graph {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  color: #888;
}
</style>