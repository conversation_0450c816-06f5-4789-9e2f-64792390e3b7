<!--src\components\ResourcePreview.vue-->
<template>
  <div v-if="visible" class="preview-dialog-mask" @click.self="close">
    <div class="preview-dialog" :class="{'wide-preview': isWideContent}">
      <div class="preview-header">
        <h3>{{ file.name }}</h3>
        <div class="preview-header-actions">
          <span class="close-btn" @click="close">×</span>
        </div>
      </div>

      <!-- 文件预览内容 -->
      <div class="preview-content">
        <!-- 图片预览 -->
        <img 
          v-if="file.type === 6" 
          :src="file.url" 
          class="preview-image" 
          alt="图片预览"
        />

        <!-- PDF预览 -->
        <iframe 
          v-else-if="file.type === 2" 
          :src="`https://docs.google.com/viewer?url=${encodeURIComponent(file.url)}&embedded=true`" 
          class="preview-iframe"
          frameborder="0"
          title="PDF预览"
        ></iframe>

        <!-- 视频预览 -->
        <video 
          v-else-if="file.type === 1" 
          controls 
          class="preview-video"
        >
          <source :src="file.url" :type="getVideoMimeType(file.fileExtension)">
          您的浏览器不支持视频播放
        </video>

        <!-- Office文档预览 -->
        <iframe
          v-else-if="[3, 4].includes(file.type)"
          :src="`https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(file.url)}`"
          class="preview-iframe"
          frameborder="0"
          title="Office文档预览"
        ></iframe>

        <!-- 外部链接 -->
        <div v-else-if="file.type === 5" class="external-link-preview">
          <p>这是一个外部资源链接</p>
          <a :href="file.url" target="_blank" class="open-link-btn">
            打开链接
          </a>
        </div>

        <!-- 不支持预览的文件类型 -->
        <div v-else class="unsupported-preview">
          <p>该文件类型不支持在线预览</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  visible: Boolean,
  file: Object
});

const emit = defineEmits(['close']);

const close = () => {
  emit('close');
};

// 判断是否为宽内容（PDF和Office文档需要更宽的窗口）
const isWideContent = computed(() => {
  return [2, 3, 4].includes(props.file?.type);
});

const getVideoMimeType = (extension) => {
  const types = {
    mp4: 'video/mp4',
    mov: 'video/quicktime',
    avi: 'video/x-msvideo',
    wmv: 'video/x-ms-wmv',
    mkv: 'video/x-matroska'
  };
  return types[extension?.toLowerCase()] || 'video/mp4';
};
</script>

<style scoped>
.preview-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

/* 基础对话框样式 */
.preview-dialog {
  background-color: white;
  border-radius: 12px;
  width: 80%;
  max-width: 900px;
  height: 80vh;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
}

/* 宽内容（PDF/Office文档）的特殊样式 */
.preview-dialog.wide-preview {
  width: 90%;
  max-width: 1200px;
  height: 85vh;
}

.preview-header {
  padding: 12px 20px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.preview-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80%;
}

.preview-header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.close-btn {
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0 5px;
  line-height: 1;
}

.close-btn:hover {
  color: #333;
}

.preview-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px;
  overflow: auto;
  background-color: #f9f9f9;
}

/* 图片预览样式 */
.preview-image {
  max-width: 100%;
  max-height: calc(80vh - 60px);
  object-fit: contain;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* PDF/Office文档iframe样式 */
.preview-iframe {
  width: 100%;
  height: calc(80vh - 60px);
  border: none;
  background: white;
}

.wide-preview .preview-iframe {
  height: calc(85vh - 60px);
}

/* 视频预览样式 */
.preview-video {
  width: 90%;
  max-width: 800px;
  max-height: calc(80vh - 60px);
}

/* 外部链接和不受支持类型的样式 */
.external-link-preview,
.unsupported-preview {
  text-align: center;
  padding: 30px 20px;
}

.open-link-btn {
  display: inline-block;
  margin-top: 15px;
  padding: 8px 16px;
  background-color: #4c7bff;
  color: white;
  border-radius: 4px;
  text-decoration: none;
  transition: background-color 0.2s;
}

.open-link-btn:hover {
  background-color: #3a6bf0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .preview-dialog {
    width: 95%;
    height: 85vh;
  }
  
  .preview-dialog.wide-preview {
    width: 98%;
    max-width: none;
  }
  
  .preview-header h3 {
    font-size: 14px;
  }
  
  .preview-iframe,
  .preview-video {
    height: calc(85vh - 60px);
  }
}
</style>