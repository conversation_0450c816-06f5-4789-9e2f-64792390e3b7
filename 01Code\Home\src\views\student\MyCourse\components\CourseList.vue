<template>
  <div>
    <div class="top-bar">
      <p>{{ getCourseCountText() }}</p>
    </div>
    <div v-if="filteredCourses.length > 0" class="course-list">
      <div 
        v-for="course in filteredCourses" 
        :key="course.course.id" 
        class="course-item"
        @click="goToCourseDetail(course.course.id)"
      >
        <div class="course-cover-container">
          <img :src="course.course.courseCover || defaultCourseCover" @error="handleImageError" alt="课程封面" class="course-cover">
        </div>
        <h3>{{ course.course.name }}</h3>
        <div class="course-meta">
          <p class="meta-info">
            {{ getCourseLeader(course.teachers)?.teacherName || '暂无' }} | 
            {{ getCourseLeader(course.teachers)?.institution || course.teachers[0]?.institution || '暂无' }} | 
            {{ course.course.credits || '0' }}学分
          </p>
        </div>
        <div class="course-status">
          <span :class="getStateClass(course.course.state)">{{ getStateText(course.course.state) }}</span>
        </div>
      </div>
    </div>

    <!-- 空状态提示 -->
    <div v-else class="empty-state">
      <img 
        src="@/assets/img/Teacher/icon-nocourse.png" 
        alt="无课程"
        class="empty-icon"
      >
      <p class="empty-text">
        {{ isEmptySearch ? '没有找到符合条件的课程' : '您尚未加入任何课程' }}
      </p>
    </div>

    <!-- 分页组件 -->
    <div v-if="filteredCourses.length > 0" class="pagination">
      <button 
        :disabled="pagination.pageNum === 1" 
        @click="changePage(pagination.pageNum - 1)"
      >
        上一页
      </button>
      <span class="page-info">
        第 {{ pagination.pageNum }} 页 / 共 {{ pagination.pages }} 页
      </span>
      <button 
        :disabled="pagination.pageNum >= pagination.pages" 
        @click="changePage(pagination.pageNum + 1)"
      >
        下一页
      </button>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { getStudentCourses } from '@/api/student/course';

const props = defineProps({
  filterParams: {
    type: Object,
    required: true,
    default: () => ({
      status: 0,
      query: ''
    })
  }
});

const router = useRouter();
const courses = ref([]);
const pagination = ref({
  pageNum: 1,
  pageSize: 16, // 每页16门课
  total: 0,
  pages: 1
});

// 获取课程负责人
const getCourseLeader = (teachers) => {
  return teachers?.find(t => t.teamRole === 1) || teachers?.[0];
};

const handleImageError = (e) => {
  e.target.src = defaultCourseCover;
};

const goToCourseDetail = (courseId) => {
  router.push({ 
    name: 'StudentCourseDetail',
    params: { courseId }
  });
};

const isEmptySearch = computed(() => {
  return props.filterParams.query || props.filterParams.status !== 0;
});

const getStateText = (state) => {
  return state === 1 ? '进行中' : '已结束';
};

const getStateClass = (state) => {
  return state === 1 ? 'state-active' : 'state-ended';
};

const filteredCourses = computed(() => {
  if (!Array.isArray(courses.value)) return [];
  
  const { status, query } = props.filterParams;
  const searchTerm = query ? query.toLowerCase() : '';
  
  return courses.value.filter(item => {
    if (!item?.course) return false;
    
    const stateMatch = status === 0 || 
      (status === 1 && item.course.state === 1) ||
      (status === 2 && item.course.state === 0);
    
    const queryMatch = !searchTerm || 
      (item.course.name && item.course.name.toLowerCase().includes(searchTerm));
    
    return stateMatch && queryMatch;
  });
});

const getCourseCountText = () => {
  const count = pagination.value.total;
  if (count === 0) {
    return isEmptySearch.value ? '没有找到符合条件的课程' : '暂无课程';
  }
  return `共 ${count} 门课程${isEmptySearch.value ? '(筛选结果)' : ''}`;
};

const fetchCourses = async () => {
  try {
    const params = {
      pageNum: pagination.value.pageNum,
      pageSize: pagination.value.pageSize,
      state: props.filterParams.status === 1 ? 1 : props.filterParams.status === 2 ? 0 : undefined,
      name: props.filterParams.query || undefined
    };
    
    const res = await getStudentCourses(params);
    courses.value = res?.result?.records || [];
    pagination.value.total = res?.result?.total || 0;
    pagination.value.pages = Math.ceil(pagination.value.total / pagination.value.pageSize);
  } catch (error) {
    console.error('加载课程失败:', error);
    courses.value = [];
    pagination.value.total = 0;
    pagination.value.pages = 1;
  }
};

const changePage = (newPage) => {
  if (newPage < 1 || newPage > pagination.value.pages) return;
  pagination.value.pageNum = newPage;
  fetchCourses();
};
// 监听筛选条件变化
watch(() => props.filterParams, () => {
  pagination.value.pageNum = 1; // 重置页码
  fetchCourses();
}, { deep: true });

onMounted(fetchCourses);
</script>

<style lang="scss" scoped>
@use '@/styles/variables' as *;

.top-bar {
  padding: 16px;
  font-size: 16px;
  color: #666;
}

.course-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 20px;
  padding: 0 16px;
}

.course-item {
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
}

.course-cover-container {
  width: 100%;
  height: 160px;
  background-color: #f5f5f5;
  
  .course-cover {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

h3 {
  padding: 12px 16px 0;
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.course-meta {
  padding: 8px 16px;
  font-size: 14px;
  color: #666;
  
  .meta-info {
    margin: 6px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.course-status {
  padding: 0 16px 12px;
  display: flex;
  justify-content: flex-end;
  
  span {
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    
    &.state-active {
      background-color: #f6ffed;
      color: #52c41a;
      border: 1px solid #52c41a;
    }
    
    &.state-ended {
      background-color: #fafafa;
      color: #8c8c8c;
      border: 1px solid #d9d9d9;
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  
  .empty-icon {
    width: 120px;
    height: 120px;
    opacity: 0.6;
  }
  
  .empty-text {
    margin-top: 20px;
    color: #999;
    font-size: 16px;
  }
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  gap: 20px;
  
  button {
    padding: 8px 16px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover:not(:disabled) {
      border-color: $primary-color;
      color: $primary-color;
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
  
  .page-info {
    color: #666;
  }
}
</style>