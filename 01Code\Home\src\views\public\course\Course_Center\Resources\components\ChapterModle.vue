<template>
  <div class="chapter-model">
    <h2>{{ currentTab }}</h2>

    <!-- 表头 -->
    <div class="resource-header">
      <div class="header-item header-name">名称</div>
      <div class="header-item header-chapter">所属章节</div>
      <div class="header-item header-size">大小</div>
      <div class="header-item header-creator">创始人</div>
    </div>

    <!-- 资源列表 -->
    <div class="resource-list">
      <template v-for="(item, index) in currentResources" :key="index">
        <!-- 主项目 -->
        <div class="resource-item" @click.stop="item.type === 'folder' ? toggleFolder(item) : openFilePreview(item)">
          <div class="item-cell item-name">
            <span v-if="item.type === 'folder'" class="folder-toggle">
              <i class="iconfont" :class="expandedFolders[item.name] ? 'icon-down' : 'icon-right'"></i>
            </span>
            <span class="item-type-icon">{{ getItemTypeIcon(item.type) }}</span>
            {{ item.name }}
            
          </div>
          <div class="item-cell item-chapter">{{ item.chapter }}</div>
          <div class="item-cell item-size">{{ item.size }}</div>
          <div class="item-cell item-creator">{{ item.creator }}</div>
        </div>

        <!-- 文件夹内容 -->
        <div v-if="item.type === 'folder' && expandedFolders[item.name]" class="folder-contents">
          <div class="resource-item sub-item" v-for="(subItem, subIndex) in item.contents" :key="subIndex"
            @click.stop="openFilePreview(subItem)">
            <div class="item-cell item-name">
              <span class="item-type-icon">{{ getItemTypeIcon(subItem.type) }}</span>
              {{ subItem.name }}
            </div>
            <div class="item-cell item-chapter">{{ subItem.chapter }}</div>
            <div class="item-cell item-size">{{ subItem.size }}</div>
            <div class="item-cell item-creator">{{ subItem.creator }}</div>
          </div>
        </div>
      </template>
    </div>

    <div v-if="currentResources.length === 0" class="empty-state">
      暂无资源
    </div>

    <!-- 文件预览弹窗 -->
    <div v-if="previewFile" class="preview-modal" @click="closeFilePreview">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ previewFile.name }}</h3>
          <button class="close-btn" @click="closeFilePreview">×</button>
        </div>
        <div class="modal-body">
          <div class="preview-container">
            <div v-if="isLoading" class="loading-placeholder">
              <div class="loading-spinner">⏳</div>
              <p>正在加载文件...</p>
            </div>
            <iframe v-else-if="previewFile.type === 'pptx' || previewFile.type === 'docx'"
              :src="`https://view.officeapps.live.com/op/embed.aspx?src=${getFileUrl(previewFile)}`" frameborder="0"
              class="office-preview"></iframe>
          </div>
          <div class="file-info">
            <a v-if="previewFile.testUrl" :href="previewFile.testUrl" target="_blank" class="download-link">
              下载文件
            </a>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  currentTab: {
    type: String,
    required: true
  }
})

// 跟踪哪些文件夹是展开的
const expandedFolders = ref({})

// 跟踪当前预览的文件
const previewFile = ref(null)
const isLoading = ref(false)

// 切换文件夹展开状态
const toggleFolder = (item) => {
  if (item.type === 'folder') {
    expandedFolders.value[item.name] = !expandedFolders.value[item.name]
  }
}

// 打开文件预览
const openFilePreview = async (item) => {
  if (item.type === 'pptx' || item.type === 'docx') {
    isLoading.value = true
    previewFile.value = item

    // 模拟加载延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    isLoading.value = false
  }
}

// 关闭文件预览
const closeFilePreview = () => {
  previewFile.value = null
  isLoading.value = false
}

// 获取文件URL
const getFileUrl = (file) => {
  if (file.testUrl) {
    return encodeURIComponent(file.testUrl)
  }
  // 实际项目中这里应该是你的文件API地址
  return encodeURIComponent(`/api/files/${file.id}`)
}

// 更新资源数据结构，包含测试文件URL
const mockResources = {
  '各具千秋的茶叶分类': [
    {
      name: '茶叶分类资料',
      type: 'folder',
      chapter: '第一章',
      size: '128MB',
      creator: '王教授',
      contents: [
        {
          name: '茶叶分类详解.pptx',
          type: 'pptx',
          chapter: '第一章',
          size: '45MB',
          creator: '王教授',
          testUrl: 'https://filesamples.com/samples/document/ppt/sample1.ppt'
        },
        {
          name: '中国六大茶类.docx',
          type: 'docx',
          chapter: '第一章',
          size: '32MB',
          creator: '李博士',
          testUrl: 'https://filesamples.com/samples/document/docx/sample3.docx'
        }
      ]
    },
    {
      name: '茶艺表演视频.pptx',
      type: 'pptx',
      chapter: '第二章',
      size: '256MB',
      creator: '刘师傅',
      testUrl: 'https://file-examples.com/wp-content/uploads/2017/08/file-example_PPT_1MB.pptx'
    }
  ],
  '追根溯源识茶貌': [
    {
      name: '茶树研究资料',
      type: 'folder',
      chapter: '第三章',
      size: '180MB',
      creator: '周博士',
      contents: [
        {
          name: '茶树起源研究.docx',
          type: 'docx',
          chapter: '第三章',
          size: '56MB',
          creator: '周博士',
          testUrl: 'https://file-examples.com/wp-content/uploads/2017/02/file-sample_1MB.docx'
        }
      ]
    }
  ]
}

// 根据当前标签获取对应的资源列表
const currentResources = computed(() => {
  return mockResources[props.currentTab] || []
})

// 资源类型图标映射
const getItemTypeIcon = (type) => {
  const typeMap = {
    folder: '📁',
    pptx: '📊',
    docx: '📄'
  }
  return typeMap[type] || '❓'
}

// 获取完整的文件类型名称
const getFullTypeName = (type) => {
  const typeMap = {
    pptx: 'PowerPoint演示文稿',
    docx: 'Word文档'
  }
  return typeMap[type] || type
}
</script>

<style scoped lang="scss">
.chapter-model {
  h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.5rem;
  }

  /* 表头样式 */
  .resource-header {
    display: grid;
    grid-template-columns: 3fr 1fr 1fr 1fr;
    margin-bottom: 10px;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 6px 6px 0 0;

    .header-item {
      padding: 12px 15px;
      font-weight: 600;
      color: #333;

      &:last-child {
        border-right: none;
      }

      &.header-name {
        border-radius: 6px 0 0 0;
      }

      &.header-creator {
        border-radius: 0 6px 0 0;
      }
    }
  }

  /* 资源列表样式 */
  .resource-list {
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 6px 6px;
  }

  .resource-item {
    display: grid;
    grid-template-columns: 3fr 1fr 1fr 1fr;
    background-color: #fff;
    border-bottom: 1px solid #ddd;
    cursor: pointer;
    transition: background-color 0.2s;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: #f0f8ff;
    }

    .item-cell {
      padding: 12px 15px;
      display: flex;
      align-items: center;

      &:last-child {
        border-right: none;
      }

      &.item-name {
        gap: 8px;
      }
    }

    .folder-toggle {
      font-size: 0.8em;
    }
  }

  /* 子项目样式 */
  .folder-contents {
    background-color: #f9f9f9;

    .sub-item {
      background-color: #f9f9f9;
      border-bottom: 1px solid #eee;

      &:hover {
        background-color: #e6f2ff;
      }

      .item-name {
        padding-left: 80px;
      }
    }
  }

  .empty-state {
    padding: 40px 20px;
    text-align: center;
    color: #999;
    font-size: 16px;
    border: 1px dashed #ddd;
    border-radius: 6px;
    margin-top: 20px;
  }

  /* 文件预览弹窗样式 */
  .preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }

  .modal-content {
    background-color: white;
    width: 85%;
    max-width: 900px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    max-height: 90vh;
  }

  .modal-header {
    padding: 16px 24px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 1.2rem;
      color: #333;
      max-width: 80%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 24px;
      color: #999;
      cursor: pointer;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        color: #333;
        background-color: #f5f5f5;
        border-radius: 50%;
      }
    }
  }

  .modal-body {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;

    .preview-container {
      height: 60vh;
      border: 1px solid #eee;
      border-radius: 4px;
      overflow: hidden;
      margin-bottom: 20px;
      position: relative;

      .office-preview {
        width: 100%;
        height: 100%;
        border: none;
        background: #f5f5f5;
      }

      .loading-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #666;

        .loading-spinner {
          margin-bottom: 16px;
          font-size: 2rem;
          animation: spin 1s linear infinite;
        }
      }
    }

    .file-info {
      display: flex;

      p {
        margin: 8px 0;
        display: flex;

        strong {
          display: inline-block;
          width: 60px;
          color: #666;
        }
      }

      .download-link {
        display: inline-block;
        margin-top: 12px;
        padding: 6px 12px;
        background-color: #f0f7ff;
        color: #1890ff;
        border-radius: 4px;
        text-decoration: none;

        &:hover {
          background-color: #e6f1ff;
        }
      }
    }
  }


}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {

  .resource-header,
  .resource-item {
    grid-template-columns: 2fr 1fr 1fr 1fr;
  }

  .modal-content {
    width: 95%;
  }
}
</style>