<!-- src/components/CourseCard.vue -->
<template>
  <div 
    class="course-card" 
    :data-course-id="course.id"
    :style="{ 'background-image': `url(${course.cover})` }"
  >
    <div class="course-content">
      <h3 class="course-title">{{ course.title }}</h3>
      <div class="course-type">{{ course.type }}</div>
      <div class="course-nature">{{ course.nature }}</div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  course: {
    type: Object,
    required: true
  }
})
</script>

<style lang="scss" scoped>
.course-card {
  min-width: 200px;
  min-height: 280px;
  border-radius: 8px;
  color: #fff;
  transition: all 0.3s ease;
  flex-shrink: 0;
  position: relative;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  margin-right: 1.5rem; /* 为滚动区域添加间距 */

   user-select: none; /* 禁止文本选择 */
  -webkit-user-select: none; /* Safari 兼容 */
  -moz-user-select: none; /* Firefox 兼容 */
  -ms-user-select: none; /* IE/Edge 兼容 */
  
  /* 背景遮罩层 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0.7) 100%);
    border-radius: 8px;
    z-index: 1;
  }

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 20px rgba(0,0,0,0.2);
  }

  .course-content {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    height: 100%;
    padding: 1rem;
    box-sizing: border-box;
  }

  .course-title {
    font-size: 1.2rem;
    margin-bottom: 0.3rem;
    font-weight: bold;
  }

  .course-type,
  .course-nature {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 0.2rem;
  }
}
</style>