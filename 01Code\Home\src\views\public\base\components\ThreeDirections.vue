<!--src\views\public\base\components\ThreeDirections.vue-->
<template>
  <div class="three-directions">
    <h2 class="section-title">三大方向</h2>
    <div class="directions-container">
      <div 
        class="direction-item" 
        v-for="(item, index) in directions" 
        :key="index"
        :style="{ backgroundImage: `url('${item.image}')` }"
        @click="handleDirectionClick(item)"
      >
        <div class="content-wrapper">
          <h3 class="direction-title">{{ item.title }}</h3>
          <!--<p class="direction-desc">{{ item.desc }}</p>-->
          <img src="@/assets/img/Home/ic_enter.png" alt="方向箭头" class="direction-arrow">
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
// 模拟数据，实际可从接口获取或通过父组件传递
const directions = ref([
  {
    image: new URL('@/assets/img/Home/ThreeDirections_XR.png', import.meta.url).href, 
    title: '仿真XR(游戏)',
    desc: '关于仿真XR（游戏）方向的详细描述...',
    type: 'simulation-xr'
  },
  {
    image: new URL('@/assets/img/Home/ThreeDirections_QZ.png', import.meta.url).href, 
    title: '全栈开发',
    desc: '关于全栈开发方向的详细描述...',
    type: 'full-stack'
  },
  {
    image: new URL('@/assets/img/Home/ThreeDirections_SZ.png', import.meta.url).href, 
    title: '数字视觉与艺术工程',
    desc: '关于数字视觉与艺术工程方向的详细描述...',
    type: 'digital-visual'
  }
]);

const emit = defineEmits(['direction-click']);

const handleDirectionClick = (item) => {
  emit('direction-click', item.type);
};
</script>

<style scoped lang="scss">
.three-directions {
  width: 100%;
  padding: 2rem;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-top: 2rem;

  .section-title {
    font-size: 2rem;
    color: #333;
    margin-bottom: 2rem;
  }

  .directions-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
  }

  .direction-item {
    width: 100%;
    height: 300px; /* 根据需求设置合适的高度 */
    background-size: cover;
    background-position: center;
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.3s ease;
    position: relative; /* 为子元素绝对定位做准备 */
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      transform: translateY(-5px);
    }

    .content-wrapper {
      text-align: center;
      color: #fff; /* 文字颜色，可根据背景图调整 */
      text-shadow: 0 0 5px rgba(0, 0, 0, 0.5); /* 增加文字阴影，提升可读性 */
      position: relative; /* 让内容相对自身定位，也可设为absolute，结合父元素定位 */
      z-index: 1; /* 确保文字在背景上方 */
    }

    .direction-title {
      font-size: 1.4rem;
      margin-bottom: 0.5rem;
    }

    .direction-desc {
      font-size: 1rem;
    }
  }
}
</style>