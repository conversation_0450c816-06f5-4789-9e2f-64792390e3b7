<template>
    <div class="filter-bar">
        <!-- 状态行 -->
        <div class="filter-status">
            共 {{ total }} 道试题
            <el-switch v-model="filterOn" active-text="筛选" size="small" />
        </div>

        <!-- 第一行：筛选项 -->
        <div class="filter-line no-wrap">
            <span class="filter-label">创建时间</span>
            <el-date-picker v-model="localFilters.startTime" type="date" size="small" placeholder="开始日期"
                style="width: 120px;" />
            至
            <el-date-picker v-model="localFilters.endTime" type="date" size="small" placeholder="结束日期"
                style="width: 120px;" />
        </div>

        <!-- 第二行：题型按钮 -->
        <div class="filter-line no-wrap">
            <span class="filter-label">题型</span>
            <el-button v-for="type in questionTypes" :key="type.value"
                :type="localFilters.qType === type.value ? 'primary' : 'text'" size="small" @click="setQType(type.value)"
                class="qtype-btn">
                {{ type.label }}
            </el-button>
        </div>

        <!-- 第三行：左右功能操作 -->
        <div class="filter-line between">
            <div class="left-actions">
                <el-button size="small" @click="$emit('batchDelete')">批量删除</el-button>
            </div>
            <div class="right-actions">
                <el-checkbox v-model="localFilters.onlyLinked" label="仅显示引用" />
                <el-checkbox v-model="localFilters.unlinkedCourse" label="未关联课程" />
                <el-button size="small" type="text" @click="$emit('toggleSortDesc')">
                    {{ localFilters.sortDesc ? '按创建时间倒序' : '按创建时间正序' }} ⇅
                </el-button>
            </div>
        </div>
    </div>
</template>
  
<script setup>
import { reactive, watch, ref, defineProps, defineEmits } from 'vue'

const props = defineProps({
    modelValue: Object,
    total: Number,
    questionTypes: { type: Array, default: () => [] },
    owners: { type: Array, default: () => [] }
})

const emit = defineEmits([
    'update:modelValue',
    'update',
    'batchMark',
    'batchPreview',
    'batchDelete',
    'batchUnlink',
    'toggleSortDesc'
])

const defaultFilters = {
    course: 'all',
    resource: 'all',
    difficulty: 'all',
    knowledge: 'all',
    type: 'all',
    owner: 'all',
    startTime: null,
    endTime: null,
    qType: 'all',
    onlyLinked: false,
    unlinkedCourse: false,
    unlinkedKnowledge: false,
    sortDesc: true
}

const localFilters = reactive({ ...defaultFilters })
const filterOn = ref(true)

watch(
    () => props.modelValue,
    (newVal) => {
        if (newVal) Object.assign(localFilters, newVal)
    },
    { immediate: true }
)

watch(
    localFilters,
    (newVal) => {
        emit('update:modelValue', { ...newVal })
        emit('update', { ...newVal })
    },
    { deep: true }
)

watch(filterOn, (val) => {
    if (!val) {
        Object.assign(localFilters, defaultFilters)
    }
})

const setQType = (type) => {
    localFilters.qType = type
}
</script>
  
<style scoped>
.filter-bar {
    background: #f8f9fc;
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 16px;
    font-size: 14px;
}

.filter-status {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 10px;
}

.filter-line {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    flex-wrap: nowrap;
    overflow-x: auto;
}

.filter-line.no-wrap>.filter-label,
.filter-line.no-wrap>el-select {
    max-width: 70px;
    min-width: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    vertical-align: middle;
    font-size: 13px;
}

.filter-line.no-wrap>el-date-picker {
    max-width: 180px;
    min-width: 140px;
}

.between {
    justify-content: space-between;
}

.left-actions,
.right-actions {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: nowrap;
}

.filter-label {
    font-weight: bold;
    color: #333;
}

.qtype-btn {
    margin-right: 6px;
    padding: 4px 8px;
    color: #333;
    border-radius: 4px;
    cursor: pointer;
}

.qtype-btn.el-button--primary {
    background-color: #409EFF;
    color: white;
    border: none;
}
</style>
