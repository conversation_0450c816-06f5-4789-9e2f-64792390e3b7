// stores/resource.js
import { defineStore } from 'pinia'
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/userStore'
import { useCourseStore } from '@/stores/courseStore'
import { useRoute } from 'vue-router';
import {getCourseFolderTree} from '@/api/teacher/publicResources'


export const useResourceStore = defineStore('resource', () => {
  const route = useRoute();
  const userStore = useUserStore();
  const courseStore = useCourseStore();

  // 状态
  const folderTree = ref([]); // 完整的树形结构
  const fileMap = ref(new Map()); // 文件ID到文件的映射
  const searchQuery = ref('');
  const resources = ref([]); 

  // 计算属性
  const createdId = computed(() => userStore.user.id);
  const currentCourseId = computed(() => route.params.courseId || courseStore.currentCourseId);
  

  // 刷新列表
const refreshFolders = async () => {
  try {
    const response = await getCourseFolderTree({ courseId: currentCourseId.value });
    if (response.code === 200) {
      const formattedFolders = formatFolderData(response.result);
      console.log('Formatted Folders:', formattedFolders);
      folderTree.value = formattedFolders;
      buildFileMap(formattedFolders);
    }
  } catch (error) {
    console.error('刷新文件夹失败:', error);
  }
};

const formatFolderData = (data) => {
  return data.map(folder => {
    // 处理当前文件夹资源
    const files = folder.resources ? folder.resources.map(resource => ({
      name: resource.name,
      checked: false,
      isLink: resource.type === 5,
      url: resource.url,
      type: resource.type || '',
      size: resource.fileSize || 0,
      id: resource.id,
      state:resource.state
    })) : [];
    
    const children = folder.children ? formatFolderData(folder.children) : [];
    
    // 计算总文件数
    const totalFileCount = files.length + children.reduce((sum, child) => sum + child.totalFileCount, 0);
    
    return {
      id: folder.id,
      name: folder.name,
      files: files,
      totalFileCount: totalFileCount,
      expanded: true,
      checked: false,
      children: children
    };
  });
}

  // 方法
  const setFolders = (folders) => {
    folderTree.value = folders;
    buildFileMap(folders);
  };

  const buildFileMap = (folders) => {
    fileMap.value = new Map();
    const traverse = (folder) => {
      folder.files.forEach(file => {
        fileMap.value.set(file.id, { ...file, folderId: folder.id });
      });
      folder.children?.forEach(traverse);
    };
    folders.forEach(traverse);
  };

  const findFolder = (folderId) => {
    const traverse = (folder) => {
      if (folder.id === folderId) return folder;
      for (const child of folder.children || []) {
        const found = traverse(child);
        if (found) return found;
      }
    };
    return folderTree.value.find(traverse);
  };




  return {
    // 状态
    folderTree,
    fileMap,
    searchQuery,
    resources,
    createdId,
    currentCourseId,
    setFolders,
    findFolder,
    refreshFolders

  };
});