<template>
  <div class="modal-overlay" v-if="visible">
    <div class="modal-content">
      <div class="modal-header">
        <h3>移动到</h3>
        <button class="close-btn" @click="handleCancel">×</button>
      </div>
      <div class="modal-body">
        <div class="folder-tree">
          <div 
            v-for="folder in folders" 
            :key="folder.id" 
            class="folder-item"
            :class="{ selected: selectedFolder && selectedFolder.id === folder.id }"
            @click="handleFolderClick(folder)"
          >
            <span class="folder-icon">📁</span>
            <span class="folder-name">{{ folder.name }}</span>
          </div>
          
          <div class="new-folder-btn" @click="handleNewFolder">
            <span class="plus-icon">+</span> 新建文件夹
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="move-btn" @click="handleConfirm">移动</button>
        <button class="cancel-btn" @click="handleCancel">取消</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MoveModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    folders: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      selectedFolder: null
    };
  },
  methods: {
    handleFolderClick(folder) {
      this.selectedFolder = folder;
    },
    handleNewFolder() {
      this.$emit('new-folder', this.selectedFolder);
    },
    handleConfirm() {
      if (!this.selectedFolder) {
        alert('请选择目标文件夹');
        return;
      }
      this.$emit('confirm', this.selectedFolder);
    },
    handleCancel() {
      this.$emit('cancel');
      this.selectedFolder = null;
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}
.modal-content {
  background-color: #fff;
  border-radius: 8px;
  width: 400px;
  max-width: 90%;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
}
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}
.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #909399;
}
.modal-body {
  padding: 20px;
}
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}
.confirm-btn, .move-btn, .delete-btn {
  padding: 8px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}
.confirm-btn, .move-btn {
  background-color: #409eff;
  color: white;
}
.confirm-btn:hover, .move-btn:hover {
  background-color: #3a8ee6;
}
.cancel-btn {
  padding: 8px 20px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
  color: #606266;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}
.cancel-btn:hover {
  background-color: #f5f7fa;
}
.folder-tree {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 15px;
}
.folder-item {
  padding: 10px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  border-radius: 4px;
  transition: background-color 0.2s;
}
.folder-item:hover {
  background-color: #f5f7fa;
}
.folder-item.selected {
  background-color: #e6f3ff;
  font-weight: 500;
}
.folder-icon {
  color: #409eff;
}
.folder-name {
  flex: 1;
}
.new-folder-btn {
  padding: 5px 10px;
  background-color: #f0f2f5;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  margin-top: 10px;
  display: inline-block;
}
.new-folder-btn:hover {
  background-color: #e7eaf3;
}
.plus-icon {
  margin-right: 5px;
  color: #409eff;
}
.new-folder-input {
  margin-top: 10px;
}
.new-folder-input input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
}
</style>