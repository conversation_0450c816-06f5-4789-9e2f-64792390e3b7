<template>
  <div class="open-education-container">
    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-box">
        <input
          v-model="searchParams.name"
          type="text"
          placeholder="搜索开放教育资源..."
          @keyup.enter="handleSearch"
          class="search-input"
        />
        <button @click="handleSearch" class="search-btn">
          <i class="fas fa-search"></i>
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>正在加载开放教育资源...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <div class="error-icon">⚠️</div>
      <p class="error-message">{{ error }}</p>
      <button @click="fetchOpenEducationList" class="retry-btn">重试</button>
    </div>

    <!-- 无数据状态 -->
    <div v-else-if="educationResources.length === 0" class="empty-container">
      <div class="empty-icon">📚</div>
      <p>暂无开放教育资源</p>
    </div>

    <!-- 资源网格 -->
    <div v-else class="resource-grid">
      <div
        v-for="item in educationResources"
        :key="item.resourceId"
        class="resource-item"
        :style="{ backgroundImage: `url(${item.coverImage || '/src/assets/img/Course/courseCover/default.png'})` }"
        @click="openResource(item)"
      >
        <div class="resource-overlay">
          <div class="resource-content">
            <h3 class="resource-title">{{ item.name || '未命名资源' }}</h3>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页组件 -->
    <div v-if="educationResources.length > 0" class="pagination-container">
      <button
        @click="prevPage"
        :disabled="pagination.current <= 1"
        class="page-btn"
      >
        上一页
      </button>
      <div class="page-info">
        第 {{ pagination.current }} / {{ pagination.pages }} 页
      </div>
      <button
        @click="nextPage"
        :disabled="pagination.current >= pagination.pages"
        class="page-btn"
      >
        下一页
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue';
import { getOpenEducationList } from '@/api/public/resource/openEducation';
import { ElMessage } from 'element-plus';

// 响应式数据
const educationResources = ref([]);
const isLoading = ref(false);
const error = ref(null);

// 搜索参数
const searchParams = reactive({
  name: ''
});

// 分页信息
const pagination = reactive({
  current: 1,
  size: 12,
  total: 0,
  pages: 1
});

// 获取开放教育资源列表
const fetchOpenEducationList = async (resetPage = false) => {
  if (resetPage) {
    pagination.current = 1;
  }

  isLoading.value = true;
  error.value = null;

  try {
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.size,
      ...searchParams
    };

    // 过滤空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key];
      }
    });

    const response = await getOpenEducationList(params);

    if (response && response.result) {
      educationResources.value = response.result.records || [];
      pagination.total = response.result.total || 0;
      pagination.pages = response.result.pages || 1;
      pagination.current = response.result.current || 1;

      // 调试信息
      console.log('获取到的开放教育资源数据:', educationResources.value);
    } else {
      throw new Error('响应数据格式错误');
    }

  } catch (err) {
    console.error('获取开放教育资源失败:', err);
    error.value = err.response?.data?.msg || err.message || '获取数据失败，请重试';
    ElMessage.error(error.value);

    // 如果API失败，使用模拟数据进行测试
    console.log('使用模拟数据进行测试...');
    educationResources.value = [
      {
        resourceId: 'edu001',
        name: '计算机科学导论',
        coverImage: 'https://example.com/cs-cover.jpg',
        teacher: '张教授',
        school: 'XX大学',
        hyperlink: 'https://example.com/cs-intro',
        description: '计算机科学入门课程，涵盖基础理论和实践',
        createTime: 1750577557,
        updateTime: 1750577557
      },
      {
        resourceId: 'edu002',
        name: '经济学原理',
        coverImage: 'https://example.com/econ-cover.jpg',
        teacher: '李教授',
        school: 'XX大学',
        hyperlink: 'https://example.com/econ-principles',
        description: '宏观与微观经济学基础理论',
        createTime: 1750577557,
        updateTime: 1750577557
      },
      {
        resourceId: 'edu003',
        name: '世界历史',
        coverImage: 'https://example.com/history-cover.jpg',
        teacher: '王教授',
        school: 'XX大学',
        hyperlink: 'https://example.com/world-history',
        description: '全球文明发展史概览',
        createTime: 1750577557,
        updateTime: 1750577557
      },
      {
        resourceId: 'edu004',
        name: '基础物理学',
        coverImage: 'https://example.com/physics-cover.jpg',
        teacher: '赵教授',
        school: 'XX大学',
        hyperlink: 'https://example.com/basic-physics',
        description: '力学与电磁学基础原理',
        createTime: 1750577557,
        updateTime: 1750577557
      },
      {
        resourceId: 'edu005',
        name: '文学欣赏',
        coverImage: 'https://example.com/literature-cover.jpg',
        teacher: '刘教授',
        school: 'XX大学',
        hyperlink: 'https://example.com/literature-app',
        description: '中外文学名著赏析与解读',
        createTime: 1750577557,
        updateTime: 1750577557
      }
    ];
    pagination.total = 5;
    pagination.pages = 1;
    pagination.current = 1;
    error.value = null; // 清除错误状态，显示模拟数据
  } finally {
    isLoading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  fetchOpenEducationList(true);
};

// 分页处理
const prevPage = () => {
  if (pagination.current > 1) {
    pagination.current--;
    fetchOpenEducationList();
  }
};

const nextPage = () => {
  if (pagination.current < pagination.pages) {
    pagination.current++;
    fetchOpenEducationList();
  }
};

// 打开资源
const openResource = (item) => {
  if (item.hyperlink) {
    window.open(item.hyperlink, '_blank');
  } else {
    ElMessage.warning('该资源暂无链接');
  }
};





// 组件挂载时获取数据
onMounted(() => {
  fetchOpenEducationList();
});
</script>

<style scoped>
.open-education-container {
  padding: 20px;
  background-color: #ffffff;
  min-height: 100vh;
}

/* 搜索区域 */
.search-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  gap: 20px;
  flex-wrap: wrap;
}

.search-box {
  display: flex;
  align-items: center;
  flex: 1;
  max-width: 400px;
}

.search-input {
  flex: 1;
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 25px 0 0 25px;
  outline: none;
  font-size: 14px;
}

.search-input:focus {
  border-color: #8a6de3;
  box-shadow: 0 0 0 2px rgba(138, 109, 227, 0.2);
}

.search-btn {
  padding: 10px 15px;
  background: #8a6de3;
  color: white;
  border: none;
  border-radius: 0 25px 25px 0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.search-btn:hover {
  background: #7a5dd3;
}



/* 状态容器 */
.loading-container, .error-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon, .empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-message {
  color: #dc3545;
  margin-bottom: 16px;
  text-align: center;
}

.retry-btn {
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.retry-btn:hover {
  background: #0056b3;
}

/* 资源网格 */
.resource-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.resource-item {
  height: 280px;
  border-radius: 12px;
  overflow: hidden;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-color: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  position: relative;
}

.resource-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.resource-item:hover .resource-overlay {
  background: rgba(0, 0, 0, 0.7);
}

.resource-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  padding: 20px;
  transition: background-color 0.3s ease;
}

.resource-content {
  color: white;
  width: 100%;
}

.resource-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 分页组件 */
.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  padding: 20px 0;
}

.page-btn {
  padding: 8px 16px;
  background: #8a6de3;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.page-btn:hover:not(:disabled) {
  background: #7a5dd3;
  transform: translateY(-1px);
}

.page-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.page-info {
  color: #666;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .open-education-container {
    padding: 15px;
  }

  .search-section {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box {
    max-width: none;
  }

  .resource-grid {
    grid-template-columns: 1fr;
  }

  .pagination-container {
    flex-direction: column;
    gap: 10px;
  }
}
</style>