<template>
  <div class="course-frame">
    <div class="edit-header">
      <h2>课程框架</h2>
      <div class="action-buttons" v-if="!isEditing">
        <el-button 
          type="primary" 
          @click="toggleEditMode"
          size="small"
          :loading="loading"
        >
          编辑
        </el-button>
      </div>
      <div class="action-buttons" v-else>
        <el-button 
          size="small"
          @click="cancelEdit"
          :disabled="loading"
        >
          取消
        </el-button>
        <el-button 
          type="primary" 
          @click="saveInfo"
          size="small"
          :loading="loading"
        >
          保存
        </el-button>
      </div>
    </div>

    <el-skeleton :loading="loading" animated :count="modules.length">
      <template #template>
        <el-skeleton-item variant="text" style="width: 30%; margin-bottom: 16px;" />
        <el-skeleton-item variant="text" style="width: 100%; margin-bottom: 24px;" />
      </template>
      <template #default>
        <div v-if="!isEditing">
          <div 
            class="course-module" 
            v-for="(module, index) in modules" 
            :key="index"
          >
            <h3>{{ module.title }}</h3>
            <div class="content ql-editor" v-html="safeHtml(module.content) || '暂无内容'"></div>
          </div>
        </div>

        <el-form v-else label-position="top" class="edit-form">
          <div 
            v-for="(module, index) in modules" 
            :key="index"
            class="form-module"
          >
            <el-form-item :label="module.title">
              <div class="quill-container">
                <quill-editor
                  v-model:content="module.content"
                  contentType="html"
                  :options="editorOptions"
                  :placeholder="`请输入${module.title}内容`"
                  @ready="(editor) => onEditorReady(`module${index}`, editor)"
                />
              </div>
            </el-form-item>
          </div>
        </el-form>
      </template>
    </el-skeleton>

    <!-- 新增模块按钮-->
    <el-button 
      type="info"  
      size="middle" 
      @click="addModule"
      class="add-module-btn"
    >
      +新增模块
    </el-button>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onBeforeUnmount } from 'vue'
import { ElMessage } from 'element-plus'
import { QuillEditor } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css'
import DOMPurify from 'dompurify'

const props = defineProps({
  course: {
    type: Object,
    required: true
  }
})

const loading = ref(false)
const isEditing = ref(false)
const quillInstances = ref({}) 
const autoSaveTimer = ref(null)

// 初始模块数据结构，可根据实际调整
const modules = reactive([
  { title: '模块1', content: '' },
  { title: '模块2', content: '' }
])

const originalModules = reactive(JSON.parse(JSON.stringify(modules)))

// XSS防护
const safeHtml = (html) => {
  return html ? DOMPurify.sanitize(html) : ''
}

const editorOptions = reactive({
  modules: {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],
      ['blockquote', 'code-block'],
      [{ 'header': 1 }, { 'header': 2 }],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'script': 'sub'}, { 'script': 'super' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      [{ 'direction': 'rtl' }],
      [{ 'size': ['small', false, 'large', 'huge'] }],
      [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'font': [] }],
      [{ 'align': [] }],
      ['clean'],
      ['link', 'image', 'video']
    ],
    clipboard: {
      matchVisual: false 
    }
  },
  placeholder: '请输入内容...',
  theme: 'snow',
  bounds: document.querySelector('.quill-container') || document.body
})

// 编辑器准备就绪回调
const onEditorReady = (field, editor) => {
  quillInstances.value[field] = editor
}

// 模拟获取课程框架信息（根据实际需求对接真实接口）
const fetchCourseFrame = async () => {
  try {
    loading.value = true
    // 这里可替换为真实接口调用，获取已有模块数据
    // 示例：假设从localStorage模拟获取
    const storedData = localStorage.getItem(`course_frame_${props.course.id}`)
    if (storedData) {
      const parsed = JSON.parse(storedData)
      parsed.forEach((item, index) => {
        modules[index].title = item.title
        modules[index].content = item.content
      })
    }
  } catch (error) {
    console.error('获取课程框架信息失败:', error)
    ElMessage.error(`获取课程框架信息失败: ${error.message}`)
  } finally {
    loading.value = false
  }
}

// 保存课程框架信息（根据实际需求对接真实接口）
const saveInfo = async () => {
  try {
    loading.value = true
    // 准备待保存数据（包含XSS防护）
    const sanitizedData = modules.map(module => ({
      title: module.title,
      content: safeHtml(module.content)
    }))
    // 这里可替换为真实接口调用，保存到后端
    // 示例：保存到localStorage
    localStorage.setItem(`course_frame_${props.course.id}`, JSON.stringify(sanitizedData))
    ElMessage.success('保存成功')
    Object.assign(originalModules, JSON.parse(JSON.stringify(modules)))
    isEditing.value = false
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error(`保存失败: ${error.message}`)
    isEditing.value = true
  } finally {
    loading.value = false
  }
}

// 取消编辑
const cancelEdit = () => {
  modules.splice(0, modules.length, ...JSON.parse(JSON.stringify(originalModules)))
  isEditing.value = false
}

// 进入编辑模式
const toggleEditMode = () => {
  Object.assign(originalModules, JSON.parse(JSON.stringify(modules)))
  isEditing.value = true
}

// 新增模块
const addModule = () => {
  modules.push({ title: `模块${modules.length + 1}`, content: '' })
}

// 自动保存草稿（示例用localStorage，可调整）
const setupAutoSave = () => {
  if (autoSaveTimer.value) {
    clearInterval(autoSaveTimer.value)
  }
  autoSaveTimer.value = setInterval(() => {
    if (isEditing.value) {
      const draftData = modules.map(module => ({
        title: module.title,
        content: module.content
      }))
      localStorage.setItem(`course_frame_draft_${props.course.id}`, JSON.stringify(draftData))
    }
  }, 10000)
}

// 加载草稿（示例用localStorage，可调整）
const loadDraft = () => {
  const draft = localStorage.getItem(`course_frame_draft_${props.course.id}`)
  if (draft) {
    try {
      const parsed = JSON.parse(draft)
      modules.splice(0, modules.length, ...parsed)
    } catch (e) {
      console.error('加载草稿失败:', e)
    }
  }
}

// 监听课程ID变化
watch(() => props.course.id, (newVal) => {
  if (newVal) {
    fetchCourseFrame()
    loadDraft()
    setupAutoSave()
  }
}, { immediate: true })

// 组件卸载前清理
onBeforeUnmount(() => {
  if (autoSaveTimer.value) {
    clearInterval(autoSaveTimer.value)
  }
  Object.values(quillInstances.value).forEach(instance => {
    if (instance) {
      instance.off('text-change')
    }
  })
})
</script>

<style scoped lang="scss">
.course-frame {
  padding: 16px;

  .edit-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 12px;
    border-bottom: 1px solid #eee;

    h2 {
      margin: 0;
      font-size: 18px;
      color: #333;
    }

    .action-buttons {
      display: flex;
      gap: 10px;
    }
  }
  // 新增模块按钮样式
  .add-module-btn {
    margin-bottom: 10px;
    background-color: #409eff;
    color: #fff;
    border: none;
    border-radius: 6px;
    &:hover {
      background-color: #66b1ff;
    }
    &:active {
      background-color: #3a8ee6;
    }
  }

  .course-module {
    margin-bottom: 24px;

    h3 {
      color: #666;
      margin-bottom: 8px;
      font-size: 16px;
      font-weight: normal;
    }

    .content {
      white-space: pre-wrap;
      line-height: 1.6;
      color: #333;
      padding: 8px 12px;
      background-color: #f9f9f9;
      border-radius: 4px;
      border: 1px solid #eee;

      &.ql-editor {
        min-height: 60px;
      }
    }
  }

  .edit-form {
    :deep(.el-form-item__label) {
      font-weight: bold;
      color: #666;
    }

    .form-module {
      margin-bottom: 20px;
    }

    .quill-container {
      min-width: 100%;
      overflow-x: auto;
      border: 1px solid #dcdfe6;
      border-radius: 4px;

      :deep(.ql-container) {
        min-height: 150px;
        max-width: 800px;
        border: none;
      }

      :deep(.ql-editor) {
        min-height: 150px;
        max-width: 800px;
        white-space: pre-wrap;
      }

      :deep(.ql-toolbar) {
        max-width: 800px;
        position: sticky;
        top: 0;
        background: white;
        z-index: 1;
        border: none;
        border-bottom: 1px solid #dcdfe6;
      }
    }

    @media (max-width: 768px) {
      :deep(.ql-toolbar) {
        position: static;
        flex-wrap: wrap;
      }

      :deep(.ql-container) {
        min-height: 200px;
      }
    }
  }
}
</style>