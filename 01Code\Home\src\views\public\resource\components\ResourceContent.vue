<template>
  <div class="resource-content-container">
    <component 
      :is="currentComponent"
      class="content-component"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import DigitalMedia from './resource-types/DigitalMedia.vue'
import VirtualSimulation from './resource-types/VirtualSimulation.vue'
import OpenEducation from './resource-types/OpenEducation.vue'
import GraduationWorks from './resource-types/GraduationWorks.vue'
import CulturalHeritage from './resource-types/CulturalHeritage.vue';
// 导入其他资源组件...

const props = defineProps({
  activeType: {
    type: String,
    required: true,
    default: 'digital-media',
    validator: (value) => [
      'digital-media',
      'virtual-simulation',
      'open-education',
      'course-management',
      'cultural-heritage',
      'graduation-works'
    ].includes(value)
  }
})

// 资源类型与组件映射
const componentMap = {
  'digital-media': DigitalMedia,
  'virtual-simulation': VirtualSimulation,
  'open-education': OpenEducation,
  'cultural-heritage':CulturalHeritage,
  'graduation-works': GraduationWorks,
  // 其他映射...
}

const currentComponent = computed(() => componentMap[props.activeType])
</script>

<style scoped lang="scss">
.resource-content-container {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  
  .content-component {
    padding: 20px;
  }

  scrollbar-width: none;
  -ms-overflow-style: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
}
</style>