// src/api/service.js
import axios from 'axios'

const request = axios.create({
    baseURL: 'http://8.134.236.247:1991/',
    timeout: 50000
})

request.interceptors.request.use(config => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers['Authorization'] = `Bearer ${token}`;
  }
  return config;
}, error => {
  return Promise.reject(error);
});

request.interceptors.response.use(
  response => {
    // 处理Blob响应
    if (response.config.responseType === 'blob') {
      return {
        data: response.data,
        headers: response.headers
      };
    }
    
    // 处理普通JSON响应
    let res = response.data;
    if (typeof res === 'string') {
      res = res ? JSON.parse(res) : res;
    }
    
    // 直接返回原始响应数据，不进行包装
    return res;
  },
  error => {
    console.log('err' + error);
    return Promise.reject(error);
  }
);

export default request;