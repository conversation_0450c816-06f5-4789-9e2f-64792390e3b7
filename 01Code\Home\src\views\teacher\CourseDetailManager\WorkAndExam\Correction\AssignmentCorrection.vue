
<!-- src\views\teacher\CourseDetailManager\WorkAndExam\Correction\ExamCorrection.vue -->
<template>
    <div class="correction-page">
        <!-- 返回按钮 -->
        <div class="nav-back">
            <el-button type="text" @click="router.back()">
                <el-icon>
                    <ArrowLeft />
                </el-icon> 返回
            </el-button>
        </div>

        <!-- 标题 & 时间 -->
        <div class="header">
            <h2>{{ title }}</h2>
            <div class="sub-info">
                <span v-if="isExam">考试时间：{{ startTime }} - {{ endTime }}</span>
                <span v-else>截止时间：{{ endTime }}</span>
                <span style="margin-left: 20px;">总分：{{ totalScore }} 分</span>
            </div>
        </div>

        <!-- 下载与设置 -->
        <div class="top-actions">
            <el-button type="primary" size="small">下载全部成绩</el-button>
            <el-dropdown>
                <el-icon style="cursor: pointer; font-size: 20px;">
                    <Setting />
                </el-icon>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item>公布成绩</el-dropdown-item>
                        <el-dropdown-item>公布答案</el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
        </div>

        <!-- 筛选栏 -->
        <div class="filter-bar">
            <el-checkbox v-model="filters.corrected">已批（{{ counts.corrected }}）</el-checkbox>
            <el-checkbox v-model="filters.uncorrected" style="margin-left: 12px;">未批（{{ counts.uncorrected }}）</el-checkbox>
            <el-checkbox v-model="filters.unsubmitted" style="margin-left: 12px;">未交（{{ counts.unsubmitted }}）</el-checkbox>

            <el-input v-model="searchKeyword" placeholder="搜索学生姓名" size="small" class="search-input" clearable>
                <template #suffix><el-icon>
                        <Search />
                    </el-icon></template>
            </el-input>
        </div>

        <!-- 学生列表 -->
        <el-table :data="filteredStudents" style="width: 100%" size="small">
            <el-table-column prop="name" label="学生" />
            <el-table-column prop="studentId" label="学号" />
            <el-table-column prop="className" label="班级" />
            <el-table-column prop="submitTime" label="提交时间" />
            <el-table-column label="总分">
                <template #default="scope">
                    <span v-if="scope.row.corrected">{{ scope.row.score }} 分</span>
                    <span v-else>未批</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="140">
                <template #default="scope">
                    <el-button v-if="!scope.row.submitted" type="primary" link @click="handleUrge(scope.row)">催交</el-button>
                    <el-button v-else type="primary" link @click="handleReview(scope.row)">开始批阅</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
  
<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ArrowLeft, Setting, Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getExamSubmitRecords, getAssignmentSubmitRecords } from '@/api/teacher/correction'

const route = useRoute()
const router = useRouter()

// 参数
const isExam = ref(route.query.isExam === 'true')
const courseId = route.params.courseId
const examOrAssignmentId = route.query.id
const classId = route.query.classId
const title = ref(route.query.title || '作业/考试')
const totalScore = ref(100)
const startTime = ref('')
const endTime = ref('')

// 数据列表
const students = ref([])

const counts = ref({
    corrected: 0,
    uncorrected: 0,
    unsubmitted: 0
})

const filters = ref({
    corrected: false,
    uncorrected: false,
    unsubmitted: false
})
const searchKeyword = ref('')

const filteredStudents = computed(() => {
    return students.value.filter((s) => {
        const matchName = !searchKeyword.value || s.name.includes(searchKeyword.value)
        const matchStatus =
            (!filters.value.corrected || s.corrected) &&
            (!filters.value.uncorrected || (s.submitted && !s.corrected)) &&
            (!filters.value.unsubmitted || !s.submitted)
        return matchName && matchStatus
    })
})

const fetchStudents = async () => {
    try {
        const res = isExam.value
            ? await getExamSubmitRecords({ examId: examOrAssignmentId, classId, page: 1, size: 100 })
            : await getAssignmentSubmitRecords({ assignmentId: examOrAssignmentId, classId, page: 1, size: 100 })


        const list = res.result?.records || []
        students.value = list.map((item) => ({
            name: item.studentName,
            studentId: item.studentNo,
            className: item.className,
            submitTime: item.submitTime ? formatTime(item.submitTime) : '',
            submitted: !!item.submitTime,
            corrected: item.corrected,
            score: item.score,
            submitRecordId: item.id
        }))

        counts.value = {
            corrected: students.value.filter(s => s.corrected).length,
            uncorrected: students.value.filter(s => s.submitted && !s.corrected).length,
            unsubmitted: students.value.filter(s => !s.submitted).length
        }

        // 设置时间
        startTime.value = formatTime(res.result?.startTime)
        endTime.value = formatTime(res.result?.endTime)
        totalScore.value = res.result?.totalScore ?? 100
    } catch (err) {
        ElMessage.error('加载学生提交记录失败')
    }
}

const formatTime = (timestamp) => {
    const date = new Date(timestamp)
    const y = date.getFullYear()
    const m = String(date.getMonth() + 1).padStart(2, '0')
    const d = String(date.getDate()).padStart(2, '0')
    const h = String(date.getHours()).padStart(2, '0')
    const min = String(date.getMinutes()).padStart(2, '0')
    return `${y}/${m}/${d} ${h}:${min}`
}

const handleUrge = (student) => {
    console.log('催交:', student)
}

const handleReview = (student) => {
    router.push({
        path: '/teacher/course-detail-manager/' + courseId + '/assignment-exam/correction/detail',
        query: {
            id: examOrAssignmentId,
            submitRecordId: student.submitRecordId,
            isExam: isExam.value,
            title: title.value,
            studentName: student.name
        }
    })
}

onMounted(fetchStudents)
</script>
  
<style scoped>
.correction-page {
    padding: 20px;
    background-color: #fff;
    min-height: 100vh;
}

.nav-back {
    margin-bottom: 10px;
}

.header {
    margin-bottom: 10px;
}

.sub-info {
    font-size: 14px;
    color: #666;
    margin-top: 4px;
}

.top-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 12px 0;
}

.filter-bar {
    display: flex;
    align-items: center;
    margin: 16px 0;
}

.search-input {
    width: 200px;
    margin-left: auto;
}
</style>