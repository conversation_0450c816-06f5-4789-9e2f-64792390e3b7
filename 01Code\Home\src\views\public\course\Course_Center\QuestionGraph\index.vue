<template>
  <div class="quetion-graph-wrapper">
    <div class="course-map-container">
      <QuetionGraph
        v-if="graphId"
        :graph-id="graphId"
        :course-title="courseTitle"
      />
      <div v-else class="no-graph">
        <p>该课程暂无问题图谱</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRoute } from "vue-router";
import QuetionGraph from "@/views/public/course/Course_Map/components/question/question-graph.vue";
import { getGraphsByCourse } from "@/api/public/course/courseMap/questionGraph.js";

const route = useRoute();
const graphId = ref(null);
const courseTitle = ref("问题图谱");

onMounted(async () => {
  try {
    const res = await getGraphsByCourse(route.params.courseId);
    if (res.code === 200 && res.result.length) {
      // 查找类型为1的问题图谱
      const kg = res.result.find((g) => g.graphType === 1);
      if (kg) {
        graphId.value = kg.graphId;
        courseTitle.value = kg.graphName || courseTitle.value;
      }
    }
  } catch (error) {
    console.error("获取问题图谱失败:", error);
  }
});
</script>

<style scoped>
.quetion-graph-wrapper {
  background: white;
  border-radius: 8px;
  padding: 0px;
  min-height: 70vh;
  height: calc(100vh);
  z-index: 999;
  overflow: hidden;
  margin: -10px;
}

.course-map-container {
  padding: 20px;
  height: 100%;
}

.no-graph {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  color: #888;
}
</style>
