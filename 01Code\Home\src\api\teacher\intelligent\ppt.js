import axios from 'axios';
import { getKey } from '../../key.js';
import CryptoJS from 'crypto-js';

// 获取正确的密钥
const appId = getKey('xunfei_appid');
const secret = getKey('xunfei');

function getSignature(appId, secret, timestamp) {
  const md5 = (str) => CryptoJS.MD5(str).toString();
  const hmacSHA1 = (key, str) => {
    const hmac = CryptoJS.HmacSHA1(str, key);
    return CryptoJS.enc.Base64.stringify(hmac);
  };
  return hmacSHA1(secret, md5(appId + timestamp));
}

export async function generatePpt(requestParams) {
  const formData = new FormData();
  const timestamp = Math.floor(Date.now() / 1000);
  const signature = getSignature(appId, secret, timestamp);

  // 调试信息
  console.log('Request payload:', {
    appId,
    timestamp,
    signature,
    params: requestParams
  });

  // 添加请求参数
  formData.append('query', requestParams.query);
  if (requestParams.language) formData.append('language', requestParams.language);
  if (requestParams.isFigure) formData.append('isFigure', requestParams.isFigure);
  if (requestParams.aiImage) formData.append('aiImage', requestParams.aiImage);
  if (requestParams.isCardNote) formData.append('isCardNote', requestParams.isCardNote);

  try {
    const response = await axios.post('/ppt-api/create', formData, {
      headers: {
        'appId': appId,
        'timestamp': timestamp.toString(),
        'signature': signature,
        // 让浏览器自动设置Content-Type
      },
      timeout: 60000
    });

    if (!response.data.flag) {
      throw new Error(response.data.desc || 'API返回失败状态');
    }
    return response.data.data;
  } catch (error) {
    console.error('API请求详细错误:', {
      error,
      response: error.response?.data,
      status: error.response?.status
    });
    throw new Error(`生成PPT失败: ${error.response?.data?.desc || error.message}`);
  }
}
