# 数媒资源管理模块开发文档

## 项目概述

数媒资源管理模块是一个综合性的教育资源展示和管理平台，为用户提供数字媒体相关的各类资源，包括通用资源、虚拟仿真、开放教育、非遗传承、毕业作品等。该模块采用Vue 3 + Composition API开发，具有响应式设计和良好的用户体验。

## 目录结构

```
src/views/public/resource/
├── index.vue                    # 主入口页面
├── components/                  # 组件目录
│   ├── LeftNav.vue             # 左侧导航组件
│   ├── ResourceContent.vue     # 资源内容容器组件
│   ├── StatCard.vue            # 统计卡片组件
│   ├── ResourceCategoryCard.vue # 资源分类卡片组件
│   ├── ProjectNav.vue          # 项目导航组件
│   ├── GraduationNav.vue       # 毕业作品导航组件
│   ├── resource-types/         # 资源类型组件
│   │   ├── DigitalMedia.vue    # 数媒资源组件
│   │   ├── VirtualSimulation.vue # 虚拟仿真组件
│   │   ├── OpenEducation.vue   # 开放教育组件
│   │   ├── CulturalHeritage.vue # 非遗传承组件
│   │   └── GraduationWorks.vue # 毕业作品组件
│   ├── graduation-content/     # 毕业作品内容组件
│   │   ├── ProjectIntroduction.vue # 项目介绍组件
│   │   ├── ThesisPaper.vue     # 论文组件
│   │   └── ProjectGallery.vue  # 项目画廊组件
│   └── heritage-content/       # 非遗内容组件
│       ├── ImageCarousel.vue   # 图片轮播组件
│       └── InfoSection.vue     # 信息展示组件
└── pages/                      # 页面组件
    ├── VirtualProject.vue      # 虚拟项目页面
    ├── HeritageDetailPage.vue  # 非遗详情页面
    └── GraduationProjects.vue  # 毕业项目页面
```

## 核心页面功能

### 1. 主入口页面 (index.vue)

**功能描述：**
- 作为资源管理模块的主入口，提供统一的页面布局
- 集成左侧导航和右侧内容区域
- 实现响应式设计，适配不同屏幕尺寸

**页面设计：**
- 采用左右分栏布局，左侧为导航菜单，右侧为内容展示区
- 使用渐变背景图片增强视觉效果
- 主内容区域采用白色背景，圆角设计提升用户体验
- 导航侧边栏固定宽度，内容区域自适应

**数据连接：**
- 通过`currentTab`响应式变量管理当前激活的标签页
- 通过`handleNavigationClick`方法处理导航切换
- 使用组件动态渲染不同资源类型的内容

### 2. 左侧导航组件 (LeftNav.vue)

**功能描述：**
- 提供资源分类导航功能
- 支持6个主要资源类型的快速切换
- 实现导航项的激活状态管理

**页面设计：**
- 采用垂直列表布局，每个导航项为可点击的卡片
- 激活状态使用紫色主题色(#8a6de3)突出显示
- 悬停效果提供良好的交互反馈
- 固定定位确保在页面滚动时保持可见

**数据连接：**
- 导航数据通过`navItems`数组静态定义
- 通过`emit('nav-click')`向父组件传递导航事件
- 使用`activeIndex`管理当前激活的导航项

### 3. 资源内容容器 (ResourceContent.vue)

**功能描述：**
- 作为动态组件容器，根据当前选中的资源类型渲染对应组件
- 提供统一的滚动和样式管理
- 支持组件的动态切换和缓存

**页面设计：**
- 采用全宽容器设计，支持内容滚动
- 隐藏滚动条，提供更清洁的视觉效果
- 内容区域添加内边距，确保内容不会贴边

**数据连接：**
- 通过`componentMap`对象映射资源类型到对应组件
- 使用`computed`属性动态计算当前应该渲染的组件
- 支持props验证，确保传入的`activeType`有效

## 资源类型组件

### 1. 数媒资源组件 (DigitalMedia.vue)

**功能描述：**
- 展示资源通用集合中心
- 提供课程资料管理及共享功能
- 展示虚拟仿真资源库

**页面设计：**
- 采用网格布局，支持3-4列响应式展示
- 每个资源卡片包含标题、描述和操作按钮
- 使用卡片悬停效果增强交互体验
- 支持加载状态、错误状态和无数据状态的展示

**数据连接：**
```javascript
// API接口调用
import { getGeneralResourceCategoryList, getGeneralResourceProblemList, getGeneralResourceSoftwareList } from '@/api/public/resource/generalResource.js'

// 数据获取方法
const fetchCategories = async () => {
  const response = await getGeneralResourceCategoryList({pageNum: 1, pageSize: 20})
  categories.value = response.result.records || []
}
```

**主要功能：**
- 资源分类展示：显示各类数字媒体资源分类
- 课程资料管理：展示课程相关文档和资料
- 虚拟仿真软件：提供软件下载和介绍
- 错误处理和重试机制

### 2. 毕业作品组件 (GraduationWorks.vue)

**功能描述：**
- 展示毕业设计作品列表
- 提供搜索和筛选功能
- 支持作品详情查看和下载

**页面设计：**
- 顶部搜索和筛选区域
- 卡片网格布局展示作品
- 每个卡片包含封面图片、作品信息和下载按钮
- 分页控件支持大量数据浏览

**数据连接：**
```javascript
// API接口调用
import { getGraduationWorksList } from '@/api/public/resource/graduation'

// 搜索和分页参数
const searchParams = reactive({
  name: '',
  graduationYear: ''
})

const pagination = reactive({
  current: 1,
  size: 12,
  total: 0,
  pages: 1
})
```

**主要功能：**
- 作品搜索：支持按名称搜索
- 年份筛选：按毕业年份筛选作品
- 分页浏览：支持大量作品的分页展示
- 作品下载：提供作品文件下载功能
- 详情查看：点击卡片跳转到作品详情页

### 3. 非遗传承组件 (CulturalHeritage.vue)

**功能描述：**
- 展示本地非遗文化资料
- 提供在线非遗资源链接
- 支持搜索和排序功能

**页面设计：**
- 分为两个主要区域：非遗文化和了解更多
- 使用卡片网格和图标网格两种展示方式
- 支持图片背景的卡片设计
- 提供排序和筛选控件

**数据连接：**
```javascript
// API接口调用
import { getLocalHeritageList, getOnlineHeritageList } from '@/api/public/resource/heritage'

// 本地和在线数据分别管理
const localCards = ref([])
const onlineIcons = ref([])
const localLoading = ref(false)
const onlineLoading = ref(false)
```

**主要功能：**
- 本地非遗展示：展示系统内的非遗文化资料
- 在线资源链接：提供外部非遗资源的快速访问
- 搜索功能：支持按名称搜索非遗资料
- 排序功能：支持按名称和时间排序
- 分页浏览：支持大量数据的分页展示

## 详情页面

### 1. 毕业项目详情页 (GraduationProjects.vue)

**功能描述：**
- 展示毕业设计作品的详细信息
- 提供项目介绍、论文、图片、视频等多个内容模块
- 支持内容导航和切换

**页面设计：**
- 分为项目概览和详细内容两个主要区域
- 左侧导航按钮，右侧内容展示
- 支持多种内容类型的展示（文本、图片、视频）
- 响应式布局适配不同屏幕

**数据连接：**
```javascript
// 获取项目详情
const fetchProjectDetail = async () => {
  const response = await getGraduationWorkDetail(route.params.id)
  projectData.value = response.result
}

// 获取项目图片
const fetchProjectImages = async () => {
  const response = await getGraduationImageList(route.params.id)
  imageList.value = response.result || []
}

// 获取项目视频
const fetchProjectVideos = async () => {
  const response = await getGraduationVideoList(route.params.id)
  videoList.value = response.result || []
}
```

**主要功能：**
- 项目概览：展示项目基本信息和封面
- 项目介绍：详细的项目描述和背景
- 毕设论文：论文下载和内容展示
- 项目图片：图片画廊展示
- 项目视频：视频播放功能

### 2. 非遗详情页 (HeritageDetailPage.vue)

**功能描述：**
- 展示非遗文化的详细信息
- 提供图片轮播和信息展示
- 支持相关资源的链接

**页面设计：**
- 采用图片轮播和信息展示的组合布局
- 使用组件化设计，便于维护和扩展
- 响应式设计适配移动端

## API接口设计

### 1. 通用资源接口 (generalResource.js)

**接口列表：**
- `getGeneralResourceCategoryList`: 获取资源分类列表
- `getGeneralResourceProblemList`: 获取课程资料列表
- `getGeneralResourceSoftwareList`: 获取虚拟仿真软件列表

**参数设计：**
```javascript
// 通用分页参数
{
  pageNum: 1,      // 页码
  pageSize: 10,    // 每页数量
  name: '',        // 搜索名称（可选）
  categoryId: ''   // 分类ID（可选）
}
```

### 2. 毕业作品接口 (graduation.js)

**接口列表：**
- `getGraduationWorksList`: 获取毕业作品列表
- `getGraduationWorkDetail`: 获取作品详情
- `getGraduationImageList`: 获取作品图片列表
- `getGraduationVideoList`: 获取作品视频列表

**参数设计：**
```javascript
// 搜索参数
{
  pageNum: 1,           // 页码
  pageSize: 12,         // 每页数量
  name: '',             // 作品名称
  graduationYear: ''    // 毕业年份
}
```

### 3. 非遗接口 (heritage.js)

**接口列表：**
- `getLocalHeritageList`: 获取本地非遗列表
- `getOnlineHeritageList`: 获取在线非遗列表
- `getHeritageDetail`: 获取非遗详情
- `getHeritageImageList`: 获取非遗图片列表

**参数设计：**
```javascript
// 搜索和排序参数
{
  pageNum: 1,        // 页码
  pageSize: 10,      // 每页数量
  name: '',          // 非遗名称
  sortType: 'ascending',    // 排序类型
  sortColumn: ''     // 排序字段
}
```

## 样式设计

### 1. 主题色彩
- 主色调：#8a6de3 (紫色)
- 背景色：白色和浅灰色
- 文字色：#333 (深灰) 和 #666 (中灰)

### 2. 布局设计
- 采用Flexbox和Grid布局
- 响应式设计，支持不同屏幕尺寸
- 卡片式设计，提升视觉层次

### 3. 交互效果
- 悬停效果：卡片上浮和阴影变化
- 过渡动画：使用CSS transition实现平滑过渡
- 加载状态：提供加载动画和错误处理

## 技术特点

### 1. 组件化设计
- 高度模块化的组件结构
- 可复用的组件设计
- 清晰的组件职责分离

### 2. 响应式数据管理
- 使用Vue 3 Composition API
- 响应式数据绑定
- 统一的状态管理

### 3. 错误处理机制
- 完善的错误捕获和处理
- 用户友好的错误提示
- 重试机制支持

### 4. 性能优化
- 组件懒加载
- 图片懒加载
- 分页加载减少初始加载时间

## 开发总结

该数媒资源管理模块成功实现了以下目标：

1. **功能完整性**：涵盖了数字媒体相关的各类资源展示和管理
2. **用户体验**：采用现代化的UI设计，提供良好的交互体验
3. **技术先进性**：使用Vue 3最新特性，代码结构清晰
4. **可维护性**：组件化设计，便于后续维护和扩展
5. **数据连接**：完善的API接口设计，支持前后端数据交互

该模块为数字媒体专业的学生和教师提供了丰富的学习资源，有效促进了教学资源的共享和利用。 