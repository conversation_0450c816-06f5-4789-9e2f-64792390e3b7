<template>
  <div class="stats-header">
    <h2>课程数据统计</h2>
    <div class="course-info">
      <div class="course-name">{{ courseInfo.name || '加载中...' }}</div>
      <div class="course-meta">
        <span v-if="courseInfo.major">专业: {{ courseInfo.major }}</span>
        <span v-if="courseInfo.subjectCategory">学科: {{ courseInfo.subjectCategory }}</span>
      </div>
      <div class="course-id">课程ID: {{ courseId }}</div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  courseInfo: {
    type: Object,
    required: true
  },
  courseId: {
    type: [String, Number],
    required: true
  }
})
</script>

<style scoped>
.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}
.stats-header h2 {
  margin: 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}
.course-info {
  text-align: right;
}
.course-name {
  font-size: 16px;
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 8px;
}
.course-meta {
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}
.course-meta span {
  margin-left: 16px;
}
.course-meta span:first-child {
  margin-left: 0;
}
.course-id {
  color: #909399;
  font-size: 14px;
  background: #f0f2f5;
  padding: 8px 16px;
  border-radius: 20px;
}
@media (max-width: 768px) {
  .stats-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  .course-info {
    text-align: center;
  }
  .course-meta span {
    display: block;
    margin: 4px 0;
  }
}
</style> 