<template>
  <div class="assistant-container">
    <template v-if="!hasInteraction">
      <div class="top">
        <img src="../../assets/photo/AI.png" alt="AI图标" class="assistant-icon">
        <h1>Hi, 我是灵犀</h1>
        <p>你有什么要问我的吗😊</p>
      </div>

      <div class="function-tools">
        <h2>猜你想问</h2>
        <div class="tools-container">
          <div v-for="(question, qIndex) in presetQuestions" :key="qIndex" class="tool-card" @click="selectQuestion(question)">
            <p class="question-item">{{ question }}</p>
          </div>
        </div>
      </div>
    </template>

    <!-- 聊天界面 -->
    <template v-else>
      <div class="chat-history">
        <div v-for="(msg, index) in messages" :key="index" :class="['message', msg.role]">
          <div class="message-bubble" :class="{ 'streaming': msg.isStreaming }">
            {{ msg.content }}
          </div>
        </div>
      </div>
    </template>

    <!-- 始终显示的输入区域 -->
    <div class="search-section">
      <div class="search-tags">
        <button @click="toggleDeepThinking">深度思考 {{ deepThinking ? '✅' : '' }}</button>
        <button @click="createNewChat">创建新对话</button>
      </div>
      <div class="input-container">
        <textarea v-model="inputMessage" placeholder="输入你的问题..." @keyup.enter.prevent="handleSend" :disabled="isGenerating"></textarea>
        <button @click="handleSend" class="generate-btn" :disabled="isGenerating">
          <span v-if="isGenerating" class="loading-dots">···</span>
          <span v-else class="arrow-icon">⮞</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import {reactive, toRefs, nextTick} from 'vue';
import {createChatCompletion} from '../../../../api/student/intelligent/dialog.js';

export default {
  setup() {
    const MAX_TOKEN_LIMIT = 400;

    const state = reactive({
      inputMessage: '',
      isGenerating: false,
      deepThinking: false,
      hasInteraction: false,
      presetQuestions: [
        '安卓开发整个流程分哪几部分？',
        '解读一下近期教育政策改革',
        '计算机网络编程的知识图谱研究',
        '一天中哪一个时间段状态最好'
      ],
      messages: []
    });

    const handleSend = async () => {
      if (!state.inputMessage.trim() || state.isGenerating) return;

      try {
        state.isGenerating = true;
        state.hasInteraction = true;

        // 添加用户消息
        const userMessage = state.inputMessage.trim();
        state.messages.push({
          role: 'user',
          content: userMessage,
          isStreaming: false
        });
        state.inputMessage = '';

        // 添加初始助手消息
        state.messages.push({
          role: 'assistant',
          content: '',
          isStreaming: true
        });

        // 构造带字数限制的上下文
        const maxTokens = state.deepThinking ? MAX_TOKEN_LIMIT : 1000;
        const chatContext = [
          {
            role: 'system',
            content: `请将回答严格控制在${maxTokens}字以内，保持简洁明了。`
          },
          ...state.messages
            .filter(m => !m.isStreaming && m.role !== 'system')
            .map(({role, content}) => ({role, content}))
        ];

        await createChatCompletion(
          chatContext,
          maxTokens,
          (contentChunk) => {
            const lastIndex = state.messages.length - 1;
            state.messages[lastIndex].content += contentChunk;
            nextTick(scrollToBottom);
          }
        );

        // 标记流结束
        const lastIndex = state.messages.length - 1;
        state.messages[lastIndex].isStreaming = false;

      } catch (error) {
        console.error('API请求失败:', error);
        const lastIndex = state.messages.length - 1;
        state.messages[lastIndex].content = error.response?.data?.message
          || '服务暂时不可用，请稍后重试';
      } finally {
        state.isGenerating = false;
        scrollToBottom();
      }
    };

    const scrollToBottom = () => {
      const container = document.querySelector('.chat-history');
      if (container) {
        container.scrollTo({
          top: container.scrollHeight,
          behavior: 'smooth'
        });
      }
    };

    const selectQuestion = (question) => {
      state.inputMessage = question;
      handleSend();
    };

    const createNewChat = () => {
      state.messages = [];
      state.hasInteraction = false;
    };

    const toggleDeepThinking = () => {
      state.deepThinking = !state.deepThinking;
    };

    return {
      ...toRefs(state),
      handleSend,
      selectQuestion,
      createNewChat,
      toggleDeepThinking
    };
  }
};
</script>

<style scoped>
.assistant-container {
  border-radius: 18px;
  display: flex;
  flex-direction: column;
  height: 75vh;
  max-width: 1200px;
  margin: 0 auto;
  padding: 10px;
  background: #ffffff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: relative;
}

.top {
  text-align: center;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.assistant-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin-bottom: 5px;
}

.function-tools {
  flex: 1;
  padding: 20px;
}

.tools-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 10px;
}

.tool-card {
  padding: 15px;
  background: white;
  border-radius: 12px;
  cursor: pointer;
  transition: transform 0.2s;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.tool-card:hover {
  transform: translateY(-3px);
}

.chat-history {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 20px;
}

.message {
  margin: 15px 0;
  display: flex;
}

.message-bubble {
  max-width: 70%;
  padding: 12px 18px;
  border-radius: 18px;
  line-height: 1.5;
  word-break: break-word;
  position: relative;
  overflow: hidden;
}

.message.user .message-bubble {
  background: #956ef6;
  color: white;
  margin-left: auto;
  border-radius: 18px 18px 0 18px;
}

.message.assistant .message-bubble {
  background: #f0f0f0;
  margin-right: auto;
  border-radius: 18px 18px 18px 0;
}

.message.assistant .message-bubble.streaming {
  border-right: 2px solid #666;
  animation: typing 1s steps(20, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from {
    width: 0
  }
  to {
    width: 100%
  }
}

@keyframes blink-caret {
  from, to {
    border-color: transparent
  }
  50% {
    border-color: #666
  }
}

.search-section {
  margin-top: auto;
  padding-top: 10px;
  border-top: 1px solid #eee;
  background: white;
}

.input-container {
  position: relative;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
}

textarea {
  width: 100%;
  height: 100px;
  padding: 15px 20px;
  border: 2px solid #eee;
  border-radius: 12px;
  resize: none;
  font-size: 16px;
  transition: border-color 0.3s;
  background-color: #fafafa;
}

textarea:focus {
  border-color: #956ef6;
  outline: none;
}

.generate-btn {
  position: absolute;
  right: 15px;
  bottom: 15px;
  width: 45px;
  height: 45px;
  background: #956ef6;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s;
}

.generate-btn:hover {
  background-color: #d2c8ea;
}

.arrow-icon {
  color: white;
  font-size: 24px;
}

.loading-dots {
  color: white;
  font-size: 24px;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.search-tags {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.search-tags button {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 20px;
  background: white;
  cursor: pointer;
  transition: all 0.3s;
}

.search-tags button:hover {
  background: #f5f5f5;
}
</style>
