<!-- src/views/student/CourseDetail/components/CourseSidebar.vue -->
<template>
  <div class="sidebar">
    <div class="sidebar-header">
      <div class="header-image">
        <img :src="courseData.cover" :alt="courseData.name">
        <span class="header-overlay-text" @click="goBackToMyCourses">返回我的课程</span>
      </div>
      <div class="header-content">
        <div class="header-text">
          <h2 class="course-title">{{ courseData.name }}</h2>

        </div>
        <div class="header-orther">
          <p class="major-name">{{ courseData.major }}</p>
           <button class="exit-course-btn" @click="exitCourse">退出课程</button>
        </div>
      </div>
    </div>
    
    <ul class="sidebar-menu">
      <li 
        v-for="(menuItem, index) in menuItems" 
        :key="index" 
        class="menu-item"
        :class="{ active: activeIndex === index }"
        @click="handleMenuItemClick(index)"
      >
        <div class="menu-icon-wrapper">
          <img 
            :src="getIconUrl(menuItem, index)" 
            :alt="menuItem.text" 
            class="menu-icon"
            @error="handleIconError"
          >
        </div>
        <span class="menu-text">{{ menuItem.text }}</span>
      </li>
    </ul>

     
  <div class="join-class-btn-wrapper">
    <!-- 根据班级状态显示不同的按钮或信息 -->
    <template v-if="currentClassStatus === 0">
      <!-- 已邀请状态，根据需求显示 -->
      <button class="join-class-btn" @click="handleInvitation">接受邀请</button>
    </template>
    <template v-else-if="currentClassStatus === 1">
      <div class="class-status-info pending">
        申请加入{{ currentClassName }}，正在审核
      </div>
    </template>
    <template v-else-if="currentClassStatus === 2">
      <div class="class-status-info joined">
        所在班级：{{ currentClassName }}
      </div>
    </template>
    <template v-else>
      <button class="join-class-btn" @click="showJoinClassDialog = true">加入班级</button>
    </template>
  </div>

    <!-- 加入班级弹窗 -->
    <JoinClassDialog
      :visible="showJoinClassDialog"
      @update:visible="showJoinClassDialog = $event"
      @confirmJoin="handleJoinClass"
    />

      <!-- 确认对话框 -->
    <ConfirmDialog
      v-model:visible="showConfirmDialog"
      title="退出课程"
      message="确定要退出该课程吗？退出后将无法访问课程内容。"
      @confirm="confirmExitCourse"
    />
    
    <!-- 通知组件 -->
    <CustomNotification
      v-if="showNotification"
      :message="notificationMessage"
      :type="notificationType"
      @close="showNotification = false"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { getCourseDetail, exitStudentCourse,joinClass,getStudentClasses    } from '@/api/student/course'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import CustomNotification from '@/components/CustomNotification.vue'
import JoinClassDialog from './JoinClassDialog.vue';

const router = useRouter()
const route = useRoute()
const courseId = route.params.courseId
const showJoinClassDialog = ref(false);
const courseData = ref({
  name: '加载中...',
  cover: defaultCourseCover,
  major: '加载中...'
})
// 对话框和通知状态
const showConfirmDialog = ref(false)
const showNotification = ref(false)
const notificationMessage = ref('')
const notificationType = ref('success')
const menuItems = ref([
  { text: '课程中心', routeName: 'course-detail', icon: 'icon-courseCenter' },
  { text: '学习任务', routeName: 'S-Task', icon: 'icon-task' },
  { text: '学习公告', routeName: 'S-Announcement', icon: 'icon-announce' },
  { text: '测验与作业', routeName: 'S-ExamAndWork', icon: 'icon-work' },
  { text: '成绩分析', routeName: 'S-Grade', icon: 'icon-grade' }
])
const activeIndex = ref(1)

// 添加班级相关状态
const studentClasses = ref([])
const currentClassStatus = ref(null) // 0:已邀请; 1:待审核; 2:已通过; 3:已拒绝/已移除
const currentClassName = ref('')
const currentClassId = ref('')


const getIconPath = (iconName) => {
  return `/src/assets/img/Student/${iconName}.png`;
};

const getIconUrl = (menuItem, index) => {
  const isActive = activeIndex.value === index;
  const iconState = isActive ? 'actived' : 'normal';
  return getIconPath(`${menuItem.icon}-${iconState}`);
};

// 处理菜单项点击
const handleMenuItemClick = (index) => {
  activeIndex.value = index
  const menuItem = menuItems.value[index]
  
  if (menuItem.routeName === 'course-detail') {
    // 特殊处理课程中心，跳转到公共课程详情页
    router.push({ 
      name: menuItem.routeName, 
      params: { courseId },
    })
  } else {
    router.push({ name: menuItem.routeName, params: { courseId } })
  }
}

// 根据路由设置激活状态
const setActiveStateFromRoute = () => {
  const currentRouteName = route.name
  // 特殊处理课程中心路由
  if (currentRouteName === 'course-detail') {
    activeIndex.value = 0 // 课程中心是第一个菜单项
    return
  }
  
  const index = menuItems.value.findIndex(item => item.routeName === currentRouteName)
  if (index !== -1) {
    activeIndex.value = index
  }
}

// 获取课程详情
const fetchCourseDetail = async () => {
  try {
    const response = await getCourseDetail(courseId)
    if (response.code === 200) {
      courseData.value = {
        name: response.result.name || '未命名课程',
        cover: response.result.courseCover || defaultCourseCover,
        major: response.result.major || '未知专业'
      }
    } else {
      console.error('获取课程详情失败:', response.msg)
    }
  } catch (error) {
    console.error('获取课程详情失败:', error)
  }
}

const goBackToMyCourses = () => {
  router.push({ name: 'StudentClass' });
};

const exitCourse = () => {
  showConfirmDialog.value = true
}

// 确认退出课程
const confirmExitCourse = async () => {
  try {
    const response = await exitStudentCourse(courseId)
    
    if (response.code === 200) {  // 修改判断条件为200
      notificationMessage.value = response.msg || '已成功退出课程' // 使用后端返回的消息
      notificationType.value = 'success'
      showNotification.value = true
      
      // 1秒后跳转到我的课程页面
      setTimeout(() => {
        router.push({ name: 'StudentClass' });
      }, 1000)
    } else {
      notificationMessage.value = response.msg || '退出课程失败'
      notificationType.value = 'error'
      showNotification.value = true
    }
  } catch (error) {
    console.error('退出课程失败:', error)
    notificationMessage.value = '退出课程失败，请重试'
    notificationType.value = 'error'
    showNotification.value = true
  }
}

//跳转到课程中心
const handleJoinClass = async (invitationCode) => {
  try {
    const response = await joinClass(invitationCode);
    if (response.code === 200) {
      notificationMessage.value = '申请成功，等待审核';
      notificationType.value = 'success';
      showNotification.value = true;
    } else {
      notificationMessage.value = response.msg || '加入班级失败，请重试';
      notificationType.value = 'error';
      showNotification.value = true;
    }
  } catch (error) {
    console.error('加入班级失败:', error);
    notificationMessage.value = '加入班级失败，请重试';
    notificationType.value = 'error';
    showNotification.value = true;
  }
};

// 获取学生班级列表
const fetchStudentClasses = async () => {
  try {
    const response = await getStudentClasses()
    if (response.code === 200) {
      studentClasses.value = response.result || []
      
      // 查找与当前课程相关的班级
      const relatedClass = studentClasses.value.find(
        cls => cls.courseId === courseId
      )
      
      if (relatedClass) {
        currentClassStatus.value = relatedClass.status
        currentClassName.value = relatedClass.className
        currentClassId.value = relatedClass.classId
      } else {
        currentClassStatus.value = null // 没有相关班级
      }
    }
  } catch (error) {
    console.error('获取班级列表失败:', error)
    currentClassStatus.value = null
  }
}
// 处理接受邀请
const handleInvitation = async () => {
  try {
    // 这里需要调用接受邀请的API
    // const response = await acceptClassInvitation(currentClassId.value)
    // if (response.code === 200) {
    //   fetchStudentClasses() // 刷新状态
    // }
    notificationMessage.value = '功能开发中，请联系老师'
    notificationType.value = 'info'
    showNotification.value = true
  } catch (error) {
    console.error('接受邀请失败:', error)
    notificationMessage.value = '操作失败，请重试'
    notificationType.value = 'error'
    showNotification.value = true
  }
}

onMounted(() => {
  fetchCourseDetail()
  setActiveStateFromRoute()
  fetchStudentClasses()
})
</script>

<style lang="scss" scoped>
@use '@/styles/variables';
// 定义变量

.sidebar {
  position: relative; /* 确保创建堆叠上下文 */
  z-index: 100; /* 高于内容区域 */
  width: 230px; /* 固定宽度 */
  background-color: #fff;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
  font-family: Arial, sans-serif;

  .sidebar-header {
    padding: 10px;
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;

    .header-image {
      position: relative;
      width: 180px;
      height: 120px;
      background-color: #af8989;
      border-radius: 4px;
      margin: 0 auto;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 4px;
      }
    }

    .header-overlay-text {
      position: absolute;
      top: 0;
      left: 0;
      color: $primary-color;
      font-size: 14px;
      cursor: pointer;
      z-index: 1;
      background-color: rgba(255, 255, 255, 0.8);
      padding: 2px 6px;
      border-radius: 0 0 3px 0;
    }

    .header-text {
      display: flex;
      flex-direction: column;
      margin-left: 10px;
      margin: 0 auto;

      .course-title {
        font-size: 16px;
        color: #333;
        margin: 0;
        font-weight: bold;
      }

    }



    .header-content {
      width: 100%;
      text-align: center;
      margin-top: 10px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .header-orther {
      display: flex;
      flex-direction: row;
      align-items: center;  // 垂直居中
      justify-content: center;  // 水平居中
      gap: 10px;  // 添加间距
      width: 100%;  // 确保宽度填满容器
      margin-top: 10px;
      
      .major-name {
        font-size: 12px;
        color: #666;
        margin: 0;  // 移除原有的margin-top
        flex-shrink: 0;  // 防止文本压缩
      }
      
      .exit-course-btn {
        margin: 0;  // 移除原有的margin-top
        padding: 2px 6px;
        background-color: #fff;
        border: 1px solid $primary-color;
        color: $primary-color;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;
        font-size: 12px;
        flex-shrink: 0;  // 防止按钮压缩
        
        &:hover {
          background-color: $primary-color;
          color: white;
        }
      }
    }
  }

  .sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;

    .menu-item {
      padding: 20px 40px 20px 50px;
      cursor: pointer;
      display: flex;
      align-items: center;
      position: relative;
      transition: background-color 0.2s;

      .menu-icon-wrapper {
        width: 24px;
        height: 24px;
        margin-right: 16px;
        display: flex;
        align-items: center;
        justify-content: center;

        .menu-icon {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }

      .menu-text {
        flex-grow: 1;
        display: inline-block;
        color: #333;
        font-size: 16px;
      }

      .badge {
        margin-left: 8px;
        background-color: $primary-color;
        color: #fff;
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 12px;
        line-height: 1;
      }

      // 鼠标悬停状态
      &:hover {
        background-color: $course-tabs;

        .menu-text {
          color: $primary-color;
        }
      }

      // 激活状态
      &.active {
        background-color: $course-tabs;
        border-left: 3px solid $primary-color;

        .menu-text {
          color: $primary-color;
          font-weight: 500;
        }
      }
    }
  }
}

.join-class-btn-wrapper {
  text-align: center;
  margin: 20px 0;

  .join-class-btn {
    padding: 8px 16px;
    background-color: #1890ff;
    color: #fff;
    border: 1px solid #1890ff;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;

    &:hover {
      background-color: #40a9ff;
      border-color: #40a9ff;
    }
  }
}

.class-status-info {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
  
  &.pending {
    background-color: #fff7e6;
    color: #fa8c16;
    border: 1px solid #ffd591;
  }
  
  &.joined {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
  }
}
</style>