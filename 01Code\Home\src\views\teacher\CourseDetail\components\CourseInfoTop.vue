<template>
  <div class="course-info">
    <div class="course-info-left">
      <div class="title">
        <div class="course-name">{{ course.name || '--' }}</div>
        <div class="course-term">{{ course.term || '--' }}</div>
      </div>
      <div class="course-base">
        <div class="part">
          <img src="@/assets/img/Teacher/icon-teacherName.png" alt="图标" class="icon">
          <span class="part-title">课程负责人</span>
          <span class="part-content">{{ course.teacher || '--' }}</span>
        </div>
        <div class="part">
          <img src="@/assets/img/Teacher/icon-score.png" alt="图标" class="icon">
          <span class="part-title">学分</span>
          <span class="part-content">{{ course.score || '--' }}</span>
        </div>
        <div class="part">
          <img src="@/assets/img/Teacher/icon-time.png" alt="图标" class="icon">
          <span class="part-title">学时</span>
          <span class="part-content">{{ course.time || '--' }}</span>
        </div>
      </div>
      <div class="course-base">
        <div class="part">
          <span class="part-title">学校:</span>
          <span class="part-content">{{ course.school || '--' }}</span>
        </div>
        <div class="part">
          <span class="part-title">学院：</span>
          <span class="part-content">{{ course.department || '--' }}</span>
        </div>
        <div class="part">
          <span class="part-title">学科：</span>
          <span class="part-content">{{ course.major || '--' }}</span>
        </div>
      </div>
      <div class="edit-btn-container">
        <el-button type="primary" size="small" @click="openEditDialog">编辑课程信息</el-button>
      </div>
    </div>
    <div class="course-info-right">
      <img 
        :src="courseCover" 
        alt="课程封面"
        class="course-cover"
        referrerpolicy="no-referrer"
        @error="fallbackTodefaultCourseCover">
      <div class="upload-trailer-btn" @click="openFilePicker">
        <img src="@/assets/img/Teacher/icon-upload.png" alt="上传" class="upload-icon">
        <span>上传片花</span>
      </div>
      <input type="file" accept="image/*" ref="fileInput" @change="handleFileChange" style="display: none;">
    </div>

    <el-dialog
      v-model="editDialogVisible"
      title="编辑课程信息"
      width="50%"
      :before-close="handleClose">
      <el-form :model="editForm" label-width="120px" ref="editFormRef">
        <el-form-item label="课程名称" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入课程名称" />
        </el-form-item>
        
        <el-form-item label="所属学校" prop="school">
          <el-input v-model="editForm.school" disabled />
        </el-form-item>
        
        <el-form-item label="所属学院" prop="department">
          <el-input v-model="editForm.department" disabled />
        </el-form-item>
        
        <el-form-item label="所属学期" prop="semester">
          <el-select v-model="editForm.semester" placeholder="请选择学期" style="width: 100%">
            <el-option
              v-for="item in semesterOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="课程类型" prop="courseType">
          <el-select v-model="editForm.courseType" placeholder="请选择课程类型" style="width: 100%">
            <el-option
              v-for="item in courseTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="课程学分" prop="credits">
          <el-input-number v-model="editForm.credits" :min="0" :max="10" :step="0.5" />
        </el-form-item>
        
        <el-form-item label="学时" prop="hours">
          <el-input-number v-model="editForm.hours" :min="0" :max="200" />
        </el-form-item>
        
        <el-form-item label="学科类别" prop="subjectCategory">
          <el-select v-model="editForm.subjectCategory" placeholder="请选择学科类别" style="width: 100%">
            <el-option
              v-for="item in subjectCategoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="课程简介" prop="introduce">
          <el-input 
            v-model="editForm.introduce" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入课程简介" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveCourseInfo">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, computed, watch } from 'vue'
import { uploadCourseCover, updateCourse, getCourseDetail } from '@/api/teacher/course'
import { ElMessage } from 'element-plus'

const props = defineProps({
  course: {
    type: Object,
    default: () => ({
      id: '',
      name: '--',
      term: '--',
      teacher: '--',
      score: '--',
      time: '--',
      school: '--',
      department: '--',
      major: '--',
      cover: '',
      courseType: '',
      credits: 0,
      hours: 0,
      subjectCategory: '',
      introduce: ''
    })
  }
});

const emit = defineEmits(['update:course', 'refresh']);

const fileInput = ref(null);
const isUploading = ref(false);
const editDialogVisible = ref(false);
const editFormRef = ref(null);

const semesterOptions = ref([
  { value: '第一学期', label: '第一学期' },
  { value: '第二学期', label: '第二学期' },
  { value: '第三学期', label: '第三学期' },
  { value: '第四学期', label: '第四学期' },
  { value: '第五学期', label: '第五学期' },
  { value: '第六学期', label: '第六学期' },
  { value: '第七学期', label: '第七学期' },
  { value: '第八学期', label: '第八学期' }
]);

const courseTypeOptions = ref([
  { value: '通识必修课程', label: '通识必修课程' },
  { value: '通识选修课程', label: '通识选修课程' },
  { value: '学科基础课程', label: '学科基础课程' },
  { value: '专业教育课程', label: '专业教育课程' },
  { value: '实践教学课程', label: '实践教学课程' },
  { value: '多元化教育课程', label: '多元化教育课程' }
]);

const subjectCategoryOptions = ref([
  { value: '工学', label: '工学' },
  { value: '理学', label: '理学' },
  { value: '医学', label: '医学' },
  { value: '文学', label: '文学' },
  { value: '法学', label: '法学' },
  { value: '经济学', label: '经济学' },
  { value: '管理学', label: '管理学' },
  { value: '艺术学', label: '艺术学' }
]);

const editForm = ref({
  name: '',
  school: '',
  department: '',
  semester: '',
  courseType: '',
  credits: 0,
  hours: 0,
  subjectCategory: '',
  introduce: ''
});

const courseCover = computed(() => {
  return props.course.cover 
    ? props.course.cover 
    : defaultCourseCover;
});

const openEditDialog = () => {
  editForm.value = {
    name: props.course.name,
    school: props.course.school,
    department: props.course.department,
    semester: props.course.term,
    courseType: props.course.courseType,
    credits: props.course.score ? Number(props.course.score) : 0,
    hours: props.course.time ? Number(props.course.time) : 0,
    subjectCategory: props.course.subjectCategory,
    introduce: props.course.introduce
  };
  editDialogVisible.value = true;
};

const saveCourseInfo = async () => {
  try {
    const updateData = {
      id: props.course.id,
      name: editForm.value.name,
      semester: editForm.value.semester,
      courseType: editForm.value.courseType,
      credits: editForm.value.credits,
      hours: editForm.value.hours,
      subjectCategory: editForm.value.subjectCategory,
      introduce: editForm.value.introduce
    };

    const response = await updateCourse(updateData);
    console.log('更新响应:', response);

    // 检查响应状态码
    if (response.code === 200) {
      const detailResponse = await getCourseDetail(props.course.id);
      const mainTeacher = detailResponse.teachers.find(t => t.teamRole === 1);
      const updatedCourse = {
        id: detailResponse.course.id,
        ...detailResponse.course,
        teacher: mainTeacher?.teacherName || '--',
        score: detailResponse.course.credits,
        time: detailResponse.course.hours,
        school: mainTeacher?.institution || '--',
        department: mainTeacher?.department || '--',
        major: detailResponse.course.major || '--',
        term: detailResponse.course.semester,
        cover: detailResponse.course.courseCover
      };

      emit('update:course', updatedCourse);
      ElMessage.success('课程信息更新成功');
      editDialogVisible.value = false;
      emit('refresh');
    } else {
      ElMessage.error(response.msg || '更新课程信息失败');
    }
  } catch (error) {
    console.error('保存课程信息失败:', error);
    ElMessage.error(error.message || '保存课程信息失败');
  }
};

const handleClose = (done) => {
  done();
};

const openFilePicker = () => {
  fileInput.value.click();
};

const handleFileChange = async (event) => {
  const file = event.target.files[0];
  if (!file) return;
  
  if (!file.type.startsWith('image/')) {
    return;
  }
  
  if (file.size > 10 * 1024 * 1024) {
    return;
  }
  
  isUploading.value = true;
  
  try {
    const uploadResponse = await uploadCourseCover(props.course.id, file);
    
    if (uploadResponse.code === 200) {
      const detailResponse = await getCourseDetail(props.course.id);
      
      if (detailResponse?.course) {
        const mainTeacher = detailResponse.teachers?.find(t => t.teamRole === 1);
        
        emit('update:course', {
          id: detailResponse.course.id,
          ...detailResponse.course,
          teacher: mainTeacher?.teacherName || '--',
          score: detailResponse.course.credits,
          time: detailResponse.course.hours,
          school: mainTeacher?.institution || '--',
          department: mainTeacher?.department || '--',
          major: detailResponse.course.major || '--',
          term: detailResponse.course.semester,
          cover: detailResponse.course.courseCover
        });
      }
    }
  } catch (error) {
    console.log('上传过程中出现错误:', error);
  } finally {
    isUploading.value = false;
    fileInput.value.value = '';
  }
};

const fallbackTodefaultCourseCover = (e) => {
  e.target.src = defaultCourseCover;
};
</script>

<style scoped lang="scss">
.course-info {
  display: flex;
  justify-content: space-between;
  height: 22vw;
  padding: 5vw 3.5vw;
  .course-info-left{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    width: 60%;
    color:white;
    .title{
      display: flex;
      flex-direction: column;
      .course-name{
        font-size: 2.4vw;
        font-weight: bold;
      }
      .course-term{
        font-size: 1vw;
      }
    }
    .course-base {
      display: grid;
      grid-template-columns: repeat(3, 1fr); 
      gap: 1vw; 
      margin-top: 2vw;
      .part {
        display: flex;
        align-items: center;
        gap: 0.3vw;
        min-width: 0; 
        white-space: nowrap; 
        font-size: 1vw;
        .icon{
          width: 1.2vw;
          height: 1.2vw;
        }
      }
    }
    
    .edit-btn-container {
      margin-top: 1vw;
      display: flex;
      justify-content: flex-start;
      .el-button {
        padding: 0.5vw 1vw;
        font-size: 0.9vw;
      }
    }
  }
  .course-info-right {
    position: relative; 
    display: block;
    width: 19.5vw;
    height: 12vw;
    
    .course-cover {
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: center;
      border-radius: 0.5vw;
    }

            .upload-trailer-btn {
          position: absolute;
          right: 0.8vw;     
          bottom: 0.8vw;    
          display: flex;
          align-items: center;
          gap: 0.4vw;
          padding: 0.6vw 1vw;
          background: rgba(0, 0, 0, 0.6); 
          border-radius: 0.3vw;
          color: white;
          cursor: pointer;
          transition: all 0.3s;
          z-index: 2; 

          &:hover {
            background: rgba(0, 0, 0, 0.8); 
            transform: scale(1.05);
          }

          .upload-icon {
            width: 1.2vw;
            height: 1.2vw;
            filter: brightness(0) invert(1); 
          }

          span {
            font-size: 0.9vw;
            white-space: nowrap;
          }
        }
      }
    }
</style>