<template>
  <div class="video-generator-container">
    <div class="top">
      <h1>视频生成</h1>
      <p>动态影像，智绘时空</p>
    </div>

    <div class="mid">
      <div class="video-row">
        <div class="video-card" v-for="(item, index) in videoItems" :key="index"
             @click="loadVideoText(item.text)">
          <video muted controls>
            <source :src="item.url" type="video/mp4">
          </video>
          <div class="overlay-text">{{ item.text }}</div>
        </div>
      </div>
    </div>

    <div class="input-container">
      <textarea v-model="promptText" placeholder="描述你想象的动态场景、运动轨迹、镜头语言..."
                @input="handleInput"></textarea>
      <button @click="generateVideo" class="generate-btn">
        <span class="arrow-icon">⮞</span>
      </button>
    </div>

    <div class="sidebar-wrapper">
      <div class="toggle-btn-container">
        <button class="toggle-btn" @click="toggleSidebar">
          <span v-if="sidebarVisible">▶</span>
          <span v-else>◀</span>
        </button>
      </div>
      <div class="sidebar-container">

        <div class="sidebar" :class="{ 'sidebar-collapsed': !sidebarVisible }">
          <h3>生成的视频 Waiting~</h3>
          <div class="sidebar-content">
            <div v-if="loading" class="loading-animation">
              <img src="../../assets/photo/loading.gif" alt="加载中..."/>
            </div>
            <div v-for="(video, index) in generatedVideos" :key="video.url + index" class="generated-video">
              <video controls :key="video.url + index">
                <source :src="video.url" type="video/mp4">
              </video>
              <div class="video-actions">
                <button @click.stop="previewVideo(video.url)">预览下载</button>
                <!--                <button @click.stop="downloadVideo(video.url)">下载</button>-->
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-if="showPreview" class="preview-modal" @click.self="closePreview">
        <div class="preview-content">
          <span class="close-btn" @click="closePreview">&times;</span>
          <video controls>
            <source :src="selectedVideo" type="video/mp4">
          </video>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {generateVideo} from '../../../../api/student/intelligent/video.js';

export default {
  data() {
    return {
      promptText: '',
      loading: false,
      generatedVideos: [],
      sidebarVisible: false,
      showPreview: false,
      selectedVideo: null,
      videoItems: [
        {
          url: 'https://sfile.chatglm.cn/api/cogvideo/0cff3cfc-393f-11f0-b908-02e158ba77fb_0.mp4',
          text: '赛博朋克都市夜景，飞行汽车穿梭，霓虹灯光在雨中折射'
        },
        {
          url: 'https://sfile.chatglm.cn/api/cogvideo/32d83408-3942-11f0-b8a4-669fa8a1ed0e_0.mp4',
          text: '梦幻森林，阳光透过树叶洒在小径上，小动物们在林间嬉戏'
        },
        {
          url: 'https://sfile.chatglm.cn/api/cogvideo/db7cf9ba-3940-11f0-b8a4-669fa8a1ed0e_0.mp4',
          text: '水彩卡通动画，展现四季变换，镜头从空中俯瞰山川河流'
        },
      ]
    };
  },
  methods: {
    handleInput(event) {
      this.promptText = event.target.value;
    },
    async generateVideo() {
      if (this.promptText.trim() === '') {
        alert('请输入描述内容');
        return;
      }
      this.loading = true;
      this.sidebarVisible = true;
      try {
        const videoData = await generateVideo(this.promptText);
        this.generatedVideos.unshift({
          url: videoData.url,
          prompt: this.promptText
        });
        this.promptText = '';
      } catch (error) {
        alert('视频生成失败: ' + error.message);
      } finally {
        this.loading = false;
      }
    },
    loadVideoText(text) {
      this.promptText = text;
    },
    downloadVideo(url) {
      const link = document.createElement('a');
      link.href = url;
      link.download = `ai-video-${Date.now()}.mp4`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    previewVideo(url) {
      this.selectedVideo = url;
      this.showPreview = true;
    },
    closePreview() {
      this.showPreview = false;
    },
    toggleSidebar() {
      this.sidebarVisible = !this.sidebarVisible;
    }
  }
}
</script>

<style scoped>
.video-generator-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 10px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.top {
  margin-bottom: 20px;
  text-align: center;
}

.top h1 {
  font-size: 24px;
  font-weight: bold;
  color: black;
}

.top p {
  margin-top: 10px;
  font-size: 16px;
  color: #666;
}

.mid {
  margin-bottom: 20px;
}

.video-row {
  margin: 10px;
  display: flex;
  overflow-x: auto;
  gap: 90px;
  padding: 35px 0;
  scroll-behavior: smooth;
}

.video-card {
  position: relative;
  width: 280px;
  height: 200px;
}

video {
  width: 110%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.overlay-text {
  width: 120%;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  color: black;
  padding: 4px;
  font-size: 12px;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.input-container {
  position: relative;
  border: 1px solid #e0e0e0;
  border-radius: 10px;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

textarea {
  width: 100%;
  height: 120px;
  border: none;
  resize: none;
  padding: 20px;
  font-size: 16px;
  outline: none;
  box-sizing: border-box;
  background-color: #fafafa;
}

textarea::placeholder {
  color: #999;
}

.generate-btn {
  position: absolute;
  right: 15px;
  bottom: 15px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #956ef6;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.generate-btn:hover {
  background-color: #8d8b8b;
}

.arrow-icon {
  font-size: 20px;
  color: white;
}

.sidebar-wrapper {
  position: fixed;
  top: 228px;
  right: 0;
  z-index: 999;
  display: flex;
  align-items: flex-start;
}

.sidebar-container {
  position: fixed;
  top: 228px;
  right: 2px;
  width: 300px;
  max-height: calc(90vh - 300px);
  z-index: 999;
}

.toggle-btn-container {
  order: -1;
  position: relative;
  z-index: 1000;
}

.toggle-btn {
  background-color: #a994de;
  color: #8863e5;
  border: none;
  border-radius: 4px 0 0 4px;
  padding: 10px 12px;
  cursor: pointer;
  z-index: 1000;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
}

.sidebar {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 15px;
  height: 100%;
  max-height: inherit;
  display: flex;
  flex-direction: column;
}

.sidebar-collapsed {
  transform: translateX(calc(100% + 40px));
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
}

.loading-animation {
  width: 100%;
  height: auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-animation img {
  max-width: 80%;
  max-height: 80%;
}

.sidebar-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: #956ef6;
  border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: rgba(149, 110, 246, 0.1);
}

.generated-video {
  margin-bottom: 15px;
  text-align: center;
}

.generated-video video {
  width: 95%;
  margin-bottom: 8px;
}

.video-actions {
  display: flex;
  gap: 10px;
  margin-top: 8px;
  justify-content: center;
}

.video-actions button {
  padding: 5px 10px;
  background-color: #956ef6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.video-actions button:hover {
  background-color: #d2c8ea;
}

.preview-content {
  max-width: 90%;
  max-height: 90%;
}

.preview-content video {
  max-width: 100%;
  max-height: 80vh;
  border-radius: 8px;
}

.preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.close-btn {
  position: absolute;
  top: -30px;
  right: -30px;
  color: white;
  font-size: 40px;
  cursor: pointer;
  transition: transform 0.3s;
}

.close-btn:hover {
  transform: scale(1.2);
}

.sidebar {
  scrollbar-width: thin;
  scrollbar-color: #956ef6 transparent;
}

.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-thumb {
  background: #956ef6;
  border-radius: 3px;
}
</style>
