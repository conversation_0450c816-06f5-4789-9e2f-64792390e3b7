<template>
  <div class="footer">
    <button class="cancel" @click="handleCancel">取消</button>
    <div class="selected">已选{{ selectedCount }}个资源</div>
    <button class="confirm" :class="{ disabled }" :disabled="disabled" @click="$emit('confirm')">
      确认关联
    </button>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

defineProps({
  selectedCount: Number,
  disabled: Boolean
})
defineEmits(['confirm'])

const handleCancel = () => {
  router.go(-1) // 返回上一页
}
</script>

<style lang="scss" scoped>
.footer {
  height: 80px;
  background: #fff;
  border-top: 1px solid #eee;
  padding: 0 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .cancel {
    border: 1px solid #ccc;
    background: #fff;
    padding: 6px 16px;
    border-radius: 6px;
    cursor: pointer;
  }

  .selected {
    font-size: 16px;
    font-weight: 555;
    color: #555;
  }

  .confirm {
    background: #4c7bff;
    color: white;
    padding: 6px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: 0.2s;

    &.disabled {
      background: #ccc;
      cursor: not-allowed;
    }
  }
}
</style>