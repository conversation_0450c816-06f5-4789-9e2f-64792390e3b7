<template>
  <div class="basic-info">
    <div v-if="viewMode === 'view'" class="view-mode">
      <div class="info-header">
        <div class="avatar-container" v-if="userInfo.idphoto">
          <img :src="userInfo.idphoto" alt="证件照" class="avatar">
        </div>
        <h3>{{ userInfo.name }}</h3>
        <p>{{ userInfo.position }}</p>
      </div>
      
      <div class="info-content">
        <div class="info-item">
          <span>学校/单位：</span>{{ userInfo.organization }}
        </div>
        <div class="info-item">
          <span>院系/部门：</span>{{ userInfo.department }}
        </div>
        <div class="info-item">
          <span>职称：</span>{{ userInfo.department }}
        </div>
        <div class="info-item">
          <span>昵称：</span>{{ userInfo.nickname }}
        </div>
        <div class="info-item">
          <span>性别：</span>{{ userInfo.gender }}
        </div>
        <div class="info-item">
          <span>入职年份：</span>{{ userInfo.enrollmentYear }}
        </div>
        <div class="info-item">
          <span>最高学历：</span>{{ userInfo.highestEducation }}
        </div>
        <div class="info-item bio-item">
          <span>简介：</span>{{ userInfo.bio }}
        </div>
      </div>
      
      <el-button type="primary" @click="editMode" :loading="loading">修改信息</el-button>
    </div>
    
    <div v-else class="edit-mode">
      <el-form :model="editForm" label-width="120px" @submit.native.prevent="saveChanges">
        <el-form-item label="姓名" :required="true">
          <el-input v-model="editForm.name" disabled></el-input>
        </el-form-item>
        <el-form-item label="职称">
          <el-input v-model="editForm.title" disabled></el-input>
        </el-form-item>
        <el-form-item label="学校/单位">
          <el-input v-model="editForm.organization" disabled></el-input>
        </el-form-item>
        <el-form-item label="院系/部门">
          <el-input v-model="editForm.department" disabled></el-input>
        </el-form-item>
        <el-form-item label="昵称">
          <el-input v-model="editForm.nickname"></el-input>
        </el-form-item>
        <el-form-item label="性别">
          <el-radio-group v-model="editForm.gender">
            <el-radio :label="'男'">男</el-radio>
            <el-radio :label="'女'">女</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="入职年份">
          <el-select v-model="editForm.enrollmentYear" placeholder="请选择入职年份">
            <el-option 
              v-for="year in enrollmentYears" 
              :key="year" 
              :label="year" 
              :value="year">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="最高学历">
          <el-select v-model="editForm.highestEducation" placeholder="请选择最高学历">
            <el-option label="高中及以下" value="高中及以下"></el-option>
            <el-option label="专科" value="专科"></el-option>
            <el-option label="本科" value="本科"></el-option>
            <el-option label="硕士" value="硕士"></el-option>
            <el-option label="博士" value="博士"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="证件照">
          <el-upload
            action="#"
            list-type="picture-card"
            :on-change="handleUpload"
            :on-preview="handlePictureCardPreview"
            :on-remove="handleRemove"
            :file-list="fileList"
            :auto-upload="false"
            :limit="1"
          >
            <i class="el-icon-plus"></i>
            <div slot="tip" class="el-upload__tip">支持 JPG/PNG 格式，大小不超过 2M</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="简介">
          <el-input type="textarea" v-model="editForm.bio" :rows="4"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="saveChanges" :loading="loading">保存</el-button>
          <el-button @click="cancelEdit">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getTeacherDetail, updateTeacher, uploadTeacherFile } from '@/api/teacher/AccountManager/teacherInfo'
import { useUserStore } from '@/stores/userStore'
import { ElMessage } from 'element-plus'

const userStore = useUserStore()
const viewMode = ref('view') // 'view' 或 'edit'
const fileList = ref([])
const loading = ref(false)

// 展示用的教师信息
const userInfo = ref({
  id: '',
  name: '',
  position: '',
  organization: '桂林电子科技大学', // 固定值或从后端获取
  department: '',
  title:'',
  nickname: '',
  gender: '',
  enrollmentYear: '',
  highestEducation: '',
  bio: '',
  idphoto: ''
})

// 编辑表单数据
const editForm = ref({ ...userInfo.value })

// 入学年份选项（1950-当前年）
const enrollmentYears = Array.from(
  { length: new Date().getFullYear() - 1950 + 1 },
  (_, i) => (new Date().getFullYear() - i).toString()
)

// 加载教师数据（从后端获取最新）
const loadTeacherData = async () => {
  try {
    loading.value = true
    const response = await getTeacherDetail(userStore.user?.id)
    
    if (response.code === 200 && response.result) {
      const apiData = response.result
      console.log(response.result)
      
      // 映射后端字段到前端结构
      userInfo.value = {
        id: apiData.id,
        name: apiData.teacherName || '未设置',
        title: apiData.teacherTitle || '未设置',
        organization: apiData.institution ||'未设置',
        department: apiData.department || '未设置',
        nickname: apiData.teacherName || '未设置', // 昵称可能需要单独字段
        gender: apiData.sex || '男',
        enrollmentYear: apiData.createdTime || '未设置',
        highestEducation: apiData.educations || '未设置',
        bio: apiData.teacherIntro || '暂无简介',
        idphoto: apiData.idphoto || ''
      }

      // 初始化证件照文件列表
      fileList.value = apiData.idphoto 
        ? [{ name: '证件照', url: apiData.idphoto }] 
        : []
    }
  } catch (error) {
    console.error('加载教师数据失败:', error)
    ElMessage.error('加载信息失败')
  } finally {
    loading.value = false
  }
}

// 进入编辑模式
const editMode = () => {
  viewMode.value = 'edit'
  editForm.value = { ...userInfo.value } // 深拷贝当前数据到编辑表单
}

// 保存修改
const saveChanges = async () => {
  try {
    loading.value = true
    
    // 构造符合后端API的字段结构
    const updateData = {
      id: editForm.value.id,
      teacherName: editForm.value.name,
      teacherTitle: editForm.value.title,
      department: editForm.value.department,
      institution: editForm.value.organization,
      sex: editForm.value.gender,
      createdTime: editForm.value.enrollmentYear,
      educations: editForm.value.highestEducation,
      teacherIntro: editForm.value.bio,
      idphoto: editForm.value.idphoto
    }

    // 1. 提交修改到后端
    const response = await updateTeacher(updateData)
    if (response.code !== 200) {
      throw new Error(response.msg || '保存失败')
    }

    // 2. 关键步骤：从后端重新加载最新数据
    await loadTeacherData()

    // 3. 返回查看模式
    viewMode.value = 'view'
    ElMessage.success('信息已更新')
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error(error.message || '保存失败')
  } finally {
    loading.value = false
  }
}

// 取消编辑
const cancelEdit = () => {
  viewMode.value = 'view'
}

// 上传证件照
const handleUpload = async (file) => {
  try {
    // 1. 前端验证
    const validTypes = ['image/jpeg', 'image/png'];
    const isLt2M = file.size / 1024 / 1024 < 2;
    
    if (!validTypes.includes(file.raw?.type || file.type)) {
      ElMessage.error('只能上传JPG/PNG格式图片');
      return false;
    }
    if (!isLt2M) {
      ElMessage.error('图片大小不能超过2MB');
      return false;
    }

    // 2. 显示上传中状态
    file.status = 'uploading';
    fileList.value = [file];

    // 3. 调用上传接口（使用优化后的接口定义）
    const response = await uploadTeacherFile(file);

    // 4. 处理响应
    if (response.code === 200 && response.result?.url) {
      editForm.value.idphoto = response.result.url;
      file.status = 'success';
      ElMessage.success('上传成功');
    } else {
      throw new Error(response.msg || '上传失败');
    }
  } catch (error) {
    console.error('上传失败:', {
      error: error.response?.data || error.message,
      request: error.config
    });
    file.status = 'fail';
    ElMessage.error(error.message || '上传失败');
  }
  return false; // 阻止自动上传
};
// 移除证件照
const handleRemove = () => {
  editForm.value.idphoto = ''
}

// 初始化加载数据
onMounted(() => {
  loadTeacherData()
})
</script>

<style scoped>
.basic-info {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.info-header {
  text-align: center;
  margin-bottom: 20px;
}

.avatar-container {
  width: 120px;
  height: 120px;
  margin: 0 auto 15px;
  border-radius: 50%;
  overflow: hidden;
  border: 1px solid #eee;
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.info-content {
  margin-bottom: 20px;
}

.info-item {
  margin-bottom: 10px;
  font-size: 14px;
  color: #333;
  line-height: 1.6;
}

.bio-item {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.edit-mode {
  margin-top: 20px;
}
</style>