/*
 * 用户认证相关API  src\api\auth\auth.js
 */
import api from '../service.js'

export const register = (userData) => {
  return api({
    url: '/user/register',
    method: 'post',
    data: userData
  }).then(response => {
    if (response.code === 200) {
      return { 
        success: true,
        data: response.result
      }
    } else {
      throw new Error(response.msg || '注册失败')
    }
  })
}
export const login = (credentials) => {
  return api({
    url: '/user/login',
    method: 'post',
    data: credentials
  }).then(response => {
    if (response.code === 200) {
      // 确保返回完整result对象
      return {
        ...response.result,
        // 添加必要字段的默认值
        name: response.result.name || null,
        avatar: response.result.avatar || null
      };
    } else {
      throw new Error(response.msg || '登录失败');
    }
  });
};

export const logout = () => {
  return api.post('/auth/logout')
}

export const checkCaptchaRequired = (account) => {
  return api({
    url: `/user/captcha/required?account=${account}`,
    method: 'get'
  }).then(response => {
    return response.result
  })
}

export const generateCaptcha = async (account) => {
  try {
    const timestamp = Date.now();
    const response = await api({
      url: `/user/captcha/generate?account=${account}&t=${timestamp}`,
      method: 'get',
      responseType: 'blob' // 确保请求配置了responseType为blob
    });

    // 从响应头获取token
    const token = response.headers['captcha-token'] || response.headers['Captcha-Token'];
    if (!token) {
      throw new Error('验证码Token获取失败');
    }

    // 创建Blob URL
    const imageUrl = URL.createObjectURL(response.data);

    return {
      imageUrl: imageUrl,  // 直接返回可用的URL
      token: token
    };
  } catch (error) {
    console.error('验证码获取失败:', error);
    throw new Error(`验证码获取失败: ${error.message}`);
  }
};