{"_attachments": {}, "_id": "yaml", "_rev": "516-61f144bcb77ea98a748f6032", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "description": "JavaScript parser and stringifier for YAML", "dist-tags": {"latest": "2.8.0"}, "license": "ISC", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "name": "yaml", "readme": "# YAML <a href=\"https://www.npmjs.com/package/yaml\"><img align=\"right\" src=\"https://badge.fury.io/js/yaml.svg\" title=\"npm package\" /></a>\n\n`yaml` is a definitive library for [YAML](https://yaml.org/), the human friendly data serialization standard.\nThis library:\n\n- Supports both YAML 1.1 and YAML 1.2 and all common data schemas,\n- Passes all of the [yaml-test-suite](https://github.com/yaml/yaml-test-suite) tests,\n- Can accept any string as input without throwing, parsing as much YAML out of it as it can, and\n- Supports parsing, modifying, and writing YAML comments and blank lines.\n\nThe library is released under the ISC open source license, and the code is [available on GitHub](https://github.com/eemeli/yaml/).\nIt has no external dependencies and runs on Node.js as well as modern browsers.\n\nFor the purposes of versioning, any changes that break any of the documented endpoints or APIs will be considered semver-major breaking changes.\nUndocumented library internals may change between minor versions, and previous APIs may be deprecated (but not removed).\n\nThe minimum supported TypeScript version of the included typings is 3.9;\nfor use in earlier versions you may need to set `skipLibCheck: true` in your config.\nThis requirement may be updated between minor versions of the library.\n\nFor more information, see the project's documentation site: [**eemeli.org/yaml**](https://eemeli.org/yaml/)\n\nTo install:\n\n```sh\nnpm install yaml\n# or\ndeno add jsr:@eemeli/yaml\n```\n\n**Note:** These docs are for `yaml@2`. For v1, see the [v1.10.0 tag](https://github.com/eemeli/yaml/tree/v1.10.0) for the source and [eemeli.org/yaml/v1](https://eemeli.org/yaml/v1/) for the documentation.\n\nThe development and maintenance of this library is [sponsored](https://github.com/sponsors/eemeli) by:\n\n<p align=\"center\" width=\"100%\">\n  <a href=\"https://www.scipress.io/\"\n    ><img\n      width=\"150\"\n      align=\"top\"\n      src=\"https://eemeli.org/yaml/images/scipress.svg\"\n      alt=\"Scipress\"\n  /></a>\n  &nbsp; &nbsp;\n  <a href=\"https://manifest.build/\"\n    ><img\n      width=\"150\"\n      align=\"top\"\n      src=\"https://eemeli.org/yaml/images/manifest.svg\"\n      alt=\"Manifest\"\n  /></a>\n</p>\n\n## API Overview\n\nThe API provided by `yaml` has three layers, depending on how deep you need to go: [Parse & Stringify](https://eemeli.org/yaml/#parse-amp-stringify), [Documents](https://eemeli.org/yaml/#documents), and the underlying [Lexer/Parser/Composer](https://eemeli.org/yaml/#parsing-yaml).\nThe first has the simplest API and \"just works\", the second gets you all the bells and whistles supported by the library along with a decent [AST](https://eemeli.org/yaml/#content-nodes), and the third lets you get progressively closer to YAML source, if that's your thing.\n\nA [command-line tool](https://eemeli.org/yaml/#command-line-tool) is also included.\n\n### Parse & Stringify\n\n```js\nimport { parse, stringify } from 'yaml'\n```\n\n- [`parse(str, reviver?, options?): value`](https://eemeli.org/yaml/#yaml-parse)\n- [`stringify(value, replacer?, options?): string`](https://eemeli.org/yaml/#yaml-stringify)\n\n### Documents\n\n<!-- prettier-ignore -->\n```js\nimport {\n  Document,\n  isDocument,\n  parseAllDocuments,\n  parseDocument\n} from 'yaml'\n```\n\n- [`Document`](https://eemeli.org/yaml/#documents)\n  - [`constructor(value, replacer?, options?)`](https://eemeli.org/yaml/#creating-documents)\n  - [`#contents`](https://eemeli.org/yaml/#content-nodes)\n  - [`#directives`](https://eemeli.org/yaml/#stream-directives)\n  - [`#errors`](https://eemeli.org/yaml/#errors)\n  - [`#warnings`](https://eemeli.org/yaml/#errors)\n- [`isDocument(foo): boolean`](https://eemeli.org/yaml/#identifying-node-types)\n- [`parseAllDocuments(str, options?): Document[]`](https://eemeli.org/yaml/#parsing-documents)\n- [`parseDocument(str, options?): Document`](https://eemeli.org/yaml/#parsing-documents)\n\n### Content Nodes\n\n<!-- prettier-ignore -->\n```js\nimport {\n  isAlias, isCollection, isMap, isNode,\n  isPair, isScalar, isSeq, Scalar,\n  visit, visitAsync, YAMLMap, YAMLSeq\n} from 'yaml'\n```\n\n- [`isAlias(foo): boolean`](https://eemeli.org/yaml/#identifying-node-types)\n- [`isCollection(foo): boolean`](https://eemeli.org/yaml/#identifying-node-types)\n- [`isMap(foo): boolean`](https://eemeli.org/yaml/#identifying-node-types)\n- [`isNode(foo): boolean`](https://eemeli.org/yaml/#identifying-node-types)\n- [`isPair(foo): boolean`](https://eemeli.org/yaml/#identifying-node-types)\n- [`isScalar(foo): boolean`](https://eemeli.org/yaml/#identifying-node-types)\n- [`isSeq(foo): boolean`](https://eemeli.org/yaml/#identifying-node-types)\n- [`new Scalar(value)`](https://eemeli.org/yaml/#scalar-values)\n- [`new YAMLMap()`](https://eemeli.org/yaml/#collections)\n- [`new YAMLSeq()`](https://eemeli.org/yaml/#collections)\n- [`doc.createAlias(node, name?): Alias`](https://eemeli.org/yaml/#creating-nodes)\n- [`doc.createNode(value, options?): Node`](https://eemeli.org/yaml/#creating-nodes)\n- [`doc.createPair(key, value): Pair`](https://eemeli.org/yaml/#creating-nodes)\n- [`visit(node, visitor)`](https://eemeli.org/yaml/#finding-and-modifying-nodes)\n- [`visitAsync(node, visitor)`](https://eemeli.org/yaml/#finding-and-modifying-nodes)\n\n### Parsing YAML\n\n```js\nimport { Composer, Lexer, Parser } from 'yaml'\n```\n\n- [`new Lexer().lex(src)`](https://eemeli.org/yaml/#lexer)\n- [`new Parser(onNewLine?).parse(src)`](https://eemeli.org/yaml/#parser)\n- [`new Composer(options?).compose(tokens)`](https://eemeli.org/yaml/#composer)\n\n## YAML.parse\n\n```yaml\n# file.yml\nYAML:\n  - A human-readable data serialization language\n  - https://en.wikipedia.org/wiki/YAML\nyaml:\n  - A complete JavaScript implementation\n  - https://www.npmjs.com/package/yaml\n```\n\n```js\nimport fs from 'fs'\nimport YAML from 'yaml'\n\nYAML.parse('3.14159')\n// 3.14159\n\nYAML.parse('[ true, false, maybe, null ]\\n')\n// [ true, false, 'maybe', null ]\n\nconst file = fs.readFileSync('./file.yml', 'utf8')\nYAML.parse(file)\n// { YAML:\n//   [ 'A human-readable data serialization language',\n//     'https://en.wikipedia.org/wiki/YAML' ],\n//   yaml:\n//   [ 'A complete JavaScript implementation',\n//     'https://www.npmjs.com/package/yaml' ] }\n```\n\n## YAML.stringify\n\n```js\nimport YAML from 'yaml'\n\nYAML.stringify(3.14159)\n// '3.14159\\n'\n\nYAML.stringify([true, false, 'maybe', null])\n// `- true\n// - false\n// - maybe\n// - null\n// `\n\nYAML.stringify({ number: 3, plain: 'string', block: 'two\\nlines\\n' })\n// `number: 3\n// plain: string\n// block: |\n//   two\n//   lines\n// `\n```\n\n---\n\nBrowser testing provided by:\n\n<a href=\"https://www.browserstack.com/open-source\">\n<img width=200 src=\"https://eemeli.org/yaml/images/browserstack.svg\" alt=\"BrowserStack\" />\n</a>\n", "time": {"created": "2022-01-26T12:55:24.106Z", "modified": "2025-05-15T08:00:07.394Z", "2.0.0-9": "2021-11-13T09:52:23.807Z", "2.0.0-8": "2021-09-06T17:27:22.328Z", "2.0.0-7": "2021-07-17T10:23:28.886Z", "2.0.0-6": "2021-06-14T06:55:59.132Z", "2.0.0-5": "2021-04-18T08:52:32.301Z", "2.0.0-4": "2021-03-13T23:08:54.849Z", "1.10.2": "2021-03-13T21:02:56.363Z", "1.10.1": "2021-03-13T11:03:58.138Z", "2.0.0-3": "2021-01-31T21:28:40.953Z", "2.0.0-2": "2021-01-31T20:33:48.902Z", "2.0.0-1": "2020-10-05T22:28:56.807Z", "2.0.0-0": "2020-08-23T15:02:15.918Z", "1.10.0": "2020-05-16T09:56:27.877Z", "1.9.2": "2020-04-20T09:28:01.007Z", "1.9.1": "2020-04-18T12:01:32.964Z", "1.9.0": "2020-04-17T16:33:54.113Z", "1.8.3": "2020-03-21T00:51:23.126Z", "1.8.2": "2020-03-11T05:51:45.069Z", "1.8.1": "2020-03-11T04:42:35.186Z", "1.8.0": "2020-03-07T19:39:11.743Z", "1.7.2": "2019-10-15T11:24:10.790Z", "1.7.1": "2019-10-07T12:39:51.322Z", "1.7.0": "2019-09-25T13:15:45.991Z", "1.6.0": "2019-05-23T14:07:54.388Z", "1.5.1": "2019-05-05T13:14:24.175Z", "1.5.0": "2019-04-06T06:50:57.336Z", "1.4.0": "2019-03-07T19:48:19.735Z", "1.3.2": "2019-02-08T21:27:42.462Z", "1.3.1": "2019-01-27T11:42:43.790Z", "1.3.0": "2019-01-27T08:33:37.515Z", "1.2.1": "2019-01-17T10:15:38.143Z", "1.2.0": "2019-01-01T09:34:08.556Z", "1.1.0": "2018-12-11T23:00:08.695Z", "1.0.3": "2018-11-28T21:08:01.305Z", "1.0.2": "2018-11-15T10:53:22.920Z", "1.0.1": "2018-11-07T10:32:19.623Z", "1.0.0": "2018-09-19T19:56:51.387Z", "1.0.0-rc.8": "2018-08-23T05:03:16.366Z", "1.0.0-rc.7": "2018-07-11T15:31:19.241Z", "1.0.0-rc.6": "2018-07-08T12:16:11.502Z", "1.0.0-rc.5": "2018-06-30T15:31:40.511Z", "1.0.0-rc.4": "2018-06-27T21:20:16.651Z", "1.0.0-rc.3": "2018-06-26T19:12:38.659Z", "1.0.0-rc.2": "2018-06-18T21:18:58.840Z", "1.0.0-rc.1": "2018-06-18T21:13:43.889Z", "1.0.0-beta.7": "2018-06-15T14:04:39.596Z", "1.0.0-beta.6": "2018-05-29T20:41:59.158Z", "1.0.0-beta.5": "2018-05-15T18:21:45.622Z", "1.0.0-beta.4": "2018-04-08T12:56:59.760Z", "1.0.0-beta.3": "2018-02-22T23:02:54.112Z", "1.0.0-beta.2": "2018-02-19T09:00:00.612Z", "1.0.0-beta.1": "2018-02-13T09:58:43.794Z", "0.3.0": "2016-02-21T13:00:23.937Z", "0.2.3": "2011-12-05T20:18:09.979Z", "0.2.2": "2011-08-31T17:45:55.087Z", "0.2.1": "2011-05-22T17:20:58.242Z", "0.2.0": "2011-05-21T19:08:13.195Z", "0.1.2": "2011-04-15T16:25:52.216Z", "0.1.1": "2011-04-15T16:25:52.216Z", "0.1.0": "2011-04-15T16:25:52.216Z", "2.0.0-10": "2021-12-31T14:49:40.336Z", "2.0.0-11": "2022-03-22T08:55:33.137Z", "2.0.0": "2022-04-06T08:47:16.324Z", "2.0.1": "2022-04-15T20:37:10.896Z", "2.1.0": "2022-05-14T09:40:59.138Z", "2.1.1": "2022-05-29T22:26:44.097Z", "2.1.2": "2022-10-02T16:49:07.255Z", "2.1.3": "2022-10-05T07:32:18.071Z", "2.2.0": "2022-12-21T12:31:43.946Z", "2.2.1": "2022-12-30T09:58:13.392Z", "2.3.0-0": "2023-03-11T12:57:24.376Z", "2.3.0-1": "2023-04-04T08:27:24.123Z", "2.3.0-2": "2023-04-14T09:01:00.965Z", "2.3.0-3": "2023-04-14T09:14:01.768Z", "2.3.0-4": "2023-04-18T09:21:40.633Z", "2.2.2": "2023-04-24T13:02:14.045Z", "2.3.0-5": "2023-05-06T17:38:58.086Z", "2.3.0": "2023-05-23T11:16:22.320Z", "2.3.1": "2023-05-26T10:39:48.703Z", "2.3.2": "2023-08-28T15:00:54.836Z", "2.3.3": "2023-10-14T08:37:11.077Z", "2.3.4": "2023-11-03T01:41:28.842Z", "2.4.0": "2024-02-25T15:34:00.239Z", "2.4.1": "2024-03-06T11:52:32.864Z", "2.4.2": "2024-04-28T09:32:55.892Z", "2.4.3": "2024-06-02T09:08:47.664Z", "2.4.4": "2024-06-08T16:22:59.714Z", "2.4.5": "2024-06-08T21:42:26.758Z", "2.5.0": "2024-07-24T10:36:40.581Z", "2.5.1": "2024-09-03T23:13:27.622Z", "2.6.0": "2024-10-13T10:36:21.312Z", "2.6.1": "2024-11-19T10:47:52.527Z", "2.7.0": "2024-12-31T04:40:47.460Z", "2.7.1": "2025-03-29T15:00:01.431Z", "2.8.0": "2025-05-15T07:58:15.800Z"}, "versions": {"2.0.0-9": {"name": "yaml", "version": "2.0.0-9", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.js", "build:node": "rollup -c config/rollup.node-config.js", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es2017 dist/index.js", "test:types": "tsc --noEmit", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "@rollup/plugin-replace": "^3.0.0", "@rollup/plugin-typescript": "^8.1.1", "@types/jest": "^27.0.1", "@types/node": "^16.9.1", "@typescript-eslint/eslint-plugin": "^4.15.2", "@typescript-eslint/parser": "^4.15.2", "babel-jest": "^27.0.1", "cross-env": "^7.0.3", "eslint": "^7.20.0", "eslint-config-prettier": "^8.1.0", "fast-check": "^2.12.0", "jest": "^27.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^2.2.1", "rollup": "^2.38.2", "tslib": "^2.1.0", "typescript": "^4.3.5"}, "engines": {"node": ">= 12"}, "types": "./dist/index.d.ts", "readmeFilename": "README.md", "gitHead": "6b2b8f1fe76bdc2aa129c818ceb4ddb0ff21e0f9", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.0.0-9", "_nodeVersion": "17.0.1", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-Bf2KowHjyVkIIiGMt7+fbhmlvKOaE8DWuD07bnL4+FQ9sPmEl/5IzGpBpoxPqOaHuyasBjJhyXDcISpJWfhCGw==", "shasum": "0099f0645d1ffa686a2c5141b6da340f545d3634", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.0.0-9.tgz", "fileCount": 225, "unpackedSize": 617754, "size": 98879, "noattachment": false}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.0.0-9_1636797143657_0.1212236176435546"}, "_hasShrinkwrap": false, "publish_time": 1636797143807, "_cnpm_publish_time": 1636797143807, "_cnpmcore_publish_time": "2021-12-13T13:14:53.711Z"}, "2.0.0-8": {"name": "yaml", "version": "2.0.0-8", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.js", "build:node": "rollup -c config/rollup.node-config.js", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es2017 dist/index.js", "test:types": "tsc --noEmit", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "@rollup/plugin-replace": "^2.3.4", "@rollup/plugin-typescript": "^8.1.1", "@types/jest": "^27.0.1", "@types/node": "^15.6.1", "@typescript-eslint/eslint-plugin": "^4.15.2", "@typescript-eslint/parser": "^4.15.2", "babel-jest": "^27.0.1", "cross-env": "^7.0.3", "eslint": "^7.20.0", "eslint-config-prettier": "^8.1.0", "fast-check": "^2.12.0", "jest": "^27.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^2.2.1", "rollup": "^2.38.2", "tslib": "^2.1.0", "typescript": "^4.3.5"}, "engines": {"node": ">= 12"}, "readmeFilename": "README.md", "gitHead": "3736cc59747a0a99e4bb0516d240a8b5a0dc8785", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.0.0-8", "_nodeVersion": "16.8.0", "_npmVersion": "7.21.0", "dist": {"shasum": "226365f0d804ba7fb8cc2b527a00a7a4a3d8ea5f", "size": 96061, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.0.0-8.tgz", "integrity": "sha512-QaYgJZMfWD6fKN/EYMk6w1oLWPCr1xj9QaPSZW5qkDb3y8nGCXhy2Ono+AF4F+CSL/vGcqswcAT0BaS//pgD2A=="}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.0.0-8_1630949242101_0.7057540612045665"}, "_hasShrinkwrap": false, "publish_time": 1630949242328, "_cnpm_publish_time": 1630949242328, "_cnpmcore_publish_time": "2021-12-13T13:14:54.092Z"}, "2.0.0-7": {"name": "yaml", "version": "2.0.0-7", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.js", "build:node": "rollup -c config/rollup.node-config.js", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es2017 dist/index.js", "test:types": "tsc --noEmit", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "@rollup/plugin-replace": "^2.3.4", "@rollup/plugin-typescript": "^8.1.1", "@types/jest": "^26.0.20", "@types/node": "^15.6.1", "@typescript-eslint/eslint-plugin": "^4.15.2", "@typescript-eslint/parser": "^4.15.2", "babel-jest": "^27.0.1", "cross-env": "^7.0.3", "eslint": "^7.20.0", "eslint-config-prettier": "^8.1.0", "fast-check": "^2.12.0", "jest": "^27.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^2.2.1", "rollup": "^2.38.2", "tslib": "^2.1.0", "typescript": "^4.1.3"}, "engines": {"node": ">= 12"}, "readmeFilename": "README.md", "gitHead": "a0b5cdffad2af5de497dd0e58eeb3a1878eaa93f", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.0.0-7", "_nodeVersion": "16.4.2", "_npmVersion": "7.19.1", "dist": {"shasum": "9799d9d85dfc8f01e4cc425e18e09215364beef1", "size": 94912, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.0.0-7.tgz", "integrity": "sha512-RbI2Tm3hl9AoHY4wWyWvGvJfFIbHOzuzaxum6ez1A0vve+uXgNor03Wys4t+2sgjJSVSe+B2xerd1/dnvqHlOA=="}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.0.0-7_1626517408734_0.5989743176531204"}, "_hasShrinkwrap": false, "publish_time": 1626517408886, "_cnpm_publish_time": 1626517408886, "_cnpmcore_publish_time": "2021-12-13T13:14:54.465Z"}, "2.0.0-6": {"name": "yaml", "version": "2.0.0-6", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.js", "build:node": "rollup -c config/rollup.node-config.js", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es2017 dist/index.js", "test:types": "tsc --noEmit", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "@rollup/plugin-replace": "^2.3.4", "@rollup/plugin-typescript": "^8.1.1", "@types/jest": "^26.0.20", "@types/node": "^15.6.1", "@typescript-eslint/eslint-plugin": "^4.15.2", "@typescript-eslint/parser": "^4.15.2", "babel-jest": "^27.0.1", "cross-env": "^7.0.3", "eslint": "^7.20.0", "eslint-config-prettier": "^8.1.0", "fast-check": "^2.12.0", "jest": "^27.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^2.2.1", "rollup": "^2.38.2", "tslib": "^2.1.0", "typescript": "^4.1.3"}, "engines": {"node": ">= 12"}, "readmeFilename": "README.md", "gitHead": "b07e9cec928d24406d515580099df7dadd76a313", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.0.0-6", "_nodeVersion": "15.11.0", "_npmVersion": "7.12.1", "dist": {"shasum": "3d858d656b9ef13a95c77d083b816e6a8d16caa0", "size": 94626, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.0.0-6.tgz", "integrity": "sha512-YPUm0Z0sei53zauT7HWkkxyIBJhb9Gnf5jv4w4ahw5/v3PjFGhZOt4paXH6g9hzcMJqmNxZwoGfF1JzE2jvSgg=="}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.0.0-6_1623653759007_0.2444526931181159"}, "_hasShrinkwrap": false, "publish_time": 1623653759132, "_cnpm_publish_time": 1623653759132, "_cnpmcore_publish_time": "2021-12-13T13:14:54.902Z"}, "2.0.0-5": {"name": "yaml", "version": "2.0.0-5", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.js", "build:node": "rollup -c config/rollup.node-config.js", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es2017 dist/index.js", "test:types": "tsc --noEmit", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "@rollup/plugin-replace": "^2.3.4", "@rollup/plugin-typescript": "^8.1.1", "@types/jest": "^26.0.20", "@types/node": "^14.14.37", "@typescript-eslint/eslint-plugin": "^4.15.2", "@typescript-eslint/parser": "^4.15.2", "babel-jest": "^26.6.3", "cross-env": "^7.0.3", "eslint": "^7.20.0", "eslint-config-prettier": "^8.1.0", "fast-check": "^2.12.0", "jest": "^26.6.3", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^2.2.1", "rollup": "^2.38.2", "tslib": "^2.1.0", "typescript": "^4.1.3"}, "engines": {"node": ">= 10"}, "readmeFilename": "README.md", "gitHead": "89f6101297cdeea41641488b49765c2e4ec451f2", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.0.0-5", "_nodeVersion": "15.11.0", "_npmVersion": "7.8.0", "dist": {"shasum": "00906046dc119427b2b4f7cf9041d34682d1480b", "size": 93359, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.0.0-5.tgz", "integrity": "sha512-qH5L5eqW8cyv/N1U6rkK/O0M7kOK3BSo48d05Ptm03ITNsVFwg6TQ47wR72Db/ULWH5RfNJv+CqnG17Pyn8eqQ=="}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.0.0-5_1618735952113_0.23361353223719283"}, "_hasShrinkwrap": false, "publish_time": 1618735952301, "_cnpm_publish_time": 1618735952301, "_cnpmcore_publish_time": "2021-12-13T13:14:55.312Z"}, "2.0.0-4": {"name": "yaml", "version": "2.0.0-4", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.js", "build:node": "rollup -c config/rollup.node-config.js", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:types": "tsc --noEmit", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "> 0.5%, not dead, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "@rollup/plugin-replace": "^2.3.4", "@rollup/plugin-typescript": "^8.1.1", "@types/jest": "^26.0.20", "@typescript-eslint/eslint-plugin": "^4.15.2", "@typescript-eslint/parser": "^4.15.2", "babel-jest": "^26.6.3", "cross-env": "^7.0.3", "eslint": "^7.20.0", "eslint-config-prettier": "^8.1.0", "fast-check": "^2.12.0", "jest": "^26.6.3", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^2.2.1", "rollup": "^2.38.2", "rollup-plugin-copy": "^3.3.0", "tslib": "^2.1.0", "typescript": "^4.1.3"}, "engines": {"node": ">= 10"}, "readmeFilename": "README.md", "gitHead": "250f62177a1ee4ea1ff22514784fd578ea1219dd", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.0.0-4", "_nodeVersion": "15.11.0", "_npmVersion": "7.6.0", "dist": {"shasum": "0b8089fecd1843d1a8eb8d0aff1470c471653e15", "size": 85448, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.0.0-4.tgz", "integrity": "sha512-MoQoNhTFI400tkaeod+X0Vety1KD2L9dUa6pa1CVcyfcATjC/iDxoMLvqZ6U3D8c5KzxBrU2HnJH+PfaXOqI7w=="}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.0.0-4_1615676934662_0.6606132487250003"}, "_hasShrinkwrap": false, "publish_time": 1615676934849, "_cnpm_publish_time": 1615676934849, "_cnpmcore_publish_time": "2021-12-13T13:14:55.709Z"}, "1.10.2": {"name": "yaml", "version": "1.10.2", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/v1/", "type": "commonjs", "main": "./index.js", "browser": {"./index.js": "./browser/index.js", "./map.js": "./browser/map.js", "./pair.js": "./browser/pair.js", "./parse-cst.js": "./browser/parse-cst.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./seq.js": "./browser/seq.js", "./types.js": "./browser/types.js", "./types.mjs": "./browser/types.js", "./types/binary.js": "./browser/types/binary.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/set.js": "./browser/types/set.js", "./types/timestamp.js": "./browser/types/timestamp.js", "./util.js": "./browser/util.js", "./util.mjs": "./browser/util.js"}, "exports": {".": "./index.js", "./parse-cst": "./parse-cst.js", "./types": [{"import": "./types.mjs"}, "./types.js"], "./util": [{"import": "./util.mjs"}, "./util.js"], "./": "./"}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c rollup.browser-config.js", "build:node": "rollup -c rollup.node-config.js", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "start": "cross-env TRACE_LEVEL=log npm run build:node && node -i -e 'YAML=require(\".\")'", "test": "jest", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest", "test:types": "tsc --lib ES2017 --noEmit tests/typings.ts", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "> 0.5%, not dead", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "babel-eslint": "^10.1.0", "babel-jest": "^26.6.3", "babel-plugin-trace": "^1.1.0", "common-tags": "^1.8.0", "cross-env": "^7.0.3", "eslint": "^7.19.0", "eslint-config-prettier": "^7.2.0", "fast-check": "^2.12.0", "jest": "^26.6.3", "prettier": "^2.2.1", "rollup": "^2.38.2", "typescript": "^4.1.3"}, "engines": {"node": ">= 6"}, "gitHead": "4cdcde632ece71155f3108ec0120c1a0329a6914", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.10.2", "_nodeVersion": "15.11.0", "_npmVersion": "7.6.0", "dist": {"shasum": "2301c5ffbf12b467de8da2333a459e29e7920e4b", "size": 95267, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.10.2.tgz", "integrity": "sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg=="}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.10.2_1615669376185_0.9559963702192358"}, "_hasShrinkwrap": false, "publish_time": 1615669376363, "_cnpm_publish_time": 1615669376363, "_cnpmcore_publish_time": "2021-12-13T13:14:56.160Z"}, "1.10.1": {"name": "yaml", "version": "1.10.1", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/v1/", "type": "commonjs", "main": "./index.js", "browser": {"./index.js": "./browser/index.js", "./map.js": "./browser/map.js", "./pair.js": "./browser/pair.js", "./parse-cst.js": "./browser/parse-cst.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./seq.js": "./browser/seq.js", "./types.js": "./browser/types.js", "./types.mjs": "./browser/types.js", "./types/binary.js": "./browser/types/binary.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/set.js": "./browser/types/set.js", "./types/timestamp.js": "./browser/types/timestamp.js", "./util.js": "./browser/util.js", "./util.mjs": "./browser/util.js"}, "exports": {".": "./index.js", "./parse-cst": "./parse-cst.js", "./types": [{"import": "./types.mjs"}, "./types.js"], "./util": [{"import": "./util.mjs"}, "./util.js"], "./": "./"}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c rollup.browser-config.js", "build:node": "rollup -c rollup.node-config.js", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "start": "cross-env TRACE_LEVEL=log npm run build:node && node -i -e 'YAML=require(\".\")'", "test": "jest", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest", "test:types": "tsc --lib ES2017 --noEmit tests/typings.ts", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "> 0.5%, not dead", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "babel-eslint": "^10.1.0", "babel-jest": "^26.6.3", "babel-plugin-trace": "^1.1.0", "common-tags": "^1.8.0", "cross-env": "^7.0.3", "eslint": "^7.19.0", "eslint-config-prettier": "^7.2.0", "fast-check": "^2.12.0", "jest": "^26.6.3", "prettier": "^2.2.1", "rollup": "^2.38.2", "typescript": "^4.1.3"}, "engines": {"node": ">= 6"}, "gitHead": "8ef015788b219a4b249736f4bb8968dafe68dcc4", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.10.1", "_nodeVersion": "15.11.0", "_npmVersion": "7.6.0", "dist": {"shasum": "bb13d805ed104fba38f533570f3441027eeeca22", "size": 95254, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.10.1.tgz", "integrity": "sha512-z/asvd+V08l1ywhaemZVirCwjdzLo6O1/0j2JbYCsGjiezupNQqjs5IIPyNtctbHjPEckqzVGd4jvpU5Lr25vQ=="}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.10.1_1615633437918_0.07375426558649667"}, "_hasShrinkwrap": false, "publish_time": 1615633438138, "_cnpm_publish_time": 1615633438138, "_cnpmcore_publish_time": "2021-12-13T13:14:56.621Z"}, "2.0.0-3": {"name": "yaml", "version": "2.0.0-3", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./index.js", "browser": {"./index.js": "./browser/index.js", "./types.js": "./browser/dist/types.js", "./types.mjs": "./browser/dist/types.js", "./util.js": "./browser/dist/util.js", "./util.mjs": "./browser/dist/util.js"}, "exports": {".": {"node": "./index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./types": {"node": {"import": "./types.mjs", "require": "./types.js"}, "default": "./browser/dist/types.js"}, "./util": {"node": {"import": "./util.mjs", "require": "./util.js"}, "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c rollup.browser-config.js", "build:node": "rollup -c rollup.node-config.js", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "start": "cross-env TRACE_LEVEL=log npm run build:node && node -i -e 'YAML=require(\".\")'", "test": "jest", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest", "test:types": "tsc --lib ES2017 --noEmit tests/typings.ts", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "> 0.5%, not dead, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "babel-eslint": "^10.1.0", "babel-jest": "^26.6.3", "babel-plugin-trace": "^1.1.0", "common-tags": "^1.8.0", "cross-env": "^7.0.3", "eslint": "^7.19.0", "eslint-config-prettier": "^7.2.0", "fast-check": "^2.12.0", "jest": "^26.6.3", "prettier": "^2.2.1", "rollup": "^2.38.2", "typescript": "^4.1.3"}, "engines": {"node": ">= 10"}, "readmeFilename": "README.md", "gitHead": "b2b35758b45e0cfc30be7443b42adc71b40b193d", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.0.0-3", "_nodeVersion": "14.7.0", "_npmVersion": "6.14.7", "dist": {"shasum": "dc126f2fac9b845ee696ccbd13c8d303e247c471", "size": 76701, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.0.0-3.tgz", "integrity": "sha512-gvtVaY+/mQ0OsXgaWy2Tf830JuXN7qEUYdXWsuiJVSkMRsBBQ90YVpQQofaURbhoA1xSbLBf7965oH6ddzNbBQ=="}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.0.0-3_1612128520803_0.25239068729749325"}, "_hasShrinkwrap": false, "publish_time": 1612128520953, "_cnpm_publish_time": 1612128520953, "_cnpmcore_publish_time": "2021-12-13T13:14:57.160Z"}, "2.0.0-2": {"name": "yaml", "version": "2.0.0-2", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./index.js", "browser": {"./index.js": "./browser/index.js", "./types.js": "./browser/dist/types.js", "./types.mjs": "./browser/dist/types.js", "./util.js": "./browser/dist/util.js", "./util.mjs": "./browser/dist/util.js"}, "exports": {".": {"node": "./index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./types": {"node": {"import": "./types.mjs", "require": "./types.js"}, "default": "./browser/dist/types.js"}, "./util": {"node": {"import": "./util.mjs", "require": "./util.js"}, "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c rollup.browser-config.js", "build:node": "rollup -c rollup.node-config.js", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "start": "cross-env TRACE_LEVEL=log npm run build:node && node -i -e 'YAML=require(\".\")'", "test": "jest", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest", "test:types": "tsc --lib ES2017 --noEmit tests/typings.ts", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "> 0.5%, not dead, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "babel-eslint": "^10.1.0", "babel-jest": "^26.6.3", "babel-plugin-trace": "^1.1.0", "common-tags": "^1.8.0", "cross-env": "^7.0.3", "eslint": "^7.19.0", "eslint-config-prettier": "^7.2.0", "fast-check": "^2.12.0", "jest": "^26.6.3", "prettier": "^2.2.1", "rollup": "^2.38.2", "typescript": "^4.1.3"}, "engines": {"node": ">= 10"}, "readmeFilename": "README.md", "gitHead": "bfd88551df003087000599ff7cc8cd79e8968731", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.0.0-2", "_nodeVersion": "14.7.0", "_npmVersion": "6.14.7", "dist": {"shasum": "1cb85be774b4b133d522cceef3dd841f22f5759f", "size": 76729, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.0.0-2.tgz", "integrity": "sha512-owqW8Vw4Zi16SxpQt30O9cCcvZcKmZzUvPYm7BAeNQY4lzMTGFDkI7IP+wKtDCuY5hSp5kxl8JseBHlTBkYkiQ=="}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.0.0-2_1612125228770_0.5865248696921088"}, "_hasShrinkwrap": false, "publish_time": 1612125228902, "_cnpm_publish_time": 1612125228902, "_cnpmcore_publish_time": "2021-12-13T13:14:57.655Z"}, "2.0.0-1": {"name": "yaml", "version": "2.0.0-1", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./index.js", "browser": {"./index.js": "./browser/index.js", "./parse-cst.js": "./browser/parse-cst.js", "./types.js": "./browser/types.js", "./types.mjs": "./browser/types.js", "./util.js": "./browser/util.js", "./util.mjs": "./browser/util.js"}, "exports": {".": "./index.js", "./package.json": "./package.json", "./parse-cst": "./parse-cst.js", "./types": [{"import": "./types.mjs"}, "./types.js"], "./util": [{"import": "./util.mjs"}, "./util.js"]}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c rollup.browser-config.js", "build:node": "rollup -c rollup.node-config.js", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "start": "cross-env TRACE_LEVEL=log npm run build:node && node -i -e 'YAML=require(\".\")'", "test": "jest", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest", "test:types": "tsc --lib ES2017 --noEmit tests/typings.ts", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "> 0.5%, not dead", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.11.6", "@babel/plugin-proposal-class-properties": "^7.10.4", "@babel/preset-env": "^7.11.5", "@rollup/plugin-babel": "^5.2.1", "babel-eslint": "^10.1.0", "babel-jest": "^26.5.0", "babel-plugin-trace": "^1.1.0", "common-tags": "^1.8.0", "cross-env": "^7.0.2", "eslint": "^7.10.0", "eslint-config-prettier": "^6.12.0", "fast-check": "^2.4.0", "jest": "^26.5.0", "prettier": "^2.1.2", "rollup": "^2.28.2", "typescript": "^4.0.3"}, "engines": {"node": ">= 6"}, "gitHead": "8dbce5235dcfb366959c4c5249ad9875daeeb7d4", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.0.0-1", "_nodeVersion": "14.7.0", "_npmVersion": "6.14.7", "dist": {"shasum": "8c3029b3ee2028306d5bcf396980623115ff8d18", "size": 102403, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.0.0-1.tgz", "integrity": "sha512-W7h5dEhywMKenDJh2iX/LABkbFnBxasD27oyXWDS/feDsxiw0dD5ncXdYXgkvAsXIY2MpW/ZKkr9IU30DBdMNQ=="}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.0.0-1_1601936936688_0.7571554792879898"}, "_hasShrinkwrap": false, "publish_time": 1601936936807, "_cnpm_publish_time": 1601936936807, "_cnpmcore_publish_time": "2021-12-13T13:14:58.125Z"}, "2.0.0-0": {"name": "yaml", "version": "2.0.0-0", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./index.js", "browser": {"./index.js": "./browser/index.js", "./parse-cst.js": "./browser/parse-cst.js", "./types.js": "./browser/types.js", "./types.mjs": "./browser/types.js", "./util.js": "./browser/util.js", "./util.mjs": "./browser/util.js"}, "exports": {".": "./index.js", "./package.json": "./package.json", "./parse-cst": "./parse-cst.js", "./types": [{"import": "./types.mjs"}, "./types.js"], "./util": [{"import": "./util.mjs"}, "./util.js"]}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c rollup.browser-config.js", "build:node": "rollup -c rollup.node-config.js", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "start": "cross-env TRACE_LEVEL=log npm run build:node && node -i -e 'YAML=require(\".\")'", "test": "jest", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest", "test:types": "tsc --lib ES2017 --noEmit tests/typings.ts", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "> 0.5%, not dead", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.11.4", "@babel/plugin-proposal-class-properties": "^7.10.4", "@babel/preset-env": "^7.11.0", "@rollup/plugin-babel": "^5.2.0", "babel-eslint": "^10.1.0", "babel-jest": "^26.3.0", "babel-plugin-trace": "^1.1.0", "common-tags": "^1.8.0", "cross-env": "^7.0.2", "eslint": "^7.7.0", "eslint-config-prettier": "^6.11.0", "fast-check": "^2.2.0", "jest": "^26.4.2", "prettier": "^2.0.5", "rollup": "^2.26.5", "typescript": "^4.0.2"}, "engines": {"node": ">= 6"}, "readmeFilename": "README.md", "gitHead": "0a4d4cc3fd821807b652aa73454983d29b429210", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.0.0-0", "_nodeVersion": "14.7.0", "_npmVersion": "6.14.7", "dist": {"shasum": "432108d836b6715fd33af2e324a8d3980e96f7fe", "size": 93558, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.0.0-0.tgz", "integrity": "sha512-+n+AwzE2hK2j21p36UB2foND6RYQU4PQkdPCK4YF2OKz8BbiNI+NvKS79CpyDChw3k/0L9jnChkjEzKoiu2C7w=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}], "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.0.0-0_1598194935750_0.7647945892021262"}, "_hasShrinkwrap": false, "publish_time": 1598194935918, "_cnpm_publish_time": 1598194935918, "_cnpmcore_publish_time": "2021-12-13T13:14:58.696Z"}, "1.10.0": {"name": "yaml", "version": "1.10.0", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./index.js", "browser": {"./index.js": "./browser/index.js", "./map.js": "./browser/map.js", "./pair.js": "./browser/pair.js", "./parse-cst.js": "./browser/parse-cst.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./seq.js": "./browser/seq.js", "./types.js": "./browser/types.js", "./types.mjs": "./browser/types.js", "./types/binary.js": "./browser/types/binary.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/set.js": "./browser/types/set.js", "./types/timestamp.js": "./browser/types/timestamp.js", "./util.js": "./browser/util.js", "./util.mjs": "./browser/util.js"}, "exports": {".": "./index.js", "./parse-cst": "./parse-cst.js", "./types": [{"import": "./types.mjs"}, "./types.js"], "./util": [{"import": "./util.mjs"}, "./util.js"], "./": "./"}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c rollup.browser-config.js", "build:node": "rollup -c rollup.node-config.js", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "start": "cross-env TRACE_LEVEL=log npm run build:node && node -i -e 'YAML=require(\".\")'", "test": "jest", "test:dist": "npm run build:node && jest", "test:types": "tsc --lib ES2017 --noEmit tests/typings.ts", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "> 0.5%, not dead", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.9.6", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.0", "babel-eslint": "^10.1.0", "babel-jest": "^26.0.1", "babel-plugin-trace": "^1.1.0", "common-tags": "^1.8.0", "cross-env": "^7.0.2", "eslint": "^7.0.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.3", "fast-check": "^1.24.2", "jest": "^26.0.1", "prettier": "^2.0.5", "rollup": "^2.10.2", "typescript": "^3.9.2"}, "engines": {"node": ">= 6"}, "gitHead": "56b873be923015bb367990f04578b6ee9895bf6e", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.10.0", "_nodeVersion": "13.10.1", "_npmVersion": "6.14.2", "dist": {"shasum": "3b593add944876077d4d683fee01081bd9fff31e", "size": 94470, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.10.0.tgz", "integrity": "sha512-yr2icI4glYaNG+KWONODapy2/jDdMSDnrONSjblABjD9B4Z5LgiircSt8m8sRZFNi08kG9Sm0uSHtEmP3zaEGg=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}], "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.10.0_1589622987762_0.2502280120579963"}, "_hasShrinkwrap": false, "publish_time": 1589622987877, "_cnpm_publish_time": 1589622987877, "_cnpmcore_publish_time": "2021-12-13T13:14:59.208Z"}, "1.9.2": {"name": "yaml", "version": "1.9.2", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./index.js", "browser": {"./index.js": "./browser/index.js", "./map.js": "./browser/map.js", "./pair.js": "./browser/pair.js", "./parse-cst.js": "./browser/parse-cst.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./seq.js": "./browser/seq.js", "./types.js": "./browser/types.js", "./types.mjs": "./browser/types.js", "./types/binary.js": "./browser/types/binary.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/set.js": "./browser/types/set.js", "./types/timestamp.js": "./browser/types/timestamp.js", "./util.js": "./browser/util.js", "./util.mjs": "./browser/util.js"}, "exports": {".": "./index.js", "./": "./", "./parse-cst": "./parse-cst.js", "./types": [{"default": "./types.js", "import": "./types.mjs"}, "./types.js"], "./util": [{"default": "./util.js", "import": "./util.mjs"}, "./util.js"]}, "scripts": {"browser:build": "cross-env BABEL_ENV=browser babel src/ --out-dir browser/dist/", "clean": "git clean -fdxe node_modules", "dist:build": "babel src/ --out-dir dist/", "build": "npm run dist:build && npm run browser:build", "prettier": "prettier --write .", "lint": "eslint src/", "start": "npm run dist:build && node -i -e 'YAML=require(\".\")'", "test": "cross-env TRACE_LEVEL=log jest", "test:trace": "cross-env TRACE_LEVEL=trace,log jest --no-cache", "test:types": "tsc --lib ES2017 --noEmit tests/typings.ts", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "> 0.5%, not dead", "jest": {"collectCoverageFrom": ["src/**/*.js"], "testMatch": ["**/tests/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.0", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-transform-runtime": "^7.9.0", "@babel/preset-env": "^7.9.5", "babel-eslint": "^10.1.0", "babel-jest": "^25.3.0", "babel-plugin-trace": "^1.1.0", "common-tags": "^1.8.0", "cross-env": "^7.0.2", "eslint": "^6.8.0", "eslint-config-prettier": "^6.10.1", "eslint-plugin-prettier": "^3.1.3", "fast-check": "^1.24.1", "jest": "^25.3.0", "prettier": "^2.0.4", "typescript": "^3.8.3"}, "dependencies": {"@babel/runtime": "^7.9.2"}, "engines": {"node": ">= 6"}, "gitHead": "0013206868966868a539efae6566e11a7e6811c9", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.9.2", "_nodeVersion": "13.10.1", "_npmVersion": "6.14.2", "dist": {"shasum": "f0cfa865f003ab707663e4f04b3956957ea564ed", "size": 95669, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.9.2.tgz", "integrity": "sha512-HPT7cGGI0DuRcsO51qC1j9O16Dh1mZ2bnXwsi0jrSpsLz0WxOLSLXfkABVl6bZO629py3CU+OMJtpNHDLB97kg=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}], "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.9.2_1587374880832_0.4887206141569671"}, "_hasShrinkwrap": false, "publish_time": 1587374881007, "_cnpm_publish_time": 1587374881007, "_cnpmcore_publish_time": "2021-12-13T13:14:59.738Z"}, "1.9.1": {"name": "yaml", "version": "1.9.1", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./index.js", "browser": {"./index.js": "./browser/index.js", "./map.js": "./browser/map.js", "./pair.js": "./browser/pair.js", "./parse-cst.js": "./browser/parse-cst.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./seq.js": "./browser/seq.js", "./types.js": "./browser/types.js", "./types.mjs": "./browser/types.js", "./types/binary.js": "./browser/types/binary.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/set.js": "./browser/types/set.js", "./types/timestamp.js": "./browser/types/timestamp.js", "./util.js": "./browser/util.js", "./util.mjs": "./browser/util.js"}, "exports": {".": "./index.js", "./": "./", "./parse-cst": "./parse-cst.js", "./types": [{"default": "./types.js", "import": "./types.mjs"}, "./types.js"], "./util": [{"default": "./util.js", "import": "./util.mjs"}, "./util.js"]}, "scripts": {"browser:build": "cross-env BABEL_ENV=browser babel src/ --out-dir browser/dist/", "clean": "git clean -fdxe node_modules", "dist:build": "babel src/ --out-dir dist/", "build": "npm run dist:build && npm run browser:build", "prettier": "prettier --write .", "lint": "eslint src/", "start": "npm run dist:build && node -i -e 'YAML=require(\".\")'", "test": "cross-env TRACE_LEVEL=log jest", "test:trace": "cross-env TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "> 0.5%, not dead", "jest": {"collectCoverageFrom": ["src/**/*.js"], "testMatch": ["**/tests/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.0", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-transform-runtime": "^7.9.0", "@babel/preset-env": "^7.9.5", "babel-eslint": "^10.1.0", "babel-jest": "^25.3.0", "babel-plugin-trace": "^1.1.0", "common-tags": "^1.8.0", "cross-env": "^7.0.2", "eslint": "^6.8.0", "eslint-config-prettier": "^6.10.1", "eslint-plugin-prettier": "^3.1.3", "fast-check": "^1.24.1", "jest": "^25.3.0", "prettier": "^2.0.4"}, "dependencies": {"@babel/runtime": "^7.9.2"}, "engines": {"node": ">= 6"}, "gitHead": "c42c25e8bb687b1e7af5943b2e44e80f193f723f", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.9.1", "_nodeVersion": "13.10.1", "_npmVersion": "6.14.2", "dist": {"shasum": "2df608ca571a0cf94e25e417e2795c08f48acdc5", "size": 95626, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.9.1.tgz", "integrity": "sha512-xbWX1ayUVoW8DPM8qxOBowac4XxSTi0mFLbiokRq880ViYglN+F3nJ4Dc2GdypXpykrknKS39d8I3lzFoHv1kA=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}], "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.9.1_1587211292759_0.5095083774998896"}, "_hasShrinkwrap": false, "publish_time": 1587211292964, "_cnpm_publish_time": 1587211292964, "_cnpmcore_publish_time": "2021-12-13T13:15:00.335Z"}, "1.9.0": {"name": "yaml", "version": "1.9.0", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./index.js", "browser": {"./index.js": "./browser/index.js", "./map.js": "./browser/map.js", "./pair.js": "./browser/pair.js", "./parse-cst.js": "./browser/parse-cst.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./seq.js": "./browser/seq.js", "./types.js": "./browser/types.js", "./types.mjs": "./browser/types.js", "./types/binary.js": "./browser/types/binary.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/set.js": "./browser/types/set.js", "./types/timestamp.js": "./browser/types/timestamp.js", "./util.js": "./browser/util.js", "./util.mjs": "./browser/util.js"}, "exports": {".": "./index.js", "./": "./", "./parse-cst": "./parse-cst.js", "./types": [{"default": "./types.js", "import": "./types.mjs"}, "./types.js"], "./util": [{"default": "./util.js", "import": "./util.mjs"}, "./util.js"]}, "scripts": {"browser:build": "cross-env BABEL_ENV=browser babel src/ --out-dir browser/dist/", "clean": "git clean -fdxe node_modules", "dist:build": "babel src/ --out-dir dist/", "build": "npm run dist:build && npm run browser:build", "prettier": "prettier --write .", "lint": "eslint src/", "start": "npm run dist:build && node -i -e 'YAML=require(\".\")'", "test": "cross-env TRACE_LEVEL=log jest", "test:trace": "cross-env TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "> 0.5%, not dead", "jest": {"collectCoverageFrom": ["src/**/*.js"], "testMatch": ["**/tests/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.0", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-transform-runtime": "^7.9.0", "@babel/preset-env": "^7.9.0", "babel-eslint": "^10.1.0", "babel-jest": "^25.1.0", "babel-plugin-trace": "^1.1.0", "common-tags": "^1.8.0", "cross-env": "^7.0.2", "eslint": "^6.8.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-prettier": "^3.1.2", "fast-check": "^1.23.0", "jest": "^25.1.0", "prettier": "^2.0.2"}, "dependencies": {"@babel/runtime": "^7.9.0"}, "engines": {"node": ">= 6"}, "gitHead": "02f9e13620946ee0a7774e068b2083da5dd3721b", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.9.0", "_nodeVersion": "13.10.1", "_npmVersion": "6.14.2", "dist": {"shasum": "dc1ff3e24837b62bc3c8ae02c28e16ee5742b9d6", "size": 95341, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.9.0.tgz", "integrity": "sha512-3GLZOj8A9Gsp0Fw3kOyj0zqk4xMq+YvhbHSDYALd2NMOfIpyZeBhz32ZiNU7AtX1MtXX/9JJgxSElGRwvv9enA=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}], "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.9.0_1587141233959_0.9280112351329557"}, "_hasShrinkwrap": false, "publish_time": 1587141234113, "_cnpm_publish_time": 1587141234113, "_cnpmcore_publish_time": "2021-12-13T13:15:00.984Z"}, "1.8.3": {"name": "yaml", "version": "1.8.3", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./index.js", "browser": {"./index.js": "./browser/index.js", "./map.js": "./browser/map.js", "./pair.js": "./browser/pair.js", "./parse-cst.js": "./browser/parse-cst.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./seq.js": "./browser/seq.js", "./types.js": "./browser/types.js", "./types.mjs": "./browser/types.js", "./types/binary.js": "./browser/types/binary.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/set.js": "./browser/types/set.js", "./types/timestamp.js": "./browser/types/timestamp.js", "./util.js": "./browser/util.js", "./util.mjs": "./browser/util.js"}, "exports": {".": "./index.js", "./": "./", "./parse-cst": "./parse-cst.js", "./types": [{"default": "./types.js", "import": "./types.mjs"}, "./types.js"], "./util": [{"default": "./util.js", "import": "./util.mjs"}, "./util.js"]}, "scripts": {"browser:build": "BABEL_ENV=browser babel src/ --out-dir browser/dist/", "browser:copy": "cpy '*.js' '!*.config.js' types/ browser/ --parents", "clean": "git clean -fdxe node_modules", "dist:build": "babel src/ --out-dir dist/", "build": "npm run dist:build && npm run browser:build && npm run browser:copy", "prettier": "prettier --write \"{src,tests}/**/*.js\"", "lint": "eslint src/", "start": "npm run dist:build && node -i -e 'YAML=require(\".\")'", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "> 0.5%, not dead", "jest": {"collectCoverageFrom": ["src/**/*.js"], "testMatch": ["**/tests/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.8.7", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/preset-env": "^7.8.7", "babel-eslint": "^10.1.0", "babel-jest": "^25.1.0", "babel-plugin-trace": "^1.1.0", "common-tags": "^1.8.0", "cpy-cli": "^3.1.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-prettier": "^3.1.2", "fast-check": "^1.23.0", "jest": "^25.1.0", "prettier": "^1.19.1"}, "dependencies": {"@babel/runtime": "^7.8.7"}, "engines": {"node": ">= 6"}, "gitHead": "21d6a86975660162c1a5080fe198d8d29ddf17df", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.8.3", "_nodeVersion": "13.10.1", "_npmVersion": "6.14.2", "dist": {"shasum": "2f420fca58b68ce3a332d0ca64be1d191dd3f87a", "size": 83542, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.8.3.tgz", "integrity": "sha512-X/v7VDnK+sxbQ2Imq4Jt2PRUsRsP7UcpSl3Llg6+NRRqWLIvxkMFYtH1FmvwNGYRKKPa+EPA4qDBlI9WVG1UKw=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}], "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.8.3_1584751882929_0.9107149528040019"}, "_hasShrinkwrap": false, "publish_time": 1584751883126, "_cnpm_publish_time": 1584751883126, "_cnpmcore_publish_time": "2021-12-13T13:15:01.591Z"}, "1.8.2": {"name": "yaml", "version": "1.8.2", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./index.js", "browser": {"./index.js": "./browser/index.js", "./map.js": "./browser/map.js", "./pair.js": "./browser/pair.js", "./parse-cst.js": "./browser/parse-cst.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./seq.js": "./browser/seq.js", "./types.js": "./browser/types.js", "./types/binary.js": "./browser/types/binary.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/set.js": "./browser/types/set.js", "./types/timestamp.js": "./browser/types/timestamp.js", "./util.js": "./browser/util.js"}, "exports": {".": "./index.js", "./": "./", "./parse-cst": "./parse-cst.js", "./types": [{"default": "./types.js", "import": "./types.mjs"}, "./types.js"], "./util": [{"default": "./util.js", "import": "./util.mjs"}, "./util.js"]}, "scripts": {"browser:build": "BABEL_ENV=browser babel src/ --out-dir browser/dist/", "browser:copy": "cpy '*.js' '!*.config.js' types/ browser/ --parents", "clean": "git clean -fdxe node_modules", "dist:build": "babel src/ --out-dir dist/", "build": "npm run dist:build && npm run browser:build && npm run browser:copy", "prettier": "prettier --write \"{src,tests}/**/*.js\"", "lint": "eslint src/", "start": "npm run dist:build && node -i -e 'YAML=require(\".\")'", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "> 0.5%, not dead", "jest": {"collectCoverageFrom": ["src/**/*.js"], "testMatch": ["**/tests/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.8.7", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/preset-env": "^7.8.7", "babel-eslint": "^10.1.0", "babel-jest": "^25.1.0", "babel-plugin-trace": "^1.1.0", "common-tags": "^1.8.0", "cpy-cli": "^3.1.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-prettier": "^3.1.2", "fast-check": "^1.23.0", "jest": "^25.1.0", "prettier": "^1.19.1"}, "dependencies": {"@babel/runtime": "^7.8.7"}, "engines": {"node": ">= 6"}, "gitHead": "3f53937a5911ecc09a2eb12fb12a0b74dc5e7c9c", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.8.2", "_nodeVersion": "13.10.1", "_npmVersion": "6.14.2", "dist": {"shasum": "a29c03f578faafd57dcb27055f9a5d569cb0c3d9", "size": 83488, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.8.2.tgz", "integrity": "sha512-omakb0d7FjMo3R1D2EbTKVIk6dAVLRxFXdLZMEUToeAvuqgG/YuHMuQOZ5fgk+vQ8cx+cnGKwyg+8g8PNT0xQg=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}], "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.8.2_1583905904852_0.5659281491877828"}, "_hasShrinkwrap": false, "publish_time": 1583905905069, "_cnpm_publish_time": 1583905905069, "_cnpmcore_publish_time": "2021-12-13T13:15:02.200Z"}, "1.8.1": {"name": "yaml", "version": "1.8.1", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./index.js", "browser": {"./index.js": "./browser/index.js", "./map.js": "./browser/map.js", "./pair.js": "./browser/pair.js", "./parse-cst.js": "./browser/parse-cst.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./seq.js": "./browser/seq.js", "./types.js": "./browser/types.js", "./types/binary.js": "./browser/types/binary.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/set.js": "./browser/types/set.js", "./types/timestamp.js": "./browser/types/timestamp.js", "./util.js": "./browser/util.js"}, "exports": {".": "./index.js", "./parse-cst": "./parse-cst.js", "./types": {"default": "./types.js", "import": "./types.mjs"}, "./util": {"default": "./util.js", "import": "./util.mjs"}}, "scripts": {"browser:build": "BABEL_ENV=browser babel src/ --out-dir browser/dist/", "browser:copy": "cpy '*.js' '!*.config.js' types/ browser/ --parents", "clean": "git clean -fdxe node_modules", "dist:build": "babel src/ --out-dir dist/", "build": "npm run dist:build && npm run browser:build && npm run browser:copy", "prettier": "prettier --write \"{src,tests}/**/*.js\"", "lint": "eslint src/", "start": "npm run dist:build && node -i -e 'YAML=require(\".\")'", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "> 0.5%, not dead", "jest": {"collectCoverageFrom": ["src/**/*.js"], "testMatch": ["**/tests/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.8.7", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/preset-env": "^7.8.7", "babel-eslint": "^10.1.0", "babel-jest": "^25.1.0", "babel-plugin-trace": "^1.1.0", "common-tags": "^1.8.0", "cpy-cli": "^3.1.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-prettier": "^3.1.2", "fast-check": "^1.23.0", "jest": "^25.1.0", "prettier": "^1.19.1"}, "dependencies": {"@babel/runtime": "^7.8.7"}, "engines": {"node": ">= 6"}, "gitHead": "99f0ebc93a883c815f60a3ac30e0ef6f60fc4deb", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.8.1", "_nodeVersion": "13.10.1", "_npmVersion": "6.14.2", "dist": {"shasum": "3a8cdb877ec9da2350f22b476a117e28e30069d8", "size": 83473, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.8.1.tgz", "integrity": "sha512-vIXHJILY3e2Ru5s+hFwmO0fSHo0zm30AJ/eBaIUd/54xVocvjzix4bPOtjIGxKm5VDSOt5psTKW6CEv3WHzWdg=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}], "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.8.1_1583901754991_0.8059014978576564"}, "_hasShrinkwrap": false, "publish_time": 1583901755186, "_cnpm_publish_time": 1583901755186, "_cnpmcore_publish_time": "2021-12-13T13:15:02.883Z"}, "1.8.0": {"name": "yaml", "version": "1.8.0", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./index.js", "browser": {"./index.js": "./browser/index.js", "./map.js": "./browser/map.js", "./pair.js": "./browser/pair.js", "./parse-cst.js": "./browser/parse-cst.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./seq.js": "./browser/seq.js", "./types.js": "./browser/types.js", "./types/binary.js": "./browser/types/binary.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/set.js": "./browser/types/set.js", "./types/timestamp.js": "./browser/types/timestamp.js", "./util.js": "./browser/util.js"}, "exports": {".": "./index.js", "./parse-cst": "./parse-cst.js", "./types": {"require": "./types.js", "import": "./types.mjs"}, "./util": {"require": "./util.js", "import": "./util.mjs"}}, "scripts": {"browser:build": "BABEL_ENV=browser babel src/ --out-dir browser/dist/", "browser:copy": "cpy '*.js' '!*.config.js' types/ browser/ --parents", "clean": "git clean -fdxe node_modules", "dist:build": "babel src/ --out-dir dist/", "build": "npm run dist:build && npm run browser:build && npm run browser:copy", "prettier": "prettier --write \"{src,tests}/**/*.js\"", "lint": "eslint src/", "start": "npm run dist:build && node -i -e 'YAML=require(\".\")'", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "> 0.5%, not dead", "jest": {"collectCoverageFrom": ["src/**/*.js"], "testMatch": ["**/tests/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.8.7", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/preset-env": "^7.8.7", "babel-eslint": "^10.1.0", "babel-jest": "^25.1.0", "babel-plugin-trace": "^1.1.0", "common-tags": "^1.8.0", "cpy-cli": "^3.1.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-prettier": "^3.1.2", "fast-check": "^1.23.0", "jest": "^25.1.0", "prettier": "^1.19.1"}, "dependencies": {"@babel/runtime": "^7.8.7"}, "engines": {"node": ">= 6"}, "gitHead": "a8c465b83ccff0dabef36c603b1ba66b70aa969e", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.8.0", "_nodeVersion": "13.10.1", "_npmVersion": "6.14.2", "dist": {"shasum": "169fbcfa2081302dc9441d02b0b6fe667e4f74c9", "size": 83475, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.8.0.tgz", "integrity": "sha512-6qI/tTx7OVtA4qNqD0OyutbM6Z9EKu4rxWm/2Y3FDEBQ4/2X2XAnyuRXMzAE2+1BPyqzksJZtrIwblOHg0IEzA=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}], "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.8.0_1583609951610_0.28173500730412715"}, "_hasShrinkwrap": false, "publish_time": 1583609951743, "_cnpm_publish_time": 1583609951743, "_cnpmcore_publish_time": "2021-12-13T13:15:03.563Z"}, "1.7.2": {"name": "yaml", "version": "1.7.2", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "main": "./index.js", "browser": {"./index.js": "./browser/index.js", "./map.js": "./browser/map.js", "./pair.js": "./browser/pair.js", "./parse-cst.js": "./browser/parse-cst.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./seq.js": "./browser/seq.js", "./types.js": "./browser/types.js", "./types/binary.js": "./browser/types/binary.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/set.js": "./browser/types/set.js", "./types/timestamp.js": "./browser/types/timestamp.js", "./util.js": "./browser/util.js"}, "scripts": {"browser:build": "BABEL_ENV=browser babel src/ --out-dir browser/dist/", "browser:copy": "cpy '*.js' '!*.config.js' types/ browser/ --parents", "clean": "git clean -fdxe node_modules", "dist:build": "babel src/ --out-dir dist/", "build": "npm run dist:build && npm run browser:build && npm run browser:copy", "prettier": "prettier --write \"{src,tests}/**/*.js\"", "lint": "eslint src/", "start": "npm run dist:build && node -i -e 'YAML=require(\".\")'", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "> 0.5%, not dead", "jest": {"testMatch": ["**/tests/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.6.4", "@babel/core": "^7.6.4", "@babel/plugin-proposal-class-properties": "^7.5.5", "@babel/plugin-transform-runtime": "^7.6.2", "@babel/preset-env": "^7.6.3", "babel-eslint": "^10.0.3", "babel-jest": "^24.9.0", "babel-plugin-trace": "^1.1.0", "common-tags": "^1.8.0", "cpy-cli": "^2.0.0", "eslint": "^6.5.1", "eslint-config-prettier": "^6.4.0", "eslint-plugin-prettier": "^3.1.1", "fast-check": "^1.17.0", "jest": "^24.9.0", "prettier": "^1.18.2"}, "dependencies": {"@babel/runtime": "^7.6.3"}, "engines": {"node": ">= 6"}, "gitHead": "752d9e0f4ba0b38e203e8a731b926d8b81e5f9e4", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.7.2", "_nodeVersion": "10.16.3", "_npmVersion": "6.9.0", "dist": {"shasum": "f26aabf738590ab61efaca502358e48dc9f348b2", "size": 98232, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.7.2.tgz", "integrity": "sha512-qXROVp90sb83XtAoqE8bP9RwAkTTZbugRUTm5YeFCBfNRPEp2YzTeqWiz7m5OORHzEvrA/qcGS8hp/E+MMROYw=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}], "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.7.2_1571138650658_0.6368482969622014"}, "_hasShrinkwrap": false, "publish_time": 1571138650790, "_cnpm_publish_time": 1571138650790, "_cnpmcore_publish_time": "2021-12-13T13:15:04.186Z"}, "1.7.1": {"name": "yaml", "version": "1.7.1", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "main": "./index.js", "browser": {"./index.js": "./browser/index.js", "./map.js": "./browser/map.js", "./pair.js": "./browser/pair.js", "./parse-cst.js": "./browser/parse-cst.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./seq.js": "./browser/seq.js", "./types.js": "./browser/types.js", "./types/binary.js": "./browser/types/binary.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/set.js": "./browser/types/set.js", "./types/timestamp.js": "./browser/types/timestamp.js", "./util.js": "./browser/util.js"}, "scripts": {"browser:build": "BABEL_ENV=browser babel src/ --out-dir browser/dist/", "browser:copy": "cpy '*.js' '!*.config.js' types/ browser/ --parents", "clean": "git clean -fdxe node_modules", "dist:build": "babel src/ --out-dir dist/", "build": "npm run dist:build && npm run browser:build && npm run browser:copy", "prettier": "prettier --write \"{src,tests}/**/*.js\"", "lint": "eslint src/", "start": "npm run dist:build && node -i -e 'YAML=require(\".\")'", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "> 0.5%, not dead", "jest": {"testMatch": ["**/tests/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-proposal-class-properties": "^7.5.5", "@babel/plugin-transform-runtime": "^7.5.5", "@babel/preset-env": "^7.5.5", "babel-eslint": "^10.0.3", "babel-jest": "^24.9.0", "babel-plugin-trace": "^1.1.0", "common-tags": "^1.8.0", "cpy-cli": "^2.0.0", "eslint": "^6.2.2", "eslint-config-prettier": "^6.1.0", "eslint-plugin-prettier": "^3.1.0", "fast-check": "^1.16.2", "jest": "^24.9.0", "prettier": "^1.18.2"}, "dependencies": {"@babel/runtime": "^7.5.5"}, "engines": {"node": ">= 6"}, "gitHead": "ebc640a0b127caad8edb91a098dd410171fdd5fc", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.7.1", "_nodeVersion": "10.16.3", "_npmVersion": "6.9.0", "dist": {"shasum": "86db1b39a6434a7928d3750cbe08303a50a24d6b", "size": 97830, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.7.1.tgz", "integrity": "sha512-sR0mJ2C3LVBgMST+0zrrrsKSijbw64bfHmTt4nEXLZTZFyIAuUVARqA/LO5kaZav2OVQMaZ+5WRrZv7QjxG3uQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}], "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.7.1_1570451991117_0.25252138960074433"}, "_hasShrinkwrap": false, "publish_time": 1570451991322, "_cnpm_publish_time": 1570451991322, "_cnpmcore_publish_time": "2021-12-13T13:15:04.790Z"}, "1.7.0": {"name": "yaml", "version": "1.7.0", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "main": "./index.js", "browser": {"./index.js": "./browser/index.js", "./map.js": "./browser/map.js", "./pair.js": "./browser/pair.js", "./parse-cst.js": "./browser/parse-cst.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./seq.js": "./browser/seq.js", "./types.js": "./browser/types.js", "./types/binary.js": "./browser/types/binary.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/set.js": "./browser/types/set.js", "./types/timestamp.js": "./browser/types/timestamp.js", "./util.js": "./browser/util.js"}, "scripts": {"browser:build": "BABEL_ENV=browser babel src/ --out-dir browser/dist/", "browser:copy": "cpy '*.js' '!*.config.js' types/ browser/ --parents", "dist:build": "babel src/ --out-dir dist/", "build": "npm run dist:build && npm run browser:build && npm run browser:copy", "prettier": "prettier --write \"{src,tests}/**/*.js\"", "lint": "eslint src/", "start": "npm run dist:build && node -i -e 'YAML=require(\".\")'", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm test && npm run build"}, "browserslist": "> 0.5%, not dead", "jest": {"testMatch": ["**/tests/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-proposal-class-properties": "^7.5.5", "@babel/plugin-transform-runtime": "^7.5.5", "@babel/preset-env": "^7.5.5", "babel-eslint": "^10.0.3", "babel-jest": "^24.9.0", "babel-plugin-trace": "^1.1.0", "common-tags": "^1.8.0", "cpy-cli": "^2.0.0", "eslint": "^6.2.2", "eslint-config-prettier": "^6.1.0", "eslint-plugin-prettier": "^3.1.0", "fast-check": "^1.16.2", "jest": "^24.9.0", "prettier": "^1.18.2"}, "dependencies": {"@babel/runtime": "^7.5.5"}, "engines": {"node": ">= 6"}, "gitHead": "40acaa76b727b9f3dd028f283e3f10edd8321909", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.7.0", "_nodeVersion": "10.16.3", "_npmVersion": "6.9.0", "dist": {"shasum": "b4cddb83490051e6c4b6ffe2bb08221c23f4c6cf", "size": 100043, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.7.0.tgz", "integrity": "sha512-BEXCJKbbJmDzjuG4At0R4nHJKlP81hxoLQqUCaxzqZ8HHgjAlOXbiOHVCQ4YuQqO/rLR8HoQ6kxGkYJ3tlKIsg=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}], "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.7.0_1569417345729_0.8224342352563792"}, "_hasShrinkwrap": false, "publish_time": 1569417345991, "_cnpm_publish_time": 1569417345991, "_cnpmcore_publish_time": "2021-12-13T13:15:05.560Z"}, "1.6.0": {"name": "yaml", "version": "1.6.0", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "main": "./index.js", "browser": {"./index.js": "./browser/index.js", "./map.js": "./browser/map.js", "./pair.js": "./browser/pair.js", "./parse-cst.js": "./browser/parse-cst.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./seq.js": "./browser/seq.js", "./types.js": "./browser/types.js", "./types/binary.js": "./browser/types/binary.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/set.js": "./browser/types/set.js", "./types/timestamp.js": "./browser/types/timestamp.js", "./util.js": "./browser/util.js"}, "scripts": {"browser:build": "BABEL_ENV=browser babel src/ --out-dir browser/dist/", "browser:copy": "cpy '*.js' '!*.config.js' types/ browser/ --parents", "dist:build": "babel src/ --out-dir dist/", "build": "npm run dist:build && npm run browser:build && npm run browser:copy", "prettier": "prettier --write \"{src,tests}/**/*.js\"", "lint": "eslint src/", "start": "npm run dist:build && node -i -e 'YAML=require(\".\")'", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm test && npm run build"}, "browserslist": "> 0.5%, not dead", "jest": {"testMatch": ["**/tests/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.4.4", "@babel/core": "^7.4.5", "@babel/plugin-proposal-class-properties": "^7.4.4", "@babel/plugin-transform-runtime": "^7.4.4", "@babel/preset-env": "^7.4.5", "babel-eslint": "^10.0.1", "babel-jest": "^24.8.0", "babel-plugin-trace": "^1.1.0", "common-tags": "^1.8.0", "cpy-cli": "^2.0.0", "eslint": "^5.16.0", "eslint-config-prettier": "^4.3.0", "eslint-plugin-prettier": "^3.1.0", "fast-check": "^1.15.1", "jest": "^24.8.0", "prettier": "^1.17.1"}, "dependencies": {"@babel/runtime": "^7.4.5"}, "engines": {"node": ">= 6"}, "gitHead": "f8d35f4b01f97ac42b12f08f7eb5db80f48e2ed4", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.6.0", "_nodeVersion": "11.9.0", "_npmVersion": "6.9.0", "dist": {"shasum": "d8a985cfb26086dd73f91c637f6e6bc909fddd3c", "size": 93777, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.6.0.tgz", "integrity": "sha512-iZfse3lwrJRoSlfs/9KQ9iIXxs9++RvBFVzAqbbBiFT+giYtyanevreF9r61ZTbGMgWQBxAua3FzJiniiJXWWw=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}], "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.6.0_1558620474283_0.07865803018687756"}, "_hasShrinkwrap": false, "publish_time": 1558620474388, "_cnpm_publish_time": 1558620474388, "_cnpmcore_publish_time": "2021-12-13T13:15:06.158Z"}, "1.5.1": {"name": "yaml", "version": "1.5.1", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "main": "./index.js", "browser": {"./index.js": "./browser/index.js", "./map.js": "./browser/map.js", "./pair.js": "./browser/pair.js", "./parse-cst.js": "./browser/parse-cst.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./seq.js": "./browser/seq.js", "./types.js": "./browser/types.js", "./types/binary.js": "./browser/types/binary.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/set.js": "./browser/types/set.js", "./types/timestamp.js": "./browser/types/timestamp.js", "./util.js": "./browser/util.js"}, "scripts": {"browser:build": "BABEL_ENV=browser babel src/ --out-dir browser/dist/", "browser:copy": "cpy '*.js' '!*.config.js' types/ browser/ --parents", "dist:build": "babel src/ --out-dir dist/", "build": "npm run dist:build && npm run browser:build && npm run browser:copy", "prettier": "prettier --write \"{src,tests}/**/*.js\"", "lint": "eslint src/", "start": "npm run dist:build && node -i -e 'YAML=require(\".\")'", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm test && npm run build"}, "browserslist": "> 0.5%, not dead", "jest": {"testMatch": ["**/tests/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.4.4", "@babel/core": "^7.4.4", "@babel/plugin-proposal-class-properties": "^7.4.4", "@babel/plugin-transform-runtime": "^7.4.4", "@babel/preset-env": "^7.4.4", "babel-eslint": "^10.0.1", "babel-jest": "^24.8.0", "babel-plugin-trace": "^1.1.0", "cpy-cli": "^2.0.0", "eslint": "^5.16.0", "eslint-config-prettier": "^4.2.0", "eslint-plugin-prettier": "^3.0.1", "fast-check": "^1.14.0", "jest": "^24.8.0", "prettier": "^1.17.0"}, "dependencies": {"@babel/runtime": "^7.4.4"}, "engines": {"node": ">= 6"}, "gitHead": "9e1c4b96d62f0ada331d426889467d5628e8a4a3", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.5.1", "_nodeVersion": "11.9.0", "_npmVersion": "6.9.0", "dist": {"shasum": "e8201678064fbcfef6afe4122ef802573b6cade8", "size": 93447, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.5.1.tgz", "integrity": "sha512-btfJvMOgVthGZSgHBMrDkLuQu4YxOycw6kwuC67cUEOKJmmNozjIa02eKvuSq7usqqqpwwCvflGTF6JcDvSudw=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}], "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.5.1_1557062063993_0.5923152821557844"}, "_hasShrinkwrap": false, "publish_time": 1557062064175, "_cnpm_publish_time": 1557062064175, "_cnpmcore_publish_time": "2021-12-13T13:15:06.729Z"}, "1.5.0": {"name": "yaml", "version": "1.5.0", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "main": "./index.js", "browser": {"./index.js": "./browser/index.js", "./map.js": "./browser/map.js", "./pair.js": "./browser/pair.js", "./parse-cst.js": "./browser/parse-cst.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./seq.js": "./browser/seq.js", "./types.js": "./browser/types.js", "./types/binary.js": "./browser/types/binary.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/set.js": "./browser/types/set.js", "./types/timestamp.js": "./browser/types/timestamp.js", "./util.js": "./browser/util.js"}, "scripts": {"browser:build": "BABEL_ENV=browser babel src/ --out-dir browser/dist/", "browser:copy": "cpy '*.js' '!*.config.js' types/ browser/ --parents", "dist:build": "babel src/ --out-dir dist/", "build": "npm run dist:build && npm run browser:build && npm run browser:copy", "prettier": "prettier --write \"{src,tests}/**/*.js\"", "lint": "eslint src/", "start": "npm run dist:build && node -i -e 'YAML=require(\".\")'", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm test && npm run build"}, "browserslist": "> 0.5%, not dead", "jest": {"testMatch": ["**/tests/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.4.3", "@babel/core": "^7.4.3", "@babel/plugin-proposal-class-properties": "^7.4.0", "@babel/plugin-transform-runtime": "^7.4.3", "@babel/preset-env": "^7.4.3", "babel-eslint": "^10.0.1", "babel-jest": "^24.7.1", "babel-plugin-trace": "^1.1.0", "cpy-cli": "^2.0.0", "eslint": "^5.16.0", "eslint-config-prettier": "^4.1.0", "eslint-plugin-prettier": "^3.0.1", "fast-check": "^1.13.0", "jest": "^24.7.1", "prettier": "^1.16.4"}, "dependencies": {"@babel/runtime": "^7.4.3"}, "engines": {"node": ">= 6"}, "gitHead": "a9cea169f5df4ce91bc9b385bf6cce06c7ff2daf", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.5.0", "_nodeVersion": "11.9.0", "_npmVersion": "6.9.0", "dist": {"shasum": "1d879dd069ce1036041c23229cb5d7ef47a58ec6", "size": 93028, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.5.0.tgz", "integrity": "sha512-nKxSWOa7vxAP2pikrGxbkZsG/garQseRiLn9mIDjzwoQsyVy7ZWIpLoARejnINGGLA4fttuzRFFNxxbsztdJgw=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jbnicolai"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.5.0_1554533457198_0.9067313453742425"}, "_hasShrinkwrap": false, "publish_time": 1554533457336, "_cnpm_publish_time": 1554533457336, "_cnpmcore_publish_time": "2021-12-13T13:15:07.430Z"}, "1.4.0": {"name": "yaml", "version": "1.4.0", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/dist/index.js", "./map.js": "./browser/map.js", "./pair.js": "./browser/pair.js", "./parse-cst.js": "./browser/parse-cst.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./seq.js": "./browser/seq.js", "./types/binary.js": "./browser/types/binary.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/set.js": "./browser/types/set.js", "./types/timestamp.js": "./browser/types/timestamp.js"}, "scripts": {"browser:build": "BABEL_ENV=browser babel src/ --out-dir browser/dist/", "browser:copy": "cpy '*.js' '!*.config.js' types/ browser/ --parents", "dist:build": "babel src/ --out-dir dist/", "build": "npm run dist:build && npm run browser:build && npm run browser:copy", "prettier": "prettier --write \"{src,tests}/**/*.js\"", "lint": "eslint src/", "start": "npm run dist:build && node -i -e 'YAML=require(\".\")'", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm test && npm run build"}, "browserslist": "> 0.5%, not dead", "jest": {"testMatch": ["**/tests/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.3.4", "@babel/plugin-proposal-class-properties": "^7.3.4", "@babel/plugin-transform-runtime": "^7.3.4", "@babel/preset-env": "^7.3.4", "babel-eslint": "^10.0.1", "babel-jest": "^24.3.0", "babel-plugin-add-module-exports": "^1.0.0", "babel-plugin-trace": "^1.1.0", "cpy-cli": "^2.0.0", "eslint": "^5.15.1", "eslint-config-prettier": "^4.1.0", "fast-check": "^1.12.0", "jest": "^24.3.0", "prettier": "^1.16.4"}, "dependencies": {"@babel/runtime": "^7.3.4"}, "engines": {"node": ">= 6"}, "gitHead": "d677a97aaa86aadf35bf3793cde8880d97d9f059", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.4.0", "_npmVersion": "6.5.0", "_nodeVersion": "11.9.0", "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "b729a3ef7e35bdc5ece8f28900e20a9b41510fc3", "size": 89553, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.4.0.tgz", "integrity": "sha512-rzU83hGJrNgyT7OE2mP/SILeZxEMRJ0mza0n4KFtkNL1aXUZ79ZgZ5pIH56yT6LiqujcAs/Rqzp0ApvvNYfUfw=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jbnicolai"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.4.0_1551988099594_0.1660201266175827"}, "_hasShrinkwrap": false, "publish_time": 1551988099735, "_cnpm_publish_time": 1551988099735, "_cnpmcore_publish_time": "2021-12-13T13:15:08.169Z"}, "1.3.2": {"name": "yaml", "version": "1.3.2", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/dist/index.js", "./map.js": "./browser/map.js", "./pair.js": "./browser/pair.js", "./parse-cst.js": "./browser/parse-cst.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./seq.js": "./browser/seq.js", "./types/binary.js": "./browser/types/binary.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/set.js": "./browser/types/set.js", "./types/timestamp.js": "./browser/types/timestamp.js"}, "scripts": {"browser:build": "BABEL_ENV=browser babel src/ --out-dir browser/dist/", "browser:copy": "cpy '*.js' '!*.config.js' types/ browser/ --parents", "dist:build": "babel src/ --out-dir dist/", "build": "npm run dist:build && npm run browser:build && npm run browser:copy", "prettier": "prettier --write \"{src,tests}/**/*.js\"", "lint": "eslint src/", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm test && npm run build"}, "browserslist": "> 0.5%, not dead", "jest": {"testMatch": ["**/tests/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.2.2", "@babel/plugin-proposal-class-properties": "^7.3.0", "@babel/plugin-transform-runtime": "^7.2.0", "@babel/preset-env": "^7.3.1", "babel-eslint": "^10.0.1", "babel-jest": "^24.0.0", "babel-plugin-add-module-exports": "^1.0.0", "babel-plugin-trace": "^1.1.0", "cpy-cli": "^2.0.0", "eslint": "^5.12.1", "eslint-config-prettier": "^4.0.0", "fast-check": "^1.10.0", "jest": "^24.0.0", "prettier": "^1.16.1"}, "dependencies": {}, "engines": {"node": ">= 6"}, "gitHead": "80e13b723f639ba4fce6e211e50ae88e887c69d8", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.3.2", "_npmVersion": "6.5.0", "_nodeVersion": "11.9.0", "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "3de83fafed3799fb0b0029c12f80035bc7611303", "size": 89132, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.3.2.tgz", "integrity": "sha512-ZZZIdcApMRcAez37EVrtCim+8JUESX0zRcsv+HMfatIX79cX22CAnVkxDrZhAmzsnka2nb/mvaTybzDYcnrIew=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jbnicolai"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.3.2_1549661262361_0.7309622508498113"}, "_hasShrinkwrap": false, "publish_time": 1549661262462, "_cnpm_publish_time": 1549661262462, "_cnpmcore_publish_time": "2021-12-13T13:15:09.056Z"}, "1.3.1": {"name": "yaml", "version": "1.3.1", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/dist/index.js", "./map.js": "./browser/map.js", "./pair.js": "./browser/pair.js", "./parse-cst.js": "./browser/parse-cst.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./seq.js": "./browser/seq.js", "./types/binary.js": "./browser/types/binary.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/set.js": "./browser/types/set.js", "./types/timestamp.js": "./browser/types/timestamp.js"}, "scripts": {"browser:build": "BABEL_ENV=browser babel src/ --out-dir browser/dist/", "browser:copy": "cpy '*.js' '!*.config.js' types/ browser/ --parents", "dist:build": "babel src/ --out-dir dist/", "build": "npm run dist:build && npm run browser:build && npm run browser:copy", "prettier": "prettier --write \"{src,tests}/**/*.js\"", "lint": "eslint src/", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm test && npm run build"}, "browserslist": "> 0.5%, not dead", "jest": {"testMatch": ["**/tests/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.2.2", "@babel/plugin-proposal-class-properties": "^7.3.0", "@babel/plugin-transform-runtime": "^7.2.0", "@babel/preset-env": "^7.3.1", "babel-eslint": "^10.0.1", "babel-jest": "^24.0.0", "babel-plugin-add-module-exports": "^1.0.0", "babel-plugin-trace": "^1.1.0", "cpy-cli": "^2.0.0", "eslint": "^5.12.1", "eslint-config-prettier": "^4.0.0", "fast-check": "^1.10.0", "jest": "^24.0.0", "prettier": "^1.16.1"}, "dependencies": {}, "engines": {"node": ">= 6"}, "gitHead": "00a411df872b7e80ca9fcfbe86ed6afb0fd05451", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.3.1", "_npmVersion": "6.5.0", "_nodeVersion": "10.11.0", "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "058453683f6e459bfa1854ae6110b68c72438c8e", "size": 89210, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.3.1.tgz", "integrity": "sha512-2AG732tOqaovhv9HNm39BjaEnEDaHssbAxh4atw521ohOqL4X8JHjcbyf2W+dYSVl6zG8/Ayd1a1J36k8eJQZw=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jbnicolai"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.3.1_1548589363639_0.8162751298100426"}, "_hasShrinkwrap": false, "publish_time": 1548589363790, "_cnpm_publish_time": 1548589363790, "_cnpmcore_publish_time": "2021-12-13T13:15:09.904Z"}, "1.3.0": {"name": "yaml", "version": "1.3.0", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/dist/index.js", "./map.js": "./browser/map.js", "./pair.js": "./browser/pair.js", "./parse-cst.js": "./browser/parse-cst.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./seq.js": "./browser/seq.js", "./types/binary.js": "./browser/types/binary.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/set.js": "./browser/types/set.js", "./types/timestamp.js": "./browser/types/timestamp.js"}, "scripts": {"browser:build": "BABEL_ENV=browser babel src/ --out-dir browser/dist/", "browser:copy": "cpy '*.js' '!*.config.js' types/ browser/ --parents", "dist:build": "babel src/ --out-dir dist/", "build": "npm run dist:build && npm run browser:build && npm run browser:copy", "prettier": "prettier --write \"{src,tests}/**/*.js\"", "lint": "eslint src/", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm test && npm run build"}, "browserslist": "> 0.5%, not dead", "jest": {"testMatch": ["**/tests/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.2.2", "@babel/plugin-proposal-class-properties": "^7.3.0", "@babel/plugin-transform-runtime": "^7.2.0", "@babel/preset-env": "^7.3.1", "babel-eslint": "^10.0.1", "babel-jest": "^24.0.0", "babel-plugin-add-module-exports": "^1.0.0", "babel-plugin-trace": "^1.1.0", "cpy-cli": "^2.0.0", "eslint": "^5.12.1", "eslint-config-prettier": "^4.0.0", "fast-check": "^1.10.0", "jest": "^24.0.0", "prettier": "^1.16.1"}, "dependencies": {}, "engines": {"node": ">= 6"}, "gitHead": "2c2fbe89631bcba08012a3b0cc411f0982477862", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.3.0", "_npmVersion": "6.5.0", "_nodeVersion": "10.11.0", "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "61abb4269417185410d91f78a45cdd1f1cf3a0a5", "size": 89174, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.3.0.tgz", "integrity": "sha512-13t7beeiJCFWrZZf+a5WLRWVdz5C6oEXdfwOncnAynHr5fMqZg/njVUZjPmOskq7wn6CwS1RBRNavFvLok7vWQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jbnicolai"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.3.0_1548578017342_0.08806500083654667"}, "_hasShrinkwrap": false, "publish_time": 1548578017515, "_cnpm_publish_time": 1548578017515, "_cnpmcore_publish_time": "2021-12-13T13:15:10.818Z"}, "1.2.1": {"name": "yaml", "version": "1.2.1", "main": "dist/index.js", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "scripts": {"build": "babel src/ --out-dir dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "lint": "eslint src/", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm test && npm run build"}, "babel": {"presets": [["@babel/env", {"targets": {"node": "6"}}]], "plugins": ["@babel/plugin-proposal-class-properties", ["babel-plugin-trace", {"strip": true}], ["babel-plugin-add-module-exports", {"addDefaultProperty": true}]]}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/cst/common"]}, "prettier": {"semi": false, "singleQuote": true}, "eslintConfig": {"root": true, "parser": "babel-es<PERSON>", "env": {"node": true}, "extends": ["eslint:recommended", "prettier"], "rules": {"array-callback-return": "error", "camelcase": "error", "consistent-return": "error", "eqeqeq": ["error", "always", {"null": "ignore"}], "no-constant-condition": ["error", {"checkLoops": false}], "no-control-regex": 0, "no-implicit-globals": "error", "no-template-curly-in-string": "warn", "no-unused-labels": 0, "no-var": "error", "prefer-const": ["warn", {"destructuring": "all"}]}, "overrides": [{"files": ["src/**/*.js"], "env": {"es6": true, "node": false}}, {"files": ["__tests__/*.js", "__tests__/cst/*.js"], "env": {"es6": true, "jest": true}, "rules": {"camelcase": 0}}]}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.2.2", "@babel/plugin-proposal-class-properties": "^7.2.3", "@babel/preset-env": "^7.2.3", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "^10.0.1", "babel-jest": "^23.6.0", "babel-plugin-add-module-exports": "^1.0.0", "babel-plugin-trace": "^1.1.0", "eslint": "^5.11.1", "eslint-config-prettier": "^3.3.0", "fast-check": "^1.9.1", "jest": "^23.6.0", "prettier": "^1.15.3"}, "dependencies": {}, "engines": {"node": ">= 6"}, "gitHead": "978a42a4d8184bbdddd3ff4cccff7f8cacf3c062", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.2.1", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "7057ee224c96e7dcd591a83a871ad89d3df1c6ed", "size": 43168, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.2.1.tgz", "integrity": "sha512-LuOeoZc6LYguU9H/XCbrPfuDpCGJVf+o/r9uLCGnsHiHk/8Cr7IVeXOilO8yK0XA8QJPlX22KVofINt8xhdwqg=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jbnicolai"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.2.1_1547720138016_0.5830178511833091"}, "_hasShrinkwrap": false, "publish_time": 1547720138143, "_cnpm_publish_time": 1547720138143, "_cnpmcore_publish_time": "2021-12-13T13:15:11.609Z"}, "1.2.0": {"name": "yaml", "version": "1.2.0", "main": "dist/index.js", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "scripts": {"build": "babel src/ --out-dir dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "lint": "eslint src/", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm test && npm run build"}, "babel": {"presets": [["@babel/env", {"targets": {"node": "6"}}]], "plugins": ["@babel/plugin-proposal-class-properties", ["babel-plugin-trace", {"strip": true}], ["babel-plugin-add-module-exports", {"addDefaultProperty": true}]]}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/cst/common"]}, "prettier": {"semi": false, "singleQuote": true}, "eslintConfig": {"root": true, "parser": "babel-es<PERSON>", "env": {"node": true}, "extends": ["eslint:recommended", "prettier"], "rules": {"array-callback-return": "error", "camelcase": "error", "consistent-return": "error", "eqeqeq": ["error", "always", {"null": "ignore"}], "no-constant-condition": ["error", {"checkLoops": false}], "no-control-regex": 0, "no-implicit-globals": "error", "no-template-curly-in-string": "warn", "no-unused-labels": 0, "no-var": "error", "prefer-const": ["warn", {"destructuring": "all"}]}, "overrides": [{"files": ["src/**/*.js"], "env": {"es6": true, "node": false}}, {"files": ["__tests__/*.js", "__tests__/cst/*.js"], "env": {"es6": true, "jest": true}, "rules": {"camelcase": 0}}]}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.2.2", "@babel/plugin-proposal-class-properties": "^7.2.3", "@babel/preset-env": "^7.2.3", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "^10.0.1", "babel-jest": "^23.6.0", "babel-plugin-add-module-exports": "^1.0.0", "babel-plugin-trace": "^1.1.0", "eslint": "^5.11.1", "eslint-config-prettier": "^3.3.0", "fast-check": "^1.9.1", "jest": "^23.6.0", "prettier": "^1.15.3"}, "dependencies": {}, "engines": {"node": ">= 6"}, "gitHead": "b6b760920c9acb6eaf24d2f39ec2a246c7cf6eb5", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.2.0", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "aa105d0794cdfeb1db5ec9f3a9efe73145dd817d", "size": 43853, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.2.0.tgz", "integrity": "sha512-nKNUuxmvbrRDQbPCVqUDvjgnOvpxWFRK6O4hO57Q3ie07CmTkGrcpoFHEN7PUTkvhhJD23CR2WpKgPyIVoj5zg=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jbnicolai"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.2.0_1546335248440_0.4342656343825382"}, "_hasShrinkwrap": false, "publish_time": 1546335248556, "_cnpm_publish_time": 1546335248556, "_cnpmcore_publish_time": "2021-12-13T13:15:12.506Z"}, "1.1.0": {"name": "yaml", "version": "1.1.0", "main": "dist/index.js", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "scripts": {"build": "babel src/ --out-dir dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm test && npm run build"}, "babel": {"presets": [["@babel/env", {"targets": {"node": "6"}}]], "plugins": ["@babel/plugin-proposal-class-properties", ["babel-plugin-trace", {"strip": true}], ["babel-plugin-add-module-exports", {"addDefaultProperty": true}]]}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/cst/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.2.0", "@babel/core": "^7.2.0", "@babel/plugin-proposal-class-properties": "^7.2.1", "@babel/preset-env": "^7.2.0", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.6.0", "babel-plugin-add-module-exports": "^1.0.0", "babel-plugin-trace": "^1.1.0", "jest": "^23.6.0", "prettier": "^1.15.3"}, "dependencies": {}, "engines": {"node": ">= 6"}, "gitHead": "31a2993c2693b443a4cab995c762f10c2e69506c", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.1.0", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6168f2beb28e80ee88f82efcb6da01d8e496ba30", "size": 38704, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.1.0.tgz", "integrity": "sha512-MD5Ptelnnjfj/w4UqdNguD9Ipzm3ws6bNiYkGkl4lkfGMU1V7QYyHkRh28EiHPdprSvV+xAhvLJ6ifyALYw7wA=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jbnicolai"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.1.0_1544569208499_0.9677343781674859"}, "_hasShrinkwrap": false, "publish_time": 1544569208695, "_cnpm_publish_time": 1544569208695, "_cnpmcore_publish_time": "2021-12-13T13:15:13.423Z"}, "1.0.3": {"name": "yaml", "version": "1.0.3", "main": "dist/index.js", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "scripts": {"build": "babel src/ --out-dir dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm test && npm run build"}, "babel": {"presets": [["@babel/env", {"targets": {"node": "6"}}]], "plugins": ["@babel/plugin-proposal-class-properties", ["babel-plugin-trace", {"strip": true}], ["babel-plugin-add-module-exports", {"addDefaultProperty": true}]]}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/cst/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.1.5", "@babel/core": "^7.1.6", "@babel/plugin-proposal-class-properties": "^7.1.0", "@babel/preset-env": "^7.1.6", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.6.0", "babel-plugin-add-module-exports": "^1.0.0", "babel-plugin-trace": "^1.1.0", "jest": "^23.6.0", "prettier": "^1.15.2"}, "dependencies": {}, "engines": {"node": ">= 6"}, "gitHead": "f4e7c1f8d3ece09da5725bbe30ccaf4010095a9d", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.0.3", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6eed55f60edb3ac1402283fba94c91b6a0a026f7", "size": 37107, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.0.3.tgz", "integrity": "sha512-GDYiMkQRa1894pgIQTIK5CGEpcjbozJb0IcSJ3WYoGyA9kZupfpRQxpSYFYfFHFKxlkaN4GgnhEF88J/VelC5A=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jbnicolai"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.0.3_1543439281126_0.8664904182340489"}, "_hasShrinkwrap": false, "publish_time": 1543439281305, "_cnpm_publish_time": 1543439281305, "_cnpmcore_publish_time": "2021-12-13T13:15:14.305Z"}, "1.0.2": {"name": "yaml", "version": "1.0.2", "main": "dist/index.js", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "scripts": {"build": "babel src/ --out-dir dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm test && npm run build"}, "babel": {"presets": [["@babel/env", {"targets": {"node": "6"}}]], "plugins": ["@babel/plugin-proposal-class-properties", ["babel-plugin-trace", {"strip": true}], ["babel-plugin-add-module-exports", {"addDefaultProperty": true}]]}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/cst/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.1.5", "@babel/core": "^7.1.6", "@babel/plugin-proposal-class-properties": "^7.1.0", "@babel/preset-env": "^7.1.6", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.6.0", "babel-plugin-add-module-exports": "^1.0.0", "babel-plugin-trace": "^1.1.0", "jest": "^23.6.0", "prettier": "^1.15.2"}, "dependencies": {}, "engines": {"node": ">= 6"}, "gitHead": "06195c068fc7a6fa5da3e6061309250c611f403a", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.0.2", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "77941a457090e17b8ca65b53322e68a050e090d4", "size": 37083, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.0.2.tgz", "integrity": "sha512-vPmXtExk6AV7F5l2N5jW4LhbhZZPPN+xOK8x/+EgSXsXoawuo19Iqa+9nYMwrltsi6nl0rMKiROA/SAfG2OgUw=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jbnicolai"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.0.2_1542279202769_0.6724954608891509"}, "_hasShrinkwrap": false, "publish_time": 1542279202920, "_cnpm_publish_time": 1542279202920, "_cnpmcore_publish_time": "2021-12-13T13:15:15.264Z"}, "1.0.1": {"name": "yaml", "version": "1.0.1", "main": "dist/index.js", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "scripts": {"build": "babel src/ --out-dir dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm test && npm run build"}, "babel": {"presets": [["@babel/env", {"targets": {"node": "6"}}]], "plugins": ["@babel/plugin-proposal-class-properties", ["babel-plugin-trace", {"strip": true}], ["babel-plugin-add-module-exports", {"addDefaultProperty": true}]]}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/cst/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.1.5", "@babel/core": "^7.1.5", "@babel/plugin-proposal-class-properties": "^7.1.0", "@babel/preset-env": "^7.1.5", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.6.0", "babel-plugin-add-module-exports": "^1.0.0", "babel-plugin-trace": "^1.1.0", "jest": "^23.6.0", "prettier": "^1.15.1"}, "dependencies": {}, "engines": {"node": ">= 6"}, "gitHead": "e48e5586bf0a4f9d577310b01cac395965877cbc", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.0.1", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "3a8b01e4dda66445904ee743a95951d67a47a0c5", "size": 37017, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.0.1.tgz", "integrity": "sha512-ysU56qumPH0tEML2hiFVAo+rCnG/0+oO2Ye3fN4c40GBN7kX1fYhDqSoX7OohimcI/Xkkr1DdaUlg5+afinRqA=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jbnicolai"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.0.1_1541586739472_0.3313737259786691"}, "_hasShrinkwrap": false, "publish_time": 1541586739623, "_cnpm_publish_time": 1541586739623, "_cnpmcore_publish_time": "2021-12-13T13:15:16.090Z"}, "1.0.0": {"name": "yaml", "version": "1.0.0", "main": "dist/index.js", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "scripts": {"build": "babel src/ --out-dir dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm test && npm run build"}, "babel": {"presets": [["@babel/env", {"targets": {"node": "6"}}]], "plugins": ["@babel/plugin-proposal-class-properties", ["babel-plugin-trace", {"strip": true}], ["babel-plugin-add-module-exports", {"addDefaultProperty": true}]]}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/cst/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.1.0", "@babel/core": "^7.1.0", "@babel/plugin-proposal-class-properties": "^7.1.0", "@babel/preset-env": "^7.1.0", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.6.0", "babel-plugin-add-module-exports": "^1.0.0", "babel-plugin-trace": "^1.1.0", "jest": "^23.6.0", "prettier": "^1.14.3"}, "dependencies": {}, "engines": {"node": ">= 6"}, "gitHead": "3649942472ddc6258415b4c8b697a73d86253207", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.8.0", "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "9d428cd4641c87ff4b17b2d4f4ab96bb6dd17154", "size": 37008, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.0.0.tgz", "integrity": "sha512-HLMg8IQRQLLPZ/tVtR0j5ShAh4HJKt8soYsu0Fn3Y5eoIFJoh1cs1mvvOnRD236mjeBDarlk5Ng/b/IHQs+5Rg=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jbnicolai"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.0.0_1537387011211_0.15249343030622997"}, "_hasShrinkwrap": false, "publish_time": 1537387011387, "_cnpm_publish_time": 1537387011387, "_cnpmcore_publish_time": "2021-12-13T13:15:16.927Z"}, "1.0.0-rc.8": {"name": "yaml", "version": "1.0.0-rc.8", "main": "dist/index.js", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "files": ["dist/", "types/", "*.js"], "scripts": {"build": "babel src/ --out-dir dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm test && npm run build"}, "babel": {"presets": [["@babel/env", {"targets": {"node": "6"}}]], "plugins": ["@babel/plugin-proposal-class-properties", ["babel-plugin-trace", {"strip": true}]]}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/cst/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.0.0-rc.2", "@babel/core": "^7.0.0-rc.2", "@babel/plugin-proposal-class-properties": "^7.0.0-rc.2", "@babel/preset-env": "^7.0.0-rc.2", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.4.2", "babel-plugin-trace": "^1.1.0", "jest": "^23.5.0", "prettier": "^1.14.2"}, "dependencies": {}, "engines": {"node": ">= 6"}, "gitHead": "21633235dfafe60796213e42b5ba1903413a2df5", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.0.0-rc.8", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "e5604c52b7b07b16e469bcf875ab0dfe08c50d42", "size": 36895, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.0.0-rc.8.tgz", "integrity": "sha512-+6a1nAiwS141AYKYsGXp9A7uLsYwY5f2ql9BhpewjfLm8u8HYh43ZD+8GWY4FNusZjU5+oa9cg9Spx0orQSf1g=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jbnicolai"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.0.0-rc.8_1535000596262_0.4265001788812868"}, "_hasShrinkwrap": false, "publish_time": 1535000596366, "_cnpm_publish_time": 1535000596366, "_cnpmcore_publish_time": "2021-12-13T13:15:17.957Z"}, "1.0.0-rc.7": {"name": "yaml", "version": "1.0.0-rc.7", "main": "dist/index.js", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML 1.2", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "files": ["dist/", "types/", "*.js"], "scripts": {"build": "babel src/ --out-dir dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm test && npm run build"}, "babel": {"presets": ["@babel/env", "@babel/stage-3"], "plugins": [["trace", {"strip": true}]]}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/cst/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.0.0-beta.52", "@babel/core": "^7.0.0-beta.52", "@babel/preset-env": "^7.0.0-beta.52", "@babel/preset-stage-3": "^7.0.0-beta.52", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.2.0", "babel-plugin-trace": "^1.1.0", "jest": "^23.3.0", "prettier": "^1.13.7"}, "dependencies": {}, "engines": {"node": ">= 6"}, "gitHead": "d41b9e71567b0210a1c09e1cdec7cf5fa9369057", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.0.0-rc.7", "_npmVersion": "6.1.0", "_nodeVersion": "10.3.0", "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "7cf9dba3c78992542b7a2d7cb9a7eeacbff63f77", "size": 42041, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.0.0-rc.7.tgz", "integrity": "sha512-tsTvdZxHPSgwv70qw0w2YeGA8VCqEpHRlYLb3T3ROhke8j92iC3t7kd2XHkiyp7pvxucKUgzzo+3Zq77WV82Kw=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jbnicolai"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.0.0-rc.7_1531323079119_0.8255308437424624"}, "_hasShrinkwrap": false, "publish_time": 1531323079241, "_cnpm_publish_time": 1531323079241, "_cnpmcore_publish_time": "2021-12-13T13:15:18.902Z"}, "1.0.0-rc.6": {"name": "yaml", "version": "1.0.0-rc.6", "main": "dist/index.js", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML 1.2", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "files": ["dist/", "types/", "*.js"], "scripts": {"build": "babel src/ --out-dir dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "version": "git commit -am \"Update version\" && git add -f dist/", "postversion": "git reset --hard HEAD^", "prepublishOnly": "npm test && npm run build"}, "babel": {"presets": ["@babel/env", "@babel/stage-3"], "plugins": [["trace", {"strip": true}]]}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/cst/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.0.0-beta.52", "@babel/core": "^7.0.0-beta.52", "@babel/preset-env": "^7.0.0-beta.52", "@babel/preset-stage-3": "^7.0.0-beta.52", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.2.0", "babel-plugin-trace": "^1.1.0", "jest": "^23.3.0", "prettier": "^1.13.7"}, "dependencies": {}, "engines": {"node": ">= 6"}, "readmeFilename": "README.md", "gitHead": "eaca37a799e7a74318a2f3aef2a3614f23180e0b", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.0.0-rc.6", "_npmVersion": "6.1.0", "_nodeVersion": "10.3.0", "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "42beacf1dd7ea2e32e50456051bbe54ea54a1244", "size": 39026, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.0.0-rc.6.tgz", "integrity": "sha512-vjE4BkY5aEYMGLykB3+Uo5yWDOvMqfT0EWFHImMPLKXz6i18fSkf+WFEjG0rfamWr73jxRCH9u+a6PEQJox35Q=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jbnicolai"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.0.0-rc.6_1531052171357_0.6230809306507668"}, "_hasShrinkwrap": false, "publish_time": 1531052171502, "_cnpm_publish_time": 1531052171502, "_cnpmcore_publish_time": "2021-12-13T13:15:19.882Z"}, "1.0.0-rc.5": {"name": "yaml", "version": "1.0.0-rc.5", "main": "dist/index.js", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML 1.2", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "files": ["dist/", "types/", "*.js"], "scripts": {"build": "babel src/ --out-dir dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "version": "git commit -am \"Update version\" && git add -f dist/", "postversion": "git reset --hard HEAD^", "prepublishOnly": "npm test && npm run build"}, "babel": {"presets": ["@babel/env", "@babel/stage-3"], "plugins": [["trace", {"strip": true}]]}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/cst/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.0.0-beta.46", "@babel/core": "^7.0.0-beta.46", "@babel/preset-env": "^7.0.0-beta.46", "@babel/preset-stage-3": "^7.0.0-beta.46", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-trace": "^1.1.0", "jest": "^22.4.3", "prettier": "1.12.1"}, "dependencies": {}, "engines": {"node": ">= 6"}, "readmeFilename": "README.md", "gitHead": "d5f7ca612a84f5f31c5aad24f06d4b441a39b710", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.0.0-rc.5", "_npmVersion": "6.1.0", "_nodeVersion": "10.3.0", "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "113aad47e7a6c1c59759fb318459e42b96708021", "size": 36640, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.0.0-rc.5.tgz", "integrity": "sha512-pstAfmG+aYz992zHy34x0NUsYPIafD+FKKAVI40ZF05AhB1Uy6TqbZfSWdt+tV0Ae3JdMIzaNFHA9qXicxLDXQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jbnicolai"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.0.0-rc.5_1530372700405_0.4796536218428211"}, "_hasShrinkwrap": false, "publish_time": 1530372700511, "_cnpm_publish_time": 1530372700511, "_cnpmcore_publish_time": "2021-12-13T13:15:20.825Z"}, "1.0.0-rc.4": {"name": "yaml", "version": "1.0.0-rc.4", "main": "dist/index.js", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML 1.2", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "files": ["dist/", "types/", "*.js"], "scripts": {"build": "babel src/ --out-dir dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "version": "git commit -am \"Update version\" && git add -f dist/", "postversion": "git reset --hard HEAD^", "prepublishOnly": "npm test && npm run build"}, "babel": {"presets": ["@babel/env", "@babel/stage-3"], "plugins": [["trace", {"strip": true}]]}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/cst/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.0.0-beta.46", "@babel/core": "^7.0.0-beta.46", "@babel/preset-env": "^7.0.0-beta.46", "@babel/preset-stage-3": "^7.0.0-beta.46", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-trace": "^1.1.0", "jest": "^22.4.3", "prettier": "1.12.1"}, "dependencies": {}, "engines": {"node": ">= 6"}, "readmeFilename": "README.md", "gitHead": "cfab593098fe7473f463a2b8c9a50e3f6a078084", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.0.0-rc.4", "_npmVersion": "6.1.0", "_nodeVersion": "10.3.0", "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "850dc77d9b03975b5e5ea8a9b37cde252a8f1f0b", "size": 34310, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.0.0-rc.4.tgz", "integrity": "sha512-nK1wu7MJaiFwkoC24KdYo0PCl58AKaAyIliHKYt3m4jPanidB2fsPDQnr5BLhNiGdQTYFA8H4zNecLCN/mnHiA=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jbnicolai"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.0.0-rc.4_1530134416486_0.0414128667357736"}, "_hasShrinkwrap": false, "publish_time": 1530134416651, "_cnpm_publish_time": 1530134416651, "_cnpmcore_publish_time": "2021-12-13T13:15:21.824Z"}, "1.0.0-rc.3": {"name": "yaml", "version": "1.0.0-rc.3", "main": "dist/index.js", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML 1.2", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "files": ["dist/", "types/", "*.js"], "scripts": {"build": "babel src/ --out-dir dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "version": "git commit -am \"Update version\" && git add -f dist/", "postversion": "git reset --hard HEAD^", "prepublishOnly": "npm test && npm run build"}, "babel": {"presets": ["@babel/env", "@babel/stage-3"], "plugins": [["trace", {"strip": true}]]}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/ast/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.0.0-beta.46", "@babel/core": "^7.0.0-beta.46", "@babel/preset-env": "^7.0.0-beta.46", "@babel/preset-stage-3": "^7.0.0-beta.46", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-trace": "^1.1.0", "jest": "^22.4.3", "prettier": "1.12.1"}, "dependencies": {}, "engines": {"node": ">= 6"}, "readmeFilename": "README.md", "gitHead": "da10c705e55c03dbbeeefa7f30e86225b10a7b82", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.0.0-rc.3", "_npmVersion": "6.1.0", "_nodeVersion": "10.3.0", "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "e4831862a0a89baca9382e772a5b7d34f60a3cf5", "size": 34276, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.0.0-rc.3.tgz", "integrity": "sha512-iQRr/5nKb4AGKgq26pnvK5wa/+q2ECXFnJwOByvjHh3T0ymHimnF/0+v9G1C56CXyQkx4ncHlPFJ4l9dSO0Vow=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jbnicolai"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.0.0-rc.3_1530040358554_0.503312615896834"}, "_hasShrinkwrap": false, "publish_time": 1530040358659, "_cnpm_publish_time": 1530040358659, "_cnpmcore_publish_time": "2021-12-13T13:15:22.732Z"}, "1.0.0-rc.2": {"name": "yaml", "version": "1.0.0-rc.2", "main": "dist/index.js", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML 1.2", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "files": ["dist/", "types/", "*.js"], "scripts": {"build": "babel src/ --out-dir dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "version": "git commit -am \"Update version\" && git add -f dist/", "postversion": "git reset --hard HEAD^", "prepublishOnly": "npm test && npm run build"}, "babel": {"presets": ["@babel/env", "@babel/stage-3"], "plugins": [["trace", {"strip": true}]]}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/ast/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.0.0-beta.46", "@babel/core": "^7.0.0-beta.46", "@babel/preset-env": "^7.0.0-beta.46", "@babel/preset-stage-3": "^7.0.0-beta.46", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-trace": "^1.1.0", "jest": "^22.4.3", "prettier": "1.12.1"}, "dependencies": {}, "engines": {"node": ">= 6"}, "readmeFilename": "README.md", "gitHead": "f6fd3d463e4a1c70dd7c2b2e4ea8637ccb05f2c7", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.0.0-rc.2", "_npmVersion": "6.1.0", "_nodeVersion": "10.3.0", "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "aeffa1ee128b7babc8f154de08e824376b674f43", "size": 33987, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.0.0-rc.2.tgz", "integrity": "sha512-I1K6Pbys/yK3ghVUCMbfbeqXX/8OjpBWIu/OdMSlMk5pkBFUyx+O57W8Wxk4OttSRs/3FqQ3TVtd5i/ODugVxQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jbnicolai"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.0.0-rc.2_1529356738698_0.7431503725995268"}, "_hasShrinkwrap": false, "publish_time": 1529356738840, "_cnpm_publish_time": 1529356738840, "_cnpmcore_publish_time": "2021-12-13T13:15:23.585Z"}, "1.0.0-rc.1": {"name": "yaml", "version": "1.0.0-rc.1", "main": "dist/index.js", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML 1.2", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "files": ["dist/"], "scripts": {"build": "babel src/ --out-dir dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "version": "git commit -am \"Update version\" && git add -f dist/", "postversion": "git reset --hard HEAD^", "prepublishOnly": "npm test && npm run build"}, "babel": {"presets": ["@babel/env", "@babel/stage-3"], "plugins": [["trace", {"strip": true}]]}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/ast/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.0.0-beta.46", "@babel/core": "^7.0.0-beta.46", "@babel/preset-env": "^7.0.0-beta.46", "@babel/preset-stage-3": "^7.0.0-beta.46", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-trace": "^1.1.0", "jest": "^22.4.3", "prettier": "1.12.1"}, "dependencies": {}, "engines": {"node": ">= 6"}, "readmeFilename": "README.md", "gitHead": "4ad0e6b81dc31b1e130b9ee5007dacde2a65d903", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.0.0-rc.1", "_npmVersion": "6.1.0", "_nodeVersion": "10.3.0", "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "0c03cab07797c2f55de3afd651e739d3cb941efd", "size": 33704, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.0.0-rc.1.tgz", "integrity": "sha512-xO+n1Q9TdC/TZIhjsbHZA7lU9fLElT1BmbD2OTR0TZdB7k25DdiGdhPS0mt+Vu1i98ftaGo4GdIbyH8xAxbrwQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jbnicolai"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.0.0-rc.1_1529356423774_0.727602425586136"}, "_hasShrinkwrap": false, "publish_time": 1529356423889, "_cnpm_publish_time": 1529356423889, "_cnpmcore_publish_time": "2021-12-13T13:15:24.525Z"}, "1.0.0-beta.7": {"name": "yaml", "version": "1.0.0-beta.7", "main": "dist/index.js", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML 1.2", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://github.com/eemeli/yaml#readme", "files": ["dist/"], "scripts": {"build": "babel src/ --out-dir dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "version": "git commit -am \"Update version\" && git add -f dist/", "postversion": "git reset --hard HEAD^", "prepublishOnly": "npm test && npm run build"}, "babel": {"presets": ["@babel/env", "@babel/stage-3"], "plugins": [["trace", {"strip": true}]]}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/ast/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.0.0-beta.46", "@babel/core": "^7.0.0-beta.46", "@babel/preset-env": "^7.0.0-beta.46", "@babel/preset-stage-3": "^7.0.0-beta.46", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-trace": "^1.1.0", "jest": "^22.4.3", "prettier": "1.12.1"}, "dependencies": {}, "readmeFilename": "README.md", "gitHead": "4286bdfe98609dbf600526930359d17b96f0c1be", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.0.0-beta.7", "_npmVersion": "6.1.0", "_nodeVersion": "10.3.0", "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "681937c5b2d85b6eb474d47e877f5e63a7efe20c", "size": 35045, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.0.0-beta.7.tgz", "integrity": "sha512-nnAWKkYkZUqP2o3D88A0tjIYpSazbv2LHYXoIVzqKOhsw18cQ9ANuNtbn055jijI8xssSbT+Z69Xb4D+Gz66pw=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jbnicolai"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.0.0-beta.7_1529071479501_0.20629537654386376"}, "_hasShrinkwrap": false, "publish_time": 1529071479596, "_cnpm_publish_time": 1529071479596, "_cnpmcore_publish_time": "2021-12-13T13:15:25.501Z"}, "1.0.0-beta.6": {"name": "yaml", "version": "1.0.0-beta.6", "main": "dist/index.js", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML 1.2", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://github.com/eemeli/yaml#readme", "files": ["dist/"], "scripts": {"build": "babel src/ --out-dir dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:install": "cd docs/ && bundle install", "docs:deploy": "cd docs/ && ./deploy.sh", "docs": "cd docs/ && bundle exec middleman server", "preversion": "npm test && npm run build", "version": "git commit -am \"Update version\" && git add -f dist/", "postversion": "git reset --hard HEAD^", "prepublishOnly": "npm test && npm run build"}, "babel": {"presets": ["@babel/env", "@babel/stage-3"], "plugins": [["trace", {"strip": true}]]}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/ast/common"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@babel/cli": "^7.0.0-beta.46", "@babel/core": "^7.0.0-beta.46", "@babel/preset-env": "^7.0.0-beta.46", "@babel/preset-stage-3": "^7.0.0-beta.46", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-trace": "^1.1.0", "jest": "^22.4.3", "prettier": "1.12.1"}, "dependencies": {}, "readmeFilename": "README.md", "gitHead": "872ce41b02827fea03987c1fd759556058ceea55", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.0.0-beta.6", "_npmVersion": "5.6.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "531cc4dd6327481f0a76c37a276b7eae4ef4f1d1", "size": 34791, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.0.0-beta.6.tgz", "integrity": "sha512-5AdGL7nfpjOu5X7Yp2WuBh/1ZDVLBHNiuB/5HR5IzZMI1bQR1EeYfDwt5yoqC+rax3MmZMLGPNn8ys9ssFWfkA=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jbnicolai"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.0.0-beta.6_1527626518743_0.8261273455543487"}, "_hasShrinkwrap": false, "publish_time": 1527626519158, "_cnpm_publish_time": 1527626519158, "_cnpmcore_publish_time": "2021-12-13T13:15:26.519Z"}, "1.0.0-beta.5": {"name": "yaml", "version": "1.0.0-beta.5", "main": "dist/index.js", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML 1.2", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://github.com/eemeli/yaml#readme", "files": ["dist/"], "scripts": {"build": "babel src/ --out-dir dist/", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "preversion": "npm test && npm run build", "version": "git commit -am \"Update version\" && git add -f dist/", "postversion": "git reset --hard HEAD^", "prepublishOnly": "npm test && npm run build"}, "babel": {"presets": ["@babel/env", "@babel/stage-3"], "plugins": [["trace", {"strip": true}]]}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/ast/common"]}, "devDependencies": {"@babel/cli": "^7.0.0-beta.46", "@babel/core": "^7.0.0-beta.46", "@babel/preset-env": "^7.0.0-beta.46", "@babel/preset-stage-3": "^7.0.0-beta.46", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-trace": "^1.1.0", "jest": "^22.4.3"}, "dependencies": {}, "readmeFilename": "README.md", "gitHead": "f8705ab4f49d1c787d383136b4783a1ff390d749", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.0.0-beta.5", "_npmVersion": "5.6.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "17ac674c225f72d74a9eeecfc81d30ec369ed1b6", "size": 32750, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.0.0-beta.5.tgz", "integrity": "sha512-ENvzwo7VBdyxTmIasviOYqC0Mqe1LnXH0/D7Z9B7E8K81SzBWn7ab7XbJ9B/zxurtPJVa84A1nQRC90xQilcxQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jbnicolai"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.0.0-beta.5_1526408505479_0.08075331494999771"}, "_hasShrinkwrap": false, "publish_time": 1526408505622, "_cnpm_publish_time": 1526408505622, "_cnpmcore_publish_time": "2021-12-13T13:15:27.501Z"}, "1.0.0-beta.4": {"name": "yaml", "version": "1.0.0-beta.4", "main": "dist/index.js", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML 1.2", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://github.com/eemeli/yaml#readme", "files": ["dist/"], "scripts": {"build": "babel src/ --out-dir dist/", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "preversion": "npm test && npm run build", "version": "git commit -am \"Update version\" && git add -f dist/", "postversion": "git reset --hard HEAD^", "prepublishOnly": "npm test && npm run build"}, "babel": {"presets": ["@babel/env", "@babel/stage-3"], "plugins": [["trace", {"strip": true}]]}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/ast/common"]}, "devDependencies": {"@babel/cli": "^7.0.0-beta.40", "@babel/core": "^7.0.0-beta.40", "@babel/preset-env": "^7.0.0-beta.40", "@babel/preset-stage-3": "^7.0.0-beta.40", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.2.2", "babel-plugin-trace": "^1.1.0", "jest": "^22.3.0"}, "dependencies": {}, "readmeFilename": "README.md", "gitHead": "443ddb7898a88ae379862ce4f5c7457572000556", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.0.0-beta.4", "_npmVersion": "5.8.0", "_nodeVersion": "9.5.0", "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c04bd5411e6126d773604c3a1725c8a025813d3f", "size": 30992, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.0.0-beta.4.tgz", "integrity": "sha512-4/ByzApkMcjsnml9sr1R8gpzw7WpnQMiKp7QVhnJzaJUUa4wjekR0eqJbFqM3Wa/r5QZLxaE9du6nTbVxVMobg=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jbnicolai"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.0.0-beta.4_1523192219652_0.9381838682754213"}, "_hasShrinkwrap": false, "publish_time": 1523192219760, "_cnpm_publish_time": 1523192219760, "_cnpmcore_publish_time": "2021-12-13T13:15:28.431Z"}, "1.0.0-beta.3": {"name": "yaml", "version": "1.0.0-beta.3", "main": "dist/index.js", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML 1.2", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://github.com/eemeli/yaml#readme", "files": ["dist/"], "scripts": {"build": "babel src/ --out-dir dist/", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "preversion": "npm test && npm run build", "version": "git commit -am \"Update version\" && git add -f dist/", "postversion": "git reset --hard HEAD^", "prepublishOnly": "npm test && npm run build"}, "babel": {"presets": ["@babel/env", "@babel/stage-3"], "plugins": [["trace", {"strip": true}]]}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/ast/common"]}, "devDependencies": {"@babel/cli": "^7.0.0-beta.40", "@babel/core": "^7.0.0-beta.40", "@babel/preset-env": "^7.0.0-beta.40", "@babel/preset-stage-3": "^7.0.0-beta.40", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.2.2", "babel-plugin-trace": "^1.1.0", "jest": "^22.3.0"}, "dependencies": {}, "readmeFilename": "README.md", "gitHead": "ebedaa294bce0e807782687010a43d583755a0cf", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.0.0-beta.3", "_npmVersion": "5.6.0", "_nodeVersion": "9.5.0", "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "87062d37a4f17b159c7a766b42d2dbec10615560", "size": 28244, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.0.0-beta.3.tgz", "integrity": "sha512-bcDtRmdEIq4rmYSpO6Zf9SRyE00ae5M5ifr1+qxVyszP2nO66EOPFaP+rZSr15w8Qw/2ulxoNWICIcwxYiP5pA=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jbnicolai"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.0.0-beta.3_1519340574025_0.14518911144196167"}, "_hasShrinkwrap": false, "publish_time": 1519340574112, "_cnpm_publish_time": 1519340574112, "_cnpmcore_publish_time": "2021-12-13T13:15:29.511Z"}, "1.0.0-beta.2": {"name": "yaml", "version": "1.0.0-beta.2", "main": "dist/index.js", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML 1.2", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://github.com/eemeli/yaml#readme", "files": ["dist/"], "scripts": {"build": "babel src/ --out-dir dist/", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "preversion": "npm test && npm run build", "version": "git commit -am \"Update version\" && git add -f dist/", "postversion": "git reset --hard HEAD^", "prepublishOnly": "npm test && npm run build"}, "babel": {"presets": ["@babel/env", "@babel/stage-3"], "plugins": [["trace", {"strip": true}]]}, "jest": {"testPathIgnorePatterns": ["__tests__/common"]}, "devDependencies": {"@babel/cli": "^7.0.0-beta.40", "@babel/core": "^7.0.0-beta.40", "@babel/preset-env": "^7.0.0-beta.40", "@babel/preset-stage-3": "^7.0.0-beta.40", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.2.2", "babel-plugin-trace": "^1.1.0", "jest": "^22.3.0"}, "dependencies": {"raw-yaml": "^0.2.5"}, "readmeFilename": "README.md", "gitHead": "5a668ef279fde809f47e1320773c5e0c05a062de", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.0.0-beta.2", "_npmVersion": "5.6.0", "_nodeVersion": "9.5.0", "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "342c988cbe8a4e1bd5bf2e1c6217093bfc0d0ab4", "size": 15751, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.0.0-beta.2.tgz", "integrity": "sha512-uWoCkDejwfdi8Cc9DtAnPnevlrPStWnInzHhdV2Hr+RHu8X7ntIIpX7dV/w0b2afzKDoDun7MdNHObkaYf66rA=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jbnicolai"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.0.0-beta.2_1519030800463_0.9062757794447585"}, "_hasShrinkwrap": false, "publish_time": 1519030800612, "_cnpm_publish_time": 1519030800612, "_cnpmcore_publish_time": "2021-12-13T13:15:30.720Z"}, "1.0.0-beta.1": {"name": "yaml", "version": "1.0.0-beta.1", "main": "dist/index.js", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML 1.2", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://github.com/eemeli/yaml#readme", "files": ["dist/"], "scripts": {"build": "babel src/ --out-dir dist/", "test": "TRACE_LEVEL=log jest", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "preversion": "npm test && npm run build", "version": "git commit -am \"Update version\" && git add -f dist/", "postversion": "git reset --hard HEAD^", "prepublishOnly": "npm test && npm run build"}, "babel": {"presets": ["@babel/env", "@babel/stage-3"], "plugins": [["trace", {"strip": true}]]}, "jest": {"testPathIgnorePatterns": ["__tests__/common"]}, "devDependencies": {"@babel/cli": "^7.0.0-beta.37", "@babel/core": "^7.0.0-beta.37", "@babel/preset-env": "^7.0.0-beta.37", "@babel/preset-stage-3": "^7.0.0-beta.37", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.0.6", "babel-plugin-trace": "github:eemeli/babel-plugin-trace#babel7-build", "jest": "^22.0.6"}, "dependencies": {"raw-yaml": "^0.2.3"}, "readmeFilename": "README.md", "gitHead": "ac6371fe45ee45da0dd50dd46cef702130922da4", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@1.0.0-beta.1", "_npmVersion": "5.6.0", "_nodeVersion": "9.5.0", "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "48b24957df6a666fe77389f1fc72bfc33d857877", "size": 15546, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-1.0.0-beta.1.tgz", "integrity": "sha512-uuHMAUpqaEFfbNNMr+efB0YKHdPEz24C6yJ9pLppXmyrV8T7vcETppQh7CnvpXk9iGhwqaEi7jPwnFEHaBHWtQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jbnicolai"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_1.0.0-beta.1_1518515923055_0.953793947984761"}, "_hasShrinkwrap": false, "publish_time": 1518515923794, "_cnpm_publish_time": 1518515923794, "_cnpmcore_publish_time": "2021-12-13T13:15:31.817Z"}, "0.3.0": {"name": "yaml", "version": "0.3.0", "description": "Yaml parser", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "main": "./lib/yaml.js", "repository": {"type": "git", "url": "https://github.com/tj/js-yaml"}, "license": "MIT", "gitHead": "18814da3b49eea33186c48e3d5ebf8a4b7c2af6c", "bugs": {"url": "https://github.com/tj/js-yaml/issues"}, "homepage": "https://github.com/tj/js-yaml", "_id": "yaml@0.3.0", "scripts": {}, "_shasum": "c31a616d07acdbc2012d73a6ba5b1b0bdd185a7f", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.6", "_npmUser": {"name": "jbnicolai", "email": "<EMAIL>"}, "dist": {"shasum": "c31a616d07acdbc2012d73a6ba5b1b0bdd185a7f", "size": 5501, "noattachment": false, "tarball": "https://registry.npmmirror.com/yaml/-/yaml-0.3.0.tgz", "integrity": "sha512-ka+SiTLi+1YsgtqfXkS+sIiVnX1mHwcRd0Fzaby37piYmwA9wOj9dZLivZhTgKnzqhIkWcVIajS03CIGEfCKXw=="}, "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/yaml-0.3.0.tgz_1456059619421_0.44368707737885416"}, "directories": {}, "publish_time": 1456059623937, "_hasShrinkwrap": false, "_cnpm_publish_time": 1456059623937, "_cnpmcore_publish_time": "2021-12-13T13:15:32.869Z"}, "0.2.3": {"name": "yaml", "version": "0.2.3", "description": "Yaml parser", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "./lib/yaml.js", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "yaml@0.2.3", "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.104", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/yaml/-/yaml-0.2.3.tgz", "shasum": "b5450e92e76ef36b5dd24e3660091ebaeef3e5c7", "size": 10240, "noattachment": false, "integrity": "sha512-LzdhmhritYCRww8GLH95Sk5A2c18ddRQMeooOUnqWkDUnBbmVfqgg2fXH2MxAHYHCVTHDK1EEbmgItQ8kOpM0Q=="}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1323116289979, "_hasShrinkwrap": false, "_cnpm_publish_time": 1323116289979, "_cnpmcore_publish_time": "2021-12-13T13:15:33.818Z"}, "0.2.2": {"name": "yaml", "version": "0.2.2", "description": "Yaml parser", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "./lib/yaml.js", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/yaml/0.2.2/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "yaml@0.2.2", "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.24", "_nodeVersion": "v0.4.11", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/yaml/-/yaml-0.2.2.tgz", "shasum": "1c83dd0b800f2dc867d6d3e4b260907f8ea0a0aa", "size": 5071, "noattachment": false, "integrity": "sha512-3YuIIDzBqRjTuvAcpDcTFm0+NjY/hxnoj5Mf+DpA5xE9sOSckpq2hrrmCcetB1o2hhC3iv+PxpfSpto5rEM1QA=="}, "scripts": {}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1314812755087, "_hasShrinkwrap": false, "_cnpm_publish_time": 1314812755087, "_cnpmcore_publish_time": "2021-12-13T13:15:34.950Z"}, "0.2.1": {"name": "yaml", "version": "0.2.1", "description": "Yaml parser", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "./lib/yaml.js", "dependencies": {}, "devDependencies": {}, "_id": "yaml@0.2.1", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.4.8", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/yaml/-/yaml-0.2.1.tgz", "shasum": "e925585765208789e7006d673d79e52deee92b54", "size": 4916, "noattachment": false, "integrity": "sha512-jrkvOhYBuSFoMkPptnISCd3v5hge9hPfbIJ4VcVlk6RE00b9gougz83oj097DTmhA9Orj4aiIBaINxeHrFRmng=="}, "scripts": {}, "directories": {}, "publish_time": 1306084858242, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1306084858242, "_cnpmcore_publish_time": "2021-12-13T13:15:36.004Z"}, "0.2.0": {"name": "yaml", "version": "0.2.0", "description": "Yaml parser", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "./lib/yaml.js", "dependencies": {}, "devDependencies": {}, "_id": "yaml@0.2.0", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.4.8", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/yaml/-/yaml-0.2.0.tgz", "shasum": "6c37d91f3081690ec0e11c1b7d624dcb0e37cbe3", "size": 4799, "noattachment": false, "integrity": "sha512-KcL8Uy8WvJhtyi6JAO2/+YMoKpYZU/aL5XSK+WmDJYhZ66oeyEkzEd9dRaAwfv7aLH6fhYtQx1abjA+j1PM4oQ=="}, "scripts": {}, "directories": {}, "publish_time": 1306004893195, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1306004893195, "_cnpmcore_publish_time": "2021-12-13T13:15:37.104Z"}, "0.1.2": {"name": "yaml", "version": "0.1.2", "description": "Yaml parser", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "./lib/yaml.js", "_id": "yaml@0.1.2", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "0.3.18", "_nodeVersion": "v0.4.6", "directories": {"lib": "./lib"}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/yaml/-/yaml-0.1.2.tgz", "shasum": "0459d27fecb4f015f072b15db8ec64ca64af603f", "size": 33448, "noattachment": false, "integrity": "sha512-oVQVCZW+EJaviR/WZMpuJXgOdfGkHfYhXkwgXYcIz71jDmHZC/CWEKHt3A7aWe9VpHKuSntR9UiCtne3w7p63g=="}, "publish_time": 1302884752216, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1302884752216, "_cnpmcore_publish_time": "2021-12-13T13:15:38.257Z"}, "0.1.1": {"name": "yaml", "version": "0.1.1", "description": "Yaml parser", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "./lib/yaml", "_id": "yaml@0.1.1", "engines": {"node": "*"}, "_nodeSupported": true, "_npmVersion": "0.2.11-5", "_nodeVersion": "v0.2.5", "dist": {"tarball": "https://registry.npmmirror.com/yaml/-/yaml-0.1.1.tgz", "shasum": "ac2a0ea9eb89f3d86c436763bd98b5bd92ed92f8", "size": 33602, "noattachment": false, "integrity": "sha512-fSKygJKoSjMFcVbvQOPf4zPANpk/h4oVWr7EQUDWrC9IPYcKBbyd2Fjp8FSuaHj7/5WU3Am2kJCYZnsv9YeMWg=="}, "directories": {}, "publish_time": 1302884752216, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1302884752216, "_cnpmcore_publish_time": "2021-12-13T13:15:39.489Z"}, "0.1.0": {"name": "yaml", "version": "0.1.0", "description": "Yaml parser", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "./lib/yaml", "_id": "yaml@0.1.0", "engines": {"node": "*"}, "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmmirror.com/yaml/-/yaml-0.1.0.tgz", "shasum": "56bf2e28ebe93efe9df58b3ed3f93ad8e16a10c3", "size": 33248, "noattachment": false, "integrity": "sha512-iXPBV1d8WL9Rv3OBQU+48sUth47xI4hjCi6hHvIaqWLeF2G1dA+eWMnDjIF5v1zlqbaF6UKkLP2Wuq/UtVXWkg=="}, "directories": {}, "publish_time": 1302884752216, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1302884752216, "_cnpmcore_publish_time": "2021-12-13T13:15:40.587Z"}, "2.0.0-10": {"name": "yaml", "version": "2.0.0-10", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.js", "build:node": "rollup -c config/rollup.node-config.js", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "@rollup/plugin-replace": "^3.0.0", "@rollup/plugin-typescript": "^8.1.1", "@types/jest": "^27.0.1", "@types/node": "^16.9.1", "@typescript-eslint/eslint-plugin": "^5.3.1", "@typescript-eslint/parser": "^5.3.1", "babel-jest": "^27.0.1", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^8.1.0", "fast-check": "^2.12.0", "jest": "^27.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^2.2.1", "rollup": "^2.38.2", "tslib": "^2.1.0", "typescript": "^4.3.5"}, "engines": {"node": ">= 12"}, "types": "./dist/index.d.ts", "readmeFilename": "README.md", "gitHead": "b70f07a2b7fc6d3796b13390e1761a72fbabec9f", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.0.0-10", "_nodeVersion": "17.3.0", "_npmVersion": "8.3.0", "dist": {"integrity": "sha512-FHV8s5ODFFQXX/enJEU2EkanNl1UDBUz8oa4k5Qo/sR+Iq7VmhCDkRMb0/mjJCNeAWQ31W8WV6PYStDE4d9EIw==", "shasum": "d5b59e2d14b8683313a534f2bbc648e211a2753e", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.0.0-10.tgz", "fileCount": 228, "unpackedSize": 633001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzxiECRA9TVsSAnZWagAAw1sP/jW+I3uWzl0wNMUdyJzb\nkOCXNi0nAaOFJxfsRRjiny7kytk0DEPGcTMsR9/dW47ya4OwmVWjZ6w7wAt6\ne99gxYFkKPNQnYedePdR+XyaDY6pIEBGHykDxI+8p4Bp7krmkJkZqnV4KHXo\nK20AvhER7ww+0w6KFUUIjxTFIocvFdkCEifxrKSkKSAEtw5AszwaDwA/XUdA\nLBD7JZ00Sr6t8T5cAe5v0MnS6ZawxXz4YY7zOoDVG2NQFbotyPp/RicL97eU\nMaT/i3doKSm6EDu2ZmNWb4XkKWBDIyTKJDqyecyWs4w2ybxwl6DlUyHwuksE\nECuNZ02kSAJq+ruEOLSB9SGXBHpyYTzuqSrPMlHDGL/isNt+132naF6l0p8d\nRvc+z6pdEP0b2KhAjenghbpuJF0k/OXOCvwENB3pBQiYdG7G48xc0wwh6MMN\n3BxVEc7OxLKWWGbJ2M4PivytqbnIL5dQ2Wi06aIBIXtdFAMm/dDnd4nbBa88\nhz1hCeIIKaqMv9CU+47HzSwpfryy8hDmEGUsNJ2vadrPRwbEoGviCQhTCj+M\npGUnU88YKKyUtZfNahp0wlxQS3skLidYnFJIwPsZiRt2+wO2PDkUbbJ4u0k8\nX8q8VJkq3G/2poyn8IFy7VgUFHnD4FAXi8DP9Ljpr1t7k3XmBHfz++l3oUut\nw6J8\r\n=jHlm\r\n-----END PGP SIGNATURE-----\r\n", "size": 104545}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.0.0-10_1640962180170_0.22082213469053258"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2021-12-31T14:56:00.292Z"}, "2.0.0-11": {"name": "yaml", "version": "2.0.0-11", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.js", "build:node": "rollup -c config/rollup.node-config.js", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "@rollup/plugin-replace": "^4.0.0", "@rollup/plugin-typescript": "^8.1.1", "@types/jest": "^27.0.1", "@types/node": "^12.20.47", "@typescript-eslint/eslint-plugin": "^5.3.1", "@typescript-eslint/parser": "^5.3.1", "babel-jest": "^27.0.1", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^8.1.0", "fast-check": "^2.12.0", "jest": "^27.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^2.2.1", "rollup": "^2.38.2", "tslib": "^2.1.0", "typescript": "^4.3.5"}, "engines": {"node": ">= 12"}, "types": "./dist/index.d.ts", "gitHead": "ab8ae8639b935dd11a3dde81b570db6ee2ec3354", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.0.0-11", "_nodeVersion": "17.4.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-5kGSQrzDyjCk0BLuFfjkoUE9vYcoyrwZIZ+GnpOSM9vhkvPjItYiWJ1jpRSo0aU4QmsoNrFwDT4O7XS2UGcBQg==", "shasum": "269af42637a41ec1ebf2abb546a28949545f0cbb", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.0.0-11.tgz", "fileCount": 226, "unpackedSize": 649241, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiOY8FACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqadg//WIrz3uKtqKNi5ClBKioR8cRpOutOfO11T2zZCP7ahhpUCaVL\r\nyx6qcQUwCO8/BDQcPy69pDy0ZnnjEpZ4wgWGwZDG+kOA17P0Sox70N/9B2ti\r\n7/838rXfbKOI4g/hmb4SnI1dhsMmORu50hZEtdUnX6CEkSralvV7ZYCphAQR\r\nnZpOh7N3dNAG/wpqw8uKmelqnL2pDykZcygMifQ2YrTFg/PDwMFxGYRu/loD\r\nby7zc3WAyMGUk6gfJdkOHxM8bEFw2KaaaellmjmAcru5gTPiyNNdNWvf5zHB\r\nmHoLJhn0z50V+7nlq71sQZP3aaQ1PGYPPPrEDjh+JoNsQU4B/W6xC3os5uiZ\r\nS7maNs8PK00bGtzUzXPgbLnp8V1LySG8AkL6jjOy3DogpiDbm9T2mLDTiau4\r\nRL55Q2s3Fjw/aPg3zrQ02lqHe1On859FWka2xQYZ4g1OWDCp/sBV6NfU7s3W\r\n3nwOSc61k+j6NxCGDGZTA4NPAmjoB7eFzCmSD/UmPtYPSDgHF9MPEZYTScxI\r\n0y1+MQSJBVj1tbcav1YWnGEGZ40h+OyGOi+JtVU7nkHX/inSr7neASGzctSb\r\nPepPVGWY6oZwUqYzQsYT80VutLE9CQ1gTLiEo632XWuE5JhYlY6prqFnp0YL\r\nbjt1ssqilWoILc6IUOzshkzA3GD6vFpTeCI=\r\n=uKYg\r\n-----END PGP SIGNATURE-----\r\n", "size": 105523}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.0.0-11_1647939333001_0.7017416418236644"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-22T09:23:26.516Z"}, "2.0.0": {"name": "yaml", "version": "2.0.0", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.js", "build:node": "rollup -c config/rollup.node-config.js", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "@rollup/plugin-replace": "^4.0.0", "@rollup/plugin-typescript": "^8.1.1", "@types/jest": "^27.0.1", "@types/node": "^12.20.47", "@typescript-eslint/eslint-plugin": "^5.3.1", "@typescript-eslint/parser": "^5.3.1", "babel-jest": "^27.0.1", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^8.1.0", "fast-check": "^2.12.0", "jest": "^27.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^2.2.1", "rollup": "^2.38.2", "tslib": "^2.1.0", "typescript": "^4.3.5"}, "engines": {"node": ">= 14"}, "types": "./dist/index.d.ts", "gitHead": "85aa802a406dc5b8ec69723e22da7b7fe07ff9c6", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.0.0", "_nodeVersion": "17.4.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-JbfdlHKGP2Ik9IHylzWlGd4pPK++EU46/IxMykphS2ZKw7a7h+dHNmcXObLgpRDriBY+rpWslldikckX8oruWQ==", "shasum": "cbc588ad58e0cd924cd3f5f2b1a9485103048e25", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.0.0.tgz", "fileCount": 226, "unpackedSize": 657211, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBWy45dGRfgeY2xmwey6Un64te735S8zgfUHmfwjIivUAiEA+308zty+ZOVdfwe1rr8O1B5VuwJ9xE28ofA9X/7z0eI="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTVOUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoTNg//SwHrgeT5cc8wzEM/aV9q0VVzNu+XpmvygF9lD6OrtHV2T3Mq\r\nQADfZIC840jDyCB8eC3nAZZwl0OIl+QbV2sbUWxWkePsuN+uGgd7vsxss9+v\r\njEQYHUxoUEmNKEE+L/E8YxnywLEKsDZELfnwAXTjlx8GjQi4mH7BXoAA/jYO\r\nEZCqtZcJ2kC8SQAqrU2Hajol8pavOWpuw+QUPbwsuDPtQNuXSNOwwrZdJ3Ni\r\nr+I/tg3i1O2SScSvh2HOgRQWYmo3soBwlT/h+jPs6rQmrXNvB3upEQaI0lTl\r\nX0rgfoSgvxuatXTZ/aMvVRyPJmOy4dAFKW2oOYiEx2fpQHsqPLMYDYkkuJLP\r\n5Xhc9G4tQgDAF8BMcjPL3Sk0APYV4ymE6ev2ZsYGvJvJdKWmIiLMZuVEMPeD\r\nbVTGmZej66/b9Pohu0KvghuUcpSSbiOAeMAe1HSx8G6AJjHrXLKTMyznoIo8\r\nxlOluw/IeyEhD7bcQHyYKYBeTzTsbTAHk56+WAXKKIRQJI8Zt9/oxWOuo1y5\r\nZEwrDpCRKZzkMUJ5OfohbjuZjZsm1TNEsu17ZePE3J6LmsFUzqYqDrJXJMjR\r\npJ0oVnRyRuuCcBUJAg4BavjoZEEhQJZ3qBJso92cG/fKzKqhtQTEQEn7tBtW\r\noSvhM6t985yGvSqnhVj3kdxFGqj37d4vctI=\r\n=BeUm\r\n-----END PGP SIGNATURE-----\r\n", "size": 106655}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.0.0_1649234836114_0.7358295768032741"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-04-06T14:07:28.654Z"}, "2.0.1": {"name": "yaml", "version": "2.0.1", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.js", "build:node": "rollup -c config/rollup.node-config.js", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "@rollup/plugin-replace": "^4.0.0", "@rollup/plugin-typescript": "^8.1.1", "@types/jest": "^27.0.1", "@types/node": "^12.20.47", "@typescript-eslint/eslint-plugin": "^5.3.1", "@typescript-eslint/parser": "^5.3.1", "babel-jest": "^27.0.1", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^8.1.0", "fast-check": "^2.12.0", "jest": "^27.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^2.2.1", "rollup": "^2.38.2", "tslib": "^2.1.0", "typescript": "^4.3.5"}, "engines": {"node": ">= 14"}, "types": "./dist/index.d.ts", "gitHead": "6e969ca6d1fad40cffbdd107681ba560879fc1ff", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.0.1", "_nodeVersion": "17.8.0", "_npmVersion": "8.5.5", "dist": {"integrity": "sha512-1NpAYQ3wjzIlMs0mgdBmYzLkFgWBIWrzYVDYfrixhoFNNgJ444/jT2kUT2sicRbJES3oQYRZugjB6Ro8SjKeFg==", "shasum": "71886d6021f3da28169dbefde78d4dd0f8d83650", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.0.1.tgz", "fileCount": 226, "unpackedSize": 657731, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDjpD4ZWxj396hhInyo1CtjqcjW8BOdjUH81HB6m6eXtAiBJypDoFaVl/81JEMaWZ9tZUInbwIl6Dxc57NOL37Frgw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWdd2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpkkQ//fm/pEi6OYqDwD7C5WG9yyvaoGnZzoR9HlxJ8jfP3VcaSanYK\r\nrhZQZ4sWOxAsIjNwjlkzVQmxDoYnH0fSyBnZBJ7p/qfJ0MJUXVkT2nrD1XaR\r\nXx58BvhSPCnZEa+mmYFxpRparJQq/ee5ROtJyQhkxYgbg6UIP4zVzR3M+3zV\r\nf+35bnB5McgJt8yaV7NSqplYFX4YlkzLNo/iRB2d8twj0qHhq6FXiG8Im6d1\r\nT/9WZxjgLchMj+RHQZ6T3dlO0x1Yuo7KCEepI1VrSmX5jCB35myaFhDI8utU\r\nC88QOrsPNe20DZMWtf5AeThkX3k/+P1t28e4Y/ePjaVM4lHfmuT68nbWFcaw\r\n/1FE7S7kuXIrLbNThBiNeZPIXoxDGzNsqe5vIuBGl5QEhRv12fZ+YaKWt9Da\r\n1GDDZa/3orF4n6tONhMEb/JYmgqTrvxAtLjMTMM9AzltHsvbMNgpWWleiSuM\r\nGfiChMtj7nB4D62jRpst8JYfTbYRBQMuAq4iTFgYNX/SHLxGl8aIVIg8s/we\r\nNgGOsEsvAgoxFNVt0gPVJ3L+DLKMmSS4xbYh+cayhPU9j439JlKoJD1FhOzW\r\nr550nXjI7jfQcLjxfYrWcSN7NfdIqH36oVAHd0mfBDKJaKiE/Oqdi3DJG6wD\r\n6874YnhC9itUFh7Vr7zpsnZSth6Z0DsoI/s=\r\n=Gd3v\r\n-----END PGP SIGNATURE-----\r\n", "size": 106751}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.0.1_1650055030736_0.038965856380512864"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-04-15T20:37:15.036Z"}, "2.1.0": {"name": "yaml", "version": "2.1.0", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.js", "build:node": "rollup -c config/rollup.node-config.js", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "@rollup/plugin-replace": "^4.0.0", "@rollup/plugin-typescript": "^8.1.1", "@types/jest": "^27.0.1", "@types/node": "^12.20.47", "@typescript-eslint/eslint-plugin": "^5.3.1", "@typescript-eslint/parser": "^5.3.1", "babel-jest": "^28.1.0", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^8.1.0", "fast-check": "^2.12.0", "jest": "^28.1.0", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^2.2.1", "rollup": "^2.38.2", "tslib": "^2.1.0", "typescript": "^4.3.5"}, "engines": {"node": ">= 14"}, "types": "./dist/index.d.ts", "gitHead": "c80d4c2ba972e53cc60ddd2d7136446bd9dae740", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.1.0", "_nodeVersion": "18.0.0", "_npmVersion": "8.6.0", "dist": {"integrity": "sha512-OuAINfTsoJrY5H7CBWnKZhX6nZciXBydrMtTHr1dC4nP40X5jyTIVlogZHxSlVZM8zSgXRfgZGsaHF4+pV+JRw==", "shasum": "96ba62ff4dd990c0eb16bd96c6254a085d288b80", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.1.0.tgz", "fileCount": 226, "unpackedSize": 648556, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAh40PK9vpIKCyjVYXs1QJewyqCkhnnk7Bm640K2VBLpAiEAiP3zubk+IU7qf2HxmQck5/I8nj4gqQQnzw6jMyq8KKs="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJif3krACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoA1hAAmgkgkbJUSVWflfYzU4XeReS+KpcOgFE64jlxgqi3p9oMvTLI\r\nMKlWaUSdYoLwPOW7Kf2kj/OPdd2fUVOeR5PGaNHIrJ1EjDalWQRQKe1rMYWF\r\nc5I2ys0hjPiAI6W05O2oG+TwqRNiChP6RRfEkc17IoEIaM6s9rrCaxy/pCba\r\nB0DnFxn3yonfMSh/0FU+6AnEvpPd2cjrbc8m6ruaCYRpqG4i2Bca6bo2N+2X\r\nfFENCBhkCyblF/DRQRB13WCzq3MklR2z41nGXgbhCqSHD9eYkhW5QEdOO3ER\r\nzXsCYpESjStwEVyGP2bPeULUeuunDaGk0IPwv24ql2bTfknj9E65cWqQi+qo\r\nkVIeI1MWYepJ198KNzD1CwBfYEianqraQMztZ64W6ULFOORzCtiRkx+7JZzS\r\n7kDXt+aeocQFtRoWgY1JDdAoQ5lPXrmZgiVvmn6kiW034D5lbXaLBVreuQjs\r\n4inEwQl5WD5EHHZgiaORjqRkfU+1/auSXf56W18eXTKofxj+X5MtP4R9eTEW\r\nvgMx9os6rXHiVEB0O9uykvxFmXyBOeX4QNBl+1oZHhWJjGVbxBD25omFwAMo\r\n7g6y3iDJZqY8BaoWsMyElYNw8oryrOar0YV3HcaTjrImTSup2VA5qN7Tp8c8\r\nPfHafeGiGLF8l9VYDtB5qt6K2uJYIEW9+68=\r\n=nJJX\r\n-----END PGP SIGNATURE-----\r\n", "size": 106283}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.1.0_1652521258910_0.5815607687292765"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-05-14T09:41:06.916Z"}, "2.1.1": {"name": "yaml", "version": "2.1.1", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.js", "build:node": "rollup -c config/rollup.node-config.js", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "@rollup/plugin-replace": "^4.0.0", "@rollup/plugin-typescript": "^8.1.1", "@types/jest": "^27.0.1", "@types/node": "^12.20.47", "@typescript-eslint/eslint-plugin": "^5.3.1", "@typescript-eslint/parser": "^5.3.1", "babel-jest": "^28.1.0", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^8.1.0", "fast-check": "^2.12.0", "jest": "^28.1.0", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^2.2.1", "rollup": "^2.38.2", "tslib": "^2.1.0", "typescript": "^4.3.5"}, "engines": {"node": ">= 14"}, "types": "./dist/index.d.ts", "gitHead": "96c699375abf6980ea5158e6ed2e86cff2927636", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.1.1", "_nodeVersion": "18.0.0", "_npmVersion": "8.6.0", "dist": {"integrity": "sha512-o96x3OPo8GjWeSLF+wOAbrPfhFOGY0W00GNaxCDv+9hkcDJEnev1yh8S7pgHF0ik6zc8sQLuL8hjHjJULZp8bw==", "shasum": "1e06fb4ca46e60d9da07e4f786ea370ed3c3cfec", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.1.1.tgz", "fileCount": 226, "unpackedSize": 648639, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD0oxHZGZNuh9ZntSjqzyFh2ubXBbPITzuttudzE1gPzQIgXTnd9Pw2vZjE0M2Inv7yT5j1M4/0qO5BUbMlp4P9LOU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJik/MkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoHCw/+K392tfnXyE+MjUIsB+SbmG1YEiptDoVXT7QTvmtev4gtDC15\r\nEZBeGZRruewuScB4kYpHKPma4xLV93LOIUPB4Ti/leGZE2DqJDX7B+4AHZbg\r\nz20bgVpDhDA+h4ToI9Gpf7T36RSmhIf9pqIKYHkAjipnwr3aD9HssFH9FDaU\r\nXZyyFUi3nZMS6e1sjL9fSOG/jVctt/m0m+w/BZ2bd09+fuB1wX9CtUiSWU4M\r\nreAC5OFo7A6JMDzx0V5hyghbAsr3upxoDMoNN9vyapZDlKwCCFnlWN2lAQoB\r\nrgiSKxCvSQFZh7RIsR1Y/GGgw5AXLe6CDQ5c28kwWbkV91aZrB7bZQK6bTmF\r\nHRlTCaAFJNVRQJVWGOX8JWUOSvww9q4x51tmc50+ugHh8hvczFcIq+Q0o+cf\r\npTIgxzxfSAbXYNLgjjbZV0X1OpSlpPGAHKKy5bI9Jo3AJHTdR62x7uWici66\r\n2tMZZN2H9P7t2sh9Vyj3GhfTbnzEm5aPIhl31u5uZwMqpnsj9vfYkPNsLcF5\r\n9K0OJptHUl7tTm20ZhV8tam7ICZ61a/LVLoFGLnyzGCJQKwODW+0Df+FP9cb\r\nNpJzwoHRVfaRe4iOj6dHHSdH6eZ4o56P2Is2W/P5kQtZfschDbgw/pnnKGym\r\njsFODEMm5pzRQK15IQONY/U4U5+nZNjZOLA=\r\n=D0ai\r\n-----END PGP SIGNATURE-----\r\n", "size": 106270}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.1.1_1653863203891_0.4705508587556806"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-05-29T22:26:47.731Z"}, "2.1.2": {"name": "yaml", "version": "2.1.2", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.js", "build:node": "rollup -c config/rollup.node-config.js", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "@rollup/plugin-replace": "^4.0.0", "@rollup/plugin-typescript": "^8.1.1", "@types/jest": "^28.1.8", "@types/node": "^12.20.47", "@typescript-eslint/eslint-plugin": "^5.3.1", "@typescript-eslint/parser": "^5.3.1", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^8.1.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^2.2.1", "rollup": "^2.38.2", "tslib": "^2.1.0", "typescript": "^4.3.5"}, "engines": {"node": ">= 14"}, "types": "./dist/index.d.ts", "gitHead": "acb5f47ae20d597b565a014866bfa44ebc67cffa", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.1.2", "_nodeVersion": "18.8.0", "_npmVersion": "8.18.0", "dist": {"integrity": "sha512-VSdf2/K3FqAetooKQv45Hcu6sA00aDgWZeGcG6V9IYJnVLTnb6988Tie79K5nx2vK7cEpf+yW8Oy+7iPAbdiHA==", "shasum": "eb0f535eb309811b60276a9cc8c02af4355db420", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.1.2.tgz", "fileCount": 226, "unpackedSize": 648829, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF9fitZUo7MWnBfaxDQJVU524Wf0X1HhdysCNLclN+qdAiEA+Dm1mpmJlec0fnkfVzQCeYW5so9HoXPhisQmrUqn6tw="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjOcEDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrbuw/9HAm3J/nVWovo+foePg5l0THqOWIGzfxpeIyys1lpF3hIzh8i\r\nzSi9rX9+PF6WLjNPb3cnHhH0BpRkHBbhtQGwJWxR5Q5dLLOxKLBZuEkrACe3\r\ni4LOmBL6npgV95CxPRt0ha5jnusfoLgmXRZyaCjc3DM6GPUEpiUX9jw8D+ys\r\nbtp63a7zDGYSOTEnn3xNBu0g+qcGVgbQcCLe+FphGK7h1nN6uCZwgMHXcjDi\r\n0OfA+2xnBOZgz3c8jsNtvoWMeG4OB+W7aq7sUFru2Wla6uAAOEJ3aBvK3k3s\r\nXHtOeFCPJnAWK6QCgcL7v+znv0MSLTECAn732M5VGlRKDWohoEdT+0AY6MSk\r\nJQZgT4Tosij/fmZvK+j92hn843D+Tuj0woTn57MnlEG351sPakpihJq/EIAq\r\nqky9jomQKlfXva/VD71m+Vj9DMsD7fdnMMQztCmM0FOyMUn3UjrkOnv6JzY+\r\nT6yxKupkmKH6cGfuyivL4TT5Mg9ARexmseVE59ViwUjQx45OdLrCpJlamOpa\r\n2yCUmATSiMRUrqk5+TudzNxilcMwVhhqApNJNVh+aHgJ9g5gACpQ4HnZjsMN\r\nnDSLK3MCqfE2tkjQo5SiFHymIl72luSeI429Hb5/1DDprwdnRYFGTA11lfxQ\r\nJruh0a7rcBH5+AK5YosahJqTAs8F2dP8dqI=\r\n=jjXp\r\n-----END PGP SIGNATURE-----\r\n", "size": 106330}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.1.2_1664729347020_0.785085770859173"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-02T16:49:14.959Z"}, "2.1.3": {"name": "yaml", "version": "2.1.3", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.js", "build:node": "rollup -c config/rollup.node-config.js", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "@rollup/plugin-replace": "^4.0.0", "@rollup/plugin-typescript": "^8.1.1", "@types/jest": "^28.1.8", "@types/node": "^12.20.47", "@typescript-eslint/eslint-plugin": "^5.3.1", "@typescript-eslint/parser": "^5.3.1", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^8.1.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^2.2.1", "rollup": "^2.38.2", "tslib": "^2.1.0", "typescript": "^4.3.5"}, "engines": {"node": ">= 14"}, "types": "./dist/index.d.ts", "gitHead": "8e7e57f2ed478213509af2e97bbe7b2395ad5a91", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.1.3", "_nodeVersion": "18.8.0", "_npmVersion": "8.18.0", "dist": {"integrity": "sha512-AacA8nRULjKMX2DvWvOAdBZMOfQlypSFkjcOcu9FalllIDJ1kvlREzcdIZmidQUqqeMv7jorHjq2HlLv/+c2lg==", "shasum": "9b3a4c8aff9821b696275c79a8bee8399d945207", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.1.3.tgz", "fileCount": 226, "unpackedSize": 649153, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDOCJk3F4R0uvdGgkwiNHd3AeHpB+wYz+bgZpoMM1oYhgIhAMRwikGs9o2Qe1JilVSFYXpRR98EtngT1zL89FADmmdS"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjPTMCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqhHA/+Iw6m2Tl/kDABvSwCW3xDk4sSQ8OnYgUixhYwuXc1yOjM4tOM\r\n+RTAAigjC6vmCmVsD6tR/SjHx3Gra83Z+SAd21wR+OEMPB9jKFTYDSV9Hr7M\r\nr+Poox3NJ3SBeFQmKFVjUDhdRxBtYAw5U0l0NLOI2AvQaXn+7gjrRnFSylms\r\nSVYPEwD/Sc68qnbnAygNgRL+UGBbZZlGFZwEwYr2OOGgMSNoQz1n0P5PJQEY\r\n7IN4MrmxuSBAwWPO74tvXrcZODNGV+7VDC37j9qPtohyqPjIYdUpQtflI1aZ\r\nw6c6uvJAXrFYvK0uJbvTYrLsSHTwknBHfcjof/medHzFFUIBHcAtOQ1KbU0N\r\nHvkCQVOi9VVgcy+7bGvFTKZAQmrItIkyV1BInZ/nwNsIA2i+ZataupdAWVN7\r\ni0lpQ8GJdYPWVzY6Q7/Srg1WZbhx2nQMbFonzwt42Rxw/x0vgxvv91aYzsxt\r\nauAKEEDRxrSq3MnmX7EpBRCBXtAAlVCVW996ByHO82EvrhkpVvpLjuMFjRJL\r\nXIqC9tKHLVHQMjTZyR6BkuvATrEqh2f8gGP4OD6xhPaUEFG0BytSJSA+ZkTl\r\nU7xhtLd5wGB1Ja3nGJBxVu5OvJogg0K0HAxfrAVqvh1G7D4OMrQ+6TW1wR8n\r\nf8u865O4QOICUjrTJsvfkVMjGvbBJiQwNts=\r\n=XEK9\r\n-----END PGP SIGNATURE-----\r\n", "size": 106347}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.1.3_1664955137815_0.8699750849046803"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-05T07:32:25.041Z"}, "2.2.0": {"name": "yaml", "version": "2.2.0", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.mjs", "build:node": "rollup -c config/rollup.node-config.mjs", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^10.0.1", "@types/jest": "^29.2.4", "@types/node": "^14.18.35", "@typescript-eslint/eslint-plugin": "^5.3.1", "@typescript-eslint/parser": "^5.3.1", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^8.1.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^2.2.1", "rollup": "^3.7.5", "tslib": "^2.1.0", "typescript": "^4.3.5"}, "engines": {"node": ">= 14"}, "types": "./dist/index.d.ts", "gitHead": "35764085bec81eb075ad16241508de0934ec3477", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.2.0", "_nodeVersion": "19.1.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-auf7Gi6QwO7HW//GA9seGvTXVGWl1CM/ADWh1+RxtXr6XOxnT65ovDl9fTi4e0monEyJxCHqDpF6QnFDXmJE4g==", "shasum": "882c762992888b4144bffdec5745df340627fdd3", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.2.0.tgz", "fileCount": 226, "unpackedSize": 651110, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDMF5FPGFFkVZ7srOpz6haU5l6X1avkzzMm9YnRw8k+VAIgN9uzkd2xAox7Yg592ifysKjMEIee1en4bvGdY1wTnuo="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjovyvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmob1w//Q2xPtikuF23J24hwLja0NO6Zroaislzqmz/+6ea7JZmlhMpo\r\nrGLuT0dd/v/AfBu3aEqxIDm/9u/Gmxr/Why7Ovxp5Ag2QW2LlZ7dqddeUVd8\r\nnOshC/BqeNm2ADXBPFDR+VymRVKFOQIdH2WJniCKFcP3180/k/g820o9O72G\r\nFBuq4vThNnZ9zd74i+OSjz/MXG/Vnk8XVPMxYadUQZE8PWwDlCGcgnkIDLjm\r\nLTtmLmtgxmoiqY5yk2Y5W28zzlFssNbBt5242bjPQRoXwhE5md9fP1HfQgU+\r\ndAS/Fm6lKzeUWEU5zU2p9QL20Xs38tI96SHk3Meo4nO7It/RWtg8EcQ56I7M\r\nHmUm1Jdn9cczeHBZwzGn36TMZ+jSMclwHrBWqFlgPwcL0N28gX37xZS58QgA\r\ngup86mMQURW8yA/QPOoHybraCcfH3/cRBc0dtIvT5nhYXcASbEGOo/+nOkYg\r\nboQR2Tqxr5SqWOmD6n3h7WnNEPF3nxLg1qCVFKbiVFDxH5W6xo+jZ95RA7QA\r\nXzCY0cjOgomV9edNGxigk5yeGwD+mnmgTwxH8kvfm0mTkp8dmJqRlCzu957j\r\n9F7VYaaBGunj+OApPMW9O0vyaR8dlvx1X4nQGH2AV8ozgqb+Acvk6gnZZMTk\r\naQKihrIFvWMo+YZaQGKTKXoqtLRgjZbuyO4=\r\n=CNmn\r\n-----END PGP SIGNATURE-----\r\n", "size": 106667}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.2.0_1671625903718_0.31332421603222826"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-21T12:31:49.791Z"}, "2.2.1": {"name": "yaml", "version": "2.2.1", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.mjs", "build:node": "rollup -c config/rollup.node-config.mjs", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^10.0.1", "@types/jest": "^29.2.4", "@types/node": "^14.18.35", "@typescript-eslint/eslint-plugin": "^5.3.1", "@typescript-eslint/parser": "^5.3.1", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^8.1.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^2.2.1", "rollup": "^3.7.5", "tslib": "^2.1.0", "typescript": "^4.3.5"}, "engines": {"node": ">= 14"}, "types": "./dist/index.d.ts", "gitHead": "c914dcc9dd19c9c4da065fbbc4c920f244304dec", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.2.1", "_nodeVersion": "19.1.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-e0WHiYql7+9wr4cWMx3TVQrNwejKaEe7/rHNmQmqRjazfOP5W8PB6Jpebb5o6fIapbz9o9+2ipcaTM2ZwDI6lw==", "shasum": "3014bf0482dcd15147aa8e56109ce8632cd60ce4", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.2.1.tgz", "fileCount": 226, "unpackedSize": 651418, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHDUy7tBE2G8V3uAp6DZsjJFiIHgXJytBF5DU5zu8/6hAiBMmcQYVz8zM0wl8R6VvQCWeERX7LnzCyaC87Hxvm2PiQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjrrY1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqEsw/9EI/tLxEh28pGjnn5gSuKGnR/1jAAJDx1RLuhh2ywS5TQTWZ8\r\nL/FA0+q8dPE9W+Gdsdla2JF0KevnjQaQ7EdqQsbNhQWaLsddIKYyuIKcSrXN\r\nSTy+UGAF5YeqLwol0lxFhgFgsj0we1DmMLlOdCTaGPm/61hwgn4EqfZArzWj\r\nf3Eokjlh2HI6eB6R6Zgzs7tMbdNAv4IXmqeTyeiPFaN286XL5bqQX5Am0YdE\r\nxgT5tyPUBqOCiEfz87oqMXSUa7eK1fHQON24IW/TEWCkPT0AsGc4bpNLNO+S\r\n0R5OgWGbKea5LqQFdTF8SXjpdAsKuAW7EacFZO+IaYCS59CXU+PI8iG6mueN\r\nqpes6Ra2tSTaqWLqtdCuuHxZPw192bB7vHYBxAHUnhyJ4Rv9YxxpKQA/Gq0p\r\nDyZw6wSYP/CS1zvyWgSTZ05zbzktGWlBfcQr4A44gnwwa/aXvkI3vmNPa1e3\r\nLQg7Ydi5Jvk+vYpkmHthxo8065XOjVzarQta6QxI1a5ok+pEQtxRdwm4NCfi\r\npgAzTg+tgzcgpRddDE97PZrCKppx5MiNDGZ/Udqs4GqV6jc3a2CjKX0e+5MG\r\nsrO3CFCVqksTEqLXYUCuFs88YgEEWz8daMIv5rkjVJp6WPZinvgHu01VMcEg\r\nfmpf2hq/gLiIwDJJnDDfMipRcJq7GuA6gwo=\r\n=yBEa\r\n-----END PGP SIGNATURE-----\r\n", "size": 106688}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.2.1_1672394293215_0.47164168289875663"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-30T09:58:40.842Z"}, "2.3.0-0": {"name": "yaml", "version": "2.3.0-0", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "typesVersions": {"*": {"*": ["dist/*"]}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.mjs", "build:node": "rollup -c config/rollup.node-config.mjs", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^10.0.1", "@types/jest": "^29.2.4", "@types/node": "^14.18.35", "@typescript-eslint/eslint-plugin": "^5.3.1", "@typescript-eslint/parser": "^5.3.1", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^8.1.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^2.2.1", "rollup": "^3.7.5", "tslib": "^2.1.0", "typescript": "^4.3.5"}, "engines": {"node": ">= 14"}, "types": "./dist/index.d.ts", "readmeFilename": "README.md", "gitHead": "72b107e2907229fe18dde084b8703e6231ac843b", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.3.0-0", "_nodeVersion": "19.1.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-XBMVJHQ9YI5kPrf+yMD0P3bXHjEA7M8yYsJhqh8pG7J3fh1Gg/hDoAnvVorqO4SNBVblsLfs2qfJ7Kl+7pknZQ==", "shasum": "0638f13167d2e82b25173d0192e588ead1c32d68", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.3.0-0.tgz", "fileCount": 225, "unpackedSize": 654029, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA0Vhr3pY4XmzVkmDn+Y/YulVAvDh6VH25YB7LFmFSZqAiAtyT3jV8lY91cp8QDgG2pdL5e4m5WbAfY5SiMvZzTFsA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkDHq0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoYFBAAjhjxEpECvKH/ZKTWhMiGae0CIGdJmXeTOOEdEW96rN9hw/+1\r\nbOL2+xod8fk2Q8nBUEJYlatzPgP9hGpNtMJTpyVUlUhFY3ATdvR90FeC8kjp\r\nJStT9fn/UQBnX2bbSB1KHngnUG8xkTQyG3NjXmXvpbBBV/xP1jrZHKPc6Zyd\r\naQvp0LAeA/WrzoJqd2TFKgrPTBxJsMBf52UmQPj6hzIl0LROERpWnwbZVk8+\r\nL9O66tME4mqCWyuhF5rXOm4t+7H/iidCuXsLHBFVae710dBKn5kUMjl3kVnL\r\nMqWnS+MCIeDuHwS7L9Pr6321SuD2ymD5b/kDHfug8zjh17ZQe23jnnDunatF\r\nbsPB6Paj6lT3q8PaIE61Cx+9wriXFnfBIegiNI5IK+Lk56+PxXjxDZcVXbeR\r\nFO/HXfFUC7e6+iZsf8cH8alcDZwT86/9Xn1pGcTGJjzOEYdmec+cNBN2EyET\r\nmqewhFUAKUJfPhk/b66g7ToGGVit2ktHwSBFp9NJxG/0tQ+LlM6l/UL7a+S5\r\nZl2WsVoXlzWOB5dnoqHKbwnAtsbU+M9OwpKD6xknF8CD/Q1btBphiPZXtgNS\r\nEHb998Qf0MmwsqmUcKORYtSF/mHQQ3swub9wp+9jWRaKYdnGaV3ErfnNW7qS\r\nMwfJuegTmv8GnkcTRux5RTjsrNyIQT/78S0=\r\n=g64d\r\n-----END PGP SIGNATURE-----\r\n", "size": 107177}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.3.0-0_1678539444211_0.9619085793232565"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-11T12:57:24.376Z", "publish_time": 1678539444376}, "2.3.0-1": {"name": "yaml", "version": "2.3.0-1", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "typesVersions": {"*": {"*": ["dist/*"]}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.mjs", "build:node": "rollup -c config/rollup.node-config.mjs", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^11.0.0", "@types/jest": "^29.2.4", "@types/node": "^14.18.35", "@typescript-eslint/eslint-plugin": "^5.3.1", "@typescript-eslint/parser": "^5.3.1", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^8.1.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^2.2.1", "rollup": "^3.7.5", "tslib": "^2.1.0", "typescript": "^5.0.3"}, "engines": {"node": ">= 14", "npm": ">= 7"}, "types": "./dist/index.d.ts", "readmeFilename": "README.md", "gitHead": "84b38114113d49abea3b80f1b4f3be680d736416", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.3.0-1", "_nodeVersion": "19.8.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-nNIWKeoO+BCmuQ3y3/S55fduk1BbCmDu1H3S3NKg9c2bv3cup5TYG6If2181SWbWmSVBV1o78ysoIzV8dxqfcA==", "shasum": "9dd8a1b2d0bbf780dee1be83e0e20ba7ee953d0b", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.3.0-1.tgz", "fileCount": 228, "unpackedSize": 658130, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC4JPD5NyoYVVsldKTMte6dP2mj1oZRhf84awL8ixkDkQIhAIYuE2Y4YqGep9sS5+gZyoTSCiNt9oN2ucIQDgme6dln"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkK99sACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo+Ig//RSLBrWF0NeJDQgOLtRCjRxA1I0CN4c/fWiF1E+qYOpIXy+Nw\r\nNSSTJH4QRs/Mz/Vct/EXIGN2R9QxZzmPDms9jORc9o98kLr0Nq+Pnh/ysfNR\r\nrDpiZFLeZ1nDCYM1q2gChclGLhQU8IDX7dNeJqIZNxV5IWciIK4b8MtY1qKF\r\nHY9mCMMCiR+wJ1DAWs3Bmf6AVG+vD4NJysuICYeAxqU5EeWyf9ZFqWJC9wB9\r\njWaMw+A2ElCdmnSL1alnsGGR3LCpnilIvuFZhuvvVKPkF5feN5axEwz/6uLL\r\nYclNwWjJAjgjCfd5x8fOdGuldoWms7KTulG8vSQHYSk3akmRTIDF93dFyzWJ\r\nf7Lc8XtMFMWlE6gMOw/dvz5y42ROI3kyo/EOQ/+4RPaIQLfeMAe/Tsl5XnsS\r\nwEysdnVI3HGOjL6YUgnxxxSiXPLI0zoyQVEuVtUGGjkhMRT4J2L6xuV/BSSL\r\ntQYQPSr4su5InDsdggsRL2UU6TMrUa8YG+rVFnm+CVRIXQ9tLneJEDf6SfF/\r\nny4vZQd2FErD9bjhtbMWudqzGJ3siRICAP46WlykzvYAXcwdsTsNHIn7dY7v\r\nw1skSbrnO0McS6h/aUZPu+VbgLn2gcqI2IvrStF1CoTA37UtB2/634rg935Z\r\nYcjkfpWjG2i2okiuLbDxFzxo2Oj/CbxJHPI=\r\n=ab5E\r\n-----END PGP SIGNATURE-----\r\n", "size": 107677}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.3.0-1_1680596843931_0.21392240450844513"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-04T08:27:24.123Z", "publish_time": 1680596844123}, "2.3.0-2": {"name": "yaml", "version": "2.3.0-2", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"types": "./dist/util.d.ts", "node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "typesVersions": {"*": {"*": ["dist/*"]}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.mjs", "build:node": "rollup -c config/rollup.node-config.mjs", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^11.0.0", "@types/jest": "^29.2.4", "@types/node": "^14.18.35", "@typescript-eslint/eslint-plugin": "^5.3.1", "@typescript-eslint/parser": "^5.3.1", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^8.1.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^2.2.1", "rollup": "^3.7.5", "tslib": "^2.1.0", "typescript": "^5.0.3"}, "engines": {"node": ">= 14", "npm": ">= 7"}, "types": "./dist/index.d.ts", "readmeFilename": "README.md", "gitHead": "a2afb000c399a9a00b4223113c3cf60fc76fc443", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.3.0-2", "_nodeVersion": "19.8.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-mUUole/YAG8a8cx2qA1HtIEL+NqYHlRz1SpR4KNM+6KBcpSW5W6D6mZ+xCffqTodCxUzV6CXYHv5zH09AjrZnQ==", "shasum": "7080325d8263dddb9290a11de39b0b5c18c9f39a", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.3.0-2.tgz", "fileCount": 228, "unpackedSize": 658418, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCw+vtbH3hbOxjQlX74MoH0cT6ee0CK2rYyxqmc7iZLbgIhAJB18oUgMFJnBlVNkbI23d54OxPksxDC6FbeuZSn0qtm"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkORZNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpB7Q/7B+XPO0Z5Mw/A1ixZFvBCliiDIuPPZ+yOlb30wabUo1sCxz0F\r\nXRiwdOKJ1LzIb4IirstqLRNJ2JBq/BXitIJ/GtUUOc5sT8AVZpopW9wWMpCc\r\nCBtJinD4uNRJSkSCwFSEOntapL0x+i+THT0ui/ifihk+7yRTae8QpzrWucEO\r\n4wO8ona+F+4bfjbt/fUuXqzNkR7HUCxfz9DQ4Rv/Ak9fCWeh+FBIsGZzAGj5\r\nZ74XbqrX09YOiRr6CwOZszUYeD3TUFVLehcQfNKSAgreeVjQVBJbqtWIZuWs\r\nm3oSNpugSPdDIOHhzuswV+OdGPyPsqH+v2wpWtI6ECukHANxsOU5mHzO2YbL\r\nrRyU4Ekxxe5ZAUEoSv9f3sEVqTDJ9Vs2Dov9Ks7u30GImmwSnvFa0Up/ebnm\r\nnIZDE88O5nJAFSZSo7cJ1BGVo3+wKmiyvuPuO8unJ8RrHnMef9LctiCrXZcY\r\neGcWI6uhj7UEkTTAKNWDZMTynosR8FcKDZ6gcVqm5E26KAUWsmahDvfoSV7W\r\nygDf5aWjxybNCSnifxJmAoEOM2hfUTlTMw315MtUVlReipkSXP6hyCHg4pbJ\r\nTn3r2FEt5PUZbvP2PxJAgNw3889/73huGPf3kgTnlr/U+2NOlz0eEl0vaBFp\r\n5XuWBAa6TNLy+hpOU3gKUKlQWwjwwQDgs+s=\r\n=oCMZ\r\n-----END PGP SIGNATURE-----\r\n", "size": 107725}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.3.0-2_1681462860790_0.05139397830136705"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-14T09:01:00.965Z", "publish_time": 1681462860965}, "2.3.0-3": {"name": "yaml", "version": "2.3.0-3", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"types": "./dist/util.d.ts", "node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.mjs", "build:node": "rollup -c config/rollup.node-config.mjs", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^11.0.0", "@types/jest": "^29.2.4", "@types/node": "^14.18.35", "@typescript-eslint/eslint-plugin": "^5.3.1", "@typescript-eslint/parser": "^5.3.1", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^8.1.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^2.2.1", "rollup": "^3.7.5", "tslib": "^2.1.0", "typescript": "^5.0.3"}, "engines": {"node": ">= 14", "npm": ">= 7"}, "types": "./dist/index.d.ts", "gitHead": "55e99ab0342b754c509ce6988389c681032f74f5", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.3.0-3", "_nodeVersion": "19.8.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-r9pu/CNeaefF0QtpSUlfmhdrLKQS4mMKwXleeVcaH5tAoPGMqX2qrNPtiyRzS8jJZTzmul5/FbRAb9wnh0Ux8g==", "shasum": "b68a2045e4192a63be99d1abdfd515cb6dfc228f", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.3.0-3.tgz", "fileCount": 228, "unpackedSize": 658337, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEqY/OTnEC7Ccdjrc7+xJ+ZgWHJ4IsaPGCJ7vlxTOHxUAiEAzlocE39tA/awuwU/URBWUj2dUuFlxktDByC/Xr1cYms="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkORlZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoH/hAAoUrIWhPC/g4rYhxGE2YBtFlkLzQwpjBxyEeFelXe8EODOxeC\r\nMxVxzGy+jxCoZtLTm6MsWFxCi0u8DBc7/y9bKDAqpSfog9FmMb7aYomg0y0g\r\nPEoWnvPj8UZPWSO47OXP2kM7byz6TdtfrKPu93Ec52OchFMA4ro87zGQiXce\r\nKxhTcTn20L+/EoiRV1Dr5hYadDViVSr20hjGUSJ1lxtHVihb5/yd8q3/5Dzf\r\ne9a4lM7xo95FeTF2J7bDxldbla6NYRtX1HUxbY7BfCOUfKuPFnGnvLaoeDWg\r\nFU+XJQ5M0nA6IQ3Mq8mVXJFBH6nfVoS0Tng0TAWf7H8LQ8/C/7rFjIZdO2uP\r\nijdO0W01xP7O2D9iHbOI6iwHbPDdOopn1j3MB23rO4BsWNIfwrBIoCbr1r/L\r\n1CEvAULDYYIjSdota+Sn2X/Pc3+vPMJrVg8ETAVu+aiELL8bAp3DhBeSKNKa\r\n8+JTbywbbxiIqInUY+ZkHOx46q/+akcwf+nDn5wgkQnlubzNxgzRcXYe5SWJ\r\nwtinzboltS9UaS8f0NIHY9HnO/x5Ammc/tweKkvAyaUCKlr4OfENZfa01jLb\r\nRlPxH5/ltE3ySyIw+GKGfXWTM9WieWcQLELGVdNPjBq6DpOv1g9BK6C4a6+t\r\nhdg5yH3pT04tv0kXPL79CSnBRxD0MUqXAO0=\r\n=D6b8\r\n-----END PGP SIGNATURE-----\r\n", "size": 107704}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.3.0-3_1681463641559_0.788746694608363"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-14T09:14:01.768Z", "publish_time": 1681463641768}, "2.3.0-4": {"name": "yaml", "version": "2.3.0-4", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"types": "./dist/util.d.ts", "node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.mjs", "build:node": "rollup -c config/rollup.node-config.mjs", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^11.0.0", "@types/jest": "^29.2.4", "@types/node": "^14.18.35", "@typescript-eslint/eslint-plugin": "^5.3.1", "@typescript-eslint/parser": "^5.3.1", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^8.1.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^2.2.1", "rollup": "^3.7.5", "tslib": "^2.1.0", "typescript": "^5.0.3"}, "engines": {"node": ">= 14", "npm": ">= 7"}, "types": "./dist/index.d.ts", "readmeFilename": "README.md", "gitHead": "ffdd2fbf0df059ea3af5929533d316783f339fa2", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.3.0-4", "_nodeVersion": "19.8.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-U7vZk3D8egK0Mu/L26P5hoRPBkj/dhJjvqW/niw1xqPkNAVwOleiOtbgr0HuFini+1AxM1w5xT7dwCLI7atx0g==", "shasum": "861e97ceefb3b98dcc455f20565b91bcdaf5bcec", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.3.0-4.tgz", "fileCount": 228, "unpackedSize": 658520, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDUiZJ2fF9roMtSxNXvGSi0yTls3FE8B/aXcojRQkn6DwIgGrtFbAUd/FZwAHf9TQr8CRYG+drHkvIgxGgQk75XlFc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPmEkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqc4A/9FU5MiSdSSuXBAxsrok8JyQrVd8MaTUBZK8uk2b3qDMR3yAJ0\r\n6s8tKqIiXp+JMWclUOikXGSqQq3uTwKxR04xEDBmrEwOMZjCMktXBZqxBdoj\r\n0JXgV0UtovRXyThQUZyLGzwTQ/15RmjA3jg4m9101Qx7UPGQ767NECJyMqpr\r\n/8b/ElB/wQpK+pnFqrbHeMrhN++hAOF1PcqstV2/OQjjgQ72WVX5exgNiPwt\r\n+xEYuHtoT0F2h6edhXdAlkk0P7i85A2tvajg2pwSjNoCbZEfPnd9qMLj+9z9\r\nTn7CeAI7Io1eajxb6n6ZkRLks5IJrkqEsX4apfnyWxvpxTVlPszlCGsWxfBy\r\nCdfHawIEMkLlCKU31a55dEps8PTsHYAhMcUoyeyNByJaVjgJiEUp5rGg7v1v\r\ns7uHY9sB6rHxVQh1K2qUEr54tc3Lpw2jy4pVin3Rf/dr6uW5+VRGBxtgTwEn\r\naFdOJDt5Psct+H+b6z90axhgsn5JCGa+9Un9EtpEt4VjxZhGXONT+bN4M61U\r\nuRV0QH9E4LSzLvO866u/Gajf3d6HSZhpBMMsZNiz4CI3nwuDWI2iNIFPAr9C\r\nfW0OpS2c0hRhcsteq3FvW4HaVXQEeh4RU2vbrNGvh3BBLRZKgsWcUX7Vj0mx\r\n2lfqXkgGwzN6RU/yJvD/LFrpiUz2AvwdBaY=\r\n=dYSt\r\n-----END PGP SIGNATURE-----\r\n", "size": 107722}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.3.0-4_1681809700499_0.140187981559867"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-18T09:21:40.633Z", "publish_time": 1681809700633}, "2.2.2": {"name": "yaml", "version": "2.2.2", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.mjs", "build:node": "rollup -c config/rollup.node-config.mjs", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^10.0.1", "@types/jest": "^29.2.4", "@types/node": "^14.18.35", "@typescript-eslint/eslint-plugin": "^5.3.1", "@typescript-eslint/parser": "^5.3.1", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^8.1.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^2.2.1", "rollup": "^3.7.5", "tslib": "^2.1.0", "typescript": "^4.3.5"}, "engines": {"node": ">= 14"}, "types": "./dist/index.d.ts", "gitHead": "f21fa455b8bb08aa3b20f07968aa923544635c2d", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.2.2", "_nodeVersion": "19.8.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-CBKFWExMn46Foo4cldiChEzn7S7SRV+wqiluAb6xmueD/fGyRHIhX8m14vVGgeFWjN540nKCNVj6P21eQjgTuA==", "shasum": "ec551ef37326e6d42872dad1970300f8eb83a073", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.2.2.tgz", "fileCount": 226, "unpackedSize": 651576, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCmyeKnlHVmJIPZEAinPP56ro+yAhe1/nZK5YWGFZllIQIgNjsnnKxfV9x6yHv2P4NBETJf/u0qLxtIMu8Ljjtvtkk="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkRn3WACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8PQ//V/Z7YlEMtjGLPA5yMPIxiImM7bV/yUrzvqb/DCSXiouBJrf1\r\nS67gD937Z9Op3f9RVa9zVOucMTv09YoohS0WxFeuK1VQDr7OVdVHCUdxz7w+\r\nJClJ4/QM2pimYWVazcPhd6xyV0k2iBPa33JpCKF96j6KLhcfvWkTxfnIUEjV\r\nZpbITgIOZ1M0a3OW7CGAuLozzfZMMEEtcboxpNOSwvLadQtPUpNO+f2LR8FZ\r\nxNc9mi9qf+wlszebG7T0QxqRroOJdINBcuewfUVwZ7Vhf5fdW3B3cO6/mjqt\r\npPNbAjWpCkimtLQ9W6sJRX2hLlsS8vBRS2sbS3HVrA8iXd4Le7o7Kw1SfS5u\r\nHGkQKe9mPq0neHKrgAzExTNtkWEDRXBIFQE7fEveTPHgWjATLzmo3Buxhp0b\r\nFqpoepFLz9MffwPc2iNcuIXn2NA+9FwgZg1hXNH5l843KwXXuDViJmqR3Ffb\r\njDvHon4NsEcjb4mCppp7PiB91QsdXR3p4zHySnXQTdxDF0hqjJsy3onYCCxh\r\nG4FbSfGHWPY67Jmo4Np7Ha1LBnr+jCreYPsESe7tImS280ruJUa95ijl/ngL\r\nGz8QAM+d3xBiMW+IR3YJBFmrrOtiKZvoCIUVRMpLtwg+hx+KN8qSesqhYuJ2\r\nqcUGEsZSnhW8Jrn9C0chRSQa6Sq7o92Xfto=\r\n=0DzZ\r\n-----END PGP SIGNATURE-----\r\n", "size": 106723}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.2.2_1682341333858_0.8924048866185064"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-24T13:02:14.045Z", "publish_time": 1682341334045}, "2.3.0-5": {"name": "yaml", "version": "2.3.0-5", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"types": "./dist/util.d.ts", "node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.mjs", "build:node": "rollup -c config/rollup.node-config.mjs", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^11.0.0", "@types/jest": "^29.2.4", "@types/node": "^14.18.35", "@typescript-eslint/eslint-plugin": "^5.3.1", "@typescript-eslint/parser": "^5.3.1", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^8.1.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^2.2.1", "rollup": "^3.7.5", "tslib": "^2.1.0", "typescript": "^5.0.3"}, "engines": {"node": ">= 14", "npm": ">= 7"}, "types": "./dist/index.d.ts", "readmeFilename": "README.md", "gitHead": "96daea578b9432a8293c1f1e52f461e1ca90f453", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.3.0-5", "_nodeVersion": "19.8.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-Y2NEcp/A4OjsMxDenJrPqucy/ufkbfQu7RRjKYWLfyATH+oGJKkPWp/4mnHPCnBpIUH29P/F8uemt/g0xB3VQg==", "shasum": "52a1ff3ddf29567f27029045eeaa55edc07585b5", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.3.0-5.tgz", "fileCount": 228, "unpackedSize": 663241, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEMCHzCH1RQho4pnU6V4OdabAeeJYLbqNssmd6PNWyxKDNICIEIqP6PmN5i2M4YszJLwisOXlgvTeRQE5Qn8uLgTQDhi"}], "size": 108481}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.3.0-5_1683394737908_0.9256431153315277"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-06T17:38:58.086Z", "publish_time": 1683394738086, "_source_registry_name": "default"}, "2.3.0": {"name": "yaml", "version": "2.3.0", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"types": "./dist/util.d.ts", "node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.mjs", "build:node": "rollup -c config/rollup.node-config.mjs", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^11.0.0", "@types/jest": "^29.2.4", "@types/node": "^14.18.35", "@typescript-eslint/eslint-plugin": "^5.3.1", "@typescript-eslint/parser": "^5.3.1", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^8.1.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^2.2.1", "rollup": "^3.7.5", "tslib": "^2.1.0", "typescript": "^5.0.3"}, "engines": {"node": ">= 14", "npm": ">= 7"}, "types": "./dist/index.d.ts", "gitHead": "431ce2231600485505673b76fc1709fb66213b97", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.3.0", "_nodeVersion": "19.8.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-8/1wgzdKc7bc9E6my5wZjmdavHLvO/QOmLG1FBugblEvY4IXrLjlViIOmL24HthU042lWTDRO90Fz1Yp66UnMw==", "shasum": "47ebe58ee718f772ce65862beb1db816210589a0", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.3.0.tgz", "fileCount": 228, "unpackedSize": 660486, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCjYjYOGoX33lnrObHBad8a/cOsxTm7VzizqcYqMUc3ewIhAO2JolCyZUOI9QYO4z0IAgYt4adJgEMuBKhycZKfCLrb"}], "size": 107621}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.3.0_1684840582064_0.16773385937846919"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-23T11:16:22.320Z", "publish_time": 1684840582320, "_source_registry_name": "default"}, "2.3.1": {"name": "yaml", "version": "2.3.1", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"types": "./dist/util.d.ts", "node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.mjs", "build:node": "rollup -c config/rollup.node-config.mjs", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^11.0.0", "@types/jest": "^29.2.4", "@types/node": "^14.18.35", "@typescript-eslint/eslint-plugin": "^5.3.1", "@typescript-eslint/parser": "^5.3.1", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^8.1.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^2.2.1", "rollup": "^3.7.5", "tslib": "^2.1.0", "typescript": "^5.0.3"}, "engines": {"node": ">= 14"}, "types": "./dist/index.d.ts", "gitHead": "a442e95ad6bb05615755aa2015c790e3600b3ab5", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.3.1", "_nodeVersion": "19.8.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-2eHWfjaoXgTBC2jNM1LRef62VQa0umtvRiDSk6HSzW7RvS5YtkabJrwYLLEKWBc8a5U2PTSCs+dJjUTJdlHsWQ==", "shasum": "02fe0975d23cd441242aa7204e09fc28ac2ac33b", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.3.1.tgz", "fileCount": 228, "unpackedSize": 660467, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID1RBRZDw+1A2MSvhgnRgPVFLD33QjRpb9uVASaQnZexAiEAuw00zcyhqnEZaT1AOZhA1bkcrgqh9KpxBdiN/nFmcvw="}], "size": 107612}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.3.1_1685097588498_0.01671935683675141"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-26T10:39:48.703Z", "publish_time": 1685097588703, "_source_registry_name": "default"}, "2.3.2": {"name": "yaml", "version": "2.3.2", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"types": "./dist/util.d.ts", "node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.mjs", "build:node": "rollup -c config/rollup.node-config.mjs", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^11.0.0", "@types/jest": "^29.2.4", "@types/node": "^14.18.35", "@typescript-eslint/eslint-plugin": "^6.4.1", "@typescript-eslint/parser": "^6.4.1", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^9.0.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^3.0.2", "rollup": "^3.7.5", "tslib": "^2.1.0", "typescript": "^5.0.3"}, "engines": {"node": ">= 14"}, "types": "./dist/index.d.ts", "gitHead": "a4d8569c2139eee8a0324b6b861283f7b3b508f8", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_id": "yaml@2.3.2", "_nodeVersion": "19.8.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-N/lyzTPaJasoDmfV7YTrYCI0G/3ivm/9wdG0aHuheKowWQwGTsK0Eoiw6utmzAnI6pkJa0DUVygvp3spqqEKXg==", "shasum": "f522db4313c671a0ca963a75670f1c12ea909144", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.3.2.tgz", "fileCount": 228, "unpackedSize": 660819, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA/P4MOa/A5oMydeBbcS3GnbISXQzNOZaTAOoYkwliJLAiA82NkRxRfm9As7J7NrS2OLXM7ro3fGhre+w0gcO5a1dQ=="}], "size": 107719}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.3.2_1693234854612_0.6336227149566043"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-28T15:00:54.836Z", "publish_time": 1693234854836, "_source_registry_name": "default"}, "2.3.3": {"name": "yaml", "version": "2.3.3", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"types": "./dist/util.d.ts", "node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.mjs", "build:node": "rollup -c config/rollup.node-config.mjs", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^11.0.0", "@types/jest": "^29.2.4", "@types/node": "^14.18.35", "@typescript-eslint/eslint-plugin": "^6.4.1", "@typescript-eslint/parser": "^6.4.1", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^9.0.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^3.0.2", "rollup": "^3.7.5", "tslib": "^2.1.0", "typescript": "^5.0.3"}, "engines": {"node": ">= 14"}, "_id": "yaml@2.3.3", "gitHead": "e8576e852035900844d2cc2d502a2139a1610f48", "types": "./dist/index.d.ts", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_nodeVersion": "20.8.0", "_npmVersion": "10.1.0", "dist": {"integrity": "sha512-zw0VAJxgeZ6+++/su5AFoqBbZbrEakwu+X0M5HmcwUiBL7AzcuPKjj5we4xfQLp78LkEMpD0cOnUhmgOVy3KdQ==", "shasum": "01f6d18ef036446340007db8e016810e5d64aad9", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.3.3.tgz", "fileCount": 228, "unpackedSize": 661137, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDhCMzucn5R71ZPpM+C0Ypfz2l+IFRmobPFD5CrzLEA0wIgcmg0izRu1+3/zFytsr26mbwiaHtYj2SbpE7ZSLgEK1Q="}], "size": 107742}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.3.3_1697272630757_0.9819863829113027"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-14T08:37:11.077Z", "publish_time": 1697272631077, "_source_registry_name": "default"}, "2.3.4": {"name": "yaml", "version": "2.3.4", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"types": "./dist/util.d.ts", "node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.mjs", "build:node": "rollup -c config/rollup.node-config.mjs", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^11.0.0", "@types/jest": "^29.2.4", "@types/node": "^14.18.35", "@typescript-eslint/eslint-plugin": "^6.4.1", "@typescript-eslint/parser": "^6.4.1", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^9.0.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^3.0.2", "rollup": "^3.7.5", "tslib": "^2.1.0", "typescript": "^5.0.3"}, "engines": {"node": ">= 14"}, "_id": "yaml@2.3.4", "gitHead": "b7696fc001837a2e9d66ad78d7c04f47943daeca", "types": "./dist/index.d.ts", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_nodeVersion": "20.8.0", "_npmVersion": "10.1.0", "dist": {"integrity": "sha512-8aAvwVUSHpfEqTQ4w/KMlf3HcRdt50E5ODIQJBw1fQ5RL34xabzxtUlzTXVqc4rkZsPbvrXKWnABCD7kWSmocA==", "shasum": "53fc1d514be80aabf386dc6001eb29bf3b7523b2", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.3.4.tgz", "fileCount": 228, "unpackedSize": 661139, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDAFMiv7303+Euj4jEgSviIzRNsnat2WCfHosR0y1qv3QIgHgYP6PuIPClAbl+OKL/nr6SpgPOUme2eW82reDeqyV8="}], "size": 107745}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.3.4_1698975688625_0.23699841080775874"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-03T01:41:28.842Z", "publish_time": 1698975688842, "_source_registry_name": "default"}, "2.4.0": {"name": "yaml", "version": "2.4.0", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "bin": {"yaml": "bin.mjs"}, "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"types": "./dist/util.d.ts", "node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.mjs", "build:node": "rollup -c config/rollup.node-config.mjs", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:install": "cd docs-slate && bundle install", "predocs:deploy": "node docs/prepare-docs.mjs", "docs:deploy": "cd docs-slate && ./deploy.sh", "predocs": "node docs/prepare-docs.mjs", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-transform-class-properties": "^7.23.3", "@babel/plugin-transform-nullish-coalescing-operator": "^7.23.4", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^11.0.0", "@types/jest": "^29.2.4", "@types/node": "^20.11.20", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^9.0.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^3.0.2", "rollup": "^4.12.0", "tslib": "^2.1.0", "typescript": "^5.0.3"}, "engines": {"node": ">= 14"}, "_id": "yaml@2.4.0", "gitHead": "8d8cfb70a19148ba16958a9d83ab14a73a809ce4", "types": "./dist/index.d.ts", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_nodeVersion": "20.8.0", "_npmVersion": "10.1.0", "dist": {"integrity": "sha512-j9iR8g+/t0lArF4V6NE/QCfT+CO7iLqrXAHZbJdo+LfjqP1vR8Fg5bSiaq6Q2lOD1AUEVrEVIgABvBFYojJVYQ==", "shasum": "2376db1083d157f4b3a452995803dbcf43b08140", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.4.0.tgz", "fileCount": 231, "unpackedSize": 670405, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEHxnQ+3p4XnBULBayIg+fBMJ3Q8kKaH7ondGNXeEZVWAiBwDqaGEi8zMi3+ULGH+TldI5ASYHMpEXwzLy5+9B1iiw=="}], "size": 110000}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.4.0_1708875240088_0.6605808463334017"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-25T15:34:00.239Z", "publish_time": 1708875240239, "_source_registry_name": "default"}, "2.4.1": {"name": "yaml", "version": "2.4.1", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "bin": {"yaml": "bin.mjs"}, "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"types": "./dist/util.d.ts", "node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.mjs", "build:node": "rollup -c config/rollup.node-config.mjs", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:install": "cd docs-slate && bundle install", "predocs:deploy": "node docs/prepare-docs.mjs", "docs:deploy": "cd docs-slate && ./deploy.sh", "predocs": "node docs/prepare-docs.mjs", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-transform-class-properties": "^7.23.3", "@babel/plugin-transform-nullish-coalescing-operator": "^7.23.4", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^11.0.0", "@types/jest": "^29.2.4", "@types/node": "^20.11.20", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^9.0.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^3.0.2", "rollup": "^4.12.0", "tslib": "^2.1.0", "typescript": "^5.0.3"}, "engines": {"node": ">= 14"}, "_id": "yaml@2.4.1", "gitHead": "4aa56d337dc5e286eb0c9111a3b370f21e321117", "types": "./dist/index.d.ts", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_nodeVersion": "21.6.2", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-pIXzoImaqmfOrL7teGUBt/T7ZDnyeGBWyXQBvOVhLkWLN37GXv8NMLK406UY6dS51JfcQHsmcW5cJ441bHg6Lg==", "shasum": "2e57e0b5e995292c25c75d2658f0664765210eed", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.4.1.tgz", "fileCount": 231, "unpackedSize": 670702, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDOZY2LzakuefNlEBOU9+ilX+5v5WB+3kwoYp4CTmfAVwIhAKeQ+KgcpL1ftmz/QiHNgcUH7JUmU4K/r5xOONM7LuhI"}], "size": 110065}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.4.1_1709725952680_0.22710035557221508"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-06T11:52:32.864Z", "publish_time": 1709725952864, "_source_registry_name": "default"}, "2.4.2": {"name": "yaml", "version": "2.4.2", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "bin": {"yaml": "bin.mjs"}, "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"types": "./dist/util.d.ts", "node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.mjs", "build:node": "rollup -c config/rollup.node-config.mjs", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:install": "cd docs-slate && bundle install", "predocs:deploy": "node docs/prepare-docs.mjs", "docs:deploy": "cd docs-slate && ./deploy.sh", "predocs": "node docs/prepare-docs.mjs", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-transform-class-properties": "^7.23.3", "@babel/plugin-transform-nullish-coalescing-operator": "^7.23.4", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^11.0.0", "@types/jest": "^29.2.4", "@types/node": "^20.11.20", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^9.0.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^3.0.2", "rollup": "^4.12.0", "tslib": "^2.1.0", "typescript": "^5.0.3"}, "engines": {"node": ">= 14"}, "_id": "yaml@2.4.2", "gitHead": "f792d1b72fb98792a66ca2dcf5b606448e762b16", "types": "./dist/index.d.ts", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_nodeVersion": "21.6.2", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-B3VqDZ+JAg1nZpaEmWtTXUlBneoGx6CPM9b0TENK6aoSu5t73dItudwdgmi6tHlIZZId4dZ9skcAQ2UbcyAeVA==", "shasum": "7a2b30f2243a5fc299e1f14ca58d475ed4bc5362", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.4.2.tgz", "fileCount": 231, "unpackedSize": 670948, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEgqP0O+YwvrUxkoENdpDULlR+X/DoevXXjY541fABlWAiBwvfLsfzxITlGAMBtHRYaj46pJp+7XMxb6+DiRRpl4BA=="}], "size": 110126}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.4.2_1714296775625_0.01112779352203308"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-28T09:32:55.892Z", "publish_time": 1714296775892, "_source_registry_name": "default"}, "2.4.3": {"name": "yaml", "version": "2.4.3", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "bin": {"yaml": "bin.mjs"}, "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"types": "./dist/util.d.ts", "node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.mjs", "build:node": "rollup -c config/rollup.node-config.mjs", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "npm run build:node", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:install": "cd docs-slate && bundle install", "predocs:deploy": "node docs/prepare-docs.mjs", "docs:deploy": "cd docs-slate && ./deploy.sh", "predocs": "node docs/prepare-docs.mjs", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-transform-class-properties": "^7.23.3", "@babel/plugin-transform-nullish-coalescing-operator": "^7.23.4", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^11.0.0", "@types/jest": "^29.2.4", "@types/node": "^20.11.20", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^9.0.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^3.0.2", "rollup": "^4.12.0", "tslib": "^2.1.0", "typescript": "^5.0.3"}, "engines": {"node": ">= 14"}, "_id": "yaml@2.4.3", "gitHead": "208d98f0a42aad737bd019bb59d85679e2d9282a", "types": "./dist/index.d.ts", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_nodeVersion": "21.6.2", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-sntgmxj8o7DE7g/Qi60cqpLBA3HG3STcDA0kO+WfB05jEKhZMbY7umNm2rBpQvsmZ16/lPXCJGW2672dgOUkrg==", "shasum": "0777516b8c7880bcaa0f426a5410e8d6b0be1f3d", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.4.3.tgz", "fileCount": 231, "unpackedSize": 671567, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDnMN0H/VTDC8S4KrgCVxI30EhCYy5EYT/b10RYbfnyGQIgMFnMR+i+KA7pjTiVaOPrfofCrkKy+X+qsZILE9KLz4k="}], "size": 110197}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.4.3_1717319327452_0.601135222389299"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-02T09:08:47.664Z", "publish_time": 1717319327664, "_source_registry_name": "default"}, "2.4.4": {"name": "yaml", "version": "2.4.4", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "bin": {"yaml": "bin.mjs"}, "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"types": "./dist/util.d.ts", "node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.mjs", "build:node": "rollup -c config/rollup.node-config.mjs", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "rollup --sourcemap -c config/rollup.node-config.mjs", "start": "node --enable-source-maps -i -e 'YAML=require(\"./dist/index.js\");const{parse,parseDocument,parseAllDocuments}=YAML'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:install": "cd docs-slate && bundle install", "predocs:deploy": "node docs/prepare-docs.mjs", "docs:deploy": "cd docs-slate && ./deploy.sh", "predocs": "node docs/prepare-docs.mjs", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-transform-class-properties": "^7.23.3", "@babel/plugin-transform-nullish-coalescing-operator": "^7.23.4", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^11.0.0", "@types/jest": "^29.2.4", "@types/node": "^20.11.20", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^9.0.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^3.0.2", "rollup": "^4.12.0", "tslib": "^2.1.0", "typescript": "^5.0.3"}, "engines": {"node": ">= 14"}, "_id": "yaml@2.4.4", "gitHead": "d06f3867ae9af453ad115f73b83bad0095b65125", "types": "./dist/index.d.ts", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_nodeVersion": "21.6.2", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-wle6DEiBMLgJAdEPZ+E8BPFauoWbwPujfuGJJFErxYiU4txXItppe8YqeFPAaWnW5CxduQ995X6b5e1NqrmxtA==", "shasum": "e463681ec48fe9567f1ce35cf1e3a25e14b7b7e7", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.4.4.tgz", "fileCount": 231, "unpackedSize": 672373, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICyaGpPlkXf3th0V6WCMvXz0z1lOFu+K64Bfelw72IP7AiEAhly2ArS66aFkwJim1btCLmYcF/hx5KhjpFJEhgbfOyE="}], "size": 110378}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.4.4_1717863779540_0.16336070983871886"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-08T16:22:59.714Z", "publish_time": 1717863779714, "_source_registry_name": "default"}, "2.4.5": {"name": "yaml", "version": "2.4.5", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "bin": {"yaml": "bin.mjs"}, "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"types": "./dist/util.d.ts", "node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.mjs", "build:node": "rollup -c config/rollup.node-config.mjs", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "rollup --sourcemap -c config/rollup.node-config.mjs", "start": "node --enable-source-maps -i -e 'YAML=require(\"./dist/index.js\");const{parse,parseDocument,parseAllDocuments}=YAML'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:install": "cd docs-slate && bundle install", "predocs:deploy": "node docs/prepare-docs.mjs", "docs:deploy": "cd docs-slate && ./deploy.sh", "predocs": "node docs/prepare-docs.mjs", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-transform-class-properties": "^7.23.3", "@babel/plugin-transform-nullish-coalescing-operator": "^7.23.4", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^11.0.0", "@types/jest": "^29.2.4", "@types/node": "^20.11.20", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^9.0.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^3.0.2", "rollup": "^4.12.0", "tslib": "^2.1.0", "typescript": "^5.0.3"}, "engines": {"node": ">= 14"}, "_id": "yaml@2.4.5", "gitHead": "1b8fde6717c096446d4b1cf9e21ef1fb87090385", "types": "./dist/index.d.ts", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_nodeVersion": "21.6.2", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-aBx2bnqDzVOyNKfsysjA2ms5ZlnjSAW2eG3/L5G/CSujfjLJTJsEw1bGw8kCf04KodQWk1pxlGnZ56CRxiawmg==", "shasum": "60630b206dd6d84df97003d33fc1ddf6296cca5e", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.4.5.tgz", "fileCount": 231, "unpackedSize": 674680, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCn9ywj18m2oD02hbzi5figW1Hf/1phPpNVFQP7uZ4eNwIgJJ6lcUyJm35UjGlMdpYFOL50sLXE6Wdmj0i/WU7y+NY="}], "size": 110641}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.4.5_1717882946551_0.44597381615828335"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-08T21:42:26.758Z", "publish_time": 1717882946758, "_source_registry_name": "default"}, "2.5.0": {"name": "yaml", "version": "2.5.0", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "bin": {"yaml": "bin.mjs"}, "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"types": "./dist/util.d.ts", "node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.mjs", "build:node": "rollup -c config/rollup.node-config.mjs", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "prestart": "rollup --sourcemap -c config/rollup.node-config.mjs", "start": "node --enable-source-maps -i -e 'YAML=require(\"./dist/index.js\");const{parse,parseDocument,parseAllDocuments}=YAML'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:install": "cd docs-slate && bundle install", "predocs:deploy": "node docs/prepare-docs.mjs", "docs:deploy": "cd docs-slate && ./deploy.sh", "predocs": "node docs/prepare-docs.mjs", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-transform-class-properties": "^7.23.3", "@babel/plugin-transform-nullish-coalescing-operator": "^7.23.4", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^11.0.0", "@types/jest": "^29.2.4", "@types/node": "^20.11.20", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-prettier": "^9.0.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^3.0.2", "rollup": "^4.12.0", "tslib": "^2.1.0", "typescript": "^5.0.3"}, "engines": {"node": ">= 14"}, "_id": "yaml@2.5.0", "gitHead": "b309e23cf70f7502acc7bbc5eb389095ab87d871", "types": "./dist/index.d.ts", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_nodeVersion": "22.5.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-2wWLbGbYDiSqqIKoPjar3MPgB94ErzCtrNE1FdqGuaO0pi2JGjmE8aW8TDZwzU7vuxcGRdL/4gPQwQ7hD5AMSw==", "shasum": "c6165a721cf8000e91c36490a41d7be25176cf5d", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.5.0.tgz", "fileCount": 231, "unpackedSize": 675753, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCqAvW6CsS+Vh43MjjDTPRrKFaycTt4KL35tR0wYhu/8QIhAL+qXCj/3nyXHnuE2yBvFNfQfM+pUA/DKxb5sCuj9UU4"}], "size": 110837}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.5.0_1721817400355_0.4683311125545344"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-24T10:36:40.581Z", "publish_time": 1721817400581, "_source_registry_name": "default"}, "2.5.1": {"name": "yaml", "version": "2.5.1", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "bin": {"yaml": "bin.mjs"}, "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"types": "./dist/util.d.ts", "node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.mjs", "build:node": "rollup -c config/rollup.node-config.mjs", "clean": "git clean -fdxe node_modules", "lint": "eslint config/ src/", "prettier": "prettier --write .", "prestart": "rollup --sourcemap -c config/rollup.node-config.mjs", "start": "node --enable-source-maps -i -e 'YAML=require(\"./dist/index.js\");const{parse,parseDocument,parseAllDocuments}=YAML'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:install": "cd docs-slate && bundle install", "predocs:deploy": "node docs/prepare-docs.mjs", "docs:deploy": "cd docs-slate && ./deploy.sh", "predocs": "node docs/prepare-docs.mjs", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@eslint/js": "^9.9.1", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^11.0.0", "@types/jest": "^29.2.4", "@types/node": "^20.11.20", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^9.9.1", "eslint-config-prettier": "^9.0.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^3.0.2", "rollup": "^4.12.0", "tslib": "^2.1.0", "typescript": "^5.0.3", "typescript-eslint": "^8.4.0"}, "engines": {"node": ">= 14"}, "_id": "yaml@2.5.1", "gitHead": "5adbb605b64094b57bc95cbc24587e6f36b3f9a8", "types": "./dist/index.d.ts", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_nodeVersion": "22.5.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-bLQOjaX/ADgQ20isPJRvF0iRUHIxVhYvr53Of7wGcWlO2jvtUlH5m87DsmulFVxRpNLOnI4tB6p/oh8D7kpn9Q==", "shasum": "c9772aacf62cb7494a95b0c4f1fb065b563db130", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.5.1.tgz", "fileCount": 230, "unpackedSize": 674664, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDbpVxK2ipfIEeVR7CJE6YctZD57H2MiZgjJIheAoNqaAiB5LwuIg9yW8qsAIupThJDcgm13FEdHqW8eqUN0hFQ0Pg=="}], "size": 110069}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.5.1_1725405207330_0.9933069423740062"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-03T23:13:27.622Z", "publish_time": 1725405207622, "_source_registry_name": "default"}, "2.6.0": {"name": "yaml", "version": "2.6.0", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "bin": {"yaml": "bin.mjs"}, "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"types": "./dist/util.d.ts", "node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.mjs", "build:node": "rollup -c config/rollup.node-config.mjs", "clean": "git clean -fdxe node_modules", "lint": "eslint config/ src/", "prettier": "prettier --write .", "prestart": "rollup --sourcemap -c config/rollup.node-config.mjs", "start": "node --enable-source-maps -i -e 'YAML=require(\"./dist/index.js\");const{parse,parseDocument,parseAllDocuments}=YAML'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:install": "cd docs-slate && bundle install", "predocs:deploy": "node docs/prepare-docs.mjs", "docs:deploy": "cd docs-slate && ./deploy.sh", "predocs": "node docs/prepare-docs.mjs", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@eslint/js": "^9.9.1", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^11.0.0", "@types/jest": "^29.2.4", "@types/node": "^20.11.20", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^9.9.1", "eslint-config-prettier": "^9.0.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^3.0.2", "rollup": "^4.12.0", "tslib": "^2.1.0", "typescript": "^5.0.3", "typescript-eslint": "^8.4.0"}, "engines": {"node": ">= 14"}, "_id": "yaml@2.6.0", "gitHead": "808fba3cdef86b5d12085fd9c6343d47f168cbc3", "types": "./dist/index.d.ts", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_nodeVersion": "22.8.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-a6ae//JvKDEra2kdi1qzCyrJW/WZCgFi8ydDV+eXExl95t+5R+ijnqHJbz9tmMh8FUjx3iv2fCQ4dclAQlO2UQ==", "shasum": "14059ad9d0b1680d0f04d3a60fe00f3a857303c3", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.6.0.tgz", "fileCount": 233, "unpackedSize": 680893, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDRB/SQezAh4DB1Zefj91yCbsNQmGBrOfEhAPGXJq4ceAIhAIj13hq9wV7AYq4ISf0IvEe4X+3034OemTwb9U+0Er6I"}], "size": 111277}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.6.0_1728815781097_0.5789709535367382"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-13T10:36:21.312Z", "publish_time": 1728815781312, "_source_registry_name": "default"}, "2.6.1": {"name": "yaml", "version": "2.6.1", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "bin": {"yaml": "bin.mjs"}, "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"types": "./dist/util.d.ts", "node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.mjs", "build:node": "rollup -c config/rollup.node-config.mjs", "clean": "git clean -fdxe node_modules", "lint": "eslint config/ src/", "prettier": "prettier --write .", "prestart": "rollup --sourcemap -c config/rollup.node-config.mjs", "start": "node --enable-source-maps -i -e 'YAML=require(\"./dist/index.js\");const{parse,parseDocument,parseAllDocuments}=YAML'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:install": "cd docs-slate && bundle install", "predocs:deploy": "node docs/prepare-docs.mjs", "docs:deploy": "cd docs-slate && ./deploy.sh", "predocs": "node docs/prepare-docs.mjs", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@eslint/js": "^9.9.1", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^11.0.0", "@types/jest": "^29.2.4", "@types/node": "^20.11.20", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^9.9.1", "eslint-config-prettier": "^9.0.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^3.0.2", "rollup": "^4.12.0", "tslib": "^2.1.0", "typescript": "^5.0.3", "typescript-eslint": "^8.4.0"}, "engines": {"node": ">= 14"}, "_id": "yaml@2.6.1", "gitHead": "aa1898ae61605ea09bb79621d25ad5e7fd9b4217", "types": "./dist/index.d.ts", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_nodeVersion": "22.8.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-7r0XPzioN/Q9kXBro/XPnA6kznR73DHq+GXh5ON7ZozRO6aMjbmiBuKste2wslTFkC5d1dw0GooOCepZXJ2SAg==", "shasum": "42f2b1ba89203f374609572d5349fb8686500773", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.6.1.tgz", "fileCount": 233, "unpackedSize": 681592, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHNfQHuurcbUBdQHYfP/c1MmL5ozBFT7RdXHPbK+iTyrAiEAww5ls80YtBnhaOQ8VCyxZf72S83DMDYbELczqfKsZm0="}], "size": 111383}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yaml_2.6.1_1732013272350_0.41795267368019196"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-19T10:47:52.527Z", "publish_time": 1732013272527, "_source_registry_name": "default"}, "2.7.0": {"name": "yaml", "version": "2.7.0", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "bin": {"yaml": "bin.mjs"}, "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"types": "./dist/util.d.ts", "node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.mjs", "build:node": "rollup -c config/rollup.node-config.mjs", "clean": "git clean -fdxe node_modules", "lint": "eslint config/ src/", "prettier": "prettier --write .", "prestart": "rollup --sourcemap -c config/rollup.node-config.mjs", "start": "node --enable-source-maps -i -e 'YAML=require(\"./dist/index.js\");const{parse,parseDocument,parseAllDocuments}=YAML'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:install": "cd docs-slate && bundle install", "predocs:deploy": "node docs/prepare-docs.mjs", "docs:deploy": "cd docs-slate && ./deploy.sh", "predocs": "node docs/prepare-docs.mjs", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@eslint/js": "^9.9.1", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^12.1.1", "@types/jest": "^29.2.4", "@types/node": "^20.11.20", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^9.9.1", "eslint-config-prettier": "^9.0.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^3.0.2", "rollup": "^4.12.0", "tslib": "^2.8.1", "typescript": "^5.7.2", "typescript-eslint": "^8.4.0"}, "engines": {"node": ">= 14"}, "_id": "yaml@2.7.0", "gitHead": "8f512b526a52e245e770be257235f7d37059ca39", "types": "./dist/index.d.ts", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_nodeVersion": "23.4.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-+hSoy/QHluxmC9kCIJyL/uyFmLmc+e5CFR5Wa+bpIhIj85LVb9ZH2nVnqrHoSvKogwODv0ClqZkmiSSaIH5LTA==", "shasum": "aef9bb617a64c937a9a748803786ad8d3ffe1e98", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.7.0.tgz", "fileCount": 233, "unpackedSize": 681128, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDfqM1WIkKFlAQQwkvbhVRkAbJ2m0qXGAt/Si2T2/7EfAiAFasdPMDgF72eAh5HvoPHPS1J5OzzmFrrgXkUyTRNT4A=="}], "size": 111348}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/yaml_2.7.0_1735620047260_0.7976759325899112"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-31T04:40:47.460Z", "publish_time": 1735620047460, "_source_registry_name": "default"}, "2.7.1": {"name": "yaml", "version": "2.7.1", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "bin": {"yaml": "bin.mjs"}, "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"types": "./dist/util.d.ts", "node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.mjs", "build:node": "rollup -c config/rollup.node-config.mjs", "clean": "git clean -fdxe node_modules", "lint": "eslint config/ src/", "prettier": "prettier --write .", "prestart": "rollup --sourcemap -c config/rollup.node-config.mjs", "start": "node --enable-source-maps -i -e 'YAML=require(\"./dist/index.js\");const{parse,parseDocument,parseAllDocuments}=YAML'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:install": "cd docs-slate && bundle install", "predocs:deploy": "node docs/prepare-docs.mjs", "docs:deploy": "cd docs-slate && ./deploy.sh", "predocs": "node docs/prepare-docs.mjs", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@eslint/js": "^9.9.1", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^12.1.1", "@types/jest": "^29.2.4", "@types/node": "^20.11.20", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^9.9.1", "eslint-config-prettier": "^9.0.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^3.0.2", "rollup": "^4.12.0", "tslib": "^2.8.1", "typescript": "^5.7.2", "typescript-eslint": "^8.4.0"}, "engines": {"node": ">= 14"}, "_id": "yaml@2.7.1", "gitHead": "a141bc0f00b194e9ac5146f52e74c0e8236784a6", "types": "./dist/index.d.ts", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_nodeVersion": "23.7.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-10ULxpnOCQXxJvBgxsn9ptjq6uviG/htZKk9veJGhlqn3w/DxQ631zFF+nlQXLwmImeS5amR2dl2U8sg6U9jsQ==", "shasum": "44a247d1b88523855679ac7fa7cda6ed7e135cf6", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.7.1.tgz", "fileCount": 233, "unpackedSize": 682531, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDSSXWeLbF6ibJFe8oktVSypTWe6mZUHlVkzIc9OqeoQAIhAPD8KURvePIfYsDFnla1zHvcogpEdATdP/uwE/QWG5Gt"}], "size": 111529}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/yaml_2.7.1_1743260401194_0.2802605330203325"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-29T15:00:01.431Z", "publish_time": 1743260401431, "_source_registry_name": "default"}, "2.8.0": {"name": "yaml", "version": "2.8.0", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "bin": {"yaml": "bin.mjs"}, "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"types": "./dist/util.d.ts", "node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.mjs", "build:node": "rollup -c config/rollup.node-config.mjs", "clean": "git clean -fdxe node_modules", "lint": "eslint config/ src/", "prettier": "prettier --write .", "prestart": "rollup --sourcemap -c config/rollup.node-config.mjs", "start": "node --enable-source-maps -i -e 'YAML=require(\"./dist/index.js\");const{parse,parseDocument,parseAllDocuments}=YAML'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:install": "cd docs-slate && bundle install", "predocs:deploy": "node docs/prepare-docs.mjs", "docs:deploy": "cd docs-slate && ./deploy.sh", "predocs": "node docs/prepare-docs.mjs", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@eslint/js": "^9.9.1", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^12.1.1", "@types/jest": "^29.2.4", "@types/node": "^20.11.20", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^9.9.1", "eslint-config-prettier": "^9.0.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^3.0.2", "rollup": "^4.12.0", "tslib": "^2.8.1", "typescript": "^5.7.2", "typescript-eslint": "^8.4.0"}, "engines": {"node": ">= 14.6"}, "_id": "yaml@2.8.0", "gitHead": "c000eb708fc04910a0b034572c6febb090ca7035", "types": "./dist/index.d.ts", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_nodeVersion": "23.11.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-4lLa/EcQCB0cJkyts+FpIRx5G/llPxfP6VQU5KByHEhLxY3IJCH0f0Hy1MHI8sClTvsIb8qwRJ6R/ZdlDJ/leQ==", "shasum": "15f8c9866211bdc2d3781a0890e44d4fa1a5fff6", "tarball": "https://registry.npmmirror.com/yaml/-/yaml-2.8.0.tgz", "fileCount": 233, "unpackedSize": 683419, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDhFTgJ6Px1HOUpnBEteaoTACUC5bkY6gTUfNcecHwYbAIhAOWfEGjbAv5+i8QVfYDa41iDjfZUfehqzYBzY++eFIT5"}], "size": 111694}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/yaml_2.8.0_1747295895612_0.7956807244103377"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-15T07:58:15.800Z", "publish_time": 1747295895800, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "homepage": "https://eemeli.org/yaml/", "keywords": ["YAML", "parser", "stringifier"], "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "_source_registry_name": "default"}