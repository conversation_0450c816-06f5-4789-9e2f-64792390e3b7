// src/stores/useCreateWorkStore.js
import { defineStore } from 'pinia'
import { reactive } from 'vue'

export const useCreateWorkStore = defineStore('createWork', () => {
    const formStepOne = reactive({
        title: '',
        description: '',
        isPublic: false,
        deadline: null,
        examStart: null,
        examEnd: null,
        examDuration: 90,
        publishTime: null,
    })

    function saveStepOne(data) {
        Object.assign(formStepOne, data)
    }

    function clearStepOne() {
        formStepOne.title = ''
        formStepOne.description = ''
        formStepOne.isPublic = false
        formStepOne.deadline = null
        formStepOne.examStart = null
        formStepOne.examEnd = null
        formStepOne.examDuration = 90
        formStepOne.publishTime = null
    }

    return {
        formStepOne,
        saveStepOne,
        clearStepOne,
    }
})
