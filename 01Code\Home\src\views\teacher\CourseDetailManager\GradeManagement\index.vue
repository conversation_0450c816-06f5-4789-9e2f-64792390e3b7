<template>
    <div class="score-management">
        <div class="button-tabs">
            <div class="tab-buttons">
                <button v-for="tab in tabs" :key="tab.name" :class="{ 'active': activeTab === tab.name }"
                    @click="activeTab = tab.name">
                    {{ tab.label }}
                </button>
            </div>

            <div class="divider"></div>
            <div class="content">
                <!-- 考勤成绩 -->
                <ScrollableTableContainer 
                    v-if="activeTab === 'attendance'" 
                    title="考勤成绩" :columns="attendanceColumns"
                    :data="attendanceData" 
                    :classes="classList" 
                    :loading="loadingAttendance"
                    @export-data="exportData"
                    @class-change="handleClassChange" />

                <!-- 平时成绩 -->
                <ScrollableTableContainer 
                    v-if="activeTab === 'daily'" 
                    title="平时成绩" 
                    :columns="dailyScoreColumns"
                    :data="dailyScoreData" 
                    :classes="classList" 
                    :loading="loadingDailyScore"
                    @export-data="exportData"
                    @class-change="handleClassChange" />

                <!-- 作业测试成绩 -->
                <ScrollableTableContainer 
                    v-if="activeTab === 'homework'" 
                    title="作业测试成绩" 
                    :columns="homeworkColumns"
                    :data="homeworkData" 
                    :classes="classList" 
                    :loading="loadingHomework"
                    @export-data="exportData"
                    @class-change="handleClassChange" />
                <!-- 考试成绩 -->
                <ScrollableTableContainer 
                    v-if="activeTab === 'exam'" 
                    title="考试成绩" 
                    :columns="examColumns"
                    :data="examData" 
                    :classes="classList"  
                    :loading="loadingExam"
                    @export-data="exportData"
                    @class-change="handleClassChange" />

                <div v-if="activeTab === 'total'">
                    <TotalScore />
                </div>
            </div>
        </div>
    </div>

    <ExportSignIn
      v-if="showExportDialog"
      :classId="selectedClassId"
      :courseId="courseId"
      @close="showExportDialog = false"
    />
</template>


<script setup>
import { ref, onMounted } from 'vue'
import TotalScore from './components/TotalScore.vue'
import ScrollableTableContainer from './components/ScrollableTableContainer .vue'
import { fetchClassList } from '@/api/teacher/class'
import { getClassAttendanceRate } from '@/api/teacher/attendance'
import { fetchRegularGrades,fetchExamGrades,fetchHomeworkGrades} from '@/api/teacher/grade'
import { useRoute } from 'vue-router';
import ExportSignIn from './components/ExportSignIn.vue' // 调整路径

const route = useRoute();
const courseId = route.params.courseId;
const showExportDialog = ref(false)
const dailyScoreData = ref([])
const loadingDailyScore = ref(false)
const loadingExam = ref(false)
const examData = ref([])
const loadingHomework = ref(false)
const homeworkData = ref([])

const exportData = () => {
  showExportDialog.value = true
}



// 班级数据
const classes = ref([
    { id: 1, name: '数字媒体技术111班(1)' },
    { id: 2, name: '数字媒体技术111班(2)' },
    { id: 3, name: '数字媒体技术112班(1)' }
])

// 标签页配置
const tabs = [
    { label: '考勤成绩', name: 'attendance' },
    { label: '平时成绩', name: 'daily' },
    { label: '作业测试成绩', name: 'homework' },
    { label: '考试成绩', name: 'exam' },
    { label: '总成绩', name: 'total' }
]

const activeTab = ref('attendance')
const loadingAttendance = ref(false)
const classList = ref([])
const attendanceData = ref([])
const selectedClassId = ref('') 
const attendanceColumns = ref([
    { key: 'studentId', title: '学号', sortable: true },
    { key: 'studentName', title: '姓名', sortable: true }
    // 动态列会在获取数据后添加
])

// 获取班级列表
const fetchClasses = async () => {
    try {
        const res = await fetchClassList({ courseId: courseId })
        classList.value = res.result.map(item => ({
            id: item.id,
            name: item.name
        }))

        if (classList.value.length > 0) {
            fetchAttendanceData(classList.value[0].id)
        }
    } catch (error) {
        console.error('获取班级列表失败:', error)
    }
}

// 获取考勤数据
const fetchAttendanceData = async (classId) => {
    loadingAttendance.value = true
    try {
        const res = await getClassAttendanceRate(classId)
        const students = res.result
        if (students.length > 0 && students[0].attendanceDetails) {
            // 添加动态列
            const dynamicColumns = students[0].attendanceDetails.map(detail => ({
                key: `attendance_${detail.attendanceId}`,
                title: detail.attendanceName || '未命名签到',
                sortable: true,
                formatter: (value) => value ? value.checkStatusName : '未签到'
            }))

            // 添加统计列
            const statColumns = [
                {
                    key: 'checkedCount',
                    title: '签到次数',
                    sortable: true
                },
                {
                    key: 'totalCount',
                    title: '总签到次数',
                    sortable: true
                },
                {
                    key: 'attendanceRate',
                    title: '出勤率',
                    sortable: true,
                    formatter: (value) => `${value}%`,
                    class: (value) => {
                        if (value >= 90) return 'high-rate'
                        if (value >= 70) return 'medium-rate'
                        return 'low-rate'
                    }
                }
            ]

            // 更新列配置
            attendanceColumns.value = [
                ...attendanceColumns.value.slice(0, 2), // 保留学号和姓名
                ...dynamicColumns,
                ...statColumns
            ]

            // 格式化学生数据
            attendanceData.value = students.map(student => {
                const studentData = {
                    studentId: student.studentId,
                    studentName: student.studentName || '未知',
                    checkedCount: student.checkedCount,
                    totalCount: student.totalCount,
                    attendanceRate: student.attendanceRate
                }

                // 添加每次签到状态
                student.attendanceDetails.forEach(detail => {
                    studentData[`attendance_${detail.attendanceId}`] = {
                        checkStatusName: detail.checkStatusName,
                        hasChecked: detail.hasChecked
                    }
                })

                return studentData
            })
        } else {
            attendanceData.value = students
        }
    } catch (error) {
        console.error('获取考勤数据失败:', error)
    } finally {
        loadingAttendance.value = false
    }
}



const fetchRegularGradesData = async (classId) => {
    loadingDailyScore.value = true
    try {
        const res = await fetchRegularGrades(classId)
        dailyScoreData.value = res.result.map(student => ({
            studentId: student.studentId,
            studentName: student.studentName || '未知',
            learningProgressScore: student.learningProgressScore,
            interactionCount: student.interactionCount,
            interactionScore: student.interactionScore,
            regularFinalScore: student.regularFinalScore
        }))
    } catch (error) {
        console.error('获取平时成绩失败:', error)
    } finally {
        loadingDailyScore.value = false
    }
}
// 平时成绩列配置
const dailyScoreColumns = ref([
    { key: 'studentId', title: '学号', sortable: true },
    { key: 'studentName', title: '姓名' },
    {
        key: 'learningProgressScore',
        title: '学习进度分',
        sortable: true,
        formatter: (value) => `${value}%`
    },
    {
        key: 'interactionCount',
        title: '互动次数',
        sortable: true,
        formatter: (value) => `${value}次`
    },
    {
        key: 'interactionScore',
        title: '互动得分',
        sortable: true,
        formatter: (value) => `${value}分`
    },
    {
        key: 'regularFinalScore',
        title: '平时成绩总分',
        sortable: true,
        formatter: (value) => `${value}分`,
        class: (value) => {
            if (value >= 90) return 'high-score'
            if (value >= 60) return 'medium-score'
            return 'low-score'
        }
    }
])

const homeworkColumns = ref([
    { key: 'studentId', title: '学号', sortable: true },
    { key: 'studentName', title: '姓名' }
])

const fetchHomeworkGradesData = async (classId) => {
    loadingHomework.value = true
    try {
        const res = await fetchHomeworkGrades(classId)
        const students = res.result
        
        if (students.length > 0 && students[0].assignmentDetails) {
            // 添加动态作业列
            const dynamicColumns = students[0].assignmentDetails.map(assignment => ({
                key: `assignment_${assignment.assignmentId}`,
                title: `${assignment.assignmentName} (总分${assignment.totalScore}分)`,
                sortable: true,
                formatter: (value) => {
                    // 未提交显示0分
                    if (value.submitStatus === '未提交') return '0分'
                    return `${value.studentScore}分`
                }
            })) // 这里缺少了闭合的括号

            // 添加总分列
            const totalColumn = {
                key: 'assignmentFinalScore',
                title: '作业成绩总分',
                sortable: true,
                formatter: (value) => `${value}分`,
                class: (value) => {
                    if (value >= 90) return 'high-score'
                    if (value >= 60) return 'medium-score'
                    return 'low-score'
                }
            }

            // 更新列配置
            homeworkColumns.value = [
                homeworkColumns.value[0], // 学号
                homeworkColumns.value[1], // 姓名
                ...dynamicColumns,
                totalColumn
            ]

            // 格式化学生数据
            homeworkData.value = students.map(student => {
                const studentData = {
                    studentId: student.studentId,
                    studentName: student.studentName || '未知',
                    assignmentFinalScore: student.assignmentFinalScore
                }

                // 添加每次作业成绩
                student.assignmentDetails.forEach(assignment => {
                    studentData[`assignment_${assignment.assignmentId}`] = {
                        studentScore: assignment.studentScore,
                        submitStatus: assignment.submitStatus
                    }
                })

                return studentData
            })
        } else {
            homeworkData.value = students
        }
    } catch (error) {
        console.error('获取作业成绩失败:', error)
    } finally {
        loadingHomework.value = false
    }
}



// 考试成绩列配置
const examColumns = ref([
    { key: 'studentId', title: '学号', sortable: true },
    { key: 'studentName', title: '姓名' }
    // 动态考试列会在获取数据后添加
])

// 获取考试成绩数据
const fetchExamGradesData = async (classId) => {
    loadingExam.value = true
    try {
        const res = await fetchExamGrades(classId)
        const students = res.result
        
        if (students.length > 0 && students[0].examDetails) {
            // 添加动态考试列
            const dynamicColumns = students[0].examDetails.map(exam => ({
                key: `exam_${exam.examId}`,
                title: `${exam.examName} (总分${exam.totalScore}分)`,
                sortable: true,
                formatter: (value) => {
                    // 未提交显示0分
                    if (value.submitStatus === '未提交') return '0分'
                    return `${value.studentScore}分`
                }
            }))

            // 添加总分列
            const totalColumn = {
                key: 'examFinalScore',
                title: '考试成绩总分',
                sortable: true,
                formatter: (value) => `${value}分`,
                class: (value) => {
                    if (value >= 90) return 'high-score'
                    if (value >= 60) return 'medium-score'
                    return 'low-score'
                }
            }

            // 更新列配置
            examColumns.value = [
                examColumns.value[0], // 学号
                examColumns.value[1], // 姓名
                ...dynamicColumns,
                totalColumn
            ]

            // 格式化学生数据
            examData.value = students.map(student => {
                const studentData = {
                    studentId: student.studentId,
                    studentName: student.studentName || '未知',
                    examFinalScore: student.examFinalScore
                }

                // 添加每次考试成绩
                student.examDetails.forEach(exam => {
                    studentData[`exam_${exam.examId}`] = {
                        studentScore: exam.studentScore,
                        submitStatus: exam.submitStatus
                    }
                })

                return studentData
            })
        } else {
            examData.value = students
        }
    } catch (error) {
        console.error('获取考试成绩失败:', error)
    } finally {
        loadingExam.value = false
    }
}



const handleClassChange = (classId) => {
  selectedClassId.value = classId
  fetchCurrentTabData(classId)
}

// 根据当前激活的标签页加载对应班级的数据
const fetchCurrentTabData = (classId) => {
  switch (activeTab.value) {
    case 'attendance':
      fetchAttendanceData(classId)
      break
    case 'daily':
      fetchRegularGradesData(classId)
      break
    case 'homework':
      fetchHomeworkGradesData(classId) // 新增的作业成绩获取
      break
    case 'exam':
      fetchExamGradesData(classId)
      break
  }
}

onMounted(() => {
    fetchClasses().then(() => {
        if (classList.value.length > 0) {
            const defaultClassId = classList.value[0].id
            selectedClassId.value = defaultClassId  // 给 selectedClassId 赋初值
            fetchAttendanceData(defaultClassId)
            if (activeTab.value === 'daily') {
                fetchRegularGradesData(defaultClassId)
            }
        }
    })
})

watch(activeTab, (newTab) => {
    if (selectedClassId.value) {
        fetchCurrentTabData(selectedClassId.value)
    }
})


</script>

<style scoped lang="scss">
/* 保持原有的样式不变 */
.score-management {
    background-color: #fff;
    padding: 20px;
    border-radius: 4px;
    height: 97%;
    display: flex;
    flex-direction: column;
}

.button-tabs {
    display: flex;
    flex-direction: column;
    gap: 15px;
    flex: 1;
}

.tab-buttons {
    display: flex;
    margin: 0 auto;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    overflow: hidden;

    button {
        flex: none;
        height: 40px;
        min-width: 140px;
        background: none;
        border: none;
        cursor: pointer;
        transition: all 0.3s;
        font-size: 14px;
        color: #606266;
        position: relative;

        &:not(:first-child) {
            border-left: 1px solid #e4e7ed;
        }

        &:first-child {
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
        }

        &:last-child {
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
        }

        &.active {
            background-color: #409eff;
            color: white;
            border-color: #409eff;

            &::before,
            &::after {
                content: '';
                position: absolute;
                top: 0;
                width: 1px;
                height: 100%;
                background-color: #409eff;
            }

            &::before {
                left: -1px;
            }

            &::after {
                right: -1px;
            }
        }

        &:hover:not(.active) {
            background-color: #f5f7fa;
        }
    }
}

.divider {
    height: 1px;
    background-color: #e4e7ed;
    margin: 10px 0;
    width: 100%;
    flex-shrink: 0;
}

.content {
    background-color: #fff;
    border-radius: 4px;
    flex: 1;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    width: 100%;
    box-sizing: border-box;
    padding: 20px;
    overflow: auto;
    min-height: 0;
}
</style>