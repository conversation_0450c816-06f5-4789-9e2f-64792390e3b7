<template>
    <div class="graphToolbar">
        <button class="btn" @click="openModal">创建图谱</button>

        <div v-if="showModal" class="modal-mask" @click.self="showModal = false">
            <div class="modal" :class="{ shake: shouldShake }">
                <h3>创建图谱</h3>
                <input v-model="graphName" placeholder="请输入图谱名称" class="modal-input" />
                <div class="error" v-if="showEmptyError">图谱名称不能为空</div>

                <select v-model="graphType" class="modal-select">
                    <option disabled value="">请选择图谱类型</option>
                    <option value="知识图谱">知识图谱</option>
                    <option value="问题图谱">问题图谱</option>
                    <option value="能力图谱">能力图谱</option>
                </select>
                <div class="error" v-if="showTypeError">已创建过该类型图谱</div>
                <div class="error" v-if="showUnselectedError">请选择图谱类型</div>
                <div class="error" v-if="showKnowledgeGraphRequiredError">必须先创建知识图谱</div>

                <div class="modal-actions">
                    <button @click="handleConfirm">确定</button>
                    <button @click="showModal = false">取消</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useKnowledgeGraphStore } from '@/stores/teacher/graphManager/knowledgeGraphStore';

const showModal = ref(false);
const graphName = ref('');
const graphType = ref('');
const showEmptyError = ref(false);
const showTypeError = ref(false);
const showUnselectedError = ref(false);
const showKnowledgeGraphRequiredError = ref(false);
const shouldShake = ref(false);

const openModal = () => {
    showModal.value = true;
    showEmptyError.value = false;
    showTypeError.value = false;
    showUnselectedError.value = false;
    showKnowledgeGraphRequiredError.value = false;
    shouldShake.value = false;
};

const store = useKnowledgeGraphStore();
const route = useRoute();
const router = useRouter();

// 检查是否已存在同类型图谱
const hasGraphType = (type) => {
    const typeMap = {
        '知识图谱': 0,
        '问题图谱': 1,
        '能力图谱': 2
    };
    const targetType = typeMap[type];
    return store.graphList.some(graph => graph.graphType === targetType);
};

// 检查是否已存在知识图谱
const hasKnowledgeGraph = () => {
    return store.graphList.some(graph => graph.graphType === 0);
};

const triggerShake = () => {
    shouldShake.value = true;
    setTimeout(() => shouldShake.value = false, 500);
};

const handleConfirm = async () => {
    // 重置错误状态
    showEmptyError.value = false;
    showTypeError.value = false;
    showUnselectedError.value = false;
    showKnowledgeGraphRequiredError.value = false;

    // 验证图谱名称
    if (!graphName.value.trim()) {
        showEmptyError.value = true;
        triggerShake();
        return;
    }

    // 验证图谱类型是否已选择
    if (!graphType.value) {
        showUnselectedError.value = true;
        triggerShake();
        return;
    }

    // 验证是否需要先创建知识图谱
    if ((graphType.value === '问题图谱' || graphType.value === '能力图谱') && !hasKnowledgeGraph()) {
        showKnowledgeGraphRequiredError.value = true;
        triggerShake();
        return;
    }

    // 验证图谱类型是否重复
    if (hasGraphType(graphType.value)) {
        showTypeError.value = true;
        triggerShake();
        return;
    }

    const resolvedType = graphType.value === '知识图谱' ? 0 : (graphType.value === '问题图谱' ? 1 : 2);

    const data = {
        courseId: route.params.courseId,
        courseName: route.params.courseName || '默认课程名',
        graphName: graphName.value.trim(),
        graphType: resolvedType,
    };

    // 创建图谱
    await store.createGraphAction(data);

    // 创建成功则刷新图谱列表
    if (!store.error) {
        await store.fetchGraphListByCourse(route.params.courseId);
    }

    graphName.value = '';
    graphType.value = '';
    showModal.value = false;
};
</script>

<style lang="scss" scoped>
.graphToolbar {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 8px;

    .btn {
        padding: 6px 12px;
        background-color: #f9f9ff;
        border: 1px solid #c9d4f3;
        border-radius: 4px;
        color: #4c7bff;
        font-size: 14px;
        cursor: pointer;

        &:hover {
            background-color: #f0f2f5;
        }
    }

    .modal-mask {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2000;

        .modal {
            background: white;
            padding: 20px;
            border-radius: 8px;
            width: 300px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease;

            &.shake {
                animation: shake 0.5s ease;
            }

            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
                20%, 40%, 60%, 80% { transform: translateX(5px); }
            }

            h3 {
                margin-bottom: 12px;
                font-size: 16px;
            }

            .modal-input,
            .modal-select {
                width: 100%;
                padding: 6px 10px;
                margin-bottom: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            }

            .error {
                color: #ff4d4f;
                font-size: 12px;
                margin-top: -5px;
                margin-bottom: 10px;
            }

            .modal-actions {
                display: flex;
                justify-content: flex-end;
                gap: 10px;

                button {
                    padding: 6px 12px;
                    background-color: #4c7bff;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;

                    &:last-child {
                        background-color: #ccc;
                    }
                }
            }
        }
    }
}
</style>