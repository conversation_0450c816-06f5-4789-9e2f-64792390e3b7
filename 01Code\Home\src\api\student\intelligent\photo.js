import {get<PERSON><PERSON>} from '../../key.js';

const deepseekApiKey = getKey('deepseek');

export const generateImage = async (prompt) => {
  try {
    const response = await fetch('/api/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${deepseekApiKey}`
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [
          {
            role: 'system',
            content: '是一个专业的美术指导，请严格遵循以下规则：\n' +
              '  1. 将用户的中文描述转换为英文的DALL-E提示词\n' +
              '  2. 使用逗号分隔的标签形式\n' +
              '  3. 包含艺术风格、色彩、构图等细节\n' +
              '  4. 禁止使用任何中文或特殊符号\n' +
              '  示例输出：futuristic cityscape, cyberpunk style, neon lights, raining, 8k ultra detailed'
          },
          {
            role: 'user',
            content: `请将以下描述转换为英文的图片生成prompt，保持细节丰富：${prompt}`
          }
        ],
        max_tokens: 500,
        temperature: 0.7
      })
    });

    if (!response.ok) {
      const rawResponse = await response.text();
      console.error('原始错误响应:', rawResponse);
      throw new Error(`API错误: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const cleanPrompt = data.choices[0].message.content
      .replace(/["']/g, '') // 移除引号
      .replace(/\n/g, ', ') // 替换行为逗号
      .replace(/[^a-zA-Z0-9,\.\- ]/g, '') // 过滤特殊字符
      .trim();

    const englishPrompt = encodeURIComponent(cleanPrompt);
    return `https://image.pollinations.ai/prompt/${englishPrompt}?width=1024&height=1024&seed=100&model=flux&nologo=true`;
  } catch (error) {
    console.error('生成失败:', error);
    throw new Error(`图片生成失败: ${error.message}`);
  }
};
