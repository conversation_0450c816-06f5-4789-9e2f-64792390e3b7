<template>
  <div class="notification-center">
    <button @click="toggleNotifications">
      <BellIcon class="icon" />
      <span v-if="unreadCount > 0" class="badge">{{ unreadCount }}</span>
    </button>
    
    <div v-if="showNotifications" class="notification-dropdown">
      <div class="notification-header">
        <h3>通知</h3>
        <button @click="markAllAsRead">全部已读</button>
      </div>
      <div class="notification-list">
        <div v-for="(item, index) in notifications" :key="index" 
             class="notification-item" :class="{ unread: index < unreadCount }">
          <p>{{ item.message }}</p>
          <small>{{ formatDate(item.timestamp) }}</small>
        </div>
        <p v-if="notifications.length === 0" class="empty-notice">暂无通知</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { BellIcon } from '@heroicons/vue/24/outline'
import { useAppStore } from '@/stores/app.js'

//获取应用状态
const appStore = useAppStore()
const showNotifications = ref(false)

//获取通知列表、未读数
const notifications = computed(() => appStore.notifications)
const unreadCount = computed(() => appStore.unreadCount)

//显示/隐藏通知列表
const toggleNotifications = () => {
  showNotifications.value = !showNotifications.value
}

//全部标记为易读
const markAllAsRead = () => {
  appStore.markAsRead()
}

//格式化时间戳为本地时间字符串
const formatDate = (timestamp) => {
  return new Date(timestamp).toLocaleString()
}
</script>

<style>
/* 图标样式 */
.icon {
  width: 1.5rem;
  height: 1.5rem;
}

.empty-notice {
  padding: 1rem;
  text-align: center;
  color: #6b7280;
}
</style>