<template>
  <el-dialog
    :model-value="visible"
    :title="title"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @update:modelValue="$emit('update:visible', $event)"
  >
    <div v-html="message"></div>
    <template #footer>
      <el-button @click="handleCancel">{{ cancelText }}</el-button>
      <el-button 
        type="primary" 
        @click="handleConfirm"
        :disabled="countdown > 0"
      >
        {{ confirmText }}{{ countdown > 0 ? `(${countdown}s)` : '' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, onMounted, onBeforeUnmount } from 'vue'

const props = defineProps({
  title: String,
  message: String,
  confirmText: {
    type: String,
    default: '确认'
  },
  cancelText: {
    type: String,
    default: '取消'
  },
  duration: {
    type: Number,
    default: 5
  },
  visible: Boolean
})

const emit = defineEmits(['confirm', 'cancel', 'update:visible'])

const countdown = ref(props.duration)
let timer = null

const startCountdown = () => {
  timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

const handleConfirm = () => {
  emit('confirm')
  emit('update:visible', false)
}

const handleCancel = () => {
  emit('cancel')
  emit('update:visible', false)
}

onMounted(() => {
  console.log('CountdownDialog mounted', props.visible)
  startCountdown()
})

onBeforeUnmount(() => {
  if (timer) clearInterval(timer)
})

// 如果需要监听 visible 变化
watch(() => props.visible, (newVal) => {
  console.log('visible changed:', newVal)
  if (newVal) {
    countdown.value = props.duration
    if (timer) clearInterval(timer)
    startCountdown()
  }
})
</script>