<!--src\components\FilePreviewDialog.vue-->
<template>
  <div v-if="visible" class="preview-dialog-mask" @click.self="close">
    <div class="preview-dialog">
      <div class="preview-header">
        <h3>{{ file.name }}</h3>
        <div class="preview-header-actions">
          <a :href="file.url" download class="download-btn">下载文件</a>
          <span class="close-btn" @click="close">×</span>
        </div>
      </div>

      <!-- 文件预览内容 -->
      <div class="preview-content">
        <!-- 图片预览 -->
        <img v-if="file.type === 6" :src="file.url" class="preview-image" />

        <!-- PDF预览 -->
        <iframe 
          v-else-if="file.type === 2" 
          :src="`https://docs.google.com/viewer?url=${encodeURIComponent(file.url)}&embedded=true`" 
          class="preview-iframe"
          frameborder="0"
        ></iframe>

        <!-- 视频预览 -->
        <video v-else-if="file.type === 1" controls class="preview-video">
          <source :src="file.url" :type="getVideoMimeType(file.fileExtension)">
        </video>

        <!-- Office文档预览 -->
        <iframe
          v-else-if="[3, 4].includes(file.type)"
          :src="`https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(file.url)}`"
          class="preview-iframe"
          frameborder="0"
        ></iframe>

        <!-- 不支持预览的文件类型 -->
        <div v-else class="unsupported-preview">
          <p>该文件类型不支持在线预览</p>
          <a :href="file.url" download class="download-btn">下载文件</a>
        </div>
      </div>


    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  visible: Boolean,
  file: Object
});

const emit = defineEmits(['close']);

const close = () => {
  emit('close');
};

const getVideoMimeType = (extension) => {
  const types = {
    mp4: 'video/mp4',
    mov: 'video/quicktime',
    avi: 'video/x-msvideo',
    wmv: 'video/x-ms-wmv'
  };
  return types[extension] || 'video/mp4';
};
</script>

<style scoped>
.preview-dialog-mask {
  position: fixed;
  top: 80px;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.preview-dialog {
  background-color: white;
  border-radius: 12px;
  width: 90%;
  max-width: 1000px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
}

.preview-header {
  padding: 16px 20px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80%;
}

.preview-header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.close-btn {
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0 5px;
}

.close-btn:hover {
  color: #333;
}

.download-btn {
  padding: 8px 16px;
  background-color: #4c7bff;
  color: white;
  border-radius: 4px;
  text-decoration: none;
  font-size: 14px;
  transition: background-color 0.2s;
}

.download-btn:hover {
  background-color: #3a6bf0;
}

.preview-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  overflow: auto;
  background-color: #f9f9f9;
}

.preview-image {
  max-width: 100%;
  max-height: calc(90vh - 150px);
  object-fit: contain;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.preview-iframe {
  width: 100%;
  height: calc(90vh - 150px);
  border: none;
  background: white;
}

.preview-video {
  width: 90%;
  max-height: calc(90vh - 150px);
}

.unsupported-preview {
  text-align: center;
  padding: 40px 20px;
}


</style>
