{"_attachments": {}, "_id": "debug", "_rev": "1438-61f14674830fd08f52a258aa", "author": {"name": "<PERSON>", "url": "https://github.com/qix-"}, "description": "Lightweight debugging utility for Node.js and the browser", "dist-tags": {"beta": "4.3.3", "latest": "4.4.1"}, "license": "MIT", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "name": "debug", "readme": "# debug\n[![OpenCollective](https://opencollective.com/debug/backers/badge.svg)](#backers)\n[![OpenCollective](https://opencollective.com/debug/sponsors/badge.svg)](#sponsors)\n\n<img width=\"647\" src=\"https://user-images.githubusercontent.com/71256/29091486-fa38524c-7c37-11e7-895f-e7ec8e1039b6.png\">\n\nA tiny JavaScript debugging utility modelled after Node.js core's debugging\ntechnique. Works in Node.js and web browsers.\n\n## Installation\n\n```bash\n$ npm install debug\n```\n\n## Usage\n\n`debug` exposes a function; simply pass this function the name of your module, and it will return a decorated version of `console.error` for you to pass debug statements to. This will allow you to toggle the debug output for different parts of your module as well as the module as a whole.\n\nExample [_app.js_](./examples/node/app.js):\n\n```js\nvar debug = require('debug')('http')\n  , http = require('http')\n  , name = 'My App';\n\n// fake app\n\ndebug('booting %o', name);\n\nhttp.createServer(function(req, res){\n  debug(req.method + ' ' + req.url);\n  res.end('hello\\n');\n}).listen(3000, function(){\n  debug('listening');\n});\n\n// fake worker of some kind\n\nrequire('./worker');\n```\n\nExample [_worker.js_](./examples/node/worker.js):\n\n```js\nvar a = require('debug')('worker:a')\n  , b = require('debug')('worker:b');\n\nfunction work() {\n  a('doing lots of uninteresting work');\n  setTimeout(work, Math.random() * 1000);\n}\n\nwork();\n\nfunction workb() {\n  b('doing some work');\n  setTimeout(workb, Math.random() * 2000);\n}\n\nworkb();\n```\n\nThe `DEBUG` environment variable is then used to enable these based on space or\ncomma-delimited names.\n\nHere are some examples:\n\n<img width=\"647\" alt=\"screen shot 2017-08-08 at 12 53 04 pm\" src=\"https://user-images.githubusercontent.com/71256/29091703-a6302cdc-7c38-11e7-8304-7c0b3bc600cd.png\">\n<img width=\"647\" alt=\"screen shot 2017-08-08 at 12 53 38 pm\" src=\"https://user-images.githubusercontent.com/71256/29091700-a62a6888-7c38-11e7-800b-db911291ca2b.png\">\n<img width=\"647\" alt=\"screen shot 2017-08-08 at 12 53 25 pm\" src=\"https://user-images.githubusercontent.com/71256/29091701-a62ea114-7c38-11e7-826a-2692bedca740.png\">\n\n#### Windows command prompt notes\n\n##### CMD\n\nOn Windows the environment variable is set using the `set` command.\n\n```cmd\nset DEBUG=*,-not_this\n```\n\nExample:\n\n```cmd\nset DEBUG=* & node app.js\n```\n\n##### PowerShell (VS Code default)\n\nPowerShell uses different syntax to set environment variables.\n\n```cmd\n$env:DEBUG = \"*,-not_this\"\n```\n\nExample:\n\n```cmd\n$env:DEBUG='app';node app.js\n```\n\nThen, run the program to be debugged as usual.\n\nnpm script example:\n```js\n  \"windowsDebug\": \"@powershell -Command $env:DEBUG='*';node app.js\",\n```\n\n## Namespace Colors\n\nEvery debug instance has a color generated for it based on its namespace name.\nThis helps when visually parsing the debug output to identify which debug instance\na debug line belongs to.\n\n#### Node.js\n\nIn Node.js, colors are enabled when stderr is a TTY. You also _should_ install\nthe [`supports-color`](https://npmjs.org/supports-color) module alongside debug,\notherwise debug will only use a small handful of basic colors.\n\n<img width=\"521\" src=\"https://user-images.githubusercontent.com/71256/29092181-47f6a9e6-7c3a-11e7-9a14-1928d8a711cd.png\">\n\n#### Web Browser\n\nColors are also enabled on \"Web Inspectors\" that understand the `%c` formatting\noption. These are WebKit web inspectors, Firefox ([since version\n31](https://hacks.mozilla.org/2014/05/editable-box-model-multiple-selection-sublime-text-keys-much-more-firefox-developer-tools-episode-31/))\nand the Firebug plugin for Firefox (any version).\n\n<img width=\"524\" src=\"https://user-images.githubusercontent.com/71256/29092033-b65f9f2e-7c39-11e7-8e32-f6f0d8e865c1.png\">\n\n\n## Millisecond diff\n\nWhen actively developing an application it can be useful to see when the time spent between one `debug()` call and the next. Suppose for example you invoke `debug()` before requesting a resource, and after as well, the \"+NNNms\" will show you how much time was spent between calls.\n\n<img width=\"647\" src=\"https://user-images.githubusercontent.com/71256/29091486-fa38524c-7c37-11e7-895f-e7ec8e1039b6.png\">\n\nWhen stdout is not a TTY, `Date#toISOString()` is used, making it more useful for logging the debug information as shown below:\n\n<img width=\"647\" src=\"https://user-images.githubusercontent.com/71256/29091956-6bd78372-7c39-11e7-8c55-c948396d6edd.png\">\n\n\n## Conventions\n\nIf you're using this in one or more of your libraries, you _should_ use the name of your library so that developers may toggle debugging as desired without guessing names. If you have more than one debuggers you _should_ prefix them with your library name and use \":\" to separate features. For example \"bodyParser\" from Connect would then be \"connect:bodyParser\".  If you append a \"*\" to the end of your name, it will always be enabled regardless of the setting of the DEBUG environment variable.  You can then use it for normal output as well as debug output.\n\n## Wildcards\n\nThe `*` character may be used as a wildcard. Suppose for example your library has\ndebuggers named \"connect:bodyParser\", \"connect:compress\", \"connect:session\",\ninstead of listing all three with\n`DEBUG=connect:bodyParser,connect:compress,connect:session`, you may simply do\n`DEBUG=connect:*`, or to run everything using this module simply use `DEBUG=*`.\n\nYou can also exclude specific debuggers by prefixing them with a \"-\" character.\nFor example, `DEBUG=*,-connect:*` would include all debuggers except those\nstarting with \"connect:\".\n\n## Environment Variables\n\nWhen running through Node.js, you can set a few environment variables that will\nchange the behavior of the debug logging:\n\n| Name      | Purpose                                         |\n|-----------|-------------------------------------------------|\n| `DEBUG`   | Enables/disables specific debugging namespaces. |\n| `DEBUG_HIDE_DATE` | Hide date from debug output (non-TTY).  |\n| `DEBUG_COLORS`| Whether or not to use colors in the debug output. |\n| `DEBUG_DEPTH` | Object inspection depth.                    |\n| `DEBUG_SHOW_HIDDEN` | Shows hidden properties on inspected objects. |\n\n\n__Note:__ The environment variables beginning with `DEBUG_` end up being\nconverted into an Options object that gets used with `%o`/`%O` formatters.\nSee the Node.js documentation for\n[`util.inspect()`](https://nodejs.org/api/util.html#util_util_inspect_object_options)\nfor the complete list.\n\n## Formatters\n\nDebug uses [printf-style](https://wikipedia.org/wiki/Printf_format_string) formatting.\nBelow are the officially supported formatters:\n\n| Formatter | Representation |\n|-----------|----------------|\n| `%O`      | Pretty-print an Object on multiple lines. |\n| `%o`      | Pretty-print an Object all on a single line. |\n| `%s`      | String. |\n| `%d`      | Number (both integer and float). |\n| `%j`      | JSON. Replaced with the string '[Circular]' if the argument contains circular references. |\n| `%%`      | Single percent sign ('%'). This does not consume an argument. |\n\n\n### Custom formatters\n\nYou can add custom formatters by extending the `debug.formatters` object.\nFor example, if you wanted to add support for rendering a Buffer as hex with\n`%h`, you could do something like:\n\n```js\nconst createDebug = require('debug')\ncreateDebug.formatters.h = (v) => {\n  return v.toString('hex')\n}\n\n// …elsewhere\nconst debug = createDebug('foo')\ndebug('this is hex: %h', new Buffer('hello world'))\n//   foo this is hex: 68656c6c6f20776f726c6421 +0ms\n```\n\n\n## Browser Support\n\nYou can build a browser-ready script using [browserify](https://github.com/substack/node-browserify),\nor just use the [browserify-as-a-service](https://wzrd.in/) [build](https://wzrd.in/standalone/debug@latest),\nif you don't want to build it yourself.\n\nDebug's enable state is currently persisted by `localStorage`.\nConsider the situation shown below where you have `worker:a` and `worker:b`,\nand wish to debug both. You can enable this using `localStorage.debug`:\n\n```js\nlocalStorage.debug = 'worker:*'\n```\n\nAnd then refresh the page.\n\n```js\na = debug('worker:a');\nb = debug('worker:b');\n\nsetInterval(function(){\n  a('doing some work');\n}, 1000);\n\nsetInterval(function(){\n  b('doing some work');\n}, 1200);\n```\n\nIn Chromium-based web browsers (e.g. Brave, Chrome, and Electron), the JavaScript console will—by default—only show messages logged by `debug` if the \"Verbose\" log level is _enabled_.\n\n<img width=\"647\" src=\"https://user-images.githubusercontent.com/7143133/152083257-29034707-c42c-4959-8add-3cee850e6fcf.png\">\n\n## Output streams\n\n  By default `debug` will log to stderr, however this can be configured per-namespace by overriding the `log` method:\n\nExample [_stdout.js_](./examples/node/stdout.js):\n\n```js\nvar debug = require('debug');\nvar error = debug('app:error');\n\n// by default stderr is used\nerror('goes to stderr!');\n\nvar log = debug('app:log');\n// set this namespace to log via console.log\nlog.log = console.log.bind(console); // don't forget to bind to console!\nlog('goes to stdout');\nerror('still goes to stderr!');\n\n// set all output to go via console.info\n// overrides all per-namespace log settings\ndebug.log = console.info.bind(console);\nerror('now goes to stdout via console.info');\nlog('still goes to stdout, but via console.info now');\n```\n\n## Extend\nYou can simply extend debugger \n```js\nconst log = require('debug')('auth');\n\n//creates new debug instance with extended namespace\nconst logSign = log.extend('sign');\nconst logLogin = log.extend('login');\n\nlog('hello'); // auth hello\nlogSign('hello'); //auth:sign hello\nlogLogin('hello'); //auth:login hello\n```\n\n## Set dynamically\n\nYou can also enable debug dynamically by calling the `enable()` method :\n\n```js\nlet debug = require('debug');\n\nconsole.log(1, debug.enabled('test'));\n\ndebug.enable('test');\nconsole.log(2, debug.enabled('test'));\n\ndebug.disable();\nconsole.log(3, debug.enabled('test'));\n\n```\n\nprint :   \n```\n1 false\n2 true\n3 false\n```\n\nUsage :  \n`enable(namespaces)`  \n`namespaces` can include modes separated by a colon and wildcards.\n   \nNote that calling `enable()` completely overrides previously set DEBUG variable : \n\n```\n$ DEBUG=foo node -e 'var dbg = require(\"debug\"); dbg.enable(\"bar\"); console.log(dbg.enabled(\"foo\"))'\n=> false\n```\n\n`disable()`\n\nWill disable all namespaces. The functions returns the namespaces currently\nenabled (and skipped). This can be useful if you want to disable debugging\ntemporarily without knowing what was enabled to begin with.\n\nFor example:\n\n```js\nlet debug = require('debug');\ndebug.enable('foo:*,-foo:bar');\nlet namespaces = debug.disable();\ndebug.enable(namespaces);\n```\n\nNote: There is no guarantee that the string will be identical to the initial\nenable string, but semantically they will be identical.\n\n## Checking whether a debug target is enabled\n\nAfter you've created a debug instance, you can determine whether or not it is\nenabled by checking the `enabled` property:\n\n```javascript\nconst debug = require('debug')('http');\n\nif (debug.enabled) {\n  // do stuff...\n}\n```\n\nYou can also manually toggle this property to force the debug instance to be\nenabled or disabled.\n\n## Usage in child processes\n\nDue to the way `debug` detects if the output is a TTY or not, colors are not shown in child processes when `stderr` is piped. A solution is to pass the `DEBUG_COLORS=1` environment variable to the child process.  \nFor example:\n\n```javascript\nworker = fork(WORKER_WRAP_PATH, [workerPath], {\n  stdio: [\n    /* stdin: */ 0,\n    /* stdout: */ 'pipe',\n    /* stderr: */ 'pipe',\n    'ipc',\n  ],\n  env: Object.assign({}, process.env, {\n    DEBUG_COLORS: 1 // without this settings, colors won't be shown\n  }),\n});\n\nworker.stderr.pipe(process.stderr, { end: false });\n```\n\n\n## Authors\n\n - TJ Holowaychuk\n - Nathan Rajlich\n - Andrew Rhyne\n - Josh Junon\n\n## Backers\n\nSupport us with a monthly donation and help us continue our activities. [[Become a backer](https://opencollective.com/debug#backer)]\n\n<a href=\"https://opencollective.com/debug/backer/0/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/0/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/1/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/1/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/2/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/2/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/3/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/3/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/4/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/4/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/5/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/5/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/6/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/6/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/7/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/7/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/8/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/8/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/9/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/9/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/10/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/10/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/11/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/11/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/12/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/12/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/13/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/13/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/14/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/14/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/15/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/15/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/16/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/16/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/17/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/17/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/18/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/18/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/19/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/19/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/20/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/20/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/21/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/21/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/22/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/22/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/23/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/23/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/24/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/24/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/25/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/25/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/26/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/26/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/27/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/27/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/28/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/28/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/29/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/29/avatar.svg\"></a>\n\n\n## Sponsors\n\nBecome a sponsor and get your logo on our README on Github with a link to your site. [[Become a sponsor](https://opencollective.com/debug#sponsor)]\n\n<a href=\"https://opencollective.com/debug/sponsor/0/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/0/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/1/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/1/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/2/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/2/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/3/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/3/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/4/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/4/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/5/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/5/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/6/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/6/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/7/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/7/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/8/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/8/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/9/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/9/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/10/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/10/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/11/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/11/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/12/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/12/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/13/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/13/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/14/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/14/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/15/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/15/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/16/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/16/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/17/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/17/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/18/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/18/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/19/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/19/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/20/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/20/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/21/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/21/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/22/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/22/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/23/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/23/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/24/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/24/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/25/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/25/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/26/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/26/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/27/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/27/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/28/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/28/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/29/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/29/avatar.svg\"></a>\n\n## License\n\n(The MIT License)\n\nCopyright (c) 2014-2017 TJ Holowaychuk &lt;<EMAIL>&gt;\nCopyright (c) 2018-2021 Josh Junon\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "time": {"created": "2022-01-26T13:02:44.378Z", "modified": "2025-06-30T02:32:24.310Z", "4.3.3": "2021-11-27T13:14:24.425Z", "4.3.2": "2020-12-09T15:36:20.909Z", "3.2.7": "2020-11-19T12:57:17.399Z", "4.3.1": "2020-11-19T12:23:08.941Z", "4.3.0": "2020-09-19T08:36:29.497Z", "4.2.0": "2020-05-19T09:51:27.149Z", "4.1.1": "2018-12-22T16:40:22.538Z", "3.2.6": "2018-10-10T06:48:00.226Z", "4.1.0": "2018-10-08T17:51:43.321Z", "4.0.1": "2018-09-11T23:16:32.204Z", "3.2.5": "2018-09-11T23:12:21.584Z", "3.2.4": "2018-09-11T09:12:30.102Z", "4.0.0": "2018-09-11T08:58:14.825Z", "3.2.3": "2018-09-11T08:30:38.788Z", "3.2.2": "2018-09-11T07:50:29.987Z", "3.2.1": "2018-09-11T06:28:53.798Z", "3.2.0": "2018-09-11T06:19:14.567Z", "3.1.0": "2017-09-26T19:13:51.492Z", "2.6.9": "2017-09-22T13:32:35.541Z", "3.0.1": "2017-08-24T19:44:31.890Z", "3.0.0": "2017-08-08T21:55:59.088Z", "1.0.5": "2017-06-15T00:14:24.388Z", "2.6.8": "2017-05-18T20:07:01.168Z", "2.6.7": "2017-05-17T04:33:51.578Z", "2.6.6": "2017-04-27T23:35:02.119Z", "2.6.5": "2017-04-27T16:04:12.415Z", "2.6.4": "2017-04-20T18:08:07.089Z", "2.6.3": "2017-03-14T03:50:34.042Z", "2.6.2": "2017-03-10T19:44:26.365Z", "2.6.1": "2017-02-10T19:00:28.639Z", "2.6.0": "2016-12-29T05:50:33.866Z", "2.5.2": "2016-12-26T02:39:46.961Z", "2.5.1": "2016-12-21T05:33:20.503Z", "2.5.0": "2016-12-21T05:03:29.680Z", "2.4.5": "2016-12-18T07:13:49.109Z", "2.4.4": "2016-12-15T01:27:05.600Z", "2.4.3": "2016-12-14T21:50:00.788Z", "2.4.2": "2016-12-14T19:40:21.566Z", "2.4.1": "2016-12-14T07:25:40.783Z", "2.4.0": "2016-12-14T06:52:06.597Z", "2.3.3": "2016-11-19T19:59:18.541Z", "2.3.2": "2016-11-10T06:30:04.055Z", "2.3.1": "2016-11-10T00:14:23.056Z", "2.3.0": "2016-11-07T17:40:37.812Z", "2.2.0": "2015-05-10T07:21:25.639Z", "2.1.3": "2015-03-13T18:50:21.566Z", "2.1.2": "2015-03-02T01:39:40.274Z", "2.1.1": "2014-12-29T21:51:01.149Z", "2.1.0": "2014-10-15T21:58:41.028Z", "2.0.0": "2014-09-01T07:21:43.687Z", "1.0.4": "2014-07-15T23:16:08.284Z", "1.0.3": "2014-07-09T16:16:47.588Z", "1.0.2": "2014-06-11T00:50:47.529Z", "1.0.1": "2014-06-06T20:23:09.807Z", "1.0.0": "2014-06-05T03:55:56.207Z", "0.8.1": "2014-04-15T02:04:45.652Z", "0.8.0": "2014-03-30T16:00:17.026Z", "0.7.4": "2013-11-13T20:08:37.779Z", "0.7.3": "2013-10-31T00:51:26.848Z", "0.7.2": "2013-02-06T23:40:19.513Z", "0.7.1": "2013-02-06T21:53:43.587Z", "0.7.0": "2012-07-09T19:11:59.699Z", "0.6.0": "2012-03-16T21:58:51.296Z", "0.5.0": "2012-02-03T00:56:44.457Z", "0.4.1": "2012-02-02T19:54:44.139Z", "0.4.0": "2012-02-01T21:20:47.417Z", "0.3.0": "2012-01-27T00:37:12.739Z", "0.2.0": "2012-01-22T18:26:41.329Z", "0.1.0": "2011-12-02T23:16:56.971Z", "0.0.1": "2011-11-29T01:11:25.405Z", "4.3.4": "2022-03-17T13:38:47.641Z", "4.3.5": "2024-05-31T11:40:15.895Z", "4.3.6": "2024-07-27T09:23:24.842Z", "4.3.7": "2024-09-06T00:52:57.702Z", "4.4.0": "2024-12-06T12:32:46.280Z", "4.4.1": "2025-05-13T20:56:36.431Z"}, "versions": {"4.3.3": {"name": "debug", "version": "4.3.3", "repository": {"type": "git", "url": "git://github.com/debug-js/debug.git"}, "description": "Lightweight debugging utility for Node.js and the browser", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser && npm run lint", "test:node": "istanbul cover _mocha -- test.js", "test:browser": "karma start --single-run", "test:coverage": "cat ./coverage/lcov.info | coveralls"}, "dependencies": {"ms": "2.1.2"}, "devDependencies": {"brfs": "^2.0.1", "browserify": "^16.2.3", "coveralls": "^3.0.2", "istanbul": "^0.4.5", "karma": "^3.1.4", "karma-browserify": "^6.0.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "xo": "^0.23.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}, "main": "./src/index.js", "browser": "./src/browser.js", "engines": {"node": ">=6.0"}, "readmeFilename": "README.md", "gitHead": "043d3cd17d30af45f71d2beab4ec7abfc9936e9e", "bugs": {"url": "https://github.com/debug-js/debug/issues"}, "homepage": "https://github.com/debug-js/debug#readme", "_id": "debug@4.3.3", "_nodeVersion": "16.0.0", "_npmVersion": "8.1.4", "dist": {"integrity": "sha512-/zxw5+vh1Tfv+4Qn7a5nsbcJKPaSvCDhojn6FEl9vupwK2VCSDtEiEtqr8DFtzYFOdz63LBkxec7DYuc2jon6Q==", "shasum": "04266e0b70a98d4462e6e288e38259213332b664", "tarball": "https://registry.npmmirror.com/debug/-/debug-4.3.3.tgz", "fileCount": 7, "unpackedSize": 42039, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhoi8wCRA9TVsSAnZWagAAMQIQAIBVX5eU9r89GpL0l/jh\nAeLvydJ2+QkHftmS+SqCXxx9d2YYiPGdSdvSt+iVR4oR1YFpa815iJAXeI6Q\n3HvInF5rZh2Xdh4JJN3eJjm0A93jIs0LHpRQw2LCUbbNQfXLprzVcrlFNSJX\nfdTA6yPTU2xJfbE0vCUI7eqY/fopE7j+VywFz1t74k3yQLHjvO6lZdvvH9zZ\nVlGjZA0YRiDVvnHckjiK3req/qxeDHchRaXvOCy7NFk0KWjBNjB4fU+skDJa\nl1TtnsRC4ApvXVKdb4FtzKjVSnybA0pOa6ZCWLwzk1rEKAiKOy6HRka579UX\n5DAv8vfWe6ssdkhTWP76b92TCorKJZGP7V2odekPXMuh5HdD9YyWERWQk+jH\npOQ9nSRriSdGvpq1EpSm35nB/PMe6x/MlRCdTRfWtqXbZrShcuiZQ2DJkpjV\nXXhAl8edrt5mDGS8K6/6ToSFjyApNfdtzBBvtAOmKZnd55prw3CndkAGyA9W\n9h8RTIjaAcN7mfB/VC5rcIqKwHZu3UrYRlDqAAXjR/KWKlLkGal9EPJBUuy8\nTqjXrAQJRh23WFHnskZFwcZ1Jzbq82DgN8j6k+o3Dg5yMXA/uziM0wuf6LKe\nbduB5k1PPHnHusUiJt7L5GQs7JNmQ7nBq1spTjChbMU6LsAt7sxfb+BaUJO4\nmtBR\r\n=LlaO\r\n-----END PGP SIGNATURE-----\r\n", "size": 13116, "noattachment": false}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/debug_4.3.3_1638018864282_0.4082672439443171"}, "_hasShrinkwrap": false, "publish_time": 1638018864425, "_cnpm_publish_time": 1638018864425, "_cnpmcore_publish_time": "2021-12-13T06:32:11.806Z"}, "4.3.2": {"name": "debug", "version": "4.3.2", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser && npm run lint", "test:node": "istanbul cover _mocha -- test.js", "test:browser": "karma start --single-run", "test:coverage": "cat ./coverage/lcov.info | coveralls"}, "dependencies": {"ms": "2.1.2"}, "devDependencies": {"brfs": "^2.0.1", "browserify": "^16.2.3", "coveralls": "^3.0.2", "istanbul": "^0.4.5", "karma": "^3.1.4", "karma-browserify": "^6.0.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "xo": "^0.23.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}, "main": "./src/index.js", "browser": "./src/browser.js", "engines": {"node": ">=6.0"}, "readmeFilename": "README.md", "gitHead": "e47f96de3de5921584364b4ac91e2769d22a3b1f", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@4.3.2", "_nodeVersion": "15.3.0", "_npmVersion": "7.0.14", "dist": {"shasum": "f0a49c18ac8779e31d4a0c6029dfb76873c7428b", "size": 12876, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-4.3.2.tgz", "integrity": "sha512-mOp8wKcvj7XxC78zLgw/ZA+6TSgkoE2C/ienthhRD298T7UNwAg9diBpLRxC0mOezLl4B0xV7M0cCO6P/O0Xhw=="}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/debug_4.3.2_1607528180776_0.655691523543942"}, "_hasShrinkwrap": false, "publish_time": 1607528180909, "_cnpm_publish_time": 1607528180909, "_cnpmcore_publish_time": "2021-12-13T06:32:12.152Z"}, "3.2.7": {"name": "debug", "version": "3.2.7", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "^2.1.1"}, "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "@babel/preset-env": "^7.0.0", "browserify": "14.4.0", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^3.0.2", "istanbul": "^0.4.5", "karma": "^3.0.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "xo": "^0.23.0"}, "main": "./src/index.js", "browser": "./src/browser.js", "unpkg": "./dist/debug.js", "readmeFilename": "README.md", "gitHead": "338326076faaf6d230090903de97f459c4bccabc", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@3.2.7", "_nodeVersion": "14.13.1", "_npmVersion": "6.14.8", "dist": {"shasum": "72580b7e9145fb39b6676f9c5e5fb100b934179a", "size": 16872, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-3.2.7.tgz", "integrity": "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ=="}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/debug_3.2.7_1605790637206_0.4902544321634046"}, "_hasShrinkwrap": false, "publish_time": 1605790637399, "_cnpm_publish_time": 1605790637399, "_cnpmcore_publish_time": "2021-12-13T06:32:12.472Z"}, "4.3.1": {"name": "debug", "version": "4.3.1", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser && npm run lint", "test:node": "istanbul cover _mocha -- test.js", "test:browser": "karma start --single-run", "test:coverage": "cat ./coverage/lcov.info | coveralls"}, "dependencies": {"ms": "2.1.2"}, "devDependencies": {"brfs": "^2.0.1", "browserify": "^16.2.3", "coveralls": "^3.0.2", "istanbul": "^0.4.5", "karma": "^3.1.4", "karma-browserify": "^6.0.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "xo": "^0.23.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}, "main": "./src/index.js", "browser": "./src/browser.js", "engines": {"node": ">=6.0"}, "readmeFilename": "README.md", "gitHead": "0d3d66b0eb47c5d34e1a940e8a204446fdd832cd", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@4.3.1", "_nodeVersion": "14.13.1", "_npmVersion": "6.14.8", "dist": {"shasum": "f0d229c505e0c6d8c49ac553d1b13dc183f6b2ee", "size": 12810, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-4.3.1.tgz", "integrity": "sha512-doEwdvm4PCeK4K3RQN2ZC2BYUBaxwLARCqZmMjtF8a51J2Rb0xpVloFRnCODwqjpwnAoao4pelN8l3RJdv3gRQ=="}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/debug_4.3.1_1605788588845_0.34190574191611"}, "_hasShrinkwrap": false, "publish_time": 1605788588941, "_cnpm_publish_time": 1605788588941, "_cnpmcore_publish_time": "2021-12-13T06:32:12.764Z"}, "4.3.0": {"name": "debug", "version": "4.3.0", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser && npm run lint", "test:node": "istanbul cover _mocha -- test.js", "test:browser": "karma start --single-run", "test:coverage": "cat ./coverage/lcov.info | coveralls"}, "dependencies": {"ms": "2.1.2"}, "devDependencies": {"brfs": "^2.0.1", "browserify": "^16.2.3", "coveralls": "^3.0.2", "istanbul": "^0.4.5", "karma": "^3.1.4", "karma-browserify": "^6.0.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "xo": "^0.23.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}, "main": "./src/index.js", "browser": "./src/browser.js", "engines": {"node": ">=6.0"}, "readmeFilename": "README.md", "gitHead": "3f56313c1e4a0d59c1054fb9b10026b6903bfba7", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@4.3.0", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.5", "dist": {"shasum": "efa41cbf14fc9448075367fdaaddf82376da211e", "size": 12799, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-4.3.0.tgz", "integrity": "sha512-jjO6JD2rKfiZQnBoRzhRTbXjHLGLfH+UtGkWLc/UXAh/rzZMyjbgn0NcfFpqT8nd1kTtFnDiJcrIFkq4UKeJVg=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/debug_4.3.0_1600504589377_0.15241949557753798"}, "_hasShrinkwrap": false, "publish_time": 1600504589497, "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "_cnpm_publish_time": 1600504589497, "_cnpmcore_publish_time": "2021-12-13T06:32:13.126Z"}, "4.2.0": {"name": "debug", "version": "4.2.0", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser && npm run lint", "test:node": "istanbul cover _mocha -- test.js", "test:browser": "karma start --single-run", "test:coverage": "cat ./coverage/lcov.info | coveralls"}, "dependencies": {"ms": "2.1.2"}, "devDependencies": {"brfs": "^2.0.1", "browserify": "^16.2.3", "coveralls": "^3.0.2", "istanbul": "^0.4.5", "karma": "^3.1.4", "karma-browserify": "^6.0.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "xo": "^0.23.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}, "main": "./src/index.js", "browser": "./src/browser.js", "engines": {"node": ">=6.0"}, "readmeFilename": "README.md", "gitHead": "80ef62a3af4df95250d77d64edfc3d0e1667e7e8", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@4.2.0", "_nodeVersion": "13.9.0", "_npmVersion": "6.13.7", "dist": {"shasum": "7f150f93920e94c58f5574c2fd01a3110effe7f1", "size": 12579, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-4.2.0.tgz", "integrity": "sha512-IX2ncY78vDTjZMFUdmsvIRFY2Cf4FnD0wRs+nQwJU8Lu99/tPFdb0VybiiMTPe3I6rQmwsqQqRBvxU+bZ/I8sg=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/debug_4.2.0_1589881887045_0.10965141172270587"}, "_hasShrinkwrap": false, "publish_time": 1589881887149, "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "_cnpm_publish_time": 1589881887149, "_cnpmcore_publish_time": "2021-12-13T06:32:13.496Z"}, "4.1.1": {"name": "debug", "version": "4.1.1", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser", "test:node": "istanbul cover _mocha -- test.js", "pretest:browser": "npm run build", "test:browser": "karma start --single-run", "prebuild:debug": "mkdir -p dist && browserify --standalone debug -o dist/debug.es6.js .", "build:debug": "babel -o dist/debug.js dist/debug.es6.js > dist/debug.js", "build:test": "babel -d dist test.js", "build": "npm run build:debug && npm run build:test", "clean": "rimraf dist coverage", "test:coverage": "cat ./coverage/lcov.info | coveralls"}, "dependencies": {"ms": "^2.1.1"}, "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "@babel/preset-env": "^7.0.0", "browserify": "14.4.0", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^3.0.2", "istanbul": "^0.4.5", "karma": "^3.0.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "xo": "^0.23.0"}, "main": "./src/index.js", "browser": "./src/browser.js", "unpkg": "./dist/debug.js", "gitHead": "68b4dc8d8549d3924673c38fccc5d594f0a38da1", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@4.1.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.14.2", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "3b72260255109c6b589cee050f1d516139664791", "size": 21773, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-4.1.1.tgz", "integrity": "sha512-pYA<PERSON>zeRo8J6KPEaJ0VWOh5Pzkbw/RetuzehGM7QRRX5he4fPHx2rdKMB256ehJCkX+XRQm16eZLqLNS8RSZXZw=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/debug_4.1.1_1545496822417_0.37311624175986635"}, "_hasShrinkwrap": false, "publish_time": 1545496822538, "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "_cnpm_publish_time": 1545496822538, "_cnpmcore_publish_time": "2021-12-13T06:32:13.857Z"}, "3.2.6": {"name": "debug", "version": "3.2.6", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "^2.1.1"}, "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "@babel/preset-env": "^7.0.0", "browserify": "14.4.0", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^3.0.2", "istanbul": "^0.4.5", "karma": "^3.0.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "xo": "^0.23.0"}, "main": "./src/index.js", "browser": "./src/browser.js", "unpkg": "./dist/debug.js", "readmeFilename": "README.md", "gitHead": "a7a17c9955460435592de2a4d3c722e9b32047a8", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@3.2.6", "_npmVersion": "6.1.0", "_nodeVersion": "10.10.0", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "e83d17de16d8a7efb7717edbe5fb10135eee629b", "size": 20436, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-3.2.6.tgz", "integrity": "sha512-mel+jf7nrtEl5Pn1Qx46zARXKDpBbvzezse7p7LqINmdoIk8PYP5SySaxEmYv6TZ0JyEKA1hsCId6DIhgITtWQ=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/debug_3.2.6_1539154080132_0.756699343768702"}, "_hasShrinkwrap": false, "publish_time": 1539154080226, "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "_cnpm_publish_time": 1539154080226, "_cnpmcore_publish_time": "2021-12-13T06:32:14.284Z"}, "4.1.0": {"name": "debug", "version": "4.1.0", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser", "test:node": "istanbul cover _mocha -- test.js", "posttest:node": "cat ./coverage/lcov.info | coveralls", "pretest:browser": "npm run build", "test:browser": "karma start --single-run", "prebuild:debug": "mkdir -p dist && browserify --standalone debug -o dist/debug.es6.js .", "build:debug": "babel -o dist/debug.js dist/debug.es6.js > dist/debug.js", "build:test": "babel -d dist test.js", "build": "npm run build:debug && npm run build:test", "clean": "rimraf dist coverage"}, "dependencies": {"ms": "^2.1.1"}, "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "@babel/preset-env": "^7.0.0", "browserify": "14.4.0", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^3.0.2", "istanbul": "^0.4.5", "karma": "^3.0.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "xo": "^0.23.0"}, "main": "./src/index.js", "browser": "./src/browser.js", "unpkg": "./dist/debug.js", "gitHead": "e30e8fdbc92c4cf6b3007cd1c3ad2c3cbb82be85", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@4.1.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.10.0", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "373687bffa678b38b1cd91f861b63850035ddc87", "size": 21469, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-4.1.0.tgz", "integrity": "sha512-heNPJUJIqC+xB6ayLAMHaIrmN9HKa7aQO8MGqKpvCA+uJYVcvR6l5kgdrhRuwPFHU7P5/A1w0BjByPHwpfTDKg=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/debug_4.1.0_1539021103154_0.029486584589888398"}, "_hasShrinkwrap": false, "publish_time": 1539021103321, "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "_cnpm_publish_time": 1539021103321, "_cnpmcore_publish_time": "2021-12-13T06:32:14.697Z"}, "4.0.1": {"name": "debug", "version": "4.0.1", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "^2.1.1"}, "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "@babel/preset-env": "^7.0.0", "browserify": "14.4.0", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^3.0.2", "istanbul": "^0.4.5", "karma": "^3.0.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "xo": "^0.23.0"}, "main": "./src/index.js", "browser": "./src/browser.js", "unpkg": "./dist/debug.js", "gitHead": "4490cd95bfb952e1ed756914ac225ddc987b2ba3", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@4.0.1", "_npmVersion": "6.1.0", "_nodeVersion": "10.10.0", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "f9bb36d439b8d1f0dd52d8fb6b46e4ebb8c1cd5b", "size": 20993, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-4.0.1.tgz", "integrity": "sha512-K23FHJ/Mt404FSlp6gSZCevIbTMLX0j3fmHhUEhQ3Wq0FMODW3+cUSoLdy1Gx4polAf4t/lphhmHH35BB8cLYw=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/debug_4.0.1_1536707791890_0.14124621815358362"}, "_hasShrinkwrap": false, "publish_time": 1536707792204, "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "_cnpm_publish_time": 1536707792204, "_cnpmcore_publish_time": "2021-12-13T06:32:15.138Z"}, "3.2.5": {"name": "debug", "version": "3.2.5", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "^2.1.1"}, "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "@babel/preset-env": "^7.0.0", "browserify": "14.4.0", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^3.0.2", "istanbul": "^0.4.5", "karma": "^3.0.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "xo": "^0.23.0"}, "main": "./src/index.js", "browser": "./src/browser.js", "unpkg": "./dist/debug.js", "readmeFilename": "README.md", "gitHead": "9a6d8c20a8b92f7df1f10f343c8238760ec4902f", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@3.2.5", "_npmVersion": "6.1.0", "_nodeVersion": "10.10.0", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "c2418fbfd7a29f4d4f70ff4cea604d4b64c46407", "size": 20435, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-3.2.5.tgz", "integrity": "sha512-D61LaDQPQkxJ5AUM2mbSJRbPkNs/TmdmOeLAi1hgDkpDfIfetSrjmWhccwtuResSwMbACjx/xXQofvM9CE/aeg=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/debug_3.2.5_1536707541447_0.29116777864588417"}, "_hasShrinkwrap": false, "publish_time": 1536707541584, "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "_cnpm_publish_time": 1536707541584, "_cnpmcore_publish_time": "2021-12-13T06:32:15.573Z"}, "3.2.4": {"name": "debug", "version": "3.2.4", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "^2.1.1"}, "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "@babel/preset-env": "^7.0.0", "browserify": "14.4.0", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^3.0.2", "istanbul": "^0.4.5", "karma": "^3.0.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "xo": "^0.23.0"}, "main": "./src/index.js", "browser": "./dist/debug.js", "gitHead": "78741cceaa01780ad2b4ba859e65ad4c9f52d65a", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@3.2.4", "_npmVersion": "6.1.0", "_nodeVersion": "10.10.0", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "82123737c51afbe9609a2b5dfe9664e7487171f0", "size": 20408, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-3.2.4.tgz", "integrity": "sha512-fCEG5fOr7m/fhgOD3KurdAov706JbXZJYXAsAOEJ7GgasGr0GO4N+1NsIcrjlIUcyvJ9oZlnelTzN3Ix8z1ecw=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/debug_3.2.4_1536657149964_0.37588515769822384"}, "_hasShrinkwrap": false, "publish_time": 1536657150102, "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "_cnpm_publish_time": 1536657150102, "_cnpmcore_publish_time": "2021-12-13T06:32:16.014Z"}, "4.0.0": {"name": "debug", "version": "4.0.0", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "^2.1.1"}, "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "@babel/preset-env": "^7.0.0", "browserify": "14.4.0", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^3.0.2", "istanbul": "^0.4.5", "karma": "^3.0.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "xo": "^0.23.0"}, "main": "./src/index.js", "browser": "./dist/debug.js", "gitHead": "7fb104b8cfcbc3a91d8e4a6727638c3fe24be8d2", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@4.0.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.10.0", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "ef7592a30b1c5e05a78e96926df2e6b36b5544e7", "size": 20963, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-4.0.0.tgz", "integrity": "sha512-PlYAp+yaKUjcs6FIDv1G2kU9jh4+OOD7AniwnWEvdoeHSsi5X6vRNuI9MDZCl8YcF/aNsvuF5EDOjY/v90zdrg=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/debug_4.0.0_1536656294677_0.496757503110125"}, "_hasShrinkwrap": false, "publish_time": 1536656294825, "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "_cnpm_publish_time": 1536656294825, "_cnpmcore_publish_time": "2021-12-13T06:32:16.559Z"}, "3.2.3": {"name": "debug", "version": "3.2.3", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "^2.1.1"}, "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "@babel/preset-env": "^7.0.0", "browserify": "14.4.0", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^3.0.2", "istanbul": "^0.4.5", "karma": "^3.0.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "xo": "^0.23.0"}, "main": "./src/index.js", "browser": "./dist/debug.js", "gitHead": "700a01074456ac42922392c7f327b2d7dfe23dc8", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@3.2.3", "_npmVersion": "6.1.0", "_nodeVersion": "10.10.0", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "3f2a4bf7afb0a73a960fed83a197692dba736b83", "size": 20439, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-3.2.3.tgz", "integrity": "sha512-4J+rux2EAUIbVjYOvNx/6tQxCSrKDAFm4qUDNTnflVPCj/jgCb5xbt7jfAkMC2iGmie1i/+ViS/QpAziYe+rPQ=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/debug_3.2.3_1536654638619_0.36104977498228963"}, "_hasShrinkwrap": false, "publish_time": 1536654638788, "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "_cnpm_publish_time": 1536654638788, "_cnpmcore_publish_time": "2021-12-13T06:32:17.036Z"}, "3.2.2": {"name": "debug", "version": "3.2.2", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "^2.1.1"}, "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "@babel/preset-env": "^7.0.0", "browserify": "14.4.0", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^3.0.2", "istanbul": "^0.4.5", "karma": "^3.0.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "xo": "^0.23.0"}, "main": "./src/index.js", "browser": "./dist/debug.js", "gitHead": "622e5798cbe1b9b48930435f2960d6c2f4684300", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@3.2.2", "_npmVersion": "6.1.0", "_nodeVersion": "10.10.0", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "1e7ad4d9714bb51e6745c0f15559ee82c0236010", "size": 20349, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-3.2.2.tgz", "integrity": "sha512-VDxmXLtYzTDjwoB/XfDYCgN5RaHUEAknclvDu17QVvkID8mpWUt2+I3zAc98xu1XOSCQY9yl072oHFXDhY9gxg=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/debug_3.2.2_1536652229813_0.9493398657476286"}, "_hasShrinkwrap": false, "publish_time": 1536652229987, "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "_cnpm_publish_time": 1536652229987, "_cnpmcore_publish_time": "2021-12-13T06:32:17.522Z"}, "3.2.1": {"name": "debug", "version": "3.2.1", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "2.0.0"}, "devDependencies": {"browserify": "14.4.0", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^2.11.15", "eslint": "^3.12.1", "istanbul": "^0.4.5", "karma": "^1.3.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon": "^1.0.5", "mocha": "^3.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "sinon": "^1.17.6", "sinon-chai": "^2.8.0"}, "main": "./src/index.js", "browser": "./src/browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "3e1849d3aaa1b9a325ad6d054acf695fddb4efe9", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@3.0.1", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "0564c612b521dc92d9f2988f0549e34f9c98db64", "size": 17144, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-3.0.1.tgz", "integrity": "sha512-6nVc6S36qbt/mutyt+UGMnawAMrPDZUPQjRZI3FS9tCtDRhvxJbK79unYBLPi+z5SLXQ3ftoVBFCblQtNSls8w=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/debug-3.0.1.tgz_1503603871771_0.21796362148597836"}, "directories": {}, "publish_time": 1503603871890, "_hasShrinkwrap": false, "_cnpm_publish_time": 1503603871890, "_cnpmcore_publish_time": "2021-12-13T06:32:20.317Z", "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797) ([WARNING] Use 3.0.1 instead of 3.2.1, reason: breaking change)"}, "3.2.0": {"name": "debug", "version": "3.2.0", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "^2.1.1"}, "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "@babel/preset-env": "^7.0.0", "browserify": "14.4.0", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^3.0.2", "istanbul": "^0.4.5", "karma": "^3.0.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "xo": "^0.23.0"}, "main": "./src/index.js", "browser": "./src/browser.js", "gitHead": "dec4b159ddf63915c94cd9d8421ad11cd06f0e76", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@3.2.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.10.0", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "cfba3c774175ee0f59a51cf8e0849aca9bfd8a9c", "size": 20965, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-3.2.0.tgz", "integrity": "sha512-Ii8hOmyHANEZ4wnsj5ZKeWUQRLt+ZD6WSb1e+/RK0BNIrVaZQrN0r2qxl8ZvnFgb4TQpd9nsjGUxdPIUXo6Snw=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/debug_3.2.0_1536646754464_0.27788234878197926"}, "_hasShrinkwrap": false, "publish_time": 1536646754567, "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "_cnpm_publish_time": 1536646754567, "_cnpmcore_publish_time": "2021-12-13T06:32:18.524Z"}, "3.1.0": {"name": "debug", "version": "3.1.0", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "2.0.0"}, "devDependencies": {"browserify": "14.4.0", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^2.11.15", "eslint": "^3.12.1", "istanbul": "^0.4.5", "karma": "^1.3.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon": "^1.0.5", "mocha": "^3.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "sinon": "^1.17.6", "sinon-chai": "^2.8.0"}, "main": "./src/index.js", "browser": "./src/browser.js", "gitHead": "f073e056f33efdd5b311381eb6bca2bc850745bf", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@3.1.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "5bb5a0672628b64149566ba16819e61518c67261", "size": 17183, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-3.1.0.tgz", "integrity": "sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/debug-3.1.0.tgz_1506453230282_0.13498495938256383"}, "directories": {}, "publish_time": 1506453231492, "_hasShrinkwrap": false, "_cnpm_publish_time": 1506453231492, "_cnpmcore_publish_time": "2021-12-13T06:32:19.051Z"}, "2.6.9": {"name": "debug", "version": "2.6.9", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "2.0.0"}, "devDependencies": {"browserify": "9.0.3", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^2.11.15", "eslint": "^3.12.1", "istanbul": "^0.4.5", "karma": "^1.3.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon": "^1.0.5", "mocha": "^3.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "sinon": "^1.17.6", "sinon-chai": "^2.8.0"}, "main": "./src/index.js", "browser": "./src/browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "13abeae468fea297d0dccc50bc55590809241083", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@2.6.9", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "5d128515df134ff327e90a4c93f4e077a536341f", "size": 16514, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/debug-2.6.9.tgz_1506087154503_0.5196126794908196"}, "directories": {}, "publish_time": 1506087155541, "_hasShrinkwrap": false, "_cnpm_publish_time": 1506087155541, "_cnpmcore_publish_time": "2021-12-13T06:32:19.697Z"}, "3.0.1": {"name": "debug", "version": "3.0.1", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "2.0.0"}, "devDependencies": {"browserify": "14.4.0", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^2.11.15", "eslint": "^3.12.1", "istanbul": "^0.4.5", "karma": "^1.3.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon": "^1.0.5", "mocha": "^3.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "sinon": "^1.17.6", "sinon-chai": "^2.8.0"}, "main": "./src/index.js", "browser": "./src/browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "3e1849d3aaa1b9a325ad6d054acf695fddb4efe9", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@3.0.1", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "0564c612b521dc92d9f2988f0549e34f9c98db64", "size": 17144, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-3.0.1.tgz", "integrity": "sha512-6nVc6S36qbt/mutyt+UGMnawAMrPDZUPQjRZI3FS9tCtDRhvxJbK79unYBLPi+z5SLXQ3ftoVBFCblQtNSls8w=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/debug-3.0.1.tgz_1503603871771_0.21796362148597836"}, "directories": {}, "publish_time": 1503603871890, "_hasShrinkwrap": false, "_cnpm_publish_time": 1503603871890, "_cnpmcore_publish_time": "2021-12-13T06:32:20.317Z"}, "3.0.0": {"name": "debug", "version": "3.0.0", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "2.0.0"}, "devDependencies": {"browserify": "14.4.0", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^2.11.15", "eslint": "^3.12.1", "istanbul": "^0.4.5", "karma": "^1.3.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon": "^1.0.5", "mocha": "^3.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "sinon": "^1.17.6", "sinon-chai": "^2.8.0"}, "main": "./src/index.js", "browser": "./src/browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "52b894cd798f492ead1866fca4d76a649f0e62c6", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@3.0.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "1d2feae53349047b08b264ec41906ba17a8516e4", "size": 17040, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-3.0.0.tgz", "integrity": "sha512-XQkHxxqbsCb+zFurCHbotmJZl5jXsxvkRt952pT6Hpo7LmjWAJF12d9/kqBg5owjbLADbBDli1olravjSiSg8g=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/debug-3.0.0.tgz_1502229358950_0.15122428792528808"}, "directories": {}, "publish_time": 1502229359088, "_hasShrinkwrap": false, "_cnpm_publish_time": 1502229359088, "_cnpmcore_publish_time": "2021-12-13T06:32:20.971Z"}, "1.0.5": {"name": "debug", "version": "1.0.5", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}], "dependencies": {"ms": "2.0.0"}, "devDependencies": {"browserify": "4.1.6", "mocha": "*"}, "main": "./node.js", "browser": "./browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "7e2d77fcd1ebf7773190d51f24e6647ee8f3fa0d", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@1.0.5", "scripts": {}, "_shasum": "f7241217430f99dec4c2b473eab92228e874c2ac", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "f7241217430f99dec4c2b473eab92228e874c2ac", "size": 8426, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-1.0.5.tgz", "integrity": "sha512-SIKSrp4+XqcUaNWhwaPJbLFnvSXPsZ4xBdH2WRK0Xo++UzMC4eepYghGAVhVhOwmfq3kqowqJ5w45R3pmYZnuA=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/debug-1.0.5.tgz_1497485664264_0.9653687297832221"}, "directories": {}, "publish_time": 1497485664388, "_hasShrinkwrap": false, "_cnpm_publish_time": 1497485664388, "_cnpmcore_publish_time": "2021-12-13T06:32:21.572Z"}, "2.6.8": {"name": "debug", "version": "2.6.8", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "2.0.0"}, "devDependencies": {"browserify": "9.0.3", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^2.11.15", "eslint": "^3.12.1", "istanbul": "^0.4.5", "karma": "^1.3.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon": "^1.0.5", "mocha": "^3.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "sinon": "^1.17.6", "sinon-chai": "^2.8.0"}, "main": "./src/index.js", "browser": "./src/browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "52e1f21284322f167839e5d3a60f635c8b2dc842", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@2.6.8", "scripts": {}, "_shasum": "e731531ca2ede27d188222427da17821d68ff4fc", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "e731531ca2ede27d188222427da17821d68ff4fc", "size": 16335, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.6.8.tgz", "integrity": "sha512-E22fsyWPt/lr4/UgQLt/pXqerGMDsanhbnmqIS3VAXuDi1v3IpiwXe2oncEIondHSBuPDWRoK/pMjlvi8FuOXQ=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/debug-2.6.8.tgz_1495138020906_0.5965513256378472"}, "directories": {}, "publish_time": 1495138021168, "_hasShrinkwrap": false, "_cnpm_publish_time": 1495138021168, "_cnpmcore_publish_time": "2021-12-13T06:32:22.229Z"}, "2.6.7": {"name": "debug", "version": "2.6.7", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "2.0.0"}, "devDependencies": {"browserify": "9.0.3", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^2.11.15", "eslint": "^3.12.1", "istanbul": "^0.4.5", "karma": "^1.3.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon": "^1.0.5", "mocha": "^3.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "sinon": "^1.17.6", "sinon-chai": "^2.8.0"}, "main": "./src/index.js", "browser": "./src/browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "6bb07f7e1bafa33631d8f36a779f17eb8abf5fea", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@2.6.7", "scripts": {}, "_shasum": "92bad1f6d05bbb6bba22cca88bcd0ec894c2861e", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.5", "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "dist": {"shasum": "92bad1f6d05bbb6bba22cca88bcd0ec894c2861e", "size": 16309, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.6.7.tgz", "integrity": "sha512-7YoSmTDGnXYkFJOvaYXfxcvNE25Y11uZ0X8Mo+pSXjHz/9WUlbCS4O6q+wj7lhubdNQQXxxsSOnlqlDG8SenXQ=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/debug-2.6.7.tgz_1494995629479_0.5576471360400319"}, "directories": {}, "publish_time": 1494995631578, "_hasShrinkwrap": false, "_cnpm_publish_time": 1494995631578, "_cnpmcore_publish_time": "2021-12-13T06:32:22.846Z"}, "2.6.6": {"name": "debug", "version": "2.6.6", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "0.7.3"}, "devDependencies": {"browserify": "9.0.3", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^2.11.15", "eslint": "^3.12.1", "istanbul": "^0.4.5", "karma": "^1.3.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon": "^1.0.5", "mocha": "^3.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "sinon": "^1.17.6", "sinon-chai": "^2.8.0"}, "main": "./src/index.js", "browser": "./src/browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "c90a2b3c6c17300f3c183f0d665092c16388c7ff", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@2.6.6", "scripts": {}, "_shasum": "a9fa6fbe9ca43cf1e79f73b75c0189cbb7d6db5a", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.2", "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "dist": {"shasum": "a9fa6fbe9ca43cf1e79f73b75c0189cbb7d6db5a", "size": 16195, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.6.6.tgz", "integrity": "sha512-ED4LYbzHt4IiPgIVjfUFfsvI5Et133QsXvQuMWw0ygFaPdvE8aeX6nfI+5ZVfyMuP8vZBk9Lv3yn6MPvGnzO9Q=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/debug-2.6.6.tgz_1493336101823_0.35170009173452854"}, "directories": {}, "publish_time": 1493336102119, "_hasShrinkwrap": false, "deprecated": "invalid release", "_cnpm_publish_time": 1493336102119, "_cnpmcore_publish_time": "2021-12-13T06:32:23.518Z"}, "2.6.5": {"name": "debug", "version": "2.6.5", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "0.7.3"}, "devDependencies": {"browserify": "9.0.3", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^2.11.15", "eslint": "^3.12.1", "istanbul": "^0.4.5", "karma": "^1.3.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon": "^1.0.5", "mocha": "^3.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "sinon": "^1.17.6", "sinon-chai": "^2.8.0"}, "main": "./src/index.js", "browser": "./src/browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "14df14c3585bbeb10262f96f5ce61549669709d8", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@2.6.5", "scripts": {}, "_shasum": "7a76247781acd4ef2a85f0fb8abf763cd1af249e", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.2", "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "dist": {"shasum": "7a76247781acd4ef2a85f0fb8abf763cd1af249e", "size": 16172, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.6.5.tgz", "integrity": "sha512-uW/FlKTTFXEY+RPb8gfK/qVsMfYDN0xL28H02x67FZ2RpShWEQ5nQhF0IQpZsbPfwCrwelcB4M68I6bs8ry+xQ=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/debug-2.6.5.tgz_1493309048647_0.05605892837047577"}, "directories": {}, "publish_time": 1493309052415, "_hasShrinkwrap": false, "deprecated": "critical regression for web workers", "_cnpm_publish_time": 1493309052415, "_cnpmcore_publish_time": "2021-12-13T06:32:24.211Z"}, "2.6.4": {"name": "debug", "version": "2.6.4", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "0.7.3"}, "devDependencies": {"browserify": "9.0.3", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^2.11.15", "eslint": "^3.12.1", "istanbul": "^0.4.5", "karma": "^1.3.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon": "^1.0.5", "mocha": "^3.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "sinon": "^1.17.6", "sinon-chai": "^2.8.0"}, "main": "./src/index.js", "browser": "./src/browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "f311b10b7b79efb33f4e23898ae6bbb152e94b16", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@2.6.4", "scripts": {}, "_shasum": "7586a9b3c39741c0282ae33445c4e8ac74734fe0", "_from": ".", "_npmVersion": "4.0.3", "_nodeVersion": "6.9.0", "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "dist": {"shasum": "7586a9b3c39741c0282ae33445c4e8ac74734fe0", "size": 16097, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.6.4.tgz", "integrity": "sha512-jhHoN6DHsKWoWHqswimxiToCuB4ClIbDw4lXDHzJmXGJb0sO3tynCdLe9JHqTXPP5d3oKgp9ynKKsf79765Ilg=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/debug-2.6.4.tgz_1492711686326_0.05656863120384514"}, "directories": {}, "publish_time": 1492711687089, "_hasShrinkwrap": false, "_cnpm_publish_time": 1492711687089, "_cnpmcore_publish_time": "2021-12-13T06:32:24.901Z"}, "2.6.3": {"name": "debug", "version": "2.6.3", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"browserify": "9.0.3", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^2.11.15", "eslint": "^3.12.1", "istanbul": "^0.4.5", "karma": "^1.3.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon": "^1.0.5", "mocha": "^3.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "sinon": "^1.17.6", "sinon-chai": "^2.8.0"}, "main": "./src/index.js", "browser": "./src/browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "9dc30f8378cc12192635cc6a31f0d96bb39be8bb", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@2.6.3", "scripts": {}, "_shasum": "0f7eb8c30965ec08c72accfa0130c8b79984141d", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.2", "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "dist": {"shasum": "0f7eb8c30965ec08c72accfa0130c8b79984141d", "size": 16144, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.6.3.tgz", "integrity": "sha512-9k275CFA9z/NW+7nojeyxyOCFYsc+Dfiq4Sg8CBP5WjzmJT5K1utEepahY7wuWhlsumHgmAqnwAnxPCgOOyAHA=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/debug-2.6.3.tgz_1489463433800_0.9440390267409384"}, "directories": {}, "publish_time": 1489463434042, "_hasShrinkwrap": false, "_cnpm_publish_time": 1489463434042, "_cnpmcore_publish_time": "2021-12-13T06:32:25.732Z"}, "2.6.2": {"name": "debug", "version": "2.6.2", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"browserify": "9.0.3", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^2.11.15", "eslint": "^3.12.1", "istanbul": "^0.4.5", "karma": "^1.3.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon": "^1.0.5", "mocha": "^3.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "sinon": "^1.17.6", "sinon-chai": "^2.8.0"}, "main": "./src/index.js", "browser": "./src/browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "017a9d68568fd24113bd73a477e0221aa969b732", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@2.6.2", "scripts": {}, "_shasum": "dfa96a861ee9b8c2f29349b3bcc41aa599a71e0f", "_from": ".", "_npmVersion": "4.0.3", "_nodeVersion": "6.9.0", "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "dist": {"shasum": "dfa96a861ee9b8c2f29349b3bcc41aa599a71e0f", "size": 16108, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.6.2.tgz", "integrity": "sha512-P3nUmoQmRAgPRGyRWfQxnWcUEwoxznn/4+B1XKgqagoOoC/oQAkkFeOwqQmBgqNxdJwengQ382Tl67gfVLRWPQ=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/debug-2.6.2.tgz_1489175064296_0.5892612014431506"}, "directories": {}, "publish_time": 1489175066365, "_hasShrinkwrap": false, "_cnpm_publish_time": 1489175066365, "_cnpmcore_publish_time": "2021-12-13T06:32:26.586Z"}, "2.6.1": {"name": "debug", "version": "2.6.1", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"browserify": "9.0.3", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^2.11.15", "eslint": "^3.12.1", "istanbul": "^0.4.5", "karma": "^1.3.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon": "^1.0.5", "mocha": "^3.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "sinon": "^1.17.6", "sinon-chai": "^2.8.0"}, "main": "./src/index.js", "browser": "./src/browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "941653e3334e9e3e2cca87cad9bbf6c5cb245215", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@2.6.1", "scripts": {}, "_shasum": "79855090ba2c4e3115cc7d8769491d58f0491351", "_from": ".", "_npmVersion": "4.0.3", "_nodeVersion": "6.9.0", "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "dist": {"shasum": "79855090ba2c4e3115cc7d8769491d58f0491351", "size": 15118, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.6.1.tgz", "integrity": "sha512-BmFi/QgceF1MztznXEqbZXATlMwzrsfWR9Iahbp4j7vTK+Sel84Mt3SZ/btENs22PSm0bw6NOoZOd2fbOczPRQ=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/debug-2.6.1.tgz_1486753226738_0.07569954148493707"}, "directories": {}, "publish_time": 1486753228639, "_hasShrinkwrap": false, "_cnpm_publish_time": 1486753228639, "_cnpmcore_publish_time": "2021-12-13T06:32:27.409Z"}, "2.6.0": {"name": "debug", "version": "2.6.0", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"browserify": "9.0.3", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^2.11.15", "eslint": "^3.12.1", "istanbul": "^0.4.5", "karma": "^1.3.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon": "^1.0.5", "mocha": "^3.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "sinon": "^1.17.6", "sinon-chai": "^2.8.0"}, "main": "./src/index.js", "browser": "./src/browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "ac5ccae70358a2bccc71d288e5f9c656a7678748", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@2.6.0", "scripts": {}, "_shasum": "bc596bcabe7617f11d9fa15361eded5608b8499b", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.2", "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "dist": {"shasum": "bc596bcabe7617f11d9fa15361eded5608b8499b", "size": 14978, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.6.0.tgz", "integrity": "sha512-XMYwiKKX0jdij1QRlpYn0O6gks0hW3iYUsx/h/RLPKouDGVeun2wlMYl29C85KBjnv1vw2vj+yti1ziHsXd7cg=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/debug-2.6.0.tgz_1482990633625_0.042889281176030636"}, "directories": {}, "publish_time": 1482990633866, "_hasShrinkwrap": false, "_cnpm_publish_time": 1482990633866, "_cnpmcore_publish_time": "2021-12-13T06:32:28.399Z"}, "2.5.2": {"name": "debug", "version": "2.5.2", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"browserify": "9.0.3", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^2.11.15", "eslint": "^3.12.1", "istanbul": "^0.4.5", "karma": "^1.3.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon": "^1.0.5", "mocha": "^3.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "sinon": "^1.17.6", "sinon-chai": "^2.8.0"}, "main": "./src/index.js", "browser": "./src/browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "9a18d66282caa2e237d270a0f2cd150362cbf636", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@2.5.2", "scripts": {}, "_shasum": "50c295a53dbf1657146e0c1b21307275e90d49cb", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.2", "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "dist": {"shasum": "50c295a53dbf1657146e0c1b21307275e90d49cb", "size": 15075, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.5.2.tgz", "integrity": "sha512-iHrIBaTK1JzBz5WvitFmZGaTCO/mHiU3NKi8UKjh7rU2JboIbVMZU7pFSCpvc2NxfkrvyaQ5zfdNRJnft/TcoQ=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/debug-2.5.2.tgz_1482719984651_0.9355534017086029"}, "directories": {}, "publish_time": 1482719986961, "_hasShrinkwrap": false, "_cnpm_publish_time": 1482719986961, "_cnpmcore_publish_time": "2021-12-13T06:32:29.111Z"}, "2.5.1": {"name": "debug", "version": "2.5.1", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"browserify": "9.0.3", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^2.11.15", "eslint": "^3.12.1", "istanbul": "^0.4.5", "karma": "^1.3.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon": "^1.0.5", "mocha": "^3.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "sinon": "^1.17.6", "sinon-chai": "^2.8.0"}, "main": "./src/index.js", "browser": "./src/browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "3950daef4c63058e4c2c130b7e90666416b3d5d1", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@2.5.1", "scripts": {}, "_shasum": "9107bb4a506052ec2a02314bc606313ed2b921c1", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.2", "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "dist": {"shasum": "9107bb4a506052ec2a02314bc606313ed2b921c1", "size": 14952, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.5.1.tgz", "integrity": "sha512-kcuXHZHHIrMikExr5bEIkDUOhXrqvMlKrAd7P34OdiDR0K4ZxG0gpT3arvATP8QgZy1bdTun1/d6nOX9TM3z9w=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/debug-2.5.1.tgz_1482298398476_0.08919672318734229"}, "directories": {}, "publish_time": 1482298400503, "_hasShrinkwrap": false, "_cnpm_publish_time": 1482298400503, "_cnpmcore_publish_time": "2021-12-13T06:32:29.808Z"}, "2.5.0": {"name": "debug", "version": "2.5.0", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"browserify": "9.0.3", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^2.11.15", "eslint": "^3.12.1", "istanbul": "^0.4.5", "karma": "^1.3.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon": "^1.0.5", "mocha": "^3.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "sinon": "^1.17.6", "sinon-chai": "^2.8.0"}, "main": "./src/index.js", "browser": "./src/browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "355e327c94e03aaf0c215b32244eeeacd8a298c8", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@2.5.0", "scripts": {}, "_shasum": "94434a384a615a75db92fa734d2c994ec75c7b55", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.2", "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "dist": {"shasum": "94434a384a615a75db92fa734d2c994ec75c7b55", "size": 14796, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.5.0.tgz", "integrity": "sha512-vXPxQlAbKSvGhu2Ys3+DX7XTMkYdoSg32xTyg4sqcF/XNRYLu/B/foqncVlYqGPdtFrc5YWDSSUhoaDN5ogWng=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/debug-2.5.0.tgz_1482296609452_0.780945998383686"}, "directories": {}, "publish_time": 1482296609680, "deprecated": "incompatible with babel-core", "_hasShrinkwrap": false, "_cnpm_publish_time": 1482296609680, "_cnpmcore_publish_time": "2021-12-13T06:32:30.659Z"}, "2.4.5": {"name": "debug", "version": "2.4.5", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"babel": "^6.5.2", "babel-eslint": "^7.1.1", "babel-polyfill": "^6.20.0", "babel-preset-es2015": "^6.18.0", "babel-register": "^6.18.0", "babel-runtime": "^6.20.0", "browserify": "9.0.3", "chai": "^3.5.0", "eslint": "^3.12.1", "eslint-plugin-babel": "^4.0.0", "mocha": "^3.2.0", "sinon": "^1.17.6"}, "main": "./index.js", "browser": "./browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "7e741fcc2f834672796333c97aa15f27f0ea2b5c", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@2.4.5", "scripts": {}, "_shasum": "34c7b12a1ca96674428f41fe92c49b4ce7cd0607", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "7.2.1", "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "dist": {"shasum": "34c7b12a1ca96674428f41fe92c49b4ce7cd0607", "size": 14075, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.4.5.tgz", "integrity": "sha512-dKKhHsZva2Re+65VIn/PUZJaDmIOjgo98JrgrTVNYmINJIxxLMk0aNIUezJ4NTDf53JvGAxB9JpUjKr31icuIw=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/debug-2.4.5.tgz_1482045228863_0.4158463138155639"}, "directories": {}, "publish_time": 1482045229109, "_hasShrinkwrap": false, "_cnpm_publish_time": 1482045229109, "_cnpmcore_publish_time": "2021-12-13T06:32:31.559Z"}, "2.4.4": {"name": "debug", "version": "2.4.4", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"babel": "^6.5.2", "babel-eslint": "^7.1.1", "babel-polyfill": "^6.20.0", "babel-preset-es2015": "^6.18.0", "babel-register": "^6.18.0", "babel-runtime": "^6.20.0", "browserify": "9.0.3", "chai": "^3.5.0", "eslint": "^3.12.1", "eslint-plugin-babel": "^4.0.0", "mocha": "^3.2.0", "sinon": "^1.17.6"}, "main": "./index.js", "browser": "./browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "f1ca2ab80b824c6bb5d58dade36b587bd2b80272", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@2.4.4", "scripts": {}, "_shasum": "c04d17a654e9202464803f096153f70a6f31f4be", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "7.2.1", "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "dist": {"shasum": "c04d17a654e9202464803f096153f70a6f31f4be", "size": 13832, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.4.4.tgz", "integrity": "sha512-pmVI0UTP+XSYRUUJgz09db0M1cAcuUlGQyHxsQh8j1yQ6/zHY21A1JTZskBAIRQbJtxoCC9tq0psn8pcb8gjqA=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/debug-2.4.4.tgz_1481765223703_0.8797183907590806"}, "directories": {}, "publish_time": 1481765225600, "_hasShrinkwrap": false, "_cnpm_publish_time": 1481765225600, "_cnpmcore_publish_time": "2021-12-13T06:32:32.395Z"}, "2.4.3": {"name": "debug", "version": "2.4.3", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"babel": "^6.5.2", "babel-eslint": "^7.1.1", "babel-polyfill": "^6.20.0", "babel-preset-es2015": "^6.18.0", "babel-register": "^6.18.0", "babel-runtime": "^6.20.0", "browserify": "9.0.3", "chai": "^3.5.0", "eslint": "^3.12.1", "eslint-plugin-babel": "^4.0.0", "mocha": "^3.2.0", "sinon": "^1.17.6"}, "main": "./index.js", "browser": "./browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "e1ee4d546a3c366146de708a9c1bf50939f0f425", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@2.4.3", "scripts": {}, "_shasum": "3fe67c5588e724d0f5d9e48c8f08ff69b4b20643", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "7.2.1", "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "dist": {"shasum": "3fe67c5588e724d0f5d9e48c8f08ff69b4b20643", "size": 13707, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.4.3.tgz", "integrity": "sha512-1Iaac9+DapEN6iCcv2af9k1cKIh5LEUpr5w74bMIQViBEGkME1wQTq+pdAfWaX92xQFYct6fBSfcVKnPoZj61g=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/debug-2.4.3.tgz_1481752200538_0.17475450248457491"}, "directories": {}, "publish_time": 1481752200788, "_hasShrinkwrap": false, "_cnpm_publish_time": 1481752200788, "_cnpmcore_publish_time": "2021-12-13T06:32:33.217Z"}, "2.4.2": {"name": "debug", "version": "2.4.2", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"babel": "^6.5.2", "babel-eslint": "^7.1.1", "babel-polyfill": "^6.20.0", "babel-preset-es2015": "^6.18.0", "babel-register": "^6.18.0", "babel-runtime": "^6.20.0", "browserify": "9.0.3", "chai": "^3.5.0", "eslint": "^3.12.1", "eslint-plugin-babel": "^4.0.0", "mocha": "^3.2.0", "sinon": "^1.17.6"}, "main": "./index.js", "browser": "./browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "4c3e80dfaaa499b451619201130c6c2ff07068c2", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@2.4.2", "scripts": {}, "_shasum": "a255d4489f58a2d7b6aaaddb9b7c60828f6ba27a", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "7.2.1", "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "dist": {"shasum": "a255d4489f58a2d7b6aaaddb9b7c60828f6ba27a", "size": 13662, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.4.2.tgz", "integrity": "sha512-ej23QcDyiKBa/ABIamf1KPW5CDF4BfVOkQsQo3ePht3nXTo52Ik6YjJLcpaN8SqMevVCyFzkMXgbLHvFpRUydA=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/debug-2.4.2.tgz_1481744419649_0.1140342962462455"}, "directories": {}, "publish_time": 1481744421566, "_hasShrinkwrap": false, "_cnpm_publish_time": 1481744421566, "_cnpmcore_publish_time": "2021-12-13T06:32:33.976Z"}, "2.4.1": {"name": "debug", "version": "2.4.1", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"browserify": "9.0.3", "mocha": "*"}, "main": "./index.js", "browser": "./browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "803fb05785675e262feb37546858c411b56dc35a", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@2.4.1", "scripts": {}, "_shasum": "ef2532d2753d282045c13c82ce47a09e56b91d53", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "7.2.1", "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "dist": {"shasum": "ef2532d2753d282045c13c82ce47a09e56b91d53", "size": 13090, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.4.1.tgz", "integrity": "sha512-3KDO2nvteNg5RLHQA/ABlmfGfNHjYIfvxFxEHM8BP/yLZe8Ne8Deb0bC02ENfFuKuF5dSuHR2k/WFodW1fleMQ=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/debug-2.4.1.tgz_1481700338914_0.6147811473347247"}, "directories": {}, "publish_time": 1481700340783, "_hasShrinkwrap": false, "_cnpm_publish_time": 1481700340783, "_cnpmcore_publish_time": "2021-12-13T06:32:34.726Z"}, "2.4.0": {"name": "debug", "version": "2.4.0", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"browserify": "9.0.3", "mocha": "*"}, "main": "./index.js", "browser": "./browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "b82d4e6c799198b3d39b05265bf68da9a9aacd41", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@2.4.0", "scripts": {}, "_shasum": "80db5e490a43bff63958e712ba88ba4e4121840f", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "7.2.1", "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "dist": {"shasum": "80db5e490a43bff63958e712ba88ba4e4121840f", "size": 13044, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.4.0.tgz", "integrity": "sha512-qkWsCdZuL12DHM6juOa8etzUxQlW0ybWh23sS6QKo7wWyaPAP62udxguN/gTGO2LgXlRy5vXbEuYWNYUsKNTEA=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/debug-2.4.0.tgz_1481698324494_0.7391216848045588"}, "directories": {}, "publish_time": 1481698326597, "deprecated": "critical bug fixed in 2.4.1", "_hasShrinkwrap": false, "_cnpm_publish_time": 1481698326597, "_cnpmcore_publish_time": "2021-12-13T06:32:35.519Z"}, "2.3.3": {"name": "debug", "version": "2.3.3", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"browserify": "9.0.3", "mocha": "*"}, "main": "./index.js", "browser": "./browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "3ad8df75614cd4306709ad73519fd579971fb8d9", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@2.3.3", "scripts": {}, "_shasum": "40c453e67e6e13c901ddec317af8986cda9eff8c", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "7.0.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "dist": {"shasum": "40c453e67e6e13c901ddec317af8986cda9eff8c", "size": 11791, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.3.3.tgz", "integrity": "sha512-dCHp4G+F11zb+RtEu7BE2U8R32AYmM/4bljQfut8LipH3PdwsVBVGh083MXvtKkB7HSQUzSwiXz53c4mzJvYfw=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/debug-2.3.3.tgz_1479585558307_0.03629564819857478"}, "directories": {}, "publish_time": 1479585558541, "_hasShrinkwrap": false, "_cnpm_publish_time": 1479585558541, "_cnpmcore_publish_time": "2021-12-13T06:32:36.856Z"}, "2.3.2": {"name": "debug", "version": "2.3.2", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"browserify": "9.0.3", "mocha": "*"}, "main": "./index.js", "browser": "./browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "1c6f45840d0dba8cb14f9975b4633bb685fda400", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@2.3.2", "scripts": {}, "_shasum": "94cb466ef7d6d2c7e5245cdd6e4104f2d0d70d30", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "7.0.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "dist": {"shasum": "94cb466ef7d6d2c7e5245cdd6e4104f2d0d70d30", "size": 11671, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.3.2.tgz", "integrity": "sha512-Pi2B3gZGhfmFd8vAAYAI8XTtRrNNkSD3xqwBTjzjNqeVTAcHc8uVMz854KTowlR+Ulzfbz5gu3pudWFGo3LFUQ=="}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/debug-2.3.2.tgz_1478759402178_0.8417916153557599"}, "directories": {}, "publish_time": 1478759404055, "_hasShrinkwrap": false, "_cnpm_publish_time": 1478759404055, "_cnpmcore_publish_time": "2021-12-13T06:32:37.624Z"}, "2.3.1": {"name": "debug", "version": "2.3.1", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"browserify": "9.0.3", "mocha": "*"}, "main": "./index.js", "browser": "./browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "6b45c3a15510ad67a9bc79b1309c1e75c3ab6e0a", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@2.3.1", "scripts": {}, "_shasum": "4b206c092eb4e5b090e429a15d1d89083737ab2b", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.0", "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "dist": {"shasum": "4b206c092eb4e5b090e429a15d1d89083737ab2b", "size": 11557, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.3.1.tgz", "integrity": "sha512-QUobWzjY1Q8Jnv+S4m6po0sGD0PXNXDbHhiouGd5tm66/j2l2vYc2E0GrS2V6rBFVc0QU+w42N9GuFrdnoSpDw=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/debug-2.3.1.tgz_1478736862830_0.16602304461412132"}, "directories": {}, "publish_time": 1478736863056, "_hasShrinkwrap": false, "_cnpm_publish_time": 1478736863056, "_cnpmcore_publish_time": "2021-12-13T06:32:38.443Z"}, "2.3.0": {"name": "debug", "version": "2.3.0", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"browserify": "9.0.3", "mocha": "*"}, "main": "./node.js", "browser": "./browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "8e5a6b3c3d436843ed8c2d9a02315d9d5f9e9442", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug#readme", "_id": "debug@2.3.0", "scripts": {}, "_shasum": "3912dc55d7167fc3af17d2b85c13f93deaedaa43", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.0", "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "dist": {"shasum": "3912dc55d7167fc3af17d2b85c13f93deaedaa43", "size": 20927, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.3.0.tgz", "integrity": "sha512-tb2o33z/sdAvVhiszTuGQHgEww24WFBT+ZzK5jNML+pnF83fDnsE9z2/eoKsxLuhIg9x2VW6IY6TlepmvjELIA=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/debug-2.3.0.tgz_1478540435945_0.11468172585591674"}, "directories": {}, "publish_time": 1478540437812, "_hasShrinkwrap": false, "_cnpm_publish_time": 1478540437812, "_cnpmcore_publish_time": "2021-12-13T06:32:39.386Z"}, "2.2.0": {"name": "debug", "version": "2.2.0", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}], "license": "MIT", "dependencies": {"ms": "0.7.1"}, "devDependencies": {"browserify": "9.0.3", "mocha": "*"}, "main": "./node.js", "browser": "./browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "b38458422b5aa8aa6d286b10dfe427e8a67e2b35", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug", "_id": "debug@2.2.0", "scripts": {}, "_shasum": "f87057e995b1a1f6ae6a4960664137bc56f039da", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.2", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "dist": {"shasum": "f87057e995b1a1f6ae6a4960664137bc56f039da", "size": 10293, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.2.0.tgz", "integrity": "sha512-X0rGvJcskG1c3TgSCPqHJ0XJgwlcvOC7elJ5Y0hYuKBZoVqWpAMfLOeIh2UI/DCQ5ruodIjvsugZtjUYUw2pUw=="}, "directories": {}, "publish_time": 1431242485639, "_hasShrinkwrap": false, "_cnpm_publish_time": 1431242485639, "_cnpmcore_publish_time": "2021-12-13T06:32:40.349Z"}, "2.1.3": {"name": "debug", "version": "2.1.3", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}], "license": "MIT", "dependencies": {"ms": "0.7.0"}, "devDependencies": {"browserify": "9.0.3", "mocha": "*"}, "main": "./node.js", "browser": "./browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "0a8e4b7e0d2d1b55ef4e7422498ca24c677ae63a", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug", "_id": "debug@2.1.3", "scripts": {}, "_shasum": "ce8ab1b5ee8fbee2bfa3b633cab93d366b63418e", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "dist": {"shasum": "ce8ab1b5ee8fbee2bfa3b633cab93d366b63418e", "size": 10155, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.1.3.tgz", "integrity": "sha512-KWau3VQmxO3YwQCjJzMPPusOtI0hx3UGsqnY7RS+QHQjUeawpOVtJvAdeTrI2Ja5DTR8KH3xaEN8c+ADbXJWeg=="}, "publish_time": 1426272621566, "_hasShrinkwrap": false, "_cnpm_publish_time": 1426272621566, "_cnpmcore_publish_time": "2021-12-13T06:32:41.433Z"}, "2.1.2": {"name": "debug", "version": "2.1.2", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}], "license": "MIT", "dependencies": {"ms": "0.7.0"}, "devDependencies": {"browserify": "9.0.3", "mocha": "*"}, "main": "./node.js", "browser": "./browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "ef0b37817e88df724511e648c8c168618e892530", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug", "_id": "debug@2.1.2", "scripts": {}, "_shasum": "d5853ec48011eafd9ec80a5c4733332c1e767a43", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "dist": {"shasum": "d5853ec48011eafd9ec80a5c4733332c1e767a43", "size": 10057, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.1.2.tgz", "integrity": "sha512-1MYjCALu7t4xPIWMoEDkUMpNpLl9WRZYWO2oXqq+zuQkBUokH5YwbKCCoNUBWwrG4uxXp2gwShVh5nxd0dgxYg=="}, "directories": {}, "publish_time": 1425260380274, "_hasShrinkwrap": false, "_cnpm_publish_time": 1425260380274, "_cnpmcore_publish_time": "2021-12-13T06:32:42.409Z"}, "2.1.1": {"name": "debug", "version": "2.1.1", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}], "license": "MIT", "dependencies": {"ms": "0.6.2"}, "devDependencies": {"browserify": "6.1.0", "mocha": "*"}, "main": "./node.js", "browser": "./browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "24cc5c04fc8886fa9afcadea4db439f9a6186ca4", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug", "_id": "debug@2.1.1", "scripts": {}, "_shasum": "e0c548cc607adc22b537540dc3639c4236fdf90c", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "dist": {"shasum": "e0c548cc607adc22b537540dc3639c4236fdf90c", "size": 9767, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.1.1.tgz", "integrity": "sha512-<PERSON>O<PERSON><PERSON><PERSON>+gc7PHrK3cZSYzASfIbTK0bMRs78/Bkjnu+sfSPxEbh/b2qcl27EKRYSK73W6Ju4QfaNHz5fnLXQKEhg=="}, "directories": {}, "publish_time": 1419889861149, "_hasShrinkwrap": false, "_cnpm_publish_time": 1419889861149, "_cnpmcore_publish_time": "2021-12-13T06:32:43.453Z"}, "2.1.0": {"name": "debug", "version": "2.1.0", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}], "license": "MIT", "dependencies": {"ms": "0.6.2"}, "devDependencies": {"browserify": "6.1.0", "mocha": "*"}, "main": "./node.js", "browser": "./browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "953162b4fa8849268d147f4bac91c737baee55bb", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug", "_id": "debug@2.1.0", "scripts": {}, "_shasum": "33ab915659d8c2cc8a41443d94d6ebd37697ed21", "_from": ".", "_npmVersion": "2.1.3", "_nodeVersion": "0.10.32", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "dist": {"shasum": "33ab915659d8c2cc8a41443d94d6ebd37697ed21", "size": 9377, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.1.0.tgz", "integrity": "sha512-mXKNuRulIxh5zRPbJ0znN6gOJljoA1I/pQaZS9QYCwM4LdeInk5sEioHFeLayLJg8YL+FNrwPZbbltDR/HIdGA=="}, "directories": {}, "publish_time": 1413410321028, "_hasShrinkwrap": false, "_cnpm_publish_time": 1413410321028, "_cnpmcore_publish_time": "2021-12-13T06:32:44.399Z"}, "2.0.0": {"name": "debug", "version": "2.0.0", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}], "dependencies": {"ms": "0.6.2"}, "devDependencies": {"browserify": "5.11.0", "mocha": "*"}, "main": "./node.js", "browser": "./browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "c61ae82bde19c6fdedfc6684817ff7eb541ff029", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug", "_id": "debug@2.0.0", "scripts": {}, "_shasum": "89bd9df6732b51256bc6705342bba02ed12131ef", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "dist": {"shasum": "89bd9df6732b51256bc6705342bba02ed12131ef", "size": 8469, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-2.0.0.tgz", "integrity": "sha512-jRxFR0Fb657ikmm6IjHY32v/Nqp9Ndcx4LBISXPfpguNaHh5JJnb+x37qalKPTu4fxMFnVBIyEGi72mmvl0BCw=="}, "directories": {}, "publish_time": 1409556103687, "_hasShrinkwrap": false, "_cnpm_publish_time": 1409556103687, "_cnpmcore_publish_time": "2021-12-13T06:32:45.349Z"}, "1.0.4": {"name": "debug", "version": "1.0.4", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}], "dependencies": {"ms": "0.6.2"}, "devDependencies": {"browserify": "4.1.6", "mocha": "*"}, "main": "./node.js", "browser": "./browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "abc10a5912f79d251752d18350e269fe0b0fbbf9", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug", "_id": "debug@1.0.4", "scripts": {}, "_shasum": "5b9c256bd54b6ec02283176fa8a0ede6d154cbf8", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "dist": {"shasum": "5b9c256bd54b6ec02283176fa8a0ede6d154cbf8", "size": 8420, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-1.0.4.tgz", "integrity": "sha512-plA8d2GHafT7kXzMDs5r7NSfYP7IKHdO8rZPVAqI33Eum7Vq/Ef/ETXm6NncF/RMif4fzI0RetSArZ6PMIxP0g=="}, "directories": {}, "publish_time": 1405466168284, "_hasShrinkwrap": false, "_cnpm_publish_time": 1405466168284, "_cnpmcore_publish_time": "2021-12-13T06:32:46.614Z"}, "1.0.3": {"name": "debug", "version": "1.0.3", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}], "dependencies": {"ms": "0.6.2"}, "devDependencies": {"browserify": "4.1.6", "mocha": "*"}, "main": "./node.js", "browser": "./browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "gitHead": "93c759961d53ad7f06a1892a8dd0bf4be4a7b9df", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug", "_id": "debug@1.0.3", "scripts": {}, "_shasum": "fc8c6b2d6002804b4081c0208e0f6460ba1fa3e4", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "dist": {"shasum": "fc8c6b2d6002804b4081c0208e0f6460ba1fa3e4", "size": 7982, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-1.0.3.tgz", "integrity": "sha512-MltK7Ykj/udtD728gD/RrONStwVnDpBNIP1h+CBcnwnJdHqHxfWHI1E8XLootUl7NOPAYTCCXlb8/Qmy7WyB1w=="}, "directories": {}, "publish_time": 1404922607588, "_hasShrinkwrap": false, "_cnpm_publish_time": 1404922607588, "_cnpmcore_publish_time": "2021-12-13T06:32:47.723Z"}, "1.0.2": {"name": "debug", "version": "1.0.2", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}], "dependencies": {"ms": "0.6.2"}, "devDependencies": {"browserify": "4.1.6", "mocha": "*"}, "main": "./node.js", "browser": "./browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug", "_id": "debug@1.0.2", "_shasum": "3849591c10cce648476c3c7c2e2e3416db5963c4", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "dist": {"shasum": "3849591c10cce648476c3c7c2e2e3416db5963c4", "size": 7957, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-1.0.2.tgz", "integrity": "sha512-T9bufXIzQvCa4VrTIpLvvwdLhH+wuBtvIJJA3xgzVcaVETGmTIWMfEXQEd1K4p8BaRmQJPn6MPut38H7YQ+iIA=="}, "directories": {}, "publish_time": 1402447847529, "_hasShrinkwrap": false, "_cnpm_publish_time": 1402447847529, "_cnpmcore_publish_time": "2021-12-13T06:32:48.709Z"}, "1.0.1": {"name": "debug", "version": "1.0.1", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}], "dependencies": {"ms": "0.6.2"}, "devDependencies": {"browserify": "4.1.6", "mocha": "*"}, "main": "./node.js", "browser": "./browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug", "_id": "debug@1.0.1", "_shasum": "3c03d40462f0be20468e4f77dd3f2bf7a722cfb7", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "dist": {"shasum": "3c03d40462f0be20468e4f77dd3f2bf7a722cfb7", "size": 7514, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-1.0.1.tgz", "integrity": "sha512-Se3uhnI9TBNE+wy7bD2kQHvJR5oY6ARosn0UWOHZkcq5TKG7GYzThuluyJ+UbjAztbtm/XlBrvQtnFx+Ll/pxg=="}, "directories": {}, "publish_time": 1402086189807, "_hasShrinkwrap": false, "_cnpm_publish_time": 1402086189807, "_cnpmcore_publish_time": "2021-12-13T06:32:49.773Z"}, "1.0.0": {"name": "debug", "version": "1.0.0", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {"ms": "0.6.2"}, "devDependencies": {"browserify": "4.1.6", "mocha": "*"}, "main": "./node.js", "browser": "./browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug", "_id": "debug@1.0.0", "_shasum": "553678b67494cacc2d5330c24dfb2f275b1ceb5a", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "dist": {"shasum": "553678b67494cacc2d5330c24dfb2f275b1ceb5a", "size": 7267, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-1.0.0.tgz", "integrity": "sha512-90ovcUGlapDDKhEeeAlmT/+/R+BECtyGz+l3dYyl05HOaMj/s03bQpOScs49ouWNnpcDQVeBk28h/vuDnbvdxw=="}, "directories": {}, "publish_time": 1401940556207, "_hasShrinkwrap": false, "_cnpm_publish_time": 1401940556207, "_cnpmcore_publish_time": "2021-12-13T06:32:50.927Z"}, "0.8.1": {"name": "debug", "version": "0.8.1", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"mocha": "*"}, "main": "lib/debug.js", "browser": "./debug.js", "engines": {"node": "*"}, "files": ["lib/debug.js", "debug.js"], "component": {"scripts": {"debug/index.js": "debug.js"}}, "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug", "_id": "debug@0.8.1", "dist": {"shasum": "20ff4d26f5e422cb68a1bacbbb61039ad8c1c130", "size": 4082, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-0.8.1.tgz", "integrity": "sha512-HlXEJm99YsRjLJ8xmuz0Lq8YUwrv7hAJkTEr6/Em3sUlSUNl0UdFA+1SrY4fnykeq1FVkUEUtwRGHs9VvlYbGA=="}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1397527485652, "_hasShrinkwrap": false, "_cnpm_publish_time": 1397527485652, "_cnpmcore_publish_time": "2021-12-13T06:32:51.974Z"}, "0.8.0": {"name": "debug", "version": "0.8.0", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"mocha": "*"}, "main": "lib/debug.js", "browser": "./debug.js", "engines": {"node": "*"}, "files": ["lib/debug.js", "debug.js"], "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug", "_id": "debug@0.8.0", "dist": {"shasum": "0541ea91f0e503fdf0c5eed418a32550234967f0", "size": 4057, "noattachment": false, "tarball": "https://registry.npmmirror.com/debug/-/debug-0.8.0.tgz", "integrity": "sha512-jR+JRuwlhTwNPpLU6/JhiMydD6+GnL/33WE8LlmnBUqPHXkEpG2iNargYBO/Wxx7wXn7oxU6XwYIZyH4YuSW9Q=="}, "_from": ".", "_npmVersion": "1.3.15", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1396195217026, "_hasShrinkwrap": false, "_cnpm_publish_time": 1396195217026, "_cnpmcore_publish_time": "2021-12-13T06:32:53.020Z"}, "0.7.4": {"name": "debug", "version": "0.7.4", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"mocha": "*"}, "main": "lib/debug.js", "browser": "./debug.js", "engines": {"node": "*"}, "files": ["lib/debug.js", "debug.js", "index.js"], "component": {"scripts": {"debug/index.js": "index.js", "debug/debug.js": "debug.js"}}, "readmeFilename": "Readme.md", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "homepage": "https://github.com/visionmedia/debug", "_id": "debug@0.7.4", "dist": {"tarball": "https://registry.npmmirror.com/debug/-/debug-0.7.4.tgz", "shasum": "06e1ea8082c2cb14e39806e22e2f6f757f92af39", "size": 4211, "noattachment": false, "integrity": "sha512-EohAb3+DSHSGx8carOSKJe8G0ayV5/i609OD0J2orCkuyae7SyZSz2aoLmQF2s0Pj5gITDebwPH7GFBlqOUQ1Q=="}, "_from": ".", "_npmVersion": "1.3.13", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1384373317779, "_hasShrinkwrap": false, "_cnpm_publish_time": 1384373317779, "_cnpmcore_publish_time": "2021-12-13T06:32:54.002Z"}, "0.7.3": {"name": "debug", "version": "0.7.3", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"mocha": "*"}, "main": "lib/debug.js", "browserify": "debug.js", "browser": "./debug.js", "engines": {"node": "*"}, "files": ["lib/debug.js", "debug.js", "index.js"], "component": {"scripts": {"debug/index.js": "index.js", "debug/debug.js": "debug.js"}}, "readmeFilename": "Readme.md", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "_id": "debug@0.7.3", "dist": {"tarball": "https://registry.npmmirror.com/debug/-/debug-0.7.3.tgz", "shasum": "ba7ae369799066a28d234fb8dad6f05837839da8", "size": 4211, "noattachment": false, "integrity": "sha512-kmMlLkXbeTeQlihhfXraOJMDImxDpFyo36vGq4LBepdq5+TwLwnupy1hI0ykK1A52WfDgmO4XJ0iYIiEkmSquA=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1383180686848, "_hasShrinkwrap": false, "_cnpm_publish_time": 1383180686848, "_cnpmcore_publish_time": "2021-12-13T06:32:55.270Z"}, "0.7.2": {"name": "debug", "version": "0.7.2", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"mocha": "*"}, "main": "lib/debug.js", "browserify": "debug.js", "engines": {"node": "*"}, "component": {"scripts": {"debug/index.js": "index.js", "debug/debug.js": "debug.js"}}, "readmeFilename": "Readme.md", "_id": "debug@0.7.2", "dist": {"tarball": "https://registry.npmmirror.com/debug/-/debug-0.7.2.tgz", "shasum": "056692c86670977f115de82917918b8e8b9a10f0", "size": 5148, "noattachment": false, "integrity": "sha512-Ch0X6QrHzrNiWwLsBJj9KgXL5IK67pfDyTsXXVPyrdObVyKuj/rPdCtZg761nHZM1GQ7wW/o9cAZf5JeTN/vRg=="}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1360194019513, "_hasShrinkwrap": false, "_cnpm_publish_time": 1360194019513, "_cnpmcore_publish_time": "2021-12-13T06:32:56.383Z"}, "0.7.1": {"name": "debug", "version": "0.7.1", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"mocha": "*"}, "main": "lib/debug.js", "browserify": "debug.js", "engines": {"node": "*"}, "readmeFilename": "Readme.md", "_id": "debug@0.7.1", "dist": {"tarball": "https://registry.npmmirror.com/debug/-/debug-0.7.1.tgz", "shasum": "d2253d37f2da6618f95c353a55fe0ab28c1c1e96", "size": 4988, "noattachment": false, "integrity": "sha512-Zuj7MDrrvChh4QJt1x03j3PAJQcHi9iGSG15E59H/I+I3AtDOsZh+I6NG2KpddCBy/zQlBuoehXvBtywwKWe1A=="}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1360187623587, "_hasShrinkwrap": false, "_cnpm_publish_time": 1360187623587, "_cnpmcore_publish_time": "2021-12-13T06:32:57.429Z"}, "0.7.0": {"name": "debug", "version": "0.7.0", "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"mocha": "*"}, "main": "index", "browserify": "debug.component.js", "engines": {"node": "*"}, "component": {"scripts": {"debug": "debug.component.js"}}, "_id": "debug@0.7.0", "dist": {"tarball": "https://registry.npmmirror.com/debug/-/debug-0.7.0.tgz", "shasum": "f5be05ec0434c992d79940e50b2695cfb2e01b08", "size": 5225, "noattachment": false, "integrity": "sha512-UWZnvGiX9tQgtrsA+mhGLKnUFvr1moempl9IvqQKyFnEgN0T4kpCE+KJcqTLcVxQjRVRnLF3VSE1Hchki5N98g=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1341861119699, "_hasShrinkwrap": false, "_cnpm_publish_time": 1341861119699, "_cnpmcore_publish_time": "2021-12-13T06:32:58.690Z"}, "0.6.0": {"name": "debug", "version": "0.6.0", "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"mocha": "*"}, "main": "index", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@0.6.0", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.9", "_nodeVersion": "v0.6.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/debug/-/debug-0.6.0.tgz", "shasum": "ce9d5d025d5294b3f0748a494bebaf3c9fd8734f", "size": 5122, "noattachment": false, "integrity": "sha512-2vIZf67+gMicLu8McscD1NNhMWbiTSJkhlByoTA1Gw54zOb/9IlxylYG+Kr9z1X2wZTHh1AMSp+YiMjYtLkVUA=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1331935131296, "_hasShrinkwrap": false, "_cnpm_publish_time": 1331935131296, "_cnpmcore_publish_time": "2021-12-13T06:32:59.909Z"}, "0.5.0": {"name": "debug", "version": "0.5.0", "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"mocha": "*"}, "main": "index", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@0.5.0", "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/debug/-/debug-0.5.0.tgz", "shasum": "9d48c946fb7d7d59807ffe07822f515fd76d7a9e", "size": 10240, "noattachment": false, "integrity": "sha512-5xwa00fC8jw+qiSnXWrjzqtNyTwDIC+N9BPQHKaj0rzPclk4HJ//H1aAta1+YVjc1+z3yj3giHI93fr+4vvOBQ=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1328230604457, "_hasShrinkwrap": false, "_cnpm_publish_time": 1328230604457, "_cnpmcore_publish_time": "2021-12-13T06:33:01.065Z"}, "0.4.1": {"name": "debug", "version": "0.4.1", "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"mocha": "*"}, "main": "index", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@0.4.1", "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/debug/-/debug-0.4.1.tgz", "shasum": "33a47f028daa312d885be4db8649fa1b4280125d", "size": 10240, "noattachment": false, "integrity": "sha512-vvcfF/pEWD7QnwQZ7nX3T6nTbJ+tYdMK3vHi8DCivuw9se3hoHo1DCYoSTxXXpOBAH1tKi3prJ3e7V3Jw5Ckzg=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1328212484139, "_hasShrinkwrap": false, "_cnpm_publish_time": 1328212484139, "_cnpmcore_publish_time": "2021-12-13T06:33:02.279Z"}, "0.4.0": {"name": "debug", "version": "0.4.0", "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"mocha": "*"}, "main": "index", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@0.4.0", "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/debug/-/debug-0.4.0.tgz", "shasum": "c7e8c63eb391820133869d3cc43aa9992fea288f", "size": 10240, "noattachment": false, "integrity": "sha512-fN7BwVwD6luMIhe0x3sZpUBA3kmi7Y1WrYkuBSM9y7SNVbTyPJftbF0S/f02vTl9jSPHw5G3DKhREKtXSKT6DA=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1328131247417, "_hasShrinkwrap": false, "_cnpm_publish_time": 1328131247417, "_cnpmcore_publish_time": "2021-12-13T06:33:03.388Z"}, "0.3.0": {"name": "debug", "version": "0.3.0", "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"mocha": "*"}, "main": "index", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@0.3.0", "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/debug/-/debug-0.3.0.tgz", "shasum": "7818fac891b38d81bb7dd97e3d969992e654e835", "size": 10240, "noattachment": false, "integrity": "sha512-yFnB6fZDgWBNalpbJusPhWBxamQIyLCm2czSKFphn1Efrbgsoh7FNfVpdFBee0ZVMO90Uq32fRn/8LNu00n1PQ=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1327624632739, "_hasShrinkwrap": false, "_cnpm_publish_time": 1327624632739, "_cnpmcore_publish_time": "2021-12-13T06:33:04.774Z"}, "0.2.0": {"name": "debug", "version": "0.2.0", "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"mocha": "*"}, "main": "index", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@0.2.0", "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.6.7", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/debug/-/debug-0.2.0.tgz", "shasum": "b6bbca5a6b41f6d3f3ba6604e2737a9ea3329e7f", "size": 10240, "noattachment": false, "integrity": "sha512-GHNutIi2PtfsfkaFV12nt2iG2KI5GDsHcv/KWanLqQxWj1s6hrC2Ihyqr9wTn/7AscXbPquJ1C/sEbhJhAxRlg=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1327256801329, "_hasShrinkwrap": false, "_cnpm_publish_time": 1327256801329, "_cnpmcore_publish_time": "2021-12-13T06:33:06.012Z"}, "0.1.0": {"name": "debug", "version": "0.1.0", "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"mocha": "*"}, "main": "index", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@0.1.0", "_engineSupported": true, "_npmVersion": "1.0.104", "_nodeVersion": "v0.6.4", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/debug/-/debug-0.1.0.tgz", "shasum": "3026f197b98b823cb51209f3758eb1498a66442c", "size": 10240, "noattachment": false, "integrity": "sha512-NLrWdwRTCljTP6KSk3Lg5EL0QKKt9nqfM12NSPXAlvxtCq+w5OEnHkj+8qDgaKFZ3DX3Bp20MwmSBY1lGkkyLA=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1322867816971, "_hasShrinkwrap": false, "_cnpm_publish_time": 1322867816971, "_cnpmcore_publish_time": "2021-12-13T06:33:07.192Z"}, "0.0.1": {"name": "debug", "version": "0.0.1", "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"mocha": "*"}, "main": "index", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@0.0.1", "_engineSupported": true, "_npmVersion": "1.0.104", "_nodeVersion": "v0.6.3", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/debug/-/debug-0.0.1.tgz", "shasum": "0faa51ad6dec7587159b532cdf18d74261376417", "size": 10240, "noattachment": false, "integrity": "sha512-wm1jCOnbiFSvX8u+NMV+mD6CSOFgGlAPTYw3aoJgDoh2OBSIwMuz0ayedqbNhU3irew6bDBDA+9ia313ZPAEZA=="}, "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1322529085405, "_hasShrinkwrap": false, "_cnpm_publish_time": 1322529085405, "_cnpmcore_publish_time": "2021-12-13T06:33:08.362Z"}, "4.3.4": {"name": "debug", "version": "4.3.4", "repository": {"type": "git", "url": "git://github.com/debug-js/debug.git"}, "description": "Lightweight debugging utility for Node.js and the browser", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser && npm run lint", "test:node": "istanbul cover _mocha -- test.js", "test:browser": "karma start --single-run", "test:coverage": "cat ./coverage/lcov.info | coveralls"}, "dependencies": {"ms": "2.1.2"}, "devDependencies": {"brfs": "^2.0.1", "browserify": "^16.2.3", "coveralls": "^3.0.2", "istanbul": "^0.4.5", "karma": "^3.1.4", "karma-browserify": "^6.0.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "xo": "^0.23.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}, "main": "./src/index.js", "browser": "./src/browser.js", "engines": {"node": ">=6.0"}, "gitHead": "da66c86c5fd71ef570f36b5b1edfa4472149f1bc", "bugs": {"url": "https://github.com/debug-js/debug/issues"}, "homepage": "https://github.com/debug-js/debug#readme", "_id": "debug@4.3.4", "_nodeVersion": "17.3.1", "_npmVersion": "8.3.0", "dist": {"integrity": "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==", "shasum": "1319f6579357f2338d3337d2cdd4914bb5dcc865", "tarball": "https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz", "fileCount": 7, "unpackedSize": 42352, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMznnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpAgw/+NHpSQ+LWMx6GQJ9qSYwtaT3YjCidhAMmCsTrWdsCd2GjfWmr\r\nZMbQmyd8ACK1L6dxCegWa8bOms8vCiQCFukzFRYxeXSYkVqPueHtfXbEDVwk\r\nC8uVdifmtXYp7bamROLrRzKilsVAshn19poz28DRH48pbaNh7yVnvT89DJji\r\nMx0u7xOZHJ7dhniwenivY1zd+gnvAgoXAioWJg1echYSfVCzfrex1KPzwc0I\r\n1Eo+qtSCgotylm37OYVPLoY/yDSeIydL3F56XtzVXpukZ1G3fKD6o9zSfrqt\r\nbrujSxRo0xys4J5kbj5ONaiwLhUpTxh7UdOLhrdZBM3/D29Hz9Do076WngmQ\r\nUoCg2Qh3b05eOvVSuU1KLPg25NDM3wXNWctFyoGFBvbor5ITWZY1W4IqcDvC\r\nxpYYOlJ75evHmouPikVJXEd67qSzs0Lb7jAhrewoBY7YH8Imljk4mzt++cJ6\r\nm69zCwbiQLULYUieLcON/Aplb//9pvQUycP2604gcdgf45NyPx08vjMmnWCt\r\nv0szJjclPl/UQr9w4yg9Tf4YZtgcNfEOnUVKZ9TH/w8B9sOWEg0Qx/diaroJ\r\nN0KZVXaIWvVMKUioK40VYUILPOjoAx9KIT0m6KuMGx+eZtPN1PXO64cpWxlB\r\nzUJ3TguaRW12vECh6zyfkyUXh0C4ADFDYp4=\r\n=9E9I\r\n-----END PGP SIGNATURE-----\r\n", "size": 13252}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/debug_4.3.4_1647524327516_0.6784624450052874"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-17T13:41:25.682Z"}, "4.3.5": {"name": "debug", "version": "4.3.5", "keywords": ["debug", "log", "debugger"], "author": {"url": "https://github.com/qix-", "name": "<PERSON>"}, "license": "MIT", "_id": "debug@4.3.5", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/debug-js/debug#readme", "bugs": {"url": "https://github.com/debug-js/debug/issues"}, "dist": {"shasum": "e83444eceb9fedd4a1da56d671ae2446a01a6e1e", "tarball": "https://registry.npmmirror.com/debug/-/debug-4.3.5.tgz", "fileCount": 7, "integrity": "sha512-pt0bNEmneDIvdL1Xsd9oDQ/wrQRkXDT4AUWlNZNPKvW5x/jyO9VFXkJUP07vQ2upmw5PlaITaPKc31jK13V+jg==", "signatures": [{"sig": "MEUCIQD2eFpf3p60i2+rFrwBiP8ctewWXYfqZxZvMEU/XyX/xAIgJXRFOrWWj+tLfHrd400HHT/bz+yV4Edh8cAmQFDP3Jw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42430, "size": 13326}, "main": "./src/index.js", "browser": "./src/browser.js", "engines": {"node": ">=6.0"}, "gitHead": "5464bdddbc6f91b2aef2ad20650d3a6cfd9fcc3a", "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser && npm run lint", "test:node": "istanbul cover _mocha -- test.js test.node.js", "test:browser": "karma start --single-run", "test:coverage": "cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/debug-js/debug.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Lightweight debugging utility for Node.js and the browser", "directories": {}, "_nodeVersion": "21.3.0", "dependencies": {"ms": "2.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "brfs": "^2.0.1", "karma": "^3.1.4", "mocha": "^5.2.0", "sinon": "^14.0.0", "istanbul": "^0.4.5", "coveralls": "^3.0.2", "browserify": "^16.2.3", "karma-mocha": "^1.3.0", "karma-browserify": "^6.0.0", "mocha-lcov-reporter": "^1.2.0", "karma-chrome-launcher": "^2.2.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/debug_4.3.5_1717155615743_0.8263391721249294", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-05-31T11:40:15.895Z", "publish_time": 1717155615895, "_source_registry_name": "default"}, "4.3.6": {"name": "debug", "version": "4.3.6", "repository": {"type": "git", "url": "git://github.com/debug-js/debug.git"}, "description": "Lightweight debugging utility for Node.js and the browser", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON>", "url": "https://github.com/qix-"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser && npm run lint", "test:node": "istanbul cover _mocha -- test.js test.node.js", "test:browser": "karma start --single-run", "test:coverage": "cat ./coverage/lcov.info | coveralls"}, "dependencies": {"ms": "2.1.2"}, "devDependencies": {"brfs": "^2.0.1", "browserify": "^16.2.3", "coveralls": "^3.0.2", "istanbul": "^0.4.5", "karma": "^3.1.4", "karma-browserify": "^6.0.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "sinon": "^14.0.0", "xo": "^0.23.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}, "main": "./src/index.js", "browser": "./src/browser.js", "engines": {"node": ">=6.0"}, "_id": "debug@4.3.6", "gitHead": "c33b464a797d6cf8c72b8d84d87e02b2822494c9", "bugs": {"url": "https://github.com/debug-js/debug/issues"}, "homepage": "https://github.com/debug-js/debug#readme", "_nodeVersion": "20.5.1", "_npmVersion": "9.8.0", "dist": {"integrity": "sha512-O/09Bd4Z1fBrU4VzkhFqVgpPzaGbw6Sm9FEkBT1A/YBXQFGuuSxa1dN2nxgxS34JmKXqYx8CZAwEVoJFImUXIg==", "shasum": "2ab2c38fbaffebf8aa95fdfe6d88438c7a13c52b", "tarball": "https://registry.npmmirror.com/debug/-/debug-4.3.6.tgz", "fileCount": 7, "unpackedSize": 42440, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICM4wOOFMyB8VKp5QzbkLY6P1j0pTYU1aD3/Y1GyMqLJAiEA3cJIP+BsJ2L4IL9vHlNz7sFwmwazGOpJnO/vx2qCI0g="}], "size": 13279}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/debug_4.3.6_1722072204675_0.7142707761781839"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-27T09:23:24.842Z", "publish_time": 1722072204842, "_source_registry_name": "default"}, "4.3.7": {"name": "debug", "version": "4.3.7", "repository": {"type": "git", "url": "git://github.com/debug-js/debug.git"}, "description": "Lightweight debugging utility for Node.js and the browser", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON>", "url": "https://github.com/qix-"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser && npm run lint", "test:node": "istanbul cover _mocha -- test.js test.node.js", "test:browser": "karma start --single-run", "test:coverage": "cat ./coverage/lcov.info | coveralls"}, "dependencies": {"ms": "^2.1.3"}, "devDependencies": {"brfs": "^2.0.1", "browserify": "^16.2.3", "coveralls": "^3.0.2", "istanbul": "^0.4.5", "karma": "^3.1.4", "karma-browserify": "^6.0.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "sinon": "^14.0.0", "xo": "^0.23.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}, "main": "./src/index.js", "browser": "./src/browser.js", "engines": {"node": ">=6.0"}, "_id": "debug@4.3.7", "gitHead": "bc60914816e5e45a5fff1cd638410438fc317521", "bugs": {"url": "https://github.com/debug-js/debug/issues"}, "homepage": "https://github.com/debug-js/debug#readme", "_nodeVersion": "20.5.1", "_npmVersion": "9.8.0", "dist": {"integrity": "sha512-Er2nc/H7RrMXZBFCEim6TCmMk02Z8vLC2Rbi1KEBggpo0fS6l0S1nnapwmIi3yW/+GOJap1Krg4w0Hg80oCqgQ==", "shasum": "87945b4151a011d76d95a198d7111c865c360a52", "tarball": "https://registry.npmmirror.com/debug/-/debug-4.3.7.tgz", "fileCount": 7, "unpackedSize": 42060, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCMQx9T+oP2ZGPLho8hL50/ibLz9EuJF2iS6VVaDwS96QIgVC4PdM67/9mYyo/GRZXILo/xg+gUPfdsrHsM5IOOBsY="}], "size": 13173}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/debug_4.3.7_1725583977461_0.3189367575089168"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-06T00:52:57.702Z", "publish_time": 1725583977702, "_source_registry_name": "default"}, "4.4.0": {"name": "debug", "version": "4.4.0", "repository": {"type": "git", "url": "git://github.com/debug-js/debug.git"}, "description": "Lightweight debugging utility for Node.js and the browser", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON>", "url": "https://github.com/qix-"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser && npm run lint", "test:node": "istanbul cover _mocha -- test.js test.node.js", "test:browser": "karma start --single-run", "test:coverage": "cat ./coverage/lcov.info | coveralls"}, "dependencies": {"ms": "^2.1.3"}, "devDependencies": {"brfs": "^2.0.1", "browserify": "^16.2.3", "coveralls": "^3.0.2", "istanbul": "^0.4.5", "karma": "^3.1.4", "karma-browserify": "^6.0.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "sinon": "^14.0.0", "xo": "^0.23.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}, "main": "./src/index.js", "browser": "./src/browser.js", "engines": {"node": ">=6.0"}, "xo": {"rules": {"import/extensions": "off"}}, "_id": "debug@4.4.0", "gitHead": "7e3814cc603bf64fdd69e714e0cf5611ec31f43b", "bugs": {"url": "https://github.com/debug-js/debug/issues"}, "homepage": "https://github.com/debug-js/debug#readme", "_nodeVersion": "20.5.1", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==", "shasum": "2b3f2aea2ffeb776477460267377dc8710faba8a", "tarball": "https://registry.npmmirror.com/debug/-/debug-4.4.0.tgz", "fileCount": 7, "unpackedSize": 42798, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAOUmdup8dsL7tpvMHDHweICku/LOxrG65yY7CZtqlhJAiAfx4wuksiMJlmkVdO/tCvfps4NFVnFhyH7k/lD64R/Sw=="}], "size": 13390}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/debug_4.4.0_1733488366116_0.7027776010484381"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-06T12:32:46.280Z", "publish_time": 1733488366280, "_source_registry_name": "default"}, "4.4.1": {"name": "debug", "version": "4.4.1", "repository": {"type": "git", "url": "git://github.com/debug-js/debug.git"}, "description": "Lightweight debugging utility for Node.js and the browser", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON>", "url": "https://github.com/qix-"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser && npm run lint", "test:node": "mocha test.js test.node.js", "test:browser": "karma start --single-run", "test:coverage": "cat ./coverage/lcov.info | coveralls"}, "dependencies": {"ms": "^2.1.3"}, "devDependencies": {"brfs": "^2.0.1", "browserify": "^16.2.3", "coveralls": "^3.0.2", "karma": "^3.1.4", "karma-browserify": "^6.0.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "sinon": "^14.0.0", "xo": "^0.23.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}, "main": "./src/index.js", "browser": "./src/browser.js", "engines": {"node": ">=6.0"}, "xo": {"rules": {"import/extensions": "off"}}, "_id": "debug@4.4.1", "gitHead": "33330fa8616b9b33f29f7674747be77266878ba6", "bugs": {"url": "https://github.com/debug-js/debug/issues"}, "homepage": "https://github.com/debug-js/debug#readme", "_nodeVersion": "22.15.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==", "shasum": "e5a8bc6cbc4c6cd3e64308b0693a3d4fa550189b", "tarball": "https://registry.npmmirror.com/debug/-/debug-4.4.1.tgz", "fileCount": 7, "unpackedSize": 42793, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIFYe34T3MTMz7GUYtvascO8kejy1IQelEEiXvtCY7ZJzAiEA65tNrHJkzI1TkrE1u4sq8InCFcWUG4iblPW3GCW5Ni8="}], "size": 13448}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/debug_4.4.1_1747169796263_0.8130823384432215"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-13T20:56:36.431Z", "publish_time": 1747169796431, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/debug-js/debug/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/debug-js/debug#readme", "keywords": ["debug", "log", "debugger"], "repository": {"type": "git", "url": "git://github.com/debug-js/debug.git"}, "_source_registry_name": "default"}