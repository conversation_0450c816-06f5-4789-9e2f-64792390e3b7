{"_attachments": {}, "_id": "websocket", "_rev": "2258-61f148a6963ca28f5ee437e3", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/theturtle32"}, "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "dist-tags": {"latest": "1.0.35"}, "license": "Apache-2.0", "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "name": "websocket", "readme": "WebSocket Client & Server Implementation for Node\n=================================================\n\n[![npm version](https://badge.fury.io/js/websocket.svg)](http://badge.fury.io/js/websocket)\n\n[![NPM Downloads](https://img.shields.io/npm/dm/websocket.svg)](https://www.npmjs.com/package/websocket)\n\n[ ![Codeship Status for theturtle32/WebSocket-Node](https://codeship.com/projects/70458270-8ee7-0132-7756-0a0cf4fe8e66/status?branch=master)](https://codeship.com/projects/61106)\n\nOverview\n--------\nThis is a (mostly) pure JavaScript implementation of the WebSocket protocol versions 8 and 13 for Node.  There are some example client and server applications that implement various interoperability testing protocols in the \"test/scripts\" folder.\n\n\nDocumentation\n=============\n\n[You can read the full API documentation in the docs folder.](docs/index.md)\n\n\nChangelog\n---------\n\n***Current Version: 1.0.35*** - Release 2024-05-12\n\n* [Updated](https://github.com/theturtle32/WebSocket-Node/pull/455) from [vulnerable version](https://security.snyk.io/vuln/SNYK-JS-ES5EXT-6095076) of es5-ext to a newer version that has been patched. Thanks, [@Tringapps-Dharshan](https://github.com/Tringapps-Dharshan)\n\n[View the full changelog](CHANGELOG.md)\n\nBrowser Support\n---------------\n\nAll current browsers are fully* supported.\n\n* Firefox 7-9 (Old) (Protocol Version 8)\n* Firefox 10+ (Protocol Version 13)\n* Chrome 14,15 (Old) (Protocol Version 8)\n* Chrome 16+ (Protocol Version 13)\n* Internet Explorer 10+ (Protocol Version 13)\n* Safari 6+ (Protocol Version 13)\n\n(Not all W3C WebSocket features are supported by browsers. More info in the [Full API documentation](docs/index.md))\n\nBenchmarks\n----------\nThere are some basic benchmarking sections in the Autobahn test suite.  I've put up a [benchmark page](http://theturtle32.github.com/WebSocket-Node/benchmarks/) that shows the results from the Autobahn tests run against AutobahnServer 0.4.10, WebSocket-Node 1.0.2, WebSocket-Node 1.0.4, and ws 0.3.4.\n\n(These benchmarks are quite a bit outdated at this point, so take them with a grain of salt. Anyone up for running new benchmarks? I'll link to your report.)\n\nAutobahn Tests\n--------------\nThe very complete [Autobahn Test Suite](http://autobahn.ws/testsuite/) is used by most WebSocket implementations to test spec compliance and interoperability.\n\n- [View Server Test Results](http://theturtle32.github.com/WebSocket-Node/test-report/servers/)\n\nInstallation\n------------\n\nIn your project root:\n\n    $ npm install websocket\n  \nThen in your code:\n\n```javascript\nvar WebSocketServer = require('websocket').server;\nvar WebSocketClient = require('websocket').client;\nvar WebSocketFrame  = require('websocket').frame;\nvar WebSocketRouter = require('websocket').router;\nvar W3CWebSocket = require('websocket').w3cwebsocket;\n```\n\nCurrent Features:\n-----------------\n- Licensed under the Apache License, Version 2.0\n- Protocol version \"8\" and \"13\" (Draft-08 through the final RFC) framing and handshake\n- Can handle/aggregate received fragmented messages\n- Can fragment outgoing messages\n- Router to mount multiple applications to various path and protocol combinations\n- TLS supported for outbound connections via WebSocketClient\n- TLS supported for server connections (use https.createServer instead of http.createServer)\n  - Thanks to [pors](https://github.com/pors) for confirming this!\n- Cookie setting and parsing\n- Tunable settings\n  - Max Receivable Frame Size\n  - Max Aggregate ReceivedMessage Size\n  - Whether to fragment outgoing messages\n  - Fragmentation chunk size for outgoing messages\n  - Whether to automatically send ping frames for the purposes of keepalive\n  - Keep-alive ping interval\n  - Whether or not to automatically assemble received fragments (allows application to handle individual fragments directly)\n  - How long to wait after sending a close frame for acknowledgment before closing the socket.\n- [W3C WebSocket API](http://www.w3.org/TR/websockets/) for applications running on both Node and browsers (via the `W3CWebSocket` class). \n\n\nKnown Issues/Missing Features:\n------------------------------\n- No API for user-provided protocol extensions.\n\n\nUsage Examples\n==============\n\nServer Example\n--------------\n\nHere's a short example showing a server that echos back anything sent to it, whether utf-8 or binary.\n\n```javascript\n#!/usr/bin/env node\nvar WebSocketServer = require('websocket').server;\nvar http = require('http');\n\nvar server = http.createServer(function(request, response) {\n    console.log((new Date()) + ' Received request for ' + request.url);\n    response.writeHead(404);\n    response.end();\n});\nserver.listen(8080, function() {\n    console.log((new Date()) + ' Server is listening on port 8080');\n});\n\nwsServer = new WebSocketServer({\n    httpServer: server,\n    // You should not use autoAcceptConnections for production\n    // applications, as it defeats all standard cross-origin protection\n    // facilities built into the protocol and the browser.  You should\n    // *always* verify the connection's origin and decide whether or not\n    // to accept it.\n    autoAcceptConnections: false\n});\n\nfunction originIsAllowed(origin) {\n  // put logic here to detect whether the specified origin is allowed.\n  return true;\n}\n\nwsServer.on('request', function(request) {\n    if (!originIsAllowed(request.origin)) {\n      // Make sure we only accept requests from an allowed origin\n      request.reject();\n      console.log((new Date()) + ' Connection from origin ' + request.origin + ' rejected.');\n      return;\n    }\n    \n    var connection = request.accept('echo-protocol', request.origin);\n    console.log((new Date()) + ' Connection accepted.');\n    connection.on('message', function(message) {\n        if (message.type === 'utf8') {\n            console.log('Received Message: ' + message.utf8Data);\n            connection.sendUTF(message.utf8Data);\n        }\n        else if (message.type === 'binary') {\n            console.log('Received Binary Message of ' + message.binaryData.length + ' bytes');\n            connection.sendBytes(message.binaryData);\n        }\n    });\n    connection.on('close', function(reasonCode, description) {\n        console.log((new Date()) + ' Peer ' + connection.remoteAddress + ' disconnected.');\n    });\n});\n```\n\nClient Example\n--------------\n\nThis is a simple example client that will print out any utf-8 messages it receives on the console, and periodically sends a random number.\n\n*This code demonstrates a client in Node.js, not in the browser*\n\n```javascript\n#!/usr/bin/env node\nvar WebSocketClient = require('websocket').client;\n\nvar client = new WebSocketClient();\n\nclient.on('connectFailed', function(error) {\n    console.log('Connect Error: ' + error.toString());\n});\n\nclient.on('connect', function(connection) {\n    console.log('WebSocket Client Connected');\n    connection.on('error', function(error) {\n        console.log(\"Connection Error: \" + error.toString());\n    });\n    connection.on('close', function() {\n        console.log('echo-protocol Connection Closed');\n    });\n    connection.on('message', function(message) {\n        if (message.type === 'utf8') {\n            console.log(\"Received: '\" + message.utf8Data + \"'\");\n        }\n    });\n    \n    function sendNumber() {\n        if (connection.connected) {\n            var number = Math.round(Math.random() * 0xFFFFFF);\n            connection.sendUTF(number.toString());\n            setTimeout(sendNumber, 1000);\n        }\n    }\n    sendNumber();\n});\n\nclient.connect('ws://localhost:8080/', 'echo-protocol');\n```\n\nClient Example using the *W3C WebSocket API*\n--------------------------------------------\n\nSame example as above but using the [W3C WebSocket API](http://www.w3.org/TR/websockets/).\n\n```javascript\nvar W3CWebSocket = require('websocket').w3cwebsocket;\n\nvar client = new W3CWebSocket('ws://localhost:8080/', 'echo-protocol');\n\nclient.onerror = function() {\n    console.log('Connection Error');\n};\n\nclient.onopen = function() {\n    console.log('WebSocket Client Connected');\n\n    function sendNumber() {\n        if (client.readyState === client.OPEN) {\n            var number = Math.round(Math.random() * 0xFFFFFF);\n            client.send(number.toString());\n            setTimeout(sendNumber, 1000);\n        }\n    }\n    sendNumber();\n};\n\nclient.onclose = function() {\n    console.log('echo-protocol Client Closed');\n};\n\nclient.onmessage = function(e) {\n    if (typeof e.data === 'string') {\n        console.log(\"Received: '\" + e.data + \"'\");\n    }\n};\n```\n    \nRequest Router Example\n----------------------\n\nFor an example of using the request router, see `libwebsockets-test-server.js` in the `test` folder.\n\n\nResources\n---------\n\nA presentation on the state of the WebSockets protocol that I gave on July 23, 2011 at the LA Hacker News meetup.  [WebSockets: The Real-Time Web, Delivered](http://www.scribd.com/doc/60898569/WebSockets-The-Real-Time-Web-Delivered)\n", "time": {"created": "2022-01-26T13:12:06.423Z", "modified": "2024-05-12T17:48:20.036Z", "1.0.34": "2021-04-14T19:51:11.758Z", "1.0.33": "2020-12-09T02:00:53.100Z", "1.0.32": "2020-08-28T16:05:14.177Z", "1.0.31": "2019-12-06T21:35:37.019Z", "1.0.30": "2019-09-12T18:11:27.462Z", "1.0.29": "2019-07-03T20:23:32.475Z", "1.0.28": "2018-09-20T00:40:13.628Z", "1.0.27": "2018-09-20T00:22:49.382Z", "1.0.26": "2018-04-26T23:08:48.217Z", "1.0.25": "2017-10-19T00:23:33.360Z", "1.0.24": "2016-12-29T02:15:58.177Z", "1.0.23": "2016-05-19T02:43:17.323Z", "1.0.22": "2015-09-29T06:46:30.445Z", "1.0.21": "2015-07-22T22:53:34.021Z", "1.0.20": "2015-07-22T22:48:42.751Z", "1.0.19": "2015-05-28T23:24:40.007Z", "1.0.18": "2015-03-19T19:37:22.247Z", "1.0.17": "2015-01-17T23:04:50.631Z", "1.0.16": "2015-01-16T21:39:22.656Z", "1.0.15": "2015-01-13T18:44:38.425Z", "1.0.14": "2014-12-03T22:08:33.371Z", "1.0.13": "2014-11-29T20:12:31.160Z", "1.0.12": "2014-11-28T19:49:59.445Z", "1.0.11": "2014-11-25T22:19:42.307Z", "1.0.10": "2014-10-23T17:16:55.648Z", "1.0.9": "2014-10-20T18:21:36.732Z", "1.0.8": "2012-12-28T17:23:44.596Z", "1.0.7": "2012-08-12T23:23:17.579Z", "1.0.6": "2012-05-22T08:43:26.428Z", "1.0.5": "2012-05-21T22:16:20.458Z", "1.0.4": "2011-12-19T02:55:37.055Z", "1.0.3": "2011-12-18T10:19:49.737Z", "1.0.2": "2011-11-28T22:37:56.321Z", "1.0.1": "2011-11-22T07:35:17.281Z", "1.0.0": "2011-10-26T11:25:52.259Z", "0.0.20": "2011-10-20T08:28:48.776Z", "0.0.19": "2011-10-11T21:16:10.076Z", "0.0.18": "2011-10-11T21:14:45.409Z", "0.0.17": "2011-09-25T19:48:05.758Z", "0.0.16": "2011-09-05T20:47:34.105Z", "0.0.15": "2011-08-30T22:37:13.100Z", "0.0.14": "2011-08-25T05:43:31.961Z", "0.0.13": "2011-08-18T23:47:29.899Z", "0.0.12": "2011-08-12T04:11:10.739Z", "0.0.11": "2011-08-12T02:02:23.844Z", "0.0.10": "2011-08-10T01:46:03.649Z", "0.0.9": "2011-08-09T01:44:38.024Z", "0.0.8": "2011-08-01T22:58:04.540Z", "0.0.7": "2011-07-23T02:45:56.734Z", "0.0.6": "2011-07-20T03:25:50.541Z", "0.0.5": "2011-07-20T01:49:40.651Z", "0.0.4": "2011-07-18T23:52:10.397Z", "0.0.3": "2011-07-18T21:02:20.583Z", "0.0.2": "2011-07-18T06:04:02.923Z", "0.0.1": "2011-07-05T23:56:30.823Z", "1.0.35": "2024-05-12T17:48:10.186Z"}, "versions": {"1.0.34": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push", "RFC-6455", "realtime", "server", "client"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/theturtle32"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://dev.sipdoc.net"}], "version": "1.0.34", "repository": {"type": "git", "url": "git+https://github.com/theturtle32/WebSocket-Node.git"}, "homepage": "https://github.com/theturtle32/WebSocket-Node", "engines": {"node": ">=4.0.0"}, "dependencies": {"bufferutil": "^4.0.1", "debug": "^2.2.0", "es5-ext": "^0.10.50", "typedarray-to-buffer": "^3.1.5", "utf-8-validate": "^5.0.2", "yaeti": "^0.0.6"}, "devDependencies": {"buffer-equal": "^1.0.0", "gulp": "^4.0.2", "gulp-jshint": "^2.0.4", "jshint-stylish": "^2.2.1", "jshint": "^2.0.0", "tape": "^4.9.1"}, "config": {"verbose": false}, "scripts": {"test": "tape test/unit/*.js", "gulp": "gulp"}, "main": "index", "directories": {"lib": "./lib"}, "browser": "lib/browser.js", "license": "Apache-2.0", "gitHead": "a2cd3065167668a9685db0d5f9c4083e8a1839f0", "bugs": {"url": "https://github.com/theturtle32/WebSocket-Node/issues"}, "_id": "websocket@1.0.34", "_nodeVersion": "10.24.1", "_npmVersion": "6.14.12", "dist": {"shasum": "2bdc2602c08bf2c82253b730655c0ef7dcab3111", "size": 40823, "noattachment": false, "tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.34.tgz", "integrity": "sha512-PRDso2sGwF6kM75QykIesBijKSVceR6jL2G8NGYyq2XrItNC2P5/qL5XeR056GhA+Ly7JMFvJb9I312mJfmqnQ=="}, "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/websocket_1.0.34_1618429871633_0.06288281968505105"}, "_hasShrinkwrap": false, "publish_time": 1618429871758, "_cnpm_publish_time": 1618429871758, "_cnpmcore_publish_time": "2021-12-15T18:09:09.135Z"}, "1.0.33": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push", "RFC-6455", "realtime", "server", "client"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/theturtle32"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://dev.sipdoc.net"}], "version": "1.0.33", "repository": {"type": "git", "url": "git+https://github.com/theturtle32/WebSocket-Node.git"}, "homepage": "https://github.com/theturtle32/WebSocket-Node", "engines": {"node": ">=4.0.0"}, "dependencies": {"bufferutil": "^4.0.1", "debug": "^2.2.0", "es5-ext": "^0.10.50", "typedarray-to-buffer": "^3.1.5", "utf-8-validate": "^5.0.2", "yaeti": "^0.0.6"}, "devDependencies": {"buffer-equal": "^1.0.0", "gulp": "^4.0.2", "gulp-jshint": "^2.0.4", "jshint-stylish": "^2.2.1", "jshint": "^2.0.0", "tape": "^4.9.1"}, "config": {"verbose": false}, "scripts": {"test": "tape test/unit/*.js", "gulp": "gulp"}, "main": "index", "directories": {"lib": "./lib"}, "browser": "lib/browser.js", "license": "Apache-2.0", "gitHead": "39bf9037707f9cd60ce04ae6fd2203c97480c2fe", "bugs": {"url": "https://github.com/theturtle32/WebSocket-Node/issues"}, "_id": "websocket@1.0.33", "_nodeVersion": "14.8.0", "_npmVersion": "6.14.7", "dist": {"shasum": "407f763fc58e74a3fa41ca3ae5d78d3f5e3b82a5", "size": 40671, "noattachment": false, "tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.33.tgz", "integrity": "sha512-XwNqM2rN5eh3G2CUQE3OHZj+0xfdH42+OFK6LdC2yqiC0YU8e5UK0nYre220T0IyyN031V/XOvtHvXozvJYFWA=="}, "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/websocket_1.0.33_1607479252910_0.21102337965095042"}, "_hasShrinkwrap": false, "publish_time": 1607479253100, "_cnpm_publish_time": 1607479253100, "_cnpmcore_publish_time": "2021-12-15T18:09:10.253Z"}, "1.0.32": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push", "RFC-6455", "realtime", "server", "client"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/theturtle32"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://dev.sipdoc.net"}], "version": "1.0.32", "repository": {"type": "git", "url": "git+https://github.com/theturtle32/WebSocket-Node.git"}, "homepage": "https://github.com/theturtle32/WebSocket-Node", "engines": {"node": ">=4.0.0"}, "dependencies": {"bufferutil": "^4.0.1", "debug": "^2.2.0", "es5-ext": "^0.10.50", "typedarray-to-buffer": "^3.1.5", "utf-8-validate": "^5.0.2", "yaeti": "^0.0.6"}, "devDependencies": {"buffer-equal": "^1.0.0", "gulp": "^4.0.2", "gulp-jshint": "^2.0.4", "jshint-stylish": "^2.2.1", "jshint": "^2.0.0", "tape": "^4.9.1"}, "config": {"verbose": false}, "scripts": {"test": "tape test/unit/*.js", "gulp": "gulp"}, "main": "index", "directories": {"lib": "./lib"}, "browser": "lib/browser.js", "license": "Apache-2.0", "gitHead": "5fd43e0fd54f4af807d3e261825080906abc4f2d", "bugs": {"url": "https://github.com/theturtle32/WebSocket-Node/issues"}, "_id": "websocket@1.0.32", "_nodeVersion": "14.8.0", "_npmVersion": "6.14.7", "dist": {"shasum": "1f16ddab3a21a2d929dec1687ab21cfdc6d3dbb1", "size": 40455, "noattachment": false, "tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.32.tgz", "integrity": "sha512-i4yhcllSP4wrpoPMU2N0TQ/q0O94LRG/eUQjEAamRltjQ1oT1PFFKOG4i877OlJgCG8rw6LrrowJp+TYCEWF7Q=="}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/websocket_1.0.32_1598630714004_0.9029482699636613"}, "_hasShrinkwrap": false, "publish_time": 1598630714177, "_cnpm_publish_time": 1598630714177, "_cnpmcore_publish_time": "2021-12-15T18:09:10.525Z"}, "1.0.31": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push", "RFC-6455", "realtime", "server", "client"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/theturtle32"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://dev.sipdoc.net"}], "version": "1.0.31", "repository": {"type": "git", "url": "git+https://github.com/theturtle32/WebSocket-Node.git"}, "homepage": "https://github.com/theturtle32/WebSocket-Node", "engines": {"node": ">=0.10.0"}, "dependencies": {"debug": "^2.2.0", "es5-ext": "^0.10.50", "nan": "^2.14.0", "typedarray-to-buffer": "^3.1.5", "yaeti": "^0.0.6"}, "devDependencies": {"buffer-equal": "^1.0.0", "faucet": "^0.0.1", "gulp": "^4.0.2", "gulp-jshint": "^2.0.4", "jshint-stylish": "^2.2.1", "jshint": "^2.0.0", "tape": "^4.9.1"}, "config": {"verbose": false}, "scripts": {"install": "(node-gyp rebuild 2> builderror.log) || (exit 0)", "test": "faucet test/unit", "gulp": "gulp"}, "main": "index", "directories": {"lib": "./lib"}, "browser": "lib/browser.js", "license": "Apache-2.0", "gitHead": "1f7ffba2f7a6f9473bcb39228264380ce2772ba7", "bugs": {"url": "https://github.com/theturtle32/WebSocket-Node/issues"}, "_id": "websocket@1.0.31", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "e5d0f16c3340ed87670e489ecae6144c79358730", "size": 43246, "noattachment": false, "tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.31.tgz", "integrity": "sha512-VAouplvGKPiKFDTeCCO65vYHsyay8DqoBSlzIO3fayrfOgU94lQN5a1uWVnFrMLceTJw/+fQXR5PGbUVRaHshQ=="}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/websocket_1.0.31_1575668136882_0.7107092135930231"}, "_hasShrinkwrap": false, "publish_time": 1575668137019, "_cnpm_publish_time": 1575668137019, "_cnpmcore_publish_time": "2021-12-15T18:09:10.820Z", "hasInstallScript": true}, "1.0.30": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push", "RFC-6455", "realtime", "server", "client"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/theturtle32"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://dev.sipdoc.net"}], "version": "1.0.30", "repository": {"type": "git", "url": "git+https://github.com/theturtle32/WebSocket-Node.git"}, "homepage": "https://github.com/theturtle32/WebSocket-Node", "engines": {"node": ">=0.10.0"}, "dependencies": {"debug": "^2.2.0", "nan": "^2.14.0", "typedarray-to-buffer": "^3.1.5", "yaeti": "^0.0.6"}, "devDependencies": {"buffer-equal": "^1.0.0", "faucet": "^0.0.1", "gulp": "^4.0.2", "gulp-jshint": "^2.0.4", "jshint-stylish": "^2.2.1", "jshint": "^2.0.0", "tape": "^4.9.1"}, "config": {"verbose": false}, "scripts": {"install": "(node-gyp rebuild 2> builderror.log) || (exit 0)", "test": "faucet test/unit", "gulp": "gulp"}, "main": "index", "directories": {"lib": "./lib"}, "browser": "lib/browser.js", "license": "Apache-2.0", "gitHead": "7db960ef47f9e87799a17e5a7ef3f46d3b1b9227", "bugs": {"url": "https://github.com/theturtle32/WebSocket-Node/issues"}, "_id": "websocket@1.0.30", "_nodeVersion": "12.9.0", "_npmVersion": "6.10.2", "dist": {"shasum": "91d3bd00c3d43e916f0cf962f8f8c451bb0b2373", "size": 42977, "noattachment": false, "tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.30.tgz", "integrity": "sha512-aO6klgaTdSMkhfl5VVJzD5fm+Srhh5jLYbS15+OiI1sN6h/RU/XW6WN9J1uVIpUKNmsTvT3Hs35XAFjn9NMfOw=="}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/websocket_1.0.30_1568311887357_0.18656696326018363"}, "_hasShrinkwrap": false, "publish_time": 1568311887462, "_cnpm_publish_time": 1568311887462, "_cnpmcore_publish_time": "2021-12-15T18:09:11.258Z", "hasInstallScript": true}, "1.0.29": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push", "RFC-6455", "realtime", "server", "client"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/theturtle32"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://dev.sipdoc.net"}], "version": "1.0.29", "repository": {"type": "git", "url": "git+https://github.com/theturtle32/WebSocket-Node.git"}, "homepage": "https://github.com/theturtle32/WebSocket-Node", "engines": {"node": ">=0.10.0"}, "dependencies": {"debug": "^2.2.0", "nan": "^2.11.0", "gulp": "^4.0.2", "typedarray-to-buffer": "^3.1.5", "yaeti": "^0.0.6"}, "devDependencies": {"buffer-equal": "^1.0.0", "faucet": "^0.0.1", "gulp-jshint": "^2.0.4", "jshint-stylish": "^2.2.1", "jshint": "^2.0.0", "tape": "^4.9.1"}, "config": {"verbose": false}, "scripts": {"install": "(node-gyp rebuild 2> builderror.log) || (exit 0)", "test": "faucet test/unit", "gulp": "gulp"}, "main": "index", "directories": {"lib": "./lib"}, "browser": "lib/browser.js", "license": "Apache-2.0", "gitHead": "6e62787df0ccc9a34b12c134686aef07716849f3", "bugs": {"url": "https://github.com/theturtle32/WebSocket-Node/issues"}, "_id": "websocket@1.0.29", "_nodeVersion": "12.2.0", "_npmVersion": "6.9.0", "dist": {"shasum": "3f83e49d3279657c58b02a22d90749c806101b98", "size": 43028, "noattachment": false, "tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.29.tgz", "integrity": "sha512-WhU8jKXC8sTh6ocLSqpZRlOKMNYGwUvjA5+XcIgIk/G3JCaDfkZUr0zA19sVSxJ0TEvm0i5IBzr54RZC4vzW7g=="}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/websocket_1.0.29_1562185412310_0.9413136733803418"}, "_hasShrinkwrap": false, "publish_time": 1562185412475, "_cnpm_publish_time": 1562185412475, "_cnpmcore_publish_time": "2021-12-15T18:09:11.531Z", "hasInstallScript": true}, "1.0.28": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push", "RFC-6455", "realtime", "server", "client"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/theturtle32"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://dev.sipdoc.net"}], "version": "1.0.28", "repository": {"type": "git", "url": "git+https://github.com/theturtle32/WebSocket-Node.git"}, "homepage": "https://github.com/theturtle32/WebSocket-Node", "engines": {"node": ">=0.10.0"}, "dependencies": {"debug": "^2.2.0", "nan": "^2.11.0", "typedarray-to-buffer": "^3.1.5", "yaeti": "^0.0.6"}, "devDependencies": {"buffer-equal": "^1.0.0", "faucet": "^0.0.1", "gulp": "git+https://github.com/gulpjs/gulp.git#4.0", "gulp-jshint": "^2.0.4", "jshint-stylish": "^2.2.1", "jshint": "^2.0.0", "tape": "^4.9.1"}, "config": {"verbose": false}, "scripts": {"install": "(node-gyp rebuild 2> builderror.log) || (exit 0)", "test": "faucet test/unit", "gulp": "gulp"}, "main": "index", "directories": {"lib": "./lib"}, "browser": "lib/browser.js", "license": "Apache-2.0", "gitHead": "c0066b50142e1d110b092057816b863303db1c8c", "bugs": {"url": "https://github.com/theturtle32/WebSocket-Node/issues"}, "_id": "websocket@1.0.28", "_npmVersion": "6.4.1", "_nodeVersion": "10.9.0", "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "dist": {"shasum": "9e5f6fdc8a3fe01d4422647ef93abdd8d45a78d3", "size": 43019, "noattachment": false, "tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.28.tgz", "integrity": "sha512-00y/20/80P7H4bCYkzuuvvfDvh+dgtXi5kzDf3UcZwN6boTYaKvsrtZ5lIYm1Gsg48siMErd9M4zjSYfYFHTrA=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/websocket_1.0.28_1537404013416_0.0479920276164143"}, "_hasShrinkwrap": false, "publish_time": 1537404013628, "_cnpm_publish_time": 1537404013628, "_cnpmcore_publish_time": "2021-12-15T18:09:11.825Z", "hasInstallScript": true}, "1.0.27": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push", "RFC-6455", "realtime", "server", "client"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/theturtle32"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://dev.sipdoc.net"}], "version": "1.0.27", "repository": {"type": "git", "url": "git+https://github.com/theturtle32/WebSocket-Node.git"}, "homepage": "https://github.com/theturtle32/WebSocket-Node", "engines": {"node": ">=0.10.0"}, "dependencies": {"debug": "^2.2.0", "nan": "^2.3.3", "typedarray-to-buffer": "^3.1.5", "yaeti": "^0.0.6"}, "devDependencies": {"buffer-equal": "^1.0.0", "faucet": "^0.0.1", "gulp": "git+https://github.com/gulpjs/gulp.git#4.0", "gulp-jshint": "^2.0.4", "jshint-stylish": "^2.2.1", "jshint": "^2.0.0", "tape": "^4.9.1"}, "config": {"verbose": false}, "scripts": {"install": "(node-gyp rebuild 2> builderror.log) || (exit 0)", "test": "faucet test/unit", "gulp": "gulp"}, "main": "index", "directories": {"lib": "./lib"}, "browser": "lib/browser.js", "license": "Apache-2.0", "gitHead": "b9ae56e425a5d73a6315e71e52e038e836d12861", "bugs": {"url": "https://github.com/theturtle32/WebSocket-Node/issues"}, "_id": "websocket@1.0.27", "_npmVersion": "6.4.1", "_nodeVersion": "10.9.0", "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "dist": {"shasum": "f8ea82264e9dbb84688c010e591ba17bfe13044f", "size": 43035, "noattachment": false, "tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.27.tgz", "integrity": "sha512-skRNZGZIJJ2712a0IHTjppFEOBK4Fx+M5yFMvEJeZxN6uKLNmOVpCuEoEsYAtUw3TWfnlhSKEpsCPijHatKhiA=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/websocket_1.0.27_1537402969188_0.06945961330819905"}, "_hasShrinkwrap": false, "publish_time": 1537402969382, "_cnpm_publish_time": 1537402969382, "_cnpmcore_publish_time": "2021-12-15T18:09:12.114Z", "hasInstallScript": true}, "1.0.26": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push", "RFC-6455", "realtime", "server", "client"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.worlize.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://dev.sipdoc.net"}], "version": "1.0.26", "repository": {"type": "git", "url": "git+https://github.com/theturtle32/WebSocket-Node.git"}, "homepage": "https://github.com/theturtle32/WebSocket-Node", "engines": {"node": ">=0.10.0"}, "dependencies": {"debug": "^2.2.0", "nan": "^2.3.3", "typedarray-to-buffer": "^3.1.2", "yaeti": "^0.0.6"}, "devDependencies": {"buffer-equal": "^1.0.0", "faucet": "^0.0.1", "gulp": "git+https://github.com/gulpjs/gulp.git#4.0", "gulp-jshint": "^2.0.4", "jshint-stylish": "^2.2.1", "jshint": "^2.0.0", "tape": "^4.0.1"}, "config": {"verbose": false}, "scripts": {"install": "(node-gyp rebuild 2> builderror.log) || (exit 0)", "test": "faucet test/unit", "gulp": "gulp"}, "main": "index", "directories": {"lib": "./lib"}, "browser": "lib/browser.js", "license": "Apache-2.0", "gitHead": "0b3d4a5eb253132b2219f6f22a420bfe4680e26a", "bugs": {"url": "https://github.com/theturtle32/WebSocket-Node/issues"}, "_id": "websocket@1.0.26", "_npmVersion": "5.6.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "dist": {"shasum": "a03a01299849c35268c83044aa919c6374be8194", "size": 42852, "noattachment": false, "tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.26.tgz", "integrity": "sha512-fjcrYDPIQxpTnqFQ9JjxUQcdvR89MFAOjPBlF+vjOt49w/XW4fJknUoMz/mDIn2eK1AdslVojcaOxOqyZZV8rw=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/websocket_1.0.26_1524784128055_0.2769635769636831"}, "_hasShrinkwrap": false, "publish_time": 1524784128217, "_cnpm_publish_time": 1524784128217, "_cnpmcore_publish_time": "2021-12-15T18:09:12.504Z", "hasInstallScript": true}, "1.0.25": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push", "RFC-6455", "realtime", "server", "client"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.worlize.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://dev.sipdoc.net"}], "version": "1.0.25", "repository": {"type": "git", "url": "git+https://github.com/theturtle32/WebSocket-Node.git"}, "homepage": "https://github.com/theturtle32/WebSocket-Node", "engines": {"node": ">=0.10.0"}, "dependencies": {"debug": "^2.2.0", "nan": "^2.3.3", "typedarray-to-buffer": "^3.1.2", "yaeti": "^0.0.6"}, "devDependencies": {"buffer-equal": "^1.0.0", "faucet": "^0.0.1", "gulp": "git+https://github.com/gulpjs/gulp.git#4.0", "gulp-jshint": "^2.0.4", "jshint-stylish": "^2.2.1", "jshint": "^2.0.0", "tape": "^4.0.1"}, "config": {"verbose": false}, "scripts": {"install": "(node-gyp rebuild 2> builderror.log) || (exit 0)", "test": "faucet test/unit", "gulp": "gulp"}, "main": "index", "directories": {"lib": "./lib"}, "browser": "lib/browser.js", "license": "Apache-2.0", "gitHead": "d941f975e8ef6b55eafc0ef45996f4198013832c", "bugs": {"url": "https://github.com/theturtle32/WebSocket-Node/issues"}, "_id": "websocket@1.0.25", "_npmVersion": "5.4.2", "_nodeVersion": "8.7.0", "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "dist": {"shasum": "998ec790f0a3eacb8b08b50a4350026692a11958", "size": 42743, "noattachment": false, "tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.25.tgz", "integrity": "sha512-M58njvi6ZxVb5k7kpnHh2BvNKuBWiwIYvsToErBzWhvBZYwlEiLcyLrG41T1jRcrY9ettqPYEqduLI7ul54CVQ=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/websocket-1.0.25.tgz_1508372613263_0.2284609314519912"}, "publish_time": 1508372613360, "_hasShrinkwrap": false, "_cnpm_publish_time": 1508372613360, "_cnpmcore_publish_time": "2021-12-15T18:09:12.764Z", "hasInstallScript": true}, "1.0.24": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push", "RFC-6455", "realtime", "server", "client"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.worlize.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://dev.sipdoc.net"}], "version": "1.0.24", "repository": {"type": "git", "url": "git+https://github.com/theturtle32/WebSocket-Node.git"}, "homepage": "https://github.com/theturtle32/WebSocket-Node", "engines": {"node": ">=0.8.0"}, "dependencies": {"debug": "^2.2.0", "nan": "^2.3.3", "typedarray-to-buffer": "^3.1.2", "yaeti": "^0.0.6"}, "devDependencies": {"buffer-equal": "^1.0.0", "faucet": "^0.0.1", "gulp": "git+https://github.com/gulpjs/gulp.git#4.0", "gulp-jshint": "^2.0.4", "jshint-stylish": "^2.2.1", "jshint": "^2.0.0", "tape": "^4.0.1"}, "config": {"verbose": false}, "scripts": {"install": "(node-gyp rebuild 2> builderror.log) || (exit 0)", "test": "faucet test/unit", "gulp": "gulp"}, "main": "index", "directories": {"lib": "./lib"}, "browser": "lib/browser.js", "license": "Apache-2.0", "gitHead": "0e15f9445953927c39ce84a232cb7dd6e3adf12e", "bugs": {"url": "https://github.com/theturtle32/WebSocket-Node/issues"}, "_id": "websocket@1.0.24", "_shasum": "74903e75f2545b6b2e1de1425bc1c905917a1890", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "7.3.0", "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "dist": {"shasum": "74903e75f2545b6b2e1de1425bc1c905917a1890", "size": 43295, "noattachment": false, "tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.24.tgz", "integrity": "sha512-racRHbgLAr3E36aEiTrtoUpPSm+bdVkUHWYw23fEwvnLeUbmyShWOhA+COxVu6QSpmr5sRwggSmoXYdPZtXkww=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/websocket-1.0.24.tgz_1482977757939_0.1858439394272864"}, "publish_time": 1482977758177, "_hasShrinkwrap": false, "_cnpm_publish_time": 1482977758177, "_cnpmcore_publish_time": "2021-12-15T18:09:13.157Z", "hasInstallScript": true}, "1.0.23": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push", "RFC-6455", "realtime", "server", "client"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.worlize.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://dev.sipdoc.net"}], "version": "1.0.23", "repository": {"type": "git", "url": "git+https://github.com/theturtle32/WebSocket-Node.git"}, "homepage": "https://github.com/theturtle32/WebSocket-Node", "engines": {"node": ">=0.8.0"}, "dependencies": {"debug": "^2.2.0", "nan": "^2.3.3", "typedarray-to-buffer": "^3.1.2", "yaeti": "^0.0.4"}, "devDependencies": {"buffer-equal": "^0.0.1", "faucet": "^0.0.1", "gulp": "git+https://github.com/gulpjs/gulp.git#4.0", "gulp-jshint": "^1.11.2", "jshint-stylish": "^1.0.2", "tape": "^4.0.1"}, "config": {"verbose": false}, "scripts": {"install": "(node-gyp rebuild 2> builderror.log) || (exit 0)", "test": "faucet test/unit", "gulp": "gulp"}, "main": "index", "directories": {"lib": "./lib"}, "browser": "lib/browser.js", "license": "Apache-2.0", "gitHead": "ba2fa7e9676c456bcfb12ad160655319af66faed", "bugs": {"url": "https://github.com/theturtle32/WebSocket-Node/issues"}, "_id": "websocket@1.0.23", "_shasum": "20de8ec4a7126b09465578cd5dbb29a9c296aac6", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "0.10.45", "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "dist": {"shasum": "20de8ec4a7126b09465578cd5dbb29a9c296aac6", "size": 51810, "noattachment": false, "tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.23.tgz", "integrity": "sha512-iakeDk+c3fIhtexjn9s01fwF9Zo5SO5Ihidk9mm9galWkvvRP3A38hANtyG4LGwipk4Ml4HjjFISL2qNCd+qjw=="}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/websocket-1.0.23.tgz_1463625793005_0.4532310354989022"}, "publish_time": 1463625797323, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463625797323, "_cnpmcore_publish_time": "2021-12-15T18:09:13.417Z", "hasInstallScript": true}, "1.0.22": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push", "RFC-6455", "realtime", "server", "client"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.worlize.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://dev.sipdoc.net"}], "version": "1.0.22", "repository": {"type": "git", "url": "git+https://github.com/theturtle32/WebSocket-Node.git"}, "homepage": "https://github.com/theturtle32/WebSocket-Node", "engines": {"node": ">=0.8.0"}, "dependencies": {"debug": "~2.2.0", "nan": "~2.0.5", "typedarray-to-buffer": "~3.0.3", "yaeti": "~0.0.4"}, "devDependencies": {"buffer-equal": "^0.0.1", "faucet": "^0.0.1", "gulp": "git+https://github.com/gulpjs/gulp.git#4.0", "gulp-jshint": "^1.11.2", "jshint-stylish": "^1.0.2", "tape": "^4.0.1"}, "config": {"verbose": false}, "scripts": {"install": "(node-gyp rebuild 2> builderror.log) || (exit 0)", "test": "faucet test/unit", "gulp": "gulp"}, "main": "index", "directories": {"lib": "./lib"}, "browser": "lib/browser.js", "license": "Apache-2.0", "gitHead": "19108bbfd7d94a5cd02dbff3495eafee9e901ca4", "bugs": {"url": "https://github.com/theturtle32/WebSocket-Node/issues"}, "_id": "websocket@1.0.22", "_shasum": "8c33e3449f879aaf518297c9744cebf812b9e3d8", "_from": ".", "_npmVersion": "2.14.3", "_nodeVersion": "3.3.1", "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "dist": {"shasum": "8c33e3449f879aaf518297c9744cebf812b9e3d8", "size": 51597, "noattachment": false, "tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.22.tgz", "integrity": "sha512-Q1DaWtFoQOKChQK5XwKPIA2XokhtaYVmAkRJcEIZhRH4ePnL092laaFLncYj2ymiaZGIc/hP+j0LGJasoeapJw=="}, "publish_time": 1443509190445, "_hasShrinkwrap": false, "_cnpm_publish_time": 1443509190445, "_cnpmcore_publish_time": "2021-12-15T18:09:13.711Z", "hasInstallScript": true}, "1.0.21": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push", "RFC-6455", "realtime", "server", "client"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.worlize.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://dev.sipdoc.net"}], "version": "1.0.21", "repository": {"type": "git", "url": "git+https://github.com/theturtle32/WebSocket-Node.git"}, "homepage": "https://github.com/theturtle32/WebSocket-Node", "engines": {"node": ">=0.8.0"}, "dependencies": {"debug": "~2.2.0", "nan": "~1.8.x", "typedarray-to-buffer": "~3.0.3", "yaeti": "~0.0.4"}, "devDependencies": {"buffer-equal": "^0.0.1", "faucet": "^0.0.1", "gulp": "git+https://github.com/gulpjs/gulp.git#4.0", "gulp-jshint": "^1.11.2", "jshint-stylish": "^1.0.2", "tape": "^4.0.1"}, "config": {"verbose": false}, "scripts": {"install": "(node-gyp rebuild 2> builderror.log) || (exit 0)", "test": "faucet test/unit", "gulp": "gulp"}, "main": "index", "directories": {"lib": "./lib"}, "browser": "lib/browser.js", "license": "Apache-2.0", "gitHead": "8f5d5f3ef3d946324fe016d525893546ff6500e1", "bugs": {"url": "https://github.com/theturtle32/WebSocket-Node/issues"}, "_id": "websocket@1.0.21", "_shasum": "f51f0a96ed19629af39922470ab591907f1c5bd9", "_from": ".", "_npmVersion": "2.12.1", "_nodeVersion": "2.3.4", "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "dist": {"shasum": "f51f0a96ed19629af39922470ab591907f1c5bd9", "size": 50986, "noattachment": false, "tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.21.tgz", "integrity": "sha512-Kf10ueQE141fWNJH/h09ETBNJrfgT9X6/FiziLKYzgoPJ1pMaOH0GQ+NElRVN/oMftKQPAQySVS2nWREkUpyEw=="}, "publish_time": 1437605614021, "_hasShrinkwrap": false, "_cnpm_publish_time": 1437605614021, "_cnpmcore_publish_time": "2021-12-15T18:09:14.001Z", "hasInstallScript": true}, "1.0.20": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push", "RFC-6455", "realtime", "server", "client"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.worlize.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://dev.sipdoc.net"}], "version": "1.0.20", "repository": {"type": "git", "url": "git+https://github.com/theturtle32/WebSocket-Node.git"}, "homepage": "https://github.com/theturtle32/WebSocket-Node", "engines": {"node": ">=0.8.0"}, "dependencies": {"debug": "~2.2.0", "nan": "~1.8.x", "typedarray-to-buffer": "~3.0.3", "yaeti": "~0.0.4"}, "devDependencies": {"buffer-equal": "^0.0.1", "faucet": "^0.0.1", "gulp": "git+https://github.com/gulpjs/gulp.git#4.0", "gulp-jshint": "^1.11.2", "jshint-stylish": "^1.0.2", "tape": "^4.0.1"}, "config": {"verbose": false}, "scripts": {"install": "(node-gyp rebuild 2> builderror.log) || (exit 0)", "test": "faucet test/unit", "gulp": "gulp"}, "main": "index", "directories": {"lib": "./lib"}, "browser": "lib/browser.js", "license": "Apache-2.0", "gitHead": "7eeacf17717e619a2272e4f6ec3d066bfc56660a", "bugs": {"url": "https://github.com/theturtle32/WebSocket-Node/issues"}, "_id": "websocket@1.0.20", "_shasum": "cef51328b26e5dbfbe2f718857920c174b228a7e", "_from": ".", "_npmVersion": "2.12.1", "_nodeVersion": "2.3.4", "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "dist": {"shasum": "cef51328b26e5dbfbe2f718857920c174b228a7e", "size": 50912, "noattachment": false, "tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.20.tgz", "integrity": "sha512-gESsdDsoEKFdxoXUjrxxtOBbMiTEB7Ih+gxD4oHbiiAMHD2Z398cc1Uu5T8ExQDt0zxZskHC6UpUXdxZLEomIw=="}, "publish_time": 1437605322751, "_hasShrinkwrap": false, "_cnpm_publish_time": 1437605322751, "_cnpmcore_publish_time": "2021-12-15T18:09:14.231Z", "hasInstallScript": true}, "1.0.19": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push", "RFC-6455", "realtime", "server", "client"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.worlize.com/"}, "version": "1.0.19", "repository": {"type": "git", "url": "git+https://github.com/theturtle32/WebSocket-Node.git"}, "homepage": "https://github.com/theturtle32/WebSocket-Node", "engines": {"node": ">=0.8.0"}, "dependencies": {"debug": "~2.1.0", "nan": "1.8.x", "typedarray-to-buffer": "~3.0.0"}, "devDependencies": {"buffer-equal": "0.0.1", "faucet": "0.0.1", "gulp": "git+https://github.com/gulpjs/gulp.git#4.0", "gulp-jshint": "^1.9.0", "jshint-stylish": "^1.0.0", "tape": "^3.0.0"}, "config": {"verbose": false}, "scripts": {"install": "(node-gyp rebuild 2> builderror.log) || (exit 0)", "test": "faucet test/unit", "gulp": "gulp"}, "main": "index", "directories": {"lib": "./lib"}, "browser": "lib/browser.js", "license": "Apache-2.0", "gitHead": "da3bd5b04e9442c84881b2e9c13432cdbbae1f16", "bugs": {"url": "https://github.com/theturtle32/WebSocket-Node/issues"}, "_id": "websocket@1.0.19", "_shasum": "e62dbf1a3c5e0767425db7187cfa38f921dfb42c", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "dist": {"shasum": "e62dbf1a3c5e0767425db7187cfa38f921dfb42c", "size": 51056, "noattachment": false, "tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.19.tgz", "integrity": "sha512-Fzuf/IMJnSFFrUIjdGmJmDRKU/dPSJfBh+UayFWsEKzoIfahAqOh6eBT4Df/ESGih2TS+VOvd6UtsapRGAO4yw=="}, "publish_time": 1432855480007, "_hasShrinkwrap": false, "_cnpm_publish_time": 1432855480007, "_cnpmcore_publish_time": "2021-12-15T18:09:14.813Z", "hasInstallScript": true}, "1.0.18": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push", "RFC-6455", "realtime", "server", "client"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.worlize.com/"}, "version": "1.0.18", "repository": {"type": "git", "url": "https://github.com/theturtle32/WebSocket-Node.git"}, "homepage": "https://github.com/theturtle32/WebSocket-Node", "engines": {"node": ">=0.8.0"}, "dependencies": {"debug": "~2.1.0", "nan": "~1.0.0", "typedarray-to-buffer": "~3.0.0"}, "devDependencies": {"buffer-equal": "0.0.1", "faucet": "0.0.1", "gulp": "git+https://github.com/gulpjs/gulp.git#4.0", "gulp-jshint": "^1.9.0", "jshint-stylish": "^1.0.0", "tape": "^3.0.0"}, "config": {"verbose": false}, "scripts": {"install": "(node-gyp rebuild 2> builderror.log) || (exit 0)", "test": "faucet test/unit", "gulp": "gulp"}, "main": "index", "directories": {"lib": "./lib"}, "browser": "lib/browser.js", "gitHead": "2888a6d8c6ea0211b429000d43ed5da76124733f", "bugs": {"url": "https://github.com/theturtle32/WebSocket-Node/issues"}, "_id": "websocket@1.0.18", "_shasum": "140280dcc90ed42caa7a701e182a8c9e2dec75ef", "_from": ".", "_npmVersion": "2.6.1", "_nodeVersion": "1.4.3", "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "dist": {"shasum": "140280dcc90ed42caa7a701e182a8c9e2dec75ef", "size": 51769, "noattachment": false, "tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.18.tgz", "integrity": "sha512-aB+CVcHDu/jofpm9qUjHZFIFXoMbUzeMI4l5/IOgYFaj+/8b8ZveYjvJUFBbl3hQDpXAM2V2JsyrGdSRQlutSA=="}, "publish_time": 1426793842247, "_hasShrinkwrap": false, "_cnpm_publish_time": 1426793842247, "_cnpmcore_publish_time": "2021-12-15T18:09:15.107Z", "hasInstallScript": true}, "1.0.17": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push", "RFC-6455", "realtime", "server", "client"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.worlize.com/"}, "version": "1.0.17", "repository": {"type": "git", "url": "https://github.com/theturtle32/WebSocket-Node.git"}, "homepage": "https://github.com/theturtle32/WebSocket-Node", "engines": {"node": ">=0.8.0"}, "dependencies": {"debug": "~2.1.0", "nan": "~1.0.0", "typedarray-to-buffer": "~3.0.0"}, "devDependencies": {"buffer-equal": "0.0.1", "faucet": "0.0.1", "gulp": "git+https://github.com/gulpjs/gulp.git#4.0", "gulp-jshint": "^1.9.0", "jshint-stylish": "^1.0.0", "tape": "^3.0.0"}, "config": {"verbose": false}, "scripts": {"install": "(node-gyp rebuild 2> builderror.log) || (exit 0)", "test": "faucet test/unit", "gulp": "gulp"}, "main": "index", "directories": {"lib": "./lib"}, "browser": "lib/browser.js", "gitHead": "cda940b883aa884906ac13158fe514229a67f426", "bugs": {"url": "https://github.com/theturtle32/WebSocket-Node/issues"}, "_id": "websocket@1.0.17", "_shasum": "8a572afc6ec120eb41473ca517d07d932f7b6a1c", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "dist": {"shasum": "8a572afc6ec120eb41473ca517d07d932f7b6a1c", "size": 50531, "noattachment": false, "tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.17.tgz", "integrity": "sha512-Vw2FZ2hzHd3lkftJ2wIFRJ7cxohyCy9g/LjcRtEJVH3eR9eVN9oseW6z34THCSwXSwUnNgnXckR6LetjY4n8Cg=="}, "publish_time": 1421535890631, "_hasShrinkwrap": false, "_cnpm_publish_time": 1421535890631, "_cnpmcore_publish_time": "2021-12-15T18:09:15.565Z", "hasInstallScript": true}, "1.0.16": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push", "RFC-6455", "realtime", "server", "client"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.worlize.com/"}, "version": "1.0.16", "repository": {"type": "git", "url": "https://github.com/theturtle32/WebSocket-Node.git"}, "homepage": "https://github.com/theturtle32/WebSocket-Node", "engines": {"node": ">=0.8.0"}, "dependencies": {"debug": "~2.1.0", "nan": "~1.0.0", "typedarray-to-buffer": "~3.0.0"}, "devDependencies": {"faucet": "0.0.1", "gulp": "git+https://github.com/gulpjs/gulp.git#4.0", "gulp-jshint": "^1.9.0", "jshint-stylish": "^1.0.0", "tape": "^3.0.0"}, "config": {"verbose": false}, "scripts": {"install": "(node-gyp rebuild 2> builderror.log) || (exit 0)", "test": "faucet test/unit", "gulp": "gulp"}, "main": "index", "directories": {"lib": "./lib"}, "browser": "lib/browser.js", "gitHead": "191ef055705fcc65c4bf56a83010ff9bf1faf398", "bugs": {"url": "https://github.com/theturtle32/WebSocket-Node/issues"}, "_id": "websocket@1.0.16", "_shasum": "6c96a1d68dcfdc445688d50c676b7fa493ef5b2a", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "dist": {"shasum": "6c96a1d68dcfdc445688d50c676b7fa493ef5b2a", "size": 50518, "noattachment": false, "tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.16.tgz", "integrity": "sha512-zkk5OqHQv8CuzGfSpcBpuMavt5YFTaWY8hgxnvid9f1kNg0r9gBCGgjTorrVexqz2zUtYBYZjWY4wcs5g/AFFQ=="}, "publish_time": 1421444362656, "_hasShrinkwrap": false, "_cnpm_publish_time": 1421444362656, "_cnpmcore_publish_time": "2021-12-15T18:09:15.847Z", "hasInstallScript": true}, "1.0.15": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push", "RFC-6455", "realtime", "server", "client"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.worlize.com/"}, "version": "1.0.15", "repository": {"type": "git", "url": "https://github.com/theturtle32/WebSocket-Node.git"}, "homepage": "https://github.com/theturtle32/WebSocket-Node", "engines": {"node": ">=0.8.0"}, "dependencies": {"debug": "~2.1.0", "nan": "~1.0.0", "typedarray-to-buffer": "~3.0.0"}, "devDependencies": {"faucet": "0.0.1", "gulp": "git+https://github.com/gulpjs/gulp.git#4.0", "gulp-jshint": "^1.9.0", "jshint-stylish": "^1.0.0", "tape": "^3.0.0"}, "config": {"verbose": false}, "scripts": {"install": "(node-gyp rebuild 2> builderror.log) || (exit 0)", "test": "faucet test/unit", "gulp": "gulp"}, "main": "index", "directories": {"lib": "./lib"}, "browser": "lib/browser.js", "gitHead": "7cd99112b319c9c52069a13c1c80a3b67167d513", "bugs": {"url": "https://github.com/theturtle32/WebSocket-Node/issues"}, "_id": "websocket@1.0.15", "_shasum": "cf9f0f9ce08bf20a7f2acac3980ee84b4abb58e1", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "dist": {"shasum": "cf9f0f9ce08bf20a7f2acac3980ee84b4abb58e1", "size": 50502, "noattachment": false, "tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.15.tgz", "integrity": "sha512-TIbiRd/12qzebb8IyQGs7wK0mv/0CpHvUX0eIYJNN7455KgiUo30tOdVBN7fyNcl2miS8CQuArdYlhTH8Iz67Q=="}, "publish_time": 1421174678425, "_hasShrinkwrap": false, "_cnpm_publish_time": 1421174678425, "_cnpmcore_publish_time": "2021-12-15T18:09:16.112Z", "hasInstallScript": true}, "1.0.14": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push", "RFC-6455", "realtime", "server", "client"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.worlize.com/"}, "version": "1.0.14", "repository": {"type": "git", "url": "https://github.com/theturtle32/WebSocket-Node.git"}, "homepage": "https://github.com/theturtle32/WebSocket-Node", "engines": {"node": ">=0.8.0"}, "dependencies": {"debug": "~2.1.0", "nan": "~1.0.0", "typedarray-to-buffer": "~3.0.0"}, "devDependencies": {"faucet": "0.0.1", "gulp": "git+https://github.com/gulpjs/gulp.git#4.0", "gulp-jshint": "^1.9.0", "jshint-stylish": "^1.0.0", "tape": "^3.0.0"}, "config": {"verbose": false}, "scripts": {"install": "(node-gyp rebuild 2> builderror.log) || (exit 0)", "test": "faucet test/unit", "gulp": "gulp"}, "main": "index", "directories": {"lib": "./lib"}, "browser": "lib/browser.js", "gitHead": "ee1c2ee1c333a1cbb122e3e385b60f051ea69706", "bugs": {"url": "https://github.com/theturtle32/WebSocket-Node/issues"}, "_id": "websocket@1.0.14", "_shasum": "1ef1ab300d7ccc619557367ce172e9cb83bdad49", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "dist": {"shasum": "1ef1ab300d7ccc619557367ce172e9cb83bdad49", "size": 50679, "noattachment": false, "tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.14.tgz", "integrity": "sha512-LwSid/9pDUY175A+l1Xx14Vve7dxLPqOjdHk8IClxJMrYUgJ2Qfr3ZC0E+LOGK+s3JSOBq8ZgVmxySXgxvw0KA=="}, "publish_time": 1417644513371, "_hasShrinkwrap": false, "_cnpm_publish_time": 1417644513371, "_cnpmcore_publish_time": "2021-12-15T18:09:16.719Z", "hasInstallScript": true}, "1.0.13": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push", "RFC-6455", "realtime", "server", "client"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.worlize.com/"}, "version": "1.0.13", "repository": {"type": "git", "url": "https://github.com/theturtle32/WebSocket-Node.git"}, "homepage": "https://github.com/theturtle32/WebSocket-Node", "engines": {"node": ">=0.8.0"}, "dependencies": {"debug": "~2.1.0", "nan": "~1.0.0", "typedarray-to-buffer": "~3.0.0"}, "devDependencies": {"faucet": "0.0.1", "gulp": "git+https://github.com/gulpjs/gulp.git#4.0", "gulp-jshint": "^1.9.0", "jshint-stylish": "^1.0.0", "tape": "^3.0.0"}, "config": {"verbose": false}, "scripts": {"install": "(node-gyp rebuild 2> builderror.log) || (exit 0)", "test": "faucet test/unit", "gulp": "gulp"}, "main": "index", "directories": {"lib": "./lib"}, "browser": "lib/browser.js", "gitHead": "08fc659153f9f77744b97e5db307278a580c105c", "bugs": {"url": "https://github.com/theturtle32/WebSocket-Node/issues"}, "_id": "websocket@1.0.13", "_shasum": "5781ca772a0e6b57c36dadee6f2c714748f14124", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "dist": {"shasum": "5781ca772a0e6b57c36dadee6f2c714748f14124", "size": 50408, "noattachment": false, "tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.13.tgz", "integrity": "sha512-rD/0GCtqPIjd2h37I9LlanalOI78EADATvqlp+LfGXntEKxBfl0VxwRnGlFHAOrwDuFZ5JLFs1m1VaNVEeU7MQ=="}, "publish_time": 1417291951160, "_hasShrinkwrap": false, "_cnpm_publish_time": 1417291951160, "_cnpmcore_publish_time": "2021-12-15T18:09:17.347Z", "hasInstallScript": true}, "1.0.12": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push", "RFC-6455", "realtime", "server", "client"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.worlize.com/"}, "version": "1.0.12", "repository": {"type": "git", "url": "https://github.com/theturtle32/WebSocket-Node.git"}, "homepage": "https://github.com/theturtle32/WebSocket-Node", "engines": {"node": ">=0.8.0"}, "dependencies": {"debug": "~2.1.0", "nan": "~1.0.0", "typedarray-to-buffer": "~3.0.0"}, "devDependencies": {"faucet": "0.0.1", "gulp": "git+https://github.com/gulpjs/gulp.git#4.0", "gulp-jshint": "^1.9.0", "jshint-stylish": "^1.0.0", "tape": "^3.0.0"}, "config": {"verbose": false}, "scripts": {"install": "(node-gyp rebuild 2> builderror.log) || (exit 0)", "test": "faucet test/unit", "gulp": "gulp"}, "main": "index", "directories": {"lib": "./lib"}, "browser": "lib/browser.js", "gitHead": "02b354c5b3683be8cc92ff34d25ebd8a3f2ba2f3", "bugs": {"url": "https://github.com/theturtle32/WebSocket-Node/issues"}, "_id": "websocket@1.0.12", "_shasum": "9bdcadca919d666db134d24129d38fd583c08b90", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "dist": {"shasum": "9bdcadca919d666db134d24129d38fd583c08b90", "size": 50080, "noattachment": false, "tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.12.tgz", "integrity": "sha512-a+FAuLrMh52RvmQpLH5SAm0lI24lcZ9K/aPZLmujC3ASqrbkR+ERx265MbxHVv8XXq4AGGZhJcWBzCHVeBYokg=="}, "publish_time": 1417204199445, "_hasShrinkwrap": false, "_cnpm_publish_time": 1417204199445, "_cnpmcore_publish_time": "2021-12-15T18:09:17.601Z", "hasInstallScript": true}, "1.0.11": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push", "RFC-6455", "realtime", "server", "client"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.worlize.com/"}, "version": "1.0.11", "repository": {"type": "git", "url": "http://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.8.0"}, "dependencies": {"debug": "~2.1.0", "nan": "~1.0.0", "typedarray-to-buffer": "~3.0.0"}, "devDependencies": {"faucet": "0.0.1", "gulp": "git+https://github.com/gulpjs/gulp.git#4.0", "gulp-jshint": "^1.9.0", "jshint-stylish": "^1.0.0", "tape": "^3.0.0"}, "config": {"verbose": false}, "scripts": {"install": "(node-gyp rebuild 2> builderror.log) || (exit 0)", "test": "faucet test/unit", "gulp": "gulp"}, "main": "index", "directories": {"lib": "./lib"}, "browser": "lib/browser.js", "gitHead": "8999be41ea81f1bd3a0badafbd0f0fb0a7fe1d4b", "bugs": {"url": "https://github.com/Worlize/WebSocket-Node/issues"}, "homepage": "https://github.com/Worlize/WebSocket-Node", "_id": "websocket@1.0.11", "_shasum": "4d51ab79140013cb6f2ee8d0879cd7b32bb2e610", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "dist": {"shasum": "4d51ab79140013cb6f2ee8d0879cd7b32bb2e610", "size": 49955, "noattachment": false, "tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.11.tgz", "integrity": "sha512-+OydwXZGyKZAieHPTjcL5QvPXKKlq6XQoX51tCHwlSXnJOZgeiZnfgz79JglIi3RBsAd9f/R4b4m9AAxCgbbpQ=="}, "publish_time": 1416953982307, "_hasShrinkwrap": false, "_cnpm_publish_time": 1416953982307, "_cnpmcore_publish_time": "2021-12-15T18:09:17.957Z", "hasInstallScript": true}, "1.0.10": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push", "RFC-6455", "realtime", "server", "client"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.worlize.com/"}, "version": "1.0.10", "repository": {"type": "git", "url": "http://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.8.0"}, "dependencies": {"nan": "~1.0.0"}, "config": {"verbose": false}, "scripts": {"install": "(node-gyp rebuild 2> builderror.log) || (exit 0)"}, "main": "index", "directories": {"lib": "./lib"}, "browser": "lib/browser.js", "gitHead": "f8b0d56c5a6d86d2067697446d06bfd8c51157a7", "bugs": {"url": "https://github.com/Worlize/WebSocket-Node/issues"}, "homepage": "https://github.com/Worlize/WebSocket-Node", "_id": "websocket@1.0.10", "_shasum": "a57532a077838814482eac4042fde17670a9d961", "_from": ".", "_npmVersion": "2.1.4", "_nodeVersion": "0.10.32", "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "dist": {"shasum": "a57532a077838814482eac4042fde17670a9d961", "size": 54503, "noattachment": false, "tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.10.tgz", "integrity": "sha512-68rMwE818gRbomT5Wa1yL4YC10P8wZSF845FCf8V1iGZ+HyUGSEJR7hYQ6jMIWXjVNYqRPbEVJRfvz7SKCjCsQ=="}, "publish_time": 1414084615648, "_hasShrinkwrap": false, "_cnpm_publish_time": 1414084615648, "_cnpmcore_publish_time": "2021-12-15T18:09:18.239Z", "hasInstallScript": true}, "1.0.9": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push", "RFC-6455", "realtime", "server", "client"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.worlize.com/"}, "version": "1.0.9", "repository": {"type": "git", "url": "http://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.8.0"}, "dependencies": {"nan": "~1.0.0"}, "config": {"verbose": false}, "scripts": {"install": "(node-gyp rebuild 2> builderror.log) || (exit 0)"}, "main": "index", "directories": {"lib": "./lib"}, "browser": "lib/browser.js", "gitHead": "6faf885b5d97bfde1d6d5fa12374b18b2a1e9ff3", "bugs": {"url": "https://github.com/Worlize/WebSocket-Node/issues"}, "homepage": "https://github.com/Worlize/WebSocket-Node", "_id": "websocket@1.0.9", "_shasum": "5f92f4b59bf898e6b5e4adbba6ae8faf44065369", "_from": ".", "_npmVersion": "2.1.4", "_nodeVersion": "0.10.32", "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "dist": {"shasum": "5f92f4b59bf898e6b5e4adbba6ae8faf44065369", "size": 54157, "noattachment": false, "tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.9.tgz", "integrity": "sha512-N2SMexcM7/dgrH6Y4i7w2aAFnexfT0nh7MQCZaNNXdKle0IXucPcNo316b8PdSg1yD33tMVkUKndIn0YVIOxPQ=="}, "publish_time": 1413829296732, "_hasShrinkwrap": false, "_cnpm_publish_time": 1413829296732, "_cnpmcore_publish_time": "2021-12-15T18:09:18.653Z", "hasInstallScript": true}, "1.0.8": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.worlize.com/"}, "version": "1.0.8", "repository": {"type": "git", "url": "http://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.6.10"}, "config": {"verbose": false}, "scripts": {"install": "node install.js"}, "main": "index", "directories": {"lib": "./lib"}, "readmeFilename": "README.md", "_id": "websocket@1.0.8", "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.8.tgz", "shasum": "41b05a737dfde899125d3343d0442a3cc67a1b00", "size": 45256, "noattachment": false, "integrity": "sha512-DHfg+jOh0MQ7rpMpKy+Y0hH9ul4kwCYA8ibIwzk3RfQXhQCg6JtuPITwOua6ijidNp5beqhVl6XZyvRoB60Y8Q=="}, "_npmVersion": "1.1.69", "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "publish_time": 1356715424596, "_hasShrinkwrap": false, "_cnpm_publish_time": 1356715424596, "_cnpmcore_publish_time": "2021-12-15T18:09:19.013Z", "hasInstallScript": true}, "1.0.7": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.worlize.com/"}, "version": "1.0.7", "repository": {"type": "git", "url": "git://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.6.10"}, "config": {"verbose": false}, "scripts": {"install": "node install.js"}, "main": "index", "directories": {"lib": "./lib"}, "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "_id": "websocket@1.0.7", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.21", "_nodeVersion": "v0.6.18", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.7.tgz", "shasum": "1b3b21a04d2b6097541e4986724bac7b29e43045", "size": 43871, "noattachment": false, "integrity": "sha512-0OLtD3B+w3ajsBPMi4woXuakjqQlKUTaRaSpk53lzBtbhGXvTEuitKtqGYgvclMh1Db0yX1grYWPu2U1gRJrHg=="}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "publish_time": 1344813797579, "_hasShrinkwrap": false, "_cnpm_publish_time": 1344813797579, "_cnpmcore_publish_time": "2021-12-15T18:09:19.283Z", "hasInstallScript": true}, "1.0.6": {"name": "websocket", "description": "Websocket Client & Server Library tracking the latest protocol drafts from the IETF.", "keywords": ["websocket", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "1.0.6", "repository": {"type": "git", "url": "git://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.6.13"}, "main": "index", "directories": {"lib": "./lib"}, "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "_id": "websocket@1.0.6", "scripts": {"install": "node-gyp rebuild"}, "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.9", "_nodeVersion": "v0.6.13", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.6.tgz", "shasum": "47bc747bdbad3fa7c83978bf86293b4683340d1c", "size": 231281, "noattachment": false, "integrity": "sha512-DgsNbBuxXKN2/ZNKlZFa2aQkecZxeDvZJYOijjf8XYQn5/vUorWu2+DbAGkfFibqtyqHV5FXyzyxNF+Q9zUf5A=="}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "publish_time": 1337676206428, "_hasShrinkwrap": false, "_cnpm_publish_time": 1337676206428, "_cnpmcore_publish_time": "2021-12-15T18:09:19.801Z", "hasInstallScript": true}, "1.0.5": {"name": "websocket", "description": "Websocket Client & Server Library tracking the latest protocol drafts from the IETF.", "keywords": ["websocket", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "1.0.5", "repository": {"type": "git", "url": "git://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.6.0"}, "scripts": {"preinstall": "node-gyp configure", "install": "node-gyp build", "preuninstall": "node-gyp clean"}, "main": "index", "directories": {"lib": "./lib"}, "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "_id": "websocket@1.0.5", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0-beta-4", "_nodeVersion": "v0.6.6", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.5.tgz", "shasum": "334085f87e936efd8d8759aa48ce1cd0e7ea43b1", "size": 97060, "noattachment": false, "integrity": "sha512-Uuo91XHt1+CTR9nJv7bPkXiKyqGWgrRZXiVc/4jqX0vTipLkVLkdq7KVscml24fCVQfaL0uGgvkD2JWhBPUyXQ=="}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "publish_time": 1337638580458, "_hasShrinkwrap": false, "_cnpm_publish_time": 1337638580458, "_cnpmcore_publish_time": "2021-12-15T18:09:20.216Z", "hasInstallScript": true}, "1.0.4": {"name": "websocket", "description": "Websocket Client & Server Library tracking the latest protocol drafts from the IETF.", "keywords": ["websocket", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "1.0.4", "repository": {"type": "git", "url": "git://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.4.7"}, "scripts": {"preinstall": "make validator"}, "main": "index", "directories": {"lib": "./lib"}, "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "_id": "websocket@1.0.4", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0-beta-4", "_nodeVersion": "v0.6.6", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.4.tgz", "shasum": "1ceb545541dce5531c12187b80de902718a6afd2", "size": 75719, "noattachment": false, "integrity": "sha512-3gi0C9xr5CdhUkFo1lYif30SVAVLWijPOxCF+cBsvlQP7tKxGjmFzeA9L0rsW+TZKD9gK7sSAvmuCXOU2KMbmw=="}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "publish_time": 1324263337055, "_hasShrinkwrap": false, "_cnpm_publish_time": 1324263337055, "_cnpmcore_publish_time": "2021-12-15T18:09:20.690Z", "hasInstallScript": true}, "1.0.3": {"name": "websocket", "description": "Websocket Client & Server Library tracking the latest protocol drafts from the IETF.", "keywords": ["websocket", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "1.0.3", "repository": {"type": "git", "url": "git://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.4.7"}, "main": "index", "directories": {"lib": "./lib"}, "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "_id": "websocket@1.0.3", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0-alpha-2", "_nodeVersion": "v0.6.3", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.3.tgz", "shasum": "2808b52040fcc3ede15c75d4c097e9a9ad74c178", "size": 71343, "noattachment": false, "integrity": "sha512-mrFxAv/Fjy9yyTYegTiFSOsyxrxnCw6SaXK7oP/zGOU9WeLnKjwmz+vCOFTus3IN2kMf4T/opBlMHOJYApXA1Q=="}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "publish_time": 1324203589737, "_hasShrinkwrap": false, "_cnpm_publish_time": 1324203589737, "_cnpmcore_publish_time": "2021-12-15T18:09:21.023Z"}, "1.0.2": {"name": "websocket", "description": "Websocket Client & Server Library tracking the latest protocol drafts from the IETF.", "keywords": ["websocket", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "1.0.2", "repository": {"type": "git", "url": "git://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.4.7"}, "main": "index", "directories": {"lib": "./lib"}, "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "_id": "websocket@1.0.2", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.6.2", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.2.tgz", "shasum": "68157fdb86f60ab241f59b6b9e4f9b1e33be0c5f", "size": 71680, "noattachment": false, "integrity": "sha512-JJgq1OU5v5fUQi/HYCmlas0a3IBjlGv3Ig2+GmLqSlK2YmcN23SHaGxv48MVfCUvvGKTwGj1dHyHxWWJBbd6iw=="}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "publish_time": 1322519876321, "_hasShrinkwrap": false, "_cnpm_publish_time": 1322519876321, "_cnpmcore_publish_time": "2021-12-15T18:09:21.367Z"}, "1.0.1": {"name": "websocket", "description": "Websocket Client & Server Library tracking the latest protocol drafts from the IETF.", "keywords": ["websocket", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "1.0.1", "repository": {"type": "git", "url": "git://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.4.7"}, "main": "index", "directories": {"lib": "./lib"}, "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "_id": "websocket@1.0.1", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.6.2", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.1.tgz", "shasum": "e11abc9502cefb5db6d921a40589463aee24df58", "size": 71680, "noattachment": false, "integrity": "sha512-qgYTA+bxkHjcx+iuuHvfk+rrUVIdJMiomxjSPgSNJ1/qB8ITaIKeexaYpkt7yb2NjkHyYHi2XmH9rO+H2nL9qw=="}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "publish_time": 1321947317281, "_hasShrinkwrap": false, "_cnpm_publish_time": 1321947317281, "_cnpmcore_publish_time": "2021-12-15T18:09:21.680Z"}, "1.0.0": {"name": "websocket", "description": "Websocket Client & Server Library tracking the latest protocol drafts from the IETF.", "keywords": ["websocket", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "1.0.0", "repository": {"type": "git", "url": "git://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.4.7"}, "main": "index", "directories": {"lib": "./lib"}, "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "_id": "websocket@1.0.0", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.97", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.0.tgz", "shasum": "725eb2f2cd1fbd5439853c95fb609d1abab6639a", "size": 71680, "noattachment": false, "integrity": "sha512-GxP5enuiyIwYXqkhzJucv9QuW7FWuq7aAMLUP/VXhOubShon5IDpq2pESOs+H+jvhroJwbAILBhRqlLhpjPNCg=="}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "publish_time": 1319628352259, "_hasShrinkwrap": false, "_cnpm_publish_time": 1319628352259, "_cnpmcore_publish_time": "2021-12-15T18:09:22.036Z"}, "0.0.20": {"name": "websocket", "description": "Websocket Client & Server Library tracking the latest protocol drafts from the IETF.", "keywords": ["websocket", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.0.20", "repository": {"type": "git", "url": "git://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.4.7"}, "main": "index", "directories": {"lib": "./lib"}, "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "_id": "websocket@0.0.20", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.97", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-0.0.20.tgz", "shasum": "569dcde3c524fbd2578d9e69a6189ac9e020f4ff", "size": 71680, "noattachment": false, "integrity": "sha512-ZWVIHOS+DonRhbntCAikipYNUMvK1oL9ARxpkYMkcsv0LYx4TIJGurfXVIZVtTkdd0id0RPtBGSZSobxDFIh7g=="}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "publish_time": 1319099328776, "_hasShrinkwrap": false, "_cnpm_publish_time": 1319099328776, "_cnpmcore_publish_time": "2021-12-15T18:09:22.781Z"}, "0.0.19": {"name": "websocket", "description": "Websocket Client & Server Library tracking the latest protocol drafts from the IETF.", "keywords": ["websocket", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.0.19", "repository": {"type": "git", "url": "git://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.4.7"}, "main": "index", "directories": {"lib": "./lib"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/websocket/0.0.19/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "websocket@0.0.19", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.23", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-0.0.19.tgz", "shasum": "dde4a56108bd8edb7bd750e7eef6d679229c8821", "size": 66637, "noattachment": false, "integrity": "sha512-zFwlHrjNCDoKpGEAlu2m5dLUadehUKoAV2bXm3xku67Y9Er+fx+iB6kzzBwepwq8etFzb4Q7gHamv20moFfhMg=="}, "scripts": {}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "publish_time": 1318367770076, "_hasShrinkwrap": false, "_cnpm_publish_time": 1318367770076, "_cnpmcore_publish_time": "2021-12-15T18:09:23.122Z"}, "0.0.18": {"name": "websocket", "description": "Websocket Client & Server Library tracking the latest protocol drafts from the IETF.", "keywords": ["websocket", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.0.18", "repository": {"type": "git", "url": "git://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.4.7"}, "main": "index", "directories": {"lib": "./lib"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/websocket/0.0.18/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "websocket@0.0.18", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.23", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-0.0.18.tgz", "shasum": "506790d6d18be585208ed7be4b80582eb7120dae", "size": 66641, "noattachment": false, "integrity": "sha512-Bm5ZeMyHZsTjxngkhDcg0NHnzcOBLKgNEIIdHoNdP0dQDnYEVcl8KQN+QaLqvlyNEM4UFCpzZ+T7Tzfl1Dv4ng=="}, "scripts": {}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "publish_time": 1318367685409, "_hasShrinkwrap": false, "_cnpm_publish_time": 1318367685409, "_cnpmcore_publish_time": "2021-12-15T18:09:23.498Z"}, "0.0.17": {"name": "websocket", "description": "Websocket Client & Server Library tracking the latest protocol drafts from the IETF.", "keywords": ["websocket", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.0.17", "repository": {"type": "git", "url": "git://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.4.7"}, "main": "index", "directories": {"lib": "./lib"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/websocket/0.0.17/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "websocket@0.0.17", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.23", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-0.0.17.tgz", "shasum": "a7dcc3dd6eb1a15c7675954b31f470dcb62c63f5", "size": 65949, "noattachment": false, "integrity": "sha512-pZ8qv9AcherkKBtDZJ1cKhZcYuQHvVSxKEhpt64Mz72EkMoFzevBIe9oeHTf7UYIMwge4rrPZ54GNpJNfPPTmQ=="}, "scripts": {}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "publish_time": 1316980085758, "_hasShrinkwrap": false, "_cnpm_publish_time": 1316980085758, "_cnpmcore_publish_time": "2021-12-15T18:09:23.826Z"}, "0.0.16": {"name": "websocket", "description": "Websocket Client & Server Library tracking the latest protocol drafts from the IETF.", "keywords": ["websocket", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.0.16", "repository": {"type": "git", "url": "git://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.4.7"}, "main": "index", "directories": {"lib": "./lib"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/websocket/0.0.16/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "websocket@0.0.16", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.23", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-0.0.16.tgz", "shasum": "bc9c85d9b7b3acc08fbfeb8a890d3c13e2608e3d", "size": 65950, "noattachment": false, "integrity": "sha512-aKwKwGISgP+S7/uGyf5a1aALn6lHrJbBkb//FFkPTE0AWJoa5i0mjaQIM+WUoPTLrJNt1m/LvaoG5/n4UT4Miw=="}, "scripts": {}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "publish_time": 1315255654105, "_hasShrinkwrap": false, "_cnpm_publish_time": 1315255654105, "_cnpmcore_publish_time": "2021-12-15T18:09:24.118Z"}, "0.0.15": {"name": "websocket", "description": "Websocket Client & Server Library tracking the latest protocol drafts from the IETF.", "keywords": ["websocket", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.0.15", "repository": {"type": "git", "url": "git://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.4.7"}, "main": "index", "directories": {"lib": "./lib"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/websocket/0.0.15/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "websocket@0.0.15", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.23", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-0.0.15.tgz", "shasum": "7efa1fd4f4693bac1ac20066e48887c00db2cb5b", "size": 65926, "noattachment": false, "integrity": "sha512-ktZnyaHXxmylKTVU9q4KtVaDnQhwk0VIaA5PFXbBxEydt+vVjttureA99bI420wo0TQSr/umq5CpzGv/Xk5n0A=="}, "scripts": {}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "publish_time": 1314743833100, "_hasShrinkwrap": false, "_cnpm_publish_time": 1314743833100, "_cnpmcore_publish_time": "2021-12-15T18:09:24.503Z"}, "0.0.14": {"name": "websocket", "description": "Websocket Client & Server Library tracking the latest protocol drafts from the IETF.", "keywords": ["websocket", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.0.14", "repository": {"type": "git", "url": "git://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.4.7"}, "main": "index", "directories": {"lib": "./lib"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/websocket/0.0.14/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "websocket@0.0.14", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.23", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-0.0.14.tgz", "shasum": "8f4dba633d71afa768d495bb2f5955924ae6c75c", "size": 65125, "noattachment": false, "integrity": "sha512-E+etnwwIFi09A9Ow5oWyelXF9vkdnZfRIRrZKDhilgXuC4eEwwLSdU0HC5/gGprhbVFeYOJC+vD0g9NmNYzcGQ=="}, "scripts": {}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "publish_time": 1314251011961, "_hasShrinkwrap": false, "_cnpm_publish_time": 1314251011961, "_cnpmcore_publish_time": "2021-12-15T18:09:24.805Z"}, "0.0.13": {"name": "websocket", "description": "Websocket Client & Server Library tracking the latest protocol drafts from the IETF.", "keywords": ["websocket", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.0.13", "repository": {"type": "git", "url": "git://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.4.7"}, "main": "index", "directories": {"lib": "./lib"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/websocket/0.0.13/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "websocket@0.0.13", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.23", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-0.0.13.tgz", "shasum": "7172155e0bacf76c1a81a17a195bd2318133a92e", "size": 60560, "noattachment": false, "integrity": "sha512-KitHHgZdBE1NwhAa8+AiImHH/DBSXloRUDfYLjorw0w5hi+wzBg9R8flIJC4bVnwryn85xdXqWHQIuUCvylMEQ=="}, "scripts": {}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "publish_time": 1313711249899, "_hasShrinkwrap": false, "_cnpm_publish_time": 1313711249899, "_cnpmcore_publish_time": "2021-12-15T18:09:25.128Z"}, "0.0.12": {"name": "websocket", "description": "Websocket Client & Server Library tracking the latest protocol drafts from the IETF.", "keywords": ["websocket", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.0.12", "repository": {"type": "git", "url": "git://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.4.7"}, "main": "index", "directories": {"lib": "./lib"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/websocket/0.0.12/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "websocket@0.0.12", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.23", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-0.0.12.tgz", "shasum": "d430596719b537eddfb068fee59b184b0c2b34d0", "size": 59789, "noattachment": false, "integrity": "sha512-Bs8N/9S3ZFtsktfrSVDwOMA/qZXxzHq73N1Yq2sfrKi0QmZ+GkxgLgOIwFV+eNGzdc0pvE/wcLkGmx3LA7YHRg=="}, "scripts": {}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "publish_time": 1313122270739, "_hasShrinkwrap": false, "_cnpm_publish_time": 1313122270739, "_cnpmcore_publish_time": "2021-12-15T18:09:25.505Z"}, "0.0.11": {"name": "websocket", "description": "Websocket Client & Server Library tracking the latest protocol drafts from the IETF.", "keywords": ["websocket", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.0.11", "repository": {"type": "git", "url": "git://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.4.7"}, "main": "index", "directories": {"lib": "./lib"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/websocket/0.0.11/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "websocket@0.0.11", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.23", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-0.0.11.tgz", "shasum": "2bbecb3fde1fa34894fa089de092dd3082966a6d", "size": 60196, "noattachment": false, "integrity": "sha512-3RPTQ5998duxisgeawHyxMRzf3vw7FMiWtVQGz0lT0O8jJXgKpQED15XupzXADpQ2ZJFGfjmW+0cuiBCm/VQrw=="}, "scripts": {}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "publish_time": 1313114543844, "_hasShrinkwrap": false, "_cnpm_publish_time": 1313114543844, "_cnpmcore_publish_time": "2021-12-15T18:09:26.015Z"}, "0.0.10": {"name": "websocket", "description": "Websocket Client & Server Library tracking the latest protocol drafts from the IETF.", "keywords": ["websocket", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.0.10", "repository": {"type": "git", "url": "git://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.4.7"}, "main": "index", "directories": {"lib": "./lib"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/websocket/0.0.10/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "websocket@0.0.10", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.23", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-0.0.10.tgz", "shasum": "5a5e2db24837c6583078c925a0f905c2c86dcfaa", "size": 60216, "noattachment": false, "integrity": "sha512-Ducv7ics1Pl+56Ck6jF218JsPwdhQUBOjG4nkH4S6Zv/BgNgDPLwddzEXqtuFVSKuBBwxXf8HBRyz7ieMo3Yzg=="}, "scripts": {}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "publish_time": 1312940763649, "_hasShrinkwrap": false, "_cnpm_publish_time": 1312940763649, "_cnpmcore_publish_time": "2021-12-15T18:09:26.485Z"}, "0.0.9": {"name": "websocket", "description": "Websocket Client & Server Library tracking the latest protocol drafts from the IETF.", "keywords": ["websocket", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.0.9", "repository": {"type": "git", "url": "git://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.4.7"}, "main": "index", "directories": {"lib": "./lib"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/websocket/0.0.9/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "websocket@0.0.9", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.23", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-0.0.9.tgz", "shasum": "b2ab26a9454d61e529fdc861ce1d27236f385379", "size": 60185, "noattachment": false, "integrity": "sha512-DIqLNC092wYxPs8OGAWEl0NBzhbs5rg8ukHbvUSUAO5cCSVSy7v/ifqKAPS2aXbv2ulxxh1soe5FVrm90qnjQA=="}, "scripts": {}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "publish_time": 1312854278024, "_hasShrinkwrap": false, "_cnpm_publish_time": 1312854278024, "_cnpmcore_publish_time": "2021-12-15T18:09:26.757Z"}, "0.0.8": {"name": "websocket", "description": "Websocket Client & Server Library tracking the latest protocol drafts from the IETF.", "keywords": ["websocket", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.0.8", "repository": {"type": "git", "url": "git://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.4.7"}, "main": "index", "directories": {"lib": "./lib"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/websocket/0.0.8/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "websocket@0.0.8", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.15", "_nodeVersion": "v0.4.9", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-0.0.8.tgz", "shasum": "bc4b6df3c631bde5e1f7cfb373c919f95df55157", "size": 60180, "noattachment": false, "integrity": "sha512-8/zBR4g9Fs+HYXwx7gkgs9qS74SF19KOOBOZkFO7uc0GMNgABi3VDnVeK5Lwp1CR03NSiUl2XmkugnR/vOgZlA=="}, "scripts": {}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "publish_time": 1312239484540, "_hasShrinkwrap": false, "_cnpm_publish_time": 1312239484540, "_cnpmcore_publish_time": "2021-12-15T18:09:27.267Z"}, "0.0.7": {"name": "websocket", "description": "Websocket Client & Server Library tracking the latest protocol drafts from the IETF.", "keywords": ["websocket", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.0.7", "repository": {"type": "git", "url": "git://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.4.7"}, "main": "index", "directories": {"lib": "./lib"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/websocket/0.0.7/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "websocket@0.0.7", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.15", "_nodeVersion": "v0.4.9", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-0.0.7.tgz", "shasum": "411d84853e41359614c9fda483c35234859df87d", "size": 59673, "noattachment": false, "integrity": "sha512-ux+xrUhpXS4/JL9xHicCEACLYl1k3tcLzuU2SvDiJaZE6QbUUloaf7Yiu4usGDhY/nqe15QigK7gCH4WLifKqQ=="}, "scripts": {}, "publish_time": 1311389156734, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1311389156734, "_cnpmcore_publish_time": "2021-12-15T18:09:27.586Z"}, "0.0.6": {"name": "websocket", "description": "Websocket Client & Server Library tracking the latest protocol drafts from the IETF.", "keywords": ["websocket", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.0.6", "repository": {"type": "git", "url": "git://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.4.7"}, "main": "index", "directories": {"lib": "./lib"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/websocket/0.0.6/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "websocket@0.0.6", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.15", "_nodeVersion": "v0.4.9", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-0.0.6.tgz", "shasum": "2938084f18d693c8826cbdfc902edf26c0b7ed08", "size": 56550, "noattachment": false, "integrity": "sha512-cUiR+nHyL3CBX8JqD0ngRQx6waNuiZ8TE7BSgFfNwUGvuaMUriPFdBcHlwdl6mMtwVPxBhacA8cvJm4t7xPf2Q=="}, "scripts": {}, "publish_time": 1311132350541, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1311132350541, "_cnpmcore_publish_time": "2021-12-15T18:09:27.884Z"}, "0.0.5": {"name": "websocket", "description": "Websocket Client & Server Library tracking the latest protocol drafts from the IETF.", "keywords": ["websocket", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.0.5", "repository": {"type": "git", "url": "git://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.4.7"}, "main": "index", "directories": {"lib": "./lib"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/websocket/0.0.5/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "websocket@0.0.5", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.15", "_nodeVersion": "v0.4.9", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-0.0.5.tgz", "shasum": "48d3856b57f24618398f4b30cf19cdd061b22134", "size": 56547, "noattachment": false, "integrity": "sha512-/nn4MR80+JbG4ULiryfcHbYtR6lO343qLktpDfNAYXulrd4v3GWbSaPJQGXtv/TekNfNkUNQV25RHxCDndtp+w=="}, "scripts": {}, "publish_time": 1311126580651, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1311126580651, "_cnpmcore_publish_time": "2021-12-15T18:09:28.184Z"}, "0.0.4": {"name": "websocket", "description": "Websocket Client & Server Library tracking the latest protocol drafts from the IETF.", "keywords": ["websocket", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.0.4", "repository": {"type": "git", "url": "git://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.4.7"}, "main": "index", "directories": {"lib": "./lib"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/websocket/0.0.4/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "websocket@0.0.4", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.15", "_nodeVersion": "v0.4.9", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-0.0.4.tgz", "shasum": "19f3ac34985370395c3ff65183a37c4c7eaaa7fa", "size": 56573, "noattachment": false, "integrity": "sha512-FinVeSXJu+/4gbllfBUpA992DBLUaWEHi93vGwanPwR5CKLGOmQjv+f07y0Yd1bXRf3I/Kn58DJUEvvDhHxnhQ=="}, "scripts": {}, "publish_time": 1311033130397, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1311033130397, "_cnpmcore_publish_time": "2021-12-15T18:09:28.608Z"}, "0.0.3": {"name": "websocket", "description": "Websocket Client & Server Library tracking the latest protocol drafts from the IETF.", "keywords": ["websocket", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.0.3", "repository": {"type": "git", "url": "git://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.4.7"}, "main": "index", "directories": {"lib": "./lib"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/websocket/0.0.3/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "websocket@0.0.3", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.15", "_nodeVersion": "v0.4.9", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-0.0.3.tgz", "shasum": "db7f90f3ca13cdd428ff70013cc76f9eb728c3be", "size": 56581, "noattachment": false, "integrity": "sha512-xI2H/IGO/3KhtgGlUEYCUpV8mv7slX/y/yqHWsRCgNP/r3tjsFWvD8uQtCZDnWw69kFbM+K3EAG4rfOn2INaIw=="}, "scripts": {}, "publish_time": 1311022940583, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1311022940583, "_cnpmcore_publish_time": "2021-12-15T18:09:28.952Z"}, "0.0.2": {"name": "websocket", "description": "Websocket Client & Server Library tracking the latest protocol drafts from the IETF.", "keywords": ["websocket", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.0.2", "repository": {"type": "git", "url": "git://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.4.7"}, "main": "index", "directories": {"lib": "./lib"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/websocket/0.0.2/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "websocket@0.0.2", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.15", "_nodeVersion": "v0.4.9", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-0.0.2.tgz", "shasum": "11b9b3cc82f34aa7d07c9d5667134aebc30cfcc7", "size": 54358, "noattachment": false, "integrity": "sha512-UWgIlkJOYkM/BYEqgfuJQOQSRT+hFEWdD0qeb/9gIVjkSODkGjwG9TfA7B9ubBfNXjJrk/P1WIJrfPZAwB7tGw=="}, "scripts": {}, "publish_time": 1310969042923, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1310969042923, "_cnpmcore_publish_time": "2021-12-15T18:09:29.294Z"}, "0.0.1": {"name": "websocket", "description": "Websocket Client & Server Library tracking the latest protocol drafts from the IETF.", "keywords": ["websocket", "socket", "networking", "comet", "push"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "0.0.1", "repository": {"type": "git", "url": "git://github.com/Worlize/WebSocket-Node.git"}, "engines": {"node": ">=0.4.7"}, "main": "index", "directories": {"lib": "./lib"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/websocket/0.0.1/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "websocket@0.0.1", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.15", "_nodeVersion": "v0.4.9", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/websocket/-/websocket-0.0.1.tgz", "shasum": "67a8c53140d29a5e52f3ea881ee813a18df7dca0", "size": 53547, "noattachment": false, "integrity": "sha512-bLWSA+362188Ol66pTXQF69S5kDMB+Gzdh3E2dTQHctVoSiDvidyEXpLHE+1LOr2c9AO0RE8PgAVF8As4HeZcA=="}, "scripts": {}, "publish_time": 1309910190823, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1309910190823, "_cnpmcore_publish_time": "2021-12-15T18:09:29.619Z"}, "1.0.35": {"name": "websocket", "description": "Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push", "RFC-6455", "realtime", "server", "client"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/theturtle32"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://dev.sipdoc.net"}], "version": "1.0.35", "repository": {"type": "git", "url": "git+https://github.com/theturtle32/WebSocket-Node.git"}, "homepage": "https://github.com/theturtle32/WebSocket-Node", "engines": {"node": ">=4.0.0"}, "dependencies": {"bufferutil": "^4.0.1", "debug": "^2.2.0", "es5-ext": "^0.10.63", "typedarray-to-buffer": "^3.1.5", "utf-8-validate": "^5.0.2", "yaeti": "^0.0.6"}, "devDependencies": {"buffer-equal": "^1.0.0", "gulp": "^4.0.2", "gulp-jshint": "^2.0.4", "jshint-stylish": "^2.2.1", "jshint": "^2.0.0", "tape": "^4.9.1"}, "config": {"verbose": false}, "scripts": {"test": "tape test/unit/*.js", "gulp": "gulp"}, "main": "index", "directories": {"lib": "./lib"}, "browser": "lib/browser.js", "license": "Apache-2.0", "_id": "websocket@1.0.35", "gitHead": "162a07a0cd38aee9280491cc3a2ab32b489b634b", "bugs": {"url": "https://github.com/theturtle32/WebSocket-Node/issues"}, "_nodeVersion": "20.11.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-/REy6amwPZl44DDzvRCkaI1q1bIiQB0mEFQLUrhz3z2EK91cp3n72rAjUlrTP0zV22HJIUOVHQGPxhFRjxjt+Q==", "shasum": "374197207d7d4cc4c36cbf8a1bb886ee52a07885", "tarball": "https://registry.npmmirror.com/websocket/-/websocket-1.0.35.tgz", "fileCount": 23, "unpackedSize": 154327, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC5XmiCsiAW7BVX+cq4JwaRVgKraKNAW+KzH+Fo98GS0wIhAN4tQ6+iEWGi4r3WTExJ1lMamfD8KWHIAQaiQBaw+mey"}], "size": 40735}, "_npmUser": {"name": "theturtle32", "email": "<EMAIL>"}, "maintainers": [{"name": "theturtle32", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/websocket_1.0.35_1715536090032_0.8468161674950581"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-12T17:48:10.186Z", "publish_time": 1715536090186, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/theturtle32/WebSocket-Node/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://dev.sipdoc.net"}], "homepage": "https://github.com/theturtle32/WebSocket-Node", "keywords": ["websocket", "websockets", "socket", "networking", "comet", "push", "RFC-6455", "realtime", "server", "client"], "repository": {"type": "git", "url": "git+https://github.com/theturtle32/WebSocket-Node.git"}, "_source_registry_name": "default"}