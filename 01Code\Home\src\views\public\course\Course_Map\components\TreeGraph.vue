<!-- src\views\public\course\Course_Map\components\TreeGraph.vue -->
<template>
  <!-- 树图容器 -->
  <div ref="graphContainer" class="tree-graph-container"></div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick, defineEmits, watch, defineProps } from 'vue'
// 替换原来的 import G6 from '@antv/g6'
import * as G6 from '@antv/g6'

// 定义组件接收的属性
const props = defineProps({
  data: {
    type: Object,
    default: () => ({
      id: 'root',
      label: '知识图谱',
      tags: [],
      children: []
    })
  }
})

// 定义组件触发的事件
const emit = defineEmits(['node-click'])

// 分支颜色配置（参考环形图分配，支持更多分支）
const branchColorsArr = [
  '#ff4d4f', // 红
  '#3b50f9', // 靛蓝
  '#feca57', // 黄
  '#f93b7c', // 粉
]

// 递归折叠除根节点和其直接子节点以外的所有节点
const collapseDeepNodes = (node, level = 0) => {
  if (node.children) {
    if (level >= 1) {
      node.collapsed = true
    }
    node.children.forEach(child => collapseDeepNodes(child, level + 1))
  }
}

// 递归为每个节点分配 branchKey，实现一级节点分配彩色
function assignBranchKey(node, branchKey = null, level = 0, branchIdx = 0) {
  node.level = level
  if (level === 0) {
    node.branchKey = null // 只有根节点深色
  } else if (level === 1) {
    node.branchKey = branchIdx // 一级节点分配彩色
  } else {
    node.branchKey = branchKey // 子孙节点继承一级节点色
  }
  if (node.children) {
    node.children.forEach((child, idx) => assignBranchKey(child, node.branchKey, level + 1, idx))
  }
}

// 树图容器引用
const graphContainer = ref(null)
// 树图实例
let graph = null

// 组件挂载后初始化树图
onMounted(async () => {
  await nextTick()
  initGraph()
  window.addEventListener('resize', handleResize)
})

// 组件卸载前销毁树图
onBeforeUnmount(() => {
  if (graph) graph.destroy()
  window.removeEventListener('resize', handleResize)
})

// 监听数据变化
watch(() => props.data, (newData) => {
  if (newData && graph) {
    // 深拷贝，避免响应式污染
    const processedData = JSON.parse(JSON.stringify(newData))
    collapseDeepNodes(processedData)
    assignBranchKey(processedData)
    graph.changeData(processedData)
    graph.fitCenter()
    graph.zoom(0.9)
  }
}, { deep: true })

// 处理窗口大小变化
const handleResize = () => {
  if (graph && graphContainer.value) {
    graph.changeSize(
      graphContainer.value.clientWidth,
      graphContainer.value.clientHeight
    )
    graph.fitCenter()
  }
}

// 初始化树图
const initGraph = () => {
  if (!graphContainer.value) return

  // 注册自定义节点
  G6.registerNode('custom-node', {
    draw(cfg, group) {
      const isRoot = (cfg.level === 0 || cfg.branchKey === null)
      const color = isRoot
        ? '#24285e'
        : branchColorsArr[cfg.branchKey % branchColorsArr.length] || '#8a6de3'

      const fontSize = 16
      const paddingX = 20

      const ctx = document.createElement('canvas').getContext('2d')
      ctx.font = `${fontSize}px sans-serif`
      const labelWidth = ctx.measureText(cfg.label).width

      const tags = cfg.tags || []
      let tagsWidth = 0
      const tagPadding = 16
      tags.forEach(tag => {
        tagsWidth += ctx.measureText(tag).width + tagPadding + 8
      })
      tagsWidth = Math.max(tagsWidth - 8, 0)

      const contentWidth = Math.max(labelWidth, tagsWidth)
      const nodeWidth = contentWidth + paddingX * 2
      const nodeHeight = 50

      const rect = group.addShape('rect', {
        attrs: {
          x: 0,
          y: 0,
          width: nodeWidth,
          height: nodeHeight,
          fill: color,
          radius: 16,
          shadowColor: '#ccc',
          shadowBlur: 6,
          cursor: 'pointer'
        }
      })

      group.addShape('text', {
        attrs: {
          x: nodeWidth / 2,
          y: nodeHeight / 2,
          text: cfg.label,
          fill: '#fff',
          fontSize,
          fontWeight: 'bold',
          textAlign: 'center',
          textBaseline: 'middle'
        }
      })

      if (tags.length) {
        let totalWidth = tagsWidth
        let startX = (nodeWidth - totalWidth) / 2
        const startY = nodeHeight + 10

        tags.forEach(tag => {
          const tagWidth = ctx.measureText(tag).width + tagPadding
          group.addShape('rect', {
            attrs: {
              x: startX,
              y: startY,
              width: tagWidth,
              height: 24,
              fill: '#f0f0f0',
              radius: 12
            }
          })
          group.addShape('text', {
            attrs: {
              x: startX + tagWidth / 2,
              y: startY + 12,
              text: tag,
              fill: '#666',
              fontSize: 12,
              textAlign: 'center',
              textBaseline: 'middle'
            }
          })
          startX += tagWidth + 8
        })
      }

      if (cfg.children && cfg.children.length > 0) {
        group.addShape('marker', {
          attrs: {
            x: nodeWidth + 10,
            y: nodeHeight / 2,
            r: 6,
            symbol: cfg.collapsed ? G6.Marker.expand : G6.Marker.collapse,
            fill: '#fff',
            stroke: '#666',
            cursor: 'pointer'
          },
          name: 'collapse-icon'
        })
      }

      return rect
    },
    getAnchorPoints() {
      return [
        [0, 0.5],
        [1, 0.5]
      ]
    },
    setState(name, value, item) {
      const group = item.getContainer()
      const rect = group.get('children')[0]
      if (name === 'highlight') {
        rect.attr('shadowColor', value ? '#f88c77' : '#ccc')
        rect.attr('shadowBlur', value ? 20 : 6)
      }
      if (name === 'dimmed') {
        rect.attr('opacity', value ? 0.3 : 1)
      }
    }
  })

  // 注册自定义边
  G6.registerEdge('custom-edge', {
    draw(cfg, group) {
      const { startPoint, endPoint } = cfg
      const path = [
        ['M', startPoint.x, startPoint.y],
        [
          'C',
          (startPoint.x + endPoint.x) / 2, startPoint.y,
          (startPoint.x + endPoint.x) / 2, endPoint.y,
          endPoint.x, endPoint.y
        ]
      ]

      // 连线颜色与 source 节点颜色保持一致
      let color = '#24285e'
      if (cfg.sourceNode) {
        const sourceModel = cfg.sourceNode.getModel()
        if (sourceModel.level === 0 || sourceModel.branchKey === null) {
          color = '#24285e'
        } else {
          color = branchColorsArr[sourceModel.branchKey % branchColorsArr.length] || '#8a6de3'
        }
      }

      return group.addShape('path', {
        attrs: {
          path,
          stroke: color,
          lineWidth: 3
        }
      })
    }
  })

  // 创建树图实例
  graph = new G6.TreeGraph({
    container: graphContainer.value,
    width: graphContainer.value.clientWidth,
    height: graphContainer.value.clientHeight,
    modes: {
      default: ['drag-canvas', 'zoom-canvas']
    },
    defaultNode: {
      type: 'custom-node'
    },
    defaultEdge: {
      type: 'custom-edge'
    },
    nodeStateStyles: {
      highlight: {
        shadowColor: '#f88c77',
        shadowBlur: 20,
        lineWidth: 2
      },
      dimmed: {
        opacity: 0.3
      }
    },
    layout: {
      type: 'compactBox',
      direction: 'LR',
      getId: d => d.id,
      getHeight: () => 50,
      getWidth: () => 140,
      getVGap: () => 40,
      getHGap: () => 120
    }
  })

  // 监听节点点击事件
  graph.on('node:click', evt => {
    const item = evt.item
    const name = evt.target.get('name')
    const model = item.getModel()

    if (name === 'collapse-icon') {
      graph.updateItem(item, {
        collapsed: !model.collapsed
      })
      graph.layout()
    } else {
      // 如果是根节点（level为0或id为'root'），则不触发node-click事件
      if (model.level === 0 || model.id === 'root') {
        return
      }
      emit('node-click', model)
    }
  })

  // 监听画布点击事件
  graph.on('canvas:click', () => {
    graph.getNodes().forEach(node => {
      graph.clearItemStates(node)
    })
  })

  // 如果有初始数据，加载并渲染树图
  if (props.data) {
    const processedData = JSON.parse(JSON.stringify(props.data))
    collapseDeepNodes(processedData)
    assignBranchKey(processedData)
    graph.data(processedData)
    graph.render()
    graph.fitCenter()
    graph.zoom(0.9)
    graph.fitCenter()
  }
}

// 高亮树图中带有某个标签的节点
const highlightByTag = (tag) => {
  if (!graph) return

  // 先清除所有状态
  graph.getNodes().forEach(node => {
    graph.clearItemStates(node)
  })

  // 设置匹配节点高亮
  graph.getNodes().forEach(node => {
    const model = node.getModel()
    if (model.tags && model.tags.includes(tag)) {
      graph.setItemState(node, 'highlight', true)
    } else {
      graph.setItemState(node, 'dimmed', true)
    }
  })
}

// 对外暴露方法
defineExpose({ highlightByTag })
</script>

<style lang="scss" scoped>
.tree-graph-container {
  width: 100%;
  height: 100%;
  min-height: 500px;
  background: #f5f7fa;
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}
</style>