<template>
  <el-dialog 
    :model-value="visible" 
    title="学生数据统计" 
    width="40%" 
    @update:model-value="updateVisible"
  >
    <div class="export-stats-container">
      <!-- 班级和学生总数 -->
      <div class="stat-section">
        <h3>班级统计</h3>
        <div class="stat-numbers">
          <div class="stat-group">
            <div class="stat-number">{{ statistics.totalClassCount }}</div>
            <div class="stat-label">班级数</div>
          </div>
          <div class="vertical-line"></div>
          <div class="stat-group">
            <div class="stat-number">{{ statistics.totalStudentCount }}</div>
            <div class="stat-label">学生总数</div>
          </div>
        </div>
      </div>

      <!-- 审核入班统计 -->
      <div class="stat-section">
        <h3>审核入班</h3>
        <div class="chart-container">
          <div class="pie-chart" ref="pieChart1"></div>
          <div class="chart-legend">
            <div class="legend-item">
              <span class="legend-color" :style="{ backgroundColor: '#4CAF50' }"></span>
              <span>已通过 {{ statistics.teacherReviewApprovedCount }}人</span>
            </div>
            <div class="legend-item">
              <span class="legend-color" :style="{ backgroundColor: '#FF5722' }"></span>
              <span>待审核 {{ statistics.teacherReviewPendingCount }}人</span>
            </div>
            <div class="legend-item">
              <span class="legend-color" :style="{ backgroundColor: '#FF0000' }"></span>
              <span>已拒绝 {{ statistics.teacherReviewRejectedCount }}人</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 邀请入班统计 -->
      <div class="stat-section">
        <h3>邀请入班</h3>
        <div class="chart-container">
          <div class="pie-chart" ref="pieChart2"></div>
          <div class="chart-legend">
            <div class="legend-item">
              <span class="legend-color" :style="{ backgroundColor: '#4CAF50' }"></span>
              <span>已通过 {{ statistics.teacherInviteApprovedCount }}人</span>
            </div>
            <div class="legend-item">
              <span class="legend-color" :style="{ backgroundColor: '#FF5722' }"></span>
              <span>待入班 {{ statistics.teacherInvitePendingCount }}人</span>
            </div>
            <div class="legend-item">
              <span class="legend-color" :style="{ backgroundColor: '#FF0000' }"></span>
              <span>已拒绝 {{ statistics.teacherInviteRejectedCount }}人</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 导出按钮 -->
      <div class="export-details-btn">
        <el-button type="primary" @click="exportDetails">导出详情</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { ref, onMounted, watch, nextTick } from 'vue';
import * as echarts from 'echarts';

export default {
  props: {
    visible: Boolean,
    statistics: {
      type: Object,
      default: () => ({
        totalClassCount: 0,
        totalStudentCount: 0,
        teacherReviewApprovedCount: 0,
        teacherReviewPendingCount: 0,
        teacherReviewRejectedCount: 0,
        teacherInviteApprovedCount: 0,
        teacherInvitePendingCount: 0,
        teacherInviteRejectedCount: 0
      }),
      validator: (stats) => {
        // 确保所有必需字段都存在
        const requiredFields = [
          'totalClassCount', 'totalStudentCount',
          'teacherReviewApprovedCount', 'teacherReviewPendingCount', 'teacherReviewRejectedCount',
          'teacherInviteApprovedCount', 'teacherInvitePendingCount', 'teacherInviteRejectedCount'
        ];
        return requiredFields.every(field => field in stats);
      }
    }
  },
  emits: ['update:visible', 'export-details', 'fetch-statistics'],
  setup(props, { emit }) {
    const pieChart1 = ref(null);
    const pieChart2 = ref(null);
    const chartInstances = ref({});
    const initialized = ref(false);

    // 监听 visible 变化，控制图表初始化/销毁
    watch(() => props.visible, async (val) => {
      if (val) {
        // 触发父组件获取最新统计数据
        emit('fetch-statistics');
        nextTick(() => {
          initCharts();
        });
      } else {
        Object.values(chartInstances.value).forEach(chart => chart.dispose());
        chartInstances.value = {};
        initialized.value = false;
      }
    });

    // 监听统计数据变化，更新图表
    watch(() => props.statistics, () => {
      if (initialized.value) {
        updateCharts();
      }
    }, { deep: true });

    // 初始化图表
    const initCharts = () => {
      // 饼图1：审核入班
      if (pieChart1.value) {
        const chart = echarts.init(pieChart1.value);
        chart.setOption({
          series: [
            {
              type: 'pie',
              radius: ['40%', '60%'],
              center: ['50%', '50%'],
              data: [
                { value: props.statistics.teacherReviewApprovedCount, name: '已通过', itemStyle: { color: '#4CAF50' } },
                { value: props.statistics.teacherReviewPendingCount, name: '待审核', itemStyle: { color: '#FF5722' } },
                { value: props.statistics.teacherReviewRejectedCount, name: '已拒绝', itemStyle: { color: '#FF0000' } }
              ],
              label: { show: false },
            }
          ]
        });
        chartInstances.value.chart1 = chart;
      }

      // 饼图2：邀请入班
      if (pieChart2.value) {
        const chart = echarts.init(pieChart2.value);
        chart.setOption({
          series: [
            {
              type: 'pie',
              radius: ['40%', '60%'],
              center: ['50%', '50%'],
              data: [
                { value: props.statistics.teacherInviteApprovedCount, name: '已通过', itemStyle: { color: '#4CAF50' } },
                { value: props.statistics.teacherInvitePendingCount, name: '待入班', itemStyle: { color: '#FF5722' } },
                { value: props.statistics.teacherInviteRejectedCount, name: '已拒绝', itemStyle: { color: '#FF0000' } }
              ],
              label: { show: false },
            }
          ]
        });
        chartInstances.value.chart2 = chart;
      }
    };

    // 更新图表数据
    const updateCharts = () => {
      if (chartInstances.value.chart1) {
        chartInstances.value.chart1.setOption({
          series: [
            {
              data: [
                { value: props.statistics.teacherReviewApprovedCount, name: '已通过' },
                { value: props.statistics.teacherReviewPendingCount, name: '待审核' },
                { value: props.statistics.teacherReviewRejectedCount, name: '已拒绝' }
              ]
            }
          ]
        });
      }

      if (chartInstances.value.chart2) {
        chartInstances.value.chart2.setOption({
          series: [
            {
              data: [
                { value: props.statistics.teacherInviteApprovedCount, name: '已通过' },
                { value: props.statistics.teacherInvitePendingCount, name: '待入班' },
                { value: props.statistics.teacherInviteRejectedCount, name: '已拒绝' }
              ]
            }
          ]
        });
      }
    };

    // 窗口resize时自适应
    onMounted(() => {
      const resizeHandler = () => {
        Object.values(chartInstances.value).forEach(chart => chart.resize());
      };
      window.addEventListener('resize', resizeHandler);
      return () => {
        window.removeEventListener('resize', resizeHandler);
      };
    });

    // 关闭弹窗
    const updateVisible = (value) => {
      emit('update:visible', value);
    };

    // 导出详情
    const exportDetails = () => {
      emit('export-details');
    };

    return {
      pieChart1,
      pieChart2,
      updateVisible,
      exportDetails
    };
  }
};
</script>

<style scoped>
.export-stats-container {
  padding: 20px;
}
.stat-section {
  margin-bottom: 24px;
}
.stat-section h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
}
.stat-numbers {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 30px;
}
.stat-group {
  text-align: center;
}
.stat-number {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 4px;
  color: #409EFF;
}
.stat-label {
  font-size: 12px;
  color: #999;
}
.vertical-line {
  width: 1px;
  height: 30px;
  background-color: #ebeef5;
}
.chart-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.pie-chart {
  width: 120px;
  height: 120px;
  margin-bottom: 8px;
}
.chart-legend {
  display: flex;
  gap: 20px;
  font-size: 12px;
  color: #666;
}
.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}
.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%; /* 圆形 */
}
.export-details-btn {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>