import api from '@/api/service.js'

/**
 * 获取专业内容
 * @returns {Promise} API响应
 */
export const getMajorContent = () => {
  return api({
    url: 'http://8.134.236.247:1991/majorContent/get',
    method: 'get'
  });
}

/**
 * 获取专业调研
 * @returns {Promise} API响应
 */
export const getMajorInvestigation = () => {
  return api({
    url: 'http://8.134.236.247:1991/majorInvestigation/get',
    method: 'get'
  });
}

/**
 * 获取AI岗位分析
 * @returns {Promise} API响应
 */
export const getMajorAiJob = () => {
  return api({
    url: 'http://8.134.236.247:1991/majorAiJob/get',
    method: 'get'
  });
}

/**
 * 获取人才培养方案列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页码，默认1
 * @param {number} params.pageSize - 每页数量，默认10
 * @param {number} params.year - 年份（可选）
 * @param {string} params.name - 名称（可选）
 * @param {string} params.fileType - 文件类型（可选）
 * @param {number} params.order - 排序（可选）
 * @returns {Promise} API响应
 */
export const getMajorCultivationList = (params = {}) => {
  // 默认参数
  const defaultParams = {
    pageNum: 1,
    pageSize: 10
  };

  // 过滤掉空值参数并合并默认参数
  const filteredParams = Object.fromEntries(
    Object.entries({
      ...defaultParams,
      ...params
    }).filter(([_, v]) => v !== '' && v !== undefined && v !== null)
  );

  return api({
    url: 'http://8.134.236.247:1991/majorCultivation/list',
    method: 'get',
    params: filteredParams
  });
}
