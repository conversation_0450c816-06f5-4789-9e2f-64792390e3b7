import mammoth from 'mammoth';
import axios from 'axios';
import { getKey } from '../../key.js';

// 提取习题函数
export const extractQuestionsFromDoc = async (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = async (event) => {
      try {
        const arrayBuffer = event.target.result;
        const { value: html } = await mammoth.convertToHtml({ arrayBuffer });
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const textContent = doc.body.textContent;

        // 解析选择题 - 改进正则表达式
        const choiceRegex = /一、选择题[\s\S]*?二、填空题/;
        const choiceMatch = textContent.match(choiceRegex);
        const choiceQuestions = choiceMatch ? parseChoiceQuestions(choiceMatch[0]) : [];

        // 解析填空题
        const blankRegex = /二、填空题[\s\S]*?三、简答题/;
        const blankMatch = textContent.match(blankRegex);
        const blankQuestions = blankMatch ? parseBlankQuestions(blankMatch[0]) : [];

        // 解析简答题
        const shortRegex = /三、简答题[\s\S]*/;
        const shortMatch = textContent.match(shortRegex);
        const shortQuestions = shortMatch ? parseShortQuestions(shortMatch[0]) : [];

        resolve([...choiceQuestions, ...blankQuestions, ...shortQuestions]);
      } catch (error) {
        reject(error);
      }
    };
    reader.onerror = reject;
    reader.readAsArrayBuffer(file);
  });
};

// 解析选择题 - 改进版本
function parseChoiceQuestions(text) {
  const questions = [];
  // 改进正则表达式，更好地匹配选择题结构
  const regex = /(\d+)\.(.*?)(?=\d+\.|答案：)/gs;
  let match;

  while ((match = regex.exec(text)) !== null) {
    const question = match[2].trim();
    const answerMatch = question.match(/答案：([A-D])/);
    if (!answerMatch) continue;

    const answer = answerMatch[1];
    const cleanQuestion = question.replace(/答案：.*/, '').trim();

    // 提取选项 - 改进方法
    const options = [];
    const optionRegex = /([A-D])\.\s*([^\n]+)/g;
    let optionMatch;
    let lastIndex = 0;

    while ((optionMatch = optionRegex.exec(cleanQuestion)) !== null) {
      options.push(`${optionMatch[1]}. ${optionMatch[2].trim()}`);
      lastIndex = optionMatch.index + optionMatch[0].length;
    }

    // 提取题目文本（排除选项部分）
    const questionText = cleanQuestion.substring(0, lastIndex).trim();

    questions.push({
      type: "选择题",
      question: `${match[1]}. ${questionText.replace(/[A-D]\./g, '\n$&')}`,
      options: options.join('、'),
      answer
    });
  }
  return questions;
}

// 解析填空题
function parseBlankQuestions(text) {
  const questions = [];
  const regex = /(\d+)\.(.*?)答案：(.+?)(?=\d+\.|$)/gs;
  let match;

  while ((match = regex.exec(text)) !== null) {
    questions.push({
      type: "填空题",
      question: `${match[1]}. ${match[2].trim()}`,
      answer: match[3].trim()
    });
  }
  return questions;
}

// 解析简答题
function parseShortQuestions(text) {
  const questions = [];
  const regex = /(\d+)\.(.*?)答案：([\s\S]*?)(?=\d+\.|$)/g;
  let match;

  while ((match = regex.exec(text)) !== null) {
    questions.push({
      type: "简答题",
      question: `${match[1]}. ${match[2].trim()}`,
      answer: match[3].trim()
    });
  }
  return questions;
}

// 分析答案函数 - 改进提示词
export const analyzeExercise = async (question, correctAnswer, questionType) => {
  try {
    const deepseekApiKey = getKey('deepseek');
    const openai = new axios.create({
      baseURL: 'https://api.deepseek.com/v1',
      headers: {
        'Authorization': `Bearer ${deepseekApiKey}`,
        'Content-Type': 'application/json',
      },
    });

    // 改进提示词，明确要求正确/错误判断和答案格式
    const systemPrompt = `你是一个专业的知识教育家，精通各种计算机类课程的知识，请根据以下规则分析题目：

    1. 请先判断参考答案是否正确：
       - 如果正确，显示"正确"并分析知识点（2-3行）
       - 如果不正确，显示"错误"，给出正确答案，然后分析知识点（3-4行）

    2. 分析内容要求：
       - 知识点分析要精炼准确
       - 语言简洁专业
       - 错误时先给出正确答案再分析

    3. 输出格式：
       第一行：正确/错误
       第二行：正确答案（仅当错误时显示）
       第三行起：知识点分析`;

    const response = await openai.post('/chat/completions', {
      model: 'deepseek-chat',
      messages: [
        { role: 'system', content: systemPrompt },
        {
          role: 'user',
          content: `题型：${questionType}
          题目：${question}
          参考答案：${correctAnswer}`
        },
      ],
      max_tokens: 500,
      temperature: 0.3,
    });

    // 解析API响应，提取正确/错误状态
    const analysisText = response.data.choices[0].message.content;
    const isCorrect = analysisText.includes('正确');

    return {
      analysis: analysisText,
      isCorrect
    };
  } catch (error) {
    console.error('分析失败:', error);
    return {
      analysis: "分析失败: " + error.message,
      isCorrect: false
    };
  }
};

// 批量分析所有题目
export const analyzeAllExercises = async (questions) => {
  const analysisResults = [];

  for (const question of questions) {
    try {
      const result = await analyzeExercise(
        question.question,
        question.answer,
        question.type
      );
      analysisResults.push({
        ...question,
        analysis: result.analysis,
        isCorrect: result.isCorrect
      });
    } catch (error) {
      console.error(`题目分析失败: ${question.question}`, error);
      analysisResults.push({
        ...question,
        analysis: "分析失败: " + error.message,
        isCorrect: false
      });
    }
  }

  return analysisResults;
};
