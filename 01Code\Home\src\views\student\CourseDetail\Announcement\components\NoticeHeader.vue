<template>
  <div class="notice-header">
    <!-- 类型切换选项卡（带统计数量） -->
    <div class="type-tabs">
      <button 
        v-for="tab in tabs" 
        :key="tab.type" 
        :class="{ active: activeTab === tab.type }"
        @click="activeTab = tab.type"
      >
        {{ tab.label }} ({{ noticeCounts[tab.type] }})
      </button>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useStudentNoticeStore } from '@/stores/student/noticeStore'

const noticeStore = useStudentNoticeStore()

// 标签配置
const tabs = computed(() => 
  Object.entries(noticeStore.NOTICE_TYPES).map(([type, config]) => ({
    type,
    label: config.label
  }))
)

// 当前激活的选项卡
const activeTab = computed({
  get() {
    return noticeStore.activeTab
  },
  set(value) {
    noticeStore.activeTab = value
  }
})

// 各类型公告数量统计
const noticeCounts = computed(() => {
  const counts = {}
  Object.keys(noticeStore.NOTICE_TYPES).forEach(type => {
    counts[type] = noticeStore.notices.filter(
      n => n.noticeType === noticeStore.NOTICE_TYPES[type].value
    ).length
  })
  return counts
})
</script>

<style lang="scss" scoped>
@use '@/styles/variables';

.notice-header {
  margin-bottom: 20px;
}

.type-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;

  button {
    padding: 8px 16px;
    border: 1px solid #ddd;
    background-color: #fff;
    color: #666;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 14px;

    &:hover {
      background-color: #f5f5f5;
      border-color: $primary-color;
    }

    &.active {
      background-color: $primary-color;
      color: #fff;
      border-color: $primary-color;
    }
  }
}
</style>
