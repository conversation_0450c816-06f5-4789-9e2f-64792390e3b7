import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

// 判断是否为开发环境
const isDev = process.env.NODE_ENV === 'development'

export default defineConfig(async () => {
  const plugins = [
    vue(),
    AutoImport({
      imports: ['vue', 'vue-router'],
      resolvers: [ElementPlusResolver()],
      dts: 'src/auto-imports.d.ts',
    }),
    Components({
      resolvers: [ElementPlusResolver()],
      dts: 'src/components.d.ts',
    }),
  ]


  // 如果是开发环境，再动态导入插件
  if (isDev) {
    const { default: vueDevTools } = await import('vite-plugin-vue-devtools')
    plugins.unshift(vueDevTools())
  }

  return {
    plugins,
    define: {
      'defaultCourseCover': JSON.stringify('/src/assets/img/Course/courseCover/courseCover-default.png'),
      'defaultTeacherAvatar': JSON.stringify('/src/assets/img/Teacher/avatar-male.png')
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/styles/variables" as *;`
        },
      },
    },
    server: {
      proxy: {
        '/api': {
          target: 'http://8.134.236.247:1991', // 后端服务器地址
          changeOrigin: true, // 改变源
          rewrite: (path) => path.replace(/^\/api/, ''), // 重写路径
        },
        '/backend-api': {
          target: 'http://8.134.236.247:1991',
          changeOrigin: true,
          secure: false,
          rewrite: path => path.replace(/^\/backend-api/, ''),
        },
      },
    },
  }
});
