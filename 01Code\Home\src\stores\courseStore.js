// stores/courseStore.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { createCourse, addCourseTeacher, getTeacherCourses, getCourseInformationList } from '@/api/teacher/course'
import { useUserStore } from './userStore'

export const useCourseStore = defineStore('course', () => {
  const courses = ref([])
  const isLoading = ref(false)
  const error = ref(null)
  const currentCourseId = ref('')
  const currentCourseInfoId = ref('')
  const userStore = useUserStore()

  const isEnrolled = ref(false)// 是否已加入课程
 const hasJoinedCourse = computed(() => isEnrolled.value)

  // 获取当前学期（保持不变）
  const getCurrentSemester = () => {
    const now = new Date()
    const year = now.getFullYear()
    const month = now.getMonth() + 1
    return `${year}-${year + 1}${month >= 9 ? '第一学期' : '第二学期'}`
  }

  //获取当前课程id
  const setCurrentCourseId = (id) => {
    currentCourseId.value = id
  }

    function setCourseEnrollmentStatus(status) {
    isEnrolled.value = status
  }
  
  //创建课程
  const createNewCourse = async (courseData) => {
    isLoading.value = true
    error.value = null

    try {
      const fullCourseData = {
        name: courseData.name,
        courseCover: courseData.courseCover || '',
        semester: courseData.semester || getCurrentSemester(),
        courseType: courseData.courseType || '公共课',
        credits: courseData.credits || 2,
        hours: courseData.hours || 0,
        subjectCategory: courseData.subjectCategory || '其他',
        major: courseData.major || '',
        introduce: courseData.introduce || '',
        invitationCode: courseData.invitationCode,
        tag: courseData.tag || '',
        status: 1,
        state: 0,
        specializations: courseData.specializations || ''
      }

      const response = await createCourse(fullCourseData)

      if (!response?.result?.id) {
        throw new Error('无效的课程响应数据')
      }

      const newCourse = response.result
      const courseId = newCourse.id


      // 更新本地状态
      courses.value.push(newCourse)
      setCurrentCourseId(courseId)

      return newCourse

    } catch (err) {
      error.value = err.response?.data?.msg || err.message || '创建课程失败'
      throw error.value
    } finally {
      isLoading.value = false
    }
  }


  //获取课程列表
  const fetchTeacherCourses = async () => {
    isLoading.value = true
    error.value = null

    try {
      const response = await getTeacherCourses()
      courses.value = response.result || []

    } catch (err) {
      console.error('获取教师课程列表失败:', err)
      error.value = err.response?.data?.msg || err.message || '获取课程失败'
      throw error.value
    } finally {
      isLoading.value = false
    }
  }

  // 保存课程信息并获取新ID
  const saveCourseInfo = async (courseId, courseInfoData) => {
    isLoading.value = true
    error.value = null

    try {
      const data = {
        ...courseInfoData,
        courseId // 关联课程ID
      }

      // 调用保存接口，获取课程信息ID
      const courseInfoId = await saveCourseInformation(data);
      currentCourseInfoId.value = courseInfoId;
      return courseInfoId;

    } catch (err) {
      error.value = err.response?.data?.msg || err.message || '保存课程信息失败'
      throw error.value
    } finally {
      isLoading.value = false
    }
  }

  const fetchCourseInfoList = async (courseId) => {
    try {
      loading.value = true;
      const response = await getCourseInformationList({
        courseId,
        pageNum: 1,
        pageSize: 10
      });

      // 如果有课程信息，则获取第一个的ID
      if (response.records?.length > 0) {
        return response.records[0].id;
      }
      return null;
    } catch (error) {
      console.error('获取课程信息列表失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 获取课程信息
  const fetchCourseInfo = async (courseId) => {
    if (!courseId) return null;

    try {
      loading.value = true;

      // 1. 先获取课程信息ID
      const infoId = await fetchCourseInfoList(courseId);
      if (!infoId) {
        console.log('该课程暂无课程信息');
        return null;
      }

      // 2. 使用获取到的ID获取详细信息
      const data = await getCourseInformation(infoId);
      currentCourseInfoId.value = infoId; // 存储当前课程信息ID

      return data;
    } catch (error) {
      console.error('获取课程信息失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  return {
    courses,
    isLoading,
    error,
    currentCourseId,
    currentCourseInfoId, // 导出课程信息ID
    isEnrolled ,
    hasJoinedCourse,
    setCourseEnrollmentStatus,
    createNewCourse,
    fetchTeacherCourses,
    setCurrentCourseId,
    saveCourseInfo,
    fetchCourseInfo
  }
})
