<template>
  <div class="office-preview-container">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <div class="loading-spinner"></div>
      <p>文件加载中...</p>
    </div>

    <!-- Office文件预览 -->
    <iframe
      v-if="isOfficeFile && !error"
      ref="previewIframe"
      :src="viewerUrl"
      class="preview-iframe"
      @load="handleLoad"
      @error="handleError"
    ></iframe>

    <!-- 视频预览 -->
    <video
      v-else-if="isVideo && !error"
      controls
      class="preview-video"
      @loadeddata="handleLoad"
      @error="handleError"
    >
      <source :src="fileUrl" type="video/mp4">
      您的浏览器不支持视频播放
    </video>

    <!-- 错误状态 -->
    <div v-if="error" class="error-state">
      <div class="error-icon">!</div>
      <p>预览加载失败</p>
      <el-button type="primary" @click="tryReload">重新加载</el-button>
      <el-button link @click="$emit('close')">关闭预览</el-button>
    </div>

    <!-- 不支持的类型 -->
    <div v-if="!isOfficeFile && !isVideo && !loading" class="unsupported-type">
      <p>不支持该文件类型的预览</p>
      <el-button link @click="$emit('close')">关闭</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  fileUrl: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['close'])

const loading = ref(true)
const error = ref(false)
const previewIframe = ref(null)

// 判断文件类型
const isOfficeFile = computed(() => {
  return /\.(docx?|pptx?|xlsx?)$/i.test(props.fileUrl)
})

const isVideo = computed(() => {
  return /\.(mp4|webm|ogg)$/i.test(props.fileUrl)
})

// 生成Office在线预览URL
const viewerUrl = computed(() => {
  return `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(props.fileUrl)}`
})

// 处理加载完成
const handleLoad = () => {
  loading.value = false
  error.value = false
}

// 处理加载错误
const handleError = () => {
  loading.value = false
  error.value = true
}

// 重新加载
const tryReload = () => {
  error.value = false
  loading.value = true
}

// 监听文件URL变化
watch(() => props.fileUrl, () => {
  loading.value = true
  error.value = false
})
</script>

<style scoped>
.office-preview-container {
  height: 70vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.preview-iframe,
.preview-video {
  width: 100%;
  height: 100%;
  border: none;
  background: #f5f5f5;
}

.loading-state,
.error-state,
.unsupported-type {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 15px;
  height: 100%;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.error-icon {
  width: 50px;
  height: 50px;
  background: #f56c6c;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>