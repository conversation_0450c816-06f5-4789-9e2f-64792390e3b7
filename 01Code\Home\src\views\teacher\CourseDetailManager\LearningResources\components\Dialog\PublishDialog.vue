<template>
  <div class="publish-dialog">
    <el-dialog
      :model-value="modelValue"
      :title="title"
      width="50%"
      :before-close="handleClose"
      @update:modelValue="(val) => $emit('update:modelValue', val)"
    >
      <div class="tree-container">
        <el-tree
          :data="treeData"
          node-key="id"
          :props="defaultProps"
          :expand-on-click-node="false"
          @node-click="handleNodeClick"
          highlight-current
        >
          <template #default="{ node}">
            <span class="tree-node">
              {{ node.label }}
            </span>
          </template>
        </el-tree>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleConfirm" 
            :disabled="!selectedNodeId"
            :loading="confirmLoading"
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '选择发布位置'
  },
  treeData: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'confirm'])

const defaultProps = {
  children: 'children',
  label: 'title'
}

const selectedNodeId = ref(null)
const confirmLoading = ref(false)

const handleNodeClick = (data) => {
  selectedNodeId.value = data.id
}

const handleClose = () => {
  emit('update:modelValue', false)
  selectedNodeId.value = null
  confirmLoading.value = false
}

const handleConfirm = () => {
  if (selectedNodeId.value) {
    confirmLoading.value = true
    emit('confirm', selectedNodeId.value)
  }
}

watch(() => props.modelValue, (newVal) => {
  if (!newVal) {
    selectedNodeId.value = null
    confirmLoading.value = false
  }
})
</script>

<style scoped>
.tree-container {
  max-height: 60vh;
  overflow-y: auto;
  padding: 10px;
}

.tree-node {
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>