<template>
  <div class="grade-container">
    <h1 class="grade-title">我的成绩</h1>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-icon class="loading-icon"><Loading /></el-icon>
      <span>成绩加载中...</span>
    </div>
    
    <!-- 错误状态 -->
    <el-alert
      v-if="error"
      :title="error"
      type="error"
      show-icon
      closable
      @close="error = null"
    />
    
    <!-- 空状态 -->
    <el-empty 
      v-if="!loading && semesters.length === 0 && !error" 
      description="暂无成绩数据" 
    />
    
    <!-- 成绩列表 -->
    <div class="semester-list">
      <el-card 
        v-for="semester in semesters" 
        :key="semester.id"
        class="semester-card"
        shadow="hover"
        @click="toggleSemester(semester.id)"
      >
        <div class="semester-header">
          <div class="semester-info">
            <h3>{{ semester.name }}</h3>
            <span class="semester-date">{{ semester.dateRange }}</span>
          </div>
          
          <div class="semester-stats">
            <div class="stat-item">
              <span class="stat-label">平均成绩</span>
              <span class="stat-value">{{ semester.averageScore }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">GPA</span>
              <span class="stat-value">{{ semester.gpa.toFixed(1) }}</span>
            </div>
          </div>
          
          <el-icon :class="['expand-icon', { 'expanded': semester.expanded }]">
            <ArrowDown />
          </el-icon>
        </div>
        
        <el-collapse-transition>
          <div v-show="semester.expanded" class="course-list">
            <div 
              v-for="course in semester.courses" 
              :key="course.id"
              class="course-item"
              @click.stop="showCourseDetail(course)"
            >
              <div class="course-main">
                <span class="course-name">{{ course.name }}</span>
                <span class="course-type">{{ course.type }}</span>
              </div>
              <div class="course-score">
                <span :class="['score-value', getScoreClass(course.score)]">
                  {{ course.score }}
                </span>
                <span class="course-credit">{{ course.credit }}学分</span>
              </div>
            </div>
          </div>
        </el-collapse-transition>
      </el-card>
    </div>
    
    <!-- 课程详情对话框 -->
    <el-dialog
    v-model="detailDialogVisible"
    :title="selectedCourse?.name || '课程详情'"
    width="80%"
    top="5vh"
  >
    <CourseDetail 
      v-if="selectedCourse" 
      :course="selectedCourse" 
      :detail-data="courseDetail || {}" 
    />
  </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ArrowDown, Loading } from '@element-plus/icons-vue'
import gradeApi from '@/api/student/grade'
import CourseDetail from './GradeDetail.vue'

// 响应式数据
const semesters = ref([])
const loading = ref(true)
const error = ref(null)
const detailDialogVisible = ref(false)
const selectedCourse = ref(null)
const courseDetail = ref(null)

// 获取学期成绩数据
const fetchSemesterGrades = async () => {
  try {
    loading.value = true
    error.value = null
    const response = await gradeApi.getGradesBySemester()
    
    // 修改这里，从response.result.gradelist获取数据
    if (response.code === 200 && response.result?.gradeList) {
      semesters.value = transformSemesterData(response.result.gradeList)
      console.log("响应数据",response.result.gradeList)
      console.log("转换数据",semesters.value)
    } else {
      throw new Error(response.msg || '获取成绩数据失败')
    }
  } catch (err) {
    error.value = err.message || '获取成绩数据失败，请稍后重试'
    console.error('获取成绩数据出错:', err)
  } finally {
    loading.value = false
  }
}

// 转换API数据为前端格式
const transformSemesterData = (gradelist) => {
    const semesterMap = {}
    
    gradelist.forEach(item => {
        // 调试打印原始数据结构
        console.log('原始课程项:', item)
        
        // 尝试多种方式获取学期信息
        const semesterKey = item['semester(学期)'] || item['学期'] || '2024-1'
        const [year, term] = semesterKey.split('-'); // 拆分学年和学期
        
        if (!semesterMap[semesterKey]) {
            semesterMap[semesterKey] = {
                id: semesterKey.replace(/\D/g, '') + '-' + semesterKey.split('-')[1],
                name: `${semesterKey.split('-')[0]}年第${semesterKey.split('-')[1]}学期`,
                dateRange: getDateRangeBySemester(year, term), // 传递 year 和 term
                courses: []
            }
        }
        
        // 安全获取各个字段
        semesterMap[semesterKey].courses.push({
            id: item['courseId(课程ID)'] || item['classId'] || '',
            name: item['courseName'] || item['courseName(课程名称)'] || item['课程名称'] || '未知课程',
            type: item['courseType'] || item['courseType(课程类型)'] || item['课程类型'] || '未知类型',
            score: item['totalScore(总成绩)'] || item['score'] || 0,
            credit: item['credits(学分)'] || item['credit'] || 0,
            hours: item['hours(学时)'] || 0,
            examScore: item['examScore()'] || 0,
            assignmentScore: item['assignmentScore(作业成绩)'] || 0,
            className: item['className(班级名称)'] || item['class'] || '',
            // 其他统计字段
            assignmentCount: item['assignmentCount(已完成作业数)'] || 0,
            totalAssignmentCount: item['totalAssignmentCount(总作业数)'] || 0,
            examCount: item['examCount(已完成考试数)'] || 0,
            totalExamCount: item['totalExamCount(总考试数)'] || 0
        })
    })
    
    return Object.values(semesterMap).map(semester => ({
        ...semester,
        averageScore: calculateAverage(semester.courses),
        gpa: calculateGPA(calculateAverage(semester.courses)),
        expanded: false
    }))
}

// 根据学期计算日期范围
const getDateRangeBySemester = (year, term) => {
  year = parseInt(year)
  return term === '1' 
    ? `${year}.03 - ${year}.07`
    : `${year}.09 - ${year+1}.01`
}

// 计算平均成绩
const calculateAverage = (courses) => {
  if (!courses || courses.length === 0) return 0
  const sum = courses.reduce((total, course) => total + (course.score || 0), 0)
  return (sum / courses.length).toFixed(1)
}

// 计算GPA（简化版）
const calculateGPA = (score) => {
  score = parseFloat(score)
  if (score >= 90) return 4.0
  if (score >= 85) return 3.7
  if (score >= 82) return 3.3
  if (score >= 78) return 3.0
  if (score >= 75) return 2.7
  if (score >= 72) return 2.3
  if (score >= 68) return 2.0
  if (score >= 64) return 1.5
  if (score >= 60) return 1.0
  return 0
}

// 切换学期展开状态
const toggleSemester = (semesterId) => {
  const semester = semesters.value.find(s => s.id === semesterId)
  if (semester) {
    semester.expanded = !semester.expanded
  }
}

// 根据分数获取样式类
const getScoreClass = (score) => {
  if (score >= 90) return 'score-excellent'
  if (score >= 80) return 'score-good'
  if (score >= 60) return 'score-pass'
  return 'score-fail'
}

// 查看课程详情
const showCourseDetail = async (course) => {
  try {
    selectedCourse.value = course
    console.log("课程详情信息：",course )
    detailDialogVisible.value = true
    
    // 获取课程详情 - 假设API返回格式包含学习进度等数据
    const response = await gradeApi.getScoreDetails(course.id)
    courseDetail.value = response.result || response
  } catch (err) {
    console.error('获取课程详情失败:', err)
    courseDetail.value = null
  }
}
// 组件挂载时获取数据
onMounted(() => {
  fetchSemesterGrades()
})
</script>

<style scoped>
.grade-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
}

.grade-title {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
  font-size: 24px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
  color: #666;
}

.loading-icon {
  margin-right: 10px;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.semester-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.semester-card {
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 8px;
}

.semester-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.semester-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
}

.semester-info {
  flex: 1;
}

.semester-info h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.semester-date {
  font-size: 14px;
  color: #888;
}

.semester-stats {
  display: flex;
  gap: 24px;
  margin-right: 20px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #409EFF;
}

.expand-icon {
  transition: transform 0.3s;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.course-list {
  padding: 0 16px 16px;
}

.course-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #eee;
  cursor: pointer;
}

.course-item:hover {
  background-color: #f5f7fa;
}

.course-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.course-name {
  font-size: 15px;
  margin-bottom: 4px;
}

.course-type {
  font-size: 12px;
  color: #909399;
}

.course-score {
  display: flex;
  align-items: center;
  gap: 12px;
}

.score-value {
  font-size: 16px;
  font-weight: bold;
  min-width: 40px;
  text-align: center;
}

.score-excellent {
  color: #67C23A;
}

.score-good {
  color: #409EFF;
}

.score-pass {
  color: #E6A23C;
}

.score-fail {
  color: #F56C6C;
}

.course-credit {
  color: #909399;
  font-size: 13px;
}

/* 课程详情样式 */
.course-detail {
  padding: 0 20px;
}

.detail-row {
  display: flex;
  margin-bottom: 15px;
  font-size: 15px;
}

.detail-label {
  font-weight: bold;
  width: 100px;
  color: #666;
}
</style>