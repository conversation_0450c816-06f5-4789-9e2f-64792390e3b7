import api from '@/api/service.js'

/**
 * 获取本地非遗资料列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页数，默认1
 * @param {number} params.pageSize - 每页数量，默认10
 * @param {string} params.name - 非遗名称（可选）
 * @param {string} params.sortType - 排序类型：ascending升序，descending降序（可选）
 * @param {string} params.sortColumn - 排序字段（可选）
 * @returns {Promise} API响应
 */
export const getLocalHeritageList = (params = {}) => {
  // 默认参数
  const defaultParams = {
    pageNum: 1,
    pageSize: 10
  };
  
  // 过滤掉空值参数并合并默认参数
  const filteredParams = Object.fromEntries(
    Object.entries({
      ...defaultParams,
      ...params
    }).filter(([_, v]) => v !== '' && v !== undefined && v !== null)
  );
  
  return api({
    url: '/heritage/local/list',
    method: 'get',
    params: filteredParams
  });
}

/**
 * 获取在线非遗资料列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页数，默认1
 * @param {number} params.pageSize - 每页数量，默认10
 * @param {string} params.name - 非遗名称（可选）
 * @param {string} params.sortType - 排序类型：ascending升序，descending降序（可选）
 * @param {string} params.sortColumn - 排序字段（可选）
 * @returns {Promise} API响应
 */
export const getOnlineHeritageList = (params = {}) => {
  // 默认参数
  const defaultParams = {
    pageNum: 1,
    pageSize: 10
  };
  
  // 过滤掉空值参数并合并默认参数
  const filteredParams = Object.fromEntries(
    Object.entries({
      ...defaultParams,
      ...params
    }).filter(([_, v]) => v !== '' && v !== undefined && v !== null)
  );
  
  return api({
    url: '/heritage/online/list',
    method: 'get',
    params: filteredParams
  });
}

/**
 * 获取非遗详情
 * @param {string} id - 非遗ID
 * @returns {Promise} API响应
 */
export const getHeritageDetail = (id) => {
  return api({
    url: `http://8.134.236.247:1991/heritage/get`,
    method: 'get',
    params: { id }
  });
}

/**
 * 获取非遗图片列表
 * @param {string} heritageId - 非遗ID
 * @returns {Promise} API响应
 */
export const getHeritageImageList = (heritageId) => {
  return api({
    url: `http://8.134.236.247:1991/heritage/image/list`,
    method: 'get',
    params: { heritageId }
  });
}
