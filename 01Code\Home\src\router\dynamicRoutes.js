// src/router/dynamicRoutes.js
import publicRoutes from './publicRoutes'
import router from './index'
import authRoutes from './authRoutes'
import studentRoutes from './studentRoutes'
import teacherRoutes from './teacherRoutes'

/**
 * 递归获取所有路由名称
 */
const getAllRouteNames = (routes) => {
  const names = []
  routes.forEach(route => {
    if (route.name) names.push(route.name)
    if (route.children) {
      names.push(...getAllRouteNames(route.children))
    }
  })
  return names
}

/**
 * 获取角色对应的默认路由
 */
export function getUserDefaultRoute(role) {
  return '/'
}

/**
 * 清理非公共路由（保留authRoutes）
 */
const cleanupDynamicRoutes = () => {
  const publicRouteNames = getAllRouteNames(publicRoutes)
  const authRouteNames = getAllRouteNames(authRoutes)
  const protectedNames = [...publicRouteNames, ...authRouteNames]

  router.getRoutes().forEach(route => {
    if (route.name && !protectedNames.includes(route.name)) {
      router.removeRoute(route.name)
    }
  })
}

/**
 * 添加角色专属路由
 */
const addRoleRoutes = (routes) => {
  routes.forEach(route => {
    if (!router.hasRoute(route.name)) {
      router.addRoute(route)
    }
  })
}

/**
 * 主方法：添加动态路由
 */
export const addDynamicRoutes = (userRole) => {
  // 0. 参数校验
  if (userRole === undefined || userRole === null) {
    console.error('addDynamicRoutes: 缺少userRole参数')
    return
  }

  // 1. 清理旧路由
  cleanupDynamicRoutes()

  // 2. 添加公共需认证路由（无角色限制）
  authRoutes.forEach(route => {
    if (!router.hasRoute(route.name)) {
      router.addRoute(route)
    }
  })

  // 3. 添加角色专属路由
  const role = Number(userRole)
  switch (role) {
    case 1: // 学生
      addRoleRoutes(studentRoutes)
      break
    case 2: // 教师
      addRoleRoutes(teacherRoutes)
      break
    case 0: // 管理员
      // 可在此添加管理员路由
      break
    default:
      console.warn(`未知角色类型: ${userRole}`)
  }

  // 4. 设置默认重定向（仅当不存在时）
  if (!router.hasRoute('DefaultRedirect')) {
    router.addRoute({
      path: '/',
      name: 'DefaultRedirect',
      redirect: getUserDefaultRoute(role)
    })
  }
}