<!--src\views\teacher\CourseDetailManager\StudentManagement\components\PhoneInviteDialog.vue-->
<template>
  <el-dialog :model-value="visible" title="手机号邀请" width="25%" @update:model-value="updateVisible">
    <div class="phone-invite-container">
      <div class="form-item">
        <label>学生手机号</label>
        <el-input
          v-model="studentPhone"
          placeholder="请输入要邀请的学生手机号"
          class="input-field"
        ></el-input>
      </div>
      
      <div class="form-item">
        <label>验证码</label>
        <div class="verification-code-container">
          <el-input
            v-model="verificationCode"
            placeholder="请输入验证码"
            class="input-field"
          ></el-input>
          <img :src="verificationCodeImage" alt="验证码" class="verification-code-image" @click="refreshVerificationCode">
        </div>
      </div>
      
      <div class="button-container">
        <el-button type="primary" @click="submitInvite">确定添加</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { ref } from 'vue';

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 添加验证码图片属性
    verificationCodeImage: {
      type: String,
      default: ''
    }
  },
  emits: ['update:visible', 'invite-submitted', 'refresh-verification'],
  setup(props, { emit }) {
    const studentPhone = ref('');
    const verificationCode = ref('');

    const updateVisible = (value) => {
      emit('update:visible', value);
    };

    const submitInvite = () => {
      if (!studentPhone.value.trim()) {
        alert('请输入学生手机号');
        return;
      }
      if (!verificationCode.value.trim()) {
        alert('请输入验证码');
        return;
      }
      
      // 发送邀请逻辑
      emit('invite-submitted', {
        studentPhone: studentPhone.value,
        verificationCode: verificationCode.value
      });
    };

    const refreshVerificationCode = () => {
      // 触发刷新验证码事件
      emit('refresh-verification');
    };

    return {
      studentPhone,
      verificationCode,
      updateVisible,
      submitInvite,
      refreshVerificationCode
    };
  }
};
</script>

<!-- 样式保持不变 -->

<style scoped>
.phone-invite-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-field {
  width: 100%;
}

.verification-code-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.verification-code-image {
  height: 36px;
  cursor: pointer;
}

.button-container {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
</style>