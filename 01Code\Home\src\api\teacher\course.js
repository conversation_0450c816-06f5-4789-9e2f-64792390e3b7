/*src\api\teacher\course.js*/
import request from '@/api/service'

export const createCourse = (courseData) => {
  return request.post('/course/save', courseData)
}

export const addCourseTeacher = (data) => {
  return request.post('/courseTeacher/save', data)
}

// 获取教师课程列表
export const getTeacherCourses = () => {
  return request.get(`/course/teacher`)
}

// 根据课程ID获取课程详情及教师信息
export const getCourseDetail = (courseId) => {
  return request({
    url: `/course/detail`,
    method: 'get',
    params: { id: courseId }
  }).then(response => {
    if (response.code === 200) {
      return response.result;
    } else {
      // 明确抛出错误，让上层 catch 捕获
      throw new Error(response.msg || '获取课程详情失败');
    }
  }).catch(error => {
    // 新增：若请求失败（如网络错误），打印详细日志
    console.error('getCourseDetail 请求失败:', error);
    throw error; // 继续抛出，让上层处理
  });
};

// 上传课程封面
export const uploadCourseCover = (courseId, file) => {
  const formData = new FormData();
  formData.append('file', file);
  
  return request({
    url: `/course/upload`,
    method: 'post',
    params: { courseId },
    data: formData,
    headers: {
    }
  });
};

// 获取课程详细信息
export const getCourseInformation = (courseId) => {
  return request({
    url: '/course/information/get',
    method: 'get',
    params: { id: courseId }
  }).then(response => {
    if (response.code === 200) {
      return response.result || {}; // 处理null情况
    } else {
      throw new Error(response.msg || '获取课程信息失败');
    }
  });
};

// 添加或更新课程信息
export const saveCourseInformation = (data) => {
  return request({
    url: '/course/information/save', 
    method: 'post',
    data: data
  }).then(response => {
    if (response.code === 200) {
      return response.result;
    } else {
      throw new Error(response.msg || '保存课程信息失败');
    }
  });
};

// 添加课程大纲
export const addSyllabus = (data) => {
  return request({
    url: '/course/syllabus/save',
    method: 'post',
    data
  }).then(response => {
    if (response.code === 200) {
      return response; // 返回完整响应
    } else {
      throw new Error(response.msg || '添加大纲失败');
    }
  });
};

// 更新课程大纲
export const updateSyllabus = (data) => {
  return request({
    url: '/course/syllabus/update',
    method: 'post',
    data
  }).then(response => {
    if (response.code === 200) {
      return response; // 返回完整响应
    } else {
      throw new Error(response.msg || '更新大纲失败');
    }
  });
};

// 删除课程大纲
export const deleteSyllabus = (id) => {
  return request({
    url: '/course/syllabus/remove',
    method: 'post',
    data: { id }
  }).then(response => {
    if (response.code === 200) {
      return response; // 返回完整响应
    } else {
      throw new Error(response.msg || '删除大纲失败');
    }
  });
};

// 获取大纲树
export const getSyllabusTree = (courseId) => {
  return request({
    url: '/course/syllabus/getCourseId',
    method: 'get',
    params: { courseId }
  })
}

// 获取课程信息列表
export const getCourseInformationList = (params) => {
  return request({
    url: '/course/information/list',
    method: 'get',
    params
  }).then(response => {
    if (response.code === 200) {
      return response.result;
    } else {
      throw new Error(response.msg || '获取课程信息列表失败');
    }
  });
};

// 更新课程介绍
export const updateCourseInformation = (data) => {
  return request({
    url: '/course/information/update',
    method: 'post',
    data: data
  }).then(response => {
    console.log('完整响应:', response); // 调试用
    if (response.code === 200) {
      return response; // 返回完整响应，而非仅 result
    } else {
      throw new Error(response.msg || '更新课程信息失败');
    }
  });
};

// 更新课程基础信息
export const updateCourse = (data) => {
  return request({
    url: '/course/update',
    method: 'post',
    data: data
  }).then(response => {
    console.log('完整响应:', response); // 调试用
    if (response.code === 200) {
      return response; // 返回完整响应而不是仅result
    } else {
      throw new Error(response.msg || '更新课程失败');
    }
  });
};


// 下载资源导入模板
export const downloadResourceTemplate = () => {
  return request({
    url: '/course/resource/downloadResourceTemplate',
    method: 'get',
    responseType: 'blob' // 重要：指定响应类型为blob
  })
}

// 导入课程资源
export const importCourseResources = (courseId, file, folderId = null, syllabusId = null) => {
  const formData = new FormData()
  formData.append('file', file)
  
  return request({
    url: '/course/resource/import',
    method: 'post',
    params: {
      courseId,
      ...(folderId && { folderId }),
      ...(syllabusId && { syllabusId })
    },
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取章节资源列表
export const getChapterResources = (params) => {
  return request({
    url: '/course/link/chapter-resources',
    method: 'get',
    params
  }).then(response => {
    if (response.code === 200) {
      return response.result || []; // 确保返回数组
    } else {
      throw new Error(response.msg || '获取章节资源失败');
    }
  });
};

// 删除章节资源
export const deleteChapterResource = (resourceId) => {
  return request({
    url: '/course/link/remove-resource',
    method: 'post',
    data: { id: resourceId }
  }).then(response => {
    if (response.code === 200) {
      return response;
    } else {
      throw new Error(response.msg || '删除资源失败');
    }
  });
};