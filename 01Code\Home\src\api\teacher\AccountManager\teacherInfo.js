import request from '@/api/service'

// 获取教师列表
export const getTeacherList = (params) => {
  return request({
    url: '/teacher/list',
    method: 'get',
    params
  })
}

// 获取教师详情
export const getTeacherDetail = (id) => {
  return request({
    url: '/teacher/get',
    method: 'get',
    params: { id }
  })
}

// 添加教师
export const addTeacher = (data) => {
  return request({
    url: '/teacher/save',
    method: 'post',
    data
  })
}

// 更新教师信息
export const updateTeacher = (data) => {
  return request({
    url: '/teacher/update',
    method: 'put',
    data
  })
}

// 删除教师
export const deleteTeacher = (id) => {
  return request({
    url: '/teacher/remove',
    method: 'post',
    data: { id }
  })
}

// 批量删除教师
export const batchDeleteTeachers = (ids) => {
  return request({
    url: '/teacher/removeAll',
    method: 'post',
    data: ids
  })
}

// 上传教师证书/照片
export const uploadTeacherFile = (file) => {
  const formData = new FormData();
  formData.append('file', file.raw || file); // 兼容两种文件对象格式

  return request({
    url: '/teacher/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data', // 明确设置内容类型
      // 如果需要token认证可添加：
      // 'Authorization': `Bearer ${getToken()}`
    },
    transformRequest: [data => data] // 防止axios自动转换
  });
};

