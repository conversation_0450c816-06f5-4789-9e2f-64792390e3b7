// utils/resourceUtils.js
export function buildFormData(file) {
  const formData = new FormData()
  formData.append('file', file, file.name)
  return formData
}

export function formatFolderItem(item) {
  return {
    id: item.id,
    name: item.name,
    type: 'folder',
    size: '--',
    parent: item.parentId || 'root',
    ...item
  }
}

export function formatFileItem(item) {
  return {
    id: item.id,
    name: item.name,
    type: 'file',
    parent: item.folderId || 'root',
    url: item.url,
    fileExtension: item.fileExtension,
    ...item
  }
}

export function formatError(error) {
  if (error.response) {
    return new Error(
      error.response.data.message || error.response.data.error || `服务器错误: ${error.response.status}`
    )
  } else if (error.request) {
    return new Error('网络错误，请检查您的网络连接')
  } else {
    return error instanceof Error ? error : new Error(String(error))
  }
}
