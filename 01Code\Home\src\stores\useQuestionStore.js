// src/stores/useQuestionStore.js
import { defineStore } from 'pinia'

const STORAGE_KEY = 'selected_test_cache'

export const useQuestionStore = defineStore('selectedTest', {
    state: () => ({
        selectedTests: JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]')
    }),
    actions: {
        setTests(tests) {
            this.selectedTests = tests
            localStorage.setItem(STORAGE_KEY, JSON.stringify(this.selectedTests))
        },
        clearTests() {
            this.selectedTests = []
            localStorage.removeItem(STORAGE_KEY)
        },
        addTests(newTests) {
            const existingIds = new Set(this.selectedTests.map(t => t.id))
            const filtered = newTests.filter(t => !existingIds.has(t.id))
            this.selectedTests = [...this.selectedTests, ...filtered]
            localStorage.setItem(STORAGE_KEY, JSON.stringify(this.selectedTests))
        }
    }
})
