<template>
  <div class="my-participate">
    <div class="tab-header">
      <button 
        v-for="(tab, index) in tabs" 
        :key="index" 
        @click="activeTab = tab.value"
        :class="{ active: activeTab === tab.value }"
      >
        {{ tab.label }}
      </button>
    </div>
    <!-- 我的回答内容 -->
    <div v-if="activeTab === 'my-answers'" class="my-answers-content">
      <!-- 回答列表 -->
      <div class="answer-list">
        <div 
          v-for="(item, index) in answerList" 
          :key="index" 
          class="answer-item"
          @click="openDiscussDetail(item.postId)"
        >
          <div class="answer-header">
  <span class="topic-title">
    <span v-if="item.isPinned" class="pinned-tag">【置顶】</span>
    <img :src="item.isAnswer ? IconAnswer : IconQuestion" class="header-icon" />
    {{ item.postTitle }}
  </span>
  <span class="update-time">{{ formatAgo(item.publishTime) }}</span>
            </div>
            <div class="answer-content">
              <img :src="IconAnswer" class="content-icon" />
              <span v-html="item.content"></span>
            </div>
        </div>
      </div>
      
      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          v-if="answerTotal > 0"
          class="pagination"
          background
          layout="prev, pager, next"
          :current-page="answerCurrentPage" 
          :page-size="answerPageSize"
          :total="answerTotal"
          @current-change="handleAnswerPageChange"
        />
      </div>
      
      <!-- 加载状态 -->
      <div v-if="answerLoading" class="loading">加载中...</div>
      <div v-if="!answerLoading && answerList.length === 0" class="empty">暂无回答</div>
    </div>
    <!-- 我的话题内容 -->
    <div v-if="activeTab === 'my-topics'" class="my-topics-content">
      <!-- 话题列表 -->
      <div class="topic-list">
        <div 
          v-for="(item, index) in topicList" 
          :key="index" 
          class="topic-item"
          @click="openDiscussDetail(item.id)"
        >
          <div class="topic-content">
            <div class="topic-title">
              <span v-if="item.isPinned" class="pinned-tag">【置顶】</span>
              {{ item.title }}
              <span v-if="item.status === 0" class="status-tag unapproved">未审核</span>
              <span v-else-if="item.status === 1" class="status-tag approved">已通过</span>
              <span v-else-if="item.status === 2" class="status-tag rejected">未通过</span>
            </div>
            <div class="topic-meta">
              <span>{{ item.viewCount || 0 }} 人浏览</span>
              <span>{{ item.answerCount || 0 }} 条回答</span>
              <span>{{ formatAgo(item.publishTime) }}更新</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          v-if="total > 0"
          class="pagination"
          background
          layout="prev, pager, next"
          :current-page="currentPage" 
          :page-size="pageSize"
          :total="total"
          @current-change="handlePageChange"
        />
      </div>
      
      <!-- 加载状态 -->
      <div v-if="loading" class="loading">加载中...</div>
      <div v-if="!loading && topicList.length === 0" class="empty">暂无话题</div>
    </div>
    <!-- 回答点赞内容 -->
      <div v-if="activeTab === 'answer-like'" class="my-likes-content">
    <!-- 点赞列表 -->
    <div class="like-list">
      <div 
        v-for="(item, index) in likeList" 
        :key="index" 
        class="like-item"
        @click="openDiscussDetail(item.postId)"
      >
        <div class="like-header">
          <div class="user-info">
            <img :src="item.userAvatar || defaultTeacherAvatar" class="avatar" />
            <div class="user-meta">
              <span class="user-name">{{ item.userName }}</span>
              <span class="user-role">{{ formatRole(item.userRole) }}</span>
            </div>
          </div>
          <span class="like-time">{{ formatAgo(item.createTime) }}</span>
        </div>
        <div class="comment-content">
          <img :src="IconAnswer" class="comment-icon" />
          <span v-html="item.commentContent"></span>
        </div>
        <div class="topic-info">
          <span class="topic-title">
            <span v-if="item.isPinned" class="pinned-tag">【置顶】</span>
            所属话题：{{ item.postTitle }}
          </span>
        </div>
      </div>
    </div>
    
    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        v-if="likeTotal > 0"
        class="pagination"
        background
        layout="prev, pager, next"
        :current-page="likeCurrentPage" 
        :page-size="likePageSize"
        :total="likeTotal"
        @current-change="handleLikePageChange"
      />
    </div>
    
    <!-- 加载状态 -->
    <div v-if="likeLoading" class="loading">加载中...</div>
    <div v-if="!likeLoading && likeList.length === 0" class="empty">暂无点赞</div>
  </div>
      <div v-if="activeTab === 'answer-stor'" class="my-stars-content">
    <!-- 收藏列表 -->
    <div class="star-list">
      <div 
        v-for="(item, index) in starList" 
        :key="index" 
        class="star-item"
        @click="openDiscussDetail(item.postId)"
      >
        <div class="star-header">
          <div class="user-info">
            <img :src="item.userAvatar || defaultTeacherAvatar" class="avatar" />
            <div class="user-meta">
              <span class="user-name">{{ item.userName }}</span>
              <span class="user-role">{{ formatRole(item.userRole) }}</span>
            </div>
          </div>
          <span class="star-time">{{ formatAgo(item.createTime) }}</span>
        </div>
        <div class="comment-content">
          <img :src="IconAnswer" class="comment-icon" />
          <span v-html="item.commentContent"></span>
        </div>
        <div class="topic-info">
          <span class="topic-title">
            <span v-if="item.isPinned" class="pinned-tag">【置顶】</span>
            所属话题：{{ item.postTitle }}
          </span>
        </div>
      </div>
    </div>
    
    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        v-if="starTotal > 0"
        class="pagination"
        background
        layout="prev, pager, next"
        :current-page="starCurrentPage" 
        :page-size="starPageSize"
        :total="starTotal"
        @current-change="handleStarPageChange"
      />
    </div>
    
    <!-- 加载状态 -->
    <div v-if="starLoading" class="loading">加载中...</div>
    <div v-if="!starLoading && starList.length === 0" class="empty">暂无收藏</div>
  </div>
  </div>
  
</template>

<script setup>
import { ref, onMounted, computed, watch, inject } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth/auth'
import { getTeacherPosts,getCommentList,getPostDetail,getCommentLikes,getLikeList,getCommentDetail,getUserDetail,getStarList   } from '@/api/teacher/discuss'
import { formatAgo } from '@/utils/dateUtils'
import { ElMessage } from 'element-plus'
import IconQuestion from '@/assets/img/Teacher/icon-question.png'
import IconAnswer from '@/assets/img/Teacher/icon-answer.png'

const route = useRoute()
const router = useRouter()
const refreshTopics = inject('refreshTopics')
const postTitleCache = ref({})

// 标签数据
const tabs = ref([
  { label: '我的回答', value: 'my-answers' },
  { label: '我的话题', value: 'my-topics' },
  { label: '回答点赞', value: 'answer-like' },
  { label: '回答收藏', value: 'answer-stor' },
])
// 激活的标签
const activeTab = ref('my-topics')
// 我的回答相关状态
const answerList = ref([])
const answerLoading = ref(false)
const answerCurrentPage = ref(1)
const answerPageSize = ref(5)
const answerTotal = ref(0)

// 我的点赞相关状态
const likeList = ref([])
const likeLoading = ref(false)
const likeCurrentPage = ref(1)
const likePageSize = ref(3)
const likeTotal = ref(0)

// 我的收藏相关状态
const starList = ref([])
const starLoading = ref(false)
const starCurrentPage = ref(1)
const starPageSize = ref(3)
const starTotal = ref(0)

// 格式化角色显示
const formatRole = (role) => {
  switch(role) {
    case 1: return '学生'
    case 2: return '教师'
    case 0: return '管理员'
    default: return '未知角色'
  }
}
// 加载状态
const loading = ref(false)

// 存储所有话题数据
const allTopics = ref([])

// 分页设置
const currentPage = ref(1)
const pageSize = ref(5) // 每页显示数量
const total = computed(() => allTopics.value.length) // 总数量
/**
 * 获取我的回答数据
 */
const fetchMyAnswers = async () => {
  try {
    answerLoading.value = true
    
    const authStore = useAuthStore()
    if (!authStore.user?.id) {
      throw new Error('用户未登录或用户信息不完整')
    }
    
    const params = {
      pageNum: answerCurrentPage.value,
      pageSize: answerPageSize.value,
      commenterId: authStore.user.id
    }
    
    const res = await getCommentList(params)
    if (res.code === 200 && res.result?.records) {
      // 获取所有回答对应的话题详情
      const answersWithPostTitles = await Promise.all(
  res.result.records.map(async (answer) => {
    // 检查缓存
    if (postTitleCache.value[answer.postId]) {
      return {
        ...answer,
        postTitle: postTitleCache.value[answer.postId].title,
        isPinned: postTitleCache.value[answer.postId].isPinned
      }
    }
    
    try {
      const postRes = await getPostDetail(answer.postId)
      if (postRes.code === 200 && postRes.result) {
        // 存入缓存
        postTitleCache.value[answer.postId] = {
          title: postRes.result.title,
          isPinned: postRes.result.isPinned
        }
        return {
          ...answer,
          postTitle: postRes.result.title,
          isPinned: postRes.result.isPinned
        }
      }
      return {
        ...answer,
        postTitle: '未知话题',
        isPinned: false
      }
    } catch (error) {
      console.error('获取话题详情失败:', error)
      return {
        ...answer,
        postTitle: '获取话题失败',
        isPinned: false
      }
    }
  })
)
      
      answerList.value = answersWithPostTitles
      answerTotal.value = res.result.total
    } else {
      answerList.value = []
      answerTotal.value = 0
      ElMessage.warning('暂无回答数据')
    }
  } catch (error) {
    console.error('获取我的回答列表失败:', error)
    ElMessage.error(error.message || '获取我的回答列表失败')
    answerList.value = []
    answerTotal.value = 0
    
    if (error.message.includes('未登录')) {
      router.push('/login')
    }
  } finally {
    answerLoading.value = false
  }
}
/**
 * 回答分页变化处理
 */
const handleAnswerPageChange = (page) => {
  answerCurrentPage.value = page
  fetchMyAnswers()
}
// 计算当前显示的话题列表（分页）
const topicList = computed(() => {
  // 先进行置顶排序
  const sortedTopics = [...allTopics.value].sort((a, b) => {
    // 置顶的排在前面
    if (a.isPinned && !b.isPinned) return -1
    if (!a.isPinned && b.isPinned) return 1
    // 如果都置顶或都不置顶，按发布时间降序
    return new Date(b.publishTime) - new Date(a.publishTime)
  })
  
  // 然后进行分页
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return sortedTopics.slice(start, end)
})
/**
 * 获取我的话题数据
 */
const fetchMyTopics = async () => {
  try {
    loading.value = true
    const params = {
      pageNum: 1,
      pageSize: 100, // 获取适量数据，前端分页
      courseId: route.params.courseId,
      onlyMine: true, // 只获取自己的话题
      withCommentCount: true
    }
    
    const res = await getTeacherPosts(params)
    if (res.code === 200 && res.result?.records) {
      allTopics.value = res.result.records.map(topic => ({
        ...topic,
        answerCount: topic.commentCount || 0,
        isPinned: topic.isPinned || false
      }))
    } else {
      allTopics.value = []
    }
  } catch (error) {
    console.error('获取我的话题列表失败:', error)
    ElMessage.error('获取我的话题列表失败')
    allTopics.value = []
  } finally {
    loading.value = false
  }
}

/**
 * 分页变化处理
 */
const handlePageChange = (page) => {
  currentPage.value = page
}


/**
 * 获取我的点赞数据
 */
const fetchMyLikes = async () => {
  try {
    likeLoading.value = true
    
    const params = {
      pageNum: likeCurrentPage.value,
      pageSize: likePageSize.value
    }
    
    const res = await getLikeList(params)
    if (res.code === 200 && res.result?.records) {
      // 获取所有点赞对应的评论详情和用户信息
      const likesWithDetails = await Promise.all(
        res.result.records.map(async (like) => {
          try {
            // 获取评论详情
            const commentRes = await getCommentDetail(like.commentId)
            if (commentRes.code !== 200 || !commentRes.result) {
              return null
            }
            
            const comment = commentRes.result
            // 获取用户详情
            const userRes = await getUserDetail(like.userId)
            const user = userRes.code === 200 ? userRes.result : {}
            
            // 获取话题详情
            const postRes = await getPostDetail(comment.postId)
            const postTitle = postRes.code === 200 ? postRes.result.title : '未知话题'
            const isPinned = postRes.code === 200 ? postRes.result.isPinned : false
            
            return {
              ...like,
              commentContent: comment.content,
              postId: comment.postId,
              postTitle,
              isPinned,
              userId: like.userId,
              userName: user.name || '匿名用户',
              userAvatar: user.avatar || defaultAvatar,
              userRole: user.role || 1,
              createTime: like.createTime
            }
          } catch (error) {
            console.error('获取点赞详情失败:', error)
            return null
          }
        })
      )
      
      // 过滤掉获取失败的项
      likeList.value = likesWithDetails.filter(item => item !== null)
      likeTotal.value = res.result.total
    } else {
      likeList.value = []
      likeTotal.value = 0
      ElMessage.warning('暂无点赞数据')
    }
  } catch (error) {
    console.error('获取我的点赞列表失败:', error)
    ElMessage.error(error.message || '获取我的点赞列表失败')
    likeList.value = []
    likeTotal.value = 0
  } finally {
    likeLoading.value = false
  }
}

/**
 * 点赞分页变化处理
 */
const handleLikePageChange = (page) => {
  likeCurrentPage.value = page
  fetchMyLikes()
}

/**
 * 获取我的收藏数据
 */
const fetchMyStars = async () => {
  try {
    starLoading.value = true
    
    const params = {
      pageNum: starCurrentPage.value,
      pageSize: starPageSize.value
    }
    
    const res = await getStarList(params)
    if (res.code === 200 && res.result?.records) {
      // 获取所有收藏对应的评论详情和用户信息
      const starsWithDetails = await Promise.all(
        res.result.records.map(async (star) => {
          try {
            // 首先获取评论详情
            const commentRes = await getCommentDetail(star.postId) // 注意这里star.postId实际上是commentId
            if (commentRes.code !== 200 || !commentRes.result) {
              return null
            }
            
            const comment = commentRes.result
            // 然后获取话题详情
            const postRes = await getPostDetail(comment.postId)
            const postTitle = postRes.code === 200 ? postRes.result.title : '未知话题'
            const isPinned = postRes.code === 200 ? postRes.result.isPinned : false
            
            // 获取用户详情
            const userRes = await getUserDetail(star.userId)
            const user = userRes.code === 200 ? userRes.result : {}
            
            return {
              ...star,
              commentContent: comment.content,
              postId: comment.postId, // 这里使用评论关联的实际postId
              postTitle,
              isPinned,
              userName: user.name || '匿名用户',
              userAvatar: user.avatar || defaultAvatar,
              userRole: user.role || 1
            }
          } catch (error) {
            console.error('获取收藏详情失败:', error)
            return null
          }
        })
      )
      
      // 过滤掉获取失败的项
      starList.value = starsWithDetails.filter(item => item !== null)
      starTotal.value = res.result.total
    } else {
      starList.value = []
      starTotal.value = 0
      ElMessage.warning('暂无收藏数据')
    }
  } catch (error) {
    console.error('获取我的收藏列表失败:', error)
    ElMessage.error(error.message || '获取我的收藏列表失败')
    starList.value = []
    starTotal.value = 0
  } finally {
    starLoading.value = false
  }
}

/**
 * 收藏分页变化处理
 */
const handleStarPageChange = (page) => {
  starCurrentPage.value = page
  fetchMyStars()
}
/**
 * 打开话题详情页
 */
const openDiscussDetail = (topicId) => {
  const routeData = router.resolve({
    name: 'DiscussDetail',
    params: {
      courseId: route.params.courseId,
      topicId: topicId
    }
  })
  window.open(routeData.href, '_blank')
}

// 组件挂载时获取数据
onMounted(() => {
  if (activeTab.value === 'my-topics') {
    fetchMyTopics()
  }
})

// 监听标签切换
watch(activeTab, (newVal) => {
  if (newVal === 'my-topics') {
    fetchMyTopics()
    currentPage.value = 1
  } else if (newVal === 'my-answers') {
    fetchMyAnswers()
    answerCurrentPage.value = 1
  } else if (newVal === 'answer-like') {
    fetchMyLikes()
    likeCurrentPage.value = 1
  } else if (newVal === 'answer-stor') {
    fetchMyStars()
    starCurrentPage.value = 1
  }
})

// 暴露方法给父组件
defineExpose({
  fetchMyTopics
})
</script>

<style lang="scss" scoped>
.my-participate {
  margin-bottom: 20px;
  padding: 30px;
  background-color: white;
  height: 80vh;
  position: relative;
  display: flex;
  flex-direction: column;
  
  .tab-header {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;

    button {
      padding: 8px 16px;
      border: none;
      background: transparent;
      cursor: pointer;
      color: #666;
      transition: all 0.3s ease;
      position: relative;

      &:hover {
        color: #333;
      }

      &.active {
        color: #333;
        font-weight: 500;
      }

      &.active::after {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        height: 3px;
        background-color: $primary-color;
      }
    }
  }

    .my-answers-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .answer-list {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
    
    .answer-item {
      padding: 15px;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      cursor: pointer;
      transition: box-shadow 0.3s;
      
      &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }
      
      .answer-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        
        .update-time {
          font-size: 12px;
          color: #999;
          margin-left: 10px;
        }
      }
    }
  }
  
  .my-topics-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .topic-list {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
    
    .topic-item {
      padding: 12px;
      border-bottom: 1px solid #e5e7eb;
      cursor: pointer;
      transition: background-color 0.3s;
      
      &:hover {
        background-color: #f9fafb;
      }
      
      .topic-title {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 8px;
        color: #333;
        display: flex;
        align-items: center;
        gap: 8px;
        
        &:hover {
          color: $primary-color;
        }
      }
      
      .topic-meta {
        font-size: 12px;
        color: #909399;
        display: flex;
        gap: 20px;
      }
    }
  }
  
  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
  
  .loading, .empty {
    text-align: center;
    padding: 20px;
    color: #999;
  }
}

.pinned-tag {
  color: #ff4d4f;
  font-weight: bold;
  font-size: 0.9em;
}

.status-tag {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 3px;
  
  &.unapproved {
    background-color: #fff7e6;
    color: #fa8c16;
    border: 1px solid #ffd591;
  }
  
  &.approved {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
  }
  
  &.rejected {
    background-color: #fff1f0;
    color: #ff4d4f;
    border: 1px solid #ffa39e;
  }
}

.topic-title {
  display: flex;
  align-items: center; /* 垂直居中 */
  gap: 6px; /* 图标和文字间距 */
  
  .header-icon {
    width: 16px;
    height: 16px;
    object-fit: contain;
  }
}

.answer-content {
  display: flex;
  align-items: center; /* 垂直居中 */
  gap: 8px;
  margin-top: 6px;
  
  .content-icon {
    width: 16px;
    height: 16px;
    object-fit: contain;
    flex-shrink: 0;
    margin-right: 5px;
  }
  
  /* 保持原有文本样式 */
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.my-likes-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.like-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
  
  .like-item {
    padding: 15px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: box-shadow 0.3s;
    
    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
    
    .like-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      
      .user-info {
        display: flex;
        align-items: center;
        gap: 10px;
        
        .avatar {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          object-fit: cover;
        }
        
        .user-meta {
          display: flex;
          flex-direction: column;
          
          .user-name {
            font-weight: 500;
            font-size: 14px;
          }
          
          .user-role {
            font-size: 12px;
            color: #999;
          }
        }
      }
      
      .like-time {
        font-size: 12px;
        color: #999;
      }
    }
    
    .comment-content {
      display: flex;
      gap: 8px;
      margin: 10px 0;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 4px;
      align-items: center;
      
      .comment-icon {
        width: 16px;
        height: 16px;
        object-fit: contain;
        flex-shrink: 0;
      }
      
      span {
        font-size: 14px;
        color: #333;
        line-height: 1.6;
      }
    }
    
    .topic-info {
      margin-top: 10px;
      padding-top: 10px;
      border-top: 1px dashed #eee;
      font-size: 12px;
      color: #999;
      
      .topic-title {
        display: flex;
        align-items: center;
        gap: 6px;
      }
    }
  }
}
.my-stars-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.star-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
  
  .star-item {
    padding: 15px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s;
    
    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
    
    .star-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      
      .user-info {
        display: flex;
        align-items: center;
        gap: 10px;
        
        .avatar {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          object-fit: cover;
        }
        
        .user-meta {
          display: flex;
          flex-direction: column;
          
          .user-name {
            font-weight: 500;
            font-size: 14px;
          }
          
          .user-role {
            font-size: 12px;
            color: #999;
          }
        }
      }
      
      .star-time {
        font-size: 12px;
        color: #999;
      }
    }
    
    .comment-content {
      display: flex;
      gap: 8px;
      margin: 10px 0;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 4px;
      align-items: flex-start; /* 修改为flex-start避免垂直拉伸 */
      
      .comment-icon {
        width: 16px;
        height: 16px;
        min-width: 16px; /* 确保最小宽度 */
        object-fit: contain;
        margin-top: 2px; /* 可选，用于垂直对齐调整 */
      }
      
      span {
        font-size: 14px;
        color: #333;
        line-height: 1.6;
      }
    }
    
    .topic-info {
      margin-top: 10px;
      padding-top: 10px;
      border-top: 1px dashed #eee;
      font-size: 12px;
      color: #999;
      cursor: pointer;
      
      &:hover {
        color: $primary-color;
      }
      
      .topic-title {
        display: flex;
        align-items: center;
        gap: 6px;
      }
    }
  }
}
</style>