@use '@/styles/variables' as *;

.home-header {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  padding-top: 16vh;
  padding-bottom: 8vh;
  padding-left: 5vw;
  padding-right: 5vw;
  background: url('@/assets/img/Home/ic_home_bg.png') top/cover no-repeat;
  color: #fff;

  .header-01 {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    gap: 2rem;
    width: 100%;

    .pro-info {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;

      .school-name {
        font-size: 1rem;
        opacity: 0.9;
      }

      .pro-title {
        font-size: 4rem;
        font-weight: bold;
      }
    }

    .visits {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      background-color: $bg-gray-color;
      border-radius: 15px;

      &>div {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 10px 30px;
        min-width: 150px;
      }

      .visit-label {
        font-size: 0.8rem;
        opacity: 0.5;
        margin-bottom: 0.5rem;
      }

      .visit-number {
        font-size: 1.2rem;
      }
    }
  }

  .header-02 {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;

    .pro-btn {
      width: fit-content;
      padding: 10px 20px;
      background-color: white;
      border: 1px solid #fff;
      color: $primary-color;
      border-radius: 50px;
      font-size: 1.4rem;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        color: white;
        background-color: rgba(255, 255, 255, 0.3);
      }
    }

    .stats {
      display: flex;
      gap: 60px;

      .stat-item {
        text-align: end;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        box-shadow: 0 -1px 0 inset rgba(255, 255, 255, 0.3);

        &>div {
          display: flex;
          align-items: flex-end;
          gap: 0.8rem;
        }

        .stat-number {
          font-size: 5rem;
          margin-bottom: 0;
        }

        .stat-label {
          opacity: 0.5;
        }
      }
    }
  }

  .header-03 {
    margin-top: 4rem;
    width: 100%;
    
/*     .course-scroll {
      display: flex;
      gap: 1.5rem;
      overflow-x: auto;
      padding-bottom: 1rem;
      scroll-snap-type: x mandatory;
      scroll-behavior: smooth;
      scrollbar-width: none;

      &::-webkit-scrollbar {
        display: none;
      }

      &>* {
        scroll-snap-align: start;
        flex: 0 0 auto;
      }
    } */
    

    .course-scroll {
  display: flex;
  gap: 1.5rem;
  overflow-x: auto;
  padding-bottom: 1rem;
  scroll-snap-type: x mandatory;
  scroll-behavior: smooth;
  scrollbar-width: none;
  cursor: grab; // 鼠标悬停时显示抓取手势
  
  // 滚动时的状态
  &:active {
    cursor: grabbing;
  }
  
  // 隐藏滚动条
  &::-webkit-scrollbar {
    display: none;
  }
  
  // 确保子元素对齐且不被压缩
  & > * {
    scroll-snap-align: start;
    flex: 0 0 auto;
  }
  
}
    .semester-nav {
      display: flex;
      gap: 2rem;
      margin-top: 2rem;
      align-items: center;
      justify-content: space-between;
      /* 确保子项平均分配空间 */
    }

    .semester-item {
      position: relative;
      flex: 1;
      /* 让每个子项平均分配宽度 */
      padding: 0.8rem 1.2rem;
      cursor: pointer;
      text-align: center;
      transition: all 0.3s ease;
      display: flex;
      /* 使子项内容居中 */
      justify-content: center;
      /* 水平居中 */
      align-items: center;
      /* 垂直居中 */
    }

    .indicator {
      position: absolute;
      top: -10px;
      left: 50%;
      transform: translateX(-50%);
      width: 170px;
      height: 6px;
      background-color: white;
      border-radius: 2px;
      transition: all 0.3s ease;
    }

    .semester-item.active {
      color: $primary-color;
      font-weight: bold;
    }

    .semester-item.active .indicator {
      background-color: $primary-color;
      width: 170px;
    }

    .semester-item:hover {
      opacity: 0.8;
    }
  }
}

.home-plan {
  display: flex;
  flex-direction: row;
  margin-top: 32px;

  .smartImage {
    width: 50%;
    height: 100%;
    background: black;
  }
}