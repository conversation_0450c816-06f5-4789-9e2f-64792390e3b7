<!--src\views\public\course\Course_Center\Overview\index.vue-->
<template>
  <div class="anchor-container">
    <el-row class="container-row">
      <!-- 左侧导航 -->
      <el-col :span="4" class="anchor-column">
        <div class="sticky-anchor">
          <el-anchor direction="vertical" offset="80" @click="handleAnchorClick">
            <el-anchor-link href="#part1" title="课程核心数据" />
            <el-anchor-link href="#part2" title="课程目标" />
            <el-anchor-link href="#part3" title="教学重点难点" />
            <el-anchor-link href="#part4" title="教学计划" />
            <el-anchor-link href="#part5" title="教学内容" />
            <el-anchor-link href="#part6" title="考核要求" />
          </el-anchor>
        </div>
      </el-col>

      <!-- 右侧内容 -->
      <el-col :span="20" class="content-column">
        <!-- 课程核心数据 -->
        <section id="part1" class="content-section">
          <span class="card-title">课程核心数据</span>
          <div class="card-grid">
            <!-- 知识点 -->
            <div class="card card-blue">
              <div class="card-label">知识点</div>
              <div class="card-value">138</div>
            </div>

            <!-- 知识节点和知识模块 -->
            <div class="knowledge-card">
              <div class="small-card  top-card ">
                <div class="card-label">知识节点</div>
                <div class="card-value">937</div>
              </div>
              <div class="small-card  bottom-card card-lightblue">
                <div class="card-label">知识模块</div>
                <div class="card-value">8</div>
              </div>
            </div>

            <!-- 教学资源 -->
            <div class="card card-lightblue">
              <div class="card-label">教学资源</div>
              <div class="card-value">1003</div>
            </div>
            <!-- 外部资源 -->
            <div class="card card-lightgreen">
              <div class="card-label">引用外部资源</div>
              <div class="card-value">523</div>
            </div>

            <!-- 能力目标 -->
            <div class="knowledge-card">
              <div class="small-card  top-card ">
                <div class="card-label">能力目标</div>
                <div class="card-value">14</div>
              </div>
              <div class="small-card bottom-card card-lightblue">
                <div class="card-label">问题图谱</div>
                <div class="card-value">101</div>
              </div>
            </div>

            <!-- AI出题 -->
            <div class="card  ai-card">
              <div class="card-label">AI自动出题</div>
              <div class="card-value">130</div>
              <div class="card-badge">AI智能</div>
            </div>
          </div>
        </section>
        <!-- 课程背景 -->
        <section id="part2" class="content-section">
          <span class="card-title">课程目标</span>
          <div class="Description">
            {{ courseData?.courseTarget ? courseData.courseTarget : '暂无内容' }}
          </div>

        </section>
        <!-- 课程定位 -->
        <section id="part3" class="content-section">
          <span class="card-title">重点难点</span>
          <div class="Description">
            {{ courseData?.coursePoints || '暂无内容' }}
          </div>
        </section>
        <!-- 课程目标 -->
        <section id="part4" class="content-section">
          <span class="card-title">教学计划</span>
          <div class="Description">
             {{ courseData?.coursePlan || '暂无内容' }}
          </div>
        </section>
        <!-- 课程设计原则 -->
        <section id="part5" class="content-section">
          <span class="card-title">教学内容</span>
          <div class="Description">
            {{ courseData?.courseContent || '暂无内容' }}
          </div>
        </section>
        <!-- 课程特色 -->
        <section id="part6" class="content-section">
          <span class="card-title">考核要求</span>
          <div class="Description">
            {{ courseData?.courseRequire || '暂无内容' }}
          </div>
        </section>
        
     
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { getCourseDetail } from '@/api/public/course/course';
import { getCourseInformationList } from '@/api/public/course/course'

const route = useRoute();
const courseData = ref(null);

const handleAnchorClick = (e) => {
  e.preventDefault();
  const href = e.target.getAttribute('href');
  if (href) {
    const target = document.querySelector(href);
    target?.scrollIntoView({ behavior: 'smooth' });
  }
};

onMounted(async () => {
  try {
    const res = await getCourseInformationList({courseId: route.params.courseId});
    
    // 直接解构第一条数据
    if (res.result?.records?.length > 0) {
      courseData.value = res.result.records[0];
      console.log(courseData.value);
      
    }
  } catch (error) {
    console.error('获取课程信息失败:', error);
  }
});
</script>

<style lang="scss" scoped>
// 公共变量
$primary-padding: 0.925vw;
$border-radius: 0.5vw;
$box-shadow: 0 0.093vw 0.37vw rgba(0, 0, 0, 0.1);
$content-padding-top: 4vw;
$card-gap: 2vw;
$meta-size: 1vw;

/* 颜色样式 */
$color-blue: #3b82f6;
$color-lightblue: #dbeafe;
$color-lightgreen: #dcfce7;
$color-gray: #f3f4f6;

.anchor-container {
  width: 100%;
  padding: $primary-padding;
  min-height: 100vh;
}

.anchor-column {
  padding-right: $primary-padding;

  .sticky-anchor {
    position: sticky;
    top: 1vw;
    height: fit-content;
    background: white;
    padding: 0.05208vw;
    border-radius: $border-radius;
    box-shadow: $box-shadow;

    ::v-deep .el-anchor__list {
      padding-left: 1vw;  // 调整左侧内边距
      margin: 0.5vw 0;     // 调整上下外边距
    }
    ::v-deep .el-anchor__link {
      color: $text-color;
      font-size: 0.95vw;
      max-height:3vw;
      padding: 0.5vw;
      line-height: 2.5;
    }



    ::v-deep .el-anchor__link.is-active {
      color: $primary-color !important;
    }

    ::v-deep .el-anchor__marker {
      background-color: $primary-color !important;
      width: 0.152vw;
    }

  }
}

.content-column {
  padding-left: $primary-padding;
}

.content-section {
  padding-top: $content-padding-top;
  /* min-height: 2.604vw;
 */
  .card-title {
    font-size: 2.35vw;
  }

  .Description {
    white-space: pre-line;
    color: #333;
    font-family: "Alibaba PuHuiTi 3.0";
    font-size: 1.5vw;
    line-height: 2vw;
    margin-top: 1vw;
  }
}

/* 课程核心数据 */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, 10vw);
  height: 14.875vw;
  gap: $card-gap;
  margin-top: 1vw;

  .card,
  .small-card {
    width: 11vw;
    padding: 1vw;
    border-radius: $border-radius;
    box-shadow: 0 0.104vw 0.312vw rgba(0, 0, 0, 0.08);
    display: flex;
    flex-direction: column;
    justify-content: space-between;


    .card-label {
      align-self: flex-start;
      font-size: 1.35vw;
      margin-bottom: 1vw;
    }

    .card-value {
      font-size: 1.25vw;
      font-weight: bold;
      align-self: flex-start;
    }
  }

  .small-card {
    padding: 0.5625vw;
  }
}

.card-blue {
  background-color: $color-blue;
  color: white;
}

.card-lightblue {
  background-color: $color-lightblue;
}

.card-lightgreen {
  background-color: $color-lightgreen;
}

.card-gray {
  background-color: $color-gray;
}

/* 知识节点模块 */
.knowledge-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;

  .top-card,
  .bottom-card {
    flex: 1;

    &:not(:last-child) {
      margin-bottom: 0.26vw;
    }

  }
}

/* AI徽标 */
.ai-card {
  position: relative;

  .card-badge {
    position: absolute;
    top: 0.417vw;
    right:0.417vw;
    font-size: 0.572vw;
    background-color: #e0f2fe;
    color: $primary-color;
    padding: 0.104vw 0.312vw;
    border-radius: 0.312vw;
  }
}

/* 课程定位元数据 */
.course-position-meta {
  display: inline-block;
  padding:0 0.52vw;
  border-radius: $border-radius;
  background: #f7f7f7;
  color: #000;
  font-family: "Alibaba PuHuiTi 3.0";
  font-size: $meta-size;
  font-weight: 700;
  opacity: 0.9;
  margin: 0 0.52vw  0;
}

/* 教学计划表 */
.teaching-plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  gap: 1vw;
  margin-bottom: 1.04vw;

  .meta-container {
    display: flex;
    gap: 0.52vw;

    .CourseMeta {
      display: flex;
      min-width: 5.2vw;
      justify-content: space-between;
      padding: 0.65vw;
      border-radius: $border-radius;
      background: #fafafa;

      .meta-label,
      .meta-value {
        font-family: "Alibaba PuHuiTi 3.0";
        font-size: 0.9375vw;
        font-weight: 700;
        opacity: 0.9;
      }

      .meta-label {
        margin-right: 0.417vw;
      }
    }
  }
}

.teaching-plan-container {
  max-height: 21.2625vw;
  overflow-y: auto;
  border: 0.052vw solid #ebebeb;
  padding: 1.04vw;
  border-radius: 1.25vw;

  .teaching-plan-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.625vw;
    border-radius: $border-radius;
    background: #fafafa;
    box-shadow: 0 0.104vw 0.208vw rgba(0, 0, 0, 0.1);
    font-size: 1vw;
    margin-bottom: 0.725vw;

    .teaching-plan-cell {
      flex: 1;
      margin: 0 0.52vw;
      white-space: nowrap; // 防止内容换行

      &.left-cell {
        text-align: left;
      }

      &.right-cell {
        text-align: right;
      }
    }
  }
}
</style>