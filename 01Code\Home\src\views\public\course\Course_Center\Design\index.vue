<template>
  <div class="anchor-container">
    <el-row class="container-row">
      <!-- 左侧导航 -->
      <el-col :span="4" class="anchor-column">
        <div class="sticky-anchor">
          <el-anchor direction="vertical" offset="80" @click="handleAnchorClick">
            <el-anchor-link href="#course-system-design" title="课程体系设计" />
            <el-anchor-link href="#course-frame" title="课程框架" />
            <el-anchor-link href="#knowledge-module" title="知识模块" />
          </el-anchor>
        </div>
      </el-col>

      <!-- 右侧内容 -->
      <el-col :span="20" class="content-column">
        <!-- 课程体系设计 -->
        <section id="course-system-design" class="content-section">
          <span class="card-title">课程体系设计 
            <el-button
              type="primary"
              class="view-detail-btn"
              @click="openMapDetail"
            >
              查看详情
            </el-button>
          </span>
          <div class="course-map-container">
            <img
              src="  "
              alt="课程图谱"
              class="course-map-screenshot"
            />
          </div>
        </section>

        <!-- 课程框架 -->
        <section id="course-frame" class="content-section">
          <span class="card-title">课程框架</span>
          <div
            v-for="(module, index) in courseFrameModules"
            :key="index"
            class="frame-module"
            :style="{ 
              'background-color': getModuleColor(index),
              'background-color': module.isHover ? getHoverColor(index) : getModuleColor(index)
            }"
            @mouseenter="handleModuleHover(module, index)"
            @mouseleave="handleModuleLeave(module)"
          >
            <div class="module-header">
              <span>{{ module.title }}</span>
              <el-icon v-if="module.isOpen">
                <Minus />
              </el-icon>
              <el-icon v-else>
                <Plus />
              </el-icon>
            </div>
            <div class="module-content" v-show="module.isOpen">
              {{ module.content }}
            </div>
          </div>
        </section>

        <!-- 知识模块 -->
        <section id="knowledge-module" class="content-section">
          <span class="card-title">知识模块</span>
          <div class="knowledge-module-content">
            <div class="teacher-text" v-html="knowledgeModuleText"></div>
            <div class="ai-image-container" v-if="knowledgeModuleAiImage">
              <img
                :src="knowledgeModuleAiImage"
                alt="AI 生成图片"
                class="ai-generated-image"
              />
            </div>
          </div>
        </section>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { Plus, Minus } from '@element-plus/icons-vue'; 

export default {
  components: {
    Plus,
    Minus,
  },
  data() {
    return {
      lightColors: [
        'hsl(200, 100%, 95%)',
        'hsl(120, 50%, 95%)',
        'hsl(30, 100%, 95%)',
        'hsl(340, 50%, 95%)',
        'hsl(240, 30%, 95%)',
        'hsl(180, 30%, 95%)'
      ],
      courseFrameModules: [],
      knowledgeModuleText: '创新创业课程分为3个模块，共20个单元。第一个模块：导论，涵盖创意、创造、创新及教育、创业及教育、创新创业教育、创新能力构成和创新能力测试，帮助学生建立创新和创业的基础认识。',
      knowledgeModuleAiImage: '',
      hoverLightenPercent: 8 // 悬停时亮度减少比例
    };
  },
  created() {
    this.courseFrameModules = [
      { title: '模块1:导论', content: '本章节作为课程的开篇，旨在引导学生理解创新的基本概念、类型和方式，同时探讨创意、创新与创造之间的内在联系。', isOpen: false, isHover: false },
      { title: '模块2:创造性思维', content: '本章节作为课程的开篇，旨在引导学生理解创新的基本概念、类型和方式，同时探讨创意、创新与创造之间的内在联系。', isOpen: false, isHover: false },
      { title: '模块3:创新方法', content: '本章节作为课程的开篇，旨在引导学生理解创新的基本概念、类型和方式，同时探讨创意、创新与创造之间的内在联系。', isOpen: false, isHover: false }
    ].map((module, index) => {
      return {
        ...module,
        colorIndex: index % this.lightColors.length
      };
    });
  },
  methods: {
    handleAnchorClick(e) {
      e.preventDefault();
      const href = e.target.getAttribute('href');
      if (href) {
        const target = document.querySelector(href);
        target?.scrollIntoView({ behavior: 'smooth' });
      }
    },
    handleModuleHover(module, index) {
      module.isOpen = true;
      module.isHover = true;
    },
    handleModuleLeave(module) {
      module.isOpen = false;
      module.isHover = false;
    },
    openMapDetail() {
      window.open('https://example.com/map-detail-page', '_blank');
    },
    getModuleColor(index) {
      const module = this.courseFrameModules[index];
      return this.lightColors[module.colorIndex];
    },
    getHoverColor(index) {
      const color = this.getModuleColor(index);
      if (color.startsWith('hsl')) {
        const hsl = color.match(/hsl\(\s*(\d+)\s*,\s*(\d+)%\s*,\s*(\d+)%\s*\)/);
        if (hsl) {
          const lightness = Math.min(100, parseInt(hsl[3]) - this.hoverLightenPercent);
          return `hsl(${hsl[1]}, ${hsl[2]}%, ${lightness}%)`;
        }
      }
      return color;
    }
  },
};
</script>

<style lang="scss" scoped>
// 公共变量
$primary-padding: 0.925vw;
$border-radius: 0.5vw;
$box-shadow: 0 0.093vw 0.37vw rgba(0, 0, 0, 0.1);
$content-padding-top: 4vw;
$card-gap: 2vw;
$meta-size: 1vw;

.anchor-container {
  width: 100%;
  padding: $primary-padding;
  min-height: 100vh;
}

.anchor-column {
  padding-right: $primary-padding;

  .sticky-anchor {
    position: sticky;
    top: 1vw;
    height: fit-content;
    background: white;
    padding: 0.05208vw;
    border-radius: $border-radius;
    box-shadow: $box-shadow;

    ::v-deep .el-anchor__list {
      padding-left: 1vw;
      margin: 0.5vw 0;
    }
    ::v-deep .el-anchor__link {
      color: #333;
      font-size: 0.95vw;
      max-height: 3vw;
      padding: 0.5vw;
      line-height: 2.5;
    }
    ::v-deep .el-anchor__link.is-active {
      color: #409eff !important;
    }
    ::v-deep .el-anchor__marker {
      background-color: #409eff !important;
      width: 0.152vw;
    }
  }
}

.content-column {
  padding-left: $primary-padding;
}

.content-section {
  padding-top: $content-padding-top;

 .card-title {
  font-size: 2.35vw;
  display: flex;
  justify-content: space-between; // 添加此行
  align-items: center;
  margin-bottom: 1vw;
  }
}

/* 课程体系设计 - 图谱相关 */
.course-map-container {
  display: flex;
  align-items: center;
  gap: 1vw;
  margin-top: 1vw;

  .course-map-screenshot {
    max-width: 60%;
    border-radius: $border-radius;
    box-shadow: $box-shadow;
  }

  .view-detail-btn {
    padding: 0.5vw 1vw;
    font-size: 1vw;
  }
}

/* 课程框架模块 - 动态颜色 */
.frame-module {
  width: fit-content;
  min-width: 70vw;
  padding: 1vw;
  margin-bottom: 1vw;
  border-radius: $border-radius;
  box-shadow: 0 0.104vw 0.312vw rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: all 0.3s ease;
  color: #333;

  .module-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5vw;

    span {
      font-size: 1.2vw;
      font-weight: 500;
    }
  }

  .module-content {
    font-size: 1vw;
    line-height: 1.6;
    color: #666;
  }
}

/* 知识模块 */
.knowledge-module-content {
  margin-top: 1vw;

  .teacher-text {
    font-size: 1vw;
    line-height: 1.6;
    color: #333;
    margin-bottom: 1vw;
  }

  .ai-image-container {
    max-width: 60%;

    .ai-generated-image {
      width: 100%;
      border-radius: $border-radius;
      box-shadow: $box-shadow;
    }
  }
}
</style>