<template>
  <div class="third-party-account-binding">
    <el-form>
      <el-form-item label="绑定登录账号">
        <div class="binding-list">
          <div v-for="(account, index) in thirdPartyAccounts" :key="index" class="binding-item">
            <img :src="account.icon" alt="icon" class="icon">
            <span>{{ account.name }}</span>
            <el-switch
              v-model="account.bound"
              :disabled="account.disabled"
              @change="toggleBinding(account)"
            ></el-switch>
          </div>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="addAccount">添加账号</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const thirdPartyAccounts = ref([
  { name: '微信', icon: 'path/to/wechat-icon.png', bound: false, disabled: false },
  { name: 'QQ', icon: 'path/to/qq-icon.png', bound: false, disabled: false },
]);

function toggleBinding(account) {
  // 调用后端API来绑定或解绑账号
  console.log(`Toggle binding for ${account.name}`);
}

function addAccount() {
  // 添加账号的逻辑
  console.log('Add new account');
}
</script>

<style scoped>
.third-party-account-binding {
  padding: 20px;
}

.binding-list {
  display: flex;
  flex-wrap: wrap;
}

.binding-item {
  display: flex;
  align-items: center;
  margin-right: 20px;
  margin-bottom: 10px;
}

.icon {
  width: 24px;
  height: 24px;
  margin-right: 10px;
}
</style>