/*src\router\teacherRoutes.js*/
import component from 'element-plus/es/components/tree-select/src/tree-select-option.mjs'

const teacherRoutes = [
  {
    path: '/teacher',
    component: () => import('@/views/DefaultLayout.vue'),
    children: [
      // 仪表盘
      {
        path: 'dashboard',
        name: 'TeacherDashboard',
        component: () => import('@/views/teacher/Dashboard.vue'),
        meta: { requiresAuth: true, role: '2', navTitle: '教师中心' }
      },

      // 教学课堂
      {
        path: 'teach-class',
        name: 'TeachClass',
        component: () => import('@/views/teacher/TeachClass/index.vue'),
        meta: { requiresAuth: true, role: '2' }
      },

      // 账号管理
      {
        path: 'teacher-account',
        name: 'TeacherAccount',
        component: () => import('@/views/teacher/AccountManagement/index.vue'),
        meta: { requiresAuth: true, role: '2' }
      },
      {
        path: 'tea-account',
        name: 'teacher-account-alias',
        component: () => import('@/views/teacher/CourseDetail/components/Overview/components/courseTeachTeam/TeachingTeam.vue'),
      },

      // 课程创建
      {
        path: 'create-course',
        name: 'CreateCourse',
        component: () => import('@/views/teacher/TeachClass/MyClass/components/CreateCourse.vue'),
        meta: { requiresAuth: true, role: '2' }
      },

      // 课程详情管理
      {

        path: 'course-detail-manager/:courseId/:courseName', //创建课程图谱需要courseName字段，不要删！！！！！！！！！
        // path: 'course-detail-manager/:courseId',
        name: 'CourseDetailManager',
        component: () => import('@/views/teacher/CourseDetailManager/index.vue'),
        redirect: { name: 'Announcement' },
        meta: { requiresAuth: true, role: '2', forceNavbarWhite: true },
        children: [
          // 公告管理
          {
            path: 'announcement',
            name: 'Announcement',
            component: () => import('@/views/teacher/CourseDetailManager/Announcement/index.vue'),
            meta: { showNoticeList: true, requiresAuth: true, role: '2', forceNavbarWhite: true },
            children: [
              {
                path: 'create-annoucement',
                name: 'CreateAnnoucement',
                component: () => import('@/views/teacher/CourseDetailManager/Announcement/CreateAnnoucement/index.vue'),
                meta: { showNoticeList: false, requiresAuth: true, role: '2' }
              },
              {
                path: 'edit/:noticeId',
                name: 'EditAnnoucement',
                component: () => import('@/views/teacher/CourseDetailManager/Announcement/CreateAnnoucement/index.vue'),
                meta: { showNoticeList: false, requiresAuth: true, role: '2' },
                props: true
              }
            ]
          },

          //作业考试路由
          {
            path: 'assignment-exam',
            name: 'AssignmentExamHome',
            component: () => import('@/views/teacher/CourseDetailManager/WorkAndExam/index.vue'),
            props: true,
            meta: { requiresAuth: true, role: '2' }
          },
          {
            path: 'assignment-exam/create',
            name: 'AssignmentExamCreate',
            component: () => import('@/views/teacher/CourseDetailManager/WorkAndExam/CreateTest/CreateWork.vue'),
            props: true,
            meta: { requiresAuth: true, role: '2' }
          },
          // 批改作业页面
          {
            path: 'assignment-exam/correction/exam',
            name: 'ExamCorrection',
            component: () => import('@/views/teacher/CourseDetailManager/WorkAndExam/Correction/ExamCorrection.vue'),
            props: route => ({ courseId: route.params.courseId, examId: route.query.id }),
            meta: { requiresAuth: true, role: '2' }
          },
          {
            path: 'assignment-exam/correction/assignment',
            name: 'AssignmentCorrection',
            component: () => import('@/views/teacher/CourseDetailManager/WorkAndExam/Correction/AssignmentCorrection.vue'),
            props: route => ({ courseId: route.params.courseId, assignmentId: route.query.id }),
            meta: { requiresAuth: true, role: '2' }
          },
          {
            path: 'assignment-exam/correction/detail',
            name: 'CorrectionDetail',
            component: () => import('@/views/teacher/CourseDetailManager/WorkAndExam/Correction/CorrectionDetail.vue'),
            props: route => ({ courseId: route.params.courseId, assignmentId: route.query.id }),
            meta: { requiresAuth: true, role: '2' }
          },

          // 学习资源
          {
            path: 'learning-resources',
            name: 'LearningResources',
            component: () => import('@/views/teacher/CourseDetailManager/LearningResources/index.vue'),
            meta: { showNoticeList: false, requiresAuth: true, role: '2' },
          },

          // 学习任务
          {
            path: 'learning-task',
            name: 'LearningTask',
            component: () => import('@/views/teacher/CourseDetailManager/LearningTask/index.vue'),
          },
          // 更多功能
          {
            path: 'more-features',
            name: 'MoreFeatures',
            component: () => import('@/views/teacher/CourseDetailManager/MoreFeatures/index.vue'),
          },

          // 学生管理
          {
            path: 'student-management',
            name: 'StudentManagement',
            component: () => import('@/views/teacher/CourseDetailManager/StudentManagement/index.vue'),
          },
          //图谱管理
          {
            path: 'graph-manager',
            name: 'GraphManager',
            component: () => import('@/views/teacher/CourseDetailManager/MoreFeatures/GraphManager/index.vue'),
          },
          //课程数据
          {
            path: 'course-statistics',
            name: 'CourseStatistics',
            component: () => import('@/views/teacher/CourseDetailManager/CourseStatistics/index.vue'),
          },
          //成绩管理
          {
            path: 'grade-management',
            name: 'GradeManagement',
            component: () => import('@/views/teacher/CourseDetailManager/GradeManagement/index.vue'),
          },
          {
            path: 'weight-ratio',
            name: 'WeightRatio',
            component: () => import('@/views/teacher/CourseDetailManager/GradeManagement/components/WeightRatio.vue'),
          },
          //签到管理
           {
            path: 'attendance-management',
            name: 'AttendanceManagement',
            component: () => import('@/views/teacher/CourseDetailManager/AttendanceManagement/index.vue'),
          },
           {
            path: 'Sign-in-detail/:id',
            name: 'SignInDetail',
            component: () => import('@/views/teacher/CourseDetailManager/AttendanceManagement/components/SignInDetail.vue'),
          },
          //课程管理
          {
            path: 'course-management',
            name: 'CourseManagement',
            component: () => import('@/views/teacher/CourseDetailManager/CourseManagement/index.vue'),
          },
          //问答讨论
          {
            path: 'Q-A',
            name: 'Q-A',
            component: () => import('@/views/teacher/CourseDetailManager/Q-A/index.vue'),
            props: route => ({ courseId: route.params.courseId })
          },

        ]
      },
      // 课程详情
      {
        path: 'course-detail/:courseId',
        name: 'CourseDetail',
        component: () => import('@/views/teacher/CourseDetail/index.vue'),
        meta: { showNoticeList: false, requiresAuth: true, role: '2' },
        props: true
      },

      // 题库
      {
        path: 'public-test-database',
        name: 'PublicTestDataBase',
        component: () => import('@/views/teacher/CourseDetailManager/WorkAndExam/TestDataBase/PublicTestDatabase.vue'),
        meta: { requiresAuth: true, role: '2' }
      },
      {
        path: 'course-test-database',
        name: 'CourseTestDataBase',
        component: () => import('@/views/teacher/CourseDetailManager/WorkAndExam/TestDataBase/CourseTestDataBase.vue'),
        meta: { requiresAuth: true, role: '2' }
      },
      {
        path: 'question-bank',
        name: 'QuestionBank',
        component: () => import('@/views/teacher/CourseDetailManager/QuestionBank/index.vue'),
        meta: { requiresAuth: true, role: '2' }
      },
      {
        path: 'new-test-data',
        name: 'NewTestData',
        component: () => import('@/views/teacher/CourseDetailManager/WorkAndExam/TestDataBase/NewTestData.vue'),
        meta: { requiresAuth: true, role: '2' }
      },

      // 数媒智能广场
      {
        path: 'intelligent2',
        name: 'intelligent2',
        component: () => import('@/views/teacher/intelligent/FunctionHome2.vue'),
        meta: { requiresAuth: true, requiresTeacher: true },
        children: [
          {
            path: '',
            name: 'photo',
            component: () => import('@/views/public/intelligent/Components/AIPhoto.vue')
          },
          {
            path: 'video',
            name: 'video',
            component: () => import('@/views/public/intelligent/Components/AIVideo.vue')
          },
          {
            path: 'dialog',
            name: 'dialog',
            component: () => import('@/views/public/intelligent/Components/AIDialog.vue')
          },
          {
            path: 'paper',
            name: 'paper',
            component: () => import('@/views/teacher/intelligent/Components/AIPaper.vue')
          },
          {
            path: 'plan',
            name: 'plan',
            component: () => import('@/views/teacher/intelligent/Components/AIPlan.vue')
          },
          {
            path: 'ppt',
            name: 'ppt',
            component: () => import('@/views/teacher/intelligent/Components/AIPPT.vue')
          }
        ]
      }
    ]
  },

  //公共题库
  {
    path: '/public-database',
    name: 'AssignmentExamPublicDatabase',
    component: () => import('@/views/teacher/CourseDetailManager/WorkAndExam/TestDataBase/PublicTestDatabase.vue'),
    meta: { requiresAuth: true, role: '2', forceNavbarWhite: true }
  },
  {
    path: '/course-database',
    name: 'AssignmentExamCourseDatabase',
    component: () => import('@/views/teacher/CourseDetailManager/WorkAndExam/TestDataBase/CourseTestDataBase.vue'),
    meta: { requiresAuth: true, role: '2', forceNavbarWhite: true }
  },
  {
    path: '/new-test',
    name: 'AssignmentExamNewTest',
    component: () => import('@/views/teacher/CourseDetailManager/WorkAndExam/TestDataBase/NewTestData.vue'),
    meta: { requiresAuth: true, role: '2' }
  },
  // 图谱页走空白布局，不带导航栏
  {
    path: '/graph',
    component: () => import('@/views/BlankLayout.vue'),
    children: [
      // 知识图谱
      {
        path: 'create-knowledge-graph/:courseId/:courseName/:graphId?',
        name: 'CreateKnowledgeGraph',
        component: () => import('@/views/teacher/CourseDetailManager/MoreFeatures/GraphManager/CreateGraph/KnowledgeGraph/CreateKnowledgeGraph.vue'),
      },
      // 能力图谱
      {
        path: 'create-ability-graph/:courseId/:courseName/:graphId?',
        name: 'CreateAbilityGraph',
        component: () => import('@/views/teacher/CourseDetailManager/MoreFeatures/GraphManager/CreateGraph/AbilityGraph/CreateAbilityGraph.vue'),
      },
      // 问题图谱
      {
        path: 'create-question-graph/:courseId/:courseName/:graphId?',
        name: 'CreateQuestionGraph',
        component: () => import('@/views/teacher/CourseDetailManager/MoreFeatures/GraphManager/CreateGraph/QuestionGraph/CreateQuestionGraph.vue'),
      },
      //关联资源
      {
        path: 'resource-association/:courseId/:nodeId',
        name: 'ResourceAssociation',
        component: () => import('@/views/teacher/CourseDetailManager/MoreFeatures/GraphManager/CreateGraph/KnowledgeGraph/ResourceAssociation.vue'),
        meta: { requiresAuth: true, role: '2' }
      },
       //问题图谱关联资源
       {
        path: "question-resource-association/:courseId/:nodeId",
        name: "QuestionResourceAssociation",
        component: () =>
          import(
            "@/views/teacher/CourseDetailManager/MoreFeatures/GraphManager/CreateGraph/QuestionGraph/ResourceAssociation.vue"
          ),
        meta: { requiresAuth: true, role: "2" },
      },
    ]
  },
  //学习路径
  {
    path: '/learning-path/:courseId/:courseName',
    name: 'LearningPath',
    component: () => import('@/views/teacher/CourseDetailManager/MoreFeatures/LearningPath/index.vue'),
  },
  {
    path: '/teacher/Q-A/:courseId/:topicId',
    name: 'DiscussDetail',
    component: () => import('@/views/teacher/CourseDetailManager/Q-A/DiscussDetail.vue'),
    props: true
  }
]

export default teacherRoutes