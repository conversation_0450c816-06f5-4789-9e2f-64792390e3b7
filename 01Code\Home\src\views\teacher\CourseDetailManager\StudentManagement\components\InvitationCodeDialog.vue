<template>
  <!-- 用自定义 ConfirmDialog 作为外层容器 -->
  <div class="confirm-dialog-overlay" v-if="visible" @click.self="handleClose">
    <div class="confirm-dialog">
      <!-- 标题栏：新增右上角关闭按钮 -->
      <div class="confirm-dialog-header">
        <h3>邀请学生</h3>
        <button class="close-btn" @click="handleClose">×</button> <!-- 右上角关闭按钮 -->
      </div>

      <!-- 内容区：邀请码信息 -->
      <div class="confirm-dialog-body">
        <div class="invitation-info">
          <div class="info-item">
            <span class="label">课程名称：</span>
            <span class="value">{{ courseName }}</span>
          </div>
          <div class="info-item">
            <span class="label">课程邀请码：</span>
            <span class="value">{{ courseInvitationCode }}</span>
            <button class="copy-btn" @click="copyToClipboard(courseInvitationCode, '课程邀请码')">复制</button>
          </div>
          <div class="info-item">
            <span class="label">班级名称：</span>
            <span class="value">{{ className }}</span>
          </div>
          <div class="info-item">
            <span class="label">班级邀请码：</span>
            <span class="value">{{ classInvitationCode }}</span>
            <button class="copy-btn" @click="copyToClipboard(classInvitationCode, '班级邀请码')">复制</button>
          </div>
        </div>

        <div class="invitation-instruction">
          <div class="alert-info">
            <p>请提醒学生：必须先使用<strong>课程邀请码</strong>加入课程，然后才能使用<strong>班级邀请码</strong>加入班级。</p>
          </div>
          <div class="copy-all">
            <button class="primary-btn" @click="copyInvitationInstruction">一键复制邀请指令</button>
          </div>
        </div>
      </div>

      <!-- 底部按钮：移除原关闭按钮（如需保留可不移除） -->
      <div class="confirm-dialog-footer">
        <!-- 可选：如果仍需要底部关闭按钮可保留，否则删除 -->
        <!-- <button class="cancel-btn" @click="handleClose">关闭</button> -->
      </div>
    </div>
  </div>

  <!-- 复制成功/失败的自动提示 -->
  <CustomNotification
    v-if="showNotification"
    :message="notificationMessage"
    :type="notificationType"
    :duration="3000"
    :key="notificationKey"
  />
</template>

<script>
import CustomNotification from '@/components/CustomNotification.vue';
import { ref } from 'vue'; // 需引入ref

export default {
  components: {
    CustomNotification
  },
  props: {
    visible: Boolean,
    courseName: String,
    courseInvitationCode: String,
    className: String,
    classInvitationCode: String
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    // 通知相关状态：新增key解决多次显示问题
    const showNotification = ref(false);
    const notificationMessage = ref('');
    const notificationType = ref('success');
    const notificationKey = ref(0); // 用于强制刷新提示组件

    // 单个邀请码复制
    const copyToClipboard = (text, type) => {
      navigator.clipboard.writeText(text).then(() => {
        notificationKey.value++; // 每次显示时更新key
        showNotification.value = true;
        notificationType.value = 'success';
        notificationMessage.value = `${type}已复制到剪贴板`;
        // 3秒后手动隐藏（解决提示不消失问题）
        setTimeout(() => {
          showNotification.value = false;
        }, 3000);
      }).catch(() => {
        notificationKey.value++;
        showNotification.value = true;
        notificationType.value = 'error';
        notificationMessage.value = `复制${type}失败，请手动复制`;
        setTimeout(() => {
          showNotification.value = false;
        }, 3000);
      });
    };

    // 一键复制邀请指令
    const copyInvitationInstruction = () => {
      const instruction = `请使用以下邀请码加入课程和班级：
课程名称：${props.courseName}
课程邀请码：${props.courseInvitationCode}
班级名称：${props.className}
班级邀请码：${props.classInvitationCode}

加入步骤：
1. 使用课程邀请码加入课程
2. 加入课程后，再使用班级邀请码加入班级`;
      
      navigator.clipboard.writeText(instruction).then(() => {
        notificationKey.value++;
        showNotification.value = true;
        notificationType.value = 'success';
        notificationMessage.value = '邀请指令已全部复制到剪贴板';
        setTimeout(() => {
          showNotification.value = false;
        }, 3000);
      }).catch(() => {
        notificationKey.value++;
        showNotification.value = true;
        notificationType.value = 'error';
        notificationMessage.value = '邀请指令复制失败，请手动复制';
        setTimeout(() => {
          showNotification.value = false;
        }, 3000);
      });
    };

    // 关闭弹窗
    const handleClose = () => {
      emit('update:visible', false);
    };

    return {
      copyToClipboard,
      copyInvitationInstruction,
      handleClose,
      showNotification,
      notificationMessage,
      notificationType,
      notificationKey // 导出key
    };
  }
};
</script>

<style scoped lang="scss">
/* 复用ConfirmDialog的基础样式 */
.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.confirm-dialog {
  background-color: #fff;
  border-radius: 8px;
  width: 600px;
  max-width: 90%;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 标题栏：新增关闭按钮样式 */
.confirm-dialog-header {
  padding: 16px 24px;
  border-bottom: 1px solid #eee;
  display: flex; /* 开启flex布局 */
  justify-content: space-between; /* 标题左对齐，关闭按钮右对齐 */
  align-items: center; /* 垂直居中 */
  
  h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 20px;
    color: #999;
    cursor: pointer;
    padding: 0 5px;
    line-height: 1; /* 消除按钮默认行高影响 */
    
    &:hover {
      color: #333; /* hover时变深 */
    }
  }
}

/* 其他样式保持不变 */
.confirm-dialog-body {
  padding: 24px;
  max-height: 500px;
  overflow-y: auto;
}

.confirm-dialog-footer {
  padding: 12px 24px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #eee;
}

/* 邀请码内容样式 */
.invitation-info {
  margin-bottom: 20px;
}

.info-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.label {
  font-weight: bold;
  min-width: 100px;
  color: #333;
}

.value {
  flex: 1;
  word-break: break-all;
  color: #666;
}

.copy-btn {
  padding: 4px 12px;
  background-color: #f5f5f5;
  color: #666;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  
  &:hover {
    background-color: #eaeaea;
  }
}

.primary-btn {
  padding: 8px 16px;
  background-color: #1890ff;
  color: #fff;
  border: 1px solid #1890ff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  
  &:hover {
    background-color: #40a9ff;
  }
}

.cancel-btn {
  padding: 8px 16px;
  background-color: #f5f5f5;
  color: #666;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  
  &:hover {
    background-color: #eaeaea;
  }
}

.alert-info {
  padding: 10px 15px;
  background-color: #f0f7ff;
  border-left: 4px solid #1890ff;
  margin-bottom: 15px;
  color: #1890ff;
  font-size: 14px;
}

.copy-all {
  text-align: center;
  margin-top: 20px;
}
</style>