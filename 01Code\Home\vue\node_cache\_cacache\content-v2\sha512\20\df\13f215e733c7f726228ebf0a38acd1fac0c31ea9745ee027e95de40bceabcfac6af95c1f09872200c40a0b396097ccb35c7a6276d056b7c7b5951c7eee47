{"_attachments": {}, "_id": "fdir", "_rev": "301484-61f1d06d61011c8ed86b1f5d", "author": {"name": "thecodrr", "email": "<EMAIL>"}, "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "dist-tags": {"latest": "6.4.6"}, "license": "MIT", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "name": "fdir", "readme": "<p align=\"center\">\n<img src=\"https://github.com/thecodrr/fdir/raw/master/assets/fdir.gif\" width=\"75%\"/>\n\n<h1 align=\"center\">The Fastest Directory Crawler & Globber for NodeJS</h1>\n<p align=\"center\">\n  <a href=\"https://www.npmjs.com/package/fdir\"><img src=\"https://img.shields.io/npm/v/fdir?style=for-the-badge\"/></a>\n  <a href=\"https://www.npmjs.com/package/fdir\"><img src=\"https://img.shields.io/npm/dw/fdir?style=for-the-badge\"/></a>\n  <a href=\"https://codeclimate.com/github/thecodrr/fdir/maintainability\"><img src=\"https://img.shields.io/codeclimate/maintainability-percentage/thecodrr/fdir?style=for-the-badge\"/></a>\n  <a href=\"https://coveralls.io/github/thecodrr/fdir?branch=master\"><img src=\"https://img.shields.io/coveralls/github/thecodrr/fdir?style=for-the-badge\"/></a>\n  <a href=\"https://www.npmjs.com/package/fdir\"><img src=\"https://img.shields.io/bundlephobia/minzip/fdir?style=for-the-badge\"/></a>\n  <a href=\"https://www.producthunt.com/posts/fdir-every-millisecond-matters\"><img src=\"https://img.shields.io/badge/ProductHunt-Upvote-red?style=for-the-badge&logo=product-hunt\"/></a>\n  <a href=\"https://dev.to/thecodrr/how-i-wrote-the-fastest-directory-crawler-ever-3p9c\"><img src=\"https://img.shields.io/badge/dev.to-Read%20Blog-black?style=for-the-badge&logo=dev.to\"/></a>\n  <a href=\"./LICENSE\"><img src=\"https://img.shields.io/github/license/thecodrr/fdir?style=for-the-badge\"/></a>\n</p>\n</p>\n\n⚡ **The Fastest:** Nothing similar (in the NodeJS world) beats `fdir` in speed. It can easily crawl a directory containing **1 million files in < 1 second.**\n\n💡 **Stupidly Easy:** `fdir` uses expressive Builder pattern to build the crawler increasing code readability.\n\n🤖 **Zero Dependencies\\*:** `fdir` only uses NodeJS `fs` & `path` modules.\n\n🕺 **Astonishingly Small:** < 2KB in size gzipped & minified.\n\n🖮 **Hackable:** Extending `fdir` is extremely simple now that the new Builder API is here. Feel free to experiment around.\n\n_\\* `picomatch` must be installed manually by the user to support globbing._\n\n## 🚄 Quickstart\n\n### Installation\n\nYou can install using `npm`:\n\n```sh\n$ npm i fdir\n```\n\nor Yarn:\n\n```sh\n$ yarn add fdir\n```\n\n### Usage\n\n```ts\nimport { fdir } from \"fdir\";\n\n// create the builder\nconst api = new fdir().withFullPaths().crawl(\"path/to/dir\");\n\n// get all files in a directory synchronously\nconst files = api.sync();\n\n// or asynchronously\napi.withPromise().then((files) => {\n  // do something with the result here.\n});\n```\n\n## Documentation:\n\nDocumentation for all methods is available [here](/documentation.md).\n\n## 📊 Benchmarks:\n\nPlease check the benchmark against the latest version [here](/BENCHMARKS.md).\n\n## 🙏Used by:\n\n`fdir` is downloaded over 200k+ times a week by projects around the world. Here's a list of some notable projects using `fdir` in production:\n\n> Note: if you think your project should be here, feel free to open an issue. Notable is anything with a considerable amount of GitHub stars.\n\n1. [rollup/plugins](https://github.com/rollup/plugins)\n2. [SuperchupuDev/tinyglobby](https://github.com/SuperchupuDev/tinyglobby)\n3. [pulumi/pulumi](https://github.com/pulumi/pulumi)\n4. [dotenvx/dotenvx](https://github.com/dotenvx/dotenvx)\n5. [mdn/yari](https://github.com/mdn/yari)\n6. [streetwriters/notesnook](https://github.com/streetwriters/notesnook)\n7. [imba/imba](https://github.com/imba/imba)\n8. [moroshko/react-scanner](https://github.com/moroshko/react-scanner)\n9. [netlify/build](https://github.com/netlify/build)\n10. [yassinedoghri/astro-i18next](https://github.com/yassinedoghri/astro-i18next)\n11. [selfrefactor/rambda](https://github.com/selfrefactor/rambda)\n12. [whyboris/Video-Hub-App](https://github.com/whyboris/Video-Hub-App)\n\n## 🦮 LICENSE\n\nCopyright &copy; 2024 Abdullah Atta under MIT. [Read full text here.](https://github.com/thecodrr/fdir/raw/master/LICENSE)\n", "time": {"created": "2022-01-26T22:51:25.295Z", "modified": "2025-06-10T07:21:39.591Z", "5.1.0": "2021-05-21T13:03:57.727Z", "5.0.0": "2021-01-12T17:23:39.278Z", "4.1.0": "2020-08-04T15:24:03.809Z", "4.0.0": "2020-07-27T18:26:11.618Z", "3.4.3": "2020-06-02T23:07:47.019Z", "3.4.2": "2020-05-18T00:39:20.041Z", "3.4.1": "2020-05-17T09:33:32.624Z", "3.4.0": "2020-05-15T17:35:09.369Z", "3.3.0": "2020-05-13T09:19:45.050Z", "3.2.1": "2020-05-12T18:16:57.094Z", "3.2.0": "2020-05-12T15:56:29.990Z", "3.1.1": "2020-05-11T00:20:27.307Z", "3.1.0": "2020-05-11T00:09:41.544Z", "3.0.0": "2020-05-10T18:42:45.986Z", "2.1.1": "2020-04-27T07:29:41.173Z", "2.1.0": "2020-03-21T18:52:56.998Z", "2.0.3": "2020-03-18T18:34:02.161Z", "2.0.2": "2020-03-18T18:33:29.730Z", "2.0.1": "2020-03-16T08:16:15.281Z", "2.0.0": "2020-03-16T08:15:37.209Z", "1.2.0": "2020-03-15T16:31:28.798Z", "1.1.1": "2020-03-14T17:03:15.410Z", "1.1.0": "2020-03-14T17:01:35.435Z", "1.0.3": "2020-03-14T10:30:32.415Z", "1.0.2": "2020-03-13T21:09:08.046Z", "1.0.1": "2020-03-13T21:04:07.491Z", "1.0.0": "2020-03-13T20:58:27.499Z", "5.2.0": "2022-01-18T06:13:39.331Z", "5.2.1": "2022-10-17T18:37:41.998Z", "5.3.0": "2022-10-18T17:39:22.736Z", "6.0.0": "2023-02-08T18:05:42.434Z", "6.0.1": "2023-02-09T07:45:47.214Z", "6.0.2": "2023-07-31T21:17:35.824Z", "6.1.0": "2023-08-13T18:29:08.556Z", "6.1.1": "2023-11-05T17:26:07.154Z", "6.2.0": "2024-07-22T04:22:13.742Z", "6.3.0": "2024-08-25T13:54:08.680Z", "6.4.0": "2024-09-30T07:25:11.390Z", "6.4.1": "2024-10-16T18:14:23.917Z", "6.4.2": "2024-10-16T19:34:48.255Z", "6.4.3": "2025-01-17T05:54:05.708Z", "6.4.4": "2025-04-19T04:41:43.916Z", "6.4.5": "2025-05-28T05:07:10.182Z", "6.4.6": "2025-06-10T07:20:54.902Z"}, "versions": {"5.1.0": {"name": "fdir", "version": "5.1.0", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "main": "index.js", "scripts": {"test": "jest __tests__/fdir.test.js", "test:coverage": "jest __tests__/fdir.test.js --coverage", "bench": "node benchmarks/benchmark.js", "bench:glob": "node benchmarks/glob-benchmark.js", "bench:fdir": "node benchmarks/fdir-benchmark.js", "release": "./scripts/release.sh"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "types": "index.d.ts", "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fast-glob": "^3.2.2", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^2.0.0", "glob": "^7.1.6", "klaw-sync": "^6.0.0", "mock-fs": "^4.11.0", "picomatch": "^2.2.2", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.0.0", "recursive-readdir": "^2.2.2", "rrdir": "^6.1.2", "systeminformation": "^5.3.1", "tiny-glob": "^0.2.9", "walk-sync": "^2.0.2"}, "gitHead": "3b1b7076322353be169f00fe38a8bb1a8c49afec", "_id": "fdir@5.1.0", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"shasum": "973e4934e6a3666b59ebdfc56f60bb8e9b16acb8", "size": 9413, "noattachment": false, "tarball": "https://registry.npmmirror.com/fdir/-/fdir-5.1.0.tgz", "integrity": "sha512-IgTtZwL52tx2wqWeuGDzXYTnNsEjNLahZpJw30hCQDyVnoHXwY5acNDnjGImTTL1R0z1PCyLw20VAbE5qLic3Q=="}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_5.1.0_1621602237540_0.3325064570452141"}, "_hasShrinkwrap": false, "publish_time": 1621602237727, "_cnpm_publish_time": 1621602237727, "_cnpmcore_publish_time": "2021-12-16T23:50:36.782Z"}, "5.0.0": {"name": "fdir", "version": "5.0.0", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "main": "index.js", "scripts": {"test": "jest __tests__/fdir.test.js", "test:coverage": "jest __tests__/fdir.test.js --coverage", "bench": "node benchmarks/benchmark.js", "bench:glob": "node benchmarks/glob-benchmark.js", "bench:fdir": "node benchmarks/fdir-benchmark.js", "release": "./scripts/release.sh"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "types": "index.d.ts", "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fast-glob": "^3.2.2", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^2.0.0", "glob": "^7.1.6", "klaw-sync": "^6.0.0", "mock-fs": "^4.11.0", "picomatch": "^2.2.2", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.0.0", "recursive-readdir": "^2.2.2", "rrdir": "^6.1.2", "systeminformation": "^4.26.1", "walk-sync": "^2.0.2"}, "gitHead": "05b69c49e56f5eecaa4dce43edc290e09413a801", "_id": "fdir@5.0.0", "_nodeVersion": "12.18.4", "_npmVersion": "6.14.6", "dist": {"shasum": "a40b5d9adfb530daeca55558e8ad87ec14a44769", "size": 8239, "noattachment": false, "tarball": "https://registry.npmmirror.com/fdir/-/fdir-5.0.0.tgz", "integrity": "sha512-cteqwWMA43lEmgwOg5HSdvhVFD39vHjQDhZkRMlKmeoNPtSSgUw1nUypydiY2upMdGiBFBZvNBDbnoBh0yCzaQ=="}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_5.0.0_1610472219143_0.038973662184640157"}, "_hasShrinkwrap": false, "publish_time": 1610472219278, "_cnpm_publish_time": 1610472219278, "_cnpmcore_publish_time": "2021-12-16T23:50:37.001Z"}, "4.1.0": {"name": "fdir", "version": "4.1.0", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "main": "index.js", "scripts": {"test": "jest __tests__/fdir.test.js", "test:coverage": "jest __tests__/fdir.test.js --coverage", "bench": "node benchmarks/benchmark.js", "bench:glob": "node benchmarks/glob-benchmark.js", "bench:fdir": "node benchmarks/fdir-benchmark.js"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "types": "index.d.ts", "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fast-glob": "^3.2.2", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^2.0.0", "glob": "^7.1.6", "klaw-sync": "^6.0.0", "mock-fs": "^4.11.0", "picomatch": "^2.2.2", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.0.0", "recursive-readdir": "^2.2.2", "rrdir": "^6.1.2", "systeminformation": "^4.26.1", "walk-sync": "^2.0.2"}, "gitHead": "6d0adbbde36a8e0f788b7a2ca887a6febefe393e", "_id": "fdir@4.1.0", "_nodeVersion": "13.13.0", "_npmVersion": "6.14.5", "dist": {"shasum": "f739ec79f61f69779a6430a622e5f54c57caf921", "size": 7541, "noattachment": false, "tarball": "https://registry.npmmirror.com/fdir/-/fdir-4.1.0.tgz", "integrity": "sha512-o<PERSON><PERSON>hnPg4nUIkd6w22iGbFD7c7UvVnXB3a7/GHcPSsXDUGm6Jxp12bGI5O0gr0YuhDh5l/vDExdHOnrW/j9EqQ=="}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_4.1.0_1596554643702_0.8993769255830975"}, "_hasShrinkwrap": false, "publish_time": 1596554643809, "_cnpm_publish_time": 1596554643809, "_cnpmcore_publish_time": "2021-12-16T23:50:37.201Z"}, "4.0.0": {"name": "fdir", "version": "4.0.0", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "main": "index.js", "scripts": {"test": "jest __tests__/fdir.test.js", "test:coverage": "jest __tests__/fdir.test.js --coverage", "bench": "node benchmarks/benchmark.js", "bench:glob": "node benchmarks/glob-benchmark.js", "bench:fdir": "node benchmarks/fdir-benchmark.js"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "types": "index.d.ts", "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fast-glob": "^3.2.2", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^2.0.0", "glob": "^7.1.6", "klaw-sync": "^6.0.0", "mock-fs": "^4.11.0", "picomatch": "^2.2.2", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.0.0", "recursive-readdir": "^2.2.2", "rrdir": "^6.1.2", "systeminformation": "^4.26.1", "walk-sync": "^2.0.2"}, "gitHead": "293319ed0a1832bf337543e4e4a7f45d2dfb3e34", "_id": "fdir@4.0.0", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"shasum": "d4a3d7b40fc10cbc3ecd3d1af5674eaee5cb46b8", "size": 7417, "noattachment": false, "tarball": "https://registry.npmmirror.com/fdir/-/fdir-4.0.0.tgz", "integrity": "sha512-bCdcdBowDx2seQyTn9C/HnisDPrqMr503/fNF9d8MQCRiW1u5qGrCLphB6PCMpRVd64NHGEQSnKk2CWOMCOpfg=="}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_4.0.0_1595874371394_0.68526622034767"}, "_hasShrinkwrap": false, "publish_time": 1595874371618, "_cnpm_publish_time": 1595874371618, "_cnpmcore_publish_time": "2021-12-16T23:50:37.545Z"}, "3.4.3": {"name": "fdir", "version": "3.4.3", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "main": "index.js", "scripts": {"test": "jest __tests__/fdir.test.js", "test:coverage": "jest __tests__/fdir.test.js --coverage", "bench": "node benchmarks/benchmark.js", "bench:glob": "node benchmarks/glob-benchmark.js", "bench:fdir": "node benchmarks/fdir-benchmark.js"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "types": "index.d.ts", "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fast-glob": "^3.2.2", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^2.0.0", "glob": "^7.1.6", "klaw-sync": "^6.0.0", "mock-fs": "^4.11.0", "picomatch": "^2.2.2", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.0.0", "recursive-readdir": "^2.2.2", "rrdir": "^6.1.2", "systeminformation": "^4.26.1", "walk-sync": "^2.0.2"}, "gitHead": "5435c5026574e600fbb01084062dbdda2f2ea165", "_id": "fdir@3.4.3", "_nodeVersion": "13.13.0", "_npmVersion": "6.14.5", "dist": {"shasum": "9f9470d8b06af967ddd597a381c140872d7c8b74", "size": 7416, "noattachment": false, "tarball": "https://registry.npmmirror.com/fdir/-/fdir-3.4.3.tgz", "integrity": "sha512-JFNfD4wNQLQ/8ht/iH4dLdYYZaxNuCsld619o+eKHORVyjzcog3YU1X8kpk8J525I8EMnqBW80SceTFdLNtklA=="}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_3.4.3_1591139266836_0.057827494166922344"}, "_hasShrinkwrap": false, "publish_time": 1591139267019, "_cnpm_publish_time": 1591139267019, "_cnpmcore_publish_time": "2021-12-16T23:50:38.351Z"}, "3.4.2": {"name": "fdir", "version": "3.4.2", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "main": "index.js", "scripts": {"test": "jest __tests__/fdir.test.js", "test:coverage": "jest __tests__/fdir.test.js --coverage", "bench": "node benchmarks/benchmark.js", "bench:glob": "node benchmarks/glob-benchmark.js", "bench:fdir": "node benchmarks/fdir-benchmark.js"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "types": "index.d.ts", "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fast-glob": "^3.2.2", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^2.0.0", "glob": "^7.1.6", "klaw-sync": "^6.0.0", "mock-fs": "^4.11.0", "picomatch": "^2.2.2", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.0.0", "recursive-readdir": "^2.2.2", "rrdir": "^6.1.2", "walk-sync": "^2.0.2"}, "gitHead": "d6afde783bdd28ab90fc3568eef2980c1aa5f74c", "_id": "fdir@3.4.2", "_nodeVersion": "13.13.0", "_npmVersion": "6.14.5", "dist": {"shasum": "978bc4e8cf9884b03b1429d50feb0cc9fd4824ce", "size": 7388, "noattachment": false, "tarball": "https://registry.npmmirror.com/fdir/-/fdir-3.4.2.tgz", "integrity": "sha512-Bya64kBW9us4CmX4T7P3JV+TBY0cXB3JhmyYBQ6CXEePat5FD72f7eaKt75ljSP2R8EdwD4txEestENsH/D0Jg=="}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_3.4.2_1589762359895_0.6151842423977918"}, "_hasShrinkwrap": false, "publish_time": 1589762360041, "_cnpm_publish_time": 1589762360041, "_cnpmcore_publish_time": "2021-12-16T23:50:38.534Z"}, "3.4.1": {"name": "fdir", "version": "3.4.1", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "main": "index.js", "scripts": {"test": "jest __tests__/fdir.test.js", "test:coverage": "jest __tests__/fdir.test.js --coverage", "bench": "node benchmarks/benchmark.js", "bench:glob": "node benchmarks/glob-benchmark.js", "bench:fdir": "node benchmarks/fdir-benchmark.js"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "types": "index.d.ts", "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fast-glob": "^3.2.2", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^2.0.0", "glob": "^7.1.6", "klaw-sync": "^6.0.0", "mock-fs": "^4.11.0", "picomatch": "^2.2.2", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.0.0", "recursive-readdir": "^2.2.2", "rrdir": "^6.1.2", "walk-sync": "^2.0.2"}, "gitHead": "36b51ba05fd54c3eb7a661674eef29842c2342c0", "_id": "fdir@3.4.1", "_nodeVersion": "13.13.0", "_npmVersion": "6.14.5", "dist": {"shasum": "faf45fafe637eae9c688e50ce2cf28bd27a5eaad", "size": 7385, "noattachment": false, "tarball": "https://registry.npmmirror.com/fdir/-/fdir-3.4.1.tgz", "integrity": "sha512-urbsiP7l58+B0JPxGv1UlYtOsMyr+DNxHc7iY29bs7TN0VWK/8p4knqbjWaeW6f/uA//2mD5BiVED9YTdEgedw=="}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_3.4.1_1589708012501_0.8111172723985212"}, "_hasShrinkwrap": false, "publish_time": 1589708012624, "_cnpm_publish_time": 1589708012624, "_cnpmcore_publish_time": "2021-12-16T23:50:38.736Z"}, "3.4.0": {"name": "fdir", "version": "3.4.0", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "main": "index.js", "scripts": {"test": "jest __tests__/fdir.test.js", "test:coverage": "jest __tests__/fdir.test.js --coverage", "bench": "node benchmarks/benchmark.js", "bench:glob": "node benchmarks/glob-benchmark.js", "bench:fdir": "node benchmarks/fdir-benchmark.js"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "types": "index.d.ts", "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fast-glob": "^3.2.2", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^2.0.0", "glob": "^7.1.6", "klaw-sync": "^6.0.0", "mock-fs": "^4.11.0", "picomatch": "^2.2.2", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.0.0", "recursive-readdir": "^2.2.2", "rrdir": "^6.1.2", "walk-sync": "^2.0.2"}, "gitHead": "6c20d4b9eab9437cc7e6d50ee243448d2746c376", "_id": "fdir@3.4.0", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"shasum": "8e8596a7f8c7634c86088580fca55f73b696bbff", "size": 7382, "noattachment": false, "tarball": "https://registry.npmmirror.com/fdir/-/fdir-3.4.0.tgz", "integrity": "sha512-3HuDVf8DkK277RlmsiDJFZ+pWqB1HzPWNBhoaUk4vPKwaMHI8urdA/S5UPzI8veQ/qLJYrMKC0poqQCJ/sHOyg=="}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_3.4.0_1589564109256_0.14450182034141967"}, "_hasShrinkwrap": false, "publish_time": 1589564109369, "_cnpm_publish_time": 1589564109369, "_cnpmcore_publish_time": "2021-12-16T23:50:39.023Z"}, "3.3.0": {"name": "fdir", "version": "3.3.0", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "main": "index.js", "scripts": {"test": "jest __tests__/fdir.test.js", "test:coverage": "jest __tests__/fdir.test.js --coverage", "bench": "node benchmarks/benchmark.js", "bench:glob": "node benchmarks/glob-benchmark.js"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "types": "index.d.ts", "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fast-glob": "^3.2.2", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^2.0.0", "glob": "^7.1.6", "klaw-sync": "^6.0.0", "mock-fs": "^4.11.0", "picomatch": "^2.2.2", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.0.0", "recursive-readdir": "^2.2.2", "rrdir": "^6.1.2", "walk-sync": "^2.0.2"}, "gitHead": "e5364d9fb12935ecc788e63a0adf7da14cb5d0be", "_id": "fdir@3.3.0", "_nodeVersion": "13.13.0", "_npmVersion": "6.14.5", "dist": {"shasum": "12e40d364d8a0e23fa7de7bffee45c8718ebca94", "size": 7201, "noattachment": false, "tarball": "https://registry.npmmirror.com/fdir/-/fdir-3.3.0.tgz", "integrity": "sha512-JEQLavRfrkufmxVTMqEIN4vvkr7tPjbTA5sAZMWZfZxhdCBdQSPbWFg8MOO74TSQXban37Ub/7vKl4z99V90Mw=="}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_3.3.0_1589361584952_0.9290802931157944"}, "_hasShrinkwrap": false, "publish_time": 1589361585050, "_cnpm_publish_time": 1589361585050, "_cnpmcore_publish_time": "2021-12-16T23:50:39.251Z"}, "3.2.1": {"name": "fdir", "version": "3.2.1", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "main": "index.js", "scripts": {"test": "jest __tests__/fdir.test.js", "test:coverage": "jest __tests__/fdir.test.js --coverage", "benchmark": "node benchmark.js"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "types": "index.d.ts", "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^1.0.7", "klaw-sync": "^6.0.0", "mock-fs": "^4.11.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.0.0", "recursive-readdir": "^2.2.2", "rrdir": "^6.1.2", "walk-sync": "^2.0.2", "picomatch": "^2.2.2"}, "gitHead": "0b25d781047054d2881e75299314587542689eaf", "_id": "fdir@3.2.1", "_nodeVersion": "13.13.0", "_npmVersion": "6.14.5", "dist": {"shasum": "cf9f08bc58deab8efe9b273ec526c2fa777e7118", "size": 7108, "noattachment": false, "tarball": "https://registry.npmmirror.com/fdir/-/fdir-3.2.1.tgz", "integrity": "sha512-5EdvXijomCF1mFaRxAKzdXMJ0aSs97jJG0Wib4bUmkL1xPve6fT8Ja7dGy3v/ynci6pgASV58wFoWgeJoJ623w=="}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_3.2.1_1589307416960_0.47067713288742175"}, "_hasShrinkwrap": false, "publish_time": 1589307417094, "_cnpm_publish_time": 1589307417094, "_cnpmcore_publish_time": "2021-12-16T23:50:39.460Z"}, "3.2.0": {"name": "fdir", "version": "3.2.0", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "main": "index.js", "scripts": {"test": "jest __tests__/fdir.test.js", "test:coverage": "jest __tests__/fdir.test.js --coverage", "benchmark": "node benchmark.js"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "types": "index.d.ts", "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^1.0.7", "klaw-sync": "^6.0.0", "mock-fs": "^4.11.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.0.0", "recursive-readdir": "^2.2.2", "rrdir": "^6.1.2", "walk-sync": "^2.0.2", "picomatch": "^2.2.2"}, "gitHead": "ecb1444a1502a41327f002149c47e7011bccbf1d", "_id": "fdir@3.2.0", "_nodeVersion": "13.13.0", "_npmVersion": "6.14.5", "dist": {"shasum": "75706505450dadf3575ae22e67667a551cee5c8b", "size": 7094, "noattachment": false, "tarball": "https://registry.npmmirror.com/fdir/-/fdir-3.2.0.tgz", "integrity": "sha512-bRbMJtLS3CpqhBR2piQgsz2rmNWxi2L/vYM7Zg5m8yHyQ/mA0bm4l3c4hjkdhz7ttRooQ5rZzobc9k3mH9tekw=="}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_3.2.0_1589298989806_0.1788472725109358"}, "_hasShrinkwrap": false, "publish_time": 1589298989990, "_cnpm_publish_time": 1589298989990, "_cnpmcore_publish_time": "2021-12-16T23:50:39.671Z"}, "3.1.1": {"name": "fdir", "version": "3.1.1", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "main": "index.js", "scripts": {"test": "jest __tests__/fdir.test.js", "test:coverage": "jest __tests__/fdir.test.js --coverage", "benchmark": "node benchmark.js"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "types": "index.d.ts", "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^1.0.7", "klaw-sync": "^6.0.0", "mock-fs": "^4.11.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.0.0", "recursive-readdir": "^2.2.2", "rrdir": "^6.1.2", "walk-sync": "^2.0.2", "picomatch": "^2.2.2"}, "gitHead": "7a553039abd2873246d48ceddbf0122ddc37040b", "_id": "fdir@3.1.1", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"shasum": "72d2d1ca945fca5239e8fd9ba1e6d35d173f6441", "size": 7019, "noattachment": false, "tarball": "https://registry.npmmirror.com/fdir/-/fdir-3.1.1.tgz", "integrity": "sha512-+e68Ju11VJXE9qSz3DWMsNTo+vc313hiJfk0tKffZX4vT3mkDsxNiITFVJL1NdPbA8vEkMTeBAhsiMGhuL3tLg=="}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_3.1.1_1589156426975_0.12149584649819745"}, "_hasShrinkwrap": false, "publish_time": 1589156427307, "_cnpm_publish_time": 1589156427307, "_cnpmcore_publish_time": "2021-12-16T23:50:39.895Z"}, "3.1.0": {"name": "fdir", "version": "3.1.0", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "main": "index.js", "scripts": {"test": "jest __tests__/fdir.test.js", "test:coverage": "jest __tests__/fdir.test.js --coverage", "benchmark": "node benchmark.js"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "types": "index.d.ts", "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^1.0.7", "klaw-sync": "^6.0.0", "mock-fs": "^4.11.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.0.0", "recursive-readdir": "^2.2.2", "rrdir": "^6.1.2", "walk-sync": "^2.0.2", "picomatch": "^2.2.2"}, "gitHead": "c35d5e1537bf326528fde88a5e9858ceab3a72b8", "_id": "fdir@3.1.0", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"shasum": "bc0a72bfb0a0fbfcdc3d7a1ee3bcf818b31f9284", "size": 7116, "noattachment": false, "tarball": "https://registry.npmmirror.com/fdir/-/fdir-3.1.0.tgz", "integrity": "sha512-AYu+14CgEgeosjfi9/A6+hVr/Yui+4DwQLgwTBlfbHjydxCcg70Cbjv7SxJZP+zpGuQWyKvFon44Cr9pT7UIhw=="}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_3.1.0_1589155781397_0.3715703895400382"}, "_hasShrinkwrap": false, "publish_time": 1589155781544, "_cnpm_publish_time": 1589155781544, "_cnpmcore_publish_time": "2021-12-16T23:50:40.122Z"}, "3.0.0": {"name": "fdir", "version": "3.0.0", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "main": "index.js", "scripts": {"test": "jest __tests__/fdir.test.js", "test:coverage": "jest __tests__/fdir.test.js --coverage", "benchmark": "node benchmark.js"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "types": "index.d.ts", "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^1.0.7", "klaw-sync": "^6.0.0", "mock-fs": "^4.11.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.0.0", "recursive-readdir": "^2.2.2", "rrdir": "^6.1.2", "walk-sync": "^2.0.2"}, "gitHead": "8a37b802c35b5e4ea58b5238e2b67668e5231980", "_id": "fdir@3.0.0", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"shasum": "6e0ca5ad4d1734cb01d759871c967a642c2cb9b5", "size": 6800, "noattachment": false, "tarball": "https://registry.npmmirror.com/fdir/-/fdir-3.0.0.tgz", "integrity": "sha512-rW85fKIixACH1QeZpbL3cWDJimQer9lrzcpiTe32w+59ub1fhjXJxGlTL/RwHDjz8X4jPdue1bR+U4jC08jerA=="}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_3.0.0_1589136165880_0.7068900618587308"}, "_hasShrinkwrap": false, "publish_time": 1589136165986, "_cnpm_publish_time": 1589136165986, "_cnpmcore_publish_time": "2021-12-16T23:50:40.366Z"}, "2.1.1": {"name": "fdir", "version": "2.1.1", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "main": "index.js", "scripts": {"test": "jest __tests__/fdir.test.js", "test:coverage": "jest __tests__/fdir.test.js --coverage", "benchmark": "node benchmark.js"}, "types": "index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^1.0.7", "klaw-sync": "^6.0.0", "mock-fs": "^4.11.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.0.0", "recursive-readdir": "^2.2.2", "rrdir": "^6.1.2", "walk-sync": "^2.0.2"}, "gitHead": "7f2e2f1c8d02c5ba3dfe0f0e708b215d8d51c2da", "_id": "fdir@2.1.1", "_nodeVersion": "13.13.0", "_npmVersion": "6.14.4", "dist": {"shasum": "11b3d8b9b74aab626791c73c5e5ce28cf8fcc8af", "size": 5908, "noattachment": false, "tarball": "https://registry.npmmirror.com/fdir/-/fdir-2.1.1.tgz", "integrity": "sha512-pBc2pGnR4cTkJf7y0NmhWlDFtZw4r92i5TW+tirHYxgivX7ZlfGKRk9KZ667xGbmCvrzb9f11a4j+qAeHz+oqQ=="}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_2.1.1_1587972581072_0.6707792697490127"}, "_hasShrinkwrap": false, "publish_time": 1587972581173, "_cnpm_publish_time": 1587972581173, "_cnpmcore_publish_time": "2021-12-16T23:50:40.598Z"}, "2.1.0": {"name": "fdir", "version": "2.1.0", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "main": "index.js", "scripts": {"test": "jest __tests__/fdir.test.js", "test:coverage": "jest __tests__/fdir.test.js --coverage", "benchmark": "node benchmark.js"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^1.0.7", "klaw-sync": "^6.0.0", "mock-fs": "^4.11.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.0.0", "recursive-readdir": "^2.2.2", "rrdir": "^6.1.2", "walk-sync": "^2.0.2"}, "gitHead": "9f1d47c4e2af6231ea61fc8157e8247d98b6d48a", "_id": "fdir@2.1.0", "_nodeVersion": "13.11.0", "_npmVersion": "6.13.7", "dist": {"shasum": "92ea339a3a4493fcbd31475f539d0ab80c92acfa", "size": 5331, "noattachment": false, "tarball": "https://registry.npmmirror.com/fdir/-/fdir-2.1.0.tgz", "integrity": "sha512-3OrEdo5P0o6SCvy0G4TNXx97D4/f3E+oKMF0q9MmWGIxJDLSjQabRJnlBApLtopdoaZ108WjPjHQM7niFYHYGg=="}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_2.1.0_1584816776860_0.261173853932126"}, "_hasShrinkwrap": false, "publish_time": 1584816776998, "_cnpm_publish_time": 1584816776998, "_cnpmcore_publish_time": "2021-12-16T23:50:40.797Z"}, "2.0.3": {"name": "fdir", "version": "2.0.3", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "main": "index.js", "scripts": {"test": "jest __tests__/fdir.test.js", "test:coverage": "jest __tests__/fdir.test.js --coverage", "benchmark": "node benchmark.js"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^1.0.7", "klaw-sync": "^6.0.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.0.0", "recursive-readdir": "^2.2.2", "rrdir": "^6.1.2", "walk-sync": "^2.0.2"}, "gitHead": "77dac2bf46e45ef06ce6dcdd6865fcfdf7648a65", "_id": "fdir@2.0.3", "_nodeVersion": "13.11.0", "_npmVersion": "6.13.7", "dist": {"shasum": "e0df292e3ff13ea3e645673b82752d0f3bddfaac", "size": 5117, "noattachment": false, "tarball": "https://registry.npmmirror.com/fdir/-/fdir-2.0.3.tgz", "integrity": "sha512-kg8oTgW2Ef+5FHBTqL9HAOEoZy21J+nCYCX7sD9yw7fP5sWd51q3tLqocOKaN/7bZW1dtiNfzwc39QhiIw8z1Q=="}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_2.0.3_1584556441965_0.12115473584501557"}, "_hasShrinkwrap": false, "publish_time": 1584556442161, "_cnpm_publish_time": 1584556442161, "_cnpmcore_publish_time": "2021-12-16T23:50:41.013Z"}, "2.0.2": {"name": "fdir", "version": "2.0.2", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "main": "index.js", "scripts": {"test": "jest __tests__/fdir.test.js", "test:coverage": "jest __tests__/fdir.test.js --coverage", "benchmark": "node benchmark.js"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^1.0.7", "klaw-sync": "^6.0.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.0.0", "recursive-readdir": "^2.2.2", "rrdir": "^6.1.2", "walk-sync": "^2.0.2"}, "gitHead": "77dac2bf46e45ef06ce6dcdd6865fcfdf7648a65", "_id": "fdir@2.0.2", "_nodeVersion": "13.11.0", "_npmVersion": "6.13.7", "dist": {"shasum": "f778a3d99d04a174ad4b8195d1245cecfaa30876", "size": 5775, "noattachment": false, "tarball": "https://registry.npmmirror.com/fdir/-/fdir-2.0.2.tgz", "integrity": "sha512-5a8W4Z7HOB5F480mHvAt/raHkjXecQIr7WFXtMdSMI5zhzTclzyL2S4v1Fgk+xYCDAOUDMdq92/YFt4CE63BRg=="}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_2.0.2_1584556409604_0.43643740346984994"}, "_hasShrinkwrap": false, "publish_time": 1584556409730, "_cnpm_publish_time": 1584556409730, "_cnpmcore_publish_time": "2021-12-16T23:50:41.296Z"}, "2.0.1": {"name": "fdir", "version": "2.0.1", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "main": "index.js", "scripts": {"test": "jest __tests__/fdir.test.js", "test:coverage": "jest __tests__/fdir.test.js --coverage", "benchmark": "node benchmark.js"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^1.0.7", "klaw-sync": "^6.0.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.0.0", "recursive-readdir": "^2.2.2", "rrdir": "^6.1.2", "walk-sync": "^2.0.2"}, "gitHead": "d6ca7741b4a5bc57590785050606d72468350c6c", "_id": "fdir@2.0.1", "_nodeVersion": "13.11.0", "_npmVersion": "6.13.7", "dist": {"shasum": "647461ffb418ef046b26499c890c900251bf95cf", "size": 4902, "noattachment": false, "tarball": "https://registry.npmmirror.com/fdir/-/fdir-2.0.1.tgz", "integrity": "sha512-FJ/Wkcawd1nPhJ+vDTtoIZKLDYnnFiPr7o9AECmqp9fEEPT/rUzdluhBUfflI16m0DHtXmZmz+E3ERYjHgMFTQ=="}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_2.0.1_1584346575142_0.6543708596028741"}, "_hasShrinkwrap": false, "publish_time": 1584346575281, "_cnpm_publish_time": 1584346575281, "_cnpmcore_publish_time": "2021-12-16T23:50:41.973Z"}, "2.0.0": {"name": "fdir", "version": "2.0.0", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "main": "index.js", "scripts": {"test": "jest __tests__/fdir.test.js", "test:coverage": "jest __tests__/fdir.test.js --coverage", "benchmark": "node benchmark.js"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^1.0.7", "klaw-sync": "^6.0.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.0.0", "recursive-readdir": "^2.2.2", "rrdir": "^6.1.2", "walk-sync": "^2.0.2"}, "gitHead": "2fc16f2aa18232fa1d2d12bb48146512aa6c92f3", "_id": "fdir@2.0.0", "_nodeVersion": "13.11.0", "_npmVersion": "6.13.7", "dist": {"shasum": "b2f38d646e4786ca256720c6dbe61ebc17a928cb", "size": 5274, "noattachment": false, "tarball": "https://registry.npmmirror.com/fdir/-/fdir-2.0.0.tgz", "integrity": "sha512-cDrfKNZyHeQneP/GQewfLIABN+ZxFzx/JeGowN/krdWsMtAQVEA2VPITgvG4ZRdaW35fvNBDuVxv+ndb2f6Q0g=="}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_2.0.0_1584346537088_0.1739858587033385"}, "_hasShrinkwrap": false, "publish_time": 1584346537209, "_cnpm_publish_time": 1584346537209, "_cnpmcore_publish_time": "2021-12-16T23:50:42.179Z"}, "1.2.0": {"name": "fdir", "version": "1.2.0", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "main": "index.js", "scripts": {"test": "jest", "test:coverage": "jest --coverage", "benchmark": "node benchmark.js"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^1.0.7", "klaw-sync": "^6.0.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.0.0", "recursive-readdir": "^2.2.2", "rrdir": "^6.1.2", "walk-sync": "^2.0.2"}, "gitHead": "72d02d1852199831033a696fca9f4165d17f5a8f", "_id": "fdir@1.2.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.3.0", "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "dist": {"shasum": "05573596422464612cb10d97129b2f6a9fcecd6c", "size": 21331, "noattachment": false, "tarball": "https://registry.npmmirror.com/fdir/-/fdir-1.2.0.tgz", "integrity": "sha512-wZSMj7wgLfouv2dECNcMX9w4VgM0RNMgygLDkSufR99zXPqRUC6xLbY5IhJJaZZVSbWqvnVTRDdhgFR45wQ9gQ=="}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_1.2.0_1584289888705_0.7224602488473086"}, "_hasShrinkwrap": false, "publish_time": 1584289888798, "_cnpm_publish_time": 1584289888798, "_cnpmcore_publish_time": "2021-12-16T23:50:42.415Z"}, "1.1.1": {"name": "fdir", "version": "1.1.1", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "main": "index.js", "scripts": {"test": "jest", "test:coverage": "jest --coverage", "benchmark": "node benchmark.js"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^1.0.7", "jest": "24.0.0", "klaw-sync": "^6.0.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-readdir": "^2.2.2", "rrdir": "^2.0.0", "walk-sync": "^2.0.2"}, "gitHead": "469e3fe138e91ef057e885c6939bc8025fbc83bf", "_id": "fdir@1.1.1", "_nodeVersion": "13.11.0", "_npmVersion": "6.13.7", "dist": {"shasum": "2c996d47088927bd08993228dcb0ce468a27533d", "size": 4822, "noattachment": false, "tarball": "https://registry.npmmirror.com/fdir/-/fdir-1.1.1.tgz", "integrity": "sha512-wQTqkAv9EB1Tq/9iPEkvZu8M1Ow2QVuAUmixo4trVEuefoqPWZrHPm5sROMq2TfDzlApYmGT6FUe58uc7NZdTw=="}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_1.1.1_1584205395296_0.2617602548499933"}, "_hasShrinkwrap": false, "publish_time": 1584205395410, "_cnpm_publish_time": 1584205395410, "_cnpmcore_publish_time": "2021-12-16T23:50:42.621Z"}, "1.1.0": {"name": "fdir", "version": "1.1.0", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "main": "index.js", "scripts": {"test": "jest", "test:coverage": "jest --coverage", "benchmark": "node benchmark.js"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^1.0.7", "jest": "24.0.0", "klaw-sync": "^6.0.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-readdir": "^2.2.2", "rrdir": "^2.0.0", "walk-sync": "^2.0.2"}, "gitHead": "9d5dfd8311627eb92c754a36d1198f647115baf5", "_id": "fdir@1.1.0", "_nodeVersion": "13.11.0", "_npmVersion": "6.13.7", "dist": {"shasum": "a37df0794f6083e46d95538dbc034ac6cec86e5c", "size": 22143, "noattachment": false, "tarball": "https://registry.npmmirror.com/fdir/-/fdir-1.1.0.tgz", "integrity": "sha512-c/0XxB0Za7/dBWoPn92mI9kuSD0E/pS6L13evZd+0CCt7Q6ab415NGOtALejZ59FXMQ28SlyfpJvhl+iWSnqMQ=="}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_1.1.0_1584205295308_0.046230999315329147"}, "_hasShrinkwrap": false, "publish_time": 1584205295435, "_cnpm_publish_time": 1584205295435, "_cnpmcore_publish_time": "2021-12-16T23:50:42.829Z"}, "1.0.3": {"name": "fdir", "version": "1.0.3", "description": "The fastest directory crawler written for NodeJS. Zero dependencies. Grabs 10k files in 13ms.", "main": "index.js", "scripts": {"test": "test", "benchmark": "node benchmark.js"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"@folder/readdir": "^2.1.0", "all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^1.0.7", "klaw-sync": "^6.0.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-readdir": "^2.2.2", "rrdir": "^5.0.0", "walk-sync": "^2.0.2"}, "gitHead": "c6fa353623f10533244bce8053faeb3c2865d7e5", "_id": "fdir@1.0.3", "_nodeVersion": "13.9.0", "_npmVersion": "6.14.1", "dist": {"shasum": "99b021a779d32ae0e97f8c4082e98f3fa9c4a94f", "size": 4263, "noattachment": false, "tarball": "https://registry.npmmirror.com/fdir/-/fdir-1.0.3.tgz", "integrity": "sha512-CxrtkPN0orhlog3LwWfahyK7kYxjLCT6AO+f+rphBf4gIwisfBC8+bouDHyAPiMXQuu51TI+VyEp66tZhAGGtw=="}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_1.0.3_1584181832289_0.10666282104543723"}, "_hasShrinkwrap": false, "publish_time": 1584181832415, "_cnpm_publish_time": 1584181832415, "_cnpmcore_publish_time": "2021-12-16T23:50:43.056Z"}, "1.0.2": {"name": "fdir", "version": "1.0.2", "description": "The fastest directory crawler written for NodeJS. Zero dependencies. Grabs 10k files in 13ms.", "main": "index.js", "scripts": {"test": "test", "benchmark": "node benchmark.js"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"@folder/readdir": "^2.1.0", "all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^1.0.7", "klaw-sync": "^6.0.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-readdir": "^2.2.2", "rrdir": "^5.0.0", "walk-sync": "^2.0.2"}, "gitHead": "6a05fe94f92272f7ae7310c7874e68be26f89081", "_id": "fdir@1.0.2", "_nodeVersion": "13.9.0", "_npmVersion": "6.14.1", "dist": {"shasum": "8ff2572fa33cd7f5ab06247ad9d59871fba1a568", "size": 4194, "noattachment": false, "tarball": "https://registry.npmmirror.com/fdir/-/fdir-1.0.2.tgz", "integrity": "sha512-/uvsBcz4ldDNDl4TzTQ0jzF7jayjRTGHuXunjDyudvRgsYl66v/ciufXiD8U9IYQMDYc6/WHdD/Gv92PyKaXQw=="}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_1.0.2_1584133747912_0.16804039697218176"}, "_hasShrinkwrap": false, "publish_time": 1584133748046, "_cnpm_publish_time": 1584133748046, "_cnpmcore_publish_time": "2021-12-16T23:50:43.243Z"}, "1.0.1": {"name": "fdir", "version": "1.0.1", "description": "The fastest directory crawler written for NodeJS. Zero dependencies. Grabs 10k files in 13ms.", "main": "index.js", "scripts": {"test": "test", "benchmark": "node benchmark.js"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"@folder/readdir": "^2.1.0", "all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^1.0.7", "klaw-sync": "^6.0.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-readdir": "^2.2.2", "rrdir": "^5.0.0", "walk-sync": "^2.0.2"}, "gitHead": "6a05fe94f92272f7ae7310c7874e68be26f89081", "_id": "fdir@1.0.1", "_nodeVersion": "13.9.0", "_npmVersion": "6.14.1", "dist": {"shasum": "bb9f67c0e983c9395470a5a8cf1eb907151bb5ee", "size": 4194, "noattachment": false, "tarball": "https://registry.npmmirror.com/fdir/-/fdir-1.0.1.tgz", "integrity": "sha512-aTsQM5TFdKqSTdOjBf5ubxEoTJoDorABOeN9EEV6ndUfgwhpv87Pme9ey1G03mCJmQfBkZrrDDdGf1UMU0odVg=="}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_1.0.1_1584133447344_0.4147828627666701"}, "_hasShrinkwrap": false, "publish_time": 1584133447491, "_cnpm_publish_time": 1584133447491, "_cnpmcore_publish_time": "2021-12-16T23:50:43.449Z"}, "1.0.0": {"name": "fdir", "version": "1.0.0", "description": "The fastest directory walker written in NodeJS", "main": "index.js", "scripts": {"test": "test", "benchmark": "node benchmark.js"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"@folder/readdir": "^2.1.0", "all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^1.0.7", "klaw-sync": "^6.0.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-readdir": "^2.2.2", "rrdir": "^5.0.0", "walk-sync": "^2.0.2"}, "gitHead": "3abb1cd3699f189438cc4ada7d3d6a985cc2c44d", "_id": "fdir@1.0.0", "_nodeVersion": "13.9.0", "_npmVersion": "6.14.1", "dist": {"shasum": "4a3a197e57f0da3449aef17322a8a463939c419f", "size": 134242, "noattachment": false, "tarball": "https://registry.npmmirror.com/fdir/-/fdir-1.0.0.tgz", "integrity": "sha512-ZAW2znCQvj8EPK/SYLyI6WdV/+uhxvn5u2DetrZo6zStHyDyTOcOznfnhX/N0akWreISZCLaLcHELxdg+vmk+w=="}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_1.0.0_1584133107336_0.13950252841103183"}, "_hasShrinkwrap": false, "publish_time": 1584133107499, "_cnpm_publish_time": 1584133107499, "_cnpmcore_publish_time": "2021-12-16T23:50:43.895Z"}, "5.2.0": {"name": "fdir", "version": "5.2.0", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "main": "index.js", "scripts": {"test": "jest __tests__/fdir.test.js", "test:coverage": "jest __tests__/fdir.test.js --coverage", "bench": "node benchmarks/benchmark.js", "bench:glob": "node benchmarks/glob-benchmark.js", "bench:fdir": "node benchmarks/fdir-benchmark.js", "release": "./scripts/release.sh"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "types": "index.d.ts", "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fast-glob": "^3.2.2", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^2.0.0", "glob": "^7.1.6", "klaw-sync": "^6.0.0", "mock-fs": "^5.0.0", "picomatch": "^2.2.2", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.0.0", "recursive-readdir": "^2.2.2", "rrdir": "^6.1.2", "systeminformation": "^5.3.1", "tiny-glob": "^0.2.9", "walk-sync": "^2.0.2"}, "gitHead": "65f790b86223e0743896eed46e30db3f886a9e49", "_id": "fdir@5.2.0", "_nodeVersion": "17.3.1", "_npmVersion": "8.3.0", "dist": {"integrity": "sha512-skyI2Laxtj9GYzmktPgY6DT8uswXq+VoxH26SskykvEhTSbi7tRM/787uZt/p8maxrQCJdzC90zX1btbxiJ6lw==", "shasum": "5b3d6ae282f8bc0ef48bf913d46f9e08496304ea", "tarball": "https://registry.npmmirror.com/fdir/-/fdir-5.2.0.tgz", "fileCount": 14, "unpackedSize": 30677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5lqTCRA9TVsSAnZWagAAPzQP/R1AZV6j2PNOufLXZLG2\nFScviDROLgiZs8O6Ew3FVCJUdbBb6A8zEvwCyn18ZJMXH/v4TAG2ZPDsqKAN\npWIG3y77sHt4Ha+UxnCMSSobaPtcr0sanhHf+hJHO3UKmYETwetRvh3/y5Po\n7n1PxvvAg1Z9K4XnyQHM4fdVDslcdKI048rWmqT9U1J0UsW6h8k0DGUgJBBW\nsn1thASCXHRBA2hh/pxlSZZcQm40we18ffONMQkAiokNEmLKch9tTH7Mtmc/\nSnFtNCDcYb+Bny717qYDFdIzeQNjybYNaRMlRXwt8pUK0wXPzIEucpc4pjjg\nlDcajoG4Fqxzhvg9/p6uQ4t3KA8HfdSv9eh50iwZHuOWRl6kgOJ1a11Kr8pf\nGLJyZEKxAceWDFnFsGroKxmCkKpAs5eLrtNSi6taRbzix254QQ44ERS4DHB+\nz6eAWyZmONL/wNtOXwZFqphZPpSH86f0r+R6YpgUbz+2AbpeHM6W94lKosCk\nziKWdvf6bD0nYoaWAhXJBwk6TdQeZIGZr/dEEHma9jwBlfZ//XGkgmaM7+dp\n67ugHnukKd/xDQCQgDN/G8zrE02SXGqMEmkXWiWhCakSdoBicW18uhKkNZMS\nIeR3giYiLu5QhCVah3dyHAIr7WZOq+IqkfgB/1ODZXw/LKSeSJ1CIwQD3/AW\nxs6+\r\n=CqHE\r\n-----END PGP SIGNATURE-----\r\n", "size": 9537}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_5.2.0_1642486419134_0.28027301142453975"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-18T06:13:51.973Z"}, "5.2.1": {"name": "fdir", "version": "5.2.1", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "main": "index.js", "scripts": {"test": "jest __tests__/fdir.test.js", "test:coverage": "jest __tests__/fdir.test.js --coverage", "bench": "node benchmarks/benchmark.js", "bench:glob": "node benchmarks/glob-benchmark.js", "bench:fdir": "node benchmarks/fdir-benchmark.js", "release": "./scripts/release.sh"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "types": "index.d.ts", "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fast-glob": "^3.2.2", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^2.0.0", "glob": "^7.1.6", "klaw-sync": "^6.0.0", "mock-fs": "^5.0.0", "picomatch": "^2.2.2", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.0.0", "recursive-readdir": "^2.2.2", "rrdir": "^6.1.2", "systeminformation": "^5.3.1", "tiny-glob": "^0.2.9", "walk-sync": "^2.0.2"}, "peerDependencies": {"picomatch": "2.x"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "gitHead": "0ed9e53686466152aa006669debdcd45349498f0", "_id": "fdir@5.2.1", "_nodeVersion": "16.17.1", "_npmVersion": "8.15.0", "dist": {"integrity": "sha512-YtT6MYUmSreefWXhX8XE2tmK7cI8+nRyi5VgOI0CE6AQZE7UyFWwjTbitLCq1mn6vRdYc5zyjWnUHeb0lN//9g==", "shasum": "d1eefb071c860f9c2ea5570b05cf6f74089c4171", "tarball": "https://registry.npmmirror.com/fdir/-/fdir-5.2.1.tgz", "fileCount": 14, "unpackedSize": 30810, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDVjl05uVKOTgS1e2LwmLpNznYY/PWnD+ozrrX/YxuDggIhAMGysQuoO5ib/xtjVCobpTMCmpODn5EImp4jhlY2o4W4"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTaD2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoOfg/+Nl0vQpxiR6/ZqQ5UXbWvb4F8seE4gBlbjmZ48IiUZ8/sjal/\r\nEHaqjgB+7fAD/+4s7TEFDrk2x02OjneUX3UQXZQrOFXftVVuHK353QDLVOAr\r\nkT81YNqYRsWHwE1br8nsTmOyNOofYnffLxFpIkWanKn3GSVDr37JogV49hTz\r\nR2mwbqsdD9JZlQHQcBmRtwoGP2usGWklIwg4tmDGllWiE6u9kQr9FyUwjm/v\r\n7wqIQWmUQ17lBhRz7PaEEl+WRLiE2Ew6Ov2GhkvPJQhOxCc50moQw0De5C6x\r\nrbf7hFWmxy2O0AGcPsC0P35mBqPoeoFVnsioitYpGNPWUzA/7ITEH32FEXaf\r\nY4kEggwWBkME8wNlLmkvelaotiy07EwRx+yi74zL+TAHZphZbUzZKMPrFt+K\r\nA/ZlLGvIwhZ1rL3ontsT+sGj47exUStl4qIxlfjBDE8vHvO9H6kUPoMDwQKt\r\nl3NTHGi+y90RIY2TV7BdX7Ete91D9nWdfV8k6mPUhz3WgrcIEVIMw/wYJcvx\r\nF6ImVWhmtYxMt7aHTwmbGF77rqkX7yyvQO4U1BFixv5nf5ufPqwF5CN7Cno2\r\ntxSibT0UURcmHSNkkCM0DTgOPTLz9+spXTEyanZQQIac0Ap0Ci6Hd1L831WB\r\nJr3aOeaUPft0R7wAnN1wk9AI6YBor4+eJQ4=\r\n=IArI\r\n-----END PGP SIGNATURE-----\r\n", "size": 9572}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_5.2.1_1666031861789_0.9789395006633979"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-18T17:41:42.270Z"}, "5.3.0": {"name": "fdir", "version": "5.3.0", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "main": "index.js", "scripts": {"test": "jest __tests__/fdir.test.js", "test:coverage": "jest __tests__/fdir.test.js --coverage", "bench": "node benchmarks/benchmark.js", "bench:glob": "node benchmarks/glob-benchmark.js", "bench:fdir": "node benchmarks/fdir-benchmark.js", "release": "./scripts/release.sh"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "types": "index.d.ts", "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"all-files-in-tree": "^1.1.2", "benny": "^3.6.14", "fast-glob": "^3.2.2", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^2.0.0", "glob": "^7.1.6", "klaw-sync": "^6.0.0", "mock-fs": "^5.0.0", "picomatch": "^2.2.2", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.0.0", "recursive-readdir": "^2.2.2", "rrdir": "^6.1.2", "systeminformation": "^5.3.1", "tiny-glob": "^0.2.9", "walk-sync": "^2.0.2"}, "peerDependencies": {"picomatch": "2.x"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "gitHead": "926609a108fcd53c0694826e2e2fa7f197d06af3", "_id": "fdir@5.3.0", "_nodeVersion": "16.17.1", "_npmVersion": "8.15.0", "dist": {"integrity": "sha512-BtE53+jaa7nNHT+gPdfU6cFAXOJUWDs2b5GFox8dtl6zLXmfNf/N6im69b9nqNNwDyl27mpIWX8qR7AafWzSdQ==", "shasum": "67c6a75edebb887906fb22fec224fa5c2b1ff1e8", "tarball": "https://registry.npmmirror.com/fdir/-/fdir-5.3.0.tgz", "fileCount": 14, "unpackedSize": 31880, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICc5XkVzyqkpyNQLTq+twd6y+cQzmKj8XIV0awxsPuQ4AiEAzNSXKf7z0nSzB/lksrU4PYw6KmT7QhWsztEwXnhtvXY="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTuTKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqPuQ/8CysDx5njtKFu624A3BPYMszyh2Mlk3bGWoCy4KXKQBooEvvK\r\n8dJs7c48ypTFlsB1YTVMpWMufm06iatc3uJvrElqxNYSvaUV+tszTctf48Og\r\nNvnhkXjOy4daaqfC21ZmsLCMv0CYiD7wMsRrxU8XDY3GHCY2LlJZDe89icXx\r\nqEHLOPxJh0sCxnThuFFhN1bvQsWbmhtmPJmkTyLZtYaxsFdzGrBL5vfTDkgT\r\nmYyoS6Dpiziy+ZLXYWohbD89tqbOZGVNuoRMULSJIHXhezWSC+P7AECFHtX9\r\nEbjA8Rn4YY+vL0Mu9e5s8iYBp4bHsLYnNgCjmCpoDxDHbDKeRlmjy5qRAEyq\r\nYfRK0c9s+tywc54brp26A5qzLr2kQRt2qut5DdpWQtMSOdp+5AOzMS7xxbbk\r\njrx+7wy40CL3BoOZNg+4ZGW6ShC5TOB67TZdFeSnf4IBv4anMMQqgWkLPFSl\r\nh4/UNtSNCXJVyi3CtKZG7vdJYy1Yuo1qKbPp2NgwQXhKIB480xArlE9nSIr6\r\ng+Megph8D/rvf054fpVofiNN9iWjX/FxDwmrBKT5MO9Ghjt15xuCEqaxZEGD\r\n0uwXO4xcYCzjA4HilYxdJYZ7DoGbUaa1gXKx8FIvn5aPWOOtViefYuZ8a0Nx\r\n9CA1RFRbHggiiN0qxOarloF0z2qbRC76eB8=\r\n=7P0e\r\n-----END PGP SIGNATURE-----\r\n", "size": 9781}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_5.3.0_1666114762500_0.4674492286125229"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-18T17:41:42.571Z"}, "6.0.0": {"name": "fdir", "version": "6.0.0", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "main": "dist/index.js", "scripts": {"prepublishOnly": "npm run test && npm run build", "build": "tsc", "test": "tap --ts --no-check-coverage __tests__/", "test:coverage": "tap --ts --no-check-coverage --coverage-report=html __tests__/", "bench": "node benchmarks/benchmark.js", "bench:glob": "node benchmarks/glob-benchmark.js", "bench:fdir": "node benchmarks/fdir-benchmark.js", "release": "./scripts/release.sh"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "types": "index.d.ts", "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"@types/glob": "^8.0.1", "@types/mock-fs": "^4.13.1", "@types/node": "^18.13.0", "@types/picomatch": "^2.3.0", "@types/tap": "^15.0.8", "all-files-in-tree": "^1.1.2", "benny": "^3.7.1", "expect": "^29.4.2", "fast-glob": "^3.2.2", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^4.1.0", "glob": "^8.1.0", "klaw-sync": "^6.0.0", "mock-fs": "^5.2.0", "picomatch": "^2.3.1", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.1.0", "recursive-readdir": "^2.2.3", "rrdir": "^10.1.1", "systeminformation": "^5.17.8", "tap": "^16.3.4", "tiny-glob": "^0.2.9", "ts-node": "^10.9.1", "typescript": "^4.9.5", "walk-sync": "^3.0.0"}, "peerDependencies": {"picomatch": "2.x"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "gitHead": "d4b6e71dd478d115b9170b06a8f1daa6dcb0752a", "_id": "fdir@6.0.0", "_nodeVersion": "18.14.0", "_npmVersion": "9.3.1", "dist": {"integrity": "sha512-OTY4oARmFlLhd6dMi3qWGW7MkSi5cRmorjwBp1X9SE51s61qkKspeTSPOSKzCNb/+gXkhayqQonMALkEEq/tqQ==", "shasum": "277a777a39d7e54da2e4f15ed3d3ba95a4556b36", "tarball": "https://registry.npmmirror.com/fdir/-/fdir-6.0.0.tgz", "fileCount": 50, "unpackedSize": 54369, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC5HXwPGjnaxU7i5RHkUaZlW42ZlfWyUZhW6/e1C9F3gwIhAIwOZkNv45YyjdESqiPvWhoxJdvEI+lN2a3lqGCDco3N"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4+R2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrr2w//QJ6AK/2FWFLRkKY3ASax6sw0N925aeI4k1UWe9nv+vJYpLqD\r\nHz94NKI1aiHKQprQKKZpUZ+RMkCivGLOxjJJUCAtlMlMsKrg1knSiN05IsUE\r\nkfCcnF1N6B6X4XBL+hKhpvtIkuf+GObcd59EBXCFZk261MDXsNlL8PtNBX23\r\nZ5pSsw6F1TPRGq1g48nPARVOc0IGqZjECrYItPOd9oveZd9Xz+4QwBQkCvDh\r\n4O0YQTJ62L0+zAnG2TsvuNztT0oQW0ZaCaK20eI3ELaMucT3uR19CqwjDjEm\r\nqVrt8bW40ULXRRbaVh98tk379P1lQh+CSAIGXX+zOFobMXTRqAXy5AFVC7H3\r\n6M/ZVTnuc6WPrghQ1sIu2DvDXLMKDFCwyilgAP9yC2zRbzZ9z9C3GBBG+2/z\r\nxXn1yJRH6KjmakjwmOtFGANSbPCdfEfFCnxN+Xyzrks9ULs2d0m3MYNvwnDy\r\nJDj58BVo4obZIz6syxi0DyEYRD25nOUSIs4TYqjYV2wMrdrWpo0bTa77uuP0\r\nn2iJrLZ0xQFhYIIHclBBnSWl3qzsRatRow9+4+jymOmHWwZmpvOisxBghkrM\r\nlPRyWoohnvtEX7slCouSORyh7cVL3RhrqJGPHIKwI+YCbm/8ponyC2ktzdjU\r\nlcwn2McUbnxd7WuUE7mYw7TjNFa5Eii5zCE=\r\n=XDZM\r\n-----END PGP SIGNATURE-----\r\n", "size": 13335}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_6.0.0_1675879542248_0.7946965134011363"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-08T18:05:42.434Z", "publish_time": 1675879542434}, "6.0.1": {"name": "fdir", "version": "6.0.1", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"prepublishOnly": "npm run test && npm run build", "build": "tsc", "test": "tap --ts --no-check-coverage __tests__/", "test:coverage": "tap --ts --no-check-coverage --coverage-report=html __tests__/", "bench": "node benchmarks/benchmark.js", "bench:glob": "node benchmarks/glob-benchmark.js", "bench:fdir": "node benchmarks/fdir-benchmark.js", "release": "./scripts/release.sh"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"@types/glob": "^8.0.1", "@types/mock-fs": "^4.13.1", "@types/node": "^18.13.0", "@types/picomatch": "^2.3.0", "@types/tap": "^15.0.8", "all-files-in-tree": "^1.1.2", "benny": "^3.7.1", "expect": "^29.4.2", "fast-glob": "^3.2.2", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^4.1.0", "glob": "^8.1.0", "klaw-sync": "^6.0.0", "mock-fs": "^5.2.0", "picomatch": "^2.3.1", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.1.0", "recursive-readdir": "^2.2.3", "rrdir": "^10.1.1", "systeminformation": "^5.17.8", "tap": "^16.3.4", "tiny-glob": "^0.2.9", "ts-node": "^10.9.1", "typescript": "^4.9.5", "walk-sync": "^3.0.0"}, "peerDependencies": {"picomatch": "2.x"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "gitHead": "6a5dba68344bc7641af171d633c1377c9e901da3", "_id": "fdir@6.0.1", "_nodeVersion": "16.19.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-bdrUUb0eYQrPRlaAtlSRoLs7sp6yKEwbMQuUgwvi/14TnaqhM/deSZUrC5ic+yjm5nEPPWE61oWpTTxQFQMmLA==", "shasum": "1edca6c0295d072802e2c67a2f4fbf941cd32af5", "tarball": "https://registry.npmmirror.com/fdir/-/fdir-6.0.1.tgz", "fileCount": 50, "unpackedSize": 52983, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICsD4FOV5awokH50o5S/MFXcGVY6pHeFQrrs9sgNE7b2AiAoS5aGLCrhrqmHApClbGcpGgSHkUr/xTAD7SMtcTN0iQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5KSrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrnFhAAmqGye5MQDXR+JpHMq2uUj7j0I0/8wl9IYxQMscJr25IKQpCN\r\nITAAlFxpGnBw8v+MAyTwle7mTdbJh36k0lW/3Nraw1IT0VxdBesgEHh6Dv0i\r\nMmqBeXrHH4I0heuplc0j4wbdqXTAsB4I+Bd9QcMeAmbY0XZFB4HT4lCKxRhQ\r\nuWSQ0xWToayPN1M0MzCrdTA53V2AhwZZcK6eQ8BkYIhU5J8hdzqrwyXkD1D9\r\nqAVtvPFjB9bQH+Im0orKQbvZfjgDujSjVTj4rWkpFBNonL9l+AqU4X2OIkbr\r\nahai2IC7ZnqvKghsC+qZp6dmWPi8Q45u8W8jHIN0TZiBf/UFF9tYdud1MArk\r\nVBZYDBX3W88QMR1ZAw4mDwB69FyLTrGUOfkRtyeOX5KPljFJlp+9A9D83rJL\r\n7Ai21yE3bM3QwRoW3kG1ySPVX9MBZ0MidPOEn25lFwSqbkuYDC2m9Um4z8/q\r\nmteC4MLH3nzKApk9vK6nL/svmSHjIN8z+nJCGrD/Kjn4WuFv3/LNs2pP46BX\r\n7UPBGC7txmW8Hj1v/05stSsvwYBdNKEAErwFLrAxmPz+w0bSk+z3GdXY/Rds\r\nA/z+t5Uj1IJIN0/Sl5fL5ZWJNzCxF4JiqBEgvDwJTxjjXqqtKaS3+HCAIp9a\r\nLggZrjUWfXna/RaH9Ue//AgYwMne5Zsraxc=\r\n=ngcu\r\n-----END PGP SIGNATURE-----\r\n", "size": 13242}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_6.0.1_1675928747015_0.12975760641991108"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-09T07:45:47.214Z", "publish_time": 1675928747214}, "6.0.2": {"name": "fdir", "version": "6.0.2", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"prepublishOnly": "npm run test && npm run build", "build": "tsc", "test": "vitest run __tests__/", "test:coverage": "vitest run --coverage __tests__/", "test:watch": "vitest __tests__/", "bench": "node benchmarks/benchmark.js", "bench:glob": "ts-node benchmarks/glob-benchmark.ts", "bench:fdir": "ts-node benchmarks/fdir-benchmark.ts", "release": "./scripts/release.sh"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"@types/glob": "^8.0.1", "@types/mock-fs": "^4.13.1", "@types/node": "^18.13.0", "@types/picomatch": "^2.3.0", "@types/tap": "^15.0.8", "@vitest/coverage-c8": "^0.29.8", "all-files-in-tree": "^1.1.2", "benny": "^3.7.1", "csv-to-markdown-table": "^1.3.0", "expect": "^29.4.2", "fast-glob": "^3.3.1", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^4.1.0", "glob": "^10.3.3", "klaw-sync": "^6.0.0", "mock-fs": "^5.2.0", "picomatch": "^2.3.1", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.1.0", "recursive-readdir": "^2.2.3", "rrdir": "^10.1.1", "systeminformation": "^5.17.8", "tiny-glob": "^0.2.9", "ts-node": "^10.9.1", "typescript": "^4.9.5", "vitest": "^0.29.8", "walk-sync": "^3.0.0"}, "peerDependencies": {"picomatch": "2.x"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "_id": "fdir@6.0.2", "gitHead": "b3774d9a5c0a7813e0dd27f493af62d6730f34a7", "_nodeVersion": "20.5.0", "_npmVersion": "9.8.0", "dist": {"integrity": "sha512-XJVxBciDoEpRipMYyrTCqVQA4jMTfHNiYNy8OvIGTaQzEFPuMJEvmps+Rouo6rsnivkQax9s5m5gy1lHmY2Hmg==", "shasum": "077480393c4b20583e389b1bd72b9e964a1e310c", "tarball": "https://registry.npmmirror.com/fdir/-/fdir-6.0.2.tgz", "fileCount": 50, "unpackedSize": 55247, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA7syhQpkFX2NrWzQXzsowbAWxPwa/4PZ9hSyyzkwy44AiEA3uBOPhqzFZ16B+QF3Lk5wQZl11qw5WFI4UUedkfGVE8="}], "size": 13489}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_6.0.2_1690838255642_0.15017049014694694"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-31T21:17:35.824Z", "publish_time": 1690838255824, "_source_registry_name": "default"}, "6.1.0": {"name": "fdir", "version": "6.1.0", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"prepublishOnly": "npm run test && npm run build", "build": "tsc", "test": "vitest run __tests__/", "test:coverage": "vitest run --coverage __tests__/", "test:watch": "vitest __tests__/", "bench": "ts-node benchmarks/benchmark.js", "bench:glob": "ts-node benchmarks/glob-benchmark.ts", "bench:fdir": "ts-node benchmarks/fdir-benchmark.ts", "release": "./scripts/release.sh"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"@types/glob": "^8.0.1", "@types/mock-fs": "^4.13.1", "@types/node": "^18.13.0", "@types/picomatch": "^2.3.0", "@types/tap": "^15.0.8", "@vitest/coverage-c8": "^0.29.8", "all-files-in-tree": "^1.1.2", "benny": "^3.7.1", "csv-to-markdown-table": "^1.3.0", "expect": "^29.4.2", "fast-glob": "^3.3.1", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^4.1.0", "glob": "^10.3.3", "klaw-sync": "^6.0.0", "mock-fs": "^5.2.0", "picomatch": "^2.3.1", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.1.0", "recursive-readdir": "^2.2.3", "rrdir": "^10.1.1", "systeminformation": "^5.17.8", "tiny-glob": "^0.2.9", "ts-node": "^10.9.1", "typescript": "^4.9.5", "vitest": "^0.29.8", "walk-sync": "^3.0.0"}, "peerDependencies": {"picomatch": "2.x"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "gitHead": "192e7262cc50c7027eb37bf7066479043524b0bc", "_id": "fdir@6.1.0", "_nodeVersion": "18.17.0", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-274qhz5PxNnA/fybOu6apTCUnM0GnO3QazB6VH+oag/7DQskdYq8lm07ZSm90kEQuWYH5GvjAxGruuHrEr0bcg==", "shasum": "e5813b659e14671994e21287dae0f61fec8f9a62", "tarball": "https://registry.npmmirror.com/fdir/-/fdir-6.1.0.tgz", "fileCount": 50, "unpackedSize": 56024, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEmQdt7mQvXWZhRRywe58Xt0xwIg3a5uqLpMgThT7MrIAiAfZqGiQKF0fwcPQDNJWP+Rrsk8qH20nuj5We5gVvnB0Q=="}], "size": 13708}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_6.1.0_1691951348220_0.19719882289305146"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-13T18:29:08.556Z", "publish_time": 1691951348556, "_source_registry_name": "default"}, "6.1.1": {"name": "fdir", "version": "6.1.1", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"prepublishOnly": "npm run test && npm run build", "build": "tsc", "test": "vitest run __tests__/", "test:coverage": "vitest run --coverage __tests__/", "test:watch": "vitest __tests__/", "bench": "ts-node benchmarks/benchmark.js", "bench:glob": "ts-node benchmarks/glob-benchmark.ts", "bench:fdir": "ts-node benchmarks/fdir-benchmark.ts", "release": "./scripts/release.sh"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"@types/glob": "^8.0.1", "@types/mock-fs": "^4.13.1", "@types/node": "^18.13.0", "@types/picomatch": "^2.3.2", "@types/tap": "^15.0.8", "@vitest/coverage-c8": "^0.29.8", "all-files-in-tree": "^1.1.2", "benny": "^3.7.1", "csv-to-markdown-table": "^1.3.0", "expect": "^29.4.2", "fast-glob": "^3.3.1", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^4.1.0", "glob": "^10.3.3", "klaw-sync": "^6.0.0", "mock-fs": "^5.2.0", "picomatch": "^3.0.1", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.1.0", "recursive-readdir": "^2.2.3", "rrdir": "^10.1.1", "systeminformation": "^5.17.8", "tiny-glob": "^0.2.9", "ts-node": "^10.9.1", "typescript": "^4.9.5", "vitest": "^0.29.8", "walk-sync": "^3.0.0"}, "peerDependencies": {"picomatch": "3.x"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "_id": "fdir@6.1.1", "gitHead": "686e20d6db007d8655e7982d0ccf5fc16bd6f510", "_nodeVersion": "20.7.0", "_npmVersion": "10.1.0", "dist": {"integrity": "sha512-QfKBVg453Dyn3mr0Q0O+Tkr1r79lOTAKSi9f/Ot4+qVEwxWhav2Z+SudrG9vQjM2aYRMQQZ2/Q1zdA8ACM1pDg==", "shasum": "316b58145a05223b75c8b371e80bb3bad8f1441e", "tarball": "https://registry.npmmirror.com/fdir/-/fdir-6.1.1.tgz", "fileCount": 50, "unpackedSize": 56266, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE4vREQRpi/SDPMGN5fKwX2gsoA1kikpndoCfojcoSh6AiAeJwyrMi24z7ojLTEcMg8ndzWiREdb4WGvK1GL759qmQ=="}], "size": 13784}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_6.1.1_1699205166964_0.7398672852703851"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-05T17:26:07.154Z", "publish_time": 1699205167154, "_source_registry_name": "default"}, "6.2.0": {"name": "fdir", "version": "6.2.0", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"prepublishOnly": "npm run test && npm run build", "build": "tsc", "test": "vitest run __tests__/", "test:coverage": "vitest run --coverage __tests__/", "test:watch": "vitest __tests__/", "bench": "ts-node benchmarks/benchmark.js", "bench:glob": "ts-node benchmarks/glob-benchmark.ts", "bench:fdir": "ts-node benchmarks/fdir-benchmark.ts", "release": "./scripts/release.sh"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"@types/glob": "^8.1.0", "@types/mock-fs": "^4.13.4", "@types/node": "^20.9.4", "@types/picomatch": "^3.0.0", "@types/tap": "^15.0.11", "@vitest/coverage-v8": "^0.34.6", "all-files-in-tree": "^1.1.2", "benny": "^3.7.1", "csv-to-markdown-table": "^1.3.1", "expect": "^29.7.0", "fast-glob": "^3.3.2", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^4.1.0", "glob": "^10.3.10", "klaw-sync": "^6.0.0", "mock-fs": "^5.2.0", "picomatch": "^4.0.2", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.1.0", "recursive-readdir": "^2.2.3", "rrdir": "^12.1.0", "systeminformation": "^5.21.17", "tiny-glob": "^0.2.9", "ts-node": "^10.9.1", "typescript": "^5.3.2", "vitest": "^0.34.6", "walk-sync": "^3.0.0"}, "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "_id": "fdir@6.2.0", "gitHead": "db61890ca0b70f68ea0ffc2cb3fe17ca32bbecdd", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-9XaWcDl0riOX5j2kYfy0kKdg7skw3IY6kA4LFT8Tk2yF9UdrADUy8D6AJuBLtf7ISm/MksumwAHE3WVbMRyCLw==", "shasum": "9120f438d566ef3e808ca37864d9dd18e1a4f9b5", "tarball": "https://registry.npmmirror.com/fdir/-/fdir-6.2.0.tgz", "fileCount": 52, "unpackedSize": 58290, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFZ5zMYtgzHQjCy5bM8dEegRZp3+gBAuqeSI/lfvjVM8AiEA70QhKHccBXPDbJ7k+pGA5VdHSsFFuPys6RtbMLrB0Mc="}], "size": 14837}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_6.2.0_1721622133581_0.9584083959033181"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-22T04:22:13.742Z", "publish_time": 1721622133742, "_source_registry_name": "default"}, "6.3.0": {"name": "fdir", "version": "6.3.0", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"prepublishOnly": "npm run test && npm run build", "build": "tsc", "test": "vitest run __tests__/", "test:coverage": "vitest run --coverage __tests__/", "test:watch": "vitest __tests__/", "bench": "ts-node benchmarks/benchmark.js", "bench:glob": "ts-node benchmarks/glob-benchmark.ts", "bench:fdir": "ts-node benchmarks/fdir-benchmark.ts", "release": "./scripts/release.sh"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"@types/glob": "^8.1.0", "@types/mock-fs": "^4.13.4", "@types/node": "^20.9.4", "@types/picomatch": "^3.0.0", "@types/tap": "^15.0.11", "@vitest/coverage-v8": "^0.34.6", "all-files-in-tree": "^1.1.2", "benny": "^3.7.1", "csv-to-markdown-table": "^1.3.1", "expect": "^29.7.0", "fast-glob": "^3.3.2", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^4.1.0", "glob": "^10.3.10", "klaw-sync": "^6.0.0", "mock-fs": "^5.2.0", "picomatch": "^4.0.2", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.1.0", "recursive-readdir": "^2.2.3", "rrdir": "^12.1.0", "systeminformation": "^5.21.17", "tiny-glob": "^0.2.9", "ts-node": "^10.9.1", "typescript": "^5.3.2", "vitest": "^0.34.6", "walk-sync": "^3.0.0"}, "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "_id": "fdir@6.3.0", "gitHead": "5a0aab5b3e108eaf677e0e39c22ff1ea03cd1a59", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-QOnuT+BOtivR77wYvCWHfGt9s4Pz1VIMbD463vegT5MLqNXy8rYFT/lPVEqf/bhYeT6qmqrNHhsX+rWwe3rOCQ==", "shasum": "fcca5a23ea20e767b15e081ee13b3e6488ee0bb0", "tarball": "https://registry.npmmirror.com/fdir/-/fdir-6.3.0.tgz", "fileCount": 39, "unpackedSize": 38200, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGqK/5TqPUmn63TIaYc/sjQ1S0dS2tKcoULPI1QxQm3dAiEAoXdnui9mDMR7yGI3shbjYR23cByIxOo3GAC1Ub59PxY="}], "size": 10713}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_6.3.0_1724594048505_0.8152725786187789"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-25T13:54:08.680Z", "publish_time": 1724594048680, "_source_registry_name": "default"}, "6.4.0": {"name": "fdir", "version": "6.4.0", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"prepublishOnly": "npm run test && npm run build", "build": "tsc", "test": "vitest run __tests__/", "test:coverage": "vitest run --coverage __tests__/", "test:watch": "vitest __tests__/", "bench": "ts-node benchmarks/benchmark.js", "bench:glob": "ts-node benchmarks/glob-benchmark.ts", "bench:fdir": "ts-node benchmarks/fdir-benchmark.ts", "release": "./scripts/release.sh"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"@types/glob": "^8.1.0", "@types/mock-fs": "^4.13.4", "@types/node": "^20.9.4", "@types/picomatch": "^3.0.0", "@types/tap": "^15.0.11", "@vitest/coverage-v8": "^0.34.6", "all-files-in-tree": "^1.1.2", "benny": "^3.7.1", "csv-to-markdown-table": "^1.3.1", "expect": "^29.7.0", "fast-glob": "^3.3.2", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^4.1.0", "glob": "^10.3.10", "klaw-sync": "^6.0.0", "mock-fs": "^5.2.0", "picomatch": "^4.0.2", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.1.0", "recursive-readdir": "^2.2.3", "rrdir": "^12.1.0", "systeminformation": "^5.21.17", "tiny-glob": "^0.2.9", "ts-node": "^10.9.1", "typescript": "^5.3.2", "vitest": "^0.34.6", "walk-sync": "^3.0.0"}, "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "_id": "fdir@6.4.0", "gitHead": "5f551d5872173aeb504db5b1e883ee0cf89f8b70", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-3oB133prH1o4j/L5lLW7uOCF1PlD+/It2L0eL/iAqWMB91RBbqTewABqxhj0ibBd90EEmWZq7ntIWzVaWcXTGQ==", "shasum": "8e80ab4b18a2ac24beebf9d20d71e1bc2627dbae", "tarball": "https://registry.npmmirror.com/fdir/-/fdir-6.4.0.tgz", "fileCount": 39, "unpackedSize": 40190, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCRT9OnMoLLLHY8SMl4+p0wnWJaf+dsyGyP7T6EHhXaTAIgS/KgJCXHNxCNgGbPCu3P4hTj/q84SjbQxPdfJWbwr0c="}], "size": 11085}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_6.4.0_1727681111206_0.2858660272164242"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-30T07:25:11.390Z", "publish_time": 1727681111390, "_source_registry_name": "default"}, "6.4.1": {"name": "fdir", "version": "6.4.1", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"prepublishOnly": "npm run test && npm run build", "build": "tsc", "test": "vitest run __tests__/", "test:coverage": "vitest run --coverage __tests__/", "test:watch": "vitest __tests__/", "bench": "ts-node benchmarks/benchmark.js", "bench:glob": "ts-node benchmarks/glob-benchmark.ts", "bench:fdir": "ts-node benchmarks/fdir-benchmark.ts", "release": "./scripts/release.sh"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"@types/glob": "^8.1.0", "@types/mock-fs": "^4.13.4", "@types/node": "^20.9.4", "@types/picomatch": "^3.0.0", "@types/tap": "^15.0.11", "@vitest/coverage-v8": "^0.34.6", "all-files-in-tree": "^1.1.2", "benny": "^3.7.1", "csv-to-markdown-table": "^1.3.1", "expect": "^29.7.0", "fast-glob": "^3.3.2", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^4.1.0", "glob": "^10.3.10", "klaw-sync": "^6.0.0", "mock-fs": "^5.2.0", "picomatch": "^4.0.2", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.1.0", "recursive-readdir": "^2.2.3", "rrdir": "^12.1.0", "systeminformation": "^5.21.17", "tiny-glob": "^0.2.9", "ts-node": "^10.9.1", "typescript": "^5.3.2", "vitest": "^0.34.6", "walk-sync": "^3.0.0"}, "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "_id": "fdir@6.4.1", "gitHead": "e17009b5bea83ab5cfbefdfbdd595a1332dbce9f", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-upAYvbFc8GU44qWv+r6YnWp3h13/hPFOnpCsuahbxFGmOCm7sc5UT1SytkHLS7Go/UwvhVD9k4dF6PJxpIjqAA==", "shasum": "51a873897b1dbb6a53d59d3c3eb227ce2f69e278", "tarball": "https://registry.npmmirror.com/fdir/-/fdir-6.4.1.tgz", "fileCount": 43, "unpackedSize": 45142, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDjxT+ce7m8YpPBRMkXwrK52xk63+tjA8l84XXSlYhpwAIhAMbxjcLfnugobbjJxjs8xvbImtjnPuLrxwBIS3bJNiF6"}], "size": 12418}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_6.4.1_1729102463665_0.8084182469740953"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-16T18:14:23.917Z", "publish_time": 1729102463917, "_source_registry_name": "default"}, "6.4.2": {"name": "fdir", "version": "6.4.2", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"prepublishOnly": "npm run test && npm run build", "build": "tsc", "test": "vitest run __tests__/", "test:coverage": "vitest run --coverage __tests__/", "test:watch": "vitest __tests__/", "bench": "ts-node benchmarks/benchmark.js", "bench:glob": "ts-node benchmarks/glob-benchmark.ts", "bench:fdir": "ts-node benchmarks/fdir-benchmark.ts", "release": "./scripts/release.sh"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"@types/glob": "^8.1.0", "@types/mock-fs": "^4.13.4", "@types/node": "^20.9.4", "@types/picomatch": "^3.0.0", "@types/tap": "^15.0.11", "@vitest/coverage-v8": "^0.34.6", "all-files-in-tree": "^1.1.2", "benny": "^3.7.1", "csv-to-markdown-table": "^1.3.1", "expect": "^29.7.0", "fast-glob": "^3.3.2", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^4.1.0", "glob": "^10.3.10", "klaw-sync": "^6.0.0", "mock-fs": "^5.2.0", "picomatch": "^4.0.2", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.1.0", "recursive-readdir": "^2.2.3", "rrdir": "^12.1.0", "systeminformation": "^5.21.17", "tiny-glob": "^0.2.9", "ts-node": "^10.9.1", "typescript": "^5.3.2", "vitest": "^0.34.6", "walk-sync": "^3.0.0"}, "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "_id": "fdir@6.4.2", "gitHead": "8288d392337714b0ab881a6eee2dce4629de56fd", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-KnhMXsKSPZlAhp7+IjUkRZKPb4fUyccpDrdFXbi4QL1qkmFh9kVY09Yox+n4MaOb3lHZ1Tv829C3oaaXoMYPDQ==", "shasum": "ddaa7ce1831b161bc3657bb99cb36e1622702689", "tarball": "https://registry.npmmirror.com/fdir/-/fdir-6.4.2.tgz", "fileCount": 43, "unpackedSize": 45174, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE08fZfx7ZOhtxd6O76stLsDBX6fsFb/N3pc9aUNRe5zAiEAq1B8PuNC2/tGfm5gy3ciAknoM1d9m2ZsicJFkqYcLVU="}], "size": 12420}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fdir_6.4.2_1729107288059_0.5800172926488418"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-16T19:34:48.255Z", "publish_time": 1729107288255, "_source_registry_name": "default"}, "6.4.3": {"name": "fdir", "version": "6.4.3", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"prepublishOnly": "npm run test && npm run build", "build": "tsc", "test": "vitest run __tests__/", "test:coverage": "vitest run --coverage __tests__/", "test:watch": "vitest __tests__/", "bench": "ts-node benchmarks/benchmark.js", "bench:glob": "ts-node benchmarks/glob-benchmark.ts", "bench:fdir": "ts-node benchmarks/fdir-benchmark.ts", "release": "./scripts/release.sh"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"@types/glob": "^8.1.0", "@types/mock-fs": "^4.13.4", "@types/node": "^20.9.4", "@types/picomatch": "^3.0.0", "@types/tap": "^15.0.11", "@vitest/coverage-v8": "^0.34.6", "all-files-in-tree": "^1.1.2", "benny": "^3.7.1", "csv-to-markdown-table": "^1.3.1", "expect": "^29.7.0", "fast-glob": "^3.3.2", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^4.1.0", "glob": "^10.3.10", "klaw-sync": "^6.0.0", "mock-fs": "^5.2.0", "picomatch": "^4.0.2", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.1.0", "recursive-readdir": "^2.2.3", "rrdir": "^12.1.0", "systeminformation": "^5.21.17", "tiny-glob": "^0.2.9", "ts-node": "^10.9.1", "typescript": "^5.3.2", "vitest": "^0.34.6", "walk-sync": "^3.0.0"}, "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "_id": "fdir@6.4.3", "gitHead": "1f4eece91127359619c46c359606a6c3294fc5b7", "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-PMXmW2y1hDDfTSRc9gaXIuCCRpuoz3Kaz8cUelp3smouvfT632ozg2vrT6lJsHKKOF59YLbOGfAWGUcKEfRMQw==", "shasum": "011cdacf837eca9b811c89dbb902df714273db72", "tarball": "https://registry.npmmirror.com/fdir/-/fdir-6.4.3.tgz", "fileCount": 43, "unpackedSize": 45205, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAbWvckQNoZzYDFInBTBvziztip+PA+VcfRc0toKka7gAiBte5BTvIasFVqbIUsF3Ab0zmrnr1CyyUjj1w/Ykq//vQ=="}], "size": 12416}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/fdir_6.4.3_1737093245532_0.9672006402884059"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-17T05:54:05.708Z", "publish_time": 1737093245708, "_source_registry_name": "default"}, "6.4.4": {"name": "fdir", "version": "6.4.4", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"prepublishOnly": "npm run test && npm run build", "build": "tsc", "format": "prettier --write src __tests__ benchmarks", "test": "vitest run __tests__/", "test:coverage": "vitest run --coverage __tests__/", "test:watch": "vitest __tests__/", "bench": "ts-node benchmarks/benchmark.js", "bench:glob": "ts-node benchmarks/glob-benchmark.ts", "bench:fdir": "ts-node benchmarks/fdir-benchmark.ts", "release": "./scripts/release.sh"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"@types/glob": "^8.1.0", "@types/mock-fs": "^4.13.4", "@types/node": "^20.9.4", "@types/picomatch": "^3.0.0", "@types/tap": "^15.0.11", "@vitest/coverage-v8": "^0.34.6", "all-files-in-tree": "^1.1.2", "benny": "^3.7.1", "csv-to-markdown-table": "^1.3.1", "expect": "^29.7.0", "fast-glob": "^3.3.2", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^4.1.0", "glob": "^10.3.10", "klaw-sync": "^6.0.0", "mock-fs": "^5.2.0", "picomatch": "^4.0.2", "prettier": "^3.5.3", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.1.0", "recursive-readdir": "^2.2.3", "rrdir": "^12.1.0", "systeminformation": "^5.21.17", "tiny-glob": "^0.2.9", "ts-node": "^10.9.1", "typescript": "^5.3.2", "vitest": "^0.34.6", "walk-sync": "^3.0.0"}, "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "_id": "fdir@6.4.4", "gitHead": "22cc385f065bd229c0e4c608b7549f4c9549bb40", "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-1NZP+GK4GfuAv3PqKvxQRDMjdSRZjnkq7KfhlNrCNNlZ0ygQFpebfrnfnq/W7fpUnAv9aGWmY1zKx7FYL3gwhg==", "shasum": "1cfcf86f875a883e19a8fab53622cfe992e8d2f9", "tarball": "https://registry.npmmirror.com/fdir/-/fdir-6.4.4.tgz", "fileCount": 43, "unpackedSize": 45593, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIA0b7uEOvDjk2ByAb1jQYVnanbpB+ZwnNA+F4IF8jMIJAiAIpC1iobIgmMCmhjXeiXxBg+X3jzvBQR6UGjULz6DwwA=="}], "size": 12544}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/fdir_6.4.4_1745037703743_0.48355881694366376"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-19T04:41:43.916Z", "publish_time": 1745037703916, "_source_registry_name": "default"}, "6.4.5": {"name": "fdir", "version": "6.4.5", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"prepublishOnly": "npm run test && npm run build", "build": "tsc", "format": "prettier --write src __tests__ benchmarks", "test": "vitest run __tests__/", "test:coverage": "vitest run --coverage __tests__/", "test:watch": "vitest __tests__/", "bench": "ts-node benchmarks/benchmark.js", "bench:glob": "ts-node benchmarks/glob-benchmark.ts", "bench:fdir": "ts-node benchmarks/fdir-benchmark.ts", "release": "./scripts/release.sh"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"@types/glob": "^8.1.0", "@types/mock-fs": "^4.13.4", "@types/node": "^20.9.4", "@types/picomatch": "^3.0.0", "@types/tap": "^15.0.11", "@vitest/coverage-v8": "^0.34.6", "all-files-in-tree": "^1.1.2", "benny": "^3.7.1", "csv-to-markdown-table": "^1.3.1", "expect": "^29.7.0", "fast-glob": "^3.3.2", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^4.1.0", "glob": "^10.3.10", "klaw-sync": "^6.0.0", "mock-fs": "^5.2.0", "picomatch": "^4.0.2", "prettier": "^3.5.3", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.1.0", "recursive-readdir": "^2.2.3", "rrdir": "^12.1.0", "systeminformation": "^5.21.17", "tiny-glob": "^0.2.9", "ts-node": "^10.9.1", "typescript": "^5.3.2", "vitest": "^0.34.6", "walk-sync": "^3.0.0"}, "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "_id": "fdir@6.4.5", "gitHead": "7a72642f2caf857c92b99fc462d7909800e26174", "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-4BG7puHpVsIYxZUbiUE3RqGloLaSSwzYie5jvasC4LWuBWzZawynvYouhjbQKw2JuIGYdm0DzIxl8iVidKlUEw==", "shasum": "328e280f3a23699362f95f2e82acf978a0c0cb49", "tarball": "https://registry.npmmirror.com/fdir/-/fdir-6.4.5.tgz", "fileCount": 43, "unpackedSize": 45866, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCICwIvDO2PjwKvLmRGhLwq61U/tMQD4Bo5zfm+jl2FkTrAiBs5pOkB+Hd3kE5UBzmfcyPwIRHEh2Iei3vTOpGF+YN9Q=="}], "size": 12705}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/fdir_6.4.5_1748408830004_0.1969686659385923"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-28T05:07:10.182Z", "publish_time": 1748408830182, "_source_registry_name": "default"}, "6.4.6": {"name": "fdir", "version": "6.4.6", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"prepublishOnly": "npm run test && npm run build", "build": "tsc", "format": "prettier --write src __tests__ benchmarks", "test": "vitest run __tests__/", "test:coverage": "vitest run --coverage __tests__/", "test:watch": "vitest __tests__/", "bench": "ts-node benchmarks/benchmark.js", "bench:glob": "ts-node benchmarks/glob-benchmark.ts", "bench:fdir": "ts-node benchmarks/fdir-benchmark.ts", "release": "./scripts/release.sh"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"@types/glob": "^8.1.0", "@types/mock-fs": "^4.13.4", "@types/node": "^20.9.4", "@types/picomatch": "^3.0.0", "@types/tap": "^15.0.11", "@vitest/coverage-v8": "^0.34.6", "all-files-in-tree": "^1.1.2", "benny": "^3.7.1", "csv-to-markdown-table": "^1.3.1", "expect": "^29.7.0", "fast-glob": "^3.3.2", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^4.1.0", "glob": "^10.3.10", "klaw-sync": "^6.0.0", "mock-fs": "^5.2.0", "picomatch": "^4.0.2", "prettier": "^3.5.3", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.1.0", "recursive-readdir": "^2.2.3", "rrdir": "^12.1.0", "systeminformation": "^5.21.17", "tiny-glob": "^0.2.9", "ts-node": "^10.9.1", "typescript": "^5.3.2", "vitest": "^0.34.6", "walk-sync": "^3.0.0"}, "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "_id": "fdir@6.4.6", "gitHead": "4a8b167170a049b516fe3aed18dfe1f8ef4a1bdf", "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-hiFoqpyZcfNm1yc4u8oWCf9A2c4D3QjCrks3zmoVKVxpQRzmPNar1hUJcBG2RQHvEVGDN+Jm81ZheVLAQMK6+w==", "shasum": "2b268c0232697063111bbf3f64810a2a741ba281", "tarball": "https://registry.npmmirror.com/fdir/-/fdir-6.4.6.tgz", "fileCount": 43, "unpackedSize": 85936, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIDVV2Vgc+1nMOs75Z570AQmzcX9I22RJr5+EzVLpOUr1AiBT5i4ZT9zz3MJCAUpL19NEL1Aibnh51tgI55aKrn+nSg=="}], "size": 19488}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/fdir_6.4.6_1749540054735_0.330254074070808"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-10T07:20:54.902Z", "publish_time": 1749540054902, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "_source_registry_name": "default"}