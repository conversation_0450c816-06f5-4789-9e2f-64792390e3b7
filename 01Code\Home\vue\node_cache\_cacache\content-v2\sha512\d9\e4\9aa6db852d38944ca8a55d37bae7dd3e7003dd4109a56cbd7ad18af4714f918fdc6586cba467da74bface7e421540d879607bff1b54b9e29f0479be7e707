{"_attachments": {}, "_id": "zrender", "_rev": "180-61f1441f830fd08f52a1ca95", "description": "A lightweight graphic library providing 2d draw for Apache ECharts", "dist-tags": {"alpha": "5.0.0-alpha.3", "beta": "5.0.0-beta.2", "latest": "5.6.1", "rc": "6.0.0-rc.1", "v4-stable": "4.3.3"}, "license": "BSD-3-<PERSON><PERSON>", "maintainers": [{"name": "lang", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "otakustay", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}, {"name": "jinz<PERSON>", "email": "<EMAIL>"}, {"name": "ecomfe-admin", "email": "<EMAIL>"}, {"name": "luyuan", "email": "<EMAIL>"}, {"name": "zttonly", "email": "<EMAIL>"}, {"name": "gkiwi001", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fuqiang15", "email": "<EMAIL>"}, {"name": "xdbobname", "email": "<EMAIL>"}], "name": "zrender", "readme": "ZRender\r\n=======\r\n\r\nA lightweight graphic library which provides 2d draw for [Apache ECharts](https://github.com/apache/echarts).\r\n\r\n[![](https://img.shields.io/github/actions/workflow/status/ecomfe/zrender/ci.yml)]() [![](https://img.shields.io/npm/dw/zrender.svg?label=npm%20downloads&style=flat)](https://www.npmjs.com/package/zrender) ![Commits Since 4.0.0](https://img.shields.io/github/commits-since/ecomfe/zrender/4.0.0.svg?colorB=%234c1&style=flat)\r\n\r\n## Documentation\r\n\r\n[https://ecomfe.github.io/zrender-doc/public/](https://ecomfe.github.io/zrender-doc/public/)\r\n\r\n\r\n## License\r\n\r\nBSD 3-Clause License\r\n\r\nCopyright (c) 2017, Baidu Inc.\r\nAll rights reserved.\r\n\r\nRedistribution and use in source and binary forms, with or without\r\nmodification, are permitted provided that the following conditions are met:\r\n\r\n* Redistributions of source code must retain the above copyright notice, this\r\n  list of conditions and the following disclaimer.\r\n\r\n* Redistributions in binary form must reproduce the above copyright notice,\r\n  this list of conditions and the following disclaimer in the documentation\r\n  and/or other materials provided with the distribution.\r\n\r\n* Neither the name of the copyright holder nor the names of its\r\n  contributors may be used to endorse or promote products derived from\r\n  this software without specific prior written permission.\r\n\r\nTHIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\r\nAND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\r\nIMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\r\nDISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE\r\nFOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL\r\nDAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR\r\nSERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER\r\nCAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,\r\nOR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\r\nOF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\r\n\r\n## Notice\r\n\r\nThe Apache Software Foundation [Apache ECharts, ECharts](https://echarts.apache.org/), Apache, the Apache feather, and the Apache ECharts project logo are either registered trademarks or trademarks of the [Apache Software Foundation](https://www.apache.org/).\r\n", "time": {"created": "2022-01-26T12:52:47.800Z", "modified": "2025-06-25T11:29:21.458Z", "5.2.1": "2021-09-17T02:11:06.592Z", "5.2.0": "2021-08-28T08:54:14.067Z", "5.1.1": "2021-06-04T05:27:33.445Z", "5.1.0": "2021-04-12T08:22:30.724Z", "5.0.4": "2021-02-01T12:54:13.276Z", "5.0.3": "2021-01-12T09:44:25.036Z", "5.0.2": "2021-01-12T08:53:21.564Z", "5.0.1": "2020-11-20T15:26:02.148Z", "5.0.0": "2020-11-10T17:28:29.254Z", "5.0.0-rc.1": "2020-11-10T15:31:00.216Z", "5.0.0-beta.2": "2020-10-26T06:52:54.166Z", "5.0.0-beta.1": "2020-10-14T11:44:31.393Z", "5.0.0-alpha.3": "2020-10-14T11:11:49.554Z", "5.0.0-alpha.2": "2020-08-17T17:25:52.742Z", "4.3.2": "2020-08-06T12:51:54.461Z", "5.0.0-alpha.1": "2020-07-31T16:02:57.354Z", "4.3.1": "2020-05-06T09:51:07.919Z", "4.3.0": "2020-03-02T08:06:44.308Z", "4.2.0": "2019-12-12T14:08:49.911Z", "4.1.2": "2019-11-07T14:14:10.286Z", "4.1.1": "2019-09-29T06:04:02.876Z", "4.1.0": "2019-08-30T14:36:45.964Z", "4.0.7": "2019-02-25T08:33:32.484Z", "4.0.6": "2019-01-25T06:51:46.730Z", "4.0.5": "2018-09-15T08:03:56.004Z", "4.0.4": "2018-04-29T15:18:16.953Z", "4.0.3": "2018-02-28T12:41:00.496Z", "4.0.2": "2018-02-28T06:44:09.232Z", "4.0.1": "2018-01-18T07:50:58.972Z", "4.0.0": "2018-01-16T14:17:59.909Z", "3.7.4": "2017-11-14T06:16:04.721Z", "3.7.3": "2017-11-13T12:57:58.458Z", "3.7.2": "2017-11-10T05:20:36.468Z", "3.7.1": "2017-11-10T05:05:00.670Z", "3.7.0": "2017-11-08T12:44:05.970Z", "3.6.3": "2017-09-28T07:53:13.532Z", "3.6.2": "2017-09-28T03:29:16.230Z", "3.6.1": "2017-09-01T11:00:09.115Z", "3.6.0": "2017-08-22T04:34:10.427Z", "3.5.2": "2017-06-15T15:39:57.482Z", "3.5.1": "2017-05-27T02:25:30.205Z", "3.5.0": "2017-05-26T06:35:57.823Z", "3.4.4": "2017-04-27T02:54:34.956Z", "3.4.3": "2017-04-14T07:43:39.210Z", "3.4.2": "2017-04-05T11:12:10.436Z", "3.4.1": "2017-03-27T13:48:21.864Z", "3.4.0": "2017-03-26T13:18:31.350Z", "3.3.0": "2017-01-13T05:15:30.728Z", "3.2.2": "2016-11-24T08:30:47.953Z", "3.2.1": "2016-11-03T08:15:35.266Z", "3.2.0": "2016-11-01T08:33:12.664Z", "3.1.3": "2016-08-17T05:09:18.669Z", "3.1.2": "2016-07-12T12:24:21.453Z", "3.1.1": "2016-06-30T15:28:26.532Z", "3.1.0": "2016-05-19T07:43:30.595Z", "3.0.9": "2016-05-11T08:04:42.033Z", "3.0.8": "2016-04-22T06:48:39.899Z", "3.0.7": "2016-04-11T05:55:58.679Z", "3.0.6": "2016-03-29T06:27:22.266Z", "3.0.5": "2016-03-21T07:02:19.891Z", "3.0.4": "2016-03-10T08:58:32.674Z", "3.0.3": "2016-03-01T12:30:05.110Z", "3.0.2": "2016-02-22T12:06:04.547Z", "3.0.1": "2016-01-29T02:50:53.693Z", "3.0.0": "2016-01-14T06:48:27.921Z", "2.0.2": "2014-08-29T06:10:04.848Z", "5.3.0": "2022-01-23T06:43:48.468Z", "5.3.1": "2022-03-02T06:04:13.010Z", "5.3.2": "2022-06-09T08:01:20.063Z", "5.4.0": "2022-09-13T09:09:29.835Z", "5.4.1": "2022-11-28T05:28:01.314Z", "5.4.2": "2023-03-09T09:36:56.073Z", "5.4.3": "2023-03-10T06:50:33.819Z", "5.4.4": "2023-07-12T08:56:54.698Z", "5.5.0": "2024-01-30T04:38:43.354Z", "5.6.0": "2024-06-17T14:41:22.848Z", "4.3.3": "2024-12-06T15:08:51.688Z", "5.6.1": "2024-12-11T03:11:54.227Z", "6.0.0-rc.1": "2025-06-25T10:58:55.927Z"}, "versions": {"5.2.1": {"name": "zrender", "version": "5.2.1", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "npm run release", "build": "npm run build:bundle && npm run build:lib", "release": "node build/build.js --minify && npm run build:lib", "prepare:nightly": "node build/prepareNightly.js", "prepare:nightly-next": "node build/prepareNightly.js --next", "build:bundle": "node build/build.js", "build:lib": "npx tsc -m ES2015 --outDir lib", "watch:bundle": "node build/build.js --watch", "watch:lib": "npx tsc -w -m ES2015 --outDir lib", "test": "npx jest --config test/ut/jest.config.js", "lint": "npx eslint src/**/*.ts"}, "license": "BSD-3-<PERSON><PERSON>", "types": "index.d.ts", "module": "index.js", "main": "dist/zrender.js", "dependencies": {"tslib": "2.3.0"}, "sideEffects": ["lib/canvas/canvas.js", "lib/svg/svg.js"], "devDependencies": {"@microsoft/api-extractor": "^7.7.2", "@types/jest": "^25.1.2", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.9.1", "chalk": "^3.0.0", "commander": "2.11.0", "eslint": "6.3.0", "fs-extra": "4.0.2", "jest": "^25.1.0", "jsdom": "^16.0.0", "rollup": "^1.28.0", "rollup-plugin-typescript2": "^0.25.3", "rollup-plugin-uglify": "^6.0.4", "ts-jest": "^25.2.0", "typescript": "4.3.5", "uglify-js": "^3.10.0"}, "gitHead": "0fbf9b30e0b3fbc1f630d22725510ebec367f24d", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@5.2.1", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "_npmUser": {"name": "lang", "email": "<EMAIL>"}, "dist": {"shasum": "5f4bbda915ba6d412b0b19dc2431beaad05417bb", "size": 961905, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-5.2.1.tgz", "integrity": "sha512-M3b<PERSON><PERSON>ZuyLTNBC6LiNKXJwSCtglMp8XUEqEBG+2MdICDI3d1s500Y4P0CzldQGsqpRVB7fkvf3BKQQRxsEaTlsw=="}, "directories": {}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_5.2.1_1631844666204_0.4526598809579825"}, "_hasShrinkwrap": false, "publish_time": 1631844666592, "_cnpm_publish_time": 1631844666592, "_cnpmcore_publish_time": "2021-12-16T16:48:00.394Z"}, "5.2.0": {"name": "zrender", "version": "5.2.0", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "npm run release", "build": "npm run build:bundle && npm run build:lib", "release": "node build/build.js --minify && npm run build:lib", "prepare:nightly": "node build/prepareNightly.js", "build:bundle": "node build/build.js", "build:lib": "npx tsc -m ES2015 --outDir lib", "watch:bundle": "node build/build.js --watch", "watch:lib": "npx tsc -w -m ES2015 --outDir lib", "test": "npx jest --config test/ut/jest.config.js", "lint": "npx eslint src/**/*.ts"}, "license": "BSD-3-<PERSON><PERSON>", "types": "index.d.ts", "module": "index.js", "main": "dist/zrender.js", "dependencies": {"tslib": "2.3.0"}, "sideEffects": ["lib/canvas/canvas.js", "lib/svg/svg.js"], "devDependencies": {"@microsoft/api-extractor": "^7.7.2", "@types/jest": "^25.1.2", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.9.1", "chalk": "^3.0.0", "commander": "2.11.0", "eslint": "6.3.0", "fs-extra": "4.0.2", "jest": "^25.1.0", "jsdom": "^16.0.0", "rollup": "^1.28.0", "rollup-plugin-typescript2": "^0.25.3", "rollup-plugin-uglify": "^6.0.4", "ts-jest": "^25.2.0", "typescript": "4.3.5", "uglify-js": "^3.10.0"}, "gitHead": "68878cfeac19496b3d7915c632202c586caf71f1", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@5.2.0", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "f8abc484ac4a8a51b04c3ccd37beabe1def342cd", "size": 966738, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-5.2.0.tgz", "integrity": "sha512-87v3gvB0lcWy48ObA/DwrhQ95ADMMRhECVrXmHDFCBNvbxHFfEDZtrZh4VmVjLAeFAjimY4PyZ65rbLCivdszA=="}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_5.2.0_1630140853771_0.9620653490021089"}, "_hasShrinkwrap": false, "publish_time": 1630140854067, "_cnpm_publish_time": 1630140854067, "_cnpmcore_publish_time": "2021-12-16T16:48:02.200Z"}, "5.1.1": {"name": "zrender", "version": "5.1.1", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "npm run release", "build": "npm run build:bundle && npm run build:lib", "release": "node build/build.js --minify && npm run build:lib", "prepare:nightly": "node build/prepareNightly.js", "build:bundle": "node build/build.js", "build:lib": "npx tsc -m ES2015 --outDir lib", "watch:bundle": "node build/build.js --watch", "watch:lib": "npx tsc -w -m ES2015 --outDir lib", "test": "npx jest --config test/ut/jest.config.js"}, "license": "BSD-3-<PERSON><PERSON>", "types": "index.d.ts", "module": "index.js", "main": "dist/zrender.js", "dependencies": {"tslib": "2.0.3"}, "sideEffects": ["lib/canvas/canvas.js", "lib/svg/svg.js"], "devDependencies": {"@microsoft/api-extractor": "^7.7.2", "@types/jest": "^25.1.2", "@typescript-eslint/eslint-plugin": "^2.24.0", "@typescript-eslint/parser": "^2.24.0", "chalk": "^3.0.0", "commander": "2.11.0", "eslint": "6.3.0", "fs-extra": "4.0.2", "jest": "^25.1.0", "jsdom": "^16.0.0", "rollup": "^1.28.0", "rollup-plugin-typescript2": "^0.25.3", "rollup-plugin-uglify": "^6.0.4", "ts-jest": "^25.2.0", "typescript": "^4.1.2", "uglify-js": "^3.10.0"}, "gitHead": "659887953255f5d17034c6b7c50988fa5200d577", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@5.1.1", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "0515f4f8cc0f4742f02a6b8819550a6d13d64c5c", "size": 939441, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-5.1.1.tgz", "integrity": "sha512-oeWlmUZPQdS9f5hK4pV21tHPqA3wgQ7CkKkw7l0CCBgWlJ/FP+lRgLFtUBW6yam4JX8y9CdHJo1o587VVrbcoQ=="}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_5.1.1_1622784453230_0.7127714693023854"}, "_hasShrinkwrap": false, "publish_time": 1622784453445, "_cnpm_publish_time": 1622784453445, "_cnpmcore_publish_time": "2021-12-16T16:48:04.670Z"}, "5.1.0": {"name": "zrender", "version": "5.1.0", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "npm run release", "build": "npm run build:bundle && npm run build:lib", "release": "node build/build.js --minify && npm run build:lib", "build:bundle": "node build/build.js", "build:lib": "npx tsc -m ES2015 --outDir lib", "watch:bundle": "node build/build.js --watch", "watch:lib": "npx tsc -w -m ES2015 --outDir lib", "test": "npx jest --config test/ut/jest.config.js"}, "license": "BSD-3-<PERSON><PERSON>", "types": "index.d.ts", "module": "index.js", "main": "dist/zrender.js", "dependencies": {"tslib": "2.0.3"}, "sideEffects": ["lib/canvas/canvas.js", "lib/svg/svg.js"], "devDependencies": {"@microsoft/api-extractor": "^7.7.2", "@types/jest": "^25.1.2", "@typescript-eslint/eslint-plugin": "^2.24.0", "@typescript-eslint/parser": "^2.24.0", "chalk": "^3.0.0", "commander": "2.11.0", "eslint": "6.3.0", "fs-extra": "4.0.2", "jest": "^25.1.0", "jsdom": "^16.0.0", "rollup": "^1.28.0", "rollup-plugin-typescript2": "^0.25.3", "rollup-plugin-uglify": "^6.0.4", "ts-jest": "^25.2.0", "typescript": "^4.1.2", "uglify-js": "^3.10.0"}, "gitHead": "30a3fd8cae0af842f25a6f2dfae28c3ea7748f6c", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@5.1.0", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "b6a84c3aa7ccc6642ee0519901ca4c0835c4d85e", "size": 935239, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-5.1.0.tgz", "integrity": "sha512-c+8VRx52ycbmqwHeHLlo/BAfIHBl/JZNLM6cfDQFgzIH05yb+f5J9F/fbRsP+zGc8dW9XHuhdt8/iqukgMZSeg=="}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_5.1.0_1618215750499_0.07190120701220981"}, "_hasShrinkwrap": false, "publish_time": 1618215750724, "_cnpm_publish_time": 1618215750724, "_cnpmcore_publish_time": "2021-12-16T16:48:05.981Z"}, "5.0.4": {"name": "zrender", "version": "5.0.4", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "npm run release", "build": "npm run build:bundle && npm run build:lib", "release": "node build/build.js --minify && npm run build:lib", "build:bundle": "node build/build.js", "build:lib": "npx tsc -m ES2015 --outDir lib", "watch:bundle": "node build/build.js --watch", "watch:lib": "npx tsc -w -m ES2015 --outDir lib", "test": "npx jest --config test/ut/jest.config.js"}, "license": "BSD-3-<PERSON><PERSON>", "types": "index.d.ts", "module": "index.js", "main": "dist/zrender.js", "dependencies": {"tslib": "2.0.3"}, "sideEffects": ["lib/canvas/canvas.js", "lib/svg/svg.js"], "devDependencies": {"@microsoft/api-extractor": "^7.7.2", "@types/jest": "^25.1.2", "@typescript-eslint/eslint-plugin": "^2.24.0", "@typescript-eslint/parser": "^2.24.0", "chalk": "^3.0.0", "commander": "2.11.0", "eslint": "6.3.0", "fs-extra": "4.0.2", "jest": "^25.1.0", "jsdom": "^16.0.0", "rollup": "^1.28.0", "rollup-plugin-typescript2": "^0.25.3", "rollup-plugin-uglify": "^6.0.4", "ts-jest": "^25.2.0", "typescript": "^4.1.2", "uglify-js": "^3.10.0"}, "gitHead": "4023b585c94b698fc13e36efd09a6b26ea6718ed", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@5.0.4", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "89c355af908b9f64a301b38f751b7951f2c8a95a", "size": 925340, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-5.0.4.tgz", "integrity": "sha512-DJpy0yrHYY5CuH6vhb9IINWbjvBUe/56J8aH86Jb7O8rRPAYZ3M2E469Qf5B3EOIfM3o3aUrO5edRQfLJ+l1Qw=="}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_5.0.4_1612184053059_0.06834924833511669"}, "_hasShrinkwrap": false, "publish_time": 1612184053276, "_cnpm_publish_time": 1612184053276, "_cnpmcore_publish_time": "2021-12-16T16:48:07.719Z"}, "5.0.3": {"name": "zrender", "version": "5.0.3", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "npm run release", "build": "npm run build:bundle && npm run build:lib", "release": "node build/build.js --minify && npm run build:lib", "build:bundle": "node build/build.js", "build:lib": "npx tsc -m ES2015 --outDir lib", "watch:bundle": "node build/build.js --watch", "watch:lib": "npx tsc -w -m ES2015 --outDir lib", "test": "npx jest --config test/ut/jest.config.js"}, "license": "BSD-3-<PERSON><PERSON>", "types": "index.d.ts", "module": "index.js", "main": "dist/zrender.js", "dependencies": {"tslib": "2.0.3"}, "sideEffects": ["lib/canvas/canvas.js", "lib/svg/svg.js"], "devDependencies": {"@microsoft/api-extractor": "^7.7.2", "@types/jest": "^25.1.2", "@typescript-eslint/eslint-plugin": "^2.24.0", "@typescript-eslint/parser": "^2.24.0", "chalk": "^3.0.0", "commander": "2.11.0", "eslint": "6.3.0", "fs-extra": "4.0.2", "jest": "^25.1.0", "jsdom": "^16.0.0", "rollup": "^1.28.0", "rollup-plugin-typescript2": "^0.25.3", "rollup-plugin-uglify": "^6.0.4", "ts-jest": "^25.2.0", "typescript": "^4.1.2", "uglify-js": "^3.10.0"}, "gitHead": "a2a6c26f39843a2e0bbd36e32c27aee6c72c2bb2", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@5.0.3", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "5e04a55d36455067e4536c8ce9c25093f50f6b68", "size": 974309, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-5.0.3.tgz", "integrity": "sha512-TVcN2IMdo7je3GEq/E4CER4AGBe/n50/izILdupppyHf/hVHuiXCRliqdu8+32Z1OmGg6RfKt5qQlkX+bOtU0g=="}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_5.0.3_1610444664826_0.7067611968726637"}, "_hasShrinkwrap": false, "publish_time": 1610444665036, "_cnpm_publish_time": 1610444665036, "_cnpmcore_publish_time": "2021-12-16T16:48:09.303Z"}, "5.0.2": {"name": "zrender", "version": "5.0.2", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "npm run release", "build": "npm run build:bundle && npm run build:lib", "release": "node build/build.js --minify && npm run build:lib", "build:bundle": "node build/build.js", "build:lib": "npx tsc -m ES2015 --outDir lib", "watch:bundle": "node build/build.js --watch", "watch:lib": "npx tsc -w -m ES2015 --outDir lib", "test": "npx jest --config test/ut/jest.config.js"}, "license": "BSD-3-<PERSON><PERSON>", "types": "index.d.ts", "module": "index.js", "main": "dist/zrender.js", "dependencies": {"tslib": "2.0.3"}, "sideEffects": ["lib/canvas/canvas.js", "lib/svg/svg.js"], "devDependencies": {"@microsoft/api-extractor": "^7.7.2", "@types/jest": "^25.1.2", "@typescript-eslint/eslint-plugin": "^2.24.0", "@typescript-eslint/parser": "^2.24.0", "chalk": "^3.0.0", "commander": "2.11.0", "eslint": "6.3.0", "fs-extra": "4.0.2", "jest": "^25.1.0", "jsdom": "^16.0.0", "rollup": "^1.28.0", "rollup-plugin-typescript2": "^0.25.3", "rollup-plugin-uglify": "^6.0.4", "ts-jest": "^25.2.0", "typescript": "^4.1.2", "uglify-js": "^3.10.0"}, "gitHead": "37b950b34ea02148629363e2856fc7c9186a475c", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@5.0.2", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "4910f2507bf729f43c4fc4b128d2537ddb3762e1", "size": 974302, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-5.0.2.tgz", "integrity": "sha512-IkTkA2gKgnuYWm/uTdYhsxYkI6SF4AaYznvfl4higUG/T06dMtgWcGgi93ayqjyRU1i4YXnDDOUqLBdGP8/yzw=="}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_5.0.2_1610441601328_0.2920738361390771"}, "_hasShrinkwrap": false, "publish_time": 1610441601564, "_cnpm_publish_time": 1610441601564, "_cnpmcore_publish_time": "2021-12-16T16:48:11.575Z"}, "5.0.1": {"name": "zrender", "version": "5.0.1", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "npm run release", "build": "npm run build:bundle && npm run build:cjs && npm run build:esm", "release": "node build/build.js --minify && npm run build:cjs && npm run build:esm", "build:bundle": "node build/build.js", "build:cjs": "npx tsc -m CommonJS --outDir lib", "build:esm": "npx tsc -m ES2015 --outDir esm", "watch:bundle": "node build/build.js --watch", "watch:esm": "npx tsc -w -m ES2015 --outDir esm", "watch:cjs": "npx tsc -w -m CommonJS --outDir lib", "test": "npx jest --config test/ut/jest.config.js"}, "license": "BSD-3-<PERSON><PERSON>", "types": "index.d.ts", "module": "index.esm.js", "main": "index.js", "dependencies": {"tslib": "1.10.0"}, "devDependencies": {"@microsoft/api-extractor": "^7.7.2", "@types/jest": "^25.1.2", "@typescript-eslint/eslint-plugin": "^2.24.0", "@typescript-eslint/parser": "^2.24.0", "chalk": "^3.0.0", "commander": "2.11.0", "eslint": "6.3.0", "fs-extra": "4.0.2", "jest": "^25.1.0", "jsdom": "^16.0.0", "rollup": "^1.28.0", "rollup-plugin-typescript2": "^0.25.3", "rollup-plugin-uglify": "^6.0.4", "ts-jest": "^25.2.0", "typescript": "3.8.3", "uglify-js": "^3.10.0"}, "gitHead": "bbb230d57668b6d8952663b3c9408d0d98e90fbd", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@5.0.1", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "cc377136a9d00d0a177ca7f65c32aed2901928da", "size": 981924, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-5.0.1.tgz", "integrity": "sha512-i8FNCKAKfF0EfZFJ6w2p30umBrCyy481/PePFQqPdtNgCl5Hp5z7/dovqb7soEoFkhNvhjJ/J4W9zFALeae6yA=="}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_5.0.1_1605885961623_0.5199243821407309"}, "_hasShrinkwrap": false, "publish_time": 1605885962148, "_cnpm_publish_time": 1605885962148, "_cnpmcore_publish_time": "2021-12-16T16:48:13.590Z"}, "5.0.0": {"name": "zrender", "version": "5.0.0", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "npm run release", "build": "npm run build:bundle && npm run build:cjs && npm run build:esm", "release": "node build/build.js --minify && npm run build:cjs && npm run build:esm", "build:bundle": "node build/build.js", "build:cjs": "npx tsc -m CommonJS --outDir lib", "build:esm": "npx tsc -m ES2015 --outDir esm", "watch:bundle": "node build/build.js --watch", "watch:esm": "npx tsc -w -m ES2015 --outDir esm", "watch:cjs": "npx tsc -w -m CommonJS --outDir lib", "test": "npx jest --config test/ut/jest.config.js"}, "license": "BSD-3-<PERSON><PERSON>", "types": "index.d.ts", "module": "index.esm.js", "main": "index.js", "dependencies": {"tslib": "1.10.0"}, "devDependencies": {"@microsoft/api-extractor": "^7.7.2", "@types/jest": "^25.1.2", "@typescript-eslint/eslint-plugin": "^2.24.0", "@typescript-eslint/parser": "^2.24.0", "chalk": "^3.0.0", "commander": "2.11.0", "eslint": "6.3.0", "fs-extra": "4.0.2", "jest": "^25.1.0", "jsdom": "^16.0.0", "rollup": "^1.28.0", "rollup-plugin-typescript2": "^0.25.3", "rollup-plugin-uglify": "^6.0.4", "ts-jest": "^25.2.0", "typescript": "3.8.3", "uglify-js": "^3.10.0"}, "gitHead": "1bd13a996e22ffebbdca39dd2859a96110ba4882", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@5.0.0", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "a859fb89151811e813252359a0ce5ee6ff9425a6", "size": 980676, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-5.0.0.tgz", "integrity": "sha512-bOHqJkAKQOEbEXNNSbHUrXQybeOjR3lXyHebSn7LYVvXmXaYQHEaVwT6q2AItNl18EdMBcMrrZPgmLM8ZgJMjg=="}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_5.0.0_1605029309011_0.8526407524531496"}, "_hasShrinkwrap": false, "publish_time": 1605029309254, "_cnpm_publish_time": 1605029309254, "_cnpmcore_publish_time": "2021-12-16T16:48:15.647Z"}, "5.0.0-rc.1": {"name": "zrender", "version": "5.0.0-rc.1", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "npm run release", "build": "npm run build:bundle && npm run build:cjs && npm run build:esm", "release": "node build/build.js --minify && npm run build:cjs && npm run build:esm", "build:bundle": "node build/build.js", "build:cjs": "npx tsc -m CommonJS --outDir lib", "build:esm": "npx tsc -m ES2015 --outDir esm", "watch:bundle": "node build/build.js --watch", "watch:esm": "npx tsc -w -m ES2015 --outDir esm", "watch:cjs": "npx tsc -w -m CommonJS --outDir lib", "test": "npx jest --config test/ut/jest.config.js"}, "license": "BSD-3-<PERSON><PERSON>", "types": "index.d.ts", "module": "index.esm.js", "main": "index.js", "dependencies": {"tslib": "1.10.0"}, "devDependencies": {"@microsoft/api-extractor": "^7.7.2", "@types/jest": "^25.1.2", "@typescript-eslint/eslint-plugin": "^2.24.0", "@typescript-eslint/parser": "^2.24.0", "chalk": "^3.0.0", "commander": "2.11.0", "eslint": "6.3.0", "fs-extra": "4.0.2", "jest": "^25.1.0", "jsdom": "^16.0.0", "rollup": "^1.28.0", "rollup-plugin-typescript2": "^0.25.3", "rollup-plugin-uglify": "^6.0.4", "ts-jest": "^25.2.0", "typescript": "3.8.3", "uglify-js": "^3.10.0"}, "readmeFilename": "README.md", "gitHead": "b4982c54cbe638b95357cbd96089334f3a5a9a56", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@5.0.0-rc.1", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "40481a2622d56be40177aaff80d38e9f045960a2", "size": 980862, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-5.0.0-rc.1.tgz", "integrity": "sha512-Q9bhWDDSdd4cYGtBcf5eTshbBUacaRBGT2G8t68a+gTloQ4Xe/Qx4O3wzx9Dh5Dpum6sYGSCr4o+Y5roc9ikjw=="}, "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_5.0.0-rc.1_1605022259896_0.25048877710272377"}, "_hasShrinkwrap": false, "publish_time": 1605022260216, "_cnpm_publish_time": 1605022260216, "_cnpmcore_publish_time": "2021-12-16T16:48:17.387Z"}, "5.0.0-beta.2": {"name": "zrender", "version": "5.0.0-beta.2", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "npm run release", "build": "npm run build:bundle && npm run build:cjs && npm run build:esm", "release": "node build/build.js --minify && npm run build:cjs && npm run build:esm", "build:bundle": "node build/build.js", "build:cjs": "npx tsc -m CommonJS --outDir lib", "build:esm": "npx tsc -m ES2015 --outDir esm", "watch:bundle": "node build/build.js --watch", "watch:esm": "npx tsc -w -m ES2015 --outDir esm", "watch:cjs": "npx tsc -w -m CommonJS --outDir lib", "test": "npx jest --config test/ut/jest.config.js"}, "license": "BSD-3-<PERSON><PERSON>", "types": "index.d.ts", "module": "index.esm.js", "main": "index.js", "dependencies": {"tslib": "1.10.0"}, "devDependencies": {"@microsoft/api-extractor": "^7.7.2", "@types/jest": "^25.1.2", "@typescript-eslint/eslint-plugin": "^2.24.0", "@typescript-eslint/parser": "^2.24.0", "chalk": "^3.0.0", "commander": "2.11.0", "eslint": "6.3.0", "fs-extra": "4.0.2", "jest": "^25.1.0", "jsdom": "^16.0.0", "rollup": "^1.28.0", "rollup-plugin-typescript2": "^0.25.3", "rollup-plugin-uglify": "^6.0.4", "ts-jest": "^25.2.0", "typescript": "3.8.3", "uglify-js": "^3.10.0"}, "readmeFilename": "README.md", "gitHead": "ee45fa26e6d8d9ae6b64364d499f0a3d05fe1cbb", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@5.0.0-beta.2", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "0932cd2a50f580472add1ae902a66935440c4697", "size": 1029211, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-5.0.0-beta.2.tgz", "integrity": "sha512-VClfQCYzpjR9nDs3A1jYcHNPeg5tjNlhV8he9CHLQTK8lz1ZMthwBASxwfli4AK2J9muwuLQ9XXRmEWV5ebARQ=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_5.0.0-beta.2_1603695174003_0.3436321428232445"}, "_hasShrinkwrap": false, "publish_time": 1603695174166, "_cnpm_publish_time": 1603695174166, "_cnpmcore_publish_time": "2021-12-16T16:48:18.975Z"}, "5.0.0-beta.1": {"name": "zrender", "version": "5.0.0-beta.1", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "npm run release", "build": "npm run build:bundle && npm run build:cjs && npm run build:esm", "release": "node build/build.js --minify && npm run build:cjs && npm run build:esm", "build:bundle": "node build/build.js", "build:cjs": "npx tsc -m CommonJS --outDir lib", "build:esm": "npx tsc -m ES2015 --outDir esm", "watch:bundle": "node build/build.js --watch", "watch:esm": "npx tsc -w -m ES2015 --outDir esm", "watch:cjs": "npx tsc -w -m CommonJS --outDir lib", "test": "npx jest --config test/ut/jest.config.js"}, "license": "BSD-3-<PERSON><PERSON>", "types": "index.d.ts", "module": "index.esm.js", "main": "index.js", "dependencies": {"tslib": "1.10.0"}, "devDependencies": {"@microsoft/api-extractor": "^7.7.2", "@types/jest": "^25.1.2", "@typescript-eslint/eslint-plugin": "^2.24.0", "@typescript-eslint/parser": "^2.24.0", "chalk": "^3.0.0", "commander": "2.11.0", "eslint": "6.3.0", "fs-extra": "4.0.2", "jest": "^25.1.0", "jsdom": "^16.0.0", "rollup": "^1.28.0", "rollup-plugin-typescript2": "^0.25.3", "rollup-plugin-uglify": "^6.0.4", "ts-jest": "^25.2.0", "typescript": "3.8.3", "uglify-js": "^3.10.0"}, "readmeFilename": "README.md", "gitHead": "d7812615f7b058b8393de2681ec1fc8dc5424c67", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@5.0.0-beta.1", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "900b567d8b84febc9be262e3ae85f97bd14df60f", "size": 978673, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-5.0.0-beta.1.tgz", "integrity": "sha512-MZRgR/6LqIQ00TzAuAU08wQ0HzsBRVmrcBQXVgursk/Gy/CG64+li2fepaFbTOHGvjQA2VPZ4mxrNmkJnBZxyA=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_5.0.0-beta.1_1602675871177_0.8372744228312583"}, "_hasShrinkwrap": false, "publish_time": 1602675871393, "_cnpm_publish_time": 1602675871393, "_cnpmcore_publish_time": "2021-12-16T16:48:20.446Z"}, "5.0.0-alpha.3": {"name": "zrender", "version": "5.0.0-alpha.3", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "npm run release", "build": "npm run build:bundle && npm run build:cjs && npm run build:esm", "release": "node build/build.js --minify && npm run build:cjs && npm run build:esm", "build:bundle": "node build/build.js", "build:cjs": "npx tsc -m CommonJS --outDir lib", "build:esm": "npx tsc -m ES2015 --outDir esm", "watch:bundle": "node build/build.js --watch", "watch:esm": "npx tsc -w -m ES2015 --outDir esm", "watch:cjs": "npx tsc -w -m CommonJS --outDir lib", "test": "npx jest --config test/ut/jest.config.js"}, "license": "BSD-3-<PERSON><PERSON>", "types": "index.d.ts", "module": "index.esm.js", "main": "index.js", "dependencies": {"tslib": "1.10.0"}, "devDependencies": {"@microsoft/api-extractor": "^7.7.2", "@types/jest": "^25.1.2", "@typescript-eslint/eslint-plugin": "^2.24.0", "@typescript-eslint/parser": "^2.24.0", "chalk": "^3.0.0", "commander": "2.11.0", "eslint": "6.3.0", "fs-extra": "4.0.2", "jest": "^25.1.0", "jsdom": "^16.0.0", "rollup": "^1.28.0", "rollup-plugin-typescript2": "^0.25.3", "rollup-plugin-uglify": "^6.0.4", "ts-jest": "^25.2.0", "typescript": "3.8.3", "uglify-js": "^3.10.0"}, "gitHead": "6735c20d08ab9f1e505488a27f7a849cb749b8e2", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@5.0.0-alpha.3", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "66cde1db598eaef19db0fee74a09a212cd0c1683", "size": 978673, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-5.0.0-alpha.3.tgz", "integrity": "sha512-TL7mkFzpJUxeT60lLBNYNhhxyMGNOO8pFUQXUf9XDyA0NIqJ4lD6Mm/jBaesmqvonl7aHYehnILkMQQbsb2NPw=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_5.0.0-alpha.3_1602673909339_0.07693782158734819"}, "_hasShrinkwrap": false, "publish_time": 1602673909554, "_cnpm_publish_time": 1602673909554, "_cnpmcore_publish_time": "2021-12-16T16:48:22.621Z"}, "5.0.0-alpha.2": {"name": "zrender", "version": "5.0.0-alpha.2", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "npm run release", "build": "npm run build:bundle && npm run build:cjs && npm run build:esm", "release": "node build/build.js --minify && npm run build:cjs && npm run build:esm", "build:bundle": "node build/build.js", "build:cjs": "npx tsc -m CommonJS --outDir lib", "build:esm": "npx tsc -m ES2015 --outDir esm", "watch:bundle": "node build/build.js --watch", "watch:esm": "npx tsc -w -m ES2015 --outDir esm", "watch:cjs": "npx tsc -w -m CommonJS --outDir lib", "test": "npx jest --config test/ut/jest.config.js"}, "license": "BSD-3-<PERSON><PERSON>", "types": "index.d.ts", "module": "index.esm.js", "main": "index.js", "dependencies": {"tslib": "1.10.0"}, "devDependencies": {"@microsoft/api-extractor": "^7.7.2", "@types/jest": "^25.1.2", "@typescript-eslint/eslint-plugin": "^2.24.0", "@typescript-eslint/parser": "^2.24.0", "chalk": "^3.0.0", "commander": "2.11.0", "eslint": "6.3.0", "fs-extra": "4.0.2", "jest": "^25.1.0", "jsdom": "^16.0.0", "rollup": "^1.28.0", "rollup-plugin-typescript2": "^0.25.3", "rollup-plugin-uglify": "^6.0.4", "ts-jest": "^25.2.0", "typescript": "3.8.3", "uglify-js": "^3.10.0"}, "readmeFilename": "README.md", "gitHead": "26b4336b2a956bf6b6104f59547c19e07c986177", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@5.0.0-alpha.2", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "e694e6b0b83ca94ce8baf4b412de17bb35370915", "size": 938974, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-5.0.0-alpha.2.tgz", "integrity": "sha512-QZHAsZGO6gHnDBrhr9G4jGvO6jmeLzwkOcqpqyqUPHz59fIvht55Yn1Z3vEhjjr7HTq5LfuHUAh30eYQsg5YGg=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_5.0.0-alpha.2_1597685152439_0.5029444057030539"}, "_hasShrinkwrap": false, "publish_time": 1597685152742, "_cnpm_publish_time": 1597685152742, "_cnpmcore_publish_time": "2021-12-16T16:48:24.705Z"}, "4.3.2": {"name": "zrender", "version": "4.3.2", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/build.js --prepublish", "build": "node build/build.js --release", "dev": "node build/build.js --watch", "test": "node build/build.js", "lint": "eslint src"}, "license": "BSD-3-<PERSON><PERSON>", "devDependencies": {"@babel/core": "7.3.4", "@babel/helper-module-transforms": "7.2.2", "@babel/helper-simple-access": "7.1.0", "@babel/template": "7.2.2", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "fs-extra": "4.0.2", "jsdiff": "1.1.1", "rollup": "0.50.0", "rollup-plugin-uglify": "2.0.1", "eslint": "6.3.0"}, "gitHead": "3e6cddaf132add09d77594466961bc4a22394fbd", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@4.3.2", "_nodeVersion": "10.16.3", "_npmVersion": "6.9.0", "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "dist": {"shasum": "ec7432f9415c82c73584b6b7b8c47e1b016209c6", "size": 792816, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-4.3.2.tgz", "integrity": "sha512-bIusJLS8c4DkIcdiK+s13HiQ/zjQQVgpNohtd8d94Y2DnJqgM1yjh/jpDb8DoL6hd7r8Awagw8e3qK/oLaWr3g=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_4.3.2_1596718314265_0.35983284847924035"}, "_hasShrinkwrap": false, "publish_time": 1596718314461, "_cnpm_publish_time": 1596718314461, "_cnpmcore_publish_time": "2021-12-16T16:48:26.364Z"}, "5.0.0-alpha.1": {"name": "zrender", "version": "5.0.0-alpha.1", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "npm run release", "build": "npm run build:bundle && npm run build:cjs && npm run build:esm", "release": "node build/build.js --minify && npm run build:cjs && npm run build:esm", "build:bundle": "node build/build.js", "build:cjs": "npx tsc -m CommonJS --outDir lib", "build:esm": "npx tsc -m ES2015 --outDir esm", "watch:bundle": "node build/build.js --watch", "watch:esm": "npx tsc -w -m ES2015 --outDir esm", "watch:cjs": "npx tsc -w -m CommonJS --outDir lib", "test": "npx jest --config test/ut/jest.config.js"}, "license": "BSD-3-<PERSON><PERSON>", "types": "index.d.ts", "module": "index.esm.js", "main": "index.js", "dependencies": {"tslib": "^1.10.0"}, "devDependencies": {"@microsoft/api-extractor": "^7.7.2", "@types/jest": "^25.1.2", "@typescript-eslint/eslint-plugin": "^2.24.0", "@typescript-eslint/parser": "^2.24.0", "chalk": "^3.0.0", "commander": "2.11.0", "eslint": "6.3.0", "fs-extra": "4.0.2", "jest": "^25.1.0", "jsdom": "^16.0.0", "rollup": "^1.28.0", "rollup-plugin-typescript2": "^0.25.3", "rollup-plugin-uglify": "^6.0.4", "ts-jest": "^25.2.0", "typescript": "3.8.3", "uglify-js": "^3.10.0"}, "readmeFilename": "README.md", "gitHead": "33029c8d4a8680f39d8001910750c0e37ced4351", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@5.0.0-alpha.1", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "fefcd113c29f340196781accf84dc7f683c584df", "size": 937478, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-5.0.0-alpha.1.tgz", "integrity": "sha512-4iGc4DRCI526gxAOEEPPRAwAHDZamXqvJhs/O/dd2R8vPUvaBxo/8NOmqVuWZXQEHo2+XZg++PPUsvWq6fZ/3A=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_5.0.0-alpha.1_1596211376994_0.2185040580930082"}, "_hasShrinkwrap": false, "publish_time": 1596211377354, "_cnpm_publish_time": 1596211377354, "_cnpmcore_publish_time": "2021-12-16T16:48:28.911Z"}, "4.3.1": {"name": "zrender", "version": "4.3.1", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/build.js --prepublish", "build": "node build/build.js --release", "dev": "node build/build.js --watch", "test": "node build/build.js", "lint": "eslint src"}, "license": "BSD-3-<PERSON><PERSON>", "devDependencies": {"@babel/core": "7.3.4", "@babel/helper-module-transforms": "7.2.2", "@babel/helper-simple-access": "7.1.0", "@babel/template": "7.2.2", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "fs-extra": "4.0.2", "jsdiff": "1.1.1", "rollup": "0.50.0", "rollup-plugin-uglify": "2.0.1", "eslint": "6.3.0"}, "gitHead": "d4e1bdb1c4197d54de9effe799d8a2baf1dd9a11", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@4.3.1", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "baf8aa6dc8187a2f819692d7d5f9bedfa2b90fa3", "size": 731864, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-4.3.1.tgz", "integrity": "sha512-CeH2TpJeCdG0TAGYoPSAcFX2ogdug1K7LIn9UO/q9HWqQ54gWhrMAlDP9AwWYMUDhrPe4VeazQ4DW3msD96nUQ=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_4.3.1_1588758667629_0.9139403405262168"}, "_hasShrinkwrap": false, "publish_time": 1588758667919, "_cnpm_publish_time": 1588758667919, "_cnpmcore_publish_time": "2021-12-16T16:48:29.844Z"}, "4.3.0": {"name": "zrender", "version": "4.3.0", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/build.js --prepublish", "build": "node build/build.js --release", "dev": "node build/build.js --watch", "test": "node build/build.js", "lint": "eslint src"}, "license": "BSD-3-<PERSON><PERSON>", "devDependencies": {"@babel/core": "7.3.4", "@babel/helper-module-transforms": "7.2.2", "@babel/helper-simple-access": "7.1.0", "@babel/template": "7.2.2", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "fs-extra": "4.0.2", "jsdiff": "1.1.1", "rollup": "0.50.0", "rollup-plugin-uglify": "2.0.1", "eslint": "6.3.0"}, "gitHead": "3013da256df26011a4b70ca16b1ac421e611748c", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@4.3.0", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "9f056121b20bbae44414d287bf6a119ff7042661", "size": 729797, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-4.3.0.tgz", "integrity": "sha512-Dii6j2bDsPkxQayuVf2DXJeruIB/mKVxxcGRZQ9GExiBd4c3w7+oBuvo1O/JGHeFeA1nCmSDVDs/S7yKZG1nrA=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_4.3.0_1583136404085_0.09420958416973746"}, "_hasShrinkwrap": false, "publish_time": 1583136404308, "_cnpm_publish_time": 1583136404308, "_cnpmcore_publish_time": "2021-12-16T16:48:31.317Z"}, "4.2.0": {"name": "zrender", "version": "4.2.0", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/build.js --prepublish", "build": "node build/build.js --release", "dev": "node build/build.js --watch", "test": "node build/build.js", "lint": "eslint src"}, "license": "BSD-3-<PERSON><PERSON>", "devDependencies": {"@babel/core": "7.3.4", "@babel/helper-module-transforms": "7.2.2", "@babel/helper-simple-access": "7.1.0", "@babel/template": "7.2.2", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "fs-extra": "4.0.2", "jsdiff": "1.1.1", "rollup": "0.50.0", "rollup-plugin-uglify": "2.0.1", "eslint": "6.3.0"}, "gitHead": "11572b1aae4907868956efce960e07813d72fc85", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@4.2.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "dist": {"shasum": "d001302e155f28de1f9fc7fcd5c254bad28471cf", "size": 780205, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-4.2.0.tgz", "integrity": "sha512-YJ9hxt5uFincYYU3KK31+Ce+B6PJmYYK0Q9fQ6jOUAoC/VHbe4kCKAPkxKeT7jGTxrK5wYu18R0TLGqj2zbEOA=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_4.2.0_1576159729665_0.9957131814827704"}, "_hasShrinkwrap": false, "publish_time": 1576159729911, "_cnpm_publish_time": 1576159729911, "_cnpmcore_publish_time": "2021-12-16T16:48:33.715Z"}, "4.1.2": {"name": "zrender", "version": "4.1.2", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/build.js --prepublish", "build": "node build/build.js --release", "dev": "node build/build.js --watch", "test": "node build/build.js", "lint": "eslint src"}, "license": "BSD-3-<PERSON><PERSON>", "devDependencies": {"@babel/core": "7.3.4", "@babel/helper-module-transforms": "7.2.2", "@babel/helper-simple-access": "7.1.0", "@babel/template": "7.2.2", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "fs-extra": "4.0.2", "jsdiff": "1.1.1", "rollup": "0.50.0", "rollup-plugin-uglify": "2.0.1", "eslint": "6.3.0"}, "gitHead": "991809bdcbfa0c55c100d0227379a215cd567ee9", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@4.1.2", "_npmVersion": "5.6.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "dist": {"shasum": "8368deff24c7e237cbcbd3a2ff93017905ae43f7", "size": 763759, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-4.1.2.tgz", "integrity": "sha512-MJYEo1ZOVesjxYsfcGtPXnUREmh4ACMV08QZLGZ3S7D1xOd96iz3O6nf6pv5PHb5NSHkbizr7ChSIgtAGwncvA=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_4.1.2_1573136050075_0.5308955592012841"}, "_hasShrinkwrap": false, "publish_time": 1573136050286, "_cnpm_publish_time": 1573136050286, "_cnpmcore_publish_time": "2021-12-16T16:48:35.122Z"}, "4.1.1": {"name": "zrender", "version": "4.1.1", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/build.js --prepublish", "build": "node build/build.js --release", "dev": "node build/build.js --watch", "test": "node build/build.js", "lint": "eslint src"}, "license": "BSD-3-<PERSON><PERSON>", "devDependencies": {"@babel/core": "7.3.4", "@babel/helper-module-transforms": "7.2.2", "@babel/helper-simple-access": "7.1.0", "@babel/template": "7.2.2", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "fs-extra": "4.0.2", "jsdiff": "1.1.1", "rollup": "0.50.0", "rollup-plugin-uglify": "2.0.1", "eslint": "6.3.0"}, "gitHead": "f19acf6d4841db63181395650601143b7aa2a054", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@4.1.1", "_npmVersion": "5.6.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "dist": {"shasum": "201e58a55cf1a7e973e1dbad3c15596ef8779f54", "size": 761659, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-4.1.1.tgz", "integrity": "sha512-epy1rl82dP/JKxhZl+JRfwcZjobKOoynxBzvBZJBPjmDMAxBE4grLjDryx8BHkHlKyWsUkis4XEL2wN61w5l4w=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_4.1.1_1569737042702_0.0703751428200936"}, "_hasShrinkwrap": false, "publish_time": 1569737042876, "_cnpm_publish_time": 1569737042876, "_cnpmcore_publish_time": "2021-12-16T16:48:36.962Z"}, "4.1.0": {"name": "zrender", "version": "4.1.0", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/build.js --prepublish", "build": "node build/build.js --release", "dev": "node build/build.js --watch", "test": "node build/build.js"}, "license": "BSD-3-<PERSON><PERSON>", "devDependencies": {"@babel/core": "7.3.4", "@babel/helper-module-transforms": "7.2.2", "@babel/helper-simple-access": "7.1.0", "@babel/template": "7.2.2", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "fs-extra": "4.0.2", "jsdiff": "1.1.1", "rollup": "0.50.0", "rollup-plugin-uglify": "2.0.1"}, "gitHead": "e06ee839dc3578647470258660744f00c7489e3d", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@4.1.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.4.1", "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "dist": {"shasum": "866ab97d0744c473d9b314ac5a89e7207c140608", "size": 761158, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-4.1.0.tgz", "integrity": "sha512-PkWDpBSRIZAKyBBSkizWCP5/bIL5s/+W+FM/AwkivN4m4QCVH04PS0nJmBB/E6CnUNQv9ZPfIV4tthg1qEP1gg=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_4.1.0_1567175805747_0.7486902046834945"}, "_hasShrinkwrap": false, "publish_time": 1567175805964, "_cnpm_publish_time": 1567175805964, "_cnpmcore_publish_time": "2021-12-16T16:48:38.864Z"}, "4.0.7": {"name": "zrender", "version": "4.0.7", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/build.js --prepublish", "build": "node build/build.js --release", "dev": "node build/build.js --watch"}, "license": "BSD", "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "fs-extra": "4.0.2", "jsdiff": "1.1.1", "rollup": "0.50.0", "rollup-plugin-uglify": "2.0.1"}, "gitHead": "d5a4349ed9cfe608a04b65977c72120b57e50d4c", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@4.0.7", "_npmVersion": "6.1.0", "_nodeVersion": "10.4.1", "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "dist": {"shasum": "15ae960822f5efed410995d37e5107fe3de10e6d", "size": 746554, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-4.0.7.tgz", "integrity": "sha512-TNloHe0ums6zxbHfnaCryM61J4IWDajZwNq6dHk9vfWhhysO/OeFvvR0drBs/nbXha2YxSzfQj2FiCd6RVBe+Q=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_4.0.7_1551083612315_0.6372871266856852"}, "_hasShrinkwrap": false, "publish_time": 1551083612484, "_cnpm_publish_time": 1551083612484, "_cnpmcore_publish_time": "2021-12-16T16:48:40.115Z"}, "4.0.6": {"name": "zrender", "version": "4.0.6", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/build.js --prepublish", "build": "node build/build.js --release", "dev": "node build/build.js --watch"}, "license": "BSD", "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "fs-extra": "4.0.2", "jsdiff": "1.1.1", "rollup": "0.50.0", "rollup-plugin-uglify": "2.0.1"}, "gitHead": "5b7fc9fb365d4ad55b7292e058fe96d7417e0952", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@4.0.6", "_nodeVersion": "10.10.0", "_npmVersion": "6.7.0", "dist": {"shasum": "cb4f681c09e63692d11053d366b8f89e6537cc69", "size": 746515, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-4.0.6.tgz", "integrity": "sha512-uGR+vMdFyhP/qki8TRVOa3UbFlHFDdrA26v/6tX7D2cqSaCbdF4Hlng0ufU62R0j+ZkhLGGnFZ0VmWEVK9XyWA=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_4.0.6_1548399106576_0.9344403093478701"}, "_hasShrinkwrap": false, "publish_time": 1548399106730, "_cnpm_publish_time": 1548399106730, "_cnpmcore_publish_time": "2021-12-16T16:48:41.848Z"}, "4.0.5": {"name": "zrender", "version": "4.0.5", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/build.js --prepublish", "build": "node build/build.js --release", "dev": "node build/build.js --watch"}, "license": "BSD", "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "fs-extra": "4.0.2", "jsdiff": "1.1.1", "rollup": "0.50.0", "rollup-plugin-uglify": "2.0.1"}, "gitHead": "aa4b8d65691e4daca2a466190fb2bf491d94bfcd", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@4.0.5", "_npmVersion": "6.1.0", "_nodeVersion": "10.4.1", "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "dist": {"shasum": "6e8f738971ce2cd624aac82b2156729b1c0e5a82", "size": 737576, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-4.0.5.tgz", "integrity": "sha512-SintgipGEJPT9Sz2ABRoE4ZD7Yzy7oR7j7KP6H+C9FlbHWnLUfGVK7E8UV27pGwlxAMB0EsnrqhXx5XjAfv/KA=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_4.0.5_1536998635707_0.23726889236100446"}, "_hasShrinkwrap": false, "publish_time": 1536998636004, "_cnpm_publish_time": 1536998636004, "_cnpmcore_publish_time": "2021-12-16T16:48:43.356Z"}, "4.0.4": {"name": "zrender", "version": "4.0.4", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/build.js --prepublish", "build": "node build/build.js --release", "dev": "node build/build.js --watch"}, "license": "BSD", "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "fs-extra": "4.0.2", "jsdiff": "1.1.1", "rollup": "0.50.0", "rollup-plugin-uglify": "2.0.1"}, "gitHead": "fdd600891646644d215fcae015d3ebeb71e695f3", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@4.0.4", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "ecomfe-core", "email": "<EMAIL>"}, "dist": {"shasum": "910e60d888f00c9599073f23758dd23345fe48fd", "size": 697386, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-4.0.4.tgz", "integrity": "sha512-03Vd/BDl/cPXp8E61f5+Xbgr/a4vDyFA+uUtUc1s+5KgcPbyY2m+78R/9LQwkR6QwFYHG8qk25Q8ESGs/qpkZw=="}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_4.0.4_1525015096750_0.872410165393215"}, "_hasShrinkwrap": false, "publish_time": 1525015096953, "_cnpm_publish_time": 1525015096953, "_cnpmcore_publish_time": "2021-12-16T16:48:44.571Z"}, "4.0.3": {"name": "zrender", "version": "4.0.3", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/build.js --prepublish", "build": "node build/build.js --release", "dev": "node build/build.js --watch"}, "license": "BSD", "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "fs-extra": "4.0.2", "jsdiff": "1.1.1", "rollup": "0.50.0", "rollup-plugin-uglify": "2.0.1"}, "gitHead": "56e1ffc302fb3896b9a84280458265ffe87df3a6", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@4.0.3", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "801ef001101750a61857f84e64c4d5fb3c88cca7", "size": 696765, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-4.0.3.tgz", "integrity": "sha512-LdkntRaNogzKAwlICuS0wdZcYaeA94llQ0SWqsgbcd6SPasgkjstaoe6vr5P9Pd2ID/rlhf3UrmIuFzqOLdDuA=="}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_4.0.3_1519821660325_0.5500512314343446"}, "_hasShrinkwrap": false, "publish_time": 1519821660496, "_cnpm_publish_time": 1519821660496, "_cnpmcore_publish_time": "2021-12-16T16:48:45.962Z"}, "4.0.2": {"name": "zrender", "version": "4.0.2", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/build.js --prepublish", "build": "node build/build.js --release", "dev": "node build/build.js --watch"}, "license": "BSD", "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "fs-extra": "4.0.2", "jsdiff": "1.1.1", "rollup": "0.50.0", "rollup-plugin-uglify": "2.0.1"}, "gitHead": "0b04615d31b8455d6a900cda857e28f1b3f444ae", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@4.0.2", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "d09e6e5d236e20ddc319436b0f00f6499f3a0024", "size": 695513, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-4.0.2.tgz", "integrity": "sha512-TPaK8bHopnj1o0y6xoS9saFk0lKS88UkofZtX6l5opuBpZ84qAZ59QWColOeDcRLL/r/GJ0Ve3yG/4kxWpJBRg=="}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_4.0.2_1519800249049_0.15208650585112515"}, "_hasShrinkwrap": false, "publish_time": 1519800249232, "_cnpm_publish_time": 1519800249232, "_cnpmcore_publish_time": "2021-12-16T16:48:48.180Z"}, "4.0.1": {"name": "zrender", "version": "4.0.1", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/build.js --prepublish", "build": "node build/build.js --release", "dev": "node build/build.js --watch"}, "license": "BSD", "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "fs-extra": "4.0.2", "jsdiff": "1.1.1", "rollup": "0.50.0", "rollup-plugin-uglify": "2.0.1"}, "gitHead": "fab4ff14bd2d74dc01543962a6f9b7c8ee1f52db", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@4.0.1", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "961e77abc5042eaa624b5230f8d673bf648298e9", "size": 690973, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-4.0.1.tgz", "integrity": "sha512-VC9bTAZwd5OVgryysxBgNvIZuwBsRJbIDkSyK9PAk7e9wE/M0k4R3eylVYc1IAcDXvccSFnj4/NkzCteh/YSpA=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender-4.0.1.tgz_1516261858770_0.47240659897215664"}, "directories": {}, "publish_time": 1516261858972, "_hasShrinkwrap": false, "_cnpm_publish_time": 1516261858972, "_cnpmcore_publish_time": "2021-12-16T16:48:50.047Z"}, "4.0.0": {"name": "zrender", "version": "4.0.0", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/build.js --prepublish", "build": "node build/build.js --release", "dev": "node build/build.js --watch"}, "license": "BSD", "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "fs-extra": "4.0.2", "jsdiff": "1.1.1", "rollup": "0.50.0", "rollup-plugin-uglify": "2.0.1"}, "gitHead": "5bc0b16f9072660f6c775dbd3ee5646c4cd59746", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@4.0.0", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "908935151329fa95145a333d186edd4011cf86c5", "size": 690892, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-4.0.0.tgz", "integrity": "sha512-yb9gbnjg7BoMn5D01fX1hj788/CQgyIt+byUYdbgTZTz9TOpBy/b3m0Rx+TDT1kjfQIWWhE+HOkaAwJxbcCt1A=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender-4.0.0.tgz_1516112279722_0.6665770020335913"}, "directories": {}, "publish_time": 1516112279909, "_hasShrinkwrap": false, "_cnpm_publish_time": 1516112279909, "_cnpmcore_publish_time": "2021-12-16T16:48:51.158Z"}, "3.7.4": {"name": "zrender", "version": "3.7.4", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/build.js --prepublish"}, "license": "BSD", "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "fs-extra": "4.0.2", "jsdiff": "1.1.1", "rollup": "0.50.0", "rollup-plugin-uglify": "2.0.1"}, "gitHead": "7b4bc39ef6cdec10a5a9b3d622bd6a1ec5908768", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@3.7.4", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "f847d53948481ef6d42906d1ea9aeec7acbefdf2", "size": 679295, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.7.4.tgz", "integrity": "sha512-5Nz7+L1wIoL0+Pp/iOP56jD6eD017qC9VRSgUBheXBiAHgOBJZ4uh4/g6e83acIwa8RKSyZf/FlceKu5ntUuxQ=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender-3.7.4.tgz_1510640164567_0.268104882678017"}, "directories": {}, "publish_time": 1510640164721, "_hasShrinkwrap": false, "_cnpm_publish_time": 1510640164721, "_cnpmcore_publish_time": "2021-12-16T16:48:53.736Z"}, "3.7.3": {"name": "zrender", "version": "3.7.3", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/build.js --prepublish"}, "license": "BSD", "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "fs-extra": "4.0.2", "jsdiff": "1.1.1", "rollup": "0.50.0", "rollup-plugin-uglify": "2.0.1"}, "gitHead": "a3824e5f653204c835fdea9edc364ba415621717", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@3.7.3", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "4d8e643aa18054d177afcf75d89b9e4ad44134c3", "size": 581269, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.7.3.tgz", "integrity": "sha512-ujXpz/TW0IoVLp/ufOLbrrXQU9TZsxn5wTvINkWf8pHzUGmFkgh9bMUj0BteGgnJnNJDL0FxNV690P/y+rYJOg=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender-3.7.3.tgz_1510577878312_0.11788009549491107"}, "directories": {}, "publish_time": 1510577878458, "_hasShrinkwrap": false, "_cnpm_publish_time": 1510577878458, "_cnpmcore_publish_time": "2021-12-16T16:48:55.288Z"}, "3.7.2": {"name": "zrender", "version": "3.7.2", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/build.js --prepublish"}, "license": "BSD", "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "fs-extra": "4.0.2", "jsdiff": "1.1.1", "rollup": "0.50.0", "rollup-plugin-uglify": "2.0.1"}, "gitHead": "c7370464e73cebd0b2cb851c292f5ed760c7ca6f", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@3.7.2", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "2d296158f267ea8bf818822459f6bb331ce6984d", "size": 580378, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.7.2.tgz", "integrity": "sha512-UWKsQ15yMsORWMY1jRm9r+dlvJLl1OHidy9Fs6E6+DW0t73PObkilg6V/80XQqHuYRjUzDu615RGAddDItjAUw=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender-3.7.2.tgz_1510291236266_0.7806371601764113"}, "directories": {}, "publish_time": 1510291236468, "_hasShrinkwrap": false, "_cnpm_publish_time": 1510291236468, "_cnpmcore_publish_time": "2021-12-16T16:48:56.829Z"}, "3.7.1": {"name": "zrender", "version": "3.7.1", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "rm -r lib; cp -r src lib"}, "license": "BSD", "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "commander": "2.11.0", "fs-extra": "4.0.2", "jsdiff": "1.1.1", "rollup": "0.50.0", "rollup-plugin-uglify": "2.0.1"}, "gitHead": "fae82399fccf76a2c75e779f3f8539e394e30e6c", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@3.7.1", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "46596e0e5dcf0a487b4fa8e625ded60481d4f8e2", "size": 584412, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.7.1.tgz", "integrity": "sha512-wS9wsfZXq5F3eDbh/BRLa9c2kohQQgshrEIZ4i4JkXjvyW0982IRafBGoLMw+v7mcStyhL3G5fY6SqSRmEDw1g=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender-3.7.1.tgz_1510290300555_0.10878228023648262"}, "directories": {}, "publish_time": 1510290300670, "_hasShrinkwrap": false, "_cnpm_publish_time": 1510290300670, "_cnpmcore_publish_time": "2021-12-16T16:48:57.818Z"}, "3.7.0": {"name": "zrender", "version": "3.7.0", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "rm -r lib; cp -r src lib"}, "license": "BSD", "devDependencies": {"commander": "^2.11.0", "fs-extra": "^4.0.2", "jsdiff": "^1.1.1", "rollup": "^0.50.0", "rollup-plugin-uglify": "^2.0.1"}, "gitHead": "28dbe1da48c24d95372ccfcec93d030de76b5547", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@3.7.0", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "490e773b4765f076eb5a6247f19e9659da233d0f", "size": 583393, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.7.0.tgz", "integrity": "sha512-SbZGdZ3/uMWmLLFwAdbP2SWkhjFBghNoh5Yi28vwE2pyPULMC6+q8AG/8+Rce9UUwsm7ckD5O8ho1FgWE/oW9Q=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender-3.7.0.tgz_1510145045837_0.8386789963115007"}, "directories": {}, "publish_time": 1510145045970, "_hasShrinkwrap": false, "_cnpm_publish_time": 1510145045970, "_cnpmcore_publish_time": "2021-12-16T16:48:59.338Z"}, "3.6.3": {"name": "zrender", "version": "3.6.3", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "rm -r lib; node build/amd2common.bundle.js"}, "license": "BSD", "devDependencies": {"jsdiff": "^1.1.1", "rollup": "^0.50.0", "rollup-plugin-commonjs": "^8.2.1", "rollup-plugin-node-resolve": "^3.0.0"}, "gitHead": "ef1b394e2a01f18caf8964c5062e19fe093731d2", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@3.6.3", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "e3a06ba105cc45eb36492a818e7b7ae879a7b15b", "size": 419116, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.6.3.tgz", "integrity": "sha512-SSIkN+Xzs3yyH582Sr8Uo9CkQS4oP23CYiKsmcNOqgfvlJwaHxZeY2IHYGa1FgzZlBeh+UcYg9Wk/eGD16uQ8A=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender-3.6.3.tgz_1506585193347_0.3740454448852688"}, "directories": {}, "publish_time": 1506585193532, "_hasShrinkwrap": false, "_cnpm_publish_time": 1506585193532, "_cnpmcore_publish_time": "2021-12-16T16:49:00.183Z"}, "3.6.2": {"name": "zrender", "version": "3.6.2", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "license": "BSD", "devDependencies": {"jsdiff": "^1.1.1", "rollup": "^0.50.0", "rollup-plugin-commonjs": "^8.2.1", "rollup-plugin-node-resolve": "^3.0.0"}, "gitHead": "7379c1e6bf164211a404187d84661fae36a3af15", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@3.6.2", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "0880a503f57805cd262516071c92cb277997fa50", "size": 420085, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.6.2.tgz", "integrity": "sha512-diSmyh7sBITQEJ914Unc7NtNtyEwxjrmIBYZySmaFz6LKE43+8iQCAoj4p5wHurP2iodjM8YwB2Y920bMdx5KQ=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender-3.6.2.tgz_1506569355704_0.983733115484938"}, "directories": {}, "publish_time": 1506569356230, "_hasShrinkwrap": false, "_cnpm_publish_time": 1506569356230, "_cnpmcore_publish_time": "2021-12-16T16:49:01.198Z"}, "3.6.1": {"name": "zrender", "version": "3.6.1", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "license": "BSD", "main": "lib/zrender.js", "gitHead": "f0aa73bea98a4058d42f10903d8a5833837f843f", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@3.6.1", "_shasum": "362e8c4eb6c970a8d97c84daa8d65f37921b826b", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.3", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "362e8c4eb6c970a8d97c84daa8d65f37921b826b", "size": 248546, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.6.1.tgz", "integrity": "sha512-K00n6QyOE7U88WLwUsLUifsR/x5bfSdJCHV5yt5HQaPbLmWn+MiyoOSwNyuKVkUNkuBYgjs6cjyFoklNaolumA=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender-3.6.1.tgz_1504263608889_0.040896031307056546"}, "directories": {}, "publish_time": 1504263609115, "_hasShrinkwrap": false, "_cnpm_publish_time": 1504263609115, "_cnpmcore_publish_time": "2021-12-16T16:49:02.173Z"}, "3.6.0": {"name": "zrender", "version": "3.6.0", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "license": "BSD", "main": "lib/zrender.js", "gitHead": "aa65bf6d1b4318e720e500f25bfd2c98d90135d3", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@3.6.0", "_shasum": "b8f4de98200c2c90785b90695680a1f12b4c71ba", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.3", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "b8f4de98200c2c90785b90695680a1f12b4c71ba", "size": 245546, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.6.0.tgz", "integrity": "sha512-3PjZ07Fq26pyE5BWWPAKIvaSTpnCS1V/KOc2clS23uGmgU7mOJ0go7L+fRbpkf7DYMX99AxlPNmU8GGKntdaAw=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender-3.6.0.tgz_1503376450036_0.9072136064060032"}, "directories": {}, "publish_time": 1503376450427, "_hasShrinkwrap": false, "_cnpm_publish_time": 1503376450427, "_cnpmcore_publish_time": "2021-12-16T16:49:03.126Z"}, "3.5.2": {"name": "zrender", "version": "3.5.2", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "license": "BSD", "main": "lib/zrender.js", "gitHead": "9f9c50d447e24852d513e384ba6914baefadfe3c", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@3.5.2", "_shasum": "e770cbe978b5f4981c1b93d9b84147a3474f21d2", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.3", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "e770cbe978b5f4981c1b93d9b84147a3474f21d2", "size": 220858, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.5.2.tgz", "integrity": "sha512-HzEjJIWfbLf2sCQhlsAox7BQknvHQgrNGhGHrlttbQB+SzpM0LcnSZIH9N1H+9u5JOo27VKEDi1zO2qY7PdjGQ=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender-3.5.2.tgz_1497541197346_0.20446437667123973"}, "directories": {}, "publish_time": 1497541197482, "_hasShrinkwrap": false, "_cnpm_publish_time": 1497541197482, "_cnpmcore_publish_time": "2021-12-16T16:49:03.919Z"}, "3.5.1": {"name": "zrender", "version": "3.5.1", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "license": "BSD", "main": "lib/zrender.js", "gitHead": "4de8ebcd1d9e9bd4b4e33b7021b6afe33fc23b93", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@3.5.1", "_shasum": "70494096f017684c714ddd8bfa24cda6feecec34", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.3", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "70494096f017684c714ddd8bfa24cda6feecec34", "size": 220535, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.5.1.tgz", "integrity": "sha512-xxW++qoKVfvHCx5nhnAHho2ycT9+6vvN23Eb5BT6T8L1AuczQU2rIqpdh9MuP8tgjeVGHv1VfEmHRrkk+jbntQ=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender-3.5.1.tgz_1495851930091_0.7041672468185425"}, "directories": {}, "publish_time": 1495851930205, "_hasShrinkwrap": false, "_cnpm_publish_time": 1495851930205, "_cnpmcore_publish_time": "2021-12-16T16:49:04.924Z"}, "3.5.0": {"name": "zrender", "version": "3.5.0", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "license": "BSD", "main": "lib/zrender.js", "gitHead": "1089fbe075146d1c264b6267d3256ee09e64f894", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@3.5.0", "_shasum": "096fb72a40edb615f7f443aa71e300875befb1c5", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.3", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "096fb72a40edb615f7f443aa71e300875befb1c5", "size": 220402, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.5.0.tgz", "integrity": "sha512-g/SO7dgTl1rodput4uF7o6dexta68SU+o/dF1v2ie93XBxCrPG3isKOU7h6kUDWmdW1ro7KQFsnFgHAWiyXgCg=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender-3.5.0.tgz_1495780557529_0.6023243456147611"}, "directories": {}, "publish_time": 1495780557823, "_hasShrinkwrap": false, "_cnpm_publish_time": 1495780557823, "_cnpmcore_publish_time": "2021-12-16T16:49:05.615Z"}, "3.4.4": {"name": "zrender", "version": "3.4.4", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "license": "BSD", "main": "lib/zrender.js", "gitHead": "0434d55cb63cba0ce53d8f72f363cff5e28e2379", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "_id": "zrender@3.4.4", "_shasum": "ed011f6d05c7f7a472b03480436fd449bc3bc59f", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.2", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "ed011f6d05c7f7a472b03480436fd449bc3bc59f", "size": 219713, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.4.4.tgz", "integrity": "sha512-IfUTT5aOAYq373eyrOpM/UidaUZvLxaOCI1rVmj7yWSSsT9TUAaILWBxwFmVqNooDENLDRX8Pesas+KU23pUkQ=="}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/zrender-3.4.4.tgz_1493261671552_0.8900265193078667"}, "directories": {}, "publish_time": 1493261674956, "_hasShrinkwrap": false, "_cnpm_publish_time": 1493261674956, "_cnpmcore_publish_time": "2021-12-16T16:49:06.130Z"}, "3.4.3": {"name": "zrender", "version": "3.4.3", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "license": "BSD", "main": "lib/zrender.js", "gitHead": "60f249bea824c7d3cee0459c9e2b7387d690b325", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "_id": "zrender@3.4.3", "_shasum": "acb9561cefa98fa8f919232ba0d95585cb51aa2e", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "acb9561cefa98fa8f919232ba0d95585cb51aa2e", "size": 219502, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.4.3.tgz", "integrity": "sha512-<PERSON>bkiEy8lek4Bj80T2JKgel8Enk6QrTV4vV4ySXrQmD8jftP9iCl2hDoNVwRFP0j56/ZHEmjH6Nquw5CPdNzYGQ=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/zrender-3.4.3.tgz_1492155818982_0.**********7289567"}, "directories": {}, "publish_time": 1492155819210, "_hasShrinkwrap": false, "_cnpm_publish_time": 1492155819210, "_cnpmcore_publish_time": "2021-12-16T16:49:06.625Z"}, "3.4.2": {"name": "zrender", "version": "3.4.2", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "license": "BSD", "main": "lib/zrender.js", "gitHead": "66e17bdde477491ccfa9809a659daa5bdec75368", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "_id": "zrender@3.4.2", "_shasum": "7286ef00e640d9686b63892eb2087bcffd1b7ea1", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "7286ef00e640d9686b63892eb2087bcffd1b7ea1", "size": 218199, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.4.2.tgz", "integrity": "sha512-Y38iTpJDHJkUypcedCkic3ozzLvvDMYgPf9N01aT2znvoxcA99GiupOoicW+be3j+L7o123m6ZG2BvUVZfrQpw=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/zrender-3.4.2.tgz_1491390730201_0.5780440343078226"}, "directories": {}, "publish_time": 1491390730436, "_hasShrinkwrap": false, "_cnpm_publish_time": 1491390730436, "_cnpmcore_publish_time": "2021-12-16T16:49:07.243Z"}, "3.4.1": {"name": "zrender", "version": "3.4.1", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "license": "BSD", "main": "lib/zrender.js", "gitHead": "c0f3b3d86993ee2b369d1425a62cf9cec4b385cc", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "_id": "zrender@3.4.1", "_shasum": "fee1d6bc092adf15625ab4d16182b04096f8cdc2", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "fee1d6bc092adf15625ab4d16182b04096f8cdc2", "size": 218218, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.4.1.tgz", "integrity": "sha512-APtxq1XYzAVJR1MKx7shuow1yc+TQl23pfRYEMzC/0J/mYFWAEwDbcaTLl6lUbDSbbTm8e//cwSCyTcXgWo6nA=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/zrender-3.4.1.tgz_1490622501587_0.4828250757418573"}, "directories": {}, "publish_time": 1490622501864, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490622501864, "_cnpmcore_publish_time": "2021-12-16T16:49:08.098Z"}, "3.4.0": {"name": "zrender", "version": "3.4.0", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "license": "BSD", "main": "lib/zrender.js", "gitHead": "b49ce1a74ace09872c4c5171035362d3875c11c3", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "_id": "zrender@3.4.0", "_shasum": "aec7694e3adbb50b03535dc6a632eb212d01edc9", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "aec7694e3adbb50b03535dc6a632eb212d01edc9", "size": 218232, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.4.0.tgz", "integrity": "sha512-Bz7A0omboO4EdtThmIdOo77Y/iXSOYgSPdwaiYnK5oWo/9bia+oGvXoPXrGgQhkZVPQuOelFRz/YAPi0VKca6A=="}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/zrender-3.4.0.tgz_1490534309169_0.46451811818405986"}, "directories": {}, "publish_time": 1490534311350, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490534311350, "_cnpmcore_publish_time": "2021-12-16T16:49:08.739Z"}, "3.3.0": {"name": "zrender", "version": "3.3.0", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "license": "BSD", "main": "lib/zrender.js", "gitHead": "b9972f2d7957a608ad607a2b2d9f6064acbec26e", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "_id": "zrender@3.3.0", "_shasum": "efb5280b248aa48e3bba8d8f8e5469033a355d26", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "efb5280b248aa48e3bba8d8f8e5469033a355d26", "size": 214403, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.3.0.tgz", "integrity": "sha512-J4aLE38XD5Tk467Q3wMe1SI4HiK9c5IbKFxPkOSD+LV94hc4rKxq2pLqw8iDzUOOQ/UMujhNvIqJghw1llgaJg=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/zrender-3.3.0.tgz_1484284530486_0.8396436371840537"}, "directories": {}, "publish_time": 1484284530728, "_hasShrinkwrap": false, "_cnpm_publish_time": 1484284530728, "_cnpmcore_publish_time": "2021-12-16T16:49:09.417Z"}, "3.2.2": {"name": "zrender", "version": "3.2.2", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "license": "BSD", "main": "lib/zrender.js", "gitHead": "c755dced512cdbd23846ec69f29bae112478c7a4", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "_id": "zrender@3.2.2", "_shasum": "b33d250456859944a48c4f3550986168a0952ec5", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "b33d250456859944a48c4f3550986168a0952ec5", "size": 211067, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.2.2.tgz", "integrity": "sha512-eeiSTfHJOrXYwfr/+74w5PrL+YPcgmusjp1KMUxLfAZUnrPKe+DBJ0/qmOfxZDo+1hmNdzXneVCN6pBe73QYLA=="}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/zrender-3.2.2.tgz_1479976245825_0.2358754752203822"}, "directories": {}, "publish_time": 1479976247953, "_hasShrinkwrap": false, "_cnpm_publish_time": 1479976247953, "_cnpmcore_publish_time": "2021-12-16T16:49:10.022Z"}, "3.2.1": {"name": "zrender", "version": "3.2.1", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "license": "BSD", "main": "lib/zrender.js", "gitHead": "1e6efe532ca3a188b0286a08944978f3ff1a67de", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "_id": "zrender@3.2.1", "_shasum": "67f2ab8953b4822b6ffe3387dfd066053fa9753a", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "67f2ab8953b4822b6ffe3387dfd066053fa9753a", "size": 209053, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.2.1.tgz", "integrity": "sha512-u8lx9c79xLtSc3FXsEG2SDnh4gn81rNhVznJHMPu09yTF+MfD64lq3Vh7RNxtyCuXsEyKNTs8+n3tLxcl5jjVA=="}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/zrender-3.2.1.tgz_1478160933199_0.7468213436659425"}, "directories": {}, "publish_time": 1478160935266, "_hasShrinkwrap": false, "_cnpm_publish_time": 1478160935266, "_cnpmcore_publish_time": "2021-12-16T16:49:10.610Z"}, "3.2.0": {"name": "zrender", "version": "3.2.0", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "license": "BSD", "main": "lib/zrender.js", "gitHead": "83fe64b2b4e1d085536f28ca012a42319c9ee61e", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "_id": "zrender@3.2.0", "_shasum": "1f59ce72c6bc8b0efbef876f37c5d299bc23c844", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "1f59ce72c6bc8b0efbef876f37c5d299bc23c844", "size": 208973, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.2.0.tgz", "integrity": "sha512-p+z8b8HfUHFo+YeQA+iPSNeYgxRMt7LurzGM9YFZC58vcqljFHFjNgHgsG05OPOSumxxz56qF+8OkAnKnuiPEw=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/zrender-3.2.0.tgz_1477989192433_0.2399592436850071"}, "directories": {}, "publish_time": 1477989192664, "_hasShrinkwrap": false, "_cnpm_publish_time": 1477989192664, "_cnpmcore_publish_time": "2021-12-16T16:49:11.235Z"}, "3.1.3": {"name": "zrender", "version": "3.1.3", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "license": "BSD", "main": "lib/zrender.js", "gitHead": "a9b0b322cad3b4f4eeac208b1e3c129f060f18ba", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "_id": "zrender@3.1.3", "_shasum": "445897e40a431a4224ef0e71357c61e2d7497c8c", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "445897e40a431a4224ef0e71357c61e2d7497c8c", "size": 205377, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.1.3.tgz", "integrity": "sha512-a8t3N4OA02lhqpFMgcpDA59fEukApTW3URD/1Y5f4A6UC8HPzfayKU0YlYxMv5KRfaZ8zYr/KAwEAS+x6ylLkw=="}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/zrender-3.1.3.tgz_1471410554526_0.34379175840876997"}, "directories": {}, "publish_time": 1471410558669, "_hasShrinkwrap": false, "_cnpm_publish_time": 1471410558669, "_cnpmcore_publish_time": "2021-12-16T16:49:12.157Z"}, "3.1.2": {"name": "zrender", "version": "3.1.2", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "license": "BSD", "main": "lib/zrender.js", "gitHead": "f3890c8c09dc6adc5d5f1110d1328ccdfe8e468a", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "_id": "zrender@3.1.2", "_shasum": "5d3bb0a317de4d35d9c3bab8dcadbf807d0e716b", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "5d3bb0a317de4d35d9c3bab8dcadbf807d0e716b", "size": 204540, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.1.2.tgz", "integrity": "sha512-BMV89PPEXNq1UFWuqwEV/cJaBEYJAQh5loHWklgpu1J5c3ybgCREn5uxtVJDyJV/ShDN2kE5YFaeu2kj1nbB0g=="}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/zrender-3.1.2.tgz_1468326258386_0.9510606604162604"}, "directories": {}, "publish_time": 1468326261453, "_hasShrinkwrap": false, "_cnpm_publish_time": 1468326261453, "_cnpmcore_publish_time": "2021-12-16T16:49:12.761Z"}, "3.1.1": {"name": "zrender", "version": "3.1.1", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "license": "BSD", "main": "lib/zrender.js", "gitHead": "3762d4c0e9a331447cde319d0472eed034ba16f9", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "_id": "zrender@3.1.1", "_shasum": "e9e5225c65960ec0b9cb6153b308c436ca98296f", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.9.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "e9e5225c65960ec0b9cb6153b308c436ca98296f", "size": 204128, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.1.1.tgz", "integrity": "sha512-rYtg/TEDCtS+QtfkCfwz4eeNgFA4Ie5pOoL5prUKnIq7z1q5OY/FCXQtsVNz3nTLiZnWFjAG5q89iXzfNI84QQ=="}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/zrender-3.1.1.tgz_1467300503002_0.1786976088769734"}, "directories": {}, "publish_time": 1467300506532, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467300506532, "_cnpmcore_publish_time": "2021-12-16T16:49:13.285Z"}, "3.1.0": {"name": "zrender", "version": "3.1.0", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "license": "BSD", "main": "lib/zrender.js", "gitHead": "8f5af1e45aca40f202063ba7fa01313f3738a4ff", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "_id": "zrender@3.1.0", "_shasum": "998d7bb98059339e3fd35581cbf9afbbab032659", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.9.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "998d7bb98059339e3fd35581cbf9afbbab032659", "size": 189637, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.1.0.tgz", "integrity": "sha512-s9QD5EDzEgB8LJGSZBbn7TcWDxmRmgcYmsrPr1w8CbUjjeR6lGsZbMEDeMb3mQxBQ7a9oljk+jAu22hOror5tA=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/zrender-3.1.0.tgz_1463643810018_0.20177137339487672"}, "directories": {}, "publish_time": 1463643810595, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463643810595, "_cnpmcore_publish_time": "2021-12-16T16:49:13.790Z"}, "3.0.9": {"name": "zrender", "version": "3.0.9", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "license": "BSD", "main": "lib/zrender.js", "gitHead": "0567618a1b047127465fc2490e27d6bf7c332997", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "_id": "zrender@3.0.9", "_shasum": "0709f94dea4ad2ea655e413a460021cd98af0d0b", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.9.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "0709f94dea4ad2ea655e413a460021cd98af0d0b", "size": 188860, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.0.9.tgz", "integrity": "sha512-oF+IHNiuU2x/5A5du4crzmW0oPnsfmE/1vCPXZ5JCyHSgClqqd/AIutR5ikvfpYm+u07p6nnRszbylC4T0iOKQ=="}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/zrender-3.0.9.tgz_1462953879515_0.6402369670104235"}, "directories": {}, "publish_time": 1462953882033, "_hasShrinkwrap": false, "_cnpm_publish_time": 1462953882033, "_cnpmcore_publish_time": "2021-12-16T16:49:14.579Z"}, "3.0.8": {"name": "zrender", "version": "3.0.8", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "license": "BSD", "main": "lib/zrender.js", "gitHead": "9229312d1a8df28b3882e4260683d2eabe40b276", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "_id": "zrender@3.0.8", "_shasum": "85e5c55602bcf05caf6c5cf6770ffc9824e61339", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.9.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "85e5c55602bcf05caf6c5cf6770ffc9824e61339", "size": 187719, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.0.8.tgz", "integrity": "sha512-+oQgbeTOV+jKaETsSvbsDJvsTxo3t7/IDvZmkwskgtoI/TwEdc9DeEjE5kHLlnzyVeDVD5KZgZGMzIu1pEbz1w=="}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/zrender-3.0.8.tgz_1461307716746_0.8997189835645258"}, "directories": {}, "publish_time": 1461307719899, "_hasShrinkwrap": false, "_cnpm_publish_time": 1461307719899, "_cnpmcore_publish_time": "2021-12-16T16:49:15.114Z"}, "3.0.7": {"name": "zrender", "version": "3.0.7", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "license": "BSD", "main": "lib/zrender.js", "gitHead": "b02d297ae025212bd5d3724e00eaf2f7e113a4f9", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "_id": "zrender@3.0.7", "_shasum": "440c1c73eab2a63d1cd8f7a10964cb4e8690b641", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.9.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "440c1c73eab2a63d1cd8f7a10964cb4e8690b641", "size": 187553, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.0.7.tgz", "integrity": "sha512-QG2eGikI2n90nKwIwdCVAejxNpp+oXvee2tcol/ASwJw8kgniM4VCuivF9Pc+MXwcd4jNJPVRufoSX7XDMq4Zg=="}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/zrender-3.0.7.tgz_1460354155963_0.83980015758425"}, "directories": {}, "publish_time": 1460354158679, "_hasShrinkwrap": false, "_cnpm_publish_time": 1460354158679, "_cnpmcore_publish_time": "2021-12-16T16:49:16.165Z"}, "3.0.6": {"name": "zrender", "version": "3.0.6", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "license": "BSD", "main": "lib/zrender.js", "gitHead": "185c51a08527113773d5315a5d1b1f0c9e14ed5a", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "_id": "zrender@3.0.6", "_shasum": "0e802842ff70565f2c06aeb22a7e0c08c6e55547", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.9.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "0e802842ff70565f2c06aeb22a7e0c08c6e55547", "size": 187267, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.0.6.tgz", "integrity": "sha512-zQ47fXo5CLmUuW3DbFE8x5XISx0XMUkw9GWjfXWXZhK0zk9ucFVxmYvGWApJBfrJKzApBUWqqZe18tegorXZjQ=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/zrender-3.0.6.tgz_1459232841833_0.7197192264720798"}, "directories": {}, "publish_time": 1459232842266, "_hasShrinkwrap": false, "_cnpm_publish_time": 1459232842266, "_cnpmcore_publish_time": "2021-12-16T16:49:16.653Z"}, "3.0.5": {"name": "zrender", "version": "3.0.5", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "license": "BSD", "main": "lib/zrender.js", "gitHead": "14e98f481693a13b2d30ef76e5565e628aa0eeb3", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "_id": "zrender@3.0.5", "_shasum": "e9cd7d9be6e61e023f1cf104ca7389bffd5fd864", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.9.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "e9cd7d9be6e61e023f1cf104ca7389bffd5fd864", "size": 187041, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.0.5.tgz", "integrity": "sha512-ad+VfryCiyWf9Djx1tRqJyy5jd9mGl4O8gPzAOyQHnsp5AoAURbBTmvytZ3hrrQk9T7ywwYgnfoXNMQzAPryIw=="}, "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/zrender-3.0.5.tgz_1458543739408_0.8289067461155355"}, "directories": {}, "publish_time": 1458543739891, "_hasShrinkwrap": false, "_cnpm_publish_time": 1458543739891, "_cnpmcore_publish_time": "2021-12-16T16:49:17.253Z"}, "3.0.4": {"name": "zrender", "version": "3.0.4", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "license": "BSD", "main": "lib/zrender.js", "gitHead": "a15c0c23ba7206e75b1615fab769fe7d4c603c01", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "_id": "zrender@3.0.4", "_shasum": "0a1dbeff9c4b98c4c9171623efd8e962a1cc61c1", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "0a1dbeff9c4b98c4c9171623efd8e962a1cc61c1", "size": 185878, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.0.4.tgz", "integrity": "sha512-YwIUGVuoNvLNuY5O48baCWwLM3ogli/Zry589WxWA1y5YnIq6dtonDKV7m2XTQwKg6Hf13En6Il6r/Wzad9XkA=="}, "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/zrender-3.0.4.tgz_1457600312250_0.849122641608119"}, "directories": {}, "publish_time": 1457600312674, "_hasShrinkwrap": false, "_cnpm_publish_time": 1457600312674, "_cnpmcore_publish_time": "2021-12-16T16:49:17.830Z"}, "3.0.3": {"name": "zrender", "version": "3.0.3", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "license": "BSD", "main": "lib/zrender.js", "gitHead": "36c8e798ad4ab5697a85c85d4140dbea4ed5e31b", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "_id": "zrender@3.0.3", "_shasum": "cc54e0714bb863bb48aaa773ab2ea24d49109edf", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "cc54e0714bb863bb48aaa773ab2ea24d49109edf", "size": 185065, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.0.3.tgz", "integrity": "sha512-oJJvUQKoCB544W3SLn3si3lnjXkDBI9pKWHxN/xXW+2IdQ2hQcLr0yZ0ElWjM64zsFVC0fJTi1mlYVruEbXwGQ=="}, "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/zrender-3.0.3.tgz_1456835401393_0.5672784489579499"}, "directories": {}, "publish_time": 1456835405110, "_hasShrinkwrap": false, "_cnpm_publish_time": 1456835405110, "_cnpmcore_publish_time": "2021-12-16T16:49:18.910Z"}, "3.0.2": {"name": "zrender", "version": "3.0.2", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "node build/amd2common.bundle.js"}, "license": "BSD", "main": "lib/zrender.js", "gitHead": "9da8d8dd2ee98edb1dc06403a1bb431695e290a1", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "_id": "zrender@3.0.2", "_shasum": "beb4c82755af599bc083d837f7a288702e7417c4", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.5.0", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "beb4c82755af599bc083d837f7a288702e7417c4", "size": 185129, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.0.2.tgz", "integrity": "sha512-peQn/7b3wVorf/O4o4ZFWakDH41bZw268pDAeyB4JYTE4ALqL+mR/fWhZFu7SuAg5bpwQkypzC89Vy2f2jsWog=="}, "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/zrender-3.0.2.tgz_1456142760032_0.7086878025438637"}, "directories": {}, "publish_time": 1456142764547, "_hasShrinkwrap": false, "_cnpm_publish_time": 1456142764547, "_cnpmcore_publish_time": "2021-12-16T16:49:19.801Z"}, "3.0.1": {"name": "zrender", "version": "3.0.1", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/zrender.git"}, "license": "BSD", "main": "build/zrender.min.js", "files": ["build", "src"], "gitHead": "b3f793e56e9e4f15a6d02335dd82e7c0bf6a6ff7", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "_id": "zrender@3.0.1", "scripts": {}, "_shasum": "4ee7f8b1b67dcf634529b10f963aebd3593855f4", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.1.1", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "4ee7f8b1b67dcf634529b10f963aebd3593855f4", "size": 399318, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.0.1.tgz", "integrity": "sha512-AsPR2YJ68wzAJVZebiWGoyjhTWTfdP9Yt0cpBzyn1pZxzmlYtNziFQqty8bJgxUZS31Psyf5lkxq8a7/IA1c1A=="}, "directories": {}, "publish_time": 1454035853693, "_hasShrinkwrap": false, "_cnpm_publish_time": 1454035853693, "_cnpmcore_publish_time": "2021-12-16T16:49:20.527Z"}, "3.0.0": {"name": "zrender", "version": "3.0.0", "description": "A lightweight canvas library.", "keywords": ["canvas", "2d"], "author": "", "contributors": [{"name": "erik", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ecomfe/zrender.git"}, "main": "build/zrender.min.js", "files": ["build", "src"], "gitHead": "b675dba6e971d31139cf71fa3d6899c6a15e2288", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "_id": "zrender@3.0.0", "scripts": {}, "_shasum": "84f7a8eb2a5522f4bb21cbf8ff6944b0b43f78c2", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.1.1", "_npmUser": {"name": "ecomfe", "email": "<EMAIL>"}, "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "dist": {"shasum": "84f7a8eb2a5522f4bb21cbf8ff6944b0b43f78c2", "size": 398935, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-3.0.0.tgz", "integrity": "sha512-EYKWQt+tmK378PGzzen7gf0S7+Fyu+4iSOyi1vR+EYIKDnWUTUehj6KE0z15S/8jXfjZW9ytHdNJ2l+NJ2Od0Q=="}, "directories": {}, "publish_time": 1452754107921, "_hasShrinkwrap": false, "_cnpm_publish_time": 1452754107921, "_cnpmcore_publish_time": "2021-12-16T16:49:22.037Z"}, "2.0.2": {"name": "zrender", "version": "2.0.2", "maintainers": [{"name": "errorrik", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}], "main": "zrender", "description": "一个轻量级的Canvas类库，MVC封装，数据驱动，提供类Dom事件模型，让canvas绘图大不同！", "homepage": "https://ecomfe.github.com/zrender", "_id": "zrender@2.0.2", "scripts": {}, "_shasum": "d0dbfc205f957884a02dafc138d07b4cd3de2cd7", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d0dbfc205f957884a02dafc138d07b4cd3de2cd7", "size": 112458, "noattachment": false, "tarball": "https://registry.npmmirror.com/zrender/-/zrender-2.0.2.tgz", "integrity": "sha512-ToYHXG99GDlcqOXqds4yBgGVmqDDUgZ1B+VOEByjZ7TJvx0/mfoJO+m17aPSVCi51wK/N2UMGnZi//HwYzHWFA=="}, "directories": {}, "publish_time": 1409292604848, "_hasShrinkwrap": false, "_cnpm_publish_time": 1409292604848, "_cnpmcore_publish_time": "2021-12-16T16:49:22.536Z"}, "5.3.0": {"name": "zrender", "version": "5.3.0", "description": "A lightweight graphic library providing 2d draw for Apache ECharts", "keywords": ["canvas", "svg", "2d", "html5", "vector-graphics"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "npm run release", "build": "npm run build:bundle && npm run build:lib", "release": "node build/build.js --minify && npm run build:lib", "prepare:nightly": "node build/prepareNightly.js", "prepare:nightly-next": "node build/prepareNightly.js --next", "build:bundle": "node build/build.js", "build:lib": "npx tsc -m ES2015 --outDir lib && node build/processLib.js", "watch:bundle": "node build/build.js --watch", "watch:lib": "npx tsc-watch -m ES2015 --outDir lib --synchronousWatchDirectory --onSuccess \"node build/processLib.js\"", "test": "npx jest --config test/ut/jest.config.js", "lint": "npx eslint src/**/*.ts"}, "license": "BSD-3-<PERSON><PERSON>", "types": "index.d.ts", "module": "index.js", "main": "dist/zrender.js", "dependencies": {"tslib": "2.3.0"}, "sideEffects": ["lib/canvas/canvas.js", "lib/svg/svg.js"], "devDependencies": {"@microsoft/api-extractor": "^7.7.2", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^3.0.0", "@types/jest": "^27.0.2", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.9.1", "chalk": "^3.0.0", "commander": "2.11.0", "eslint": "6.3.0", "fs-extra": "4.0.2", "globby": "^11.0.4", "jest": "^27.2.5", "jsdom": "^16.0.0", "rollup": "^1.28.0", "rollup-plugin-typescript2": "^0.25.3", "rollup-plugin-uglify": "^6.0.4", "ts-jest": "^27.0.6", "typescript": "^4.4.3", "uglify-js": "^3.10.0"}, "gitHead": "708834f5e54dd95c1d4d60e4930325f93cc1d7f5", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@5.3.0", "_nodeVersion": "16.13.0", "_npmVersion": "6.14.15", "_npmUser": {"name": "lang", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Ln2QB5uqI1ftNYMtCRxd+XDq6MOttLgam2tmhKAVA+j0ko47UT+VNlDvKTkqe4K2sJhBvB0EhYNLebqlCTjatQ==", "shasum": "297c05dc2521362816c4ddced10a1e306646bbc8", "tarball": "https://registry.npmmirror.com/zrender/-/zrender-5.3.0.tgz", "fileCount": 370, "unpackedSize": 4002552, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7PkkCRA9TVsSAnZWagAA9ZMP/2gYAfhuCxfSIRcxtA67\niDUOgFP3CBKmxocmiCQ633pKMikZyfRb245ORFfcs/n7X48tzdOSqXZJFpnd\nXVT6/pXI9WsdJAahKPwT/nctyTeHWEEpl5co+xfrChmd80tCLt3XdL1G+1Yv\n68q6CeV/2hDAAlEosueEHAYtSafnjit2WRHyK2YJzr5dIRWDupO7VEyIETM2\nl1ly/h9zhy2j8J0sGoopuo0o/iWf4C95CrG1OMzjbY/j87UOunIqQ71wysx+\nJHwBBt/cT4L+7KRulopeymEcwgqrF3+XUCcQijsu5Hn+xylhTN6E50wgio8a\n9XH04VdzSih0+jSPG8N9CujbUBXA4k94Zf0C/1P3L4mBHXDOIQD54jPd7tGj\nR7VBqNfXIDRK8sb7wrrFw8Kfwl7kE3ZzrTWuo9I4yJ+RTE06U0srD0DPoOXl\nfxFbepabTAaFxdrTxfXovZkaJjb8cd6Jt/xaqMMipOz0yHMfL2xCZYkJUurT\nX1o+aO6xIAIno1w5v2BxogKSpzPOREhOay/3uPV4kPxUMIlpsjCTLJZJv+rd\nvyU8Z6Uo8wOm7uTiqhamlGMGRhlgOgC5YwzQ5uVnPbxSLgI/fQzq1Mf2AWwE\nGjcOL3ImoxUNpBOev4DS5dsPvRlCDo28xLGiLqbU+harWTlfHbs3R5brBHbB\nPGDg\r\n=88x0\r\n-----END PGP SIGNATURE-----\r\n", "size": 856058}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "ecomfe-admin", "email": "<EMAIL>"}, {"name": "otakustay", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "luyuan", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "jinz<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_5.3.0_1642920228252_0.9745254299326611"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-23T06:53:53.479Z"}, "5.3.1": {"name": "zrender", "version": "5.3.1", "description": "A lightweight graphic library providing 2d draw for Apache ECharts", "keywords": ["canvas", "svg", "2d", "html5", "vector-graphics"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "npm run release", "build": "npm run build:bundle && npm run build:lib", "release": "node build/build.js --minify && npm run build:lib", "prepare:nightly": "node build/prepareNightly.js", "prepare:nightly-next": "node build/prepareNightly.js --next", "build:bundle": "node build/build.js", "build:lib": "npx tsc -m ES2015 --outDir lib && node build/processLib.js", "watch:bundle": "node build/build.js --watch", "watch:lib": "npx tsc-watch -m ES2015 --outDir lib --synchronousWatchDirectory --onSuccess \"node build/processLib.js\"", "test": "npx jest --config test/ut/jest.config.js", "lint": "npx eslint src/**/*.ts"}, "license": "BSD-3-<PERSON><PERSON>", "types": "index.d.ts", "module": "index.js", "main": "dist/zrender.js", "dependencies": {"tslib": "2.3.0"}, "sideEffects": ["lib/canvas/canvas.js", "lib/svg/svg.js"], "devDependencies": {"@microsoft/api-extractor": "^7.7.2", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^3.0.0", "@types/jest": "^27.0.2", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.9.1", "chalk": "^3.0.0", "commander": "2.11.0", "eslint": "6.3.0", "fs-extra": "4.0.2", "globby": "^11.0.4", "jest": "^27.2.5", "jsdom": "^16.0.0", "rollup": "^1.28.0", "rollup-plugin-typescript2": "^0.25.3", "rollup-plugin-uglify": "^6.0.4", "ts-jest": "^27.0.6", "typescript": "^4.4.3", "uglify-js": "^3.10.0"}, "gitHead": "d44f50e66b6efcddc781c1a1f1d636f1b2a30a68", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@5.3.1", "_nodeVersion": "16.13.0", "_npmVersion": "6.14.15", "_npmUser": {"name": "lang", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-7olqIjy0gWfznKr6vgfnGBk7y4UtdMvdwFmK92vVQsQeDPyzkHW1OlrLEKg6GHz1W5ePf0FeN1q2vkl/HFqhXw==", "shasum": "fa8e63ac7e719cfd563831fe8c42a9756c5af384", "tarball": "https://registry.npmmirror.com/zrender/-/zrender-5.3.1.tgz", "fileCount": 370, "unpackedSize": 4001955, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHwjdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo96hAAgF8kb87wNlS+eMWxO3pMWc4UgcxQKCYWxFlPv7f6WvpgjDJi\r\nbep9NAPB5xwwOQZ8CeJnRg03jbLY/GZDk28T25yl+uFPtj3hyJB5KN6lxNuV\r\nW9KORlFDoYXlG+OoLBlS8p+4sadB4LOvxFbWMaGTplFvm4OygYBcFDZTTUtx\r\nUNtn7k1SdvUwLl2lTRke50MnpxXfusuTWeMHLs4D5JnkfqJF6N/DBjni8thv\r\nPx5UIByVFAk8budI3O/i3xB2kttWm57vPhsGeQdpN6A3ljj7sffX4J7ACIQE\r\nbB6957f9gajMefnhGCee/Lx48Q9lGCxQI9WzcRcj42NNeFWnBBzsMvfyA6cp\r\nxzWLlfGymSUQktyJJ61xcmC8QdOndFnLdlznTSZLU4GqRZ07/55GVAKZoHrP\r\nDZzWhqm4a5mXPGWMbIhevcO+sOT/rJNk6NpZQF7dElpaXuC8Jw5sHh2Pysox\r\nvCqe/OqPkT0OuD1ZHHh1ZGpR0tiaJZUfW/fNT9UDMRfUEIjbtRowbXhEtA5V\r\nENh9RXwi29ar9uxC3qSQv4JCeStcOKZp7QBjvvGgPUm1IzDCuBbuc5LpIjw3\r\ntpDxl/VRsGyGW7IxwkBMt9Hi4BCsiKeOVS0NEg/D3Bwco0GstN7gT3POnbK5\r\ncNA6Cz2qaBvvjqAAp4rrsru1uINAqQsTaLI=\r\n=RStZ\r\n-----END PGP SIGNATURE-----\r\n", "size": 854582}, "directories": {}, "maintainers": [{"name": "100pah", "email": "<EMAIL>"}, {"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "ecomfe-admin", "email": "<EMAIL>"}, {"name": "otakustay", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "luyuan", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "jinz<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_5.3.1_1646201052782_0.8824004224783317"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-02T06:04:18.446Z"}, "5.3.2": {"name": "zrender", "version": "5.3.2", "description": "A lightweight graphic library providing 2d draw for Apache ECharts", "keywords": ["canvas", "svg", "2d", "html5", "vector-graphics"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "npm run release", "build": "npm run build:bundle && npm run build:lib", "release": "node build/build.js --minify && npm run build:lib", "prepare:nightly": "node build/prepareNightly.js", "prepare:nightly-next": "node build/prepareNightly.js --next", "build:bundle": "node build/build.js", "build:lib": "npx tsc -m ES2015 --outDir lib && node build/processLib.js", "watch:bundle": "node build/build.js --watch", "watch:lib": "npx tsc-watch -m ES2015 --outDir lib --synchronousWatchDirectory --onSuccess \"node build/processLib.js\"", "test": "npx jest --config test/ut/jest.config.js", "lint": "npx eslint src/**/*.ts"}, "license": "BSD-3-<PERSON><PERSON>", "types": "index.d.ts", "module": "index.js", "main": "dist/zrender.js", "dependencies": {"tslib": "2.3.0"}, "sideEffects": ["lib/canvas/canvas.js", "lib/svg/svg.js"], "devDependencies": {"@microsoft/api-extractor": "^7.7.2", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^3.0.0", "@types/jest": "^27.0.2", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.9.1", "chalk": "^3.0.0", "commander": "2.11.0", "eslint": "6.3.0", "fs-extra": "4.0.2", "globby": "^11.0.4", "jest": "^27.2.5", "jsdom": "^16.0.0", "rollup": "^1.28.0", "rollup-plugin-typescript2": "^0.25.3", "rollup-plugin-uglify": "^6.0.4", "ts-jest": "^27.0.6", "typescript": "^4.4.3", "uglify-js": "^3.10.0"}, "gitHead": "559d610bd0a612fa8ed5a7e6a8dd4b23a318e94d", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@5.3.2", "_nodeVersion": "16.13.1", "_npmVersion": "8.12.1", "dist": {"integrity": "sha512-8IiYdfwHj2rx0UeIGZGGU4WEVSDEdeVCaIg/fomejg1Xu6OifAL1GVzIPHg2D+MyUkbNgPWji90t0a8IDk+39w==", "shasum": "f67b11d36d3d020d62411d3bb123eb1c93cccd69", "tarball": "https://registry.npmmirror.com/zrender/-/zrender-5.3.2.tgz", "fileCount": 390, "unpackedSize": 4067188, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDiLc6A34dCmiJ80SF/FNKlxDXjjty+ft582BdK3AvZhgIgUn2LKUWg5NOkCjYeWAvmc3YJJ/x5Sz54xiHtKbs05/M="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioajQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoxww//d2pQ3E674M3Ifbve7WOv9wsVH0L8ekjE3gkcPaQipuWbkesb\r\nqL9A+JU2Fi4uHiGENXdFsmIZgNdieHKkPq/kNTbGADGoxVz0n6yC63QQo3i/\r\nW8mJI8e79dQofUivtfGrgpRO0tRgPxmbHZPL1juW+r3bB/1Hq4jWsd7B2yGC\r\n8oMmDdgZmw/OQ7rWxR6vbaUeFCUXbdasdC5FLClWkchmNli9h7aXxk8qb0/F\r\n0QbMxvhFkJX6KSBK7moCe8VKpFlyzSrsFPZL1H/eE3T5KkX6eETapjMC7skB\r\nN0uU51BUkEjHfwBpYVsV1jktQj4zf0hIg4B7hHdqMnj5c+AljThii0O7oeby\r\nHhFFhd6YFpEOpoOZ/duj1Sh0fibWN/nGzVU4MUlXnnJjSNr/HgzCDMit37p6\r\nzaU1aSoFql2GLDwcnB9DXnFpNRU9lTNRRKeIxeX7Qsxk7xEYaYttLjGrgQjE\r\n+7AB0wGo5FQQJCVPGP0QUiZbwgR7i2co/fcbZ25HRaOEI7pLgiyN7KpMvRiq\r\nP8bRfp+Y3RRt5EwawimzOMCk3p462wz0o/za7btEvl/Kh6d2VRkKddwcHpvs\r\n4NenmpZtH6afSpnxgRyCWUPt/8+npMvQZkwRMK5VSPpcqp1IM3btn2t1fi3S\r\nBW1RGGn2EVpcg0j523VAp3RnoorZmWXrvUw=\r\n=q2X1\r\n-----END PGP SIGNATURE-----\r\n", "size": 850857}, "_npmUser": {"name": "o<PERSON>a", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "ecomfe-admin", "email": "<EMAIL>"}, {"name": "otakustay", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "luyuan", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "jinz<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_5.3.2_1654761679648_0.5821005562621886"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-09T09:14:46.439Z"}, "5.4.0": {"name": "zrender", "version": "5.4.0", "description": "A lightweight graphic library providing 2d draw for Apache ECharts", "keywords": ["canvas", "svg", "2d", "html5", "vector-graphics"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "npm run release", "build": "npm run build:bundle && npm run build:lib", "release": "node build/build.js --minify && npm run build:lib", "prepare:nightly": "node build/prepareNightly.js", "prepare:nightly-next": "node build/prepareNightly.js --next", "build:bundle": "node build/build.js", "build:lib": "npx tsc -m ES2015 --outDir lib && node build/processLib.js", "watch:bundle": "node build/build.js --watch", "watch:lib": "npx tsc-watch -m ES2015 --outDir lib --synchronousWatchDirectory --onSuccess \"node build/processLib.js\"", "test": "npx jest --config test/ut/jest.config.js", "lint": "npx eslint src/**/*.ts"}, "license": "BSD-3-<PERSON><PERSON>", "types": "index.d.ts", "module": "index.js", "main": "dist/zrender.js", "dependencies": {"tslib": "2.3.0"}, "sideEffects": ["lib/canvas/canvas.js", "lib/svg/svg.js"], "devDependencies": {"@microsoft/api-extractor": "^7.7.2", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^3.0.0", "@types/jest": "^27.0.2", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chalk": "^3.0.0", "commander": "2.11.0", "eslint": "6.3.0", "fs-extra": "4.0.2", "globby": "^11.0.4", "jest": "^27.2.5", "jsdom": "^16.0.0", "rollup": "^1.28.0", "rollup-plugin-typescript2": "^0.25.3", "rollup-plugin-uglify": "^6.0.4", "ts-jest": "^27.0.6", "typescript": "^4.4.3", "uglify-js": "^3.10.0"}, "gitHead": "518705322cd74228aa3cd2834a477cee94684cf3", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@5.4.0", "_nodeVersion": "16.9.1", "_npmVersion": "7.21.1", "dist": {"integrity": "sha512-rOS09Z2HSVGFs2dn/TuYk5BlCaZcVe8UDLLjj1ySYF828LATKKdxuakSZMvrDz54yiKPDYVfjdKqcX8Jky3BIA==", "shasum": "d4f76e527b2e3bbd7add2bdaf27a16af85785576", "tarball": "https://registry.npmmirror.com/zrender/-/zrender-5.4.0.tgz", "fileCount": 519, "unpackedSize": 4807914, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDU90/ZSH2x1pO3C5Tj51WiKJ1+32ghAxIC5Prs8luldAiB6rf6172KcvmLTvJnf/LTNV/XJL9UFTSqztRa4iWaFLw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIEjJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpYRA//Z1CojFkjYbA9aVMNhboukjJysSeVbKrR14UwEI2jL3y7LRPu\r\nO9HuZznj3Gm23yWpWGT+eLtZR8diLc5NDug7KLYy9SYJcG85HaRjl2DPNZ9P\r\nhojIcHfDX5eVZed5oZzfMXZRZrJOpyrCZtMQmBGhEI5vFYs5sg8wqnj4ACro\r\n9BwolzcDx5F/CTAi9m7fDCPhSnOabnmrJMZxDstEXZG4SQi3aCWU2PGPdw4H\r\n6vundYbqvUWZafqNXFHak5F7UZQls/iBYtBeNsePezd+CsI8VePPzywIAJI2\r\n0Qf1d127N97PJ5wLMTyHRbj2wBQ1ZsXeKCNAG2+XPaV7I2f3VinbfW+HDyLw\r\n3rFHXBt8z/y4J5gvTHjyL2DXjYFy2LkylrFtjGNmzZ49OoqzA0Hwro01iixk\r\nefztYochARrkFU4raaktBAX5ZqXFGcgA0RAuZNFJ+BXPzCtY8f5BkhaSd76d\r\naTfpVgd6C5CT3rfOUAGUFArrnJDpgNzNXmJCUOjT1+kj2KKOuVdPsXvNMOK5\r\nNhm9+7vdR0WRjXse0nhEiVY5IVzikbVRMmawDA8Q/TXMh2HRglEm9TYAnAt4\r\nGH+haB05i4ladPBDyiXoLLmapGT/3k5C9QfO4X86ktnNeZLIMij9gsYfF2P8\r\njd+W5IVq6DfyuDbxWUbd03ApKRRguBvMEw4=\r\n=Pbwv\r\n-----END PGP SIGNATURE-----\r\n", "size": 982099}, "_npmUser": {"name": "100pah", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "ecomfe-admin", "email": "<EMAIL>"}, {"name": "otakustay", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "luyuan", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "jinz<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_5.4.0_1663060169565_0.7774918260086399"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-09-13T09:45:22.347Z"}, "5.4.1": {"name": "zrender", "version": "5.4.1", "description": "A lightweight graphic library providing 2d draw for Apache ECharts", "keywords": ["canvas", "svg", "2d", "html5", "vector-graphics"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "npm run release", "build": "npm run build:bundle && npm run build:lib", "release": "node build/build.js --minify && npm run build:lib", "prepare:nightly": "node build/prepareNightly.js", "prepare:nightly-next": "node build/prepareNightly.js --next", "build:bundle": "node build/build.js", "build:lib": "npx tsc -m ES2015 --outDir lib && node build/processLib.js", "watch:bundle": "node build/build.js --watch", "watch:lib": "npx tsc-watch -m ES2015 --outDir lib --synchronousWatchDirectory --onSuccess \"node build/processLib.js\"", "test": "npx jest --config test/ut/jest.config.js", "lint": "npx eslint src/**/*.ts"}, "license": "BSD-3-<PERSON><PERSON>", "types": "index.d.ts", "module": "index.js", "main": "dist/zrender.js", "dependencies": {"tslib": "2.3.0"}, "sideEffects": ["lib/canvas/canvas.js", "lib/svg/svg.js"], "devDependencies": {"@microsoft/api-extractor": "^7.7.2", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^3.0.0", "@types/jest": "^27.0.2", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chalk": "^3.0.0", "commander": "2.11.0", "eslint": "6.3.0", "fs-extra": "4.0.2", "globby": "^11.0.4", "jest": "^27.2.5", "jsdom": "^16.0.0", "rollup": "^1.28.0", "rollup-plugin-typescript2": "^0.25.3", "rollup-plugin-uglify": "^6.0.4", "ts-jest": "^27.0.6", "typescript": "^4.4.3", "uglify-js": "^3.10.0"}, "gitHead": "870b3b2a6e3fc7da698f7a59b723a14712027347", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@5.4.1", "_nodeVersion": "16.13.1", "_npmVersion": "8.12.1", "dist": {"integrity": "sha512-M4Z05BHWtajY2241EmMPHglDQAJ1UyHQcYsxDNzD9XLSkPDqMq4bB28v9Pb4mvHnVQ0GxyTklZ/69xCFP6RXBA==", "shasum": "892f864b885c71e1dc25dcb3c7a4ba42678d3f11", "tarball": "https://registry.npmmirror.com/zrender/-/zrender-5.4.1.tgz", "fileCount": 390, "unpackedSize": 4107736, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHb3PvPOS9sMoQCyxYXUy7AptH4Btmh+AuH5PL4Spq4FAiEAiX+yB/qU89Pd2QFybRLm/ir1yGzzivccDdIwTpPRa3Q="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhEbhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpxPQ/+LV76HVO1nMGYO2lXQC4dsvwX8EwELbN50M2hviGkm6GfO/pY\r\nbFYpfJ77iD5AHyGRrHy0cqchlo5ZEG178hNIgmELEPy/hpiMuoUG7c0g8pke\r\n/LPvLR906U06vKecD3HsF4YQks1LgBItMqff9ihImhINAdqlb93yIFE/rQuC\r\nbmeyoN0D4HFM7EgNpLSdie35t7wW3yr9lfG+u5l6FeIBaNq+QtJU56kACvPG\r\nlSua7mjK6C+gycbRjQTwYA69FtdebLzc5wxBwgFOZAcrJrjoOVI8xVRQ8NMZ\r\nmN39Ki8Hbq8BoAvlF+yF3xvGJ40Jl+AReel3ZwTH/jkXmVRf9WCYtFEeLD2e\r\nMX0CFi9SJSsV6nVuhWVuJOEtH4XWNLexAhF8W5X0698rVDmv1IpJOIAiu3jJ\r\ngeNyky9b3beqaiQRjE//nqWaFzIa+JsZ+750r3TEGkb8kRulzeZFQFusZ1Ng\r\nbVw6s76/mbMt4a2AmDUHukQng/s1T/i1K53ChKiruDXYG9RN58QOoeBJ6wKp\r\njh3riXipup5f7CESgomdaJtcWZe9w9ckkznvcEnXz8QhlOUCQYeSDBMe7ZSC\r\ncqx2uyDQUzh0wFiH2CXZbnwDCquOVSb99QtkndWD97OCMQ3jKtVjOQy1BADY\r\nRwWwrsNjdvMVrZywbrYdoQtiEXKLo3cCZtc=\r\n=0lAc\r\n-----END PGP SIGNATURE-----\r\n", "size": 860274}, "_npmUser": {"name": "o<PERSON>a", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zttonly", "email": "<EMAIL>"}, {"name": "gkiwi001", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "ecomfe-admin", "email": "<EMAIL>"}, {"name": "otakustay", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "luyuan", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "jinz<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_5.4.1_1669613281108_0.17091591247079796"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-11-28T05:28:08.295Z"}, "5.4.2": {"name": "zrender", "version": "5.4.2", "description": "A lightweight graphic library providing 2d draw for Apache ECharts", "keywords": ["canvas", "svg", "2d", "html5", "vector-graphics"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "npm run release", "build": "npm run build:bundle && npm run build:lib", "release": "node build/build.js --minify && npm run build:lib", "prepare:nightly": "node build/prepareNightly.js", "prepare:nightly-next": "node build/prepareNightly.js --next", "build:bundle": "node build/build.js", "build:lib": "npx tsc -m ES2015 --outDir lib && node build/processLib.js", "watch:bundle": "node build/build.js --watch", "watch:lib": "npx tsc-watch -m ES2015 --outDir lib --synchronousWatchDirectory --onSuccess \"node build/processLib.js\"", "test": "npx jest --config test/ut/jest.config.js", "lint": "npx eslint src/**/*.ts"}, "license": "BSD-3-<PERSON><PERSON>", "types": "index.d.ts", "module": "index.js", "main": "dist/zrender.js", "dependencies": {"tslib": "2.3.0"}, "sideEffects": ["lib/canvas/canvas.js", "lib/svg/svg.js"], "devDependencies": {"@microsoft/api-extractor": "^7.7.2", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^3.0.0", "@types/jest": "^27.0.2", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chalk": "^3.0.0", "commander": "2.11.0", "eslint": "6.3.0", "fs-extra": "4.0.2", "globby": "^11.0.4", "jest": "^27.2.5", "jsdom": "^16.0.0", "rollup": "^1.28.0", "rollup-plugin-typescript2": "^0.25.3", "rollup-plugin-uglify": "^6.0.4", "ts-jest": "^27.0.6", "typescript": "^4.4.3", "uglify-js": "^3.10.0"}, "gitHead": "98707bd5ff26b7fde64e21463b437cbe90746fda", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@5.4.2", "_nodeVersion": "16.19.1", "_npmVersion": "9.4.0", "dist": {"integrity": "sha512-c74s36Aw1dwgRAzKUA2NYB4F8lXP5CKElNCFYzs7qJU9Mc7KoHDXY821ZOoRkBe9PDWZipsU0WFuP4KSeUCnrg==", "shasum": "30b3cd7915c40d90e8d78d08d4b3db581cf1facc", "tarball": "https://registry.npmmirror.com/zrender/-/zrender-5.4.2.tgz", "fileCount": 370, "unpackedSize": 4045450, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDIvEd5Sn8sY94n8YNPgmsCTsgrS+XJ8+k0XjXXKMOZogIgScn05AENq0dkkv/sVU6avVIsvSrrESl5QtnF2bFrabA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCai4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqH5A/+N7oHdJIFsKWTCP6wyhnVgLeVeFd9/FD3Q9BlYerU2wSfdTie\r\nzKIE+jJFdtW+bSTj8gFOcK3tO6yZcSJoTMFPryL5nFiFuXTy7PrfUc/wY1if\r\ngdrGuGk2BUUzCOoGNGVcCS//L6+N0lo4Oa87Jwwp5FVoyMvCciIJ0vSSa04J\r\nvGZRf7PO/gIhLotEP837Pi9Jmx6+iX05qW4Oet2i7gH/ZLR4MMXBicAVZ+V1\r\nj6lgVvPDXTceyozGkEDWX/ICTAZMthZqNJzozFu5AUOxiIvfAN4pdMm8KIgq\r\nC0IViwcTqlgmVDWmhJTCq2b+fq0M6qBQxTEF0qbs2RQ4w8hkBqSBnRxB0FhF\r\nkYDVZtQFF5iTOUBLbujWgX1g6svgr3mzbMwthTRPKE7QTsQPIVa1gejIIHJi\r\n2nqhI5O8VsrbxEEvutniK/35Db9WCPVgDrtanSCXLY8JXRq4NPNOeNf347zO\r\n5Cm3t8L9uJCqVTT+t0DpddsY9hI/gSTfsT7g/b5dpZuFR0vYzHq/HZgXYK3J\r\nlndezhVmraDVB/VVFkmCIJWEzxGIk9rZmiFEa+q+UUCdBwjyBsCcO2biqYY5\r\nQ3XKYwhVpIkjUmD76Bx0YQjJp5uAm+GS8O6L4KzTIT+YVebdlUJdLUH5WWMR\r\nMPs5+K24uvVIO8sQ2ooqY+reAcTMzUaLNIo=\r\n=G5bs\r\n-----END PGP SIGNATURE-----\r\n", "size": 851211}, "_npmUser": {"name": "o<PERSON>a", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zttonly", "email": "<EMAIL>"}, {"name": "gkiwi001", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "ecomfe-admin", "email": "<EMAIL>"}, {"name": "otakustay", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "luyuan", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "jinz<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_5.4.2_1678354615904_0.8116905621498769"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-09T09:36:56.073Z", "publish_time": 1678354616073}, "5.4.3": {"name": "zrender", "version": "5.4.3", "description": "A lightweight graphic library providing 2d draw for Apache ECharts", "keywords": ["canvas", "svg", "2d", "html5", "vector-graphics"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepublish": "npm run release", "build": "npm run build:bundle && npm run build:lib", "release": "node build/build.js --minify && npm run build:lib", "prepare:nightly": "node build/prepareNightly.js", "prepare:nightly-next": "node build/prepareNightly.js --next", "build:bundle": "node build/build.js", "build:lib": "npx tsc -m ES2015 --outDir lib && node build/processLib.js", "watch:bundle": "node build/build.js --watch", "watch:lib": "npx tsc-watch -m ES2015 --outDir lib --synchronousWatchDirectory --onSuccess \"node build/processLib.js\"", "test": "npx jest --config test/ut/jest.config.js", "lint": "npx eslint src/**/*.ts"}, "license": "BSD-3-<PERSON><PERSON>", "types": "index.d.ts", "module": "index.js", "main": "dist/zrender.js", "dependencies": {"tslib": "2.3.0"}, "sideEffects": ["lib/canvas/canvas.js", "lib/svg/svg.js"], "devDependencies": {"@microsoft/api-extractor": "^7.7.2", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^3.0.0", "@types/jest": "^27.0.2", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chalk": "^3.0.0", "commander": "2.11.0", "eslint": "6.3.0", "fs-extra": "4.0.2", "globby": "^11.0.4", "jest": "^27.2.5", "jsdom": "^16.0.0", "rollup": "^1.28.0", "rollup-plugin-typescript2": "^0.25.3", "rollup-plugin-uglify": "^6.0.4", "ts-jest": "^27.0.6", "typescript": "^4.4.3", "uglify-js": "^3.10.0"}, "gitHead": "25ae07a574bb94a8845d1c56b35a5310f3cc5c87", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@5.4.3", "_nodeVersion": "16.19.1", "_npmVersion": "9.4.0", "dist": {"integrity": "sha512-DRUM4ZLnoaT0PBVvGBDO9oWIDBKFdAVieNWxWwK0niYzJCMwGchRk21/hsE+RKkIveH3XHCyvXcJDkgLVvfizQ==", "shasum": "41ffaf835f3a3210224abd9d6964b48ff01e79f5", "tarball": "https://registry.npmmirror.com/zrender/-/zrender-5.4.3.tgz", "fileCount": 370, "unpackedSize": 4045450, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCgTEnjWtymw9Vv4OsGfhesY+OTkbHUojDFbnlw7/cq9wIge2Dqhj0b+BvpnETXBO6Cieknv2KEbaFjkwhQP/ubJRE="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCtM5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqWUA/+NyAPccfH2U0usXWEoAJipIudIevCfv5CWBR0kM0MVUwSEUub\r\nJpIDd3JqJaA0B7IE96dqE7Sn/wkoIqK4jc1u358v+qa1rZUyuFoDSQoCjXNr\r\nR8K6WMUsL1pZCJfVsqe01aInV2hn5KG1SorIp5u2yXEZKXrc09VpA45pwsZg\r\nu8DFdsplGCTvB4TrxafAlO8PW07L1+QTGTFDWhPIBldMAaeaTs9a6T8Mv6uc\r\nYg8ll0AbPunbesG+uaJrOmBI6LOAwKHbTSrLkr+1gmREvsR1rc2d9WgE7epD\r\nWBBEhYnJKYqa/GsZH47u65OO7n8vQRpLG+PuJ8Hv57vK4kfHWWBH1uyuCXeI\r\nytVbBLUXEmVSc1vrKdgCfleUM3YCkDq1kFqhfnWOifixNm3XpIqfyPytjSGK\r\n5SfTWMSJcfkMxURurna1bYJZI5nWsQgtD+27hdy3ZSfzITkZec2nsxqu20uk\r\nLYKeW4vT0C3cSE9nM/2aeTJSsSI8WfkEg9zrSteTwwP9l6vfgiBamgMHLp0m\r\neTHqXDZEDqdH3icOHRJsV1+RPJemLIa+P6a/YCHAXU0sB7049h8rlsG4mt6x\r\n29ANwmumj+fZmgoj+wreHlGQO0L7na8VAz6kJeB1xApD6+4qK/nzF/hO4BGS\r\nzOgR/80XQMJAPMDOfPDE6hQYAzu6Rl61oaw=\r\n=5/Z0\r\n-----END PGP SIGNATURE-----\r\n", "size": 851212}, "_npmUser": {"name": "o<PERSON>a", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zttonly", "email": "<EMAIL>"}, {"name": "gkiwi001", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "ecomfe-admin", "email": "<EMAIL>"}, {"name": "otakustay", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "luyuan", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "jinz<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_5.4.3_1678431033601_0.0067008769315197725"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-10T06:50:33.819Z", "publish_time": 1678431033819}, "5.4.4": {"name": "zrender", "version": "5.4.4", "description": "A lightweight graphic library providing 2d draw for Apache ECharts", "keywords": ["canvas", "svg", "2d", "html5", "vector-graphics"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepare": "npm run build:lib", "build": "npm run build:bundle && npm run build:lib", "release": "node build/build.js --minify && npm run build:lib", "prepublishOnly": "node build/prepublish.js", "prepare:nightly": "node build/prepareNightly.js", "prepare:nightly-next": "node build/prepareNightly.js --next", "build:bundle": "node build/build.js", "build:lib": "npx tsc -m ES2015 --outDir lib && node build/processLib.js", "watch:bundle": "node build/build.js --watch", "watch:lib": "npx tsc-watch -m ES2015 --outDir lib --synchronousWatchDirectory --onSuccess \"node build/processLib.js\"", "test": "npx jest --config test/ut/jest.config.js", "lint": "npx eslint src/**/*.ts"}, "license": "BSD-3-<PERSON><PERSON>", "types": "index.d.ts", "module": "index.js", "main": "dist/zrender.js", "dependencies": {"tslib": "2.3.0"}, "sideEffects": ["lib/canvas/canvas.js", "lib/svg/svg.js"], "devDependencies": {"@microsoft/api-extractor": "^7.7.2", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^3.0.0", "@types/jest": "^27.0.2", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chalk": "^3.0.0", "commander": "2.11.0", "eslint": "6.3.0", "fs-extra": "4.0.2", "globby": "^11.0.4", "jest": "^27.2.5", "jsdom": "^16.0.0", "rollup": "^1.28.0", "rollup-plugin-typescript2": "^0.25.3", "rollup-plugin-uglify": "^6.0.4", "ts-jest": "^27.0.6", "typescript": "^4.4.3", "uglify-js": "^3.10.0"}, "gitHead": "55a95028f2ff385bbc696a690510ab7c62b81b36", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@5.4.4", "_nodeVersion": "16.19.1", "_npmVersion": "9.4.0", "dist": {"integrity": "sha512-0VxCNJ7AGOMCWeHVyTrGzUgrK4asT4ml9PEkeGirAkKNYXYzoPJCLvmyfdoOXcjTHPs10OZVMfD1Rwg16AZyYw==", "shasum": "8854f1d95ecc82cf8912f5a11f86657cb8c9e261", "tarball": "https://registry.npmmirror.com/zrender/-/zrender-5.4.4.tgz", "fileCount": 371, "unpackedSize": 4104033, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF1DubAWZ6t4wkBwI9JObtZluAWHLenueaB5gXyx8zMoAiEAsI70/aOI6ZwJW+fRMQKRYmSaJKa3HbtdJX686yVC/9M="}], "size": 854665}, "_npmUser": {"name": "o<PERSON>a", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zttonly", "email": "<EMAIL>"}, {"name": "gkiwi001", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "ecomfe-admin", "email": "<EMAIL>"}, {"name": "otakustay", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "luyuan", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "jinz<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_5.4.4_1689152214463_0.4544349474086624"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-12T08:56:54.698Z", "publish_time": 1689152214698, "_source_registry_name": "default"}, "5.5.0": {"name": "zrender", "version": "5.5.0", "description": "A lightweight graphic library providing 2d draw for Apache ECharts", "keywords": ["canvas", "svg", "2d", "html5", "vector-graphics"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepare": "npm run build:lib", "build": "npm run build:bundle && npm run build:lib", "release": "node build/build.js --minify && npm run build:lib", "prepublishOnly": "node build/prepublish.js", "prepare:nightly": "node build/prepareNightly.js", "prepare:nightly-next": "node build/prepareNightly.js --next", "build:bundle": "node build/build.js", "build:lib": "npx tsc -m ES2015 --outDir lib && node build/processLib.js", "watch:bundle": "node build/build.js --watch", "watch:lib": "npx tsc-watch -m ES2015 --outDir lib --synchronousWatchDirectory --onSuccess \"node build/processLib.js\"", "test": "npx jest --config test/ut/jest.config.js", "lint": "npx eslint src/**/*.ts"}, "license": "BSD-3-<PERSON><PERSON>", "types": "index.d.ts", "module": "index.js", "main": "dist/zrender.js", "dependencies": {"tslib": "2.3.0"}, "sideEffects": ["lib/canvas/canvas.js", "lib/svg/svg.js", "lib/all.js"], "devDependencies": {"@microsoft/api-extractor": "^7.7.2", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^3.0.0", "@types/jest": "^27.0.2", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chalk": "^3.0.0", "commander": "2.11.0", "eslint": "6.3.0", "fs-extra": "4.0.2", "globby": "^11.0.4", "jest": "^27.2.5", "jsdom": "^16.0.0", "rollup": "^1.28.0", "rollup-plugin-typescript2": "^0.25.3", "rollup-plugin-uglify": "^6.0.4", "ts-jest": "^27.0.6", "typescript": "^4.4.3", "uglify-js": "^3.10.0"}, "type": "module", "exports": {".": {"types": "./index.d.ts", "require": "./dist/zrender.js", "import": "./index.js"}, "./lib/canvas/canvas": "./lib/canvas/canvas.js", "./lib/svg/svg": "./lib/svg/svg.js", "./lib/vml/vml": "./lib/vml/vml.js", "./lib/canvas/Painter": "./lib/canvas/Painter.js", "./lib/svg/Painter": "./lib/svg/Painter.js", "./lib/svg/patch": "./lib/svg/patch.js", "./lib/Storage": "./lib/Storage.js", "./lib/core/util": "./lib/core/util.js", "./lib/core/env": "./lib/core/env.js", "./lib/core/Transformable": "./lib/core/Transformable.js", "./lib/core/BoundingRect": "./lib/core/BoundingRect.js", "./lib/core/vector": "./lib/core/vector.js", "./lib/core/bbox": "./lib/core/bbox.js", "./lib/contain/polygon": "./lib/contain/polygon.js", "./lib/tool/color": "./lib/tool/color.js", "./lib/graphic/LinearGradient": "./lib/graphic/LinearGradient.js", "./lib/graphic/RadialGradient": "./lib/graphic/RadialGradient.js", "./*": "./*"}, "gitHead": "223035d9ada9c56b86dc65b7abc70a961471bc96", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@5.5.0", "_nodeVersion": "16.19.1", "_npmVersion": "9.4.0", "dist": {"integrity": "sha512-O3MilSi/9mwoovx77m6ROZM7sXShR/O/JIanvzTwjN3FORfLSr81PsUGd7jlaYOeds9d8tw82oP44+3YucVo+w==", "shasum": "54d0d6c4eda81a96d9f60a9cd74dc48ea026bc1e", "tarball": "https://registry.npmmirror.com/zrender/-/zrender-5.5.0.tgz", "fileCount": 379, "unpackedSize": 4086229, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDXuQpFbE+Alc+etg3JPshFgscDzQaG/zqnHSOhiPlxPAIhAO0vDQA0KhlzBHF6HanK7Xe6+aZ/qxEzQoLMfHuWRJyV"}], "size": 860415}, "_npmUser": {"name": "o<PERSON>a", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zttonly", "email": "<EMAIL>"}, {"name": "gkiwi001", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "ecomfe-admin", "email": "<EMAIL>"}, {"name": "otakustay", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "luyuan", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "jinz<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_5.5.0_1706589523116_0.9403280650052899"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-30T04:38:43.354Z", "publish_time": 1706589523354, "_source_registry_name": "default"}, "5.6.0": {"name": "zrender", "version": "5.6.0", "description": "A lightweight graphic library providing 2d draw for Apache ECharts", "keywords": ["canvas", "svg", "2d", "html5", "vector-graphics"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepare": "npm run build:lib", "build": "npm run build:bundle && npm run build:lib", "release": "node build/build.js --minify && npm run build:lib", "prepublishOnly": "node build/prepublish.js", "prepare:nightly": "node build/prepareNightly.js", "prepare:nightly-next": "node build/prepareNightly.js --next", "build:bundle": "node build/build.js", "build:lib": "npx tsc -m ES2015 --outDir lib && node build/processLib.js", "watch:bundle": "node build/build.js --watch", "watch:lib": "npx tsc-watch -m ES2015 --outDir lib --synchronousWatchDirectory --onSuccess \"node build/processLib.js\"", "test": "npx jest --config test/ut/jest.config.js", "lint": "npx eslint src/**/*.ts"}, "license": "BSD-3-<PERSON><PERSON>", "types": "index.d.ts", "module": "index.js", "main": "dist/zrender.js", "dependencies": {"tslib": "2.3.0"}, "sideEffects": ["lib/canvas/canvas.js", "lib/svg/svg.js", "lib/all.js"], "devDependencies": {"@microsoft/api-extractor": "^7.7.2", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^3.0.0", "@types/jest": "^27.0.2", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chalk": "^3.0.0", "commander": "2.11.0", "eslint": "6.3.0", "fs-extra": "4.0.2", "globby": "^11.0.4", "jest": "^27.2.5", "jsdom": "^16.0.0", "rollup": "^1.28.0", "rollup-plugin-typescript2": "^0.25.3", "rollup-plugin-uglify": "^6.0.4", "ts-jest": "^27.0.6", "typescript": "^4.4.3", "uglify-js": "^3.10.0"}, "type": "module", "exports": {".": {"types": "./index.d.ts", "require": "./dist/zrender.js", "import": "./index.js"}, "./lib/canvas/canvas": "./lib/canvas/canvas.js", "./lib/svg/svg": "./lib/svg/svg.js", "./lib/vml/vml": "./lib/vml/vml.js", "./lib/canvas/Painter": "./lib/canvas/Painter.js", "./lib/svg/Painter": "./lib/svg/Painter.js", "./lib/svg/patch": "./lib/svg/patch.js", "./lib/Storage": "./lib/Storage.js", "./lib/core/util": "./lib/core/util.js", "./lib/core/env": "./lib/core/env.js", "./lib/core/Transformable": "./lib/core/Transformable.js", "./lib/core/BoundingRect": "./lib/core/BoundingRect.js", "./lib/core/vector": "./lib/core/vector.js", "./lib/core/bbox": "./lib/core/bbox.js", "./lib/contain/polygon": "./lib/contain/polygon.js", "./lib/tool/color": "./lib/tool/color.js", "./lib/graphic/LinearGradient": "./lib/graphic/LinearGradient.js", "./lib/graphic/RadialGradient": "./lib/graphic/RadialGradient.js", "./*": "./*"}, "gitHead": "d3e0e17569c64f514258269622f99799148d095d", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_id": "zrender@5.6.0", "_nodeVersion": "18.15.0", "_npmVersion": "9.5.0", "dist": {"integrity": "sha512-uzgraf4njmmHAbEUxMJ8Oxg+P3fT04O+9p7gY+wJRVxo8Ge+KmYv0WJev945EH4wFuc4OY2NLXz46FZrWS9xJg==", "shasum": "01325b0bb38332dd5e87a8dbee7336cafc0f4a5b", "tarball": "https://registry.npmmirror.com/zrender/-/zrender-5.6.0.tgz", "fileCount": 379, "unpackedSize": 4086567, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFiWpJ7vNBl/+LIGTmla382PMFZ9IDY0tiWV8fQ61ffkAiBsGf++yf7QUu4bxnXPuen9uFrTZAWklK1VvXZ54O3q0w=="}], "size": 860568}, "_npmUser": {"name": "lang", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fuqiang15", "email": "<EMAIL>"}, {"name": "xdbobname", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zttonly", "email": "<EMAIL>"}, {"name": "gkiwi001", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "ecomfe-admin", "email": "<EMAIL>"}, {"name": "otakustay", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "luyuan", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "jinz<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/zrender_5.6.0_1718635282607_0.6077490157618259"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-17T14:41:22.848Z", "publish_time": 1718635282848, "_source_registry_name": "default"}, "4.3.3": {"name": "zrender", "version": "4.3.3", "keywords": ["canvas", "2d"], "license": "BSD-3-<PERSON><PERSON>", "_id": "zrender@4.3.3", "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "ecomfe-admin", "email": "<EMAIL>"}, {"name": "otakustay", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "luyuan", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "jinz<PERSON>", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "gkiwi001", "email": "<EMAIL>"}, {"name": "zttonly", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xdbobname", "email": "<EMAIL>"}, {"name": "fuqiang15", "email": "<EMAIL>"}], "homepage": "https://github.com/ecomfe/zrender#readme", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "dist": {"shasum": "f5cab6d15a43024c1fd289ec7072d41464dd913a", "tarball": "https://registry.npmmirror.com/zrender/-/zrender-4.3.3.tgz", "fileCount": 228, "integrity": "sha512-LeAlZBxlGyux6Y3+yoTJyCenQBoIMriDYmtvQL169t3IOPm20zNbkM24KGRZlgUqo/tTZmT3hDo1ptxwOuxy6g==", "signatures": [{"sig": "MEQCIEzBjCMppQ6yuuquEpQJB7rhtsU6y36Vx9IbLslBzMe1AiBeh8jzXiIPJQ2pkj+iskbVblQ04SVSwqiywa1N4GaqOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3439563, "size": 731176}, "gitHead": "4ccacea7613482f016a913126574b4802cc7fc19", "scripts": {"dev": "node build/build.js --watch", "lint": "eslint src", "test": "node build/build.js", "build": "node build/build.js --release", "prepublish": "node build/build.js --prepublish"}, "_npmUser": {"name": "100pah", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ecomfe/zrender.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "A lightweight canvas library.", "directories": {}, "_nodeVersion": "12.22.12", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"assert": "1.4.1", "eslint": "6.3.0", "jsdiff": "1.1.1", "rollup": "0.50.0", "fs-extra": "4.0.2", "commander": "2.11.0", "@babel/core": "7.3.4", "@babel/types": "7.0.0-beta.31", "@babel/template": "7.2.2", "rollup-plugin-uglify": "2.0.1", "@babel/helper-simple-access": "7.1.0", "@babel/helper-module-transforms": "7.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/zrender_4.3.3_1733497731296_0.08071568939220541", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-12-06T15:08:51.688Z", "publish_time": 1733497731688, "_source_registry_name": "default"}, "5.6.1": {"name": "zrender", "version": "5.6.1", "description": "A lightweight graphic library providing 2d draw for Apache ECharts", "keywords": ["canvas", "svg", "2d", "html5", "vector-graphics"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepare": "npm run build:lib", "build": "npm run build:bundle && npm run build:lib", "release": "node build/build.js --minify && npm run build:lib", "prepublishOnly": "node build/prepublish.js", "prepare:nightly": "node build/prepareNightly.js", "prepare:nightly-next": "node build/prepareNightly.js --next", "build:bundle": "node build/build.js", "build:lib": "npx tsc -m ES2015 --outDir lib && node build/processLib.js", "watch:bundle": "node build/build.js --watch", "watch:lib": "npx tsc-watch -m ES2015 --outDir lib --synchronousWatchDirectory --onSuccess \"node build/processLib.js\"", "test": "npx jest --config test/ut/jest.config.js", "lint": "npx eslint src/**/*.ts"}, "license": "BSD-3-<PERSON><PERSON>", "types": "index.d.ts", "module": "index.js", "main": "dist/zrender.js", "dependencies": {"tslib": "2.3.0"}, "sideEffects": ["lib/canvas/canvas.js", "lib/svg/svg.js", "lib/all.js"], "devDependencies": {"@microsoft/api-extractor": "^7.7.2", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^3.0.0", "@types/jest": "^27.0.2", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chalk": "^3.0.0", "commander": "2.11.0", "eslint": "6.3.0", "fs-extra": "4.0.2", "globby": "^11.0.4", "jest": "^27.2.5", "jsdom": "^16.0.0", "rollup": "^1.28.0", "rollup-plugin-typescript2": "^0.25.3", "rollup-plugin-uglify": "^6.0.4", "ts-jest": "^27.0.6", "typescript": "^4.4.3", "uglify-js": "^3.10.0"}, "type": "module", "exports": {".": {"types": "./index.d.ts", "require": "./dist/zrender.js", "import": "./index.js"}, "./lib/canvas/canvas": "./lib/canvas/canvas.js", "./lib/svg/svg": "./lib/svg/svg.js", "./lib/vml/vml": "./lib/vml/vml.js", "./lib/canvas/Painter": "./lib/canvas/Painter.js", "./lib/svg/Painter": "./lib/svg/Painter.js", "./lib/svg/patch": "./lib/svg/patch.js", "./lib/Storage": "./lib/Storage.js", "./lib/core/util": "./lib/core/util.js", "./lib/core/env": "./lib/core/env.js", "./lib/core/Transformable": "./lib/core/Transformable.js", "./lib/core/BoundingRect": "./lib/core/BoundingRect.js", "./lib/core/vector": "./lib/core/vector.js", "./lib/core/bbox": "./lib/core/bbox.js", "./lib/contain/polygon": "./lib/contain/polygon.js", "./lib/tool/color": "./lib/tool/color.js", "./lib/graphic/LinearGradient": "./lib/graphic/LinearGradient.js", "./lib/graphic/RadialGradient": "./lib/graphic/RadialGradient.js", "./*": "./*"}, "_id": "zrender@5.6.1", "gitHead": "7bbbdbed7e7eaa371bc66c4696b3c7c2a8a8ec97", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-OFXkDJKcrlx5su2XbzJvj/34Q3m6PvyCZkVPHGYpcCJ52ek4U/ymZyfuV1nKE23AyBJ51E/6Yr0mhZ7xGTO4ag==", "shasum": "e08d57ecf4acac708c4fcb7481eb201df7f10a6b", "tarball": "https://registry.npmmirror.com/zrender/-/zrender-5.6.1.tgz", "fileCount": 376, "unpackedSize": 4116415, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDgQ9DpM40EezfI2WGdDeGRQ+hPqt4iAm4imvC51haFDgIgTLW7tO9d+/wXtQUHqOB0XvzpIWL3DOCHcbXo0KB12ck="}], "size": 866166}, "_npmUser": {"name": "o<PERSON>a", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "ecomfe-admin", "email": "<EMAIL>"}, {"name": "otakustay", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "luyuan", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "jinz<PERSON>", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "gkiwi001", "email": "<EMAIL>"}, {"name": "zttonly", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xdbobname", "email": "<EMAIL>"}, {"name": "fuqiang15", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/zrender_5.6.1_1733886713873_0.09711374308367104"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-11T03:11:54.227Z", "publish_time": 1733886714227, "_source_registry_name": "default"}, "6.0.0-rc.1": {"name": "zrender", "version": "6.0.0-rc.1", "description": "A lightweight graphic library providing 2d draw for Apache ECharts", "keywords": ["canvas", "svg", "2d", "html5", "vector-graphics"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "scripts": {"prepare": "npm run build:lib", "build": "npm run build:bundle && npm run build:lib", "release": "node build/build.js --minify && npm run build:lib", "prepublishOnly": "node build/prepublish.js", "prepare:nightly": "node build/prepareNightly.js", "prepare:nightly-next": "node build/prepareNightly.js --next", "build:bundle": "node build/build.js", "build:lib": "npx tsc -m ES2015 --outDir lib && node build/processLib.js", "watch:bundle": "node build/build.js --watch", "watch:lib": "npx tsc-watch -m ES2015 --outDir lib --synchronousWatchDirectory --onSuccess \"node build/processLib.js\"", "test": "npx jest --config test/ut/jest.config.js", "lint": "npx eslint src/**/*.ts", "checktype": "tsc --noEmit"}, "license": "BSD-3-<PERSON><PERSON>", "types": "index.d.ts", "module": "index.js", "main": "dist/zrender.js", "dependencies": {"tslib": "2.3.0"}, "sideEffects": ["lib/canvas/canvas.js", "lib/svg/svg.js", "lib/all.js"], "devDependencies": {"@microsoft/api-extractor": "^7.7.2", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^3.0.0", "@types/jest": "^27.0.2", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chalk": "^3.0.0", "commander": "2.11.0", "eslint": "6.3.0", "fs-extra": "4.0.2", "globby": "^11.0.4", "jest": "^27.2.5", "jsdom": "^16.0.0", "rollup": "^1.28.0", "rollup-plugin-typescript2": "^0.25.3", "rollup-plugin-uglify": "^6.0.4", "ts-jest": "^27.0.6", "typescript": "^4.4.3", "uglify-js": "^3.10.0"}, "type": "module", "exports": {".": {"types": "./index.d.ts", "require": "./dist/zrender.js", "import": "./index.js"}, "./lib/canvas/canvas": "./lib/canvas/canvas.js", "./lib/svg/svg": "./lib/svg/svg.js", "./lib/vml/vml": "./lib/vml/vml.js", "./lib/canvas/Painter": "./lib/canvas/Painter.js", "./lib/svg/Painter": "./lib/svg/Painter.js", "./lib/svg/patch": "./lib/svg/patch.js", "./lib/Storage": "./lib/Storage.js", "./lib/core/util": "./lib/core/util.js", "./lib/core/env": "./lib/core/env.js", "./lib/core/Transformable": "./lib/core/Transformable.js", "./lib/core/BoundingRect": "./lib/core/BoundingRect.js", "./lib/core/vector": "./lib/core/vector.js", "./lib/core/bbox": "./lib/core/bbox.js", "./lib/contain/polygon": "./lib/contain/polygon.js", "./lib/tool/color": "./lib/tool/color.js", "./lib/graphic/LinearGradient": "./lib/graphic/LinearGradient.js", "./lib/graphic/RadialGradient": "./lib/graphic/RadialGradient.js", "./*": "./*"}, "_id": "zrender@6.0.0-rc.1", "readmeFilename": "README.md", "gitHead": "4cb9642e218d3ce1c74616a33ba984558e5360e3", "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "_nodeVersion": "22.12.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-DWYxDvSHb69PlZ9bs2C4NHt0xHMojHztGForDFAiNSzw9XDwycwXAhJydFrNyq/vy0I8usTZ+KbtZyrX+6ePJQ==", "shasum": "c5415af95f267a5dc27048eb0783b4f7bc29f4aa", "tarball": "https://registry.npmmirror.com/zrender/-/zrender-6.0.0-rc.1.tgz", "fileCount": 380, "unpackedSize": 4191272, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIH5ITiU6uA0VKv1H2y82vCiX3G4U89z9f98G9bs8/i/KAiEAlIKo0PdZqjWBIJIGmLCv/v3ESobFIyzKClPkBWWrf+Q="}], "size": 887032}, "_npmUser": {"name": "o<PERSON>a", "email": "<EMAIL>", "actor": {"name": "o<PERSON>a", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "ecomfe-core", "email": "<EMAIL>"}, {"name": "ecomfe-admin", "email": "<EMAIL>"}, {"name": "otakustay", "email": "<EMAIL>"}, {"name": "leeight", "email": "<EMAIL>"}, {"name": "justineo", "email": "<EMAIL>"}, {"name": "gdjinbo", "email": "<EMAIL>"}, {"name": "luyuan", "email": "<EMAIL>"}, {"name": "errorrik", "email": "<EMAIL>"}, {"name": "ksky521", "email": "<EMAIL>"}, {"name": "lang", "email": "<EMAIL>"}, {"name": "100pah", "email": "<EMAIL>"}, {"name": "jinz<PERSON>", "email": "<EMAIL>"}, {"name": "o<PERSON>a", "email": "<EMAIL>"}, {"name": "gkiwi001", "email": "<EMAIL>"}, {"name": "zttonly", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xdbobname", "email": "<EMAIL>"}, {"name": "fuqiang15", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/zrender_6.0.0-rc.1_1750849135711_0.8599482159479908"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-25T10:58:55.927Z", "publish_time": 1750849135927, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/ecomfe/zrender/issues"}, "homepage": "https://github.com/ecomfe/zrender#readme", "keywords": ["canvas", "svg", "2d", "html5", "vector-graphics"], "repository": {"type": "git", "url": "git+https://github.com/ecomfe/zrender.git"}, "_source_registry_name": "default"}