<!--src\views\public\course\Course_Center\Chapter\ChapterNode.vue-->
<template>
  <div :id="anchorId" :class="['chapter-node', depth > 0 ? 'nested' : '']">
    <!-- 节点标题行 -->
    <div 
      class="node-header"
      :class="{ 'top-level': depth === 0 }"
      @click="toggleExpanded"
    >
      <!-- 展开/折叠图标 -->
      <span v-if="isExpandable" class="expand-icon" :class="{ 'low-opacity': !hasChildren }">
        <svg 
          v-if="isExpanded"
          xmlns="http://www.w3.org/2000/svg" 
          width="16" 
          height="16" 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke="currentColor" 
          stroke-width="2" 
          stroke-linecap="round" 
          stroke-linejoin="round"
        >
          <polyline points="6 9 12 15 18 9"></polyline>
        </svg>
        <svg 
          v-else
          xmlns="http://www.w3.org/2000/svg" 
          width="16" 
          height="16" 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke="currentColor" 
          stroke-width="2" 
          stroke-linecap="round" 
          stroke-linejoin="round"
        >
          <polyline points="9 18 15 12 9 6"></polyline>
        </svg>
      </span>
      <span v-else class="expand-placeholder"></span>
      
      <!-- 章节编号 -->
      <span class="chapter-number">
        {{ formattedChapterNumber }}
      </span>
      
      <!-- 标题文本 -->
      <span 
        class="title-text"
        :class="{
          'highlighted': isHighlighted,
          'large-text': depth === 0
        }"
      >
        {{ node.title }}
      </span>
      
      <!-- 章节性质标签 -->
      <span class="nature-tag" :class="natureTagClass">
        {{ natureText }}
      </span>
      
      <!-- 资源统计 -->
      <div class="resource-stats">
        <span v-if="resources.length > 0" class="resource-count">
          {{ resources.length }}个资源
        </span>
      </div>
    </div>
    
    <!-- 子节点 -->
    <div v-if="isExpanded && hasChildren" class="children-container">
      <ChapterNode
        v-for="(child, index) in node.children"
        :key="index"
        :node="child"
        :depth="depth + 1"
        :search-query="searchQuery"
        @preview-resource="emit('preview-resource', $event)"
      />
    </div>
    
    <!-- 资源列表 -->
    <div v-if="isExpanded && resources.length > 0" class="resource-list">
      <div 
        v-for="(resource, index) in resources" 
        :key="index"
        class="resource-item"
      >
        <div class="resource-content">
          <span class="resource-type-tag" :class="resourceTypeClass(resource)">
            {{ resourceTypeText(resource) }}
          </span>
          <i :class="resourceIcon(resource)" class="resource-icon"></i>
          <span 
            class="resource-title"
            @click="handleResourceClick(resource)"
          >
            {{ resource.name }}
          </span>
        </div>
        <div class="resource-meta">
          <span>{{ formatFileSize(resource.fileSize) }}</span>
          <span>{{ resource.publisherName }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits } from 'vue'
import { getChapterResources } from '@/api/student/course'

const props = defineProps({
  anchorId: String,
  node: Object,
  depth: { type: Number, default: 0 },
  searchQuery: String
})

// 状态
const isExpanded = ref(false)
const resources = ref([])
const loading = ref(false)

// 映射配置
const NATURE_CONFIG = {
  1: { text: '重点', class: 'important' },
  2: { text: '难点', class: 'difficult' },
  3: { text: '普通', class: 'normal' }
}

const RESOURCE_CONFIG = {
  1: { text: '#视频', class: 'video', icon: 'el-icon-video-camera' },
  2: { text: '#教材', class: 'textbook', icon: 'el-icon-notebook-2' },
  3: { text: '#PPT', class: 'ppt', icon: 'el-icon-data-analysis' },
  4: { text: '#Word', class: 'word', icon: 'el-icon-document' },
  5: { text: '#外部资源', class: 'external', icon: 'el-icon-link' },
  6: { text: '#其他', class: 'other', icon: 'el-icon-files' }
}

const emit = defineEmits(['preview-resource'])

// 计算属性
const hasChildren = computed(() => props.node.children?.length > 0)
const isExpandable = computed(() => hasChildren.value || props.node.id)
const isHighlighted = computed(() => 
  props.searchQuery && props.node.title.toLowerCase().includes(props.searchQuery.toLowerCase())
)
const formattedChapterNumber = computed(() => 
  props.depth === 0 ? `第${props.node.chapterNumber}章` : `${props.node.chapterNumber}.${props.node.sortOrder}`
)
const natureText = computed(() => NATURE_CONFIG[props.node.nature]?.text || '普通')
const natureTagClass = computed(() => NATURE_CONFIG[props.node.nature]?.class || 'normal')

// 资源相关方法
const resourceTypeText = (resource) => RESOURCE_CONFIG[resource.type]?.text || '其他'
const resourceTypeClass = (resource) => {
  const type = resource.type || 6; // 默认为其他类型
  return `resource-type-${type}`; // 生成类名如 resource-type-1, resource-type-2 等
}
const resourceIcon = (resource) => RESOURCE_CONFIG[resource.type]?.icon || 'el-icon-question'

const formatFileSize = (bytes) => {
  if (!bytes) return '未知大小'
  const units = ['B', 'KB', 'MB', 'GB']
  let size = bytes
  let unitIndex = 0
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }
  return `${size.toFixed(1)}${units[unitIndex]}`
}

const openResource = (url) => {
  if (url) window.open(url, '_blank')
}

const toggleExpanded = async () => {
  if (!isExpandable.value) return
  
  isExpanded.value = !isExpanded.value
  if (isExpanded.value && !resources.value.length) {
    await fetchResources()
  }
}

const fetchResources = async () => {
  if (!props.node.id) return
  
  loading.value = true
  try {
    const res = await getChapterResources(props.node.courseId, props.node.id)
    if (res.code === 200) {
      resources.value = res.result || []
    }
  } finally {
    loading.value = false
  }
}

// 处理资源点击事件（优先预览，无URL则打开链接）
const handleResourceClick = (resource) => {
  // 如果有URL，触发预览事件；否则仍执行原打开链接逻辑
  if (resource.url) {
    emit('preview-resource', resource)  // 向父组件传递要预览的资源
  } else {
    openResource(resource.url)
  }
}

</script>

<style lang="scss" scoped>
.chapter-node {
  margin-left: 20px;
  
  &.nested {
    border-left: 2px solid #e5e7eb;
    padding-left: 1rem;
  }
}

.node-header {
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background-color 200ms;
  
  &:hover {
    background-color: #f9fafb;
  }
  
  &.top-level {
    background-color: #f5f5ff;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    
    &:hover {
      background-color: #f1f1fd;
    }
  }
}

.expand-icon {
  margin-right: 0.5rem;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1rem;
  height: 1rem;
  
  svg {
    transition: transform 0.2s;
  }
  
  &.low-opacity {
    opacity: 0.3;
  }
}

.expand-placeholder {
  margin-right: 0.5rem;
  width: 1rem;
}

.type-icon {
  margin-right: 0.5rem;
  color: #6b7280;
  
  .icon-small {
    font-size: 0.875rem;
  }
}

.title-text {
  font-weight: 500;
  color: #1f2937;
  
  
  &.highlighted {
    color: #2563eb;
  }
  
  &.large-text {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

.resource-stats {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.resource-count {
  padding: 0.125rem 0.375rem;
  background-color: #d1d1ff;
  color: $primary-color;
  border-radius: 9999px;
  font-size: 0.75rem;
}

.resource-type-stats {
  display: flex;
  gap: 1rem;
}

.resource-type-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .resource-type-count {
    font-size: 0.875rem;
    font-weight: bold;
  }
  
  .resource-type-label {
    font-size: 0.75rem;
    color: #6b7280;
  }
}

.children-container {
  margin-top: 0.25rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.resource-list {
  margin-top: 0.5rem;
  margin-left: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.resource-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  transition: background-color 200ms;
  


  &:hover {
    background-color: #f9fafb;
  }
}

.resource-content {
  display: flex;
  align-items: center;
  .resource-title{
    cursor: pointer;
    &:hover {
      color: $primary-color;
    }
  }
}

// 在style部分添加或修改以下样式
// 在style部分添加或修改以下样式
.resource-type-tag {
  margin-right: 0.75rem;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-size: 0.85rem;
  font-weight: normal;
  color: #b5b5b5;
}

.resource-icon {
  color: #9ca3af;
  margin-right: 0.5rem;
}

.resource-title {
  font-size: 0.875rem;
  color: #374151;
  max-width: 20rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  
  &.highlighted {
    color: #2563eb;
  }
}

.resource-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.75rem;
  color: #6b7280;
}

.nature-tag {
  margin-left: 0.75rem;
  padding: 0.125rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  
  &.important {
    background-color: #fee2e2;
    color: #b91c1c;
  }
  
  &.difficult {
    background-color: #ffedd5;
    color: #9a3412;
  }
  
  &.normal {
    background-color: #e5e7eb;
    color: #4b5563;
  }
}

</style>