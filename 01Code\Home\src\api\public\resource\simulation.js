import api from '@/api/service.js'

/**
 * 获取虚拟仿真项目列表
 * @param {Object} params - 查询参数
 * @param {number} params.current - 当前页码，默认1
 * @param {number} params.size - 每页数量，默认10
 * @param {string} params.name - 项目名称（可选）
 * @param {string} params.semester - 学期（可选）
 * @param {string} params.subject - 学科（可选）
 * @returns {Promise} API响应
 */
export const getSimulationList = (params = {}) => {
  // 默认参数
  const defaultParams = {
    current: 1,
    size: 10
  };
  
  // 过滤掉空值参数并合并默认参数
  const filteredParams = Object.fromEntries(
    Object.entries({
      ...defaultParams,
      ...params
    }).filter(([_, v]) => v !== '' && v !== undefined && v !== null)
  );
  
  return api({
    url: '/simulation/list',
    method: 'get',
    params: filteredParams
  });
}

/**
 * 获取虚拟仿真项目详情
 * @param {string} simulationId - 仿真项目ID
 * @returns {Promise} API响应
 */
export const getSimulationDetail = (simulationId) => {
  return api({
    url: `http://8.134.236.247:1991/simulation/detail`,
    method: 'get',
    params: { simulationId }
  });
}

/**
 * 获取虚拟仿真项目文档列表
 * @param {string} simulationId - 仿真项目ID
 * @returns {Promise} API响应
 */
export const getSimulationDocumentList = (simulationId) => {
  return api({
    url: `http://8.134.236.247:1991/simulation/document/list`,
    method: 'get',
    params: { simulationId }
  });
}

/**
 * 获取虚拟仿真项目文档详情
 * @param {string} documentId - 文档ID
 * @returns {Promise} API响应
 */
export const getSimulationDocumentDetail = (documentId) => {
  return api({
    url: `http://8.134.236.247:1991/simulation/document/detail`,
    method: 'get',
    params: { documentId }
  });
}

/**
 * 获取虚拟仿真项目指导视频
 * @param {string} simulationId - 仿真项目ID
 * @returns {Promise} API响应
 */
export const getSimulationVideoGuide = (simulationId) => {
  return api({
    url: `http://8.134.236.247:1991/simulation/video/guide`,
    method: 'get',
    params: { simulationId }
  });
}

/**
 * 获取虚拟仿真项目介绍视频
 * @param {string} simulationId - 仿真项目ID
 * @returns {Promise} API响应
 */
export const getSimulationVideoIntro = (simulationId) => {
  return api({
    url: `http://8.134.236.247:1991/simulation/video/intro`,
    method: 'get',
    params: { simulationId }
  });
}

/**
 * 获取虚拟仿真项目视频列表
 * @param {string} simulationId - 仿真项目ID
 * @returns {Promise} API响应
 */
export const getSimulationVideoList = (simulationId) => {
  return api({
    url: `http://8.134.236.247:1991/simulation/video/list`,
    method: 'get',
    params: { simulationId }
  });
}
