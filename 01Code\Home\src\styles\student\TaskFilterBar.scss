@use '@/styles/variables' as *;

.task-filter-bar {
  width: 100%;
  padding: 12px 16px;
  border-radius: 4px;
  box-sizing: border-box; // 确保padding不影响宽度计算
  
  .filter-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
    width: 100%;
    flex-wrap: nowrap;
    
    .status-group {
      display: flex;
      align-items: center;
      flex-wrap: nowrap; // 禁止换行
      gap: 8px;
      flex-shrink: 1;
      min-width: 0; // 允许收缩
      
      .group-label {
        margin-right: 8px;
        font-weight: 500;
        color: #333;
        flex-shrink: 0;
      }
      
      span {
        padding: 8px 12px;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s;
        flex-shrink: 0;
        white-space: nowrap;
        
        &:hover {
          background-color: $hover-color;
        }
        
        &.active {
          background-color: $primary-color;
          color: white;
        }
      }
    }
    
    .search-box {
      display: flex;
      align-items: center;
      min-width: 200px;
      width: 30%;
      flex-shrink: 0;
      
      input {
        flex: 1;
        padding: 8px 32px 8px 12px;
        border: 1px solid $border-color;
        border-radius: 4px;
        outline: none;
        transition: border-color 0.2s;
        min-width: 120px;
        
        &:focus {
          border-color: $primary-color;
        }
      }
      
      button {
        margin-left: -28px;
        background: none;
        border: none;
        padding: 0;
        cursor: pointer;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        
        .search-icon {
          width: 100%;
          height: 100%;
          filter: invert(100%);
          transition: filter 0.2s;
        }
        
        &:hover .search-icon {
          filter: invert(50%) sepia(100%) saturate(1000%) hue-rotate(200deg);
        }
      }
    }
  }
}