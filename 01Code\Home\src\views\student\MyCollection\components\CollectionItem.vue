<template>
  <!-- 父文件夹行 -->
  <tr :class="['collection-item', { selected: isSelected }]">
    <td class="checkbox-column">
      <input
        type="checkbox"
        :checked="isSelected"
        @change="$emit('select-change', item.id)"
      >
    </td>
    <td>
      <div v-if="item.type === 'folder'" class="folder-icon" @click="toggleFolder">
        <span v-if="hasChildren">{{ item.expanded ? '▼' : '▶' }}</span>
        📁
      </div>
      <div v-else class="file-icon">📄</div>
      <template v-if="renamingItem && renamingItem.id === item.id">
        <RenameInput
          :visible="true"
          :originalName="item.name"
          @confirm="$emit('rename', $event)"
          @cancel="cancelRename"
        />
      </template>
      <template v-else>
        <span class="item-name" @click="startRename">{{ item.name }}</span>
      </template>
    </td>
    <td>
      {{ item.type === 'file' ? item.size : '-' }}
    </td>
    <td>
      {{ formattedDate(item.createdAt) }}
    </td>
  </tr>
  
  <!-- 子文件行（仅当文件夹展开且存在子文件时显示） -->
  <template v-if="item.type === 'folder' && item.expanded && hasChildren">
    <tr 
      v-for="child in item.children" 
      :key="child.id"
      :class="['collection-item', 'child-row', { selected: selectedItems.some(i => i.id === child.id) }]"
    >
      <td class="checkbox-column child-checkbox-column">
        <input
          type="checkbox"
          :checked="selectedItems.some(i => i.id === child.id)"
          @change="$emit('select-change', child.id)"
        >
      </td>
      <td class="child-item">
        <div class="file-icon">📄</div>
        <template v-if="renamingItem && renamingItem.id === child.id">
          <RenameInput
            :visible="true"
            :originalName="child.name"
            @confirm="$emit('rename', $event)"
            @cancel="cancelRename"
          />
        </template>
        <template v-else>
          <span class="item-name" @click="startRename(child)">{{ child.name }}</span>
        </template>
      </td>
      <td>
        {{ child.size }}
      </td>
      <td>
        {{ formattedDate(child.createdAt) }}
      </td>
    </tr>
  </template>
</template>

<script>
import RenameInput from './RenameInput.vue';

export default {
  name: 'CollectionItem',
  components: {
    RenameInput
  },
  props: {
    item: {
      type: Object,
      required: true
    },
    isSelected: {
      type: Boolean,
      default: false
    },
    selectedItems: {
      type: Array,
      default: () => []
    },
    renamingItem: {
      type: Object,
      default: null
    }
  },
  emits: ['select-change', 'rename', 'download', 'delete', 'toggle-folder'],
  computed: {
    hasChildren() {
      return this.item.children && this.item.children.length > 0;
    }
  },
  methods: {
    formattedDate(date) {
      return new Date(date).toLocaleString();
    },
    toggleFolder() {
      if (this.hasChildren) {
        this.$emit('toggle-folder', this.item.id);
      }
    },
    startRename(targetItem = this.item) {
      if (this.selectedItems.some(i => i.id === targetItem.id)) {
        // 这里可以触发父组件的重命名逻辑
      }
    },
    cancelRename() {
      this.$emit('rename', this.item.name);
    }
  }
}
</script>

<style scoped>
.collection-item.selected,
.child-row.selected {
  background-color: #e6f3ff;
}

.collection-item td,
.child-row td {
  padding-top: 12px;
  padding-bottom: 12px;
  vertical-align: middle;
}

.folder-icon, .file-icon {
  margin-right: 10px;
  color: #4f4f4f;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
}

.item-name {
  display: inline-block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
}

.checkbox-column {
  width: 30px;
  text-align: center;
  vertical-align: middle;
}

.child-checkbox-column {
  padding-left: 30px;
}

.child-item {
  padding-left: 15px;
}

tr {
  vertical-align: middle;
}
</style>