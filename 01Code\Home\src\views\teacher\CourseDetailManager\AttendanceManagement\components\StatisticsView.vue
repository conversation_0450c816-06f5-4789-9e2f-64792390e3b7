<template>
  <div class="score-container">
    <!-- 标题和操作按钮 -->
    <div class="header flex justify-between items-center mb-4">
      <h1 class="text-xl font-bold">总成绩</h1>
      <div class="flex space-x-2">
        <el-button type="primary" :icon="Download" @click="handleDownload">
          下载总成绩
        </el-button>
        <el-button type="primary" :icon="Setting" @click="handleWeightSettings">
          成绩加权设置
        </el-button>
        <el-button type="primary" :icon="Box" @click="handleArchive">
          成绩归档封存
        </el-button>
      </div>
    </div>

    <!-- 专业名称和图表标题 -->
    <div class="text-lg font-medium mb-2">数字媒体技术</div>
    <div class="text-md font-medium mb-4">总成绩分布</div>

    <!-- 统计数据 -->
    <div class="flex justify-between items-center mb-4">
      <div></div>
      <div class="flex space-x-6">
        <div class="flex items-center">
          <span class="text-gray-600 mr-2">平均分：</span>
          <span class="font-semibold">{{ averageScore.toFixed(1) }}</span>
        </div>
        <div class="flex items-center">
          <span class="text-gray-600 mr-2">最高分：</span>
          <span class="font-semibold">{{ maxScore }}</span>
        </div>
        <div class="flex items-center">
          <span class="text-gray-600 mr-2">最低分：</span>
          <span class="font-semibold">{{ minScore }}</span>
        </div>
      </div>
    </div>

    <!-- 成绩分布图 -->
    <div class="chart-container">
      <div class="chart-header">
        <div class="chart-legend">
          <div class="legend-item">
            <span class="legend-color" style="background-color: #409EFF"></span>
            <span class="legend-text">学生人数</span>
          </div>
        </div>
      </div>
      <div ref="chartRef" style="height: 400px;"></div>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted, onUnmounted } from 'vue';
import * as echarts from 'echarts';
import { ElButton } from 'element-plus';
import { Download, Setting, Box } from '@element-plus/icons-vue';
import 'element-plus/dist/index.css';

export default defineComponent({
  name: 'ScoreStatistics',
  components: {
    ElButton,
    Download,
    Setting,
    Box
  },
  setup() {
    const chartRef = ref(null);
    const chartInstance = ref(null);
    const averageScore = ref(0);
    const maxScore = ref(0);
    const minScore = ref(0);
    const scoreData = ref([]);

    // 生成模拟成绩数据 - 正态分布
    const generateScoreData = () => {
      const data = [];
      const mean = 75; // 平均分
      const stdDev = 15; // 标准差
      
      // 生成150个学生的成绩，近似正态分布
      for (let i = 0; i < 150; i++) {
        let score;
        do {
          // Box-Muller变换生成正态分布
          const u1 = Math.random();
          const u2 = Math.random();
          const z0 = Math.sqrt(-2.0 * Math.log(u1)) * Math.cos(2.0 * Math.PI * u2);
          score = Math.round(z0 * stdDev + mean);
        } while (score < 0 || score > 100); // 确保分数在0-100之间
        data.push(score);
      }
      
      return data;
    };

    // 生成图表数据
    const generateChartData = () => {
      const data = generateScoreData();
      scoreData.value = data;
      
      // 更新统计数据
      averageScore.value = data.reduce((sum, score) => sum + score, 0) / data.length;
      maxScore.value = Math.max(...data);
      minScore.value = Math.min(...data);
      
      // 创建直方图数据 - 10个分数段
      const bins = Array(10).fill(0);
      data.forEach(score => {
        const binIndex = Math.min(Math.floor(score / 10), 9);
        bins[binIndex]++;
      });
      
      return {
        bins,
        xAxisLabels: ['0-10', '11-20', '21-30', '31-40', '41-50', '51-60', '61-70', '71-80', '81-90', '91-100']
      };
    };

    // 初始化图表 - 使用更美观的样式
    const initChart = () => {
      if (!chartRef.value) return;
      
      chartInstance.value = echarts.init(chartRef.value);
      const { bins, xAxisLabels } = generateChartData();
      
      chartInstance.value.setOption({
        title: { 
          text: '成绩分布直方图',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: '{b}: {c}人'
        },
        grid: {
          top: '20%',
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisLabels,
          axisLine: {
            lineStyle: {
              color: '#999'
            }
          },
          axisLabel: {
            color: '#666'
          }
        },
        yAxis: {
          type: 'value',
          name: '学生人数',
          axisLine: {
            show: true,
            lineStyle: {
              color: '#999'
            }
          },
          axisLabel: {
            color: '#666'
          },
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        series: [{
          name: '学生人数',
          data: bins,
          type: 'bar',
          barWidth: '60%',
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#83bff6' },
              { offset: 0.5, color: '#409EFF' },
              { offset: 1, color: '#2c78bb' }
            ]),
            borderRadius: [4, 4, 0, 0]
          },
          emphasis: {
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#6aa8f3' },
                { offset: 0.7, color: '#1e88e5' },
                { offset: 1, color: '#1a75bb' }
              ])
            }
          },
          label: {
            show: true,
            position: 'top',
            color: '#333'
          }
        }]
      });
    };

    onMounted(() => {
      initChart();
      window.addEventListener('resize', () => {
        chartInstance.value?.resize();
      });
    });

    onUnmounted(() => {
      chartInstance.value?.dispose();
      window.removeEventListener('resize', () => {
        chartInstance.value?.resize();
      });
    });

    return {
      chartRef,
      averageScore,
      maxScore,
      minScore,
      handleDownload: () => alert('下载功能'),
      handleWeightSettings: () => alert('设置功能'),
      handleArchive: () => alert('归档功能'),
      Download,
      Setting,
      Box
    };
  }
});
</script>

<style scoped>
.score-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.chart-container {
  margin-top: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 16px;
  background-color: #fff;
}

.chart-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
}

.chart-legend {
  display: flex;
  align-items: center;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-left: 20px;
}

.legend-color {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 6px;
}

.legend-text {
  font-size: 14px;
  color: #666;
}
</style>