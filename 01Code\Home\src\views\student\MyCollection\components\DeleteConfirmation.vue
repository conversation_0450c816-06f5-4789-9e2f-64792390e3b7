<template>
  <div class="modal-overlay" v-if="visible">
    <div class="modal-content">
      <div class="modal-header">
        <h3>确定删除选中的收藏夹吗？</h3>
        <button class="close-btn" @click="handleCancel">×</button>
      </div>
      <div class="modal-body">
        <p>收藏夹: {{ folderName }}</p>
      </div>
      <div class="modal-footer">
        <button class="delete-btn" @click="handleConfirm">删除</button>
        <button class="cancel-btn" @click="handleCancel">取消</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DeleteConfirmation',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    folderName: {
      type: String,
      required: true
    }
  },
  methods: {
    handleConfirm() {
      this.$emit('confirm');
    },
    handleCancel() {
      this.$emit('cancel');
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}
.modal-content {
  background-color: #fff;
  border-radius: 8px;
  width: 400px;
  max-width: 90%;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
}
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}
.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #909399;
}
.modal-body {
  padding: 20px;
}
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}
.confirm-btn, .move-btn, .delete-btn {
  padding: 8px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}
.confirm-btn, .move-btn {
  background-color: #409eff;
  color: white;
}
.confirm-btn:hover, .move-btn:hover {
  background-color: #3a8ee6;
}
.cancel-btn {
  padding: 8px 20px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
  color: #606266;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}
.cancel-btn:hover {
  background-color: #f5f7fa;
}
.delete-btn {
  background-color: #f56c6c;
  color: white;
}
.delete-btn:hover {
  background-color: #e94747;
}
</style>