<template>
  <div class="create-notice">
    <NoticeHeader v-model="title" :isLoading="isSubmitting || noticeStore.isLoading" />
    <EditorAndPreview v-model="content" :disabled="isSubmitting || noticeStore.isLoading" />
    <NoticeFooter @submit="handleSubmit" @cancel="handleCancel" :isLoading="isSubmitting || noticeStore.isLoading"
      :isEditMode="isEditMode" :initialNoticeType="initialNoticeType" 
       :initialClasses="initialClasses" :courseId="route.params.courseId"
      />
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useNoticeStore } from '@/stores/public/teacher/noticeStore'
import { useUserStore } from '@/stores/userStore'
import {saveNotices} from '@/api/teacher/notice'
import NoticeFooter from './components/CreateNoticeFooter.vue'
import NoticeHeader from './components/CreateNoticeHeader.vue'
import EditorAndPreview from './components/QuillEditor.vue'

const title = ref('')
const content = ref('')
const isEditMode = ref(false)
const noticeId = ref(null)
const isSubmitting = ref(false)
const noticeStore = useNoticeStore()
const UserStore = useUserStore()
const router = useRouter()
const route = useRoute()

const selectedNotice = computed(() => noticeStore.selectedNotice);
const initialNoticeType = ref('course');
const initialClasses = ref([])

watch(selectedNotice, (newVal) => {
  if (newVal && isEditMode.value) {
    title.value = newVal.title || ''
    content.value = newVal.content || ''
    const typeKey = Object.keys(noticeStore.NOTICE_TYPES).find(
      key => noticeStore.NOTICE_TYPES[key].value === newVal.noticeType
    )
    initialNoticeType.value = typeKey || 'course'
    initialClasses.value = newVal.classes?.map(c => c.id) || []
  }
}, { immediate: true })

onMounted(() => {
  if (route.params.noticeId) {
    isEditMode.value = true;
    noticeId.value = route.params.noticeId;
    noticeStore.prepareEdit(noticeId.value);
  }


});


const handleSubmit = async ({ type, classes }) => {
  if (!title.value.trim() || !content.value.trim()) {
    alert('标题和内容不能为空');
    return;
  }

  isSubmitting.value = true;
  try {
    const submitData = {
      title: title.value,
      content: content.value,
      noticeType: noticeStore.NOTICE_TYPES[type]?.value || 0,
      classIds: classes,
      courseId: route.params.courseId,
      publisherId: UserStore.user.id
    };

    if (isEditMode.value) {
      submitData.id = noticeId.value;
      await noticeStore.updateNoticeItem(submitData);
      
      alert('公告更新成功！');
    } else {
      await saveNotices(submitData);
      alert('通知发布成功！');
      await noticeStore.fetchTypeCounts()
      noticeStore.selectedNotice = null;
    }

     await noticeStore.fetchNotices({ pageNum: 1 }); 
    router.back();
  } catch (error) {
    console.error('操作失败:', error);
    alert(error.message || '操作失败，请重试');
  } finally {
    isSubmitting.value = false;
  }
};

const handleCancel = () => {
  if (confirm('确定要取消发布吗？未保存的内容将会丢失。')) {
    title.value = '';
    content.value = '';
    // 清空selectedNotice，以防万一
    noticeStore.selectedNotice = null;
    router.back();
  }
};
</script>