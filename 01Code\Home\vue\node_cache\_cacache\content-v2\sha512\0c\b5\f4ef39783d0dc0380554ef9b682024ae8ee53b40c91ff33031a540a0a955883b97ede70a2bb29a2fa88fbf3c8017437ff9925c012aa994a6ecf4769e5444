{"_attachments": {}, "_id": "sugarss", "_rev": "3150-61f14b2fb677e08f5114a398", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "description": "Indent-based CSS syntax for PostCSS", "dist-tags": {"latest": "5.0.0"}, "license": "MIT", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "name": "sugarss", "readme": "# SugarSS\n\n<img align=\"right\" width=\"120\" height=\"155\"\n     title=\"<PERSON><PERSON> logo by <PERSON>\"\n     src=\"http://postcss.github.io/sugarss/logo.svg\">\n\nIndent-based CSS syntax for [PostCSS].\n\n```sass\na\n  color: blue\n\n.multiline,\n.selector\n  box-shadow: 1px 0 9px rgba(0, 0, 0, .4),\n              1px 0 3px rgba(0, 0, 0, .6)\n\n// Mobile\n@media (max-width: 400px)\n  .body\n    padding: 0 10px\n```\n\nAs any PostCSS custom syntax, SugarSS has source map, [stylelint]\nand [postcss-sorting] support out-of-box.\n\nIt was designed to be used with [postcss-simple-vars] and [postcss-nested].\nBut you can use it with any PostCSS plugins\nor use it without any PostCSS plugins.\nWith [postcss-mixins] you can use `@mixin` syntax as in Sass.\n\n<a href=\"https://evilmartians.com/?utm_source=sugarss\">\n  <img src=\"https://evilmartians.com/badges/sponsored-by-evil-martians.svg\"\n       alt=\"Sponsored by Evil Martians\" width=\"236\" height=\"54\">\n</a>\n\n[postcss-mixins]:              https://github.com/postcss/postcss-mixins\n[postcss-nested]:              https://github.com/postcss/postcss-nested\n[postcss-simple-vars]:         https://github.com/postcss/postcss-simple-vars\n[postcss-sorting]:             https://github.com/hudochenkov/postcss-sorting\n[stylelint]:                   http://stylelint.io/\n[PostCSS]:                     https://github.com/postcss/postcss\n\n\n## Docs\nRead full docs **[here](https://github.com/postcss/sugarss#readme)**.\n", "time": {"created": "2022-01-26T13:22:55.683Z", "modified": "2024-11-20T12:26:03.003Z", "4.0.1": "2021-06-14T06:04:19.874Z", "4.0.0": "2021-06-14T05:55:51.569Z", "3.0.3": "2020-11-05T16:40:46.495Z", "3.0.2": "2020-11-05T16:14:28.667Z", "3.0.1": "2020-09-27T00:31:11.781Z", "3.0.0": "2020-09-15T17:05:24.349Z", "2.0.0": "2018-08-11T03:56:19.490Z", "1.0.1": "2017-11-05T18:43:26.608Z", "1.0.0": "2017-05-07T10:50:11.416Z", "0.2.0": "2016-10-07T15:02:10.426Z", "0.1.6": "2016-09-08T03:13:41.695Z", "0.1.5": "2016-07-23T12:56:27.758Z", "0.1.4": "2016-05-30T16:40:30.399Z", "0.1.3": "2016-04-23T07:46:11.580Z", "0.1.2": "2016-03-17T06:16:17.826Z", "0.1.1": "2016-03-15T16:34:12.909Z", "0.1.0": "2016-03-09T00:36:12.200Z", "0.0.1": "2016-03-08T08:24:12.388Z", "5.0.0": "2024-11-20T10:37:58.702Z"}, "versions": {"4.0.1": {"name": "sugarss", "version": "4.0.1", "description": "Indent-based CSS syntax for PostCSS", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/sugarss.git"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}, "engines": {"node": ">=12.0"}, "exports": {".": {"require": "./index.js", "import": "./index.mjs"}, "./parse": "./parse.js", "./stringify": "./stringify.js", "./tokenize": "./tokenize.js", "./package.json": "./package.json"}, "peerDependencies": {"postcss": "^8.3.3"}, "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "homepage": "https://github.com/postcss/sugarss#readme", "_id": "sugarss@4.0.1", "_nodeVersion": "16.3.0", "_npmVersion": "7.15.1", "dist": {"shasum": "128a783ed71ee0fc3b489ce1f7d5a89bc1e24383", "size": 61028, "noattachment": false, "tarball": "https://registry.npmmirror.com/sugarss/-/sugarss-4.0.1.tgz", "integrity": "sha512-WCjS5NfuVJjkQzK10s8WOBY+hhDxxNt/N6ZaGwxFZ+wN3/lKKFSaaKUNecULcTTvE4urLcKaZFQD8vO0mOZujw=="}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sugarss_4.0.1_1623650659682_0.45044453889318103"}, "_hasShrinkwrap": false, "publish_time": 1623650659874, "_cnpm_publish_time": 1623650659874, "_cnpmcore_publish_time": "2021-12-14T04:43:28.035Z"}, "4.0.0": {"name": "sugarss", "version": "4.0.0", "description": "Indent-based CSS syntax for PostCSS", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/sugarss.git"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}, "engines": {"node": ">=12.0"}, "exports": {".": {"require": "./index.js", "import": "./index.mjs"}, "./parse": "./parse.js", "./stringify": "./stringify.js", "./tokenize": "./tokenize.js"}, "peerDependencies": {"postcss": "^8.3.3"}, "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "homepage": "https://github.com/postcss/sugarss#readme", "_id": "sugarss@4.0.0", "_nodeVersion": "16.3.0", "_npmVersion": "7.15.1", "dist": {"shasum": "f27cd3581a629f5945308bba2cff4c0ec3f48559", "size": 61025, "noattachment": false, "tarball": "https://registry.npmmirror.com/sugarss/-/sugarss-4.0.0.tgz", "integrity": "sha512-UGRXeXalq9AnzfA+1cacM3lt29TtBUw21CqGPUNpgkilW6K19uAKZ8s0elCfIskFl8HtS3af23vmx3R95s5WXw=="}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sugarss_4.0.0_1623650151449_0.9584451023609482"}, "_hasShrinkwrap": false, "publish_time": 1623650151569, "_cnpm_publish_time": 1623650151569, "_cnpmcore_publish_time": "2021-12-14T04:43:28.369Z"}, "3.0.3": {"name": "sugarss", "version": "3.0.3", "description": "Indent-based CSS syntax for PostCSS", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/sugarss.git"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}, "dependencies": {"postcss": "^8.1.6"}, "engines": {"node": ">=10.0"}, "exports": {".": {"require": "./index.js", "import": "./index.mjs"}, "./": "./"}, "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "homepage": "https://github.com/postcss/sugarss#readme", "_id": "sugarss@3.0.3", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"shasum": "bb2489961b98fbd15e4e35d6b9f4f2ee5547a6cb", "size": 10405, "noattachment": false, "tarball": "https://registry.npmmirror.com/sugarss/-/sugarss-3.0.3.tgz", "integrity": "sha512-uxa2bbuc+w7ov7DyYIhF6bM0qZF3UkFT5/nE8AJgboiVnKsBDbwxs++dehEIe1JNhpMaGJc37wGQ2QrrWey2Sg=="}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sugarss_3.0.3_1604594446355_0.17258071795762442"}, "_hasShrinkwrap": false, "publish_time": 1604594446495, "_cnpm_publish_time": 1604594446495, "_cnpmcore_publish_time": "2021-12-14T04:43:28.575Z"}, "3.0.2": {"name": "sugarss", "version": "3.0.2", "description": "Indent-based CSS syntax for PostCSS", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/sugarss.git"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}, "dependencies": {"postcss": "^8.1.5"}, "engines": {"node": ">=10.0"}, "exports": {".": {"require": "./index.js", "import": "./index.mjs"}, "./*": "./*"}, "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "homepage": "https://github.com/postcss/sugarss#readme", "_id": "sugarss@3.0.2", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"shasum": "1665ef9ade10acc2ef04e014dead03c4bcc29b90", "size": 10392, "noattachment": false, "tarball": "https://registry.npmmirror.com/sugarss/-/sugarss-3.0.2.tgz", "integrity": "sha512-ZkHjBT8KLsevaVl5gJPgvlBGhwpFrS7/ioGtHk7j3UGtzLM8AJnPgvrD7+ynpgmWXvR9NoMOc0mWfKjaKbcIEg=="}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sugarss_3.0.2_1604592868507_0.6130340186248449"}, "_hasShrinkwrap": false, "publish_time": 1604592868667, "_cnpm_publish_time": 1604592868667, "_cnpmcore_publish_time": "2021-12-14T04:43:28.765Z"}, "3.0.1": {"name": "sugarss", "version": "3.0.1", "description": "Indent-based CSS syntax for PostCSS", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/sugarss.git"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}, "dependencies": {"postcss": "^8.1.0"}, "engines": {"node": ">=10.0"}, "exports": {".": {"require": "./index.js", "import": "./index.mjs"}, "./": "./"}, "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "homepage": "https://github.com/postcss/sugarss#readme", "_id": "sugarss@3.0.1", "_nodeVersion": "14.12.0", "_npmVersion": "6.14.8", "dist": {"shasum": "1e4e315b3b321eec477ef9617c8964bcf3833b0c", "size": 10369, "noattachment": false, "tarball": "https://registry.npmmirror.com/sugarss/-/sugarss-3.0.1.tgz", "integrity": "sha512-xW0tTjuJdd3VSsPH2dLgNDzESka1+Ul3GYVziyhX7GyXQboOARDaeEU++IjhOZPnoKoMENsU0tvtrCKr1sJwlw=="}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sugarss_3.0.1_1601166671638_0.4259563227125567"}, "_hasShrinkwrap": false, "publish_time": 1601166671781, "_cnpm_publish_time": 1601166671781, "_cnpmcore_publish_time": "2021-12-14T04:43:28.957Z"}, "3.0.0": {"name": "sugarss", "version": "3.0.0", "description": "Indent-based CSS syntax for PostCSS", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/sugarss.git"}, "dependencies": {"postcss": "^8.0.2"}, "engines": {"node": ">=10.0"}, "exports": {".": {"require": "./index.js", "import": "./index.mjs"}, "./": "./"}, "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "homepage": "https://github.com/postcss/sugarss#readme", "_id": "sugarss@3.0.0", "_nodeVersion": "14.10.1", "_npmVersion": "6.14.8", "dist": {"shasum": "fb490f9d6f5f60e0a14209a7c8e611d2ddbde489", "size": 10318, "noattachment": false, "tarball": "https://registry.npmmirror.com/sugarss/-/sugarss-3.0.0.tgz", "integrity": "sha512-9FkDUV3kQp7GHv56WHdPR11/OWTS6nf8OWnHrlkxPG9qQaOBlacB/Wb+zK3uBApUGwbwf7CFHQGlmCu3ljF1fQ=="}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sugarss_3.0.0_1600189524211_0.08362728410000697"}, "_hasShrinkwrap": false, "publish_time": 1600189524349, "_cnpm_publish_time": 1600189524349, "_cnpmcore_publish_time": "2021-12-14T04:43:29.152Z"}, "2.0.0": {"name": "sugarss", "version": "2.0.0", "description": "Indent-based CSS syntax for PostCSS", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/sugarss.git"}, "dependencies": {"postcss": "^7.0.2"}, "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "homepage": "https://github.com/postcss/sugarss#readme", "_id": "sugarss@2.0.0", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "dist": {"shasum": "ddd76e0124b297d40bf3cca31c8b22ecb43bc61d", "size": 34966, "noattachment": false, "tarball": "https://registry.npmmirror.com/sugarss/-/sugarss-2.0.0.tgz", "integrity": "sha512-WfxjozUk0UVA4jm+U1d736AUpzSrNsQcIbyOkoE364GrtWmIrFdk5lksEupgWMD4VaT/0kVx1dobpiDumSgmJQ=="}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sugarss_2.0.0_1533959779336_0.6646467809699204"}, "_hasShrinkwrap": false, "publish_time": 1533959779490, "_cnpm_publish_time": 1533959779490, "_cnpmcore_publish_time": "2021-12-14T04:43:29.395Z"}, "1.0.1": {"name": "sugarss", "version": "1.0.1", "description": "Indent-based CSS syntax for PostCSS", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/sugarss.git"}, "dependencies": {"postcss": "^6.0.14"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-eslint": "^8.0.1", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.1.0", "babel-preset-env": "^1.6.1", "babel-preset-stage-0": "^6.24.1", "eslint": "^4.10.0", "eslint-config-postcss": "^2.0.2", "jest": "^21.2.1", "lint-staged": "^4.3.0", "postcss-parser-tests": "^6.1.0", "pre-commit": "^1.2.2"}, "scripts": {"lint-staged": "lint-staged", "prepack": "npm run build", "clean": "rm *.js || echo 'Already cleaned'", "build": "npm run clean && babel -s inline -d ./ *.es6", "lint": "eslint *.es6 test/*.js", "test": "npm run build && jest && npm run lint"}, "babel": {"presets": [["env", {"targets": {"browsers": "last 1 version", "node": 4}, "loose": true}], "stage-0"], "plugins": ["add-module-exports", "precompile-charcodes"]}, "eslintConfig": {"parser": "babel-es<PERSON>", "extends": "eslint-config-postcss", "rules": {"key-spacing": ["error", {"align": "value"}], "complexity": "off"}, "env": {"jest": true}}, "lint-staged": {"test/*.js": "eslint", "*.es6": "eslint"}, "pre-commit": ["lint-staged"], "gitHead": "a5fcc84ed0bae3e257a0f1ce0a6c5360c699b634", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "homepage": "https://github.com/postcss/sugarss#readme", "_id": "sugarss@1.0.1", "_npmVersion": "5.5.1", "_nodeVersion": "9.0.0", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "dist": {"shasum": "be826d9003e0f247735f92365dc3fd7f1bae9e44", "size": 36286, "noattachment": false, "tarball": "https://registry.npmmirror.com/sugarss/-/sugarss-1.0.1.tgz", "integrity": "sha512-3qgLZytikQQEVn1/FrhY7B68gPUUGY3R1Q1vTiD5xT+Ti1DP/8iZuwFet9ONs5+bmL8pZoDQ6JrQHVgrNlK6mA=="}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sugarss-1.0.1.tgz_1509907406533_0.5031481094192713"}, "directories": {}, "publish_time": 1509907406608, "_hasShrinkwrap": false, "_cnpm_publish_time": 1509907406608, "_cnpmcore_publish_time": "2021-12-14T04:43:29.653Z"}, "1.0.0": {"name": "sugarss", "version": "1.0.0", "description": "Indent-based CSS syntax for PostCSS", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/sugarss.git"}, "dependencies": {"postcss": "^6.0.0"}, "devDependencies": {"babel-cli": "^6.24.1", "babel-core": "^6.24.1", "babel-eslint": "^7.2.3", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.0.0", "babel-preset-env": "^1.4.0", "babel-preset-stage-0": "^6.24.1", "eslint": "^3.19.0", "eslint-config-postcss": "^2.0.2", "jest": "^20.0.0", "lint-staged": "^3.4.1", "postcss-parser-tests": "^6.0.0", "pre-commit": "^1.2.2"}, "scripts": {"lint-staged": "lint-staged", "prepublish": "npm run build", "clean": "rm *.js || echo 'Already cleaned'", "build": "npm run clean && babel -s inline -d ./ *.es6", "lint": "eslint *.es6 test/*.js", "test": "npm run build && jest && npm run lint"}, "babel": {"presets": [["env", {"targets": {"browsers": "last 1 version", "node": 4}, "loose": true}], "stage-0"], "plugins": ["add-module-exports", "precompile-charcodes"]}, "eslintConfig": {"parser": "babel-es<PERSON>", "extends": "eslint-config-postcss", "rules": {"key-spacing": ["error", {"align": "value"}], "complexity": "off"}, "env": {"jest": true}}, "lint-staged": {"test/*.js": "eslint", "*.es6": "eslint"}, "pre-commit": ["lint-staged"], "gitHead": "92b0b207bf89fb610fae89bba0b859e5b0e4e8a7", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "homepage": "https://github.com/postcss/sugarss#readme", "_id": "sugarss@1.0.0", "_shasum": "65e51b3958432fb70d5451a68bb33e32d0cf1ef7", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "dist": {"shasum": "65e51b3958432fb70d5451a68bb33e32d0cf1ef7", "size": 35725, "noattachment": false, "tarball": "https://registry.npmmirror.com/sugarss/-/sugarss-1.0.0.tgz", "integrity": "sha512-d+VKMiTOsveF4swm/OriJoQ5zEE0XF+qvEd/84+3UTa9bH/SnrnrN9XBEZz8LtQWI4lLuC9SdxhoLvFDLFxKgA=="}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/sugarss-1.0.0.tgz_1494154209435_0.4844046556390822"}, "directories": {}, "publish_time": 1494154211416, "_hasShrinkwrap": false, "_cnpm_publish_time": 1494154211416, "_cnpmcore_publish_time": "2021-12-14T04:43:29.884Z"}, "0.2.0": {"name": "sugarss", "version": "0.2.0", "description": "Indent-based CSS syntax for PostCSS", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/sugarss.git"}, "dependencies": {"postcss": "^5.2.4"}, "devDependencies": {"babel-plugin-precompile-charcodes": "1.0.0", "babel-plugin-add-module-exports": "0.2.1", "eslint-config-postcss": "2.0.2", "babel-preset-stage-0": "6.16.0", "postcss-parser-tests": "5.0.10", "babel-preset-es2015": "6.16.0", "babel-eslint": "7.0.0", "lint-staged": "3.0.3", "pre-commit": "1.1.3", "babel-core": "6.17.0", "babel-cli": "6.16.0", "eslint": "3.7.1", "ava": "0.16.0"}, "scripts": {"lint-staged": "lint-staged", "prepublish": "npm run build", "clean": "rm *.js || echo 'Already cleaned'", "build": "npm run clean && babel -s inline -d ./ *.es6", "lint": "eslint *.es6 test/*.js", "test": "npm run build && ava && npm run lint"}, "lint-staged": {"test/*.js": "eslint", "*.es6": "eslint"}, "pre-commit": ["lint-staged"], "gitHead": "ded261e3d6cbb2ba779f8152efc9cfd69990c34f", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "homepage": "https://github.com/postcss/sugarss#readme", "_id": "sugarss@0.2.0", "_shasum": "ac34237563327c6ff897b64742bf6aec190ad39e", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.7.0", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "dist": {"shasum": "ac34237563327c6ff897b64742bf6aec190ad39e", "size": 35515, "noattachment": false, "tarball": "https://registry.npmmirror.com/sugarss/-/sugarss-0.2.0.tgz", "integrity": "sha512-gmAdmKbPFALbfAMVqr8lmoIn3HCdiXZMHYpZGbdbAiflg+YGqAfBbTogBMo9EFo5AWUVgOWGXeFajKtP8e0Xxw=="}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/sugarss-0.2.0.tgz_1475852527678_0.7048247170168906"}, "directories": {}, "publish_time": 1475852530426, "_hasShrinkwrap": false, "_cnpm_publish_time": 1475852530426, "_cnpmcore_publish_time": "2021-12-14T04:43:30.063Z"}, "0.1.6": {"name": "sugarss", "version": "0.1.6", "description": "Indent-based CSS syntax for PostCSS", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/sugarss.git"}, "dependencies": {"postcss": "^5.2.0"}, "devDependencies": {"babel-plugin-precompile-charcodes": "1.0.0", "babel-plugin-add-module-exports": "0.2.1", "babel-preset-es2015-loose": "7.0.0", "eslint-config-postcss": "2.0.2", "babel-preset-stage-0": "6.5.0", "postcss-parser-tests": "5.0.10", "babel-eslint": "6.1.2", "lint-staged": "2.0.3", "pre-commit": "1.1.3", "babel-core": "6.14.0", "babel-cli": "6.14.0", "eslint": "3.4.0", "ava": "0.16.0"}, "scripts": {"lint-staged": "lint-staged", "prepublish": "npm run build", "clean": "rm *.js || echo 'Already cleaned'", "build": "npm run clean && babel -s inline -d ./ *.es6", "lint": "eslint *.es6 test/*.js", "test": "npm run build && ava && npm run lint"}, "lint-staged": {"test/*.js": "eslint", "*.es6": "eslint"}, "pre-commit": ["lint-staged"], "gitHead": "399a098e0ec09079eeb8c4d30ca6c8abbc2f9f2c", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "homepage": "https://github.com/postcss/sugarss#readme", "_id": "sugarss@0.1.6", "_shasum": "fe3ac0e1e07282aef1de84a80b72386ff4e7ea37", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.5.0", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "dist": {"shasum": "fe3ac0e1e07282aef1de84a80b72386ff4e7ea37", "size": 34323, "noattachment": false, "tarball": "https://registry.npmmirror.com/sugarss/-/sugarss-0.1.6.tgz", "integrity": "sha512-4Oz04ycq0JYd49G0t3TyRnQoFE9a822sWPHcVyhsDzzG67IC7AA1RJqQ5/7qlQxk2ZW4lt9bf/XvmU0UP3SJ9w=="}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/sugarss-0.1.6.tgz_1473304421438_0.9350021921563894"}, "directories": {}, "publish_time": 1473304421695, "_hasShrinkwrap": false, "_cnpm_publish_time": 1473304421695, "_cnpmcore_publish_time": "2021-12-14T04:43:30.282Z"}, "0.1.5": {"name": "sugarss", "version": "0.1.5", "description": "Indent-based CSS syntax for PostCSS", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/sugarss.git"}, "dependencies": {"postcss": "^5.1.0"}, "devDependencies": {"babel-plugin-precompile-charcodes": "1.0.0", "babel-plugin-add-module-exports": "0.2.1", "babel-preset-es2015-loose": "7.0.0", "eslint-config-postcss": "2.0.2", "babel-preset-stage-0": "6.5.0", "postcss-parser-tests": "5.0.9", "babel-eslint": "6.1.2", "babel-core": "6.11.4", "babel-cli": "6.11.4", "eslint": "3.1.1", "ava": "0.15.2"}, "scripts": {"prepublish": "npm run build", "clean": "rm *.js || echo 'Already cleaned'", "build": "npm run clean && babel -s inline -d ./ *.es6", "lint": "eslint *.es6 test/*.js", "test": "npm run build && ava && npm run lint"}, "gitHead": "4dc5c18742af3988a0287009fcd2d5da1ba463d7", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "homepage": "https://github.com/postcss/sugarss#readme", "_id": "sugarss@0.1.5", "_shasum": "c0c25bc0941678504237e806aecd160a30a94eea", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "dist": {"shasum": "c0c25bc0941678504237e806aecd160a30a94eea", "size": 30408, "noattachment": false, "tarball": "https://registry.npmmirror.com/sugarss/-/sugarss-0.1.5.tgz", "integrity": "sha512-AJKFHsICop1t5mpjVbz8Y6lGwNyJUa4kw0lEURsrmZNGc1+OVQAYbRksIVco2CtTL2zdNM07h5oApgKwem1ACQ=="}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/sugarss-0.1.5.tgz_1469278585745_0.10302902827970684"}, "directories": {}, "publish_time": 1469278587758, "_hasShrinkwrap": false, "_cnpm_publish_time": 1469278587758, "_cnpmcore_publish_time": "2021-12-14T04:43:30.573Z"}, "0.1.4": {"name": "sugarss", "version": "0.1.4", "description": "Indent-based CSS syntax for PostCSS", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/sugarss.git"}, "dependencies": {"postcss": "^5.0.21"}, "devDependencies": {"babel-plugin-precompile-charcodes": "1.0.0", "babel-plugin-add-module-exports": "0.2.1", "babel-preset-es2015-loose": "7.0.0", "eslint-config-postcss": "2.0.2", "babel-preset-stage-0": "6.5.0", "postcss-parser-tests": "5.0.7", "babel-eslint": "6.0.4", "babel-core": "6.9.1", "babel-cli": "6.9.0", "eslint": "2.11.0", "ava": "0.15.1"}, "scripts": {"prepublish": "npm run build", "clean": "rm *.js || echo 'Already cleaned'", "build": "npm run clean && babel -s inline -d ./ *.es6", "lint": "eslint *.es6 test/*.js", "test": "npm run build && ava && npm run lint"}, "gitHead": "a869c18506168bea1429c9628d142b9011fbe986", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "homepage": "https://github.com/postcss/sugarss#readme", "_id": "sugarss@0.1.4", "_shasum": "0b1e5027232d305c14bcee90a46e2a3bc4817322", "_from": ".", "_npmVersion": "3.8.9", "_nodeVersion": "6.2.0", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "dist": {"shasum": "0b1e5027232d305c14bcee90a46e2a3bc4817322", "size": 29344, "noattachment": false, "tarball": "https://registry.npmmirror.com/sugarss/-/sugarss-0.1.4.tgz", "integrity": "sha512-2Ykg208apZZaI4/xaxhAS7fzPGYB3VLI5MLyKuf2tjBUiDi6PjLJowEr8wIRLUs4ak3UJZSEHkBO2fNnDxxnJg=="}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/sugarss-0.1.4.tgz_1464626427902_0.720276698935777"}, "directories": {}, "publish_time": 1464626430399, "_hasShrinkwrap": false, "_cnpm_publish_time": 1464626430399, "_cnpmcore_publish_time": "2021-12-14T04:43:30.785Z"}, "0.1.3": {"name": "sugarss", "version": "0.1.3", "description": "Indent-based CSS syntax for PostCSS", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/sugarss.git"}, "dependencies": {"postcss": "^5.0.19"}, "devDependencies": {"babel-plugin-precompile-charcodes": "1.0.0", "babel-plugin-add-module-exports": "0.1.2", "babel-preset-es2015-loose": "7.0.0", "eslint-config-postcss": "2.0.2", "babel-preset-stage-0": "6.5.0", "postcss-parser-tests": "5.0.6", "babel-eslint": "6.0.3", "babel-core": "6.7.7", "babel-cli": "6.7.7", "eslint": "2.8.0", "ava": "0.14.0"}, "scripts": {"prepublish": "npm run build", "clean": "rm *.js || echo 'Already cleaned'", "build": "npm run clean && babel -s inline -d ./ *.es6", "lint": "eslint *.es6 test/*.js", "test": "npm run build && ava && npm run lint"}, "gitHead": "869fa4df28c9d438f9289c072f33f741e7be4473", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "homepage": "https://github.com/postcss/sugarss#readme", "_id": "sugarss@0.1.3", "_shasum": "c3b0955c04346622064c8d674ce46ad40dcbea82", "_from": ".", "_npmVersion": "3.8.3", "_nodeVersion": "5.10.1", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "dist": {"shasum": "c3b0955c04346622064c8d674ce46ad40dcbea82", "size": 29257, "noattachment": false, "tarball": "https://registry.npmmirror.com/sugarss/-/sugarss-0.1.3.tgz", "integrity": "sha512-AR9X93aVe7WGzUNO+mbk+YAD4LpT4cCsuH0sPYzpvzXOo/kPSkvsEz0pJCsqFBsFlwo3B3KNVohxutkJ77xnXw=="}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/sugarss-0.1.3.tgz_1461397568542_0.04163599107414484"}, "directories": {}, "publish_time": 1461397571580, "_hasShrinkwrap": false, "_cnpm_publish_time": 1461397571580, "_cnpmcore_publish_time": "2021-12-14T04:43:30.978Z"}, "0.1.2": {"name": "sugarss", "version": "0.1.2", "description": "Indent-based CSS syntax for PostCSS", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/sugarss.git"}, "dependencies": {"postcss": "^5.0.19"}, "devDependencies": {"babel-plugin-precompile-charcodes": "1.0.0", "babel-plugin-add-module-exports": "0.1.2", "babel-preset-es2015-loose": "7.0.0", "eslint-config-postcss": "2.0.2", "babel-preset-stage-0": "6.5.0", "postcss-parser-tests": "5.0.6", "babel-eslint": "6.0.0-beta.6", "babel-core": "6.7.2", "babel-cli": "6.6.5", "eslint": "2.4.0", "ava": "0.13.0"}, "scripts": {"prepublish": "npm run build", "clean": "rm *.js || echo 'Already cleaned'", "build": "npm run clean && babel -s inline -d ./ *.es6", "lint": "eslint *.es6 test/*.js", "test": "npm run build && ava && npm run lint"}, "gitHead": "2871c8eedbe254fbfab87715166336df7f068765", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "homepage": "https://github.com/postcss/sugarss#readme", "_id": "sugarss@0.1.2", "_shasum": "d3c630ac59a9686354e0c5282cb2566fc64d48b3", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.8.0", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "dist": {"shasum": "d3c630ac59a9686354e0c5282cb2566fc64d48b3", "size": 31125, "noattachment": false, "tarball": "https://registry.npmmirror.com/sugarss/-/sugarss-0.1.2.tgz", "integrity": "sha512-JpIaQ+JBkDlaKZqngnGP5iK12C9J2hbIe/J6DaPFsdHX9cLUGEW8ba0f0KJ4PHBHiZ0LWynEnMbUZ96Sz4BjFA=="}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/sugarss-0.1.2.tgz_1458195375274_0.7997586966957897"}, "directories": {}, "publish_time": 1458195377826, "_hasShrinkwrap": false, "_cnpm_publish_time": 1458195377826, "_cnpmcore_publish_time": "2021-12-14T04:43:31.190Z"}, "0.1.1": {"name": "sugarss", "version": "0.1.1", "description": "Indent-based CSS syntax for PostCSS", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/sugarss.git"}, "dependencies": {"postcss": "^5.0.19"}, "devDependencies": {"babel-plugin-precompile-charcodes": "1.0.0", "babel-plugin-add-module-exports": "0.1.2", "babel-preset-es2015-loose": "7.0.0", "eslint-config-postcss": "2.0.2", "babel-preset-stage-0": "6.5.0", "postcss-parser-tests": "5.0.6", "babel-eslint": "6.0.0-beta.6", "babel-core": "6.7.2", "babel-cli": "6.6.5", "eslint": "2.4.0", "ava": "0.13.0"}, "scripts": {"prepublish": "npm run build", "clean": "rm *.js || echo 'Already cleaned'", "build": "npm run clean && babel -s inline -d ./ *.es6", "lint": "eslint *.es6 test/*.js", "test": "npm run build && ava && npm run lint"}, "gitHead": "b89292ebf74ed0098717cd435639da4bea2b2513", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "homepage": "https://github.com/postcss/sugarss#readme", "_id": "sugarss@0.1.1", "_shasum": "18901fe7be8d93629abdd915e23e2dedbe241ce9", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.8.0", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "dist": {"shasum": "18901fe7be8d93629abdd915e23e2dedbe241ce9", "size": 31125, "noattachment": false, "tarball": "https://registry.npmmirror.com/sugarss/-/sugarss-0.1.1.tgz", "integrity": "sha512-nBe0hS6546TORWj9spuXMpTQidvj2pnPi/cpnITXnEycUMvi2WiJZfnxFwSi1o3cmUNIc7bO7zDJj/zO0TRbmw=="}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/sugarss-0.1.1.tgz_1458059650433_0.43776392634026706"}, "directories": {}, "publish_time": 1458059652909, "_hasShrinkwrap": false, "_cnpm_publish_time": 1458059652909, "_cnpmcore_publish_time": "2021-12-14T04:43:31.397Z"}, "0.1.0": {"name": "sugarss", "version": "0.1.0", "description": "Indent-based CSS syntax for PostCSS", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/sugarss.git"}, "dependencies": {"postcss": "^5.0.19"}, "devDependencies": {"babel-plugin-precompile-charcodes": "1.0.0", "babel-plugin-add-module-exports": "0.1.2", "babel-preset-es2015-loose": "7.0.0", "eslint-config-postcss": "2.0.2", "babel-preset-stage-0": "6.5.0", "postcss-parser-tests": "5.0.6", "babel-eslint": "6.0.0-beta.2", "babel-core": "6.6.5", "babel-cli": "6.6.5", "eslint": "2.2.0", "ava": "0.12.0"}, "scripts": {"prepublish": "npm run build", "clean": "rm *.js || echo 'Already cleaned'", "build": "npm run clean && babel -s inline -d ./ *.es6", "lint": "eslint *.es6 test/*.js", "test": "npm run build && ava && npm run lint"}, "gitHead": "97ad86ce7d2e117cfa9c75921ef2c0cf998070fd", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "homepage": "https://github.com/postcss/sugarss#readme", "_id": "sugarss@0.1.0", "_shasum": "10418201e8d38ed5e0f32fcf1f880dadaa08651d", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "5.7.1", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "dist": {"shasum": "10418201e8d38ed5e0f32fcf1f880dadaa08651d", "size": 30720, "noattachment": false, "tarball": "https://registry.npmmirror.com/sugarss/-/sugarss-0.1.0.tgz", "integrity": "sha512-o8+J4IcudFZCH9A6rijwOmkqiV+y35qguoDCwgI7hncuzlzmPK69Im+o8uJRQ0pkF+qEhPHWa3LlSMN6qmTwvg=="}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/sugarss-0.1.0.tgz_1457483769374_0.5579545076470822"}, "directories": {}, "publish_time": 1457483772200, "_hasShrinkwrap": false, "_cnpm_publish_time": 1457483772200, "_cnpmcore_publish_time": "2021-12-14T04:43:31.613Z"}, "0.0.1": {"name": "sugarss", "version": "0.0.1", "description": "Indent-based CSS syntax for PostCSS", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/sugarss.git"}, "dependencies": {"postcss": "^5.0.19"}, "devDependencies": {"babel-plugin-precompile-charcodes": "1.0.0", "babel-plugin-add-module-exports": "0.1.2", "babel-preset-es2015-loose": "7.0.0", "eslint-config-postcss": "2.0.2", "babel-preset-stage-0": "6.5.0", "postcss-parser-tests": "5.0.6", "babel-eslint": "6.0.0-beta.2", "babel-core": "6.6.5", "babel-cli": "6.6.5", "eslint": "2.2.0", "ava": "0.12.0"}, "scripts": {"prepublish": "npm run build", "clean": "rm *.js || echo 'Already cleaned'", "build": "npm run clean && babel -s inline -d ./ *.es6", "lint": "eslint *.es6 test/*.js", "test": "npm run build && ava && npm run lint"}, "gitHead": "8048452089706c9dc012db85cb9ea030579e2049", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "homepage": "https://github.com/postcss/sugarss#readme", "_id": "sugarss@0.0.1", "_shasum": "93155f94b0b99ef49daf5d646afeb8a178eb7ae1", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "5.7.1", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "dist": {"shasum": "93155f94b0b99ef49daf5d646afeb8a178eb7ae1", "size": 30308, "noattachment": false, "tarball": "https://registry.npmmirror.com/sugarss/-/sugarss-0.0.1.tgz", "integrity": "sha512-b2GVhE0CTjpOeYMXLDAFhBkZXhD8sJJPAeezXe5/NSSAeifWxX7CAb1y0oR6IUIbZzsfKZd56RoCu3E4w+lBAw=="}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/sugarss-0.0.1.tgz_1457425450443_0.07879688148386776"}, "directories": {}, "publish_time": 1457425452388, "_hasShrinkwrap": false, "_cnpm_publish_time": 1457425452388, "_cnpmcore_publish_time": "2021-12-14T04:43:31.799Z"}, "5.0.0": {"name": "sugarss", "version": "5.0.0", "description": "Indent-based CSS syntax for PostCSS", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/sugarss.git"}, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "engines": {"node": ">=18.0"}, "exports": {".": {"require": "./index.js", "import": "./index.mjs"}, "./parse": "./parse.js", "./stringify": "./stringify.js", "./tokenize": "./tokenize.js", "./package.json": "./package.json"}, "peerDependencies": {"postcss": "^8.3.3"}, "_id": "sugarss@5.0.0", "gitHead": "0b7a62c59d4c9e2e37a4043437dd13c97ab8ed9e", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "homepage": "https://github.com/postcss/sugarss#readme", "_nodeVersion": "22.11.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-3//knMoF9btXcxHTbMRckIYjkEzSZ6pZjiaZ3wM6OIpUtQ06Uwqc0XgAr6jf+U74cLLTV/BEgmHWoeXPC+NhdQ==", "shasum": "a97ddc1b5a1598ba283a10b8d73da56a3848fe36", "tarball": "https://registry.npmmirror.com/sugarss/-/sugarss-5.0.0.tgz", "fileCount": 13, "unpackedSize": 96559, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDFLQpFWrxfIu2drDx3MuHwCVpgCvPvNlK1QFstAMRqJAiEAxWVZ2x6st2BFpb8BNiAgnSVzwq71w3eMMJVKZtxqT4Y="}], "size": 18123}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sugarss_5.0.0_1732099078536_0.6054581045290888"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-20T10:37:58.702Z", "publish_time": 1732099078702, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "homepage": "https://github.com/postcss/sugarss#readme", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "repository": {"type": "git", "url": "git+https://github.com/postcss/sugarss.git"}, "_source_registry_name": "default"}