<template>
  <div class="video-player-container">
    <div class="video-player-wrapper">
      <video
        ref="videoPlayer"
        :src="videoData.videoUrl"
        controls
        @pause="handlePause"
        @timeupdate="handleTimeUpdate"
        @ended="handleVideoEnded"
      ></video>
    </div>
    
    <div class="video-info">
      <h3>{{ videoData.videoTitle }}</h3>
      <div class="progress-info">
        <span>观看进度: {{ videoProgress.progress }}%</span>
        <span>已观看: {{ formatTime(videoProgress.watchedSeconds) }}</span>
        <span>总时长: {{ formatTime(videoProgress.duration) }}</span>
        <span>最后观看时间: {{ formatDate(videoProgress.updateTime) }}</span>
      </div>
    </div>
    
    <div class="video-actions">
      <el-button class="back-button" @click="handleBack">
        <i class="el-icon-back"></i> 返回章节列表
      </el-button>
    </div>
  </div>
</template>


<script setup>
import { ref, onMounted, onBeforeUnmount, computed } from 'vue'
import { getVideoProgress, saveVideoProgress } from '@/api/student/course'
import { throttle } from 'lodash-es'
import { ElMessage } from 'element-plus'

const props = defineProps({
  videoData: {
    type: Object,
    required: true,
    validator(value) {
      return value.id && value.videoTitle && value.videoUrl
    }
  }
})

const emit = defineEmits(['close'])

const videoPlayer = ref(null)
const currentTime = ref(0)
const videoProgress = ref({
  id: '',
  userId: '',
  videoId: '',
  watchedSeconds: 0,
  maxWatchedSeconds: 0,
  duration: 0,
  progress: 0,
  updateTime: ''
})

// 计算当前进度百分比
const currentProgress = computed(() => {
  if (!videoPlayer.value || !videoPlayer.value.duration) return 0
  return Math.floor((currentTime.value / videoPlayer.value.duration) * 100)
})

// 格式化时间显示
const formatTime = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs < 10 ? '0' : ''}${secs}`
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '暂无记录'
  const date = new Date(dateString)
  return date.toLocaleString()
}

// 格式化视频时长
const formatDuration = (seconds) => {
  return formatTime(seconds)
}


// 获取视频进度
const fetchVideoProgress = async () => {
  try {
    const res = await getVideoProgress(props.videoData.id)
    if (res.code === 200 && res.result) {
      videoProgress.value = {
        ...res.result,
        maxWatchedSeconds: res.result.watchedSeconds // 初始化最大进度
      }
      
      // 设置视频从上次观看的位置开始播放
      if (videoPlayer.value) {
        videoPlayer.value.currentTime = videoProgress.value.watchedSeconds
      }
    } else {
      // 如果没有进度记录，初始化默认值
      videoProgress.value = {
        ...videoProgress.value,
        videoId: props.videoData.id,
        duration: props.videoData.videoDuration || 0
      }
    }
  } catch (error) {
    console.error('获取视频进度失败:', error)
    ElMessage.error('获取播放进度失败')
  }
}

// 节流保存方法
const throttledSave = throttle(async () => {
  if (!videoPlayer.value || videoPlayer.value.readyState < 2) return

  const currentSeconds = Math.floor(videoPlayer.value.currentTime)
  const duration = Math.floor(videoPlayer.value.duration) || videoProgress.value.duration
  const currentProgress = Math.floor((currentSeconds / duration) * 100)
  
  // 确定要保存的progress（取最大值）
  const savedProgress = Math.max(currentProgress, videoProgress.value.progress)
  
  try {
    await saveVideoProgress({
      videoId: props.videoData.id,
      watchedSeconds: currentSeconds,  // 始终传递当前时间点
      duration: duration,
      progress: savedProgress  // 传递最大进度
    })
    
    videoProgress.value = {
      ...videoProgress.value,
      watchedSeconds: currentSeconds, 
      maxWatchedSeconds: Math.max(currentSeconds, videoProgress.value.maxWatchedSeconds), 
      progress: savedProgress,
      updateTime: new Date().toISOString()
    }
    
  } catch (err) {
    console.error('保存进度失败:', err)
  }
}, 180000, { trailing: true })


// 处理时间更新事件
const handleTimeUpdate = () => {
  if (videoPlayer.value) {
    currentTime.value = videoPlayer.value.currentTime
    throttledSave()
  }
}

// 处理暂停事件
const handlePause = () => {
  throttledSave.flush() 
}

// 处理视频结束
const handleVideoEnded = async () => {
  if (!videoPlayer.value) return
  
  const duration = Math.floor(videoPlayer.value.duration)
  try {
    await saveVideoProgress({
      videoId: props.videoData.id,
      watchedSeconds: duration, 
      duration: duration,
      progress: 100
    })
    videoProgress.value = {
      ...videoProgress.value,
      watchedSeconds: duration,
      maxWatchedSeconds: duration,
      progress: 100,
      updateTime: new Date().toISOString()
    }
  } catch (err) {
    console.error('标记视频完成失败:', err)
  }
}

const handleVisibilityChange = () => {
  if (document.hidden) {
    // 页面变为不可见时保存进度
    throttledSave.flush()
  }
}

// 返回按钮处理
const handleBack = () => {
  console.log('准备保存进度...', {
    currentTime: videoPlayer.value?.currentTime,
    duration: videoPlayer.value?.duration
  })
  throttledSave.flush() // 立即保存
  console.log('保存请求已触发')
  emit('close')
}

onMounted(() => {
  // 监听页面可见性变化
  document.addEventListener('visibilitychange', handleVisibilityChange)
  
  // 获取视频进度
  fetchVideoProgress()
})

onBeforeUnmount(() => {
  // 移除事件监听
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  // 离开前保存最后一次进度
  throttledSave.flush()
})
</script>

<style lang="scss" scoped>
.video-player-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.video-player-wrapper {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000;
  
  video {
    width: 100%;
    height: 100%;
    max-height: calc(100vh - 150px);
    object-fit: contain;
  }
}

.video-info {
  padding: 15px 20px;
  color: #333;
  
  h3 {
    margin: 0 0 10px 0;
    font-size: 18px;
  }
  
  .video-duration {
    margin: 0 0 10px 0;
    color: #666;
    font-size: 14px;
  }
  
  .progress-info {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  font-size: 14px;
  color: #666;
  margin-top: 10px;
  
  span {
    display: inline-block;
    min-width: 120px;
  }
}
}

.video-actions {
  padding: 15px 20px;
  display: flex;
  justify-content: flex-start;
  
  .back-button {
    margin-right: 10px;
  }
}
</style>