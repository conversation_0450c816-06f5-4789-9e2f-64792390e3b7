<template>
  <div class="units-container">
    <!-- 没有路径时的空状态 -->
    <div v-if="props.savedPaths.length === 0" class="empty-state">
      <div class="empty-icon">
        <svg width="80" height="80" viewBox="0 0 80 80" fill="none">
          <circle cx="40" cy="40" r="35" stroke="#d9d9d9" stroke-width="2" stroke-dasharray="5,5"/>
          <path d="M25 40h30M40 25v30" stroke="#d9d9d9" stroke-width="2" stroke-linecap="round"/>
        </svg>
      </div>
      <h3>暂无学习路径</h3>
      <p>请点击"创建路径"按钮开始创建您的第一个学习路径</p>
      <el-button type="primary" @click="$emit('create-path')">
        <el-icon><Plus /></el-icon>
        创建路径
      </el-button>
    </div>

    <!-- 有路径但没有选中时的提示 -->
    <div v-else-if="!props.selectedPath" class="empty-state">
      <div class="empty-icon">
        <svg width="80" height="80" viewBox="0 0 80 80" fill="none">
          <path d="M20 30h40v30H20z" stroke="#d9d9d9" stroke-width="2" fill="none"/>
          <circle cx="30" cy="45" r="3" fill="#d9d9d9"/>
          <circle cx="40" cy="45" r="3" fill="#d9d9d9"/>
          <circle cx="50" cy="45" r="3" fill="#d9d9d9"/>
        </svg>
      </div>
      <h3>请选择学习路径</h3>
      <p>从上方下拉菜单中选择一个学习路径来查看详情</p>
    </div>

    <!-- 选中路径但没有单元时的提示 -->
    <div v-else-if="props.currentUnits.length === 0" class="empty-state">
      <div class="empty-icon">
        <svg width="80" height="80" viewBox="0 0 80 80" fill="none">
          <rect x="15" y="25" width="50" height="30" rx="5" stroke="#d9d9d9" stroke-width="2" fill="none"/>
          <path d="M35 35h10M35 40h15M35 45h8" stroke="#d9d9d9" stroke-width="1.5"/>
        </svg>
      </div>
      <h3>暂无单元</h3>
      <p>请点击"编辑单元"按钮为当前路径添加学习单元</p>
      <el-button type="success" @click="$emit('edit-units')">
        <el-icon><Plus /></el-icon>
        编辑单元
      </el-button>
    </div>

    <!-- 有单元时显示正常内容 -->
    <div v-else class="unit-wrapper" v-for="(unit, unitIndex) in props.currentUnits" :key="unitIndex">
      <!-- 单元容器 -->
      <div class="path-flow-container">
        <!-- 单元标题栏 -->
        <div class="unit-header">
          <span class="unit-title">{{ unit.name || `单元${unitIndex + 1}` }}</span>
          <div class="unit-actions">
            <el-button  type="primary" @click="$emit('edit-nodes', unitIndex)">编辑</el-button>
          </div>
        </div>

        <!-- 流程图 -->
        <div class="flow-chart">
          <!-- 没有节点时的空状态 -->
          <div v-if="!unit.nodes || unit.nodes.length === 0" class="empty-nodes">
            <div class="empty-nodes-content">
              <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
                <circle cx="20" cy="20" r="15" stroke="#d9d9d9" stroke-width="2" stroke-dasharray="3,3"/>
                <path d="M15 20h10M20 15v10" stroke="#d9d9d9" stroke-width="1.5" stroke-linecap="round"/>
              </svg>
              <p>暂无节点</p>
              <el-button size="small" type="primary" @click="$emit('edit-nodes', unitIndex)">
                编辑节点
              </el-button>
            </div>
          </div>

          <!-- 有节点时显示正常内容 -->
          <div v-else class="flow-item" v-for="(node, nodeIndex) in unit.nodes" :key="nodeIndex">
            <div class="node-circle" :class="{ active: node.active }" @click="handleNodeClick(node, unitIndex, nodeIndex)">
              <span class="node-number">{{ nodeIndex + 1 }}</span>
            </div>
            <div class="node-label">{{ node.name || `节点${nodeIndex + 1}` }}</div>

            <!-- 节点间箭头连接线 -->
            <div v-if="nodeIndex < unit.nodes.length - 1" class="arrow-connector">
              <img :src="ArrowIcon" alt="arrow" class="arrow-icon" />
            </div>
          </div>
        </div>
      </div>

      <!-- 单元间向下箭头 -->
      <div v-if="unitIndex < props.currentUnits.length - 1" class="unit-arrow-connector">
        <div class="arrow-down">
          <img :src="ArrowIcon" alt="arrow" class="arrow-icon-down" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Plus } from '@element-plus/icons-vue'
import ArrowIcon from '@/assets/courseMap-icon/learningPath/arrowIcon.svg'

const props = defineProps({
  savedPaths: {
    type: Array,
    default: () => []
  },
  selectedPath: {
    type: String,
    default: ''
  },
  currentUnits: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['create-path', 'edit-units', 'edit-nodes', 'delete-unit', 'node-click'])

// 处理节点点击事件
const handleNodeClick = (node, unitIndex, nodeIndex) => {
  // 构造节点数据，包含必要的信息用于抽屉显示
  const nodeData = {
    id: node.id || `${unitIndex}-${nodeIndex}`, // 如果节点没有id，使用索引组合
    label: node.name || `节点${nodeIndex + 1}`,
    name: node.name || `节点${nodeIndex + 1}`,
    tags: node.tags || [],
    unitIndex,
    nodeIndex,
    active: node.active,
    enabled: node.enabled
  }

  emit('node-click', nodeData)
}
</script>

<style lang="scss" scoped>
// 单元容器样式
.units-container {
  display: flex;
  flex-direction: column;
  gap: 0;
  overflow-x: auto;
  padding: 0 20px;
  
  &::-webkit-scrollbar {
    height: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(168, 200, 255, 0.1);
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(168, 200, 255, 0.4);
    border-radius: 4px;
    
    &:hover {
      background: rgba(168, 200, 255, 0.6);
    }
  }
}

.unit-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 40px;
  text-align: center;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(168, 200, 255, 0.1);
  margin: 20px 0;

  .empty-icon {
    margin-bottom: 24px;
    opacity: 0.6;
  }

  h3 {
    margin: 0 0 12px 0;
    font-size: 20px;
    font-weight: 500;
    color: #666;
  }

  p {
    margin: 0 0 24px 0;
    font-size: 14px;
    color: #999;
    line-height: 1.6;
    max-width: 400px;
  }

  .el-button {
    padding: 12px 24px;
    font-size: 14px;
  }
}

// 空节点状态样式
.empty-nodes {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 120px;
  width: 100%;

  .empty-nodes-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 20px;

    svg {
      margin-bottom: 12px;
      opacity: 0.6;
    }

    p {
      margin: 0 0 16px 0;
      font-size: 14px;
      color: #999;
    }

    .el-button {
      font-size: 12px;
      padding: 8px 16px;
    }
  }
}

// 流程图容器样式
.path-flow-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  width: 100%;
  min-width: 800px;
  margin: 10px 0;

  .unit-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #a8c8ff;
    color: white;

    .unit-title {
      font-size: 16px;
      font-weight: 500;
    }

    .unit-actions {
      display: flex;
      gap: 8px;
    }
  }

  .flow-chart {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 90px 25px;
    background: linear-gradient(135deg, #e8f2ff 0%, #d4e8ff 100%);
    overflow-x: auto;
    overflow-y: hidden;
    min-height: 140px;
    height: 140px;
    min-width: 750px;
    
    &::-webkit-scrollbar {
      height: 8px;
    }
    
    &::-webkit-scrollbar-track {
      background: rgba(168, 200, 255, 0.1);
      border-radius: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(168, 200, 255, 0.4);
      border-radius: 4px;
      
      &:hover {
        background: rgba(168, 200, 255, 0.6);
      }
    }

    .flow-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      margin: 0 20px;
      flex-shrink: 0;
      min-width: 80px;

      &:first-child {
        margin-left: 0;
      }

      &:last-child {
        margin-right: 0;
      }

      .node-circle {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: #adc2fc;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 10px;
        transition: all 0.3s ease;
        cursor: pointer;

        &.active {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .node-number {
          color: white;
          font-weight: bold;
          font-size: 18px;
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
        }
      }

      .node-label {
        font-size: 14px;
        color: #666;
        text-align: center;
        white-space: nowrap;
        max-width: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .arrow-connector {
        position: absolute;
        top: 30px;
        left: calc(100% + 30%);
        z-index: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 30px;
        width: 50px;
        transform: translateX(-50%) translateY(-50%);

        .arrow-icon {
          width: 30px;
          height: 30px;
        }
      }
    }
  }
}

// 单元间箭头样式
.unit-arrow-connector {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 0;
  background: transparent;
  position: relative;

  .arrow-down {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    width: 60px;
    height: 60px;

    .arrow-icon-down {
      width: 60px;
      height: 60px;
      transform: rotate(90deg);
      filter: brightness(0) saturate(100%) invert(67%) sepia(36%) saturate(1000%) hue-rotate(200deg) brightness(100%) contrast(100%) drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(168, 200, 255, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(168, 200, 255, 0.4);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(168, 200, 255, 0.3);
  }
}
</style>
