<template>
  <div class="folder" :class="{ nested: isNested }">
    <div v-if="folder.id!== 'root'" class="folder-header">
      <input type="checkbox" v-model="folder.checked" @change="toggleFolder">
      <div class="header-text" @click="toggle">
        <!-- 文件夹序号 -->
        <span v-if="showNumbers && indexPath.length > 0" class="folder-index">{{ getIndex() }}.</span>
        <span class="arrow" :class="{ open: folder.expanded }">▶</span>
        <!-- 文件夹名称编辑状态 -->
        <span class="name-and-count">
          <span v-if="editingItem?.type === 'folder' && editingItem?.id === folder.id" class="name-edit-container">
            <input v-model="editingName" class="name-edit-input" @keyup.enter="saveRenaming"
              @keyup.escape="cancelRenaming">
            <button @click.stop="saveRenaming">✓</button>
            <button @click.stop="cancelRenaming">✗</button>
          </span>
          <span v-else class="folder-name">{{ folder.name }}</span>
          <span class="file-count">({{ folder.totalFileCount }})</span>
        </span>

        <!-- 文件夹操作按钮 -->
        <span class="folder-actions">
          <template v-for="(option, index) in folderOptions" :key="index">
            <div class="action-btn-container">
              <span class="action-btn" @click.stop="handleFolderOptionClick(option)"
                @mouseenter="activeFolderOptionIndex = option.subOptions ? index : -1">
                {{ option.label }}
              </span>
              <!-- 文件夹下拉菜单 -->
              <div v-if="activeFolderOptionIndex === index && option.subOptions" class="dropdown-menu"
                @mouseleave="activeFolderOptionIndex = -1">
                <div v-for="(subOption, subIndex) in option.subOptions" :key="subIndex" class="dropdown-item"
                  @click.stop="handleFolderOptionClick(subOption)">
                  {{ subOption.label }}
                </div>
              </div>
            </div>
          </template>
        </span>
      </div>
    </div>

    <!-- 文件列表 -->
<ul v-if="folder.expanded && folder.files.length > 0" class="file-list"   :style="{ 'margin-left': folder.id === 'root' ? '0' : '36px' }">
  <li v-for="(file, fileIndex) in folder.files" :key="file.id" >
    <!-- 文件序号 -->
    <span v-if="showNumbers" class="file-index">{{ getFileIndex(fileIndex) }}</span>
    <input type="checkbox" v-model="file.checked" @change="updateFolderCheck">

    <!-- 文件名称编辑状态 -->
    <span v-if="editingItem?.type === 'file' && editingItem?.id === file.id" class="name-edit-container">
      <input v-model="editingName" class="name-edit-input" @keyup.enter="saveRenaming" @keyup.escape="cancelRenaming">
      <button @click.stop="saveRenaming">✓</button>
      <button @click.stop="cancelRenaming">✗</button>
    </span>
    <span class="file-name" 
  @click.stop="handleFileClick(file)"
  :class="{ 'clickable': file.type !== 5 }">
    <span v-if="file.type === 1">🎥 {{ file.name }} (视频) <span class="publish-status" :data-state="file.state">{{ file.state === 1 ? '[已发布]' : '[未发布]' }}</span></span>
    <span v-else-if="file.type === 2">📚 {{ file.name }} (教材) <span class="publish-status">{{ file.state === 1 ? '[已发布]' : '[未发布]' }}</span></span>
    <span v-else-if="file.type === 3">📊 {{ file.name }} (PPT) <span class="publish-status">{{ file.state === 1 ? '[已发布]' : '[未发布]' }}</span></span>
    <span v-else-if="file.type === 4">📝 {{ file.name }} (Word) <span class="publish-status">{{ file.state === 1 ? '[已发布]' : '[未发布]' }}</span></span>
    <span v-else-if="file.type === 5">🔗 <a :href="file.url" target="_blank" class="link-text">{{ file.name }}</a> (外部资源) <span class="publish-status">{{ file.state === 1 ? '[已发布]' : '[未发布]' }}</span></span>
    <span v-else>📄 {{ file.name }} (其他) <span class="publish-status">{{ file.state === 1 ? '[已发布]' : '[未发布]' }}</span></span>
</span>
    <!-- 文件操作按钮 -->
    <span class="file-actions">
      <template v-for="(option, optionIndex) in fileOptions" :key="optionIndex">
        <div class="action-btn-container">
          <span class="action-btn" @click.stop="handleFileOptionClick(option, file)"
            @mouseenter="activeFileOptionIndices[file.id] = option.subOptions ? optionIndex : -1">
            {{ option.label }}
          </span>

          <!-- 文件下拉菜单 -->
          <div v-if="activeFileOptionIndices[file.id] === optionIndex && option.subOptions" class="dropdown-menu"
            @mouseleave="activeFileOptionIndices[file.id] = -1">
            <div v-for="(subOption, subIndex) in option.subOptions" :key="subIndex" class="dropdown-item"
              @click.stop="handleFileOptionClick(subOption, file)">
              {{ subOption.label }}
            </div>
          </div>
        </div>
      </template>
    </span>
  </li>
</ul>

    <!-- 子文件夹列表 -->
    <div v-if="folder.expanded" class="sub-folders">
      <FolderItem v-for="(child, childIndex) in folder.children" :key="child.id" :folder="child"
        :show-numbers="showNumbers" :index-path="indexPath.concat(childIndex)" @toggle-folder="updateFolderCheck"
        @update-all-check="updateAllCheck" />
    </div>

    <!-- 新增文件夹模态框 -->
    <div v-if="showAddFolderModal" class="modal-mask" @click.self="closeModal">
      <div class="modal">
        <h3>新建子文件夹</h3>
        <input v-model="newFolderName" placeholder="请输入文件夹名称" class="modal-input" @keyup.enter="createSubFolder" />
        <div class="modal-actions">
          <button @click="createSubFolder">确定</button>
          <button @click="closeModal">取消</button>
        </div>
      </div>
    </div>
  </div>
  <ResourceDialog v-if="dialogVisible" :title="dialogTitle" @close="dialogVisible = false"
    @confirm="handleMoveConfirm" />
  <AddLinkDialog v-if="addLinkDialogVisible" :folder-id="currentFolderId" @close="addLinkDialogVisible = false"
    @confirm="handleAddLinkConfirm" />
     <PublishDialog 
    :model-value="showPublishDialog"
    :title="publishDialogTitle"
    :tree-data="syllabusTree"
    @update:model-value="(val) => showPublishDialog = val"
    @confirm="handlePublishConfirm"
  />

<!-- 发布选项弹窗 -->
<el-dialog
  v-model="showPublishOptions"
  :title="publishDialogTitle"
  width="400px"
  :close-on-click-modal="false"
>
<div class="publish-options">
  <div class="publish-option" @click="handlePublishOption('direct')">
    <div class="option-content">
      <div class="option-title">🚀 直接发布</div>
    </div>
  </div>

  <div class="publish-option" @click="handlePublishOption('chapter')">
    <div class="option-content">
      <div class="option-title">📚 章节发布</div>
    </div>
  </div>
</div>

  
  <template #footer>
    <el-button @click="showPublishOptions = false">取消</el-button>
  </template>
</el-dialog>

  <FilePreviewDialog 
    v-if="previewVisible"
    :visible="previewVisible"
    :file="currentFile"
    @close="previewVisible = false"
  />
  
</template>

<script setup>
import { ref, computed } from 'vue'
import { useUserStore } from '@/stores/userStore';
import { useRoute } from 'vue-router';
import { saveFolder, updateResource, updateFolder, deleteResources, deleteFolders, moveCourseLink,publishCourseLink } from '@/api/teacher/publicResources'
import ResourceDialog from './Dialog/ResourceDialog.vue'
import AddLinkDialog from './Dialog/AddLinkDialog.vue'
import FilePreviewDialog from './Dialog/FilePreviewDialog.vue';
import { useBaseResourceStore } from '@/stores/public/teacher/baseResourceStore'
import {useResourceStore} from '@/stores/public/teacher/resource'
import PublishDialog from './Dialog/PublishDialog.vue'
import { getSyllabusTree } from '@/api/teacher/course'


const showAddFolderModal = ref(false)
const newFolderName = ref('')
const route = useRoute()
const courseId = ref(route.params.courseId);
const createdId = computed(() => userStore.user.id);
const userStore = useUserStore()
const editingItem = ref(null) // 当前正在编辑的项目
const editingName = ref('') // 正在编辑的名称
const tempName = ref('') // 临时存储原始名称
const dialogVisible = ref(false)
const dialogTitle = ref("")
const movingItem = ref(null)
const baseResourcStore = useBaseResourceStore()
const currentFolderId = ref('')
const addLinkDialogVisible = ref(false)
const resourceStore = useResourceStore()
const showPublishDialog = ref(false)
const syllabusTree = ref([])
const publishDialogTitle = ref('')
const publishingItem = ref(null) // 当前正在发布的项目
const previewVisible = ref(false);
const currentFile = ref(null);

const handleFileClick = (file) => {
  if (file.type === 5) {
    window.open(file.url, '_blank');
    return;
  }
  
  currentFile.value = file;
  previewVisible.value = true;
};

const props = defineProps({
  folder: {
    type: Object,
    required: true
  },

  showNumbers: {
    type: Boolean,
    default: true
  },
  isNested: {
    type: Boolean,
    default: false
  },
  indexPath: {
    type: Array,
    default: () => []
  }
})

const emits = defineEmits(['toggle-folder', 'update-all-check'])

// 文件夹操作选项
const folderOptions = [
  { label: '添加子级', action: 'addSubFolder', handler: () => showAddFolderModal.value = true },
  {
    label: '添加资源',
    subOptions: [
      { label: '本地上传', handler: () => triggerFileUpload(props.folder.id) },
      {
        label: '添加链接', handler: () => {
          currentFolderId.value = props.folder.id
          addLinkDialogVisible.value = true
        }
      },
      { label: '个人资源库', action: 'personalLibrary' }
    ]
  },
  { label: '发布', handler: () => handlePublish('folder', props.folder)},
  {
    label: '更多',
    subOptions: [
      {
        label: '重命名', action: 'rename',
        handler: () => startRenaming('folder', props.folder.id, props.folder.name)
      },
      {
        label: '移动至',
        handler: () => {
          movingItem.value = { type: 'folder', id: props.folder.id }
          dialogTitle.value = '移动文件夹至'
          dialogVisible.value = true
        }
      },
      { label: '删除', action: 'delete', handler: () => confirmDelete('folder', props.folder.id) }
    ]
  }
]

// 文件操作选项
const fileOptions = [
  { label: '发布',  handler: (file) => handlePublish('file', file)},
  {
    label: '更多',
    subOptions: [
      { label: '重命名', action: 'rename', handler: (file) => startRenaming('file', file.id, file.name) },
      {
        label: '移动至',
        handler: (file) => {
          movingItem.value = { type: 'file', id: file.id }
          dialogTitle.value = '移动文件至'
          dialogVisible.value = true
        }
      },
      { label: '删除', action: 'delete', handler: (file) => confirmDelete('file', file.id) }
    ]
  }
]

// 添加以下状态
const showPublishOptions = ref(false) // 控制发布选项弹窗显示
const publishOptions = [
  {
    label: '直接发布',
    description: '将资源发布到课程资源库',
    icon: '🚀',
    value: 'direct'
  },
  {
    label: '章节发布',
    description: '将资源关联到具体章节',
    icon: '📚',
    value: 'chapter'
  }
]

// 下拉菜单状态
const activeFolderOptionIndex = ref(-1)
const activeFileOptionIndices = ref({})

// 计算文件夹序号
const getIndex = () => {
  if (!props.showNumbers) return ''
  return props.indexPath.map(index => index + 1).join('.')
}

// 计算文件序号
const getFileIndex = (fileIndex) => {
  if (!props.showNumbers) return ''
  return getIndex() + (props.indexPath.length > 0 ? '.' : '') + (fileIndex + 1)
}

// 文件夹展开/折叠
const toggle = () => {
  props.folder.expanded = !props.folder.expanded
}

// 文件夹选中状态切换
const toggleFolder = () => {
  props.folder.files.forEach(file => {
    file.checked = props.folder.checked
  })
  emits('toggle-folder', props.folder)
}

// 更新文件夹选中状态
const updateFolderCheck = () => {
  props.folder.checked = props.folder.files.every(file => file.checked)
  emits('update-all-check')
}


// 处理文件夹选项点击
const handleFolderOptionClick = (option) => {
  if (option.handler) {
    option.handler() // 优先执行handler
  } else if (!option.subOptions) {
    console.log('执行文件夹操作:', option.action)
    // 这里可以添加实际的操作逻辑
  }
}

// 处理文件选项点击
const handleFileOptionClick = (option, file) => {
  if (option.handler) {
    option.handler(file) // 新增：触发handler函数
  } else if (option.subOptions) {
    option.handler(file)
  } else if (!option.subOptions) {
    console.log('执行文件操作:', option.action, '文件:', file.name)
  }
}
// 创建子文件夹
const createSubFolder = async () => {
  if (!newFolderName.value.trim()) return

  try {
    const folderData = {
      name: newFolderName.value.trim(),
      parentId: props.folder.id, // 当前文件夹作为父级
      folderType: 0,
      createdId: userStore.user.id,
      courseId: route.params.courseId
    }

    const response = await saveFolder(folderData)
    console.log('子文件夹创建成功:', response)

    // 触发事件通知父组件刷新数据
    emits('folder-created', response.result)
    closeModal()
  } catch (error) {
    console.error('创建子文件夹失败:', error)
  }
}

const closeModal = () => {
  showAddFolderModal.value = false
  newFolderName.value = ''
}

// 开始重命名
const startRenaming = (type, id, name) => {
  editingItem.value = { type, id }
  editingName.value = name
  tempName.value = name // 保存原始名称用于取消

  // 下一个tick聚焦输入框
  nextTick(() => {
    const input = document.querySelector('.name-edit-input')
    input?.focus()
  })
}

// 保存重命名
const saveRenaming = async () => {
  if (!editingItem.value || !editingName.value.trim()) return

  try {
    if (editingItem.value.type === 'folder') {
      await updateFolder({
        id: editingItem.value.id,
        name: editingName.value.trim()
      })
    } else {
      await updateResource({
        id: editingItem.value.id,
        name: editingName.value.trim()
      })
    }
    await resourceStore.refreshFolders();
    cancelRenaming()
  } catch (error) {
    console.error('重命名失败:', error)
  }
}

// 取消重命名
const cancelRenaming = () => {
  editingName.value = tempName.value
  editingItem.value = null
}

// 删除确认和操作
const confirmDelete = (type, id) => {
  if (confirm(`确定要删除这个${type === 'folder' ? '文件夹及其所有内容' : '文件'}吗？`)) {
    deleteItem(type, id)
  }
}

const deleteItem = async (type, id) => {
  try {
    const params = [{ id }]
    if (type === 'folder') {
      await deleteFolders(params)
    } else {
      await deleteResources(params)
    }

    alert(`${type === 'folder' ? '文件夹' : '文件'}删除成功`)
    emits('item-deleted', { type, id })
await resourceStore.refreshFolders();
  } catch (error) {
    console.error(`删除${type === 'folder' ? '文件夹' : '文件'}失败:`, error)
    alert(`删除${type === 'folder' ? '文件夹' : '文件'}失败，请重试`)
  }
}


const handleMoveConfirm = async (playload) => {
  try {
    if (!movingItem.value) {
      throw new Error('未选择移动项')
    }
    const params = {
      courseId: route.params.courseId,
      targetFolderId: playload.folderId || null  // 允许移动到根目录
    }
    // 根据类型设置不同参数
    if (movingItem.value.type === 'file') {
      params.resourceId = movingItem.value.id
      params.sourceFolderId = "null"
    } else {
      params.resourceId = "null"
      params.sourceFolderId = movingItem.value.id
    }
    await moveCourseLink(params)
    emits('item-moved', {
      type: movingItem.value.type,
      id: movingItem.value.id,
      targetFolderId
    })
    await resourceStore.refreshFolders();
    ElMessage.success('移动成功')
  } catch (error) {
    console.error('移动失败:', error)
    ElMessage.error(error.message || '移动失败')
  }
}

const triggerFileUpload = async (folderId) => {
  const input = document.createElement('input')
  input.type = 'file'
  input.multiple = true // 支持多选
  input.onchange = async (e) => {
    const files = Array.from(e.target.files)
    try {
      for (const file of files) {
        console.log(1);

        await baseResourcStore.baseUploadResource(file, {
          courseId: courseId.value,
          folderId: folderId,
          createdId: createdId.value
        })
        console.log(2);

      }
      await resourceStore.refreshFolders();
      ElMessage.success('文件上传成功')
      emits('refresh-list')
    } catch (error) {
      console.error('上传失败:', error)
      ElMessage.error('文件上传失败')
    }
  }
  input.click()
}

const handleAddLinkConfirm = (result) => {
  console.log('链接添加成功:', result)
  addLinkDialogVisible.value = false
  ElMessage.success('链接添加成功')
  emits('refresh-list')
}

// 添加发布处理方法
/* const handlePublish = async (type, item) => {
  try {
    // 获取课程大纲树
    const response = await getSyllabusTree(courseId.value)
    syllabusTree.value = response.result

    
    // 设置当前发布项
    publishingItem.value = { type, id: item.id, name: item.name }
    publishDialogTitle.value = `发布${type === 'folder' ? '文件夹' : '文件'} "${item.name}" 到`
    showPublishDialog.value = true
  } catch (error) {
    console.error('获取课程大纲失败:', error)
    ElMessage.error('获取课程大纲失败')
  }
} */

const handlePublish = (type, item) => {
  publishingItem.value = { type, id: item.id, name: item.name }
  publishDialogTitle.value = `发布${type === 'folder' ? '文件夹' : '文件'} "${item.name}"`
  showPublishOptions.value = true // 显示发布选项弹窗
}

const handlePublishOption = async (option) => {
  showPublishOptions.value = false
  
  if (option === 'direct') {
    const isConfirm = confirm(`确定要直接发布${publishingItem.value.type === 'folder' ? '文件夹' : '文件'} "${publishingItem.value.name}" 到资源库吗？`);
    if (isConfirm) {
      // 用户确认后执行发布
      await handlePublishConfirm(null);
    }
  } else {
    // 章节发布，获取大纲树
    try {
      const response = await getSyllabusTree(courseId.value)
      syllabusTree.value = response.result
      showPublishDialog.value = true
    } catch (error) {
      console.error('获取课程大纲失败:', error)
      ElMessage.error('获取课程大纲失败')
    }
  }
}

// 添加发布确认处理方法
/* const handlePublishConfirm = async (chapterId) => {
  try {
    if (publishingItem.value?.type === 'folder') {
      const allFiles = getAllFilesInFolder(props.folder);
      let successCount = 0;

      for (const file of allFiles) {
        try {
          await publishCourseLink({
            courseId: courseId.value,
            syllabusId: chapterId,
            resourceId: file.id,
          });
          successCount++;
        } catch (error) {
          console.error(`文件 ${file.name} 发布失败:`, error);
          ElMessage.error(`文件 ${file.name} 发布失败: ${error.message || '未知错误'}`);
        }
      }

      ElMessage.success(`已成功发布 ${successCount}/${allFiles.length} 个文件到选定的章节`);
    } else {
      await publishCourseLink({
        courseId: courseId.value,
        syllabusId: chapterId,
        resourceId: publishingItem.value.id,
      });
      ElMessage.success('发布成功');
    }

    await resourceStore.refreshFolders();
  } catch (error) {
    console.error('发布操作失败:', error);
    ElMessage.error(error.message || '发布操作失败');
  } finally {
    showPublishDialog.value = false; // 手动关闭对话框
  }
}; */

const handlePublishConfirm = async (chapterId) => {
  try {
    if (publishingItem.value?.type === 'folder') {
      const allFiles = getAllFilesInFolder(props.folder);
      let successCount = 0;

      for (const file of allFiles) {
        try {
          await publishCourseLink({
            courseId: courseId.value,
            syllabusId: chapterId, 
            resourceId: file.id,
          });
          successCount++;
        } catch (error) {
          console.error(`文件 ${file.name} 发布失败:`, error);
          ElMessage.error(`文件 ${file.name} 发布失败: ${error.message || '未知错误'}`);
        }
      }

      const message = chapterId 
        ? `已成功发布 ${successCount}/${allFiles.length} 个文件到选定的章节`
        : `已成功发布 ${successCount}/${allFiles.length} 个文件到资源库`;
      ElMessage.success(message);
    } else {
      await publishCourseLink({
        courseId: courseId.value,
        syllabusId: chapterId, // 这里会根据用户选择自动传null或具体章节ID
        resourceId: publishingItem.value.id,
      });
      
      const message = chapterId 
        ? '已成功发布到选定章节'
        : '已成功发布到资源库';
      ElMessage.success(message);
    }

    await resourceStore.refreshFolders();
  } catch (error) {
    console.error('发布操作失败:', error);
    ElMessage.error(error.message || '发布操作失败');
  } finally {
    showPublishDialog.value = false;
    showPublishOptions.value = false; // 确保关闭所有弹窗
  }
};
// 递归获取文件夹下的所有文件
const getAllFilesInFolder = (folder) => {
  let files = [...folder.files];
  
  // 递归处理子文件夹
  if (folder.children && folder.children.length > 0) {
    folder.children.forEach(child => {
      files = files.concat(getAllFilesInFolder(child));
    });
  }
  
  return files;
};
</script>

<style scoped lang="scss">
.folder {
  padding: 6px 0;
  margin-left: 0;
  transition: all 0.2s ease;

  &:not(:first-child) {
    margin-top: 4px;
  }

  &.nested {
    margin-left: 20px;
  }

  .folder-header {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    border-radius: 4px;
    background-color: #f5f7fa;
    cursor: pointer;

    &:hover {
      background-color: #eef2f7;
    }

    input[type="checkbox"] {
      margin-right: 4px;
      flex-shrink: 0;
    }

    .header-text {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .arrow {
      display: inline-block;
      width: 16px;
      height: 16px;
      line-height: 16px;
      text-align: center;
      transform: rotate(0deg);
      transition: transform 0.2s ease;
      color: #909399;
      font-size: 12px;

      &.open {
        transform: rotate(90deg);
      }
    }

    .folder-name {
      font-weight: 500;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .file-count {
      color: #999;
      font-size: 12px;
    }

    .folder-index {
      display: inline-block;
      width: 24px;
      text-align: right;
      margin-right: 4px;
      color: #606266;
      font-size: 12px;
    }

    .folder-actions {
      display: none;
      margin-left: auto;
      gap: 5px;
    }

    &:hover .folder-actions {
      display: flex; // 悬停时显示
    }
  }

  .file-list {
   // margin-left: 36px;
    margin-top: 6px;
    padding-left: 0;
    list-style: none;

    li {
      padding: 6px 0;
      display: flex;
      align-items: center;
      gap: 8px;
      border-radius: 4px;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: #f5f7fa;
      }

      .file-index {
        display: inline-block;
        width: 40px;
        text-align: right;
        margin-right: 4px;
        color: #606266;
        font-size: 12px;

        .publish-status {
  margin-left: 4px;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  
}

/* 已发布样式 */
.publish-status[data-state="1"] {
  color: #67c23a;
  background-color: #f0f9eb;
}

/* 未发布样式 */
.publish-status[data-state="0"] {
  color: #909399;
  background-color: #f4f4f5;
}
      }

      .file-actions {
        margin-left: auto;
        display: none;
        gap: 5px;
      }

      &:hover .file-actions {
        display: flex; // 悬停时显示
      }
    }
  }

  .sub-folders {
    margin-left: 20px;
    margin-top: 4px;
  }

  .link-item {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #409eff;
    font-size: 14px;

    a {
      color: #409eff;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .file-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
  }

  /* 操作按钮和下拉菜单样式 */
  .action-btn-container {
    position: relative;
    display: inline-block;
  }

  .action-btn {
    cursor: pointer;
    color: #409eff;
    padding: 4px 8px;
    border-radius: 4px;
    white-space: nowrap;

    &:hover {
      background-color: #ecf5ff;
    }
  }

  .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
    z-index: 10;
    padding: 5px 0;
    min-width: 120px;
  }

  .dropdown-item {
    padding: 8px 16px;
    cursor: pointer;
    white-space: nowrap;

    &:hover {
      background-color: #f5f7fa;
      color: #409eff;
    }
  }

  .modal-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;

    .modal {
      background: white;
      padding: 20px;
      border-radius: 8px;
      width: 300px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

      h3 {
        margin-bottom: 12px;
        font-size: 16px;
      }

      .modal-input {
        width: 100%;
        padding: 6px 10px;
        margin-bottom: 16px;
        border: 1px solid #ddd;
        border-radius: 4px;
      }

      .modal-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;

        button {
          padding: 6px 12px;
          background-color: #4c7bff;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;

          &:last-child {
            background-color: #ccc;
          }
        }
      }
    }
  }
  
}

.publish-options {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  padding: 1rem;
}

.publish-option {
  cursor: pointer;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background-color: #fff;
  transition: all 0.3s ease;
  width: 160px;
  text-align: center;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  }

  .option-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .option-title {
      font-size: 16px;
      font-weight: 600;
    }
  }
}

</style>