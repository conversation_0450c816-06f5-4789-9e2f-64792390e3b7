// src/api/teacher/graphManager/questionGraph.js
// 根据后端接口文档更新为问题图谱专用接口

import request from "@/api/service";

// 获取图谱数据的接口
export const createGraph = (data) => {
  return request.post("/knowledge-graph/create", data);
};

// 获取课程所有图谱
export const getGraphListByCourse = (courseId) => {
  return request.get("/knowledge-graph/list-by-course", {
    params: { courseId },
  });
};

// 删除图谱
export const deleteGraph = (graphId) => {
  return request.post("/knowledge-graph/delete", null, {
    params: { graphId },
  });
};

// 获取图谱信息
export const getGraphTree = (graphId) => {
  return request.get("/question-graph/tree-view", {
    params: { graphId },
  });
};

// 创建节点（支持节点类型）
export const createNode = (data) => {
  return request.post("/question-graph/node/create", data);
};

// 删除节点
export const deleteNode = (nodeId) => {
  return request.post("/question-graph/node/delete", null, {
    params: { nodeId },
  });
};

// 更新节点信息（支持问题图谱特有的answer字段）
export const updateNodeInfo = (data) => {
  return request.post("/question-graph/node/update", {
    questionId: data.nodeId || data.questionId,
    nodeName: data.nodeName,
    nodeDesc: data.nodeDesc,
    answer: data.answer,
  });
};

// 获取子节点数量（暂时保留，可能用于统计）
export const getChildCount = (nodeId) => {
  return request.get("/question-graph/node/child-count", {
    params: { nodeId },
  });
};

// 获取课程资源树
export function getCourseResourceFolderTree(courseId) {
  return request.get("/course/link/folderTree", {
    params: { courseId },
  });
}

// 关联资源（仅问题节点支持）
export function batchUpdateNodeResources(data) {
  return request.post("/question-graph/resources/batch-update", data);
}

// 获取节点详情
export function getNodeDetail(nodeId) {
  return request.get("/question-graph/node/detail", {
    params: { questionId: nodeId },
  });
}

// 统计节点关联的资源数量
export function countNodeResources(nodeId) {
  return request.get("/question-graph/resources/count", {
    params: { nodeId },
  });
}

// 获取节点已关联的资源列表
export function getNodeResources(nodeId) {
  return request.get("/question-graph/resources/list", {
    params: { nodeId },
  });
}

// 取消资源关联
export function unlinkResourceFromNode(nodeId, resourceId) {
  return request.post("/question-graph/resources/unlink", null, {
    params: { nodeId, resourceId },
  });
}

// 获取节点类型（暂时返回模拟数据）
export function getNodeType(nodeId) {
  // 暂时返回模拟数据，等待后端接口
  return Promise.resolve({
    code: 200,
    result: "QUESTION",
  });
}

// 检查节点是否可以创建子节点（暂时返回模拟数据）
export function canCreateChild(nodeId) {
  // 暂时返回模拟数据，等待后端接口
  return Promise.resolve({
    code: 200,
    result: true,
  });
}

// 复制知识图谱level为1的节点到问题图谱
export function copyLevel1NodesFromKnowledgeGraph(data) {
  return request.post("/question-graph/copy-level1-nodes", {
    questionGraphId: data.questionGraphId,
    courseId: data.courseId,
  });
}

// 统计问题节点数量（level=2的节点）
export function countQuestionNodes(graphId) {
  return request.get("/question-graph/question-node/count", {
    params: { graphId },
  });
}

// 统计图谱关联资源总数
export function countQuestionGraphResources(graphId) {
  return request.get("/question-graph/question-resources/count", {
    params: { graphId },
  });
}
