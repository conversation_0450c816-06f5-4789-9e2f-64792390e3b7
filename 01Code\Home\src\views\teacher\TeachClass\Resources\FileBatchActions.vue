
<template>
  <div class="batch-actions">
    <button 
      class="first-btn" 
      @click="$emit('download')" 
      :disabled="hasFolders"
      :class="{ 'disabled-btn': hasFolders }"
    >下载</button>
    <button 
      class="middle-btn" 
      @click="$emit('delete-selected', selectedItems)" 
    >删除</button>
    <button 
      class="middle-btn" 
      @click="handleRename"
      :disabled="selectedItems.length !== 1"
      :class="{ 'disabled-btn': selectedItems.length !== 1 }"
    >重命名</button>
    <button 
      class="last-btn" 
      @click="handleMove"
    >移动到</button>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const emit = defineEmits([
  'download',
  'delete-selected',
  'start-rename',  // 重命名事件
  'move-selected'
])

const props = defineProps({
  selectedItems: {
    type: Array,
    default: () => []
  },
  fileList: {
    type: Array,
    default: () => []
  }
})

// 检查选中项中是否包含文件夹
const hasFolders = computed(() => {
  return props.selectedItems.some(id => {
    const item = props.fileList.find(file => file.id === id)
    return item?.type === 'folder'
  })
})

const handleRename = () => {
  const selectedId = props.selectedItems[0]
  const file = props.fileList.find(f => f.id === selectedId)
  if (file) {
    emit('start-rename', file);
  } else {
  }
}


const handleMove = () => {
 emit('move-selected', props.selectedItems)
 }
</script>

<style lang="scss" scoped>
.batch-actions {
  display: flex;
  padding: 0; 
  height: 32px; 
  
  button {
    height: 100%; 
    padding: 0 12px; 
    border: 1px solid #8ebfff;
    margin-left: -1px;
    position: relative;
    background-color: transparent;
    display: flex; 
    align-items: center;
    justify-content: center;
    font-size: 12px; 
    color:#575858;
    white-space: nowrap; 
    text-overflow: ellipsis;
    cursor: pointer;
    
    &:hover:not(.disabled-btn) {
      color: #1a68d1;
    }
    
    &:focus {
      z-index: 1;
      outline: none;
    }

    &.disabled-btn {
      color: #c0c4cc;
      background-color: #f5f7fa;
      cursor: not-allowed;
    }
  }

  .first-btn {
    border-radius: 45px 0 0 45px;
    margin-left: 0;
  }

  .middle-btn {
    border-radius: 0;
  }

  .last-btn {
    border-radius: 0 45px 45px 0;
  }
}
</style>