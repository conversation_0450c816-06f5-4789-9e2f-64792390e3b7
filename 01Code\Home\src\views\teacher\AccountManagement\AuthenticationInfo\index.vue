<template>
  <div class="auth-info">
    <!-- 实名认证 -->
    <div class="auth-section">
      <h3>实名认证</h3>
      <el-tag :type="authStatus.realName ? 'success' : 'danger'" circle>
        {{ authStatus.realName ? '已认证' : '未认证' }}
      </el-tag>
      <el-button 
        type="primary" 
        @click="startRealNameAuth" 
        v-if="!authStatus.realName"
        :loading="loading.realName"
      >
        立即认证
      </el-button>
    </div>

    <!-- 职业认证 -->
    <div class="auth-section">
      <h3>职业认证</h3>
      <el-tag :type="authStatus.job ? 'success' : 'danger'" circle>
        {{ authStatus.job ? '已认证' : '未认证' }}
      </el-tag>
      <el-button 
        type="primary" 
        @click="startJobAuth" 
        v-if="!authStatus.job"
        :loading="loading.job"
      >
        立即认证
      </el-button>
    </div>

    <!-- 实名认证弹窗 -->
    <el-dialog v-model="dialog.realName" title="实名认证">
      <el-form :model="forms.realName" label-width="120px">
        <el-form-item label="姓名" prop="name" :required="true">
          <el-input v-model="forms.realName.name" placeholder="请输入您的姓名"></el-input>
        </el-form-item>
        <el-form-item label="性别" prop="gender" :required="true">
          <el-radio-group v-model="forms.realName.gender">
            <el-radio label="男">男</el-radio>
            <el-radio label="女">女</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="证件上传" :required="true">
          <el-upload
            class="upload-demo"
            action="#"
            :on-change="handleFileChange('realName')"
            :file-list="fileLists.realName"
            :auto-upload="false"
            :limit="1"
            accept="image/jpeg,image/png"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传jpg/png文件，且不超过2M</div>
          </el-upload>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitAuth('realName')" :loading="loading.submitRealName">
            提交
          </el-button>
          <el-button @click="dialog.realName = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 职业认证弹窗 -->
    <el-dialog v-model="dialog.job" title="职业认证">
      <el-form :model="forms.job" label-width="120px">
        <el-form-item label="学校/单位" prop="organization" :required="true">
          <el-input v-model="forms.job.organization" placeholder="请输入您的学校或单位"></el-input>
        </el-form-item>
        <el-form-item label="院系/部门" prop="department" :required="true">
          <el-input v-model="forms.job.department" placeholder="请输入您的院系或部门"></el-input>
        </el-form-item>
        <el-form-item label="职业证上传" :required="true">
          <el-upload
            class="upload-demo"
            action="#"
            :on-change="handleFileChange('job')"
            :file-list="fileLists.job"
            :auto-upload="false"
            :limit="1"
            accept="image/jpeg,image/png"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传jpg/png文件，且不超过2M</div>
          </el-upload>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitAuth('job')" :loading="loading.submitJob">
            提交
          </el-button>
          <el-button @click="dialog.job = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores/userStore'
import { 
  getTeacherDetail, 
  updateTeacher, 
  uploadTeacherFile 
} from '@/api/teacher/AccountManager/teacherInfo'
import { ElMessage } from 'element-plus'

const userStore = useUserStore()
const userId = userStore.user?.id

// 认证状态
const authStatus = ref({
  realName: false,
  job: false
})

// 加载状态
const loading = ref({
  realName: false,
  job: false,
  submitRealName: false,
  submitJob: false,
  fileUpload: false
})

// 弹窗状态
const dialog = ref({
  realName: false,
  job: false
})

// 表单数据
const forms = ref({
  realName: {
    name: '',
    gender: ''
  },
  job: {
    organization: '',
    department: ''
  }
})

// 文件列表
const fileLists = ref({
  realName: [],
  job: []
})

// 初始化加载认证状态
const loadAuthStatus = async () => {
  try {
    if (!userId) {
      ElMessage.warning('请先登录')
      return
    }

    const response = await getTeacherDetail(userId)
    if (response.code === 200 && response.result) {
      const teacherData = response.result
      authStatus.value.realName = !!teacherData.teacherName && !!teacherData.sex
      authStatus.value.job = !!teacherData.organization && !!teacherData.department
      
      forms.value.realName = {
        name: teacherData.teacherName || '',
        gender: teacherData.sex || ''
      }
      forms.value.job = {
        organization: teacherData.organization || '',
        department: teacherData.department || ''
      }
    }
  } catch (error) {
    console.error('加载认证状态失败:', error)
    ElMessage.error('加载认证信息失败，请刷新重试')
  }
}

// 开始认证
const startRealNameAuth = () => {
  dialog.value.realName = true
  fileLists.value.realName = []
}

const startJobAuth = () => {
  dialog.value.job = true
  fileLists.value.job = []
}

// 处理文件上传
const handleFileChange = (type) => (file, fileList) => {
  const validTypes = ['image/jpeg', 'image/png']
  const isLt2M = file.size / 1024 / 1024 < 2
  
  if (!validTypes.includes(file.type)) {
    ElMessage.error('只能上传JPG/PNG格式图片!')
    return false
  }
  
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过2MB!')
    return false
  }
  
  fileLists.value[type] = [file]
  return true
}

// 提交认证
const submitAuth = async (type) => {
  const loadingKey = `submit${type.charAt(0).toUpperCase() + type.slice(1)}`
  
  try {
    loading.value[loadingKey] = true
    
    // 验证必填字段
    if (type === 'realName') {
      if (!forms.value.realName.name) throw new Error('请输入姓名')
      if (!forms.value.realName.gender) throw new Error('请选择性别')
    } else {
      if (!forms.value.job.organization) throw new Error('请输入学校/单位')
      if (!forms.value.job.department) throw new Error('请输入院系/部门')
    }
    
    if (fileLists.value[type].length === 0) {
      throw new Error('请上传认证文件')
    }

    // 1. 上传文件
    loading.value.fileUpload = true
    const uploadRes = await uploadTeacherFile(fileLists.value[type][0].raw)
    loading.value.fileUpload = false
    
    if (uploadRes.code !== 200 || !uploadRes.result?.url) {
      throw new Error(uploadRes.msg || '文件上传失败')
    }

    // 2. 更新教师信息
    const updateData = {
      id: userId,
      [type === 'realName' ? 'teacherName' : 'organization']: forms.value[type][type === 'realName' ? 'name' : 'organization'],
      [type === 'realName' ? 'sex' : 'department']: forms.value[type][type === 'realName' ? 'gender' : 'department'],
      idphoto: uploadRes.result.url  // 保存文件URL
    }

    const updateRes = await updateTeacher(updateData)
    if (updateRes.code !== 200) {
      throw new Error(updateRes.msg || '信息更新失败')
    }

    // 3. 更新状态
    authStatus.value[type] = true
    dialog.value[type] = false
    ElMessage.success(`${type === 'realName' ? '实名' : '职业'}认证成功`)
    
    // 4. 刷新状态
    await loadAuthStatus()
    
  } catch (error) {
    console.error(`${type}认证失败:`, error)
    ElMessage.error(error.message || `${type === 'realName' ? '实名' : '职业'}认证失败`)
  } finally {
    loading.value[loadingKey] = false
    loading.value.fileUpload = false
  }
}

onMounted(() => {
  loadAuthStatus()
})
</script>

<style scoped>
.auth-info {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.auth-section {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.auth-section h3 {
  margin: 0;
  min-width: 100px;
}

.upload-demo {
  width: 100%;
}
</style>