<template>
  <div class="reply-list">
    <!-- 平级展示所有评论 -->
    <div class="reply-item" v-for="reply in paginatedReplies" :key="reply.id">
      <div class="reply-header">
        <img :src="reply.avatar || defaultAvatar" alt="用户头像" class="avatar">
        <div class="user-info">
          <div class="user-name">{{ reply.name }}</div>
          <div class="user-university">{{ reply.school }}</div>
        </div>

        <el-dropdown v-if="reply.commenterId === userStore.user.id" trigger="click" class="more-actions">
          <span class="el-dropdown-link">
            <img src="@/assets/img/General/icon-more.png" alt="更多操作" class="custom-more-icon">
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="handleDelete(reply)">删除评论</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>

      <div class="reply-content">
        <span v-if="reply.parentId" class="reply-to">
          回复 @{{ getParentName(reply.parentId) }}：
        </span>
        {{ reply.content }}
      </div>

      <div class="reply-meta">

        <span class="like-count" @click="handleLike(reply)">
          <img v-if="reply.isLiked" src="@/assets/img/General/icon-like-actived.png" alt="已点赞" class="like-icon">
          <img v-else src="@/assets/img/General/icon-like-normal.png" alt="未点赞" class="like-icon">
          {{ reply.likeCount || 0 }}
        </span>
        <span class="star-count" @click="handleStar(reply)">
          <img v-if="reply.isStarred" src="@/assets/img/General/icon-star-yes-hover.png" alt="已收藏" class="star-icon">
          <img v-else src="@/assets/img/General/icon-star-yes-normal.png" alt="未收藏" class="star-icon">
          {{ reply.starCount || 0 }}
        </span>
        <span class="comment-count" @click="toggleReplyBox(reply.id)">
          <el-icon>
            <ChatLineRound />
          </el-icon>
          回复
        </span>
        <span class="reply-date">{{ formatDate(reply.publishTime) }}</span>
      </div>

      <!-- 回复框 -->
      <div v-if="activeReplyId === reply.id" class="reply-box">
        <textarea v-model="replyContent" :placeholder="`回复 @${reply.name}`" rows="3" class="reply-textarea"></textarea>
        <div class="reply-actions">
          <button class="cancel-btn" @click="cancelReply">取消</button>
          <button class="submit-btn" @click="submitReply(reply)" :disabled="isSubmitting">
            {{ isSubmitting ? '提交中...' : '提交回复' }}
          </button>
        </div>
      </div>
    </div>

    <el-pagination v-if="flatReplies.length > pageSize" @size-change="handleSizeChange"
      @current-change="handleCurrentChange" :current-page="currentPage" :page-sizes="[8, 16, 24]" :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper" :total="flatReplies.length" class="pagination" />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { formatAgo } from '@/utils/dateUtils';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  saveComment, saveCommentLike, removeCommentLike, getCommentLikeList, removeUserComment,
  removeCommentStar, getCommentStarList, saveCommentStar
} from '@/api/student/discuss';
import { useUserStore } from '@/stores/userStore';
import { useCourseStore } from '@/stores/courseStore'
import { ChatLineRound } from '@element-plus/icons-vue';

const userStore = useUserStore();
const courseStore = useCourseStore()
const defaultAvatar = 'https://shumei-lingxi-platform.oss-cn-guangzhou.aliyuncs.com/courses/default-avatar.png';
const hasJoinedCourse = computed(() => courseStore.hasJoinedCourse)

const props = defineProps({
  replies: {
    type: Array,
    required: true,
    default: () => []
  },
  topicId: {
    type: String,
    required: true
  },
  allReplies: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['reply-success']);

const currentPage = ref(1);
const pageSize = ref(8);
const activeReplyId = ref(null);
const replyContent = ref('');
const isSubmitting = ref(false);

// 递归扁平化评论结构
const flattenReplies = (comments) => {
  const result = [];

  const flatten = (nodes) => {
    nodes.forEach(node => {
      const comment = { ...node };
      // 移除children属性避免重复处理
      if (comment.children) {
        delete comment.children;
      }
      result.push(comment);

      // 递归处理子评论
      if (node.children && node.children.length) {
        flatten(node.children);
      }
    });
  };

  flatten(comments || []);
  return result;
};

// 扁平化后的所有评论
const flatReplies = computed(() => {
  return flattenReplies(props.replies);
});

// 当前页显示的评论
const paginatedReplies = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return flatReplies.value.slice(start, end);
});

// 合并传入的allReplies和当前replies用于查找父评论
const allReplies = computed(() => {
  return [...props.allReplies, ...props.replies];
});

// 根据parentId获取父评论的用户名
const getParentName = (parentId) => {
  const parent = allReplies.value.find(item => item.id === parentId);
  return parent?.name || '已删除用户';
};

const formatDate = (timestamp) => {
  if (!timestamp) return '';
  return formatAgo(timestamp);
};

const handleSizeChange = (newSize) => {
  pageSize.value = newSize;
  currentPage.value = 1;
};

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage;
};

const handleLike = async (comment) => {
  if (!hasJoinedCourse.value) {
    ElMessage.warning('请先加入课程才能点赞')
    return
  }
  try {
    if (comment.isLiked) {
      // 取消点赞流程
      if (!comment.likeId) {
        // 如果没有likeId，先查询点赞记录
        const res = await getCommentLikeList({
          commentId: comment.id,
          userId: userStore.user.id
        });

        if (res.code === 200 && res.result.records.length > 0) {
          comment.likeId = res.result.records[0].id;
        } else {
          throw new Error('找不到点赞记录');
        }
      }

      // 取消点赞
      const cancelRes = await removeCommentLike({ id: comment.likeId });
      if (cancelRes.code === 200) {
        comment.isLiked = false;
        comment.likeCount = Math.max(0, (comment.likeCount || 1) - 1);
        comment.likeId = null;
        ElMessage.success('已取消点赞');
      }
    } else {
      // 点赞流程
      const res = await saveCommentLike({
        commentId: comment.id,
        userId: userStore.user.id
      });

      if (res.code === 200) {
        comment.isLiked = true;
        comment.likeCount = (comment.likeCount || 0) + 1;
        comment.likeId = res.result.id; // 保存点赞记录ID
        ElMessage.success('点赞成功');
      }
    }
  } catch (error) {
    console.error('操作失败:', error);
    ElMessage.error(error.message || '操作失败，请稍后重试');
  }
};

const handleStar = async (comment) => {
  if (!hasJoinedCourse.value) {
    ElMessage.warning('请先加入课程才能收藏')
    return
  }
  try {
    if (comment.isStarred) {
      // 取消收藏流程
      if (!comment.starId) {
        const res = await getCommentStarList({
          commentId: comment.id,
          userId: userStore.user.id
        });
        if (res.code === 200 && res.result.records.length > 0) {
          comment.starId = res.result.records[0].id;
        } else {
          throw new Error('找不到收藏记录');
        }
      }
      const cancelRes = await removeCommentStar({ id: comment.starId });
      if (cancelRes.code === 200) {
        comment.isStarred = false;
        comment.starCount = Math.max(0, (comment.starCount || 1) - 1);
        comment.starId = null;
        ElMessage.success('已取消收藏');
      }
    } else {
      const res = await saveCommentStar({
        postId: comment.id,
        userId: userStore.user.id
      });
      if (res.code === 200) {
        comment.isStarred = true;
        comment.starCount = (comment.starCount || 0) + 1;
        comment.starId = res.result.id;
        ElMessage.success('收藏成功');
      }
    }
  } catch (error) {
    console.error('收藏操作失败:', error);
    ElMessage.error(error.message || '操作失败，请稍后重试');
  }
};

const toggleReplyBox = (commentId) => {
  if (!hasJoinedCourse.value) {
    ElMessage.warning('请先加入课程才能回复')
    return
  }
  if (activeReplyId.value === commentId) {
    activeReplyId.value = null;
  } else {
    activeReplyId.value = commentId;
    replyContent.value = '';
  }
};

const cancelReply = () => {
  activeReplyId.value = null;
  replyContent.value = '';
};

const submitReply = async (comment) => {
  if (!replyContent.value.trim()) {
    ElMessage.warning('请输入回复内容');
    return;
  }

  isSubmitting.value = true;

  try {
    const data = {
      postId: props.topicId,
      commenterId: userStore.user.id,
      content: replyContent.value,
      parentId: comment.id
    };

    const res = await saveComment(data);

    if (res.code === 200) {
      ElMessage.success('回复成功');
      emit('reply-success');
      cancelReply();
    } else {
      ElMessage.error(res.message || '回复失败');
    }
  } catch (error) {
    console.error('提交回复失败:', error);
    ElMessage.error('网络错误，请稍后重试');
  } finally {
    isSubmitting.value = false;
  }
};

const handleDelete = async (comment) => {
  try {
    await ElMessageBox.confirm('确定要删除这条评论吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });
    const res = await removeUserComment({ id: comment.id });
    if (res.code === 200) {
      ElMessage.success('删除成功');
      emit('reply-success');
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
};
</script>

<style scoped lang="scss">
.reply-list {
  padding: 0;

  .reply-item {
    padding: 12px;
    margin-bottom: 12px;
    border-radius: 8px;
    background-color: #f5f5f5;
    border: 1px solid #eee;

    .reply-header {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .avatar {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        margin-right: 10px;
        background-color: #ddd;
      }

      .user-info {
        .user-name {
          font-weight: bold;
          font-size: 14px;
        }

        .user-university {
          font-size: 12px;
          color: #666;
        }
      }

      .custom-more-icon {
        width: 24px;
        height: 24px;
        cursor: pointer;
        transition: opacity 0.3s;

        &:hover {
          opacity: 0.8;
        }
      }

      .more-actions {
        margin-left: auto;
        padding: 4px;
      }
    }

    .reply-content {
      margin-left: 46px;
      margin-bottom: 8px;
      line-height: 1.5;
      font-size: 14px;
      word-break: break-word;
      white-space: pre-wrap;

      .reply-to {
        color: #409eff;
        font-weight: bold;
      }
    }

    .reply-meta {
      margin-left: 46px;
      font-size: 12px;
      color: #666;
      display: flex;
      align-items: center;
      gap: 15px;
      position: relative;



      >span {
        display: flex;
        align-items: center;
        gap: 4px;
        cursor: pointer;

        &:hover {
          color: var(--el-color-primary);
        }

        .like-icon {
          width: 12px;
          height: 12px;
        }

        .star-icon {
          width: 16px;
          height: 16px;
        }

        &.reply-date {
          margin-left: auto; 
          cursor: default; 
          pointer-events: none; 
        }
      }
    }

    .reply-box {
      margin-top: 10px;
      border: 1px solid #eee;
      border-radius: 4px;
      padding: 10px;
      background-color: #fff;

      .reply-textarea {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        resize: vertical;
        min-height: 60px;
        margin-bottom: 10px;
      }

      .reply-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;

        button {
          padding: 5px 15px;
          border-radius: 4px;
          cursor: pointer;

          &.cancel-btn {
            border: 1px solid #ddd;
            background: #fff;

            &:hover {
              background: #f5f5f5;
            }
          }

          &.submit-btn {
            border: none;
            background: var(--el-color-primary);
            color: #fff;

            &:disabled {
              opacity: 0.6;
              cursor: not-allowed;
            }
          }
        }
      }
    }
  }

  .pagination {
    margin-top: 16px;
    text-align: right;
  }
}
</style>