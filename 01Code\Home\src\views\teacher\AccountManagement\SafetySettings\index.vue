<template>
  <div class="safety-settings">
    <el-form :model="safetyForm" label-width="120px">
      <!-- 手机号部分 -->
      <el-form-item label="手机号">
        <div class="form-item-container">
          <el-input v-model="safetyForm.phone" disabled class="input-with-btn"></el-input>
          <div class="btn-group">
            <el-button type="text" @click="showUnbindPhoneDialog" class="action-btn">解绑手机号</el-button>
            <el-button type="primary" @click="showBindPhoneDialog" class="action-btn">更换</el-button>
          </div>
        </div>

        <!-- 绑定/更换手机号面板 -->
        <div v-if="bindPhonePanelVisible" class="bind-panel">
          <el-form>
            <el-form-item label="新的手机号">
              <el-input v-model="newPhone"></el-input>
            </el-form-item>
            <el-form-item label="手机验证码">
              <div class="code-input-container">
                <el-input v-model="phoneVerificationCode"></el-input>
                <el-button type="primary" @click="getVerificationCode" class="get-code-btn">获取验证码</el-button>
              </div>
            </el-form-item>
          </el-form>
          <div class="panel-actions">
            <el-button type="primary" @click="confirmBindPhone">确定</el-button>
          </div>
        </div>
      </el-form-item>

      <!-- 邮箱部分 -->
      <el-form-item label="邮箱">
        <div class="form-item-container">
          <el-input v-model="safetyForm.email" :disabled="isEmailBound" class="input-with-btn"></el-input>
          <div class="btn-group">
            <el-button v-if="!isEmailBound" type="primary" @click="showBindEmailPanel" class="action-btn">绑定</el-button>
            <el-button v-else type="primary" @click="showChangeEmailPanel" class="action-btn">修改</el-button>
          </div>
        </div>

        <!-- 绑定/更换邮箱面板 -->
        <div v-if="bindEmailPanelVisible" class="bind-panel">
          <el-form>
            <el-form-item label="邮箱地址">
              <el-input v-model="emailToBind"></el-input>
            </el-form-item>
            <el-form-item label="邮箱验证码">
              <div class="code-input-container">
                <el-input v-model="emailVerificationCode"></el-input>
                <el-button type="primary" @click="getVerificationCode" class="get-code-btn">获取验证码</el-button>
              </div>
            </el-form-item>
          </el-form>
          <div class="panel-actions">
            <el-button type="primary" @click="bindEmail">确定</el-button>
          </div>
        </div>
      </el-form-item>

      <!-- 密码部分 -->
      <el-form-item label="密码">
        <div class="form-item-container">
          <el-input v-model="safetyForm.password" type="password" :disabled="isPasswordDisabled" class="input-with-btn"></el-input>
          <div class="btn-group">
            <el-button type="primary" @click="showChangePasswordPanel" class="action-btn">修改</el-button>
          </div>
        </div>

        <!-- 修改密码面板 -->
        <div v-if="changePasswordPanelVisible" class="bind-panel">
          <el-form>
            <el-form-item label="旧密码">
              <el-input v-model="passwordForm.oldPassword" type="password"></el-input>
            </el-form-item>
            <el-form-item label="新密码">
              <el-input v-model="passwordForm.newPassword" type="password"></el-input>
            </el-form-item>
            <el-form-item label="确认新密码">
              <el-input v-model="passwordForm.confirmPassword" type="password"></el-input>
            </el-form-item>
          </el-form>
          <div class="panel-actions">
            <el-button type="primary" @click="updatePassword">确定</el-button>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="登录限制">
        <el-switch v-model="safetyForm.loginRestriction"></el-switch>
      </el-form-item>
    </el-form>

    <!-- 解绑手机号弹窗 -->
    <el-dialog v-model="unbindPhoneDialogVisible" title="解绑手机号" width="30%">
      <div class="unbind-dialog-content">
        <p>手机号解绑后，将无法使用该手机号登录平台，是否解绑？</p>
        <div class="code-input-container">
          <el-input v-model="phoneVerificationCode" placeholder="请输入手机验证码"></el-input>
          <el-button type="primary" @click="getVerificationCode" class="get-code-btn">获取验证码</el-button>
        </div>
        <div class="dialog-actions">
          <el-button @click="unbindPhoneDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmUnbindPhone">确定解绑</el-button>
        </div>
      </div>
    </el-dialog>     
  </div>
</template>

<script setup>
import { ref } from 'vue';

const isEmailBound = ref(true); // 假设邮箱已绑定
const isPasswordDisabled = ref(false); // 假设密码可修改
const safetyForm = ref({
  phone: '18798526502',
  email: '<EMAIL>',
  password: '******',
  loginRestriction: false
});
const newPhone = ref('');
const phoneVerificationCode = ref('');
const unbindPhoneDialogVisible = ref(false);
const bindPhonePanelVisible = ref(false);
const bindEmailPanelVisible = ref(false);
const changePasswordPanelVisible = ref(false);
const passwordForm = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
});

const showUnbindPhoneDialog = () => {
  unbindPhoneDialogVisible.value = true;
};

const showBindPhoneDialog = () => {
  bindPhonePanelVisible.value = true;
};

const showBindEmailPanel = () => {
  bindEmailPanelVisible.value = true;
};

const showChangeEmailPanel = () => {
  bindEmailPanelVisible.value = true;
};

const showChangePasswordPanel = () => {
  changePasswordPanelVisible.value = true;
};

const confirmUnbindPhone = () => {
  // 确认解绑手机号逻辑
  unbindPhoneDialogVisible.value = false;
};

const confirmBindPhone = () => {
  // 确认绑定手机号逻辑
  bindPhonePanelVisible.value = false;
};

const bindEmail = () => {
  // 绑定邮箱逻辑
  bindEmailPanelVisible.value = false;
};

const updatePassword = () => {
  // 修改密码逻辑
  changePasswordPanelVisible.value = false;
};
</script>

<style scoped>
.safety-settings {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.form-item-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.input-with-btn {
  flex: 1;
  min-width: 200px;
}

.btn-group {
  display: flex;
  gap: 8px;
}

.action-btn {
  min-width: 80px;
}

.bind-panel {
  margin-top: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.code-input-container {
  display: flex;
  gap: 10px;
}

.get-code-btn {
  min-width: 120px;
}

.panel-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.unbind-dialog-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}
</style>