<template>
  <div class="teaching-team-container">
    <div class="team-header">
      <h2>教学团队</h2>
      <button class="add-teacher-btn" @click="showAddMenu = !showAddMenu">
        添加老师
        <span class="dropdown-icon">▼</span>
      </button>
      
      <div v-if="showAddMenu" class="add-menu">
        <button @click="openAddModal('direct')">直接添加</button>
        <button @click="openTeamSelection">在教师列表里选择</button>
      </div>
    </div>

    <div v-if="!hasTeachers" class="empty-state">
      <p>暂无授课教师，请添加</p>
    </div>

    <div v-else class="teacher-grid-container">
      <div 
        v-for="teacher in allTeachers" 
        :key="teacher.id" 
        class="teacher-card"
      >
        <div class="teacher-actions">
          <button class="edit-btn" @click="showTeacherDetails(teacher)">查看</button>
          <button class="delete-btn" @click="confirmDelete(teacher)">移除</button>
        </div>
        
        <div class="teacher-content">
          <img :src="teacher.teacherImg || '/images/default-avatar.png'" class="teacher-avatar">
          <div class="teacher-info">
            <div class="name-tags-container">
              <h3 class="name">{{ teacher.teacherName }}</h3>
              <div class="tags">
                <span v-if="teacher.teamRole" class="tag tag-course">{{ getRoleName(teacher.teamRole) }}</span>
              </div>
            </div>
            <p class="title">{{ teacher.teacherTitle }} | {{ teacher.department }}</p>
            <p class="desc">{{ teacher.teacherIntro || '暂无教学经历描述' }}</p>
          </div>
        </div>
      </div>
    </div>

    <AddTeacherModal
      v-if="showAddModal"
      :teacherData="currentTeacher"
      :isEditMode="isEditMode"
      @close="closeModal"
      @confirm="handleTeacherConfirm"
    />

    <TeamSelectionModal
      v-if="showTeamSelection"
      :courseId="course.id"
      @close="showTeamSelection = false"
      @add-member-success="fetchCourseDetail"
    />

    <div v-if="showDeleteConfirm" class="confirm-dialog-overlay">
      <div class="confirm-dialog">
        <h3>确认移除</h3>
        <p>确定要从教学团队里移除 {{ deletingTeacher?.teacherName }} 老师吗？</p>
        <div class="dialog-buttons">
          <button class="cancel-btn" @click="showDeleteConfirm = false">取消</button>
          <button class="confirm-btn" @click="executeDelete">确定</button>
        </div>
      </div>
    </div>

    <!-- 教师详情模态窗 -->
    <div v-if="selectedTeacher" class="teacher-detail-modal">
      <div class="modal-content">
        <span class="close" @click="selectedTeacher = null">&times;</span>
        <div class="modal-img-container">
          <img :src="selectedTeacher.teacherImg || '/images/default-avatar.png'" class="modal-avatar">
        </div>
        <div class="modal-info">
          <h3>{{ selectedTeacher.teacherName }}</h3>
          <p class="modal-title">{{ selectedTeacher.teacherTitle }} | {{ selectedTeacher.department }}</p>
          <div class="modal-description">
            <div class="detail-row">
              <span class="detail-label">职称：</span>
              <span class="detail-value">{{ selectedTeacher.teacherTitle || '暂无' }}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">学院：</span>
              <span class="detail-value">{{ selectedTeacher.department || '暂无' }}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">团队角色：</span>
              <span class="detail-value">{{ getRoleName(selectedTeacher.teamRole) }}</span>
            </div>
            <div class="detail-row full-width">
              <span class="detail-label">教师简介：</span>
              <p class="detail-value">{{ selectedTeacher.teacherIntro || '暂无教师简介' }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue';
import AddTeacherModal from './AddTeacherModal.vue';
import TeamSelectionModal from './TeamSelectionModal.vue';
import { getCourseDetail, removeTeacherFromTeam } from '@/api/teacher/courseTeam';

export default {
  components: {
    AddTeacherModal,
    TeamSelectionModal
  },
  props: {
    course: {
      type: Object,
      required: true,
      validator: (course) => {
        return typeof course.id !== 'undefined'
      }
    }
  },
  setup(props) {
    const showAddMenu = ref(false);
    const showAddModal = ref(false);
    const showTeamSelection = ref(false);
    const showDeleteConfirm = ref(false);
    const isEditMode = ref(false);
    const currentTeacher = ref(null);
    const deletingTeacher = ref(null);
    const selectedTeacher = ref(null);
    const allTeachers = ref([]);
    const loading = ref(false);
    const hasTeachers = computed(() => allTeachers.value.length > 0);

    const fetchCourseDetail = async () => {
      try {
        loading.value = true;
        const response = await getCourseDetail({ 
          id: props.course.id
        });
        
        if (response?.result) {
          allTeachers.value = response.result.teachers || [];
        }
      } catch (error) {
        ElMessage.error('加载失败: ' + (error.response?.data?.message || error.message));
      } finally {
        loading.value = false;
      }
    };

    const showTeacherDetails = (teacher) => {
      selectedTeacher.value = teacher;
    };

    const confirmDelete = (teacher) => {
      deletingTeacher.value = teacher;
      showDeleteConfirm.value = true;
    };

    const executeDelete = async () => {
      try {
        await removeTeacherFromTeam(props.course.id, deletingTeacher.value.id);
        allTeachers.value = allTeachers.value.filter(t => t.id !== deletingTeacher.value.id);
        ElMessage.success('移除成功');
      } catch (error) {
        ElMessage.error('移除失败: ' + (error.response?.data?.message || error.message));
      } finally {
        showDeleteConfirm.value = false;
        deletingTeacher.value = null;
      }
    };

    const getRoleName = (teamRole) => {
      const roles = {
        1: '创建者',
        2: '教师',
        3: '助教'
      };
      return roles[teamRole] || '未知角色';
    };

    const openTeamSelection = () => {
      showAddMenu.value = false;
      showTeamSelection.value = true;
    };

    const openAddModal = (mode) => {
      isEditMode.value = false;
      currentTeacher.value = null;
      showAddMenu.value = false;
      showAddModal.value = true;
    };
    
    const closeModal = () => {
      showAddModal.value = false;
      isEditMode.value = false;
      currentTeacher.value = null;
    };

    onMounted(() => {
      fetchCourseDetail();
    });

    return {
      showAddMenu,
      showAddModal,
      showTeamSelection,
      showDeleteConfirm,
      isEditMode,
      currentTeacher,
      deletingTeacher,
      selectedTeacher,
      allTeachers,
      loading,
      hasTeachers,
      fetchCourseDetail,
      showTeacherDetails,
      confirmDelete,
      executeDelete,
      getRoleName,
      openAddModal,
      openTeamSelection,
      closeModal
    };
  }
}
</script>

<style scoped>
.teaching-team-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Arial', sans-serif;
}

.team-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  position: relative;
}

.add-teacher-btn {
  background-color: #1890ff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.2s;
}

.add-teacher-btn:hover {
  background-color: #40a9ff;
}

.dropdown-icon {
  margin-left: 8px;
  font-size: 12px;
}

.add-menu {
  position: absolute;
  right: 0;
  top: 40px;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 180px;
}

.add-menu button {
  display: block;
  width: 100%;
  padding: 8px 16px;
  text-align: left;
  background: none;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-menu button:hover {
  background-color: #f5f5f5;
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #999;
  border: 1px dashed #e8e8e8;
  border-radius: 4px;
  margin-top: 20px;
}

.teacher-grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.teacher-card {
  position: relative;
  padding: 16px;
  margin-top: 20px;
  border: 1px solid #eee;
  border-radius: 8px;
  background: white;
  transition: all 0.4s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.teacher-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.teacher-content {
  display: flex;
  gap: 16px;
}

.teacher-avatar {
  width: 80px;
  height: 100px;
  border-radius: 4px;
  object-fit: cover;
  flex-shrink: 0;
  border: 1px solid #f0f0f0;
}

.teacher-info {
  flex: 1;
  min-width: 0;
}

.name-tags-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 8px;
  gap: 8px;
}

.name {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  line-height: 1.3;
}

.tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.tag {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.5;
}

.tag-course {
  background-color: #c3e6cb;
  color: #216832;
}

.title {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.desc {
  margin: 0;
  font-size: 13px;
  color: #888;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.teacher-actions {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  gap: 6px;
}

.edit-btn, .delete-btn {
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
  cursor: pointer;
  border: none;
  transition: all 0.2s;
}

.edit-btn {
  background-color: #1890ff;
  color: white;
}

.edit-btn:hover {
  background-color: #40a9ff;
}

.delete-btn {
  background-color: #ff4d4f;
  color: white;
}

.delete-btn:hover {
  background-color: #ff7875;
}

.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.confirm-dialog {
  background: white;
  border-radius: 8px;
  padding: 20px;
  width: 400px;
  max-width: 90%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.confirm-dialog h3 {
  margin-top: 0;
  color: #333;
}

.confirm-dialog p {
  margin: 15px 0;
  color: #666;
}

.dialog-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

.cancel-btn {
  padding: 8px 16px;
  background: white;
  border: 1px solid #d9d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-btn:hover {
  background-color: #f5f5f5;
}

.confirm-btn {
  padding: 8px 16px;
  background: #ff4d4f;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.confirm-btn:hover {
  background: #ff7875;
}

/* 教师详情模态窗样式 */
.teacher-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.modal-content {
  background-color: white;
  padding: 30px;
  border-radius: 10px;
  width: 80%;
  height: 80%;
  max-width: 650px;
  position: relative;
}

.close {
  position: absolute;
  top: 15px;
  right: 15px;
  font-size: 15px;
  cursor: pointer;
  color: #666;
}

.close:hover {
  color: #333;
}

.modal-img-container {
  text-align: center;
  margin-bottom: 20px;
}

.modal-avatar {
  width: 150px;
  height: 150px;
  border-radius: 10px;
  object-fit: cover;
}

.modal-info {
  width: 100%;
  text-align: center;
}

.modal-info h3 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #333;
}

.modal-title {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #555;
  font-weight: 500;
}

.modal-description {
  margin-top: 20px;
  text-align: left;
}

.detail-row {
  display: flex;
  margin-bottom: 10px;
  line-height: 1.6;
}

.detail-row.full-width {
  display: block;
}

.detail-label {
  font-weight: bold;
  min-width: 80px;
  color: #555;
}

.detail-value {
  flex: 1;
  color: #333;
  font-size: 12px;
  max-height: 175px;
  overflow-x: hidden; /* 水平方向不显示滚动条 */
  overflow-y: auto; /* 垂直方向溢出时显示滚动条 */
}
</style>