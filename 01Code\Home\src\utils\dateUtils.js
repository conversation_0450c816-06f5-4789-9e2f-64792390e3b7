// src/utils/dateUtils.js

/**
 * 格式化时间戳为 "YYYY-MM-DD HH:mm"
 */
export function formatTime(timestamp) {
  const date = new Date(timestamp)
  return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())} ${padZero(date.getHours())}:${padZero(date.getMinutes())}`
}

/**
 * 显示为人性化的时间（如 刚刚、5分钟前、昨天）
 */
export function formatAgo(timestamp) {
  const now = new Date()
  const diff = now.getTime() - timestamp
  const minutes = Math.floor(diff / 1000 / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days === 1) return '昨天'
  if (days < 7) return `${days}天前`
  return formatTime(timestamp)
}

function padZero(num) {
  return num.toString().padStart(2, '0')
}


export function convertToTimestamp(dateStr, timeStr = '00:00') {
  if (!dateStr) return 0;
  
  const [year, month, day] = dateStr.split('-');
  const [hours = 0, minutes = 0] = timeStr.split(':');
  
  const date = new Date(
    parseInt(year),
    parseInt(month) - 1, // 月份是0-11
    parseInt(day),
    parseInt(hours),
    parseInt(minutes)
  );
  
  return date.getTime(); 
}