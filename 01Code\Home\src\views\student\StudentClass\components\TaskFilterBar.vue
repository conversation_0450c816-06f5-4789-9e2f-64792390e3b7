<!-- src/views/student/StudentClass/components/TaskFilterBar.vue -->
<template>
  <div class="task-filter-bar">
    <div class="filter-row">
      <!-- 状态筛选 -->
      <div class="filter-group status-group">
        <span class="group-label">状态</span>
        <span 
          v-for="(status, index) in statuses" 
          :key="index"
          :class="{ 'active': activeStatus === index }"
          @click="handleStatusClick(index)"
        >{{ status }}</span>
      </div>
      
      <!-- 搜索框 -->
      <div class="search-box">
        <input 
          v-model="searchQuery" 
          type="text" 
          placeholder="请输入课程名称"
          @keyup.enter="handleSearch"
        >
        <button @click="handleSearch">
          <img 
            src="@/assets/img/Teacher/icon-seacher-normal.png" 
            alt="搜索"
            class="search-icon"
          >
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const statuses = ['全部', '正在进行', '已结束'];
const activeStatus = ref(0);
const searchQuery = ref('');

const emit = defineEmits(['filter-change']);

const handleStatusClick = (index) => {
  activeStatus.value = index;
  emitFilterChange();
};

const handleSearch = () => {
  emitFilterChange();
};

const emitFilterChange = () => {
  emit('filter-change', { 
    status: activeStatus.value,
    query: searchQuery.value
  });
};

defineExpose({
  activeStatus,
  searchQuery
});
</script>

<style lang="scss" scoped>
/* 复用教师端相同的样式 */
@use '@/styles/student/TaskFilterBar';
</style>