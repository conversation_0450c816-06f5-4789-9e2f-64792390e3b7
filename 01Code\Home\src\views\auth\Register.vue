<template>
  <div class="register-container">
    <div class="register-wrapper">
      <div class="register-card">
        <h2 class="register-title">用户注册</h2>
        <form @submit.prevent="handleRegister" class="register-form">
          <div class="form-group">
            <label class="form-label">账号：</label>
            <div class="form-input-container">
              <span class="form-icon"><i class="fa fa-user"></i></span>
              <input v-model="form.account" type="text" required class="form-input">
            </div>
          </div>
          
          <div class="form-group">
            <label class="form-label">密码：</label>
            <div class="form-input-container">
              <span class="form-icon"><i class="fa fa-lock"></i></span>
              <input v-model="form.password" type="password" required class="form-input">
            </div>
          </div>
          
          <div class="form-group">
            <label class="form-label">确认密码：</label>
            <div class="form-input-container">
              <span class="form-icon"><i class="fa fa-lock"></i></span>
              <input v-model="form.confirmPassword" type="password" required class="form-input">
            </div>
          </div>
          
          <div class="form-group">
            <label class="form-label">身份：</label>
            <div class="form-input-container">
              <span class="form-icon"><i class="fa fa-id-card"></i></span>
              <select v-model="form.role" class="form-input">
                <option value="1">学生</option>
                <option value="2">教师</option>
              </select>
            </div>
          </div>
          
          <button type="submit" :disabled="isSubmitting" 
                  class="register-button bg-primary hover:bg-primary/90 text-white font-medium py-3 px-4 rounded-lg transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-primary/50">
            {{ isSubmitting ? '注册中...' : '注册' }}
          </button>
          
          <p v-if="registerSuccess" class="success-message">
            注册成功！请点击下方链接登录
          </p>
          
          <p class="login-link">
            已有账号？<router-link to="/login" class="text-primary hover:text-primary/80">立即登录</router-link>
          </p>
        </form>
      </div>
      
      <div class="register-image">
        <img src="@/assets/img/General/background-register.png" alt="注册背景图" class="side-image">
        <div class="image-overlay">
          <h3 class="image-title">加入我们</h3>
          <p class="image-subtitle">开启您的学习/教学之旅</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { register } from '@/api/auth/auth.js'
import { useAuthStore } from '@/stores/auth/auth.js'

const router = useRouter()
const authStore = useAuthStore()

const form = ref({
  account: '',
  password: '',
  confirmPassword: '',
  role: '1' // 默认学生
})

const isSubmitting = ref(false)
const errorMessage = ref('')
const registerSuccess = ref(false) // 新增注册成功状态

const handleRegister = async () => {
  // 表单验证
  if (form.value.password !== form.value.confirmPassword) {
    errorMessage.value = '两次输入的密码不一致'
    return
  }

  if (form.value.password.length < 6) {
    errorMessage.value = '密码长度不能少于6位'
    return
  }

  isSubmitting.value = true
  errorMessage.value = ''
  registerSuccess.value = false // 重置注册成功状态

  try {
    const submitData = {
      account: form.value.account,
      password: form.value.password,
      role: Number(form.value.role)
    }
    
    const response = await register(submitData)
    
    if (response.success) {
      // 注册成功，不跳转，只设置成功状态
      registerSuccess.value = true
      // 清空表单
      form.value = {
        account: '',
        password: '',
        confirmPassword: '',
        role: '1'
      }
    } else {
      throw new Error(response.msg || '注册失败')
    }
  } catch (error) {
    errorMessage.value = error.message.includes('Network Error') 
      ? '网络连接失败，请检查网络设置'
      : error.message || '注册失败，请重试'
    console.error('注册错误:', error)
  } finally {
    isSubmitting.value = false
  }
}
</script>

<style scoped lang="scss">
@use '@/styles/auth/register.scss';


</style>