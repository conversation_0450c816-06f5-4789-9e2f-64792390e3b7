<template>
  <div class="student-announcement">
    <!-- 公告头部 -->
    <NoticeHeader />

    <!-- 公告列表 -->
    <NoticeList
      :notices="filteredNotices"
      :pagination="noticeStore.pagination"
      @page-change="changePage"
      @notice-click="showNoticeDetail"
      @retry="fetchNotices"
    />

    <!-- 公告详情弹窗 -->
    <NoticeDetail
      :visible="showDetail"
      @close="closeDetail"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useStudentNoticeStore } from '@/stores/student/noticeStore'
import NoticeHeader from './components/NoticeHeader.vue'
import NoticeList from './components/NoticeList.vue'
import NoticeDetail from './components/NoticeDetail.vue'

const route = useRoute()
const noticeStore = useStudentNoticeStore()
const showDetail = ref(false)

// 获取课程ID
const courseId = computed(() => route.params.courseId)

// 获取过滤后的公告列表
const filteredNotices = computed(() => {
  return noticeStore.filteredNotices()
})

// 获取公告列表
const fetchNotices = async (pageNum = 1) => {
  try {
    const params = {
      pageNum,
      pageSize: noticeStore.pagination.pageSize,
      courseId: courseId.value
    }

    // 始终获取所有类型的数据，让前端进行过滤以确保数量统计正确
    await noticeStore.fetchNotices(params)
  } catch (error) {
    console.error('获取公告列表失败:', error)
  }
}

// 切换页码
const changePage = (pageNum) => {
  fetchNotices(pageNum)
}

// 显示公告详情
const showNoticeDetail = () => {
  showDetail.value = true
}

// 关闭公告详情
const closeDetail = () => {
  showDetail.value = false
}

// 监听标签切换
watch(() => noticeStore.activeTab, () => {
  fetchNotices(1) // 切换标签时回到第一页
})

// 初始化加载
onMounted(() => {
  fetchNotices()
})
</script>

<style lang="scss" scoped>
@use '@/styles/variables';

.student-announcement {
  background-color: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>