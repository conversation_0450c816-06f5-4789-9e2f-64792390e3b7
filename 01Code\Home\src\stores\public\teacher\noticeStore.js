import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { saveNotice, getNoticeList, deleteNotice, updateNotice, getNoticeDetail } from '@/api/teacher/notice'

const NOTICE_TYPES = {
  course: {
    value: 0,
    label: '课程公告'
  },
  homework: {
    value: 1,
    label: '作业通知'
  },
  exam: {
    value: 2,
    label: '考试通知'
  }
}

const NOTICE_TYPE_MAP = {
  0: 'course',
  1: 'homework',
  2: 'exam'
}

const typeCounts = ref({
  course: 0,
  homework: 0,
  exam: 0
})



export const useNoticeStore = defineStore('notice', () => {

  const route = useRoute()
  const notices = ref([])
  const activeTab = ref('course') // 当前激活的标签
  const isLoading = ref(false)
  const error = ref(null)
  const selectedNotice = ref(null) // 当前选中的公告
  const isEditing = ref(false)
  const pagination = ref({
    pageNum: 1,
    pageSize: 3,
    total: 0,
    pages: 0
  })

  const draft = ref({
    title: '',
    content: '',
    type: 'course' // 默认课程公告
  })

  //公告数量
  const fetchTypeCounts = async () => {
    try {
      const typeKeys = Object.keys(NOTICE_TYPES) // ['course', 'homework', 'exam']

      const requests = typeKeys.map(type =>
        getNoticeList({
          courseId: route.params.courseId,
          noticeType: NOTICE_TYPES[type].value,
          pageSize: 1,
          pageNum: 1
        }).then(res => ({
          type,
          total: res.result?.total || 0
        }))
      )

      const results = await Promise.all(requests)
      results.forEach(({ type, total }) => {
        typeCounts.value[type] = total
      })
    } catch (err) {
      console.error('获取公告总数失败:', err)
      throw err
    }
  }

  // 获取公告列表
  const fetchNotices = async (params = {}) => {
    try {
      isLoading.value = true
      error.value = null
      const requestParams = {
        pageNum: params.pageNum ?? pagination.value.pageNum,
        pageSize: params.pageSize ?? pagination.value.pageSize,
        courseId: route.params.courseId,
        noticeType: NOTICE_TYPES[activeTab.value].value,
        sortType: 'descending',
        sortColumn: 'publishTime'
      }
      const response = await getNoticeList(requestParams)

      if (response.code === 200) {
        // 直接使用API返回的分页数据
        notices.value = response.result.records || []

        // 更新分页信息
        pagination.value = {
          pageNum: response.result.current || requestParams.pageNum,
          pageSize: response.result.size || requestParams.pageSize,
          total: response.result.total || 0,
          pages: response.result.pages || 1
        }

        if (notices.value.length === 0 && pagination.value.pageNum > 1) {
          const prevPage = pagination.value.pageNum - 1
          if (prevPage < 1) {
            return { records: [], current: 1, size: requestParams.pageSize, total: 0, pages: 1 }
          }

          return await fetchNotices({
            ...params,
            pageNum: prevPage
          })
        }

        return response.result
      } else {
        throw new Error(response.msg || '获取公告列表失败')
      }
    } catch (err) {
      error.value = err.message || '获取公告列表失败'
      console.error('获取公告列表失败:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 获取单个公告详情
  const fetchNoticeDetail = async (noticeId) => {
    try {
      isLoading.value = true
      const response = await getNoticeDetail(noticeId)

      if (response.code === 200) {
        selectedNotice.value = response.result || {}
        await fetchTypeCounts()
        return response.result
      }

      throw new Error(response.message || '获取公告详情失败')
    } catch (err) {
      console.error('获取公告详情失败:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 编辑前准备（获取公告详情）
  const prepareEdit = async (noticeId) => {
    isEditing.value = true
    await fetchNoticeDetail(noticeId)
  }


  // 删除公告
  const removeNotice = async (noticeId) => {
    try {
      isLoading.value = true
      const response = await deleteNotice(noticeId)  // 直接传递noticeId

      // 根据你的API响应结构调整
      if (response.code === 200) {
        notices.value = notices.value.filter(notice => notice.id !== noticeId)
        await fetchTypeCounts()
        return true  // 返回成功状态
      } else {
        throw new Error(response.msg || '删除公告失败')
      }
    } catch (err) {
      error.value = err.message || '删除公告失败'
      console.error('删除公告失败:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }
  // 更新公告
  const updateNoticeItem = async (data) => {
    try {
      isLoading.value = true
      const response = await updateNotice(data) // 使用导入的API方法
      if (response.code === 200) {
        // 更新本地状态中的公告
        const index = notices.value.findIndex(notice => notice.id === data.id)
        if (index !== -1) {
          notices.value[index] = { ...notices.value[index], ...data }
        }
        await fetchTypeCounts()
        return response.result
      }

      throw new Error(response.msg || '更新公告失败')
    } catch (err) {
      error.value = err.message || '更新公告失败'
      console.error('更新公告失败:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }


  // 更新草稿
  const updateDraft = ({ title, content, type }) => {
    draft.value = { title, content, type }
  }

  // 清空草稿
  const clearDraft = () => {
    draft.value = { title: '', content: '', type: 'course' }
  }

  // 返回状态和动作
  return {
    typeCounts,
    notices,
    NOTICE_TYPES,
    NOTICE_TYPE_MAP,
    pagination,
    activeTab,
    draft,
    isLoading,
    error,
    selectedNotice,
    isEditing,
    updateDraft,
    updateNoticeItem,
    clearDraft,
    removeNotice,
    fetchNotices,
    fetchTypeCounts,
    fetchNoticeDetail,
    prepareEdit,

  }
})