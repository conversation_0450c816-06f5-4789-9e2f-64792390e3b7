// abilityGraph.js
import request from '@/api/service';

// 获取能力图谱根节点ID
export function getRootNodeId(graphId) {
    return request({
        url: `/ability-graph/graphs/${graphId}/root-node-id`,
        method: 'get'
    })
}

// 创建主能力
export function createMainAbility(data) {
    return request({
        url: '/ability-graph/main-abilities/create',
        method: 'post',
        data,
        validateStatus: function (status) {
            // 处理400错误，以便在组件中获取更详细的错误信息
            return status < 500; // 只要状态码小于500都认为是有效的响应
        }
    })
}

// 创建子能力
export function createSubAbility(data) {
    return request({
        url: '/ability-graph/sub-abilities/create',
        method: 'post',
        data,
        validateStatus: function (status) {
            // 处理400错误，以便在组件中获取更详细的错误信息
            return status < 500; // 只要状态码小于500都认为是有效的响应
        }
    })
}

// 获取课程所有图谱
export function getGraphsByCourse(courseId) {
    return request({
        url: '/knowledge-graph/list-by-course',
        method: 'get',
        params: { courseId }
    })
}

// 获取图谱主能力列表
export function getMainAbilities(graphId) {
    return request({
        url: `/ability-graph/graphs/${graphId}/main-abilities`,
        method: 'get'
    })
}

// 获取子能力精简列表
export function getSubAbilitiesLite(parentId) {
    return request({
        url: `/ability-graph/abilities/${parentId}/sub-abilities`,
        method: 'get'
    })
}

// 获取主能力详情
export function getMainAbilityDetail(abilityId) {
    return request({
        url: `/ability-graph/abilities/${abilityId}/detail`,
        method: 'get'
    })
}

// 更新能力节点信息
export function updateAbility(data) {
    return request({
        url: '/ability-graph/abilities/update',
        method: 'post',
        data,
        validateStatus: function (status) {
            return status < 500;
        }
    })
}

// 删除能力节点接口
export function deleteAbility(abilityId) {
    return request({
        url: '/ability-graph/abilities/delete',
        method: 'post',
        params: { abilityId }
    })
}

// 根据知识图谱ID获取树状结构的知识点
export function getKnowledgeTree(graphId) {
    return request({
        url: '/knowledge-graph/tree-view',
        method: 'get',
        params: { graphId }
    })
}

// 获取关联过的知识点
export function getRelatedKnowledgeTree(abilityId) {
    return request({
        url: `/ability-graph/abilities/${abilityId}/knowledge-tree`,
        method: 'get',
        validateStatus: function (status) {
            return status < 500; // 捕获400错误
        }
    })
}

// 更新子能力关联的知识点
export function updateRelatedKnowledge(data) {
    return request({
        url: '/ability-graph/abilities/relate-knowledge',
        method: 'post',
        data
    })
}