<template>
  <div class="ppt-container">
    <div class="headers">
      <h1>智能PPT生成</h1>
      <p>输入您的PPT要求，我们将为您生成专业的演示文稿</p>
    </div>

    <div class="input-section">
      <div class="input-group">
        <label for="prompt">PPT要求:</label>
        <textarea
          id="prompt"
          v-model="prompt"
          placeholder="请输入您的PPT要求，例如主题、内容、风格等..."
        ></textarea>
      </div>

      <!-- 可选参数设置 -->
      <div class="options-section">
        <h3>高级选项</h3>

        <div class="option-group">
          <label>
            <input type="checkbox" v-model="isFigure"> 自动配图
          </label>

          <label v-if="isFigure">
            配图类型:
            <select v-model="aiImage">
              <option value="normal">普通配图 (20%)</option>
              <option value="advanced">高级配图 (50%)</option>
            </select>
          </label>
        </div>

        <div class="option-group">
          <label>
            <input type="checkbox" v-model="isCardNote"> 生成演讲备注
          </label>
        </div>

        <div class="option-group">
          <label>
            语言:
            <select v-model="language">
              <option value="cn">中文</option>
            </select>
          </label>
        </div>
      </div>

      <button @click="generatePPT" :disabled="isGenerating" class="generate-btn">
        <span v-if="isGenerating" class="loading">生成中...</span>
        <span v-else>生成PPT</span>
      </button>
    </div>

    <div v-if="isGenerating" class="loading-section">
      <div class="loading-spinner"></div>
      <p>正在生成PPT，请稍候... (这可能需要1-2分钟)</p>
      <p class="loading-tip">提示：详细的提示词会生成更高质量的PPT</p>
    </div>

    <div class="result-section" v-if="resultData">
      <h2>生成结果</h2>
      <div v-if="resultData.error" class="error-message">
        <h3>错误信息</h3>
        <p>{{ resultData.error }}</p>
        <button @click="retryGenerate" class="retry-btn">重试</button>
      </div>
      <div v-else class="result-content">
        <div class="result-info">
          <p><strong>请求ID:</strong> {{ resultData.sid }}</p>
          <p><strong>主标题:</strong> {{ resultData.title }}</p>
          <p><strong>副标题:</strong> {{ resultData.subTitle }}</p>

          <div v-if="resultData.coverImgSrc" class="cover-preview">
            <p><strong>封面图:</strong></p>
            <img :src="resultData.coverImgSrc" alt="PPT封面预览" />
          </div>

          <div v-if="resultData.outline">
            <p><strong>PPT大纲:</strong></p>
            <pre>{{ resultData.outline }}</pre>
          </div>

          <div v-else>
            <p><strong>状态:</strong> PPT正在生成中，请稍后下载</p>
          </div>
        </div>
        <div class="result-actions">
          <button @click="downloadPPT" class="action-btn">下载PPT</button>
          <button @click="generateNew" class="action-btn new-btn">重新生成</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { generatePpt } from '../../../../api/teacher/intelligent/ppt.js';

export default {
  data() {
    return {
      prompt: '',
      resultData: null,
      isGenerating: false,

      // 高级选项
      isFigure: true,
      aiImage: 'normal',
      isCardNote: false,
      language: 'cn'
    };
  },
  methods: {
    async generatePPT() {
      if (!this.prompt.trim()) {
        alert('请输入PPT要求');
        return;
      }

      // 检查长度限制
      if (this.prompt.length > 12000) {
        alert('提示词过长，请限制在12000字以内');
        return;
      }

      this.isGenerating = true;
      this.resultData = null;

      try {
        const requestParams = {
          query: this.prompt,
          isFigure: this.isFigure,
          aiImage: this.isFigure ? this.aiImage : undefined,
          isCardNote: this.isCardNote,
          language: this.language
        };

        this.resultData = await generatePpt(requestParams);

        // 添加轮询检查PPT生成状态
        // if (this.resultData.sid) {
        //   this.checkPptStatus(this.resultData.sid);
        // }
      } catch (error) {
        console.error('生成PPT失败:', error);
        this.resultData = {
          error: error.message || '生成PPT失败，请稍后重试'
        };
      } finally {
        this.isGenerating = false;
      }
    },

    downloadPPT() {
      if (!this.resultData || !this.resultData.pptUrl) {
        alert('PPT文件尚未生成完成，请稍后再试');
        return;
      }
      window.open(this.resultData.pptUrl, '_blank');
    },

    generateNew() {
      this.prompt = '';
      this.resultData = null;
    },

    retryGenerate() {
      this.resultData = null;
      this.generatePPT();
    }
  }
};
</script>

<style scoped>
.ppt-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 5px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.headers {
  text-align: center;
  margin-bottom: 5px;
}

.headers h1 {
  color: #333;
  margin-bottom: 5px;
  font-size: 2.2rem;
}

.headers p {
  color: #666;
  font-size: 1.1rem;
}

.input-section {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 8px;
}

.input-group {
  margin-bottom: 5px;
}

.input-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #444;
  font-size: 1.05rem;
}

textarea {
  width: 100%;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  min-height: 80px;
  resize: vertical;
  transition: border-color 0.3s;
}

textarea:focus {
  border-color: #6f45d8;
  box-shadow: 0 0 0 3px rgba(111, 69, 216, 0.1);
  outline: none;
}

.options-section {
  margin: 0 0;
  padding: 10px;
  background-color: #f0f7ff;
  border-radius: 8px;
  border: 1px solid #d0e3ff;
}

.options-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #444;
  font-size: 1.1rem;
}

.option-group {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.option-group label {
  margin-right: 20px;
  display: flex;
  align-items: center;
  font-size: 0.95rem;
}

.option-group input[type="checkbox"] {
  margin-right: 8px;
  width: 18px;
  height: 18px;
}

.option-group select {
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-left: 8px;
}

.generate-btn {
  background: linear-gradient(135deg, #6f45d8, #5a36b8);
  color: white;
  border: none;
  padding: 14px 25px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1.1rem;
  width: 100%;
  margin-top: 10px;
  transition: all 0.3s;
  font-weight: 600;
}

.generate-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(111, 69, 216, 0.3);
}

.generate-btn:disabled {
  background: #b0b0b0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.loading-section {
  text-align: center;
  padding: 30px;
  margin: 20px 0;
  background: #f9f9f9;
  border-radius: 10px;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 6px solid #f0f0f0;
  border-top: 6px solid #6f45d8;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

.loading-tip {
  font-style: italic;
  color: #666;
  margin-top: 15px;
  font-size: 0.95rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.result-section {
  margin-top: 5px;
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.result-section h2 {
  color: #333;
  margin-bottom: 5px;
  padding-bottom: 5px;
  border-bottom: 2px solid #eee;
  font-size: 1.6rem;
}

.result-content {
  background-color: #f8fbff;
  padding: 25px;
  border-radius: 10px;
  border: 1px solid #e0eeff;
}

.result-info {
  margin-bottom: 25px;
}

.result-info p {
  margin-bottom: 15px;
  font-size: 1.05rem;
  line-height: 1.6;
}

.result-info strong {
  color: #6f45d8;
  font-weight: 600;
}

.cover-preview {
  margin: 15px 0;
}

.cover-preview img {
  max-width: 100%;
  border-radius: 8px;
  border: 1px solid #ddd;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

pre {
  background-color: #fff;
  padding: 18px;
  border-radius: 6px;
  overflow-x: auto;
  white-space: pre-wrap;
  font-family: 'Courier New', monospace;
  font-size: 0.95rem;
  line-height: 1.5;
  border: 1px solid #e0e0e0;
  margin-top: 10px;
}

.result-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 20px;
}

.action-btn {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s;
  min-width: 140px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

.new-btn {
  background: linear-gradient(135deg, #6f45d8, #5a36b8);
}

.error-message {
  color: #d32f2f;
  background-color: #ffebee;
  padding: 25px;
  border-radius: 10px;
  margin-bottom: 20px;
  border-left: 4px solid #d32f2f;
}

.error-message h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1.2rem;
}

.retry-btn {
  background: linear-gradient(135deg, #ff9800, #f57c00);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  margin-top: 15px;
  transition: all 0.3s;
}

.retry-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .ppt-container {
    padding: 15px;
  }

  .input-section {
    padding: 15px;
  }

  .option-group {
    flex-direction: column;
    align-items: flex-start;
  }

  .option-group label {
    margin-bottom: 8px;
  }

  .result-actions {
    flex-direction: column;
  }

  .action-btn {
    width: 100%;
  }
}
</style>
