<!--src\views\teacher\CourseDetailManager\Q-A\index.vue-->
<template>
  <div class="qa-container">
    <!-- 顶部标签切换 -->
    <div class="top-tab-header">
      <div 
        v-for="(tab, index) in topTabs" 
        :key="index" 
        @click="activeTopTab = tab.value"
        :class="[
          'tab-item', 
          { active: activeTopTab === tab.value }
        ]"
      >
        {{ tab.label }}
      </div>
      <button class="submit-btn" @click="showPublishModal">发布话题</button>
    </div>
    <div class="content">
      <!-- 根据激活的顶部标签，渲染对应组件 -->
      <TopicDiscuss 
        v-if="activeTopTab === 'topic-discuss'" 
        ref="topicDiscussRef"
      />
      <MyParticipate v-else ref="myParticipateRef" />
    </div>

    <!-- 发布话题模态框 -->
    <div v-if="showModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>发布新话题</h3>
          <button class="close-btn" @click="closeModal">&times;</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="handlePublish">
            <div class="form-group">
              <label for="title">话题标题</label>
              <input 
                id="title" 
                type="text" 
                v-model="publishForm.title" 
                placeholder="请输入话题标题"
                required
                maxlength="100"
              >
            </div>
            <div class="form-group">
              <label for="content">话题内容</label>
              <textarea 
                id="content" 
                v-model="publishForm.content" 
                placeholder="请输入话题内容"
                required
                maxlength="2000"
                rows="5"
              ></textarea>
            </div>
            <div class="form-actions">
              <button type="button" class="cancel-btn" @click="closeModal">取消</button>
              <button type="submit" class="submit-btn" :disabled="publishing">
                {{ publishing ? '发布中...' : '立即发布' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, provide } from 'vue'
import { useRoute } from 'vue-router'
import TopicDiscuss from './TopicDiscuss.vue'
import MyParticipate from './MyParticipate.vue'
import { createPost } from '@/api/teacher/discuss'
import { showNotification } from '@/utils/notification'

const topicDiscussRef = ref(null)
const myParticipateRef = ref(null)
const route = useRoute()
// 顶部标签数据
const topTabs = ref([
  { label: '话题讨论', value: 'topic-discuss' },
  { label: '我参与的', value: 'my-participate' }
])
const activeTopTab = ref('topic-discuss')

// 发布话题相关
const showModal = ref(false)
const publishForm = ref({
  title: '',
  content: ''
})
const publishing = ref(false)

// 显示发布模态框
const showPublishModal = () => {
  showModal.value = true
}

// 关闭模态框
const closeModal = () => {
  showModal.value = false
  publishForm.value = { title: '', content: '' }
}
// 更健壮的刷新函数
const refreshTopics = async () => {
  try {
    if (activeTopTab.value === 'topic-discuss' && topicDiscussRef.value?.fetchTopics) {
      await topicDiscussRef.value.fetchTopics()
      console.log('已刷新话题讨论列表')
    } else if (activeTopTab.value === 'my-participate' && myParticipateRef.value?.fetchMyTopics) {
      await myParticipateRef.value.fetchMyTopics()
      console.log('已刷新我参与的列表')
    }
  } catch (error) {
    console.error('刷新失败:', error)
    showNotification('刷新列表失败', { type: 'error' })
  }
}

provide('refreshTopics', refreshTopics)

// 发布话题方法
const handlePublish = async () => {
  try {
    publishing.value = true
    const postData = {
      courseId: route.params.courseId,
      title: publishForm.value.title,
      content: publishForm.value.content
    }
    
    const res = await createPost(postData)
    if (res.code === 200) {
      showNotification('话题发布成功', { type: 'success' })
      closeModal()
      
      // 强制刷新当前活动标签的内容
      if (activeTopTab.value === 'topic-discuss') {
        await topicDiscussRef.value?.fetchTopics?.()
      } else {
        await myParticipateRef.value?.fetchMyTopics?.()
      }
      
      // 额外调用一次全局刷新确保数据同步
      await refreshTopics()
    }
  } catch (error) {
    console.error('发布失败:', error)
    showNotification(error.message || '发布失败', { type: 'error' })
  } finally {
    publishing.value = false
  }
}
</script>

<style lang="scss" scoped>
.qa-container {
  background-color: #fff;
  border-radius: 8px;
  background-color: $bg-color;

  .top-tab-header {
    display: flex;
    flex-direction: row;
    gap: 5px;

    .tab-item {
      position: relative;
      width: 180px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      cursor: pointer;
      color: #666;
      background-color: #f2f2f2;
      border: none;
      z-index: 10;
      transform: translate(40px, 0);
      
      
      // 不规则形状 - 上标签
      &:nth-child(1) {
        border-radius: 20px 20px 0px 0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        
      }
      
      // 不规则形状 - 下标签
      &:nth-child(2) {
        border-radius: 20px 20px 0px 0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      }
      
      &:hover {
        background-color: #e5e5e5;
      }
      
      // 激活状态
      &.active {
        background-color: #fff;
        color: #333;
        z-index: 20;
      }
    }
    .submit-btn {
      position: absolute;
      right: 30px;
      width: 100px;
      height: 35px;
      text-align: center;
      color: #fff;
      background-color: $primary-color;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.164);

      &:hover{
        background-color: $button-hover;
      }
    }
  }

  .content{
    position: relative;
    z-index: 20;
  }
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;

  .modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);

    .modal-header {
      padding: 16px 20px;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
        font-size: 18px;
      }

      .close-btn {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #999;

        &:hover {
          color: #666;
        }
      }
    }

    .modal-body {
      padding: 20px;

      .form-group {
        margin-bottom: 20px;

        label {
          display: block;
          margin-bottom: 8px;
          font-weight: 500;
        }

        input, textarea {
          width: 100%;
          padding: 10px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 14px;

          &:focus {
            outline: none;
            border-color: $primary-color;
          }
        }

        textarea {
          resize: vertical;
        }
      }

      .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;

        button {
          padding: 8px 16px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;

          &.cancel-btn {
            background: white;
            border: 1px solid #ddd;

            &:hover {
              background: #f5f5f5;
            }
          }

          &.submit-btn {
            background: $primary-color;
            color: white;
            border: none;

            &:hover:not(:disabled) {
              background: $button-hover;
            }

            &:disabled {
              opacity: 0.7;
              cursor: not-allowed;
            }
          }
        }
      }
    }
  }
}
</style>