<template>
  <div class="file-manager">
    <FileToolbar @create-folder="handleCreateFolder" @upload-files="handleUploadFiles" />

<div v-if="breadcrumbs.length > 0" class="folder-nav">
  <div class="nav-left">
    <!-- 返回按钮保持现有条件 -->
    <button  @click="navigateToParent">返回上一级</button>
    <div class="breadcrumb">
      <template v-for="(item, index) in breadcrumbs" :key="item.id">
        <span @click="navigateToFolder(item.id)" class="breadcrumb-item">
          {{ item.name }}
        </span>
        <span v-if="index < breadcrumbs.length - 1"> > </span>
      </template>
    </div>
  </div>
  <div class="nav-right">
    <span>已全部加载，共 {{ fileList.length }} 个文件</span>
  </div>
</div>

  <FileTable 
  :file-list="fileList" 
  :is-creating="isCreatingNew" 
  :selected-items="selectedItems"
  @confirm-create="handleConfirmCreate" 
  @cancel-create="handleCancelCreate" 
  @delete-selected="handleDeleteSelected"
  @update-selection="selectedItems = $event" 
  @navigate-to-folder="navigateToFolder($event)"
  @download-selected="handleDownloadSelected" 
  @rename-selected="handleRenameSelected"
  @move-selected="handleMoveSelected" 
/>

<MoveDialog 
  v-if="showMoveDialog"
  :current-folder="currentFolder"
  :selected-items="itemsToMove"
  @close="showMoveDialog = false"
  @confirm="handleMoveConfirm"
/>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { usePublicResourceStore } from '@/stores/public/teacher/publicResStore'
import FileToolbar from './FileToolbar.vue'
import FileTable from './FileTable.vue'
import MoveDialog from './Dialog/MoveDialog.vue'  // 确保路径正确

const resourceStore = usePublicResourceStore()
const selectedItems = ref([])
const isCreatingNew = ref(false)
const showMoveDialog = ref(false)
const itemsToMove = ref([]) 


// 在组件中
onMounted(async () => {
  try {
    resourceStore.initRootFolder()
    await resourceStore.navigateToFolder('root')
  } catch (error) {
    console.error('初始化失败:', error)
    ElMessage.error('初始化文件夹失败: ' + error.message)
  }
})

// 计算属性
const currentFolder = computed(() => resourceStore.currentFolder)
const fileList = computed(() => currentFolder.value?.children || [])
const breadcrumbs = computed(() => resourceStore.breadcrumbs)

// 创建文件夹
const handleCreateFolder = () => {
  isCreatingNew.value = true
  currentFolder.value.children.unshift({
    id: -Date.now(),
    name: '',
    type: 'folder',
    children: [],
    size: '--',
    modifiedTime: '--',
    isNew: true,
    parent: currentFolder.value.id
  })
}



// 确认创建文件夹
const handleConfirmCreate = async (id, name) => {
  try {
    const tempIndex = fileList.value.findIndex(item => item.id.toString() === id.toString());
    if (tempIndex === -1) return

    // 调用API创建文件夹
    const newFolder = await resourceStore.createFolder(
      name,
      currentFolder.value.id === 'root' ? null : currentFolder.value.id
    )

    // 替换临时文件夹为真实文件夹
    currentFolder.value.children[tempIndex] = {
      id: newFolder.id,
      name: newFolder.name,
      type: 'folder',
      children: [],
      size: '--',
      modifiedTime: new Date(newFolder.createdTime).toLocaleString(),
      parent: newFolder.parentId || 'root'
    }

    ElMessage.success('文件夹创建成功')
  } catch (error) {
    ElMessage.error(`文件夹创建失败: ${error.message}`)
    // 移除临时文件夹
    currentFolder.value.children = currentFolder.value.children.filter(item => item.id !== id)
  } finally {
    isCreatingNew.value = false
  }
}

// 取消创建
const handleCancelCreate = () => {
  currentFolder.value.children = currentFolder.value.children.filter(i => !i.isNew)
  isCreatingNew.value = false
}


const handleDeleteSelected = async (ids) => {
  try {
    await ElMessageBox.confirm('确定要删除选中的项目吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    await resourceStore.removeResources(ids);
    
    ElMessage.success('删除成功');
    selectedItems.value = [];
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error);
      ElMessage.error(`删除失败: ${error.message || '未知错误'}`);
    }
  }
};


const handleRenameSelected = async ({ id, name }) => {
  console.log('接收到 rename-selected 事件，参数:', { id, name })
  
  // 新增参数校验
  if (!id || !name) {
    ElMessage.error('重命名参数错误')
    return
  }

  // 查找要重命名的项目
  const item = fileList.value.find(f => f.id === id)
  if (!item) {
    ElMessage.error('未找到要重命名的项目')
    return
  }

  try {
    // 根据类型调用不同的方法
    if (item.type === 'folder') {
      await resourceStore.updateFolderInfo({ id, name })
    } else {
      // 对于文件，保留文件扩展名
      const finalName = item.name.includes('.') 
        ? name + item.name.substring(item.name.lastIndexOf('.'))
        : name
      await resourceStore.updateResourceInfo({ id, name })
    }
    
    ElMessage.success('重命名成功')
  } catch (error) {
    console.error('重命名失败:', error)
    ElMessage.error(`重命名失败: ${error.message}`)
  }
}



// 上传文件
const handleUploadFiles = async (files) => {

  try {
    const uploadPromises = files.map(async (file) => {
      try {
        const result = await resourceStore.uploadResource(file)
        return {
          id: result.id || `file-${Date.now()}-${Math.random().toString(36).slice(2, 10)}`,
          name: file.name,
          type: 'file',
          modifiedTime: new Date().toLocaleString(),
          url: result.url,
          parent: currentFolder.value.id,
          fileExtension: file.name.split('.').pop() || ''
        }
      } catch (error) {
        ElMessage.error(`文件 ${file.name} 上传失败: ${error.message}`)
        return null
      }
    })

    const newFiles = (await Promise.all(uploadPromises)).filter(Boolean)
    currentFolder.value.children.unshift(...newFiles)

    if (newFiles.length > 0) {
      ElMessage.success(`成功上传 ${newFiles.length} 个文件`)
    }
  } catch (error) {
    console.error('上传过程中出错:', error)
    ElMessage.error('上传过程中出错: ' + error.message)
  }
}

// 导航到文件夹
const navigateToFolder = async (folderId) => {
  await resourceStore.navigateToFolder(folderId)
}



// 导航到父文件夹
const navigateToParent = async () => {
  if (!currentFolder.value || currentFolder.value.id === 'root') return
  await navigateToFolder(currentFolder.value.parent || 'root')
}


// 处理下载选定文件的方法
const handleDownloadSelected = async () => {
  if (selectedItems.value.length === 0) {
    ElMessage.warning('请选择要下载的文件');
    return;
  }

  const filesToDownload = selectedItems.value.filter(id => {
    const item = fileList.value.find(f => f.id === id);
    return item && item.type === 1; // 正确：文件类型为1，文件夹为'folder'
  });

  console.log(filesToDownload.length);

  if (filesToDownload.length === 0) {
    ElMessage.warning('没有可下载的文件（请确保选择的是文件而不是文件夹）');
    return;
  }
  for (const fileId of filesToDownload) {
    await resourceStore.downloadFile(fileId);
  }

};



const handleMoveSelected = (selectedIds) => {
  // 从当前文件列表中找到完整项目信息
  itemsToMove.value = fileList.value.filter(item => selectedIds.includes(item.id));
  console.log('要移动的项目:', itemsToMove.value); // 调试用
  showMoveDialog.value = true;
};

onMounted(async () => {
  await resourceStore.fetchFullFolderTree()
})
</script>

<style lang="scss" scoped>
.file-manager {
  padding: 20px;
}

.folder-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  margin-bottom: 12px;
  background-color: #f9fafb;
  border-radius: 4px;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;

  span {
    &:hover {
      color: #409eff;
    }
  }
}

.nav-right {
  color: #6b7280;
  font-size: 14px;
}
</style>