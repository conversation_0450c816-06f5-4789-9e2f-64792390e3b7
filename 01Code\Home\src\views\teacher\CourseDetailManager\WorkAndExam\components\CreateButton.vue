<template>
    <div class="flex flex-wrap items-center">
        <el-dropdown>
            <el-button type="primary">
                + 新建作业考试<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
                <el-dropdown-menu>
                    <el-dropdown-item @click="newWork">新建作业测试</el-dropdown-item>
                    <el-dropdown-item @click="newExam">新建考试</el-dropdown-item>
                </el-dropdown-menu>
            </template>
        </el-dropdown>
    </div>
</template>
  
<script setup>
import { useRouter } from 'vue-router'
import { ArrowDown } from '@element-plus/icons-vue'
import { useCourseStore } from '@/stores/courseStore'
const courseStore = useCourseStore();
const router = useRouter()

function newWork() {
    router.push({
        name: 'AssignmentExamCreate',
        query: {
            courseId: courseStore.currentCourseId,
            courseName: courseStore.currentCourseInfo?.name || '',
            mode: 'assignment',
        }
    })
}
function newExam() {
    router.push({
        name: 'AssignmentExamCreate',
        query: {
            courseId: courseStore.currentCourseId,
            courseName: courseStore.currentCourseInfo?.name || '',
            mode: 'exam',
        }
    })
}

</script>
  
<style scoped>
.example-showcase .el-dropdown+.el-dropdown {
    margin-left: 15px;
}

.example-showcase .el-dropdown-link {
    cursor: pointer;
    color: var(--el-color-primary);
    display: flex;
    align-items: center;
}
</style>
  