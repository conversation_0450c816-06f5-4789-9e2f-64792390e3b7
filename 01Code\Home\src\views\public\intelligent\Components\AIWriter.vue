<template>
  <div class="writer-container">
    <!-- 主功能选择 -->
    <div class="header-section">
<!--      <img src="../assets/photo/AI.png" alt="AI Writer" class="writer-logo">-->
      <h1>灵犀 你的智能写作助手</h1>
      <div class="type-selector">
        <select v-model="selectedType" class="type-dropdown">
          <option v-for="(type, index) in writingTypes" :key="index" :value="type.value">
            {{ type.label }}
          </option>
        </select>
      </div>
    </div>

    <!-- 内容生成区 -->
    <div class="content-wrapper">
      <div class="input-section">
<!--        <div class="param-setting">-->
<!--          <input v-model="wordCount" type="number" min="100" max="2000" class="word-input" placeholder="字数要求">-->
<!--          <span class="unit">字</span>-->
<!--        </div>-->

        <div class="input-container">
           <textarea v-model="userPrompt" class="prompt-input" placeholder="请输入您的写作主题或要求..."
             :disabled="isGenerating"
           ></textarea>

          <button @click="handleGenerate" class="generate-btn" :disabled="!canGenerate">
          <span v-if="isGenerating" class="loading">
            <span class="dot">.</span><span class="dot">.</span><span class="dot">.</span>
          </span>
            <span v-else class="arrow-icon">⮞</span>
          </button>
        </div>
      </div>

      <!-- 结果展示 -->
<!--      <div class="output-section" v-if="generatedContent">-->
<!--        <div class="result-header">-->
<!--          <h3>生成结果</h3>-->
<!--          <button @click="copyContent" class="copy-btn">📋 复制</button>-->
<!--        </div>-->
<!--        <pre class="generated-text">{{ generatedContent }}</pre>-->
<!--      </div>-->

      <!-- 原有结果展示 -->
<!--      <div class="output-section" v-if="generatedContent && !showSidePanel">-->
<!--        <div class="result-header">-->
<!--          <h3>生成结果</h3>-->
<!--          <div>-->
<!--            <button @click="togglePanel" class="panel-toggle">📋 展开面板</button>-->
<!--            <button @click="copyContent" class="copy-btn">📋 复制</button>-->
<!--          </div>-->
<!--        </div>-->
<!--        <pre class="generated-text">{{ generatedContent }}</pre>-->
<!--      </div>-->

      <!-- 右侧弹窗 -->
      <div class="side-panel" :class="{ 'panel-visible': showSidePanel }">
        <div class="panel-header">
          <h3>生成结果</h3>
          <div class="panel-controls">
            <button @click="copyContent" class="copy-btn">📋 复制</button>
            <button @click="togglePanel" class="close-btn">×</button>
          </div>
        </div>
        <div class="panel-content">
          <pre class="generated-text">{{ generatedContent }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { reactive, toRefs, computed } from 'vue'
import { createWritingContent } from '../../../../api/student/intelligent/writer.js'

export default {
  setup() {
    const state = reactive({
      writingTypes: [
        { label: '学术论文', value: 'thesis', maxWords: 1000 },
        { label: '散文创作', value: 'prose', maxWords: 1000 },
        { label: '营销文案', value: 'copywriting', maxWords: 500 },
        { label: '小说续写', value: 'novel', maxWords: 1000 }
      ],
      selectedType: 'thesis',
      userPrompt: '',
      wordCount: 500,
      generatedContent: '',
      isGenerating: false,
      showSidePanel: false
    })

    const togglePanel = () => {
      state.showSidePanel = !state.showSidePanel
    }

    const canGenerate = computed(() => {
      return state.userPrompt.trim().length >= 5 &&
        !state.isGenerating &&
        state.wordCount >= 100
    })

    const getSystemPrompt = () => {
      const typeConfig = {
        copywriting: `你是一个专业文案写手，根据用户需求创作吸引人的营销文案，要求：\n
        1. 使用口语化表达\n
        2. 包含至少3个产品卖点\n
        3. 结尾必须有行动号召\n
        字数严格控制在${state.wordCount}字以内`,

        prose: `你是一个文学作家，根据主题创作优美的散文，要求：\n
        1. 使用比喻等修辞手法\n
        2. 包含至少2处场景描写\n
        3. 体现情感变化\n
        字数严格控制在${state.wordCount}字左右`,

        thesis: `你是一个学术助手，帮助撰写规范的学术论文，要求：\n
        1. 包含明确的论点论据\n
        2. 使用学术引用格式\n
        3. 结构完整（摘要、正文、结论）\n
        字数严格不少于${state.wordCount}字`,

        novel: `你是一个小说家，根据提示续写故事，要求：\n
        1. 保持原有故事风格\n
        2. 包含人物对话描写\n
        3. 设置悬念吸引读者\n
        字数严格控制在${state.wordCount}字左右`
      }
      return typeConfig[state.selectedType]
    }

    const handleGenerate = async () => {
      try {
        state.isGenerating = true
        const messages = [
          { role: 'system', content: getSystemPrompt() },
          { role: 'user', content: state.userPrompt }
        ]

        state.generatedContent = await createWritingContent({
          messages,
          max_tokens: Math.floor(state.wordCount * 1.2)
        })
        state.showSidePanel = true // 生成成功后显示面板
      } catch (error) {
        console.error('生成失败:', error)
        state.generatedContent = '内容生成失败，请稍后重试'
      } finally {
        state.isGenerating = false
      }
    }

    const copyContent = () => {
      navigator.clipboard.writeText(state.generatedContent)
    }

    return {
      ...toRefs(state),
      canGenerate,
      handleGenerate,
      copyContent,
      togglePanel
    }
  }
}
</script>

<style scoped>
.writer-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  max-width: 1200px;
  margin: 0 auto;
  padding: 10px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.header-section {
  text-align: center;
  margin-bottom: 2rem;
}

.type-selector {
  margin: 1.5rem 0;
}

.type-dropdown {
  padding: 0.8rem 1.2rem;
  border: 2px solid #7c5cfc;
  border-radius: 8px;
  background: white;
  font-size: 1.1rem;
  width: 100%;
  max-width: 300px;
}

.content-wrapper {
  display: grid;
  gap: 2rem;
}

.input-section {
  background: white;
  padding: 2rem;
  border-radius: 12px;
}

.generate-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.output-section {
  position: relative;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  margin-top: 1rem;
  transition: all 0.3s;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.copy-btn {
  background: none;
  border: 1px solid #7c5cfc;
  color: #7c5cfc;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
}

.generated-text {
  white-space: pre-wrap;
  line-height: 1.6;
  font-size: 1rem;
}

.loading .dot {
  animation: bounce 1.4s infinite;
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-3px); }
}

.input-container {
  position: relative;
  border: 1px solid #e0e0e0;
  border-radius: 10px;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.generate-btn {
  position: absolute;
  right: 15px;
  bottom: 15px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #956ef6;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.generate-btn:hover {
  background-color: #d2c8ea;
}

.arrow-icon {
  font-size: 20px;
  color: white;
}

textarea {
  width: 100%;
  height: 120px;
  border: none;
  resize: none;
  padding: 20px;
  font-size: 16px;
  outline: none;
  box-sizing: border-box;
  background-color: #fafafa;
}

textarea::placeholder {
  color: #999;
}

/* 新增样式 */
.side-panel {
  position: fixed;
  right: -400px;
  top: 0;
  width: 380px;
  height: 100vh;
  background: white;
  box-shadow: -2px 0 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.panel-visible {
  right: 0;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #eee;
}

.panel-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  transition: color 0.2s;
}

.close-btn:hover {
  color: #ff4444;
}

.panel-toggle {
  background: none;
  border: 1px solid #7c5cfc;
  color: #7c5cfc;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  margin-right: 1rem;
  cursor: pointer;
}

</style>
