// src/utils/request.js
import axios from 'axios'
import { useAuthStore } from '@/stores/auth/auth'

const request = axios.create({
    baseURL: 'http://8.134.236.247:1991',
    timeout: 10000
})

request.interceptors.request.use(
    (config) => {
        const authStore = useAuthStore()
        const token = authStore.token || localStorage.getItem('token')
        console.log('token 是：', token)

        if (token) {
            config.headers.Authorization = `Bearer ${token}`
        }
        return config
    },
    (error) => {
        return Promise.reject(error)
    }
)

request.interceptors.response.use(
    (response) => {
        if (response.data.code === 200 || response.status === 200) {
            return response.data
        } else {
            return Promise.reject(response.data || response)
        }
    },
    (error) => {
        console.error('接口请求错误:', error)
        return Promise.reject(error)
    }
)

export default request
