<template>
  <div class="teaching-outline">
    <div class="header">
      <h3>教学大纲</h3>
      <div class="actions">
        <el-button type="primary" @click="downloadTemplate">下载导入模板</el-button>
        <el-button type="primary" @click="showImportDialog">导入课程资源</el-button>
        <el-button type="primary" @click="handleAddRoot">添加一级大纲</el-button>
        <el-button @click="expandAll">全部展开</el-button>
        <el-button @click="collapseAll">全部折叠</el-button>
      </div>
    </div>
    
    <div class="content">
      <el-table
        ref="tableRef"
        :data="tableData"
        row-key="id"
        :border="true"
        :default-expand-all="isExpandAll"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        :expand-row-keys="expandedKeys"
        @row-click="handleRowClick"
        v-loading="loading"
        class="outline-table"
      >
        <el-table-column prop="formattedChapterNumber" label="章节号" width="120" align="center">
          <template #default="{ row }">
            <span>{{ row.formattedChapterNumber }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="title" label="章节标题" min-width="200" :resizable="true">
          <template #default="{ row }">
            <div class="title-cell" :style="{ paddingLeft: (getNodeLevel(row) * 16 + 8) + 'px' }">
              <span>{{ row.title }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="nature" label="章节性质" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="natureTagType(row.nature)" size="small" effect="dark">
              {{ natureMap[row.nature] }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status ? 'success' : 'danger'" size="small" effect="dark">
              {{ row.status ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="300" align="center" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click.stop="handleAddChild(row)">添加子项</el-button>
            <el-button size="small" type="primary" @click.stop="handleEdit(row)">编辑</el-button>
            <el-button size="small" type="info" @click.stop="showResources(row)">查看资源</el-button>
            <el-popconfirm
              title="确定要删除吗？"
              @confirm="handleDelete(row)"
            >
              <template #reference>
                <el-button size="small" type="danger" @click.stop>删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 添加一级大纲对话框 -->
    <el-dialog 
      v-model="rootDialogVisible" 
      title="添加一级大纲" 
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="rootForm" label-width="100px" :rules="rootRules" ref="rootFormRef">
        <el-form-item label="章节标题" prop="title">
          <el-input v-model="rootForm.title" placeholder="请输入章节标题" />
        </el-form-item>
        
        <el-form-item label="章节性质" prop="nature">
          <el-select v-model="rootForm.nature" placeholder="请选择章节性质">
            <el-option label="重点" :value="1" />
            <el-option label="难点" :value="2" />
            <el-option label="普通" :value="3" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="rootForm.status"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="rootDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitAddRoot">确定</el-button>
      </template>
    </el-dialog>
    
    <!-- 添加子项/编辑对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="isEditMode ? '编辑大纲' : '添加子项'" 
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="form" label-width="100px" :rules="formRules" ref="formRef">
        <el-form-item label="父章节">
          <el-input v-model="parentTitle" disabled />
        </el-form-item>
        
        <el-form-item label="章节标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入章节标题" />
        </el-form-item>
        
        <el-form-item label="章节性质" prop="nature">
          <el-select v-model="form.nature" placeholder="请选择章节性质">
            <el-option label="重点" :value="1" />
            <el-option label="难点" :value="2" />
            <el-option label="普通" :value="3" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="form.status"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="isEditMode ? submitUpdate() : submitAddChild()">确定</el-button>
      </template>
    </el-dialog>

    <!-- 导入资源对话框 -->
    <el-dialog 
      v-model="importDialogVisible" 
      title="导入课程资源" 
      width="500px"
      :close-on-click-modal="false"
    >
      <el-upload
        class="upload-demo"
        drag
        :action="importUrl"
        :before-upload="beforeImportUpload"
        :on-success="handleImportSuccess"
        :on-error="handleImportError"
        :show-file-list="false"
        accept=".xlsx,.xls"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将Excel文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            请上传Excel格式文件，文件大小不超过10MB
          </div>
        </template>
      </el-upload>
    </el-dialog>

    <!-- 查看资源对话框 -->
        <!-- 查看资源对话框 -->
    <el-dialog 
      v-model="resourceDialogVisible" 
      :title="currentChapter ? `${currentChapter.title} 的资源` : '章节资源'" 
      width="800px"
      :close-on-click-modal="false"
    >
      <el-table 
        :data="resourceData" 
        border 
        style="width: 100%"
        v-loading="resourceLoading"
        empty-text="暂无资源"
      >
        <el-table-column label="资源名称" width="300">
          <template #default="{ row }">
            <div class="resource-name-cell" @click="previewResource(row)">
              <span class="resource-name">{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="type" label="资源类型" width="120" align="center" />
        
        <el-table-column prop="creator" label="创建人" width="120" align="center" />
        
        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template #default="{ row }">
            <el-button 
              size="small" 
              type="danger" 
              @click="deleteResource(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 使用FilePreviewDialog替换ResourcePreview -->
      <file-preview-dialog 
        v-if="previewVisible"
        :visible="previewVisible"
        :file="currentResource"
        @close="previewVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted,watch } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import { Document, VideoPlay, DataBoard, DocumentCopy, Files, Download, Delete } from '@element-plus/icons-vue'
import { 
  getSyllabusTree,
  addSyllabus, 
  updateSyllabus,
  deleteSyllabus,
  downloadResourceTemplate,
  importCourseResources,
  getChapterResources
} from '@/api/teacher/course'
// 添加导入
import FilePreviewDialog from '@/components/FilePreviewDialog.vue'

// 添加状态
const previewVisible = ref(false)
const currentResource = ref(null)
const route = useRoute()
const courseId = route.params.courseId

const importDialogVisible = ref(false)
const importUrl = computed(() => `/api/course/resource/import?courseId=${courseId}`)

// 数据状态
const tableData = ref([])
const loading = ref(false)
const dialogVisible = ref(false)
const rootDialogVisible = ref(false)
const isExpandAll = ref(true)
const expandedKeys = ref([])
const isEditMode = ref(false)

// 表单数据
const rootForm = ref({
  title: '',
  nature: 3,
  status: 1
})

const form = ref({
  id: '',
  pid: null,
  title: '',
  nature: 3,
  status: 1
})

// 资源类型映射
const resourceTypeMap = {
  1: '视频',
  2: '教材',
  3: 'PPT',
  4: 'Word',
  5: '外部资源',
  6: '其他资源'
}
// 添加预览方法
const previewResource = (resource) => {
  currentResource.value = {
    name: resource.name,
    url: resource.url,
    type: getResourceType(resource.rawData.type),
    fileExtension: resource.fileExtension
  }
  previewVisible.value = true
}

// 辅助方法 - 根据后端类型返回预览类型
const getResourceType = (type) => {
  switch(type) {
    case 1: return 1 // 视频
    case 2: return 2 // PDF
    case 3: return 3 // PPT
    case 4: return 4 // Word
    case 5: return 5 // 外部链接
    case 6: return 6 // 图片
    default: return 0 // 未知
  }
}
// 验证规则
const rootRules = {
  title: [
    { required: true, message: '请输入章节标题', trigger: 'blur' },
    { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
  ],
  nature: [
    { required: true, message: '请选择章节性质', trigger: 'change' }
  ]
}

const formRules = {
  title: [
    { required: true, message: '请输入章节标题', trigger: 'blur' },
    { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
  ],
  nature: [
    { required: true, message: '请选择章节性质', trigger: 'change' }
  ]
}

// 映射关系
const natureMap = {
  1: '重点',
  2: '难点',
  3: '普通'
}


const natureTagType = (nature) => {
  switch(nature) {
    case 1: return 'danger'
    case 2: return 'warning'
    default: return ''
  }
}

// 在数据状态部分添加
const resourceDialogVisible = ref(false)
const resourceData = ref([])
const currentChapter = ref(null)

// 计算父节点标题
const parentTitle = computed(() => {
  if (!form.value.pid) return '无'
  const parentNode = findNodeById(form.value.pid)
  return parentNode ? parentNode.title : '未知父节点'
})

// 方法
const fetchData = async () => {
  try {
    loading.value = true;
    const res = await getSyllabusTree(courseId);
    
    const formatChapterNumbers = (nodes, parentNumber = '') => {
      return nodes.map((node, index) => {
        let chapterNumber;
        if (!parentNumber) {
          chapterNumber = `${index + 1}`;
        } else {
          chapterNumber = `${parentNumber}.${index + 1}`;
        }

        const formattedNode = {
          ...node,
          formattedChapterNumber: chapterNumber,
        };

        if (node.children && node.children.length) {
          formattedNode.children = formatChapterNumbers(node.children, chapterNumber);
        }

        return formattedNode;
      });
    };
    
    tableData.value = formatChapterNumbers(res.result || []);
    expandedKeys.value = getAllNodeIds(tableData.value);
  } catch (error) {
    ElMessage.error('获取大纲数据失败: ' + (error.message || '未知错误'));
  } finally {
    loading.value = false;
  }
}

const getAllNodeIds = (nodes) => {
  let ids = []
  nodes.forEach(node => {
    ids.push(node.id)
    if (node.children && node.children.length) {
      ids = ids.concat(getAllNodeIds(node.children))
    }
  })
  return ids
}

const getNodeLevel = (row) => {
  let level = 0
  let current = row
  while (current && current.pid) {
    level++
    current = findNodeById(current.pid)
  }
  return level
}

const findNodeById = (id, nodes = tableData.value) => {
  for (const node of nodes) {
    if (node.id === id) return node
    if (node.children && node.children.length) {
      const found = findNodeById(id, node.children)
      if (found) return found
    }
  }
  return null
}

const handleRowClick = (row, column, event) => {
  if (event.target.closest('.el-button') || event.target.closest('.el-popconfirm')) {
    return
  }
  
  const index = expandedKeys.value.indexOf(row.id)
  if (index > -1) {
    expandedKeys.value.splice(index, 1)
  } else {
    expandedKeys.value.push(row.id)
  }
}

const expandAll = () => {
  isExpandAll.value = true
  expandedKeys.value = getAllNodeIds(tableData.value)
}

const collapseAll = () => {
  isExpandAll.value = false
  expandedKeys.value = []
}

const handleAddRoot = () => {
  rootForm.value = {
    title: '',
    nature: 3,
    status: 1
  }
  rootDialogVisible.value = true
}

const handleAddChild = (parentRow) => {
  form.value = {
    id: '',
    pid: parentRow.id,
    title: '',
    nature: 3,
    status: 1
  }
  isEditMode.value = false
  dialogVisible.value = true
}

const handleEdit = (row) => {
  form.value = {
    id: row.id,
    pid: row.pid,
    title: row.title,
    nature: row.nature,
    status: row.status
  }
  isEditMode.value = true
  dialogVisible.value = true
}

const handleDelete = async (row) => {
  try {
    console.log('准备删除节点:', row.id);
    const response = await deleteSyllabus(row.id);
    console.log('删除API响应:', response);
    
    if (response.code === 200) {
      console.log('删除成功，准备刷新数据');
      ElMessage.success('删除成功');
      await fetchData();
      console.log('数据刷新完成');
    } else {
      console.warn('删除失败，响应码:', response.code, '消息:', response.msg);
      ElMessage.error(response.msg || '删除失败');
    }
  } catch (error) {
    console.error('删除过程中出错:', error);
    ElMessage.error('删除失败: ' + (error.message || '未知错误'));
  }
};

const submitAddRoot = async () => {
  try {
    console.log('开始添加一级大纲，表单数据:', rootForm.value);
    const topLevelNodes = tableData.value.filter(item => !item.pid);
    const nextChapterNumber = topLevelNodes.length + 1;
    
    const data = {
      courseId,
      pid: null,
      title: rootForm.value.title,
      nature: rootForm.value.nature,
      chapterNumber: nextChapterNumber,
      sortOrder: 0,
      status: rootForm.value.status
    };
    
    console.log('准备提交的数据:', data);
    const response = await addSyllabus(data);
    console.log('API响应:', response);
    
    if (response.code === 200) {
      console.log('添加成功，准备关闭对话框并刷新数据');
      ElMessage.success('添加成功');
      rootDialogVisible.value = false;
      await fetchData();
      console.log('数据刷新完成');
    } else {
      console.warn('添加失败，响应码:', response.code, '消息:', response.msg);
      ElMessage.error(response.msg || '添加失败');
    }
  } catch (error) {
    console.error('添加过程中出错:', error);
    ElMessage.error('添加失败: ' + (error.message || '未知错误'));
  }
};

const submitAddChild = async () => {
  try {
    console.log('开始添加子项，表单数据:', form.value);
    const parentNode = findNodeById(form.value.pid);
    if (!parentNode) {
      throw new Error('父节点不存在');
    }
    
    const siblings = parentNode.children || [];
    const nextSortOrder = siblings.length + 1;
    
    const data = {
      courseId,
      pid: form.value.pid,
      title: form.value.title,
      nature: form.value.nature,
      chapterNumber: parentNode.chapterNumber,
      sortOrder: nextSortOrder,
      status: form.value.status
    };
    
    console.log('准备提交的数据:', data);
    const response = await addSyllabus(data);
    console.log('API响应:', response);
    
    if (response.code === 200) {
      console.log('添加成功，准备关闭对话框并刷新数据');
      ElMessage.success('添加成功');
      dialogVisible.value = false;
      await fetchData();
      console.log('数据刷新完成');
    } else {
      console.warn('添加失败，响应码:', response.code, '消息:', response.msg);
      ElMessage.error(response.msg || '添加失败');
    }
  } catch (error) {
    console.error('添加过程中出错:', error);
    ElMessage.error('添加失败: ' + (error.message || '未知错误'));
  }
};

const submitUpdate = async () => {
  try {
    console.log('开始更新大纲，表单数据:', form.value);
    const data = {
      id: form.value.id,
      title: form.value.title,
      nature: form.value.nature,
      status: form.value.status
    };
    
    console.log('准备提交的数据:', data);
    const response = await updateSyllabus(data);
    console.log('API响应:', response);
    
    if (response.code === 200) {
      console.log('更新成功，准备关闭对话框并刷新数据');
      ElMessage.success('更新成功');
      dialogVisible.value = false;
      await fetchData();
      console.log('数据刷新完成');
    } else {
      console.warn('更新失败，响应码:', response.code, '消息:', response.msg);
      ElMessage.error(response.msg || '更新失败');
    }
  } catch (error) {
    console.error('更新过程中出错:', error);
    ElMessage.error('更新失败: ' + (error.message || '未知错误'));
  }
};

const downloadTemplate = async () => {
  try {
    const response = await downloadResourceTemplate()
    
    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([response]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', '课程资源导入模板.xlsx')
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('下载模板失败:', error)
    ElMessage.error('下载模板失败: ' + (error.message || '未知错误'))
  }
}

const showImportDialog = () => {
  importDialogVisible.value = true
}

const beforeImportUpload = (file) => {
  const isExcel = file.type.includes('excel') || 
                 file.name.endsWith('.xlsx') || 
                 file.name.endsWith('.xls')
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isExcel) {
    ElMessage.error('只能上传Excel文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过10MB!')
    return false
  }
  return true
}

const handleImportSuccess = (response) => {
  if (response.code === 200) {
    ElMessage.success('导入成功')
    // 可以在这里处理导入结果，如显示成功/失败数量
    console.log('导入结果:', response.result)
  } else {
    ElMessage.error(response.msg || '导入失败')
  }
  importDialogVisible.value = false
}

const handleImportError = (error) => {
  console.error('导入失败:', error)
  ElMessage.error('导入失败: ' + (error.message || '未知错误'))
}

const showResources = async (row) => {
  try {
    currentChapter.value = row;
    resourceDialogVisible.value = true;
    
    // 调用API获取资源数据
    const params = {
      courseId: courseId,
      syllabusId: row.id
    };
    
    const resources = await getChapterResources(params);
    
    // 格式化资源数据
    resourceData.value = resources.map(resource => ({
      id: resource.id,
      name: resource.name,
      type: resourceTypeMap[resource.type] || '未知类型',
      creator: resource.publisherName,
      fileExtension: resource.fileExtension,
      url: resource.url,
      rawData: resource // 保留原始数据
    }));
    
    if (resourceData.value.length === 0) {
      ElMessage.info('该章节暂无资源');
    }
  } catch (error) {
    console.error('获取资源失败:', error);
    resourceData.value = [];
    ElMessage.error('获取资源失败: ' + (error.message || '未知错误'));
  }
};

const deleteResource = (resource) => {
  ElMessageBox.confirm(`确定要删除资源 "${resource.name}" 吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      // 这里调用删除资源的API
      // await deleteResourceAPI(resource.id)
      ElMessage.success('删除成功')
      // 重新加载资源列表
      showResources(currentChapter.value)
    } catch (error) {
      ElMessage.error('删除失败: ' + (error.message || '未知错误'))
    }
  }).catch(() => {
    // 用户取消
  })
}

onMounted(() => {
  fetchData()
})

// 在 setup() 中添加 watch
watch(rootDialogVisible, (newVal) => {
  if (!newVal) {
    rootForm.value = {
      title: '',
      nature: 3,
      status: 1
    };
  }
});

watch(dialogVisible, (newVal) => {
  if (!newVal) {
    form.value = {
      id: '',
      pid: null,
      title: '',
      nature: 3,
      status: 1
    };
    isEditMode.value = false;
  }
});
</script>

<style scoped lang="scss">
@use '@/styles/variables';
.teaching-outline {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  
  h3 {
    margin: 0;
    font-size: 18px;
    color: #303133;
  }
}

.actions {
  display: flex;
  gap: 10px;
}

.content {
  flex: 1;
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.title-cell {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

/* 修改按钮样式 */
:deep(.el-button) {
  background-color: white;
  border: $border-color solid 1px;
  color: $text-color;
  
  &:hover, &:focus {
    background-color: $button-hover;
    color: white;
  }
}

:deep(.el-button--primary) {
  background-color: white;
  border: $border-color solid 1px;
  color: $text-color;
  
  &:hover, &:focus {
    background-color: $button-hover;
    color: white;
  }
}

:deep(.el-button--danger) {
  background-color: #f56c6c;
  border-color: #f56c6c;
  
  &:hover, &:focus {
    background-color: #f78989;
    border-color: #f78989;
  }
}

/* 修改标签样式 - 正确使用 :deep() */
:deep(.el-tag.el-tag--dark.el-tag--danger) {
  background-color: #f56c6c;
}

:deep(.el-tag.el-tag--dark.el-tag--warning) {
  background-color: #e6a23c;
}

:deep(.el-tag.el-tag--dark.el-tag--success) {
  background-color: #67c23a;
}

/* 上传资源相关样式 */
/* 上传资源对话框样式 - 保留居中 */
:deep(.el-dialog) {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);
  
  &.el-dialog--center {
    text-align: center;
  }
  
  .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: auto;
  }
}

/* 上传资源内容样式 */
.upload-container {
  width: 100%;
  padding: 20px;
  
  .upload-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    text-align: left; /* 确保标题左对齐 */
    
    h4 {
      margin: 0;
      font-size: 16px;
      color: #606266;
    }
  }
  
  .upload-demo {
    width: 100%;
    text-align: center; /* 上传区域内容居中 */
    
    :deep(.el-upload) {
      width: 100%;
    }
    
    :deep(.el-upload-dragger) {
      width: 100%;
      padding: 40px 0;
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      background-color: #fafafa;
      transition: border-color 0.3s;
      text-align: center; /* 拖拽区域内容居中 */
      
      &:hover {
        border-color: #409eff;
      }
    }
    
    .el-icon--upload {
      font-size: 40px;
      color: #8c939d;
      margin-bottom: 10px;
    }
    
    .el-upload__text {
      color: #606266;
      font-size: 14px;
      
      em {
        color: #409eff;
        font-style: normal;
      }
    }
    
    .el-upload__tip {
      margin-top: 10px;
      color: #909399;
      font-size: 12px;
      text-align: center;
    }
  }
  
  .upload-result {
    margin-top: 20px;
    padding: 15px;
    border-radius: 4px;
    background-color: #f5f7fa;
    text-align: left; /* 结果区域左对齐 */
    
    .result-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      font-size: 14px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .label {
        color: #606266;
        min-width: 80px;
      }
      
      .value {
        color: #303133;
        font-weight: 500;
        
        &.success {
          color: #67c23a;
        }
        
        &.error {
          color: #f56c6c;
        }
      }
    }
  }
}
/* 在style部分添加或修改以下样式 */
:deep(.el-table) {
  .el-table__header-wrapper,
  .el-table__body-wrapper {
    th, td {
      padding: 8px 0;
    }
  }
  
  .title-cell {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .el-button {
    & + .el-button {
      margin-left: 6px;
    }
  }
}
.resource-name-cell {
  cursor: pointer;
  padding: 8px 0;
  
  .resource-name {
    &:hover {
      color: #409eff;
      text-decoration: underline;
    }
  }
}
</style>