<!-- <template>
  <div class="modal-mask" @click.self="close">
    <div class="modal">
      <div class="modal-header">
        <h3>选择个人资源文件夹</h3>
      </div>

      <div class="folder-item" @click="selectItem({ id: '', name: '全部', type: 'folder' })">
        <span class="folder-name">全部</span>
      </div>

      <div class="modal-body">
        <div class="folder-tree">
          <ul>
            <FolderItem
              v-for="folder in folders"
              :key="folder.id"
              :folder="folder"
              :expanded-folders="expandedFolders"
              :selected-item="selectedItem"
              @select-folder="handleFolderSelect"
              @select-file="handleFileSelect"
            />
          </ul>
        </div>

        <div v-if="orphanResources.length > 0" class="orphan-resources">
          <ul>
            <li v-for="file in orphanResources" :key="file.id">
              <div 
                class="file-item" 
                :class="{ 'selected': selectedItem?.id === file.id }"
                @click="handleFileSelect(file)"
              >
                <span>{{ file.name }}</span>
              </div>
            </li>
          </ul>
        </div>
      </div>

      <div class="modal-actions">
        <button @click="openFolderDialog">确定</button>
        <button @click="close">取消</button>
      </div>
    </div>
  </div>
  
  <ResourceDialog
    v-if="showFolderDialog"
    :title="'选择目标文件夹'"
    @close="showFolderDialog = false"
    @confirm="handleFolderConfirm"
  />
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { useRoute } from 'vue-router'
import { usePublicResourceStore } from '@/stores/public/teacher/publicResStore'
import { addCourseLink } from '@/api/teacher/publicResources'
import FolderItem from './FolderItem.vue'
import ResourceDialog from './ResourceDialog.vue'

const emit = defineEmits(['close', 'select'])
const resourceStore = usePublicResourceStore()
const folders = ref([])
const expandedFolders = reactive(new Set())
const orphanResources = ref([])
const showFolderDialog = ref(false)
const selectedItem = ref(null)

const route = useRoute()
const courseId = route.params.courseId

onMounted(async () => {
  await resourceStore.fetchFullFolderTree()
  folders.value = resourceStore.fullFolderTree
  orphanResources.value = resourceStore.orphanResources || []
})

const handleFolderSelect = (folder) => {
  selectedItem.value = folder
  emit('select', folder)
}

const handleFileSelect = (file) => {
  selectedItem.value = file
  emit('select', file)
}

const selectItem = (item) => {
  selectedItem.value = item
  if (item.type === 'folder') {
    handleFolderSelect(item)
  } else {
    handleFileSelect(item)
  }
}

const openFolderDialog = () => {
  if (selectedItem.value) {
    showFolderDialog.value = true
  } else {
    alert('请先选择一个资源或文件夹')
  }
}

const handleFolderConfirm = async (payload) => {
  if (!selectedItem.value?.id || !payload.folderId) {
    alert('请完整选择资源和目标文件夹')
    return
  }

  const apiParams = {
    courseId: courseId,
    resourceId: selectedItem.value.id || '',
    folderId: payload.folderId,
    operationType: 1
  }

  try {
    const res = await addCourseLink(apiParams)
    if (res.code === 200) {
      alert('移动成功')
      await resourceStore.fetchFullFolderTree()
    }
  } finally {
    showFolderDialog.value = false
    close()
  }
}

const close = () => {
  emit('close')
}
</script>

<style scoped lang="scss">
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal {
  background: white;
  padding: 20px;
  border-radius: 8px;
  width: 600px;
  max-height: 60vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

  .modal-header {
    position: sticky;
    top: 0;
    background: white;
    z-index: 1;
    padding-bottom: 10px;
    margin-bottom: 15px;
  }

  .modal-actions {
    margin-top: auto;
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-end;

    button {
      padding: 6px 12px;
      background-color: #4c7bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
  }

  .modal-body {
    overflow-y: auto;
    max-height: calc(100% - 80px);
    padding-right: 15px;
  }
}

.folder-item {
  display: flex;
  align-items: center;
  padding: 6px 0;
  cursor: pointer;

  &:hover {
    background-color: #f0f2f5;
  }

  &.selected {
    background-color: #e6f7ff;
    border-left: 3px solid #4c7bff;
  }

  .folder-name {
    flex: 1;
    padding: 4px;
    border-radius: 4px;
  }

  .toggle-icon {
    margin-left: 8px;
    font-size: 12px;
    color: #666;
    width: 20px;
    text-align: center;
  }
}

.file-item {
  padding: 6px 0;
  cursor: pointer;

  &:hover {
    background-color: #f0f2f5;
  }

  &.selected {
    background-color: #e6f7ff;
    border-left: 3px solid #4c7bff;
  }
}

.orphan-resources {
  margin-top: 10px;
  padding-top: 10px;
}

ul {
  margin-left: 20px;
  list-style-type: none;
}
</style> -->

<template>
  <div class="modal-mask" @click.self="close">
    <div class="modal">
      <!-- 上部：标题和选择资源文件夹 -->
      <div class="modal-header">
        <h3>选择个人资源文件夹</h3>
      </div>


      <!-- 中部：文件夹树和孤立资源 -->
      <div class="modal-body">
              <div class="folder-item" @click="selectItem({ id: 'all', name: '全部', type: 'folder' })">
        <span class="folder-name">全部</span>
      </div>
        <div class="folder-tree">
          <ul>
            <FolderItem
              v-for="folder in folders"
              :key="folder.id"
              :folder="folder"
              :expanded-folders="expandedFolders"
              :selected-item="selectedItem"
              @select-folder="handleFolderSelect"
              @select-file="handleFileSelect"
            />
          </ul>
        </div>

        <div v-if="orphanResources.length > 0" class="orphan-resources">
          <ul>
            <li v-for="file in orphanResources" :key="file.id">
              <div 
                class="file-item" 
                :class="{ 'selected': selectedItem?.id === file.id }"
                @click="handleFileSelect(file)"
              >
                <span>{{ file.name }}</span>
              </div>
            </li>
          </ul>
        </div>
      </div>

      <!-- 下部：确定和取消按钮 -->
      <div class="modal-actions">
        <button @click="openFolderDialog">确定</button>
        <button @click="close" >取消</button>
      </div>
    </div>
  </div>

  <ResourceDialog
    v-if="showFolderDialog"
    :title="'选择目标文件夹'"
    @close="showFolderDialog = false"
    @confirm="handleFolderConfirm"
  />
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { useRoute } from 'vue-router'
import { usePublicResourceStore } from '@/stores/public/teacher/publicResStore'
import { addCourseLink } from '@/api/teacher/publicResources'
import FolderItem from './FolderItem.vue'
import ResourceDialog from './ResourceDialog.vue'

const emit = defineEmits(['close', 'select'])
const resourceStore = usePublicResourceStore()
const folders = ref([])
const expandedFolders = reactive(new Set())
const orphanResources = ref([])
const showFolderDialog = ref(false)
const selectedItem = ref(null)

const route = useRoute()
const courseId = route.params.courseId

onMounted(async () => {
  await resourceStore.fetchFullFolderTree()
  folders.value = resourceStore.fullFolderTree
  orphanResources.value = resourceStore.orphanResources || []
})

const handleFolderSelect = (folder) => {
  selectedItem.value = folder
  emit('select', folder)
}

const handleFileSelect = (file) => {
  selectedItem.value = file
  emit('select', file)
}

const selectItem = (item) => {
  selectedItem.value = item
  if (item.type === 'folder') {
    handleFolderSelect(item)
  } else {
    handleFileSelect(item)
  }
}

const openFolderDialog = () => {
  if (selectedItem.value) {
    showFolderDialog.value = true
  } else {
    alert('请先选择一个资源或文件夹')
  }
}

const handleFolderConfirm = async (payload) => {


  const apiParams = {
    courseId: courseId,
    resourceId: selectedItem.value.id || '',
    folderId: payload.folderId ||null,
    operationType: 1
  }

  try {
    const res = await addCourseLink(apiParams)
    if (res.code === 200) {
      alert('移动成功')
      await resourceStore.fetchFullFolderTree()
    }
  } finally {
    showFolderDialog.value = false
    close()
  }
}

const close = () => {
  emit('close')
}
</script>

<style scoped lang="scss">
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal {
  background: white;
  padding: 20px;
  border-radius: 8px;
  width: 600px;
height: 60vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

  .modal-header {
    position: sticky;
    top: 0;
    background: white;
    z-index: 1;
    padding-bottom: 10px;
    margin-bottom: 15px;
    border-bottom: 1px solid #eee;
  }

  .modal-body {
    flex: 1;
    overflow-y: auto;
    padding-right: 15px;
  }

  .modal-actions {
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;

    button {
      margin-top: 20px;
      padding: 6px 12px;
      background-color: #4c7bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      margin-left: 10px;
    }
  }
}

.folder-item {
  display: flex;
  align-items: center;
  padding: 6px 0;
  cursor: pointer;

  &:hover {
    background-color: #f0f2f5;
  }

  &.selected {
    background-color: #e6f7ff;
    border-left: 3px solid #4c7bff;
  }

  .folder-name {
    flex: 1;
    padding: 4px;
    border-radius: 4px;
  }
}

.file-item {
  padding: 6px 0;
  cursor: pointer;

  &:hover {
    background-color: #f0f2f5;
  }

  &.selected {
    background-color: #e6f7ff;
    border-left: 3px solid #4c7bff;
  }
}

.orphan-resources {
  margin-top: 10px;
  padding-top: 10px;
}

ul {
  margin-left: 20px;
  list-style-type: none;
}
</style>
