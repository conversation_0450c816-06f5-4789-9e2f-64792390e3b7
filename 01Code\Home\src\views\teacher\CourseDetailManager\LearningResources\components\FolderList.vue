<template>
  <div class="folder-list">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <input type="checkbox" id="selectAll" v-model="isAllSelected" @change="toggleSelectAll">
        <label for="selectAll">全选</label>
        <div class="show-number-toggle">
          <input type="checkbox" id="showNumbers" v-model="showNumbers">
          <label for="showNumbers">显示序号</label>
        </div>
        <span>全部资源({{ totalFilesCount }})</span>
      </div>
      <div class="toolbar-right">
        <span class="action-btn" @click="syncResources">同步</span>
        <span class="action-btn" @click="batchSettings">批量设置</span>
        <span class="action-btn" @click="batchPublish">批量发布</span>
      </div>
    </div>

    <!-- 文件夹列表 - 使用提取的组件 -->
    <div v-if="folders.length > 0" class="root-folders">
      <FolderItem 
        v-for="folder in folders" 
        :key="folder.id" 
        :folder="folder" 
        :rootFiles="rootFiles"
        :show-numbers="showNumbers"
        :index-path="[]"
        @toggle-folder="updateFolderCheck"
        @update-all-check="updateAllCheck"
      />
    </div>
  </div>

  
</template>

<script setup>
import { reactive, ref, computed, onMounted } from 'vue'
import { useResourceStore } from '@/stores/public/teacher/resource'
import FolderItem from './FolderItem.vue' // 引入提取的组件


const resourceStore = useResourceStore()
const isAllSelected = ref(false)
const showNumbers = ref(true)

// FolderList.vue
const publishingItem = ref(null) // 当前正在发布的项目
const publishDialogTitle = ref('') // 发布对话框标题
const showPublishOptions = ref(false) // 控制发布选项弹窗
const showPublishDialog = ref(false) // 控制发布对话框
const syllabusTree = ref([]) // 课程大纲树数据

const folders = computed(() => {
  return resourceStore.folderTree;
});

const totalFilesCount = computed(() => {
  const countFiles = (folders) => {
    return folders.reduce((total, folder) => {
      return total + folder.files.length + countFiles(folder.children);
    }, 0);
  };
  return countFiles(folders.value);
})



// 新增方法 - 递归全选
const toggleSelectAll = () => {
  const setChecked = (folders, checked) => {
    folders.forEach(folder => {
      folder.checked = checked;
      folder.files.forEach(file => {
        file.checked = checked;
      });
      if (folder.children && folder.children.length > 0) {
        setChecked(folder.children, checked);
      }
    });
  };
  setChecked(folders.value, isAllSelected.value);
}

// 右侧功能按钮方法
const syncResources = () => {
  console.log('执行资源同步...')
}

const batchSettings = () => {
  console.log('打开批量设置对话框...')
}



// 收集所有选中的文件和文件夹
const collectSelectedItems = (folders) => {
  let selectedItems = [];
  
  folders.forEach(folder => {
    // 添加选中的文件夹
    if (folder.checked) {
      selectedItems.push({
        type: 'folder',
        id: folder.id,
        name: folder.name
      });
    }
    
    // 添加选中的文件
    folder.files.forEach(file => {
      if (file.checked) {
        selectedItems.push({
          type: 'file',
          id: file.id,
          name: file.name
        });
      }
    });
    
    // 递归处理子文件夹
    if (folder.children && folder.children.length > 0) {
      selectedItems = selectedItems.concat(collectSelectedItems(folder.children));
    }
  });
  
  return selectedItems;
};

const batchPublish = async () => {
  const selectedItems = collectSelectedItems(folders.value);
  
  if (selectedItems.length === 0) {
    ElMessage.warning('请先选择要发布的文件或文件夹');
    return;
  }
  
  // 设置当前发布项为批量发布
  publishingItem.value = { 
    type: 'batch', 
    items: selectedItems 
  };
  
  publishDialogTitle.value = `批量发布 ${selectedItems.length} 个资源`;
  showPublishOptions.value = true;
};


// FolderList.vue
const handlePublishConfirm = async (chapterId) => {
  try {
    if (!publishingItem.value) return;

    let successCount = 0;
    let totalCount = 0;

    // 处理批量发布
    if (publishingItem.value.type === 'batch') {
      const selectedItems = publishingItem.value.items;
      totalCount = selectedItems.length;

      for (const item of selectedItems) {
        try {
          if (item.type === 'folder') {
            const folder = findFolderById(folders.value, item.id);
            if (folder) {
              const files = getAllFilesInFolder(folder);
              for (const file of files) {
                await publishCourseLink({
                  courseId: courseId.value,
                  syllabusId: chapterId,
                  resourceId: file.id,
                });
                successCount++;
              }
            }
          } else {
            await publishCourseLink({
              courseId: courseId.value,
              syllabusId: chapterId,
              resourceId: item.id,
            });
            successCount++;
          }
        } catch (error) {
          console.error(`发布 ${item.name} 失败:`, error);
        }
      }
    }
    // 处理单个发布（来自FolderItem的委托）
    else {
      const { type, id } = publishingItem.value;
      totalCount = 1;

      if (type === 'folder') {
        const folder = findFolderById(folders.value, id);
        if (folder) {
          const files = getAllFilesInFolder(folder);
          totalCount = files.length;
          for (const file of files) {
            await publishCourseLink({
              courseId: courseId.value,
              syllabusId: chapterId,
              resourceId: file.id,
            });
            successCount++;
          }
        }
      } else {
        await publishCourseLink({
          courseId: courseId.value,
          syllabusId: chapterId,
          resourceId: id,
        });
        successCount = 1;
      }
    }

    const message = chapterId
      ? `已成功发布 ${successCount}/${totalCount} 个资源到章节`
      : `已成功发布 ${successCount}/${totalCount} 个资源到资源库`;
    ElMessage.success(message);
    await resourceStore.refreshFolders();
  } catch (error) {
    ElMessage.error(`发布失败: ${error.message}`);
  } finally {
    showPublishDialog.value = false;
    showPublishOptions.value = false;
  }
};
// 更新文件夹选中状态
const updateFolderCheck = (folder) => {
  folder.checked = folder.files.every(file => file.checked)
  updateAllCheck()
}

// 更新全选状态
const updateAllCheck = () => {
  const isAllChecked = (folders) => {
    return folders.every(folder => 
      folder.files.every(file => file.checked) && 
      (folder.children.length === 0 || isAllChecked(folder.children))
    );
  };
  isAllSelected.value = isAllChecked(folders.value);
}

onMounted(async () => {
  try {
    await resourceStore.refreshFolders();
  } catch (error) {
    console.error('Failed to refresh folders:', error);
  }
});
</script>

<style lang="scss" scoped>
.folder-list {
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px 8px 0;
    margin-bottom: 8px;
    border-bottom: 1px solid #e4e7ec;
    
    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 16px;
    }
    
    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    label {
      cursor: pointer;
      user-select: none;
      display: flex;
      align-items: center;
      gap: 4px;
    }
    
    .show-number-toggle {
      display: flex;
      align-items: center;
      gap: 4px;
    }
    
    .action-btn {
      cursor: pointer;
      color: #409eff;
      padding: 4px 8px;
      border-radius: 4px;
      
      &:hover {
        text-decoration: none;
        background-color: #ecf5ff;
      }
    }
  }
  
  .root-folders {
    margin-left: 0;
  }
}
</style>  