<template>
  <div class="topic-list">
    <div v-if="topics.length === 0 && !loading" class="empty-topics">
      <el-empty description="暂无讨论话题" :image-size="100"></el-empty>
    </div>
    
    <!-- 置顶话题 -->
    <div v-for="topic in pinnedTopics" :key="'pinned-'+topic.id" class="topic-container">
      <div class="topic-item pinned-topic">
        <div class="pinned-label">置顶</div>
        <div class="topic-header">
          <img :src="topic.avatar" alt="用户头像" class="avatar">
          <div class="user-info">
            <div class="user-name">{{ topic.teaName }}</div>
            <div class="user-university">{{ topic.institution }}</div>
          </div>
        </div>
        <div class="topic-title">{{ topic.title }}</div>
        <div class="topic-content">{{ topic.content }}</div>
        <div class="topic-meta">
          <span class="like-count" @click="toggleLike(topic)">
            <img 
              :src="topic.isLiked ? likeActiveIcon : likeNormalIcon" 
              class="like-icon"
              alt="点赞"
            >
            {{ topic.likeCount }}
          </span>
          <span class="star-count" @click="toggleStar(topic)">
            <img 
              :src="topic.isStarred ? starActiveIcon : starNormalIcon" 
              class="star-icon"
              alt="收藏"
            >
            {{ topic.starCount }}
          </span>
          <span class="comment-count">
            <img 
              :src="commentIcon" 
              class="comment-icon"
              alt="评论"
            >
            {{ topic.commentCount }}</span>
          <span class="reply-date">{{ formatDate(topic.publishTime) }}</span>
        </div>

        <div v-if="expandedTopicId === topic.id" class="reply-container">
          <ReplyList :replies="topic.commentVOList || []" :topicId="topic.id" @reply-success="fetchTopics" />
        </div>

        <div class="topic-footer">
          <el-button @click="toggleReplies(topic.id)" :type="expandedTopicId === topic.id ? 'primary' : 'default'"
            :loading="loadingTopics.includes(topic.id)" class="reply-btn">
            {{ expandedTopicId === topic.id ? '收起评论' : '查看评论' }}
          </el-button>
          <el-button @click="toggleReplyBox(topic.id)" class="reply-btn">
            {{ showReplyBox[topic.id] ? '取消回复' : '回答' }}
          </el-button>
        </div>
      </div>

      <!-- 回复输入框 - 放在当前话题下方 -->
      <div v-if="showReplyBox[topic.id]" class="reply-box-container">
        <div class="answer-panel">
          <textarea class="answer-textarea" v-model="replyContent[topic.id]" placeholder="请输入您的回答" rows="4"></textarea>
          <div class="answer-footer">
            <span class="tip">温馨提示：请认真作答，老师会对你回答进行评分，无效回答或刷屏会影响你的本课成绩！</span>
            <div class="btn-group">
              <button class="cancel-btn" @click="cancelReply(topic.id)">取消</button>
              <button class="submit-btn" @click="submitReply(topic.id)" :disabled="isSubmitting[topic.id]">
                {{ isSubmitting[topic.id] ? '提交中...' : '发布回答' }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 普通话题 -->
    <div v-for="topic in normalTopics" :key="'normal-'+topic.id" class="topic-container">
      <div class="topic-item">
        <div class="topic-header">
          <img :src="topic.avatar" alt="用户头像" class="avatar">
          <div class="user-info">
            <div class="user-name">{{ topic.teaName }}</div>
            <div class="user-university">{{ topic.institution }}</div>
          </div>
        </div>
        <div class="topic-title">{{ topic.title }}</div>
        <div class="topic-content">{{ topic.content }}</div>
        <div class="topic-meta">
          <span class="like-count" @click="toggleLike(topic)">
            <img 
              :src="topic.isLiked ? likeActiveIcon : likeNormalIcon" 
              class="like-icon"
              alt="点赞"
            >
            {{ topic.likeCount }}
          </span>
          <span class="star-count" @click="toggleStar(topic)">
            <img 
              :src="topic.isStarred ? starActiveIcon : starNormalIcon" 
              class="star-icon"
              alt="收藏"
            >
            {{ topic.starCount }}
          </span>
          <span class="comment-count">
            <img 
              :src="commentIcon" 
              class="comment-icon"
              alt="评论"
            >
            {{ topic.commentCount }}</span>
          <span class="reply-date">{{ formatDate(topic.publishTime) }}</span>
        </div>

        <div v-if="expandedTopicId === topic.id" class="reply-container">
          <ReplyList :replies="topic.commentVOList || []" :topicId="topic.id" @reply-success="fetchTopics" />
        </div>

        <div class="topic-footer">
          <el-button @click="toggleReplies(topic.id)" :type="expandedTopicId === topic.id ? 'primary' : 'default'"
            :loading="loadingTopics.includes(topic.id)" class="reply-btn">
            {{ expandedTopicId === topic.id ? '收起评论' : '查看评论' }}
          </el-button>
          <el-button @click="toggleReplyBox(topic.id)" class="reply-btn">
            {{ showReplyBox[topic.id] ? '取消回复' : '回答' }}
          </el-button>
        </div>
      </div>

      <!-- 回复输入框 - 放在当前话题下方 -->
      <div v-if="showReplyBox[topic.id]" class="reply-box-container">
        <div class="answer-panel">
          <textarea class="answer-textarea" v-model="replyContent[topic.id]" placeholder="请输入您的回答" rows="4"></textarea>
          <div class="answer-footer">
            <span class="tip">温馨提示：请认真作答，老师会对你回答进行评分，无效回答或刷屏会影响你的本课成绩！</span>
            <div class="btn-group">
              <button class="cancel-btn" @click="cancelReply(topic.id)">取消</button>
              <button class="submit-btn" @click="submitReply(topic.id)" :disabled="isSubmitting[topic.id]">
                {{ isSubmitting[topic.id] ? '提交中...' : '发布回答' }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import ReplyList from './ReplyList.vue';
import { 
  getPostListTree, 
  saveComment, 
  savePostLike, 
  removePostLike,
  savePostStar,
  removePostStar,
  getPostStarList,
  getPostLikeList
} from '@/api/student/discuss';
import { useUserStore } from '@/stores/userStore';
import { useCourseStore } from '@/stores/courseStore'
import { useRoute } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { formatAgo } from '@/utils/dateUtils';

// 导入图标
import likeNormalIcon from '@/assets/img/General/icon-like-normal.png';
import likeActiveIcon from '@/assets/img/General/icon-like-actived.png';
import starNormalIcon from '@/assets/img/General/icon-star-yes-normal.png';
import starActiveIcon from '@/assets/img/General/icon-star-yes-hover.png';
import commentIcon from '@/assets/img/General/icon-reply-normal.png'

const route = useRoute();
const userStore = useUserStore();
const courseStore = useCourseStore()
const topics = ref([])
const expandedTopicId = ref(null);
const loadingTopics = ref([]);
const loading = ref(false);
const courseId = ref(route.params.courseId || route.query.courseId);
const hasJoinedCourse = computed(() => courseStore.hasJoinedCourse)// 是否已加入课程

// 回复面板相关状态
const showReplyBox = ref({}); // 控制每个话题的回复框显示状态
const replyContent = ref({}); // 每个话题的回复内容
const isSubmitting = ref({}); // 每个话题的提交状态

// 计算属性：置顶话题
const pinnedTopics = computed(() => {
  return topics.value.filter(topic => topic.isPinned === 1);
});

// 计算属性：普通话题
const normalTopics = computed(() => {
  return topics.value.filter(topic => topic.isPinned !== 1);
});



const fetchTopics = async () => {
  try {
    loading.value = true;
    const res = await getPostListTree(courseId.value);
    if (res.code === 200) {
      topics.value = res.result;
    }
  } catch (error) {
    console.error('获取帖子列表失败:', error);
    ElMessage.error('获取话题列表失败');
  } finally {
    loading.value = false;
  }
};

// 点赞/取消点赞
const toggleLike = async (topic) => {
  if (!hasJoinedCourse.value) {
    ElMessage.warning('请先加入课程才能点赞')
    return
  }
  try {
    if (topic.isLiked) {
      // 取消点赞时先获取likeId
      const likeRes = await getPostLikeList({
        userId: userStore.user.id,
        postId: topic.id
      });
      
      if (likeRes.code === 200 && likeRes.result.records.length > 0) {
        const res = await removePostLike({ id: likeRes.result.records[0].id });
        if (res.code === 200) {
          topic.isLiked = false;
          topic.likeCount--;
          ElMessage.success('已取消点赞');
        }
      } else {
        throw new Error('找不到点赞记录');
      }
    } else {
      // 点赞操作保持不变
      const res = await savePostLike({
        postId: topic.id,
        userId: userStore.user.id
      });
      if (res.code === 200) {
        topic.isLiked = true;
        topic.likeCount++;
        ElMessage.success('点赞成功');
      }
    }
  } catch (error) {
    console.error('操作失败:', error);
    ElMessage.error(error.message || '操作失败，请重试');
  }
};

// 收藏/取消收藏
const toggleStar = async (topic) => {
  if (!hasJoinedCourse.value) {
    ElMessage.warning('请先加入课程才能收藏')
    return
  }
  try {
    if (topic.isStarred) {
      // 取消收藏时先获取starId
      const starRes = await getPostStarList({
        userId: userStore.user.id,
        postId: topic.id
      });
      
      if (starRes.code === 200 && starRes.result.records.length > 0) {
        const res = await removePostStar({ id: starRes.result.records[0].id });
        if (res.code === 200) {
          topic.isStarred = false;
          topic.starCount--;
          ElMessage.success('已取消收藏');
        }
      } else {
        throw new Error('找不到收藏记录');
      }
    } else {
      // 收藏操作保持不变
      const res = await savePostStar({
        postId: topic.id,
        userId: userStore.user.id
      });
      if (res.code === 200) {
        topic.isStarred = true;
        topic.starCount++;
        ElMessage.success('收藏成功');
      }
    }
  } catch (error) {
    console.error('操作失败:', error);
    ElMessage.error(error.message || '操作失败，请重试');
  }
};

const toggleReplies = async (topicId) => {
  
  if (expandedTopicId.value === topicId) {
    expandedTopicId.value = null;
    return;
  }
  expandedTopicId.value = topicId;
};

// 切换回复框显示状态
const toggleReplyBox = (topicId) => {
   if (!hasJoinedCourse.value) {
    ElMessage.warning('请先加入课程才能评论')
    return
  }
  showReplyBox.value[topicId] = !showReplyBox.value[topicId];
  if (showReplyBox.value[topicId]) {
    // 如果展开回复框，收起其他回复框
    Object.keys(showReplyBox.value).forEach(key => {
      if (key !== topicId) {
        showReplyBox.value[key] = false;
      }
    });
  }
};

// 取消回复
const cancelReply = (topicId) => {
  showReplyBox.value[topicId] = false;
  replyContent.value[topicId] = '';
};

// 提交回复
const submitReply = async (topicId) => {
  if (!replyContent.value[topicId]?.trim()) {
    ElMessage.warning('请输入回复内容');
    return;
  }

  try {
    isSubmitting.value[topicId] = true;

    const data = {
      postId: topicId,
      commenterId: userStore.user.id,
      content: replyContent.value[topicId],
      parentId: null
    };

    const res = await saveComment(data);

    if (res.code === 200) {
      ElMessage.success('回复成功');

      // 更新话题的评论数量
      const topicIndex = topics.value.findIndex(t => t.id === topicId);
      if (topicIndex !== -1) {
        // 添加新评论到列表顶部
        if (!topics.value[topicIndex].commentVOList) {
          topics.value[topicIndex].commentVOList = [];
        }

        // 假设API返回新创建的评论对象
        const newComment = {
          ...res.result, // 假设res.result包含新评论数据
          createTime: new Date().toISOString() // 添加时间戳
        };

        topics.value[topicIndex].commentVOList.unshift(newComment);
        topics.value[topicIndex].commentCount++;
      }

      // 重置回复框
      cancelReply(topicId);
    } else {
      ElMessage.error(res.message || '回复失败');
    }
  } catch (error) {
    console.error('提交回复失败:', error);
    ElMessage.error('网络错误，请稍后重试');
  } finally {
    isSubmitting.value[topicId] = false;
  }
};

const formatDate = (timestamp) => {
  if (!timestamp) return '';
  return formatAgo(timestamp);
};

onMounted(() => {
  fetchTopics();
});
</script>

<style scoped lang="scss">
.topic-list {
  max-width: 95%;
  margin: 0 auto;

  .topic-container {
    margin-bottom: 16px;
    position: relative;
  }

  .topic-item {
    position: relative;
    padding: 16px;
    border-radius: 8px;
    background-color: #f5f2f2;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    
    &.pinned-topic {
      border-left: 4px solid #ffc107; // 置顶话题左侧黄色边框
      background-color: #fffaf0; // 置顶话题浅黄色背景
    }

    .pinned-label {
      position: absolute;
      top: 10px;
      right: 10px;
      background-color: #ffc107;
      color: white;
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
    }

    .topic-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 12px;
      }

      .user-info {
        .user-name {
          font-weight: bold;
        }

        .user-university {
          font-size: 12px;
          color: #666;
        }
      }
    }

    .topic-title {
      font-weight: bold;
      font-size: 18px;
      margin-bottom: 8px;
    }

    .topic-content {
      margin-bottom: 12px;
      line-height: 1.6;
    }

    .topic-meta {
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #666;
      margin-bottom: 12px;
      justify-content: space-between;

      > span {
        padding-left: 10px;
        display: flex;
        align-items: center;
        cursor: pointer;
        

        .star-icon{
          width: 20px;
          height: 20px;
          margin-right: 4px;
        }

        .like-icon{
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }

        .comment-icon{
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }

        &:not(.reply-date):hover {
          color: var(--el-color-primary);
        }
      }

      .reply-date {
        margin-left: auto; // 时间靠右显示
        color: #999;
        cursor: default;
      }
    }

    .topic-footer {
      border-top: 1px solid #eee;
      display: flex;
      justify-content: center;
      padding-top: 16px;

      .reply-btn {
        margin: 0 8px;
        width: 120px;
      }
    }

    .reply-container {
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid #eee;
    }
  }

  .reply-box-container {
    margin-top: 8px;
  }
}

/* 回复面板样式 */
.answer-panel {
  padding: 16px;
  border: 1px solid #eee;
  border-radius: 4px;
  background-color: #f9f9f9;

  .answer-textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    resize: none;
    margin-bottom: 16px;
  }

  .answer-footer {
    display: flex;
    flex-direction: column;

    .tip {
      font-size: 12px;
      color: #666;
      margin-bottom: 16px;
    }

    .btn-group {
      display: flex;
      justify-content: flex-end;

      .cancel-btn,
      .submit-btn {
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        margin-left: 8px;
      }

      .cancel-btn {
        border: 1px solid #ccc;
        background-color: white;
      }

      .submit-btn {
        border: none;
        background-color: #409eff;
        color: white;
      }

      .submit-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }
}
</style>