import { createApp } from 'vue'
import CustomNotification from '@/components/CustomNotification.vue'

export const showNotification = (message, options = {}) => {
  const { type = 'success', duration = 3000 } = options
  
  const mountNode = document.createElement('div')
  document.body.appendChild(mountNode)
  
  const app = createApp(CustomNotification, {
    message,
    type,
    duration,
    onClose: () => {
      app.unmount()
      document.body.removeChild(mountNode)
    }
  })
  
  app.mount(mountNode)
  
  return app
}