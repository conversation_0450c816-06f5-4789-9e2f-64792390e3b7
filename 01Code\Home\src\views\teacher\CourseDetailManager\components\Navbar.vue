<template>
  <nav class="navbar">
    <!-- 左侧 Logo 和名称 -->
    <div class="navbar-left">
      <div class="logo-container">
        <img src="#" alt="Treenity Logo" class="logo-image" />
        <span class="logo-text">Treenity</span>
      </div>
    </div>

    <!-- 右侧导航链接 -->
    <div class="navbar-right">
      <ul class="nav-links">
        <li class="nav-item">
          <a href="#" class="nav-link">个人资源库</a>
        </li>
        <li class="nav-item">
          <a href="#" class="nav-link">题库</a>
        </li>
        <li class="nav-item">
          <a href="#" class="nav-link">我的课程</a>
        </li>
        <li class="nav-item user-profile">
          <a href="#" class="nav-link">
            <span class="user-icon">杨</span>
            <span class="user-name">杨兵</span>
          </a>
        </li>
      </ul>
    </div>
  </nav>
</template>

<script setup>

</script>

<style lang="scss" scoped>
// 定义颜色变量
$primary-color: #165DFF;
$text-color: #333;
$hover-color: #4080FF;
$light-bg: #F5F7FA;
$border-color: #E5E6EB;

// 导航栏整体样式
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  background-color: white;
  border-bottom: 1px solid $border-color;
  padding: 0 20px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

  // 左侧 Logo 区域
  .navbar-left {
    display: flex;
    align-items: center;
  }

  // 右侧导航链接区域
  .navbar-right {
    display: flex;
    align-items: center;
  }
}

// Logo 样式
.logo-container {
  display: flex;
  align-items: center;
  cursor: pointer;

  .logo-image {
    height: 32px;
    width: 32px;
    margin-right: 8px;
  }

  .logo-text {
    font-size: 20px;
    font-weight: 600;
    color: $primary-color;
  }
}

// 导航链接样式
.nav-links {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;

  .nav-item {
    margin-left: 24px;

    .nav-link {
      display: flex;
      align-items: center;
      height: 60px;
      color: $text-color;
      text-decoration: none;
      font-size: 16px;
      position: relative;
      transition: color 0.2s;

      &:hover {
        color: $hover-color;
      }

      // 活动状态下的下划线
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 0;
        height: 2px;
        background-color: $primary-color;
        transition: width 0.2s;
      }

      &:hover::after,
      &.active::after {
        width: 100%;
      }
    }
  }

  // 用户资料项的样式
  .user-profile {
    .nav-link {
      .user-icon {
        display: inline-flex;
        justify-content: center;
        align-items: center;
        width: 28px;
        height: 28px;
        background-color: $primary-color;
        color: white;
        border-radius: 50%;
        margin-right: 8px;
        font-size: 14px;
      }

      .user-name {
        font-weight: 500;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .navbar {
    flex-direction: column;
    height: auto;
    padding: 10px 0;

    .navbar-left,
    .navbar-right {
      width: 100%;
      justify-content: center;
    }

    .nav-links {
      flex-direction: column;
      width: 100%;

      .nav-item {
        margin-left: 0;
        text-align: center;

        .nav-link {
          height: 40px;
          justify-content: center;
        }
      }
    }
  }
}
</style>