<template>
  <div class="project-gallery">
    <div class="gallery-header">
      <h2>项目图片展示</h2>
      <div class="gallery-stats">
        <span class="stat-item">
          <i class="fas fa-images"></i>
          共 {{ galleryData.images.length }} 张图片
        </span>
        <span class="stat-item">
          <i class="fas fa-eye"></i>
          总浏览 {{ galleryData.totalViews }} 次
        </span>
      </div>
    </div>
    
    <div class="gallery-categories">
      <button
        v-for="category in galleryData.categories"
        :key="category.key"
        :class="['category-btn', { active: activeCategory === category.key }]"
        @click="setActiveCategory(category.key)"
      >
        {{ category.name }}
        <span class="count">({{ category.count }})</span>
      </button>
    </div>
    
    <div class="gallery-grid">
      <div
        v-for="(image, index) in filteredImages"
        :key="image.id"
        class="gallery-item"
        @click="openLightbox(index)"
      >
        <div class="image-container">
          <img :src="image.thumbnail" :alt="image.title" class="gallery-image" />
          <div class="image-overlay">
            <div class="overlay-content">
              <h4 class="image-title">{{ image.title }}</h4>
              <p class="image-description">{{ image.description }}</p>
              <div class="image-actions">
                <button class="action-btn" @click.stop="viewImage(image)">
                  <i class="fas fa-eye"></i>
                </button>
                <button class="action-btn" @click.stop="downloadImage(image)">
                  <i class="fas fa-download"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="image-info">
          <h4>{{ image.title }}</h4>
          <span class="image-category">{{ getCategoryName(image.category) }}</span>
        </div>
      </div>
    </div>
    
    <!-- 图片灯箱 -->
    <div v-if="showLightbox" class="lightbox-modal" @click="closeLightbox">
      <div class="lightbox-content">
        <button class="lightbox-close" @click="closeLightbox">
          <i class="fas fa-times"></i>
        </button>
        
        <button class="lightbox-nav prev" @click="prevImage" v-if="currentImageIndex > 0">
          <i class="fas fa-chevron-left"></i>
        </button>
        
        <button class="lightbox-nav next" @click="nextImage" v-if="currentImageIndex < filteredImages.length - 1">
          <i class="fas fa-chevron-right"></i>
        </button>
        
        <div class="lightbox-image-container">
          <img :src="currentImage.fullSize" :alt="currentImage.title" class="lightbox-image" />
        </div>
        
        <div class="lightbox-info">
          <h3>{{ currentImage.title }}</h3>
          <p>{{ currentImage.description }}</p>
          <div class="lightbox-meta">
            <span>类别: {{ getCategoryName(currentImage.category) }}</span>
            <span>尺寸: {{ currentImage.dimensions }}</span>
            <span>上传时间: {{ currentImage.uploadDate }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const activeCategory = ref('all')
const showLightbox = ref(false)
const currentImageIndex = ref(0)

const galleryData = ref({
  totalViews: 8642,
  categories: [
    { key: 'all', name: '全部', count: 24 },
    { key: 'design', name: '设计稿', count: 8 },
    { key: 'prototype', name: '原型图', count: 6 },
    { key: 'interface', name: '界面截图', count: 7 },
    { key: 'process', name: '制作过程', count: 3 }
  ],
  images: [
    {
      id: 1,
      title: '主界面设计',
      description: '虚拟仿真平台的主界面设计稿',
      category: 'design',
      thumbnail: '/src/assets/img/gallery/thumb1.jpg',
      fullSize: '/src/assets/img/gallery/full1.jpg',
      dimensions: '1920x1080',
      uploadDate: '2024-03-15'
    },
    {
      id: 2,
      title: '色彩选择器',
      description: '色彩设计工具的交互界面',
      category: 'interface',
      thumbnail: '/src/assets/img/gallery/thumb2.jpg',
      fullSize: '/src/assets/img/gallery/full2.jpg',
      dimensions: '1440x900',
      uploadDate: '2024-03-12'
    },
    {
      id: 3,
      title: '用户流程图',
      description: '用户操作流程的原型设计',
      category: 'prototype',
      thumbnail: '/src/assets/img/gallery/thumb3.jpg',
      fullSize: '/src/assets/img/gallery/full3.jpg',
      dimensions: '2048x1536',
      uploadDate: '2024-03-10'
    },
    {
      id: 4,
      title: '3D场景渲染',
      description: '虚拟环境的3D场景效果图',
      category: 'design',
      thumbnail: '/src/assets/img/gallery/thumb4.jpg',
      fullSize: '/src/assets/img/gallery/full4.jpg',
      dimensions: '1920x1080',
      uploadDate: '2024-03-08'
    },
    {
      id: 5,
      title: '开发过程记录',
      description: '项目开发过程的截图记录',
      category: 'process',
      thumbnail: '/src/assets/img/gallery/thumb5.jpg',
      fullSize: '/src/assets/img/gallery/full5.jpg',
      dimensions: '1366x768',
      uploadDate: '2024-03-05'
    },
    {
      id: 6,
      title: '交互原型',
      description: '用户交互的原型设计图',
      category: 'prototype',
      thumbnail: '/src/assets/img/gallery/thumb6.jpg',
      fullSize: '/src/assets/img/gallery/full6.jpg',
      dimensions: '1600x1200',
      uploadDate: '2024-03-03'
    }
  ]
})

const filteredImages = computed(() => {
  if (activeCategory.value === 'all') {
    return galleryData.value.images
  }
  return galleryData.value.images.filter(image => image.category === activeCategory.value)
})

const currentImage = computed(() => {
  return filteredImages.value[currentImageIndex.value] || {}
})

const setActiveCategory = (category) => {
  activeCategory.value = category
}

const getCategoryName = (categoryKey) => {
  const category = galleryData.value.categories.find(cat => cat.key === categoryKey)
  return category ? category.name : categoryKey
}

const openLightbox = (index) => {
  currentImageIndex.value = index
  showLightbox.value = true
  document.body.style.overflow = 'hidden'
}

const closeLightbox = () => {
  showLightbox.value = false
  document.body.style.overflow = 'auto'
}

const prevImage = () => {
  if (currentImageIndex.value > 0) {
    currentImageIndex.value--
  }
}

const nextImage = () => {
  if (currentImageIndex.value < filteredImages.value.length - 1) {
    currentImageIndex.value++
  }
}

const viewImage = (image) => {
  console.log('查看图片:', image.title)
}

const downloadImage = (image) => {
  console.log('下载图片:', image.title)
  // 实际项目中这里会触发图片下载
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.project-gallery {
  padding: 32px;
  
  .gallery-header {
    margin-bottom: 24px;
    
    h2 {
      font-size: 28px;
      font-weight: 700;
      color: $text-color;
      margin: 0 0 12px 0;
    }
    
    .gallery-stats {
      display: flex;
      gap: 24px;
      
      .stat-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        color: rgba($text-color, 0.6);
        
        i {
          color: $primary-color;
        }
      }
    }
  }
  
  .gallery-categories {
    display: flex;
    gap: 8px;
    margin-bottom: 32px;
    flex-wrap: wrap;
    
    .category-btn {
      padding: 8px 16px;
      background: white;
      border: 1px solid $border-color;
      border-radius: 20px;
      font-size: 14px;
      color: $text-color;
      cursor: pointer;
      transition: all 0.3s ease;
      
      .count {
        color: rgba($text-color, 0.5);
        margin-left: 4px;
      }
      
      &:hover {
        border-color: $primary-color;
        color: $primary-color;
      }
      
      &.active {
        background: $primary-color;
        border-color: $primary-color;
        color: white;
        
        .count {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }
  
  .gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
    
    .gallery-item {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      cursor: pointer;
      
      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        
        .image-overlay {
          opacity: 1;
        }
      }
      
      .image-container {
        position: relative;
        height: 200px;
        overflow: hidden;
        
        .gallery-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.3s ease;
        }
        
        .image-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.7);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s ease;
          
          .overlay-content {
            text-align: center;
            color: white;
            padding: 20px;
            
            .image-title {
              font-size: 16px;
              font-weight: 600;
              margin: 0 0 8px 0;
            }
            
            .image-description {
              font-size: 14px;
              margin: 0 0 16px 0;
              opacity: 0.9;
            }
            
            .image-actions {
              display: flex;
              gap: 8px;
              justify-content: center;
              
              .action-btn {
                width: 36px;
                height: 36px;
                background: rgba(255, 255, 255, 0.2);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                color: white;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;
                
                &:hover {
                  background: rgba(255, 255, 255, 0.3);
                  transform: scale(1.1);
                }
              }
            }
          }
        }
      }
      
      .image-info {
        padding: 16px;
        
        h4 {
          font-size: 16px;
          font-weight: 600;
          color: $text-color;
          margin: 0 0 8px 0;
        }
        
        .image-category {
          font-size: 12px;
          color: rgba($text-color, 0.6);
          background: rgba($primary-color, 0.1);
          padding: 2px 8px;
          border-radius: 10px;
        }
      }
    }
  }
  
  .lightbox-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    
    .lightbox-content {
      position: relative;
      max-width: 90vw;
      max-height: 90vh;
      display: flex;
      flex-direction: column;
      
      .lightbox-close {
        position: absolute;
        top: -50px;
        right: 0;
        width: 40px;
        height: 40px;
        background: rgba(255, 255, 255, 0.1);
        border: none;
        border-radius: 50%;
        color: white;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
        
        &:hover {
          background: rgba(255, 255, 255, 0.2);
        }
      }
      
      .lightbox-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 50px;
        height: 50px;
        background: rgba(255, 255, 255, 0.1);
        border: none;
        border-radius: 50%;
        color: white;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
        transition: all 0.3s ease;
        
        &:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: translateY(-50%) scale(1.1);
        }
        
        &.prev {
          left: -70px;
        }
        
        &.next {
          right: -70px;
        }
      }
      
      .lightbox-image-container {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
        
        .lightbox-image {
          max-width: 100%;
          max-height: 70vh;
          object-fit: contain;
          border-radius: 8px;
        }
      }
      
      .lightbox-info {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 8px;
        padding: 20px;
        color: white;
        text-align: center;
        
        h3 {
          font-size: 20px;
          font-weight: 600;
          margin: 0 0 8px 0;
        }
        
        p {
          font-size: 14px;
          margin: 0 0 16px 0;
          opacity: 0.9;
        }
        
        .lightbox-meta {
          display: flex;
          gap: 16px;
          justify-content: center;
          font-size: 12px;
          opacity: 0.8;
          flex-wrap: wrap;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .project-gallery {
    padding: 20px;
    
    .gallery-header h2 {
      font-size: 24px;
    }
    
    .gallery-stats {
      flex-direction: column;
      gap: 8px;
    }
    
    .gallery-grid {
      grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
      gap: 16px;
    }
    
    .lightbox-content {
      .lightbox-nav {
        &.prev {
          left: -60px;
        }
        
        &.next {
          right: -60px;
        }
      }
      
      .lightbox-info .lightbox-meta {
        flex-direction: column;
        gap: 8px;
      }
    }
  }
}
</style>
