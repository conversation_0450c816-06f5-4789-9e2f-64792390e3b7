{"_attachments": {}, "_id": "vue-types", "_rev": "296041-61f1cad9efbf788ede9434be", "author": {"name": "<PERSON>"}, "description": "Prop types utility for Vue", "dist-tags": {"beta": "5.0.0-beta.2", "latest": "6.0.0", "next": "0.0.0-20250212054805", "vue3": "3.0.2"}, "license": "MIT", "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "name": "vue-types", "readme": "ERROR: No README data found!", "time": {"created": "2022-01-26T22:27:37.750Z", "modified": "2025-02-17T01:41:03.689Z", "4.1.1": "2021-10-14T02:52:47.037Z", "4.1.1-rc.1": "2021-10-07T01:41:05.114Z", "4.1.1-rc.0": "2021-09-08T09:50:55.639Z", "4.1.0": "2021-09-06T02:40:30.651Z", "4.1.0-rc.1": "2021-08-09T02:28:02.869Z", "4.0.3": "2021-08-09T02:02:17.713Z", "4.0.2": "2021-08-08T05:23:41.544Z", "4.1.0-beta.3": "2021-08-07T05:30:09.420Z", "4.0.1": "2021-07-28T02:03:36.846Z", "4.1.0-beta.2": "2021-07-20T00:21:58.162Z", "4.1.0-beta.1": "2021-07-10T07:30:49.873Z", "4.0.0": "2021-06-27T09:05:26.934Z", "4.0.0-rc.3": "2021-06-12T07:04:22.674Z", "4.0.0-rc.2": "2021-04-15T06:45:44.786Z", "4.0.0-rc.1": "2021-04-15T05:53:45.325Z", "3.0.2": "2021-02-02T06:24:45.895Z", "2.0.3": "2021-02-02T06:16:26.198Z", "3.0.1": "2020-10-10T09:47:37.885Z", "2.0.2": "2020-10-10T09:29:42.700Z", "3.0.0": "2020-09-06T03:20:53.313Z", "2.0.1": "2020-08-14T04:44:28.718Z", "2.0.1-rc.1": "2020-08-13T12:23:40.180Z", "2.0.0": "2020-08-09T09:37:13.801Z", "2.0.0-rc.1": "2020-08-01T05:28:07.969Z", "2.0.0-rc.0": "2020-07-15T08:37:29.666Z", "1.8.1-beta.2": "2020-06-25T06:44:15.503Z", "1.8.1-beta.1": "2020-06-25T06:09:30.208Z", "2.0.0-alpha.1": "2020-06-15T06:37:12.673Z", "1.7.0": "2019-11-26T05:54:27.367Z", "1.6.2": "2019-11-12T04:13:25.420Z", "1.6.1": "2019-11-08T07:12:39.351Z", "1.6.1-beta.1": "2019-11-08T04:33:20.392Z", "1.7.0-beta.3": "2019-10-23T04:34:48.858Z", "1.7.0-beta.1": "2019-09-02T04:43:09.779Z", "1.6.0": "2019-08-05T06:01:37.307Z", "1.5.7": "2019-07-17T06:18:48.660Z", "1.5.6": "2019-07-11T01:56:53.575Z", "1.5.5": "2019-07-05T05:23:27.020Z", "1.5.4": "2019-07-05T03:21:44.763Z", "1.5.3": "2019-04-02T00:59:45.299Z", "1.5.2": "2019-04-01T06:53:39.123Z", "1.5.1": "2019-04-01T04:12:09.020Z", "1.5.0": "2019-02-14T03:55:51.854Z", "1.3.4": "2018-12-14T05:36:53.807Z", "1.3.4-beta.1": "2018-12-12T05:46:37.900Z", "1.3.4-beta.0": "2018-10-04T05:02:44.198Z", "1.3.3": "2018-09-03T04:07:37.867Z", "1.3.2": "2018-06-24T10:29:11.888Z", "1.3.1": "2018-06-12T03:52:12.887Z", "1.3.0": "2018-05-30T08:38:39.737Z", "1.2.3": "2018-05-14T02:30:39.679Z", "1.2.2": "2018-03-19T09:14:14.103Z", "1.2.1": "2018-03-19T06:31:02.762Z", "1.2.0": "2018-03-06T05:42:43.652Z", "1.1.3": "2018-01-17T12:44:58.514Z", "1.1.2": "2018-01-14T22:15:50.163Z", "1.1.1": "2017-12-21T18:36:30.413Z", "1.1.0": "2017-12-21T18:31:11.213Z", "1.0.3": "2017-12-21T16:58:59.197Z", "1.0.2": "2017-08-16T15:05:05.024Z", "1.0.1": "2017-05-29T12:20:06.809Z", "1.0.0": "2017-05-27T18:45:36.554Z", "1.0.0-beta.1": "2017-04-04T09:42:41.310Z", "0.6.5": "2017-04-03T15:53:50.864Z", "0.6.4": "2017-04-03T14:33:32.947Z", "0.6.2": "2017-03-30T16:08:10.721Z", "0.6.1": "2017-03-30T15:35:23.252Z", "0.6.0": "2017-02-27T16:10:02.936Z", "0.5.2": "2016-11-14T09:32:01.439Z", "0.5.1": "2016-11-14T09:28:14.010Z", "0.5.0": "2016-11-11T09:45:21.426Z", "0.4.0": "2016-10-29T14:02:58.721Z", "0.3.0": "2016-10-29T13:44:01.768Z", "0.2.0": "2016-10-28T15:56:05.848Z", "0.1.0": "2016-10-28T15:41:20.319Z", "4.2.0-rc.1": "2022-03-02T10:04:40.618Z", "4.2.0": "2022-07-23T02:34:17.082Z", "4.2.1": "2022-08-01T06:04:16.638Z", "5.0.0-beta.1": "2022-08-08T05:45:48.233Z", "5.0.0-beta.2": "2022-11-22T06:23:51.534Z", "5.0.0": "2022-11-22T08:42:39.078Z", "5.0.1": "2022-11-23T06:37:55.097Z", "5.0.2": "2022-12-22T03:10:11.428Z", "5.0.3-next.0": "2023-05-15T03:09:32.053Z", "5.0.3": "2023-05-15T03:12:32.757Z", "5.0.4": "2023-06-15T06:37:48.957Z", "5.1.0": "2023-06-23T05:20:39.574Z", "0.0.0-20230720044845": "2023-07-20T04:49:20.651Z", "0.0.0-20230721024211": "2023-07-21T02:42:44.594Z", "5.1.1": "2023-07-26T04:47:09.412Z", "0.0.0-20240502090343": "2024-05-02T09:04:08.275Z", "0.0.0-20240502090959": "2024-05-02T09:10:22.837Z", "0.0.0-20240502091121": "2024-05-02T09:11:45.012Z", "5.1.2": "2024-05-02T09:24:25.688Z", "0.0.0-20240701051251": "2024-07-01T05:13:16.549Z", "5.1.3": "2024-07-09T01:15:14.652Z", "0.0.0-20241212014929": "2024-12-12T01:49:39.649Z", "0.0.0-20250212054805": "2025-02-12T05:48:16.081Z", "6.0.0": "2025-02-17T01:39:45.768Z"}, "versions": {"4.1.1": {"name": "vue-types", "version": "4.1.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"require": "./shim/index.cjs.js", "import": "./shim/index.modern.js"}, "./nuxt": "./nuxt/module.js"}, "types": "dist/index.d.ts", "engines": {"node": ">=12.16.0"}, "scripts": {"prepublishOnly": "run-s lint lint:ts test build", "build": "run-s 'clean:*' copy 'build:**'", "clean:dist": "del dist", "clean:shim": "del \"shim/*.*\" \"!shim/package.json\"", "copy": "cpy src/*.d.ts dist", "build:ts": "microbundle --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json --format=umd", "build:shim:ts": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.ts -o shim/index.js --format=modern,es --no-sourcemap", "build:shim:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.cjs.js --no-pkg-main --format=cjs --no-sourcemap", "build:shim:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.js --format=umd --no-sourcemap", "test": "jest", "lint": "run-s lint:*", "lint:ts": "tsc --noEmit -p ./examples", "lint:src": "eslint '{examples,src,__tests__,.}/**/*.{ts,js,cjs}'", "examples": "vite examples -c examples/vite.config.js", "docs:dev": "vuepress dev docs", "docs:build": "cross-env NODE_ENV=production vuepress build docs"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.14.5", "@babel/plugin-proposal-optional-chaining": "7.14.5", "@nuxt/types": "2.15.8", "@types/jest": "27.0.2", "@types/node": "14.17.26", "@typescript-eslint/eslint-plugin": "5.0.0", "@typescript-eslint/parser": "5.0.0", "babel-plugin-transform-node-env-inline": "0.4.3", "cpy-cli": "3.1.1", "cross-env": "7.0.3", "del": "6.0.0", "del-cli": "4.0.1", "eslint": "8.0.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "4.0.0", "eslint-plugin-vue": "7.19.1", "jest": "27.2.5", "microbundle": "0.14.0", "npm-run-all": "4.1.5", "prettier": "2.4.1", "ts-jest": "27.0.5", "typescript": "4.4.4", "vite": "2.6.7", "vue2": "npm:vue@2.6.14", "vue3": "npm:vue@3.2.20", "vuepress": "1.8.2"}, "dependencies": {"is-plain-object": "5.0.0"}, "gitHead": "df463cf0ae52a428e3028fa0f0a793779feee64b", "_id": "vue-types@4.1.1", "_nodeVersion": "14.18.0", "_npmVersion": "6.14.15", "dist": {"shasum": "7d7a4e4a01249a51b6da3faa301248c2ea5f5b97", "size": 74053, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-4.1.1.tgz", "integrity": "sha512-Jq2GZ/w6rExJbLA/h7nHBFLciu+YNekgox0DB64wN1snZ4IIJMq+qnqp1/vE4fc7vEjZcP5KGhLzkkSjIHLRzw=="}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_4.1.1_1634179966854_0.650876041790748"}, "_hasShrinkwrap": false, "publish_time": 1634179967037, "_cnpm_publish_time": 1634179967037, "_cnpmcore_publish_time": "2021-12-16T16:04:55.578Z"}, "4.1.1-rc.1": {"name": "vue-types", "version": "4.1.1-rc.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"require": "./shim/index.cjs.js", "import": "./shim/index.modern.js"}, "./nuxt": "./nuxt/module.js"}, "types": "dist/index.d.ts", "engines": {"node": ">=12.16.0"}, "scripts": {"prepublishOnly": "run-s lint lint:ts test build", "build": "run-s 'clean:*' copy 'build:**'", "clean:dist": "del dist", "clean:shim": "del \"shim/*.*\" \"!shim/package.json\"", "copy": "cpy src/*.d.ts dist", "build:ts": "microbundle --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json --format=umd", "build:shim:ts": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.ts -o shim/index.js --format=modern,es --no-sourcemap", "build:shim:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.cjs.js --no-pkg-main --format=cjs --no-sourcemap", "build:shim:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.js --format=umd --no-sourcemap", "test": "jest", "lint": "run-s lint:*", "lint:ts": "tsc --noEmit -p ./examples", "lint:src": "eslint '{examples,src,__tests__,.}/**/*.{ts,js,cjs}'", "examples": "vite examples -c examples/vite.config.js", "docs:dev": "vuepress dev docs", "docs:build": "cross-env NODE_ENV=production vuepress build docs"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.14.5", "@babel/plugin-proposal-optional-chaining": "7.14.5", "@nuxt/types": "2.15.8", "@types/jest": "27.0.2", "@types/node": "14.17.21", "@typescript-eslint/eslint-plugin": "4.33.0", "@typescript-eslint/parser": "4.33.0", "babel-plugin-transform-node-env-inline": "0.4.3", "cpy-cli": "3.1.1", "cross-env": "7.0.3", "del": "6.0.0", "del-cli": "4.0.1", "eslint": "7.32.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "4.0.0", "eslint-plugin-vue": "7.19.1", "jest": "27.2.4", "microbundle": "0.14.0", "npm-run-all": "4.1.5", "prettier": "2.4.1", "ts-jest": "27.0.5", "typescript": "4.4.3", "vite": "2.6.3", "vue2": "npm:vue@2.6.14", "vue3": "npm:vue@3.2.19", "vuepress": "1.8.2"}, "dependencies": {"is-plain-object": "5.0.0"}, "readmeFilename": "README.md", "gitHead": "42329dfdb192fe764928d2aee46b99ad4405f536", "_id": "vue-types@4.1.1-rc.1", "_nodeVersion": "14.18.0", "_npmVersion": "6.14.15", "dist": {"shasum": "b5b486516fdc2fac208ce6e8f3e50fd2d4e4f135", "size": 74063, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-4.1.1-rc.1.tgz", "integrity": "sha512-ctXPKQIz4Rrl8hA6Hp3Av6lMvfc7fo8wkzzRFLtmcOQd8lQjcObUYSZeXCaSAVct5tKpO/cGZTwZktbycdgmiw=="}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_4.1.1-rc.1_1633570864942_0.7240025242760286"}, "_hasShrinkwrap": false, "publish_time": 1633570865114, "_cnpm_publish_time": 1633570865114, "_cnpmcore_publish_time": "2021-12-16T16:04:55.846Z"}, "4.1.1-rc.0": {"name": "vue-types", "version": "4.1.1-rc.0", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"require": "./shim/index.cjs.js", "import": "./shim/index.modern.js"}, "./nuxt": "./nuxt/module.js"}, "types": "dist/index.d.ts", "engines": {"node": ">=12.16.0"}, "scripts": {"prepublishOnly": "run-s lint lint:ts test build", "build": "run-s 'clean:*' copy 'build:**'", "clean:dist": "del dist", "clean:shim": "del \"shim/*.*\" \"!shim/package.json\"", "copy": "cpy src/*.d.ts dist", "build:ts": "microbundle --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json --format=umd", "build:shim:ts": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.ts -o shim/index.js --format=modern,es --no-sourcemap", "build:shim:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.cjs.js --no-pkg-main --format=cjs --no-sourcemap", "build:shim:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.js --format=umd --no-sourcemap", "test": "jest", "lint": "run-s lint:*", "lint:ts": "tsc --noEmit -p ./examples", "lint:src": "eslint '{examples,src,__tests__,.}/**/*.{ts,js,cjs}'", "examples": "vite examples -c examples/vite.config.js", "docs:dev": "vuepress dev docs", "docs:build": "cross-env NODE_ENV=production vuepress build docs"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.14.5", "@babel/plugin-proposal-optional-chaining": "7.14.5", "@nuxt/types": "2.15.8", "@types/jest": "26.0.24", "@types/node": "14.17.9", "@typescript-eslint/eslint-plugin": "4.29.1", "@typescript-eslint/parser": "4.29.1", "babel-plugin-transform-node-env-inline": "0.4.3", "cpy-cli": "3.1.1", "cross-env": "7.0.3", "del": "6.0.0", "del-cli": "4.0.1", "eslint": "7.32.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.4.0", "eslint-plugin-vue": "7.16.0", "jest": "27.0.6", "microbundle": "0.13.3", "npm-run-all": "4.1.5", "prettier": "2.3.2", "ts-jest": "27.0.4", "typescript": "4.3.5", "vite": "2.4.4", "vue2": "npm:vue@2.6.14", "vue3": "npm:vue@3.2.2", "vuepress": "1.8.2"}, "dependencies": {"is-plain-object": "5.0.0"}, "readmeFilename": "README.md", "gitHead": "1fa1191eeb416515378cd1b2771de1abbd0f852a", "_id": "vue-types@4.1.1-rc.0", "_nodeVersion": "14.17.6", "_npmVersion": "6.14.15", "dist": {"shasum": "5217c52a48705831b3d638ad0d7422385c2be943", "size": 74054, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-4.1.1-rc.0.tgz", "integrity": "sha512-6SyIjl307sdUZ33CK+KvxNLalrg3V/en+kzvypL2Dw2MGMEDpbMTwXo6LafJxkebIkBIR+pI+NF7VFmQzpITbQ=="}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_4.1.1-rc.0_1631094655485_0.39888133859841046"}, "_hasShrinkwrap": false, "publish_time": 1631094655639, "_cnpm_publish_time": 1631094655639, "_cnpmcore_publish_time": "2021-12-16T16:04:56.191Z"}, "4.1.0": {"name": "vue-types", "version": "4.1.0", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"require": "./shim/index.cjs.js", "import": "./shim/index.modern.js"}, "./nuxt": "./nuxt/module.js"}, "types": "dist/index.d.ts", "engines": {"node": ">=12.16.0"}, "scripts": {"prepublishOnly": "run-s lint lint:ts test build", "build": "run-s 'clean:*' copy 'build:**'", "clean:dist": "del dist", "clean:shim": "del \"shim/*.*\" \"!shim/package.json\"", "copy": "cpy src/*.d.ts dist", "build:ts": "microbundle --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json --format=umd", "build:shim:ts": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.ts -o shim/index.js --format=modern,es --no-sourcemap", "build:shim:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.cjs.js --no-pkg-main --format=cjs --no-sourcemap", "build:shim:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.js --format=umd --no-sourcemap", "test": "jest", "lint": "run-s lint:*", "lint:ts": "tsc --noEmit -p ./examples", "lint:src": "eslint '{examples,src,__tests__,.}/**/*.{ts,js,cjs}'", "examples": "vite examples -c examples/vite.config.js", "docs:dev": "vuepress dev docs", "docs:build": "cross-env NODE_ENV=production vuepress build docs"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.14.5", "@babel/plugin-proposal-optional-chaining": "7.14.5", "@nuxt/types": "2.15.8", "@types/jest": "26.0.24", "@types/node": "14.17.9", "@typescript-eslint/eslint-plugin": "4.29.1", "@typescript-eslint/parser": "4.29.1", "babel-plugin-transform-node-env-inline": "0.4.3", "cpy-cli": "3.1.1", "cross-env": "7.0.3", "del": "6.0.0", "del-cli": "4.0.1", "eslint": "7.32.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.4.0", "eslint-plugin-vue": "7.16.0", "jest": "27.0.6", "microbundle": "0.13.3", "npm-run-all": "4.1.5", "prettier": "2.3.2", "ts-jest": "27.0.4", "typescript": "4.3.5", "vite": "2.4.4", "vue2": "npm:vue@2.6.14", "vue3": "npm:vue@3.2.2", "vuepress": "1.8.2"}, "dependencies": {"is-plain-object": "5.0.0"}, "gitHead": "20f2869df34939d9290ad2d121ef7828ac854766", "_id": "vue-types@4.1.0", "_nodeVersion": "14.17.6", "_npmVersion": "6.14.15", "dist": {"shasum": "8dcbbaccf9d5c3815449ac7cb8ae5864454cfff0", "size": 74016, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-4.1.0.tgz", "integrity": "sha512-oPAeKKx5vY5Q8c7lMQPQyrBIbmWQGael5XEHqO1f+Y3V/RUZNuISz7KxI4woGjh79Vy/gDDaPX9j9zKYpaaA2g=="}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_4.1.0_1630896030444_0.4734509835794334"}, "_hasShrinkwrap": false, "publish_time": 1630896030651, "_cnpm_publish_time": 1630896030651, "_cnpmcore_publish_time": "2021-12-16T16:04:56.972Z"}, "4.1.0-rc.1": {"name": "vue-types", "version": "4.1.0-rc.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"require": "./shim/index.cjs.js", "import": "./shim/index.modern.js"}, "./nuxt": "./nuxt/module.js"}, "types": "dist/index.d.ts", "engines": {"node": ">=12.16.0"}, "scripts": {"prepublishOnly": "run-s lint lint:ts test build", "build": "run-s 'clean:*' copy 'build:**'", "clean:dist": "del dist", "clean:shim": "del \"shim/*.*\" \"!shim/package.json\"", "copy": "cpy src/*.d.ts dist", "build:ts": "microbundle --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json --format=umd", "build:shim:ts": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.ts -o shim/index.js --format=modern,es --no-sourcemap", "build:shim:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.cjs.js --no-pkg-main --format=cjs --no-sourcemap", "build:shim:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.js --format=umd --no-sourcemap", "test": "jest", "lint": "run-s lint:*", "lint:ts": "tsc --noEmit -p ./examples", "lint:src": "eslint '{examples,src,__tests__,.}/**/*.{ts,js,cjs}'", "examples": "vite examples -c examples/vite.config.js", "docs:dev": "vuepress dev docs", "docs:build": "cross-env NODE_ENV=production vuepress build docs"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.14.5", "@babel/plugin-proposal-optional-chaining": "7.14.5", "@nuxt/types": "2.15.7", "@types/jest": "26.0.24", "@types/node": "14.17.9", "@typescript-eslint/eslint-plugin": "4.29.0", "@typescript-eslint/parser": "4.29.0", "babel-plugin-transform-node-env-inline": "0.4.3", "cpy-cli": "3.1.1", "cross-env": "7.0.3", "del": "6.0.0", "del-cli": "4.0.1", "eslint": "7.32.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.4.0", "eslint-plugin-vue": "7.15.1", "jest": "27.0.6", "microbundle": "0.13.3", "npm-run-all": "4.1.5", "prettier": "2.3.2", "ts-jest": "27.0.4", "typescript": "4.3.5", "vite": "2.4.4", "vue2": "npm:vue@2.6.14", "vue3": "npm:vue@3.1.5", "vuepress": "1.8.2"}, "dependencies": {"is-plain-object": "5.0.0"}, "readmeFilename": "README.md", "gitHead": "c6fb6bbd0bf81d29fb786b11b2304d86c707296d", "_id": "vue-types@4.1.0-rc.1", "_nodeVersion": "14.17.4", "_npmVersion": "6.14.14", "dist": {"shasum": "51b063a4ed182b3b35271d67ad2bb62481ccd949", "size": 73984, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-4.1.0-rc.1.tgz", "integrity": "sha512-29MY1bNpee4cU9y6bfKc8bTMQ0x/MJWl6zEOlnOdBRckPCeuwrGDJxNSsjW/dEwWvZX56wjievLLOWLTkyJU1A=="}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_4.1.0-rc.1_1628476082713_0.6199232039839235"}, "_hasShrinkwrap": false, "publish_time": 1628476082869, "_cnpm_publish_time": 1628476082869, "_cnpmcore_publish_time": "2021-12-16T16:04:57.206Z"}, "4.0.3": {"name": "vue-types", "version": "4.0.3", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"require": "./dist/shim.cjs", "import": "./dist/shim.modern.js"}}, "types": "dist/index.d.ts", "engines": {"node": ">=12.16.0"}, "scripts": {"prepublishOnly": "run-s lint lint:examples test build", "build": "run-s build:clean build:copy build:cjs build:shim:cjs build:ts build:umd", "build:clean": "del dist", "build:copy": "cpy src/*.d.ts dist", "build:ts": "microbundle --external=vue --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:shim:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o dist/shim.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --external=vue --tsconfig=./tsconfig.build.json --format=umd", "test": "jest", "lint": "eslint '{src,__tests__,.}/**/*.{ts,js,cjs}'", "lint:examples": "tsc --noEmit -p ./examples", "examples": "cross-env NODE_ENV=development node ./scripts/run-examples.cjs", "docs:dev": "vuepress dev docs", "docs:build": "cross-env NODE_ENV=production vuepress build docs"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.14.5", "@babel/plugin-proposal-optional-chaining": "7.14.5", "@types/jest": "26.0.24", "@types/node": "14.17.9", "@typescript-eslint/eslint-plugin": "4.29.0", "@typescript-eslint/parser": "4.29.0", "babel-plugin-transform-node-env-inline": "0.4.3", "browser-sync": "2.27.5", "cpy-cli": "3.1.1", "cross-env": "7.0.3", "del": "6.0.0", "del-cli": "4.0.1", "eslint": "7.32.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.4.0", "eslint-plugin-vue": "7.15.1", "jest": "27.0.6", "microbundle": "0.13.3", "npm-run-all": "4.1.5", "prettier": "2.3.2", "ts-jest": "27.0.4", "typescript": "4.3.5", "vue2": "npm:vue@2.6.14", "vue3": "npm:vue@3.1.5", "vuepress": "1.8.2"}, "dependencies": {"is-plain-object": "5.0.0"}, "gitHead": "a85e6ef93ba693726c0de32e564a739c39403f26", "_id": "vue-types@4.0.3", "_nodeVersion": "14.17.4", "_npmVersion": "6.14.14", "dist": {"shasum": "0e0a3b88dd426b39bf161442c573b44ed34e2bb2", "size": 73610, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-4.0.3.tgz", "integrity": "sha512-Yn/TNUaYhkC9ezET5aYJuvxDzPTfEWPIvvRWVjgVAv9vuJG3VTLoTsgJvF4KmdyliBzMum6yoD6uUYVG6245yg=="}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_4.0.3_1628474537588_0.7211494003976104"}, "_hasShrinkwrap": false, "publish_time": 1628474537713, "_cnpm_publish_time": 1628474537713, "_cnpmcore_publish_time": "2021-12-16T16:04:57.510Z"}, "4.0.2": {"name": "vue-types", "version": "4.0.2", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"require": "./dist/shim.cjs", "import": "./dist/shim.modern.js"}}, "types": "dist/index.d.ts", "engines": {"node": ">=12.16.0"}, "scripts": {"prepublishOnly": "run-s lint lint:examples test build", "build": "run-s build:clean build:copy build:cjs build:shim:cjs build:ts build:umd", "build:clean": "del dist", "build:copy": "cpy src/*.d.ts dist", "build:ts": "microbundle --external=vue --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:shim:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o dist/shim.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --external=vue --tsconfig=./tsconfig.build.json --format=umd", "test": "jest", "lint": "eslint '{src,__tests__,.}/**/*.{ts,js,cjs}'", "lint:examples": "tsc --noEmit -p ./examples", "examples": "cross-env NODE_ENV=development node ./scripts/run-examples.cjs", "docs:dev": "vuepress dev docs", "docs:build": "cross-env NODE_ENV=production vuepress build docs"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.14.5", "@babel/plugin-proposal-optional-chaining": "7.14.5", "@types/jest": "26.0.24", "@types/node": "14.17.9", "@typescript-eslint/eslint-plugin": "4.29.0", "@typescript-eslint/parser": "4.29.0", "babel-plugin-transform-node-env-inline": "0.4.3", "browser-sync": "2.27.5", "cpy-cli": "3.1.1", "cross-env": "7.0.3", "del": "6.0.0", "del-cli": "4.0.1", "eslint": "7.32.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.4.0", "eslint-plugin-vue": "7.15.1", "jest": "27.0.6", "microbundle": "0.13.3", "npm-run-all": "4.1.5", "prettier": "2.3.2", "ts-jest": "27.0.4", "typescript": "4.3.5", "vue2": "npm:vue@2.6.14", "vue3": "npm:vue@3.1.5", "vuepress": "1.8.2"}, "dependencies": {"is-plain-object": "5.0.0"}, "gitHead": "1b9258ad1f4fa33eb2c51da702e72a7d9d51c544", "_id": "vue-types@4.0.2", "_nodeVersion": "14.17.4", "_npmVersion": "6.14.14", "dist": {"shasum": "aef6ed1ef92f1347e2e5d3ee05553453b7c86722", "size": 73540, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-4.0.2.tgz", "integrity": "sha512-DimV9K0BPMYCs3iE52L8LzktydLU3DTYLNnWg11l3C5DFIhrQd1m6tP/37glyrFDFM48WdYm7rOOahYnLFdIhA=="}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_4.0.2_1628400221400_0.4626683876224553"}, "_hasShrinkwrap": false, "publish_time": 1628400221544, "_cnpm_publish_time": 1628400221544, "_cnpmcore_publish_time": "2021-12-16T16:04:57.860Z"}, "4.1.0-beta.3": {"name": "vue-types", "version": "4.1.0-beta.3", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"require": "./shim/index.cjs.js", "import": "./shim/index.modern.js"}, "./nuxt": "./nuxt/module.js"}, "types": "dist/index.d.ts", "engines": {"node": ">=12.16.0"}, "scripts": {"prepublishOnly": "run-s lint lint:ts test build", "build": "run-s 'clean:*' copy 'build:**'", "clean:dist": "del dist", "clean:shim": "del \"shim/*.*\" \"!shim/package.json\"", "copy": "cpy src/*.d.ts dist", "build:ts": "microbundle --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json --format=umd", "build:shim:ts": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.ts -o shim/index.js --format=modern,es --no-sourcemap", "build:shim:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.cjs.js --no-pkg-main --format=cjs --no-sourcemap", "build:shim:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.js --format=umd --no-sourcemap", "test": "jest", "lint": "run-s lint:*", "lint:ts": "tsc --noEmit -p ./examples", "lint:src": "eslint '{examples,src,__tests__,.}/**/*.{ts,js,cjs}'", "examples": "vite examples -c examples/vite.config.js", "docs:dev": "vuepress dev docs", "docs:build": "cross-env NODE_ENV=production vuepress build docs"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.14.5", "@babel/plugin-proposal-optional-chaining": "7.14.5", "@nuxt/types": "2.15.7", "@types/jest": "26.0.24", "@types/node": "14.17.5", "@typescript-eslint/eslint-plugin": "4.28.5", "@typescript-eslint/parser": "4.28.5", "babel-plugin-transform-node-env-inline": "0.4.3", "cpy-cli": "3.1.1", "cross-env": "7.0.3", "del": "6.0.0", "del-cli": "4.0.1", "eslint": "7.30.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.4.0", "eslint-plugin-vue": "7.15.0", "jest": "27.0.6", "microbundle": "0.13.3", "npm-run-all": "4.1.5", "prettier": "2.3.2", "ts-jest": "27.0.4", "typescript": "4.3.5", "vite": "2.4.1", "vue2": "npm:vue@2.6.14", "vue3": "npm:vue@3.1.5", "vuepress": "1.8.2"}, "dependencies": {"is-plain-object": "5.0.0"}, "readmeFilename": "README.md", "gitHead": "c4975a06a6a1219d040a25e339b42fcb84e428a3", "_id": "vue-types@4.1.0-beta.3", "_nodeVersion": "14.16.1", "_npmVersion": "6.14.12", "dist": {"shasum": "c9dabf748a079d29317da0bc0f605737389efde2", "size": 74055, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-4.1.0-beta.3.tgz", "integrity": "sha512-IzU2NxeAKJ2KO9QRJE7Uw2GGN0SdlfbCNTjgOveH4v5qd0S1XXQOQOHX6qeWgxskuaEQ0+ISA4GuB0FwG3/4wQ=="}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_4.1.0-beta.3_1628314209222_0.1196877641480194"}, "_hasShrinkwrap": false, "publish_time": 1628314209420, "_cnpm_publish_time": 1628314209420, "_cnpmcore_publish_time": "2021-12-16T16:04:58.544Z"}, "4.0.1": {"name": "vue-types", "version": "4.0.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"require": "./dist/shim.cjs", "import": "./dist/shim.modern.js"}}, "types": "dist/index.d.ts", "engines": {"node": ">=12.16.0"}, "scripts": {"prepublishOnly": "run-s lint lint:examples test build", "build": "run-s build:clean build:copy build:cjs build:shim:cjs build:ts build:umd", "build:clean": "del dist", "build:copy": "cpy src/*.d.ts dist", "build:ts": "microbundle --external=vue --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:shim:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o dist/shim.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --external=vue --tsconfig=./tsconfig.build.json --format=umd", "test": "jest", "lint": "eslint '{src,__tests__,.}/**/*.{ts,js,cjs}'", "lint:examples": "tsc --noEmit -p ./examples", "examples": "cross-env NODE_ENV=development node ./scripts/run-examples.cjs", "docs:dev": "vuepress dev docs", "docs:build": "cross-env NODE_ENV=production vuepress build docs"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.14.5", "@babel/plugin-proposal-optional-chaining": "7.14.5", "@types/jest": "26.0.24", "@types/node": "14.17.5", "@typescript-eslint/eslint-plugin": "4.28.3", "@typescript-eslint/parser": "4.28.3", "babel-plugin-transform-node-env-inline": "0.4.3", "browser-sync": "2.27.4", "cpy-cli": "3.1.1", "cross-env": "7.0.3", "del": "6.0.0", "del-cli": "4.0.0", "eslint": "7.30.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.4.0", "eslint-plugin-vue": "7.13.0", "jest": "27.0.6", "microbundle": "0.13.3", "npm-run-all": "4.1.5", "prettier": "2.3.2", "ts-jest": "27.0.3", "typescript": "4.3.5", "vue2": "npm:vue@2.6.14", "vue3": "npm:vue@3.1.4", "vuepress": "1.8.2"}, "dependencies": {"is-plain-object": "5.0.0"}, "gitHead": "1b972ad16b5313d1efb524dfec3f9537115ec857", "_id": "vue-types@4.0.1", "_nodeVersion": "14.17.3", "_npmVersion": "6.14.13", "dist": {"shasum": "023aff15ac890d7823aacdc23fc4ac8e6c37b290", "size": 73391, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-4.0.1.tgz", "integrity": "sha512-prSJ8K4hBmwdtG9cupF96wDKxtLZqjGpu6Q6R9yakcbRWz1Gf+c4oUMq7j6FwAf0TFkZ77FfU7wgdWLgyZ/9Bw=="}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_4.0.1_1627437816561_0.3693288221302269"}, "_hasShrinkwrap": false, "publish_time": 1627437816846, "_cnpm_publish_time": 1627437816846, "_cnpmcore_publish_time": "2021-12-16T16:04:58.975Z"}, "4.1.0-beta.2": {"name": "vue-types", "version": "4.1.0-beta.2", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"require": "./dist/shim.cjs", "import": "./dist/shim.modern.js"}}, "types": "dist/index.d.ts", "engines": {"node": ">=12.16.0"}, "scripts": {"prepublishOnly": "run-s lint test build", "build": "run-s build:clean build:copy build:cjs build:shim:cjs build:ts build:umd", "build:clean": "del dist", "build:copy": "cpy src/*.d.ts dist", "build:ts": "microbundle --external=vue --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:shim:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o dist/shim.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --external=vue --tsconfig=./tsconfig.build.json --format=umd", "test": "jest", "lint": "run-s lint:*", "lint:examples": "tsc --noEmit -p ./examples", "lint:src": "eslint '{examples,src,__tests__,.}/**/*.{ts,js,cjs}'", "examples": "vite examples -c examples/vite.config.js", "docs:dev": "vuepress dev docs", "docs:build": "cross-env NODE_ENV=production vuepress build docs"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.14.5", "@babel/plugin-proposal-optional-chaining": "7.14.5", "@types/jest": "26.0.23", "@types/node": "14.17.5", "@typescript-eslint/eslint-plugin": "4.28.2", "@typescript-eslint/parser": "4.28.2", "babel-plugin-transform-node-env-inline": "0.4.3", "cpy-cli": "3.1.1", "cross-env": "7.0.3", "del": "6.0.0", "del-cli": "4.0.0", "eslint": "7.30.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.4.0", "eslint-plugin-vue": "7.12.1", "jest": "27.0.6", "microbundle": "0.13.3", "npm-run-all": "4.1.5", "prettier": "2.3.2", "ts-jest": "27.0.3", "typescript": "4.3.5", "vite": "2.4.1", "vue2": "npm:vue@2.6.14", "vue3": "npm:vue@3.1.4", "vuepress": "1.8.2"}, "dependencies": {"is-plain-object": "5.0.0"}, "readmeFilename": "README.md", "gitHead": "e5eb45a512c17742a55a1cf8daedccb6ff2e4b5a", "_id": "vue-types@4.1.0-beta.2", "_nodeVersion": "14.17.3", "_npmVersion": "6.14.13", "dist": {"shasum": "ee3e95d8a9338d0b1d58884ab1ef5647fecc159e", "size": 73889, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-4.1.0-beta.2.tgz", "integrity": "sha512-bcCzqcwVJCk8LqmdyFATMQYEMyLpgAHdujPduRVhU5hiA3sN0zSluUZw3AgYynanl7wzOUn/KVhGPRqI6ZVT1w=="}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_4.1.0-beta.2_1626740517945_0.325768646592417"}, "_hasShrinkwrap": false, "publish_time": 1626740518162, "_cnpm_publish_time": 1626740518162, "_cnpmcore_publish_time": "2021-12-16T16:04:59.269Z"}, "4.1.0-beta.1": {"name": "vue-types", "version": "4.1.0-beta.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"require": "./dist/shim.cjs", "import": "./dist/shim.modern.js"}}, "types": "dist/index.d.ts", "engines": {"node": ">=12.16.0"}, "scripts": {"prepublishOnly": "run-s lint test build", "build": "run-s build:clean build:copy build:cjs build:shim:cjs build:ts build:umd", "build:clean": "del dist", "build:copy": "cpy src/*.d.ts dist", "build:ts": "microbundle --external=vue --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:shim:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o dist/shim.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --external=vue --tsconfig=./tsconfig.build.json --format=umd", "test": "jest", "lint": "run-s lint:*", "lint:examples": "tsc --noEmit -p ./examples", "lint:src": "eslint '{examples,src,__tests__,.}/**/*.{ts,js,cjs}'", "examples": "vite examples -c examples/vite.config.js", "docs:dev": "vuepress dev docs", "docs:build": "cross-env NODE_ENV=production vuepress build docs"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.14.5", "@babel/plugin-proposal-optional-chaining": "7.14.5", "@types/jest": "26.0.23", "@types/node": "14.17.5", "@typescript-eslint/eslint-plugin": "4.28.2", "@typescript-eslint/parser": "4.28.2", "babel-plugin-transform-node-env-inline": "0.4.3", "cpy-cli": "3.1.1", "cross-env": "7.0.3", "del": "6.0.0", "del-cli": "4.0.0", "eslint": "7.30.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.4.0", "eslint-plugin-vue": "7.12.1", "jest": "27.0.6", "microbundle": "0.13.3", "npm-run-all": "4.1.5", "prettier": "2.3.2", "ts-jest": "27.0.3", "typescript": "4.3.5", "vite": "2.4.1", "vue2": "npm:vue@2.6.14", "vue3": "npm:vue@3.1.4", "vuepress": "1.8.2"}, "dependencies": {"is-plain-object": "5.0.0"}, "readmeFilename": "README.md", "gitHead": "b8c199d8a06549242772c3c15874de6c9d81d65b", "_id": "vue-types@4.1.0-beta.1", "_nodeVersion": "14.17.3", "_npmVersion": "6.14.13", "dist": {"shasum": "85e3a2b806ec6297498c980cf71baca754bb7f8e", "size": 72411, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-4.1.0-beta.1.tgz", "integrity": "sha512-ummNTH7e/ZZvuJL6aXswcScPbuksj/XMaCYrHTxgaNhtMf5iqVDURfZ/GMPi5ImUhy13GLhHph4s/0ihuR4byg=="}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_4.1.0-beta.1_1625902249756_0.5077789627106479"}, "_hasShrinkwrap": false, "publish_time": 1625902249873, "_cnpm_publish_time": 1625902249873, "_cnpmcore_publish_time": "2021-12-16T16:04:59.604Z"}, "4.0.0": {"name": "vue-types", "version": "4.0.0", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"require": "./dist/vue-types.cjs", "default": "./dist/vue-types.modern.js"}, "./shim": {"require": "./dist/shim.cjs", "default": "./dist/shim.modern.js"}}, "types": "dist/index.d.ts", "engines": {"node": ">=12.16.0"}, "scripts": {"prepublishOnly": "run-s lint test build", "build": "run-s build:clean build:copy build:cjs build:shim:cjs build:ts build:umd", "build:clean": "del dist", "build:copy": "cpy src/*.d.ts dist", "build:ts": "microbundle --external=vue --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:shim:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o dist/shim.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --external=vue --tsconfig=./tsconfig.build.json --format=umd", "test": "jest", "lint": "eslint '{src,__tests__,.}/**/*.{ts,js,cjs}'", "examples": "cross-env NODE_ENV=development node ./scripts/run-examples.cjs", "docs:dev": "vuepress dev docs", "docs:build": "cross-env NODE_ENV=production vuepress build docs"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.14.2", "@babel/plugin-proposal-optional-chaining": "7.14.2", "@types/jest": "26.0.23", "@types/node": "14.14.37", "@typescript-eslint/eslint-plugin": "4.19.0", "@typescript-eslint/parser": "4.19.0", "babel-plugin-transform-node-env-inline": "0.4.3", "browser-sync": "2.26.14", "cpy-cli": "3.1.1", "cross-env": "7.0.3", "del": "6.0.0", "del-cli": "3.0.1", "eslint": "7.23.0", "eslint-config-prettier": "8.1.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.3.1", "eslint-plugin-vue": "7.8.0", "jest": "27.0.4", "microbundle": "0.13.1", "npm-run-all": "4.1.5", "prettier": "2.2.1", "ts-jest": "27.0.3", "typescript": "4.2.3", "vue2": "npm:vue@2.6.12", "vue3": "npm:vue@3.0.11", "vuepress": "1.8.2"}, "dependencies": {"is-plain-object": "5.0.0"}, "gitHead": "f02772e63e96844b755ba20718f6eea34998fefa", "_id": "vue-types@4.0.0", "_nodeVersion": "14.16.1", "_npmVersion": "6.14.12", "dist": {"shasum": "da13ccca0f979d3cfd076ce3a2b7050a9627ed5a", "size": 71580, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-4.0.0.tgz", "integrity": "sha512-CGJ6br+FhK2OFgPzagVQxf6icQ63OVrOFjw+ekmb4bnTgn/FuKkjdlOqybOdq5aot7BF01yPoTczGD+fEMpKRw=="}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_4.0.0_1624784726756_0.3351748800161931"}, "_hasShrinkwrap": false, "publish_time": 1624784726934, "_cnpm_publish_time": 1624784726934, "_cnpmcore_publish_time": "2021-12-16T16:04:59.866Z"}, "4.0.0-rc.3": {"name": "vue-types", "version": "4.0.0-rc.3", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"require": "./dist/vue-types.cjs", "default": "./dist/vue-types.modern.js"}, "./shim": {"require": "./dist/shim.cjs", "default": "./dist/shim.modern.js"}}, "types": "dist/index.d.ts", "engines": {"node": ">=12.16.0"}, "scripts": {"prepublishOnly": "run-s lint test build", "build": "run-s build:clean build:copy build:cjs build:shim:cjs build:ts build:umd", "build:clean": "del dist", "build:copy": "cpy src/*.d.ts dist", "build:ts": "microbundle --external=vue --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:shim:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o dist/shim.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --external=vue --tsconfig=./tsconfig.build.json --format=umd", "test": "jest", "lint": "eslint  '{src,__tests__,.}/**/*.{ts,js,cjs}'", "examples": "cross-env NODE_ENV=development node ./scripts/run-examples.cjs", "docs:dev": "vuepress dev docs", "docs:build": "cross-env NODE_ENV=production vuepress build docs"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.14.2", "@babel/plugin-proposal-optional-chaining": "7.14.2", "@types/jest": "26.0.23", "@types/node": "14.14.37", "@typescript-eslint/eslint-plugin": "4.19.0", "@typescript-eslint/parser": "4.19.0", "babel-plugin-transform-node-env-inline": "0.4.3", "browser-sync": "2.26.14", "cpy-cli": "3.1.1", "cross-env": "7.0.3", "del": "6.0.0", "del-cli": "3.0.1", "eslint": "7.23.0", "eslint-config-prettier": "8.1.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.3.1", "eslint-plugin-vue": "7.8.0", "jest": "27.0.4", "microbundle": "0.13.1", "npm-run-all": "4.1.5", "prettier": "2.2.1", "ts-jest": "27.0.3", "typescript": "4.2.3", "vue2": "npm:vue@2.6.12", "vue3": "npm:vue@3.0.11", "vuepress": "1.8.2"}, "dependencies": {"is-plain-object": "5.0.0"}, "readmeFilename": "README.md", "gitHead": "7b1d4900a7cb990972d3ef46007aa35053060f96", "_id": "vue-types@4.0.0-rc.3", "_nodeVersion": "14.16.1", "_npmVersion": "6.14.12", "dist": {"shasum": "f11b6783c7747d3dcf1be90f1d1ab366aab992dc", "size": 71579, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-4.0.0-rc.3.tgz", "integrity": "sha512-UhG/e+lejQ7BeOIvCk3/rhGCNEQe1uk4VdgbByJIIrUr42SfhmIdZZubG6a/U+qJwbm0WSIeW0CbkpO5Z5ls8w=="}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_4.0.0-rc.3_1623481462437_0.4724406538957038"}, "_hasShrinkwrap": false, "publish_time": 1623481462674, "_cnpm_publish_time": 1623481462674, "_cnpmcore_publish_time": "2021-12-16T16:05:00.218Z"}, "4.0.0-rc.2": {"name": "vue-types", "version": "4.0.0-rc.2", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/vue-types.js", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "types": "dist/index.d.ts", "engines": {"node": ">=10.15.0"}, "scripts": {"prepublishOnly": "run-s lint test build", "build": "run-s build:clean build:copy build:cjs build:shim:cjs build:ts build:umd", "build:clean": "del dist", "build:copy": "cpy src/*.d.ts dist", "build:ts": "microbundle --external=vue --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.js --no-pkg-main --format=cjs", "build:shim:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o dist/shim.js --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --external=vue --tsconfig=./tsconfig.build.json --format=umd", "test": "karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:saucelab": "karma start karma-sauce.conf.js --single-run", "lint": "eslint  '{src,test,.}/**/*.{ts,js}'", "examples": "cross-env NODE_ENV=development node ./scripts/run-examples.js", "docs:dev": "vuepress dev docs", "docs:build": "cross-env NODE_ENV=production vuepress build docs"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.13.8", "@babel/plugin-proposal-optional-chaining": "7.13.12", "@types/jasmine": "3.6.9", "@types/node": "14.14.37", "@typescript-eslint/eslint-plugin": "4.19.0", "@typescript-eslint/parser": "4.19.0", "babel-plugin-transform-node-env-inline": "0.4.3", "browser-sync": "2.26.14", "core-js": "3.9.1", "core-js-bundle": "3.9.1", "cpy-cli": "3.1.1", "cross-env": "7.0.3", "del": "6.0.0", "del-cli": "3.0.1", "eslint": "7.23.0", "eslint-config-prettier": "8.1.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.3.1", "eslint-plugin-vue": "7.8.0", "jasmine": "3.7.0", "karma": "6.3.1", "karma-chrome-launcher": "3.1.0", "karma-jasmine": "4.0.1", "karma-sauce-launcher": "4.3.5", "karma-typescript": "5.5.1", "karma-typescript-es6-transform": "5.5.1", "microbundle": "0.13.0", "npm-run-all": "4.1.5", "prettier": "2.2.1", "puppeteer": "8.0.0", "typescript": "4.2.3", "vue2": "npm:vue@2.6.12", "vue3": "npm:vue@3.0.11", "vue-class-component": "7.2.6", "vuepress": "1.8.2"}, "dependencies": {"is-plain-object": "5.0.0"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "readmeFilename": "README.md", "gitHead": "61807ea484d2171093c06774821c70b573e1edb3", "_id": "vue-types@4.0.0-rc.2", "_nodeVersion": "14.15.5", "_npmVersion": "6.14.11", "dist": {"shasum": "dc5c462ce5f84b589cb8104a2db0c8d14e2fd1c9", "size": 70859, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-4.0.0-rc.2.tgz", "integrity": "sha512-4CHAlDjEwFWwsu6HG3FwFsi2irrC/fitJ+rygfn5qYpx19WJNL0P2GV7uPPh5Qw8jVHx0CQtrMM6TcTe48eoYw=="}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_4.0.0-rc.2_1618469144628_0.6076828026203647"}, "_hasShrinkwrap": false, "publish_time": 1618469144786, "_cnpm_publish_time": 1618469144786, "_cnpmcore_publish_time": "2021-12-16T16:05:00.680Z"}, "4.0.0-rc.1": {"name": "vue-types", "version": "4.0.0-rc.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/vue-types.js", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "types": "dist/index.d.ts", "engines": {"node": ">=10.15.0"}, "scripts": {"prepublishOnly": "run-s lint test build", "build": "run-s build:clean build:copy build:cjs build:shim:cjs build:ts build:umd", "build:clean": "del dist", "build:copy": "cpy src/*.d.ts dist", "build:ts": "microbundle --external=vue --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.js --no-pkg-main --format=cjs", "build:shim:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o dist/shim.js --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --external=vue --tsconfig=./tsconfig.build.json --format=umd", "test": "karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:saucelab": "karma start karma-sauce.conf.js --single-run", "lint": "eslint  '{src,test,.}/**/*.{ts,js}'", "examples": "cross-env NODE_ENV=development node ./scripts/run-examples.js", "docs:dev": "vuepress dev docs", "docs:build": "cross-env NODE_ENV=production vuepress build docs"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 | ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.13.8", "@babel/plugin-proposal-optional-chaining": "7.13.12", "@types/jasmine": "3.6.9", "@types/node": "14.14.37", "@typescript-eslint/eslint-plugin": "4.19.0", "@typescript-eslint/parser": "4.19.0", "babel-plugin-transform-node-env-inline": "0.4.3", "browser-sync": "2.26.14", "core-js": "3.9.1", "core-js-bundle": "3.9.1", "cpy-cli": "3.1.1", "cross-env": "7.0.3", "del": "6.0.0", "del-cli": "3.0.1", "eslint": "7.23.0", "eslint-config-prettier": "8.1.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.3.1", "eslint-plugin-vue": "7.8.0", "jasmine": "3.7.0", "karma": "6.3.1", "karma-chrome-launcher": "3.1.0", "karma-jasmine": "4.0.1", "karma-sauce-launcher": "4.3.5", "karma-typescript": "5.5.1", "karma-typescript-es6-transform": "5.5.1", "microbundle": "0.13.0", "npm-run-all": "4.1.5", "prettier": "2.2.1", "puppeteer": "8.0.0", "typescript": "4.2.3", "vue2": "npm:vue@2.6.12", "vue3": "npm:vue@3.0.11", "vue-class-component": "7.2.6", "vuepress": "1.8.2"}, "dependencies": {"is-plain-object": "5.0.0"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "readmeFilename": "README.md", "gitHead": "7a1dd524e890b232a1ca3f7ee7f14b1ebdef77a7", "_id": "vue-types@4.0.0-rc.1", "_nodeVersion": "14.15.5", "_npmVersion": "6.14.11", "dist": {"shasum": "eba1d51776892fb0a1d77d668d676fe8507a8488", "size": 70009, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-4.0.0-rc.1.tgz", "integrity": "sha512-+LRvu6Kr0sqlHA6qsS461fWVEmoFlyc69KruZAN+FUz1eonuC5x28Vsmm7ae6xaDzbUX5ncXkDSo3ikVtMahkQ=="}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_4.0.0-rc.1_1618466025181_0.804363301131152"}, "_hasShrinkwrap": false, "publish_time": 1618466025325, "_cnpm_publish_time": 1618466025325, "_cnpmcore_publish_time": "2021-12-16T16:05:01.040Z"}, "3.0.2": {"name": "vue-types", "version": "3.0.2", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/vue-types.js", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "types": "dist/index.d.ts", "engines": {"node": ">=10.15.0"}, "scripts": {"prepublishOnly": "run-s lint test build", "build": "run-s build:clean build:copy build:cjs build:shim:cjs build:ts build:umd", "build:clean": "del dist", "build:copy": "cpy src/*.d.ts dist", "build:ts": "microbundle --external=vue --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.js --no-pkg-main --format=cjs", "build:shim:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o dist/shim.js --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --external=vue --tsconfig=./tsconfig.build.json --format=umd", "test": "karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:saucelab": "karma start karma-sauce.conf.js --single-run", "lint": "eslint  '{src,test,.}/**/*.{ts,js}'", "examples": "cross-env NODE_ENV=development node ./scripts/run-examples.js", "docs:dev": "vuepress dev docs", "docs:build": "cross-env NODE_ENV=production vuepress build docs"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.10.4", "@babel/plugin-proposal-optional-chaining": "7.10.4", "@types/jasmine": "3.5.11", "@types/node": "14.0.23", "@typescript-eslint/eslint-plugin": "3.6.1", "@typescript-eslint/parser": "3.6.1", "babel-plugin-transform-node-env-inline": "0.4.3", "browser-sync": "2.26.12", "core-js": "3.6.5", "core-js-bundle": "3.6.5", "cpy-cli": "3.1.1", "cross-env": "7.0.2", "del": "5.1.0", "del-cli": "3.0.1", "eslint": "7.4.0", "eslint-config-prettier": "6.11.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-vue": "6.2.2", "jasmine": "3.5.0", "karma": "5.1.0", "karma-chrome-launcher": "3.1.0", "karma-jasmine": "3.3.1", "karma-sauce-launcher": "4.1.4", "karma-typescript": "5.0.3", "karma-typescript-es6-transform": "5.0.3", "microbundle": "0.12.3", "npm-run-all": "4.1.5", "prettier": "2.0.5", "puppeteer": "5.1.0", "typescript": "3.9.6", "vue": "3.0.0-rc.7", "vue-class-component": "7.2.3", "vuepress": "1.5.2"}, "dependencies": {"is-plain-object": "3.0.1"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "readmeFilename": "README.md", "gitHead": "cf8140564af060aaf2c0e7fd753eccddd33c2b62", "_id": "vue-types@3.0.2", "_nodeVersion": "12.16.0", "_npmVersion": "6.13.4", "dist": {"shasum": "ec16e05d412c038262fc1efa4ceb9647e7fb601d", "size": 70211, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-3.0.2.tgz", "integrity": "sha512-IwUC0Aq2zwaXqy74h4WCvFCUtoV0iSWr0snWnE9TnU18S66GAQyqQbRf2qfJtUuiFsBf6qp0MEwdonlwznlcrw=="}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_3.0.2_1612247085707_0.1836584431052648"}, "_hasShrinkwrap": false, "publish_time": 1612247085895, "_cnpm_publish_time": 1612247085895, "_cnpmcore_publish_time": "2021-12-16T16:05:01.348Z"}, "2.0.3": {"name": "vue-types", "version": "2.0.3", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/vue-types.js", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "types": "dist/index.d.ts", "engines": {"node": ">=10.15.0"}, "scripts": {"prepublishOnly": "run-s lint test build", "build": "run-s build:clean build:copy build:cjs build:shim:cjs build:ts build:umd", "build:clean": "del dist", "build:copy": "cpy src/*.d.ts dist", "build:ts": "microbundle --external=vue --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.js --no-pkg-main --format=cjs", "build:shim:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o dist/shim.js --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --external=vue --tsconfig=./tsconfig.build.json --format=umd", "test": "karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:saucelab": "karma start karma-sauce.conf.js --single-run", "lint": "eslint  '{src,test,.}/**/*.{ts,js}'", "examples": "cross-env NODE_ENV=development node ./scripts/run-examples.js", "docs:dev": "vuepress dev docs", "docs:build": "cross-env NODE_ENV=production vuepress build docs"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.10.4", "@babel/plugin-proposal-optional-chaining": "7.10.4", "@types/jasmine": "3.5.11", "@types/node": "14.0.23", "@typescript-eslint/eslint-plugin": "3.6.1", "@typescript-eslint/parser": "3.6.1", "babel-plugin-transform-node-env-inline": "0.4.3", "browser-sync": "2.26.12", "core-js": "3.6.5", "core-js-bundle": "3.6.5", "cpy-cli": "3.1.1", "cross-env": "7.0.2", "del": "5.1.0", "del-cli": "3.0.1", "eslint": "7.4.0", "eslint-config-prettier": "6.11.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-vue": "6.2.2", "jasmine": "3.5.0", "karma": "5.1.0", "karma-chrome-launcher": "3.1.0", "karma-jasmine": "3.3.1", "karma-sauce-launcher": "4.1.4", "karma-typescript": "5.0.3", "karma-typescript-es6-transform": "5.0.3", "microbundle": "0.12.3", "npm-run-all": "4.1.5", "prettier": "2.0.5", "puppeteer": "5.1.0", "typescript": "3.9.6", "vue": "^2.0.0", "vue-class-component": "7.2.3", "vuepress": "1.5.2"}, "dependencies": {"is-plain-object": "3.0.1"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "gitHead": "f29af9533d478b792a059ac69e3f7c27568de840", "_id": "vue-types@2.0.3", "_nodeVersion": "12.16.0", "_npmVersion": "6.13.4", "dist": {"shasum": "d4d3b124e8ae09e2d58785054451a6eb7e672d6d", "size": 70808, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-2.0.3.tgz", "integrity": "sha512-pLTOquc/x5uvVLZBjEKwC8e3eni17wv9M/2XdYbDQ/93pk3vW6eXvoynMw0ACBvHad7GgQ92F+D+C7d7e5msBw=="}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_2.0.3_1612246585991_0.09707722786402884"}, "_hasShrinkwrap": false, "publish_time": 1612246586198, "_cnpm_publish_time": 1612246586198, "_cnpmcore_publish_time": "2021-12-16T16:05:01.558Z"}, "3.0.1": {"name": "vue-types", "version": "3.0.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/vue-types.js", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "types": "dist/index.d.ts", "engines": {"node": ">=10.15.0"}, "scripts": {"prepublishOnly": "run-s lint test build", "build": "run-s build:clean build:copy build:cjs build:shim:cjs build:ts build:umd", "build:clean": "del dist", "build:copy": "cpy src/*.d.ts dist", "build:ts": "microbundle --external=vue --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.js --no-pkg-main --format=cjs", "build:shim:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o dist/shim.js --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --external=vue --tsconfig=./tsconfig.build.json --format=umd", "test": "karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:saucelab": "karma start karma-sauce.conf.js --single-run", "lint": "eslint  '{src,test,.}/**/*.{ts,js}'", "examples": "cross-env NODE_ENV=development node ./scripts/run-examples.js", "docs:dev": "vuepress dev docs", "docs:build": "cross-env NODE_ENV=production vuepress build docs"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.10.4", "@babel/plugin-proposal-optional-chaining": "7.10.4", "@types/jasmine": "3.5.11", "@types/node": "14.0.23", "@typescript-eslint/eslint-plugin": "3.6.1", "@typescript-eslint/parser": "3.6.1", "babel-plugin-transform-node-env-inline": "0.4.3", "browser-sync": "2.26.12", "core-js": "3.6.5", "core-js-bundle": "3.6.5", "cpy-cli": "3.1.1", "cross-env": "7.0.2", "del": "5.1.0", "del-cli": "3.0.1", "eslint": "7.4.0", "eslint-config-prettier": "6.11.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-vue": "6.2.2", "jasmine": "3.5.0", "karma": "5.1.0", "karma-chrome-launcher": "3.1.0", "karma-jasmine": "3.3.1", "karma-sauce-launcher": "4.1.4", "karma-typescript": "5.0.3", "karma-typescript-es6-transform": "5.0.3", "microbundle": "0.12.3", "npm-run-all": "4.1.5", "prettier": "2.0.5", "puppeteer": "5.1.0", "typescript": "3.9.6", "vue": "3.0.0-rc.7", "vue-class-component": "7.2.3", "vuepress": "1.5.2"}, "dependencies": {"is-plain-object": "3.0.1"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "readmeFilename": "README.md", "gitHead": "e9c1f12c01a284744f4e72ae2a32ea64c4e5919e", "_id": "vue-types@3.0.1", "_nodeVersion": "12.16.3", "_npmVersion": "6.14.8", "dist": {"shasum": "20e9baae8673de8093d0a989234695d08d544be0", "size": 70369, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-3.0.1.tgz", "integrity": "sha512-UbvbzPu8DNzZRfMB1RDTFKBB6seMm80scMFdP+GkKaw00EugC3cjq9AtlS4y258vDkpAe9HfqbRO4cp63qVHXQ=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_3.0.1_1602323257683_0.4080029024302294"}, "_hasShrinkwrap": false, "publish_time": 1602323257885, "_cnpm_publish_time": 1602323257885, "_cnpmcore_publish_time": "2021-12-16T16:05:02.039Z"}, "2.0.2": {"name": "vue-types", "version": "2.0.2", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/vue-types.js", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "types": "dist/index.d.ts", "engines": {"node": ">=10.15.0"}, "scripts": {"prepublishOnly": "run-s lint test build", "build": "run-s build:clean build:copy build:cjs build:shim:cjs build:ts build:umd", "build:clean": "del dist", "build:copy": "cpy src/*.d.ts dist", "build:ts": "microbundle --external=vue --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.js --no-pkg-main --format=cjs", "build:shim:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o dist/shim.js --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --external=vue --tsconfig=./tsconfig.build.json --format=umd", "test": "karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:saucelab": "karma start karma-sauce.conf.js --single-run", "lint": "eslint  '{src,test,.}/**/*.{ts,js}'", "examples": "cross-env NODE_ENV=development node ./scripts/run-examples.js", "docs:dev": "vuepress dev docs", "docs:build": "cross-env NODE_ENV=production vuepress build docs"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.10.4", "@babel/plugin-proposal-optional-chaining": "7.10.4", "@types/jasmine": "3.5.11", "@types/node": "14.0.23", "@typescript-eslint/eslint-plugin": "3.6.1", "@typescript-eslint/parser": "3.6.1", "babel-plugin-transform-node-env-inline": "0.4.3", "browser-sync": "2.26.12", "core-js": "3.6.5", "core-js-bundle": "3.6.5", "cpy-cli": "3.1.1", "cross-env": "7.0.2", "del": "5.1.0", "del-cli": "3.0.1", "eslint": "7.4.0", "eslint-config-prettier": "6.11.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-vue": "6.2.2", "jasmine": "3.5.0", "karma": "5.1.0", "karma-chrome-launcher": "3.1.0", "karma-jasmine": "3.3.1", "karma-sauce-launcher": "4.1.4", "karma-typescript": "5.0.3", "karma-typescript-es6-transform": "5.0.3", "microbundle": "0.12.3", "npm-run-all": "4.1.5", "prettier": "2.0.5", "puppeteer": "5.1.0", "typescript": "3.9.6", "vue": "^2.0.0", "vue-class-component": "7.2.3", "vuepress": "1.5.2"}, "dependencies": {"is-plain-object": "3.0.1"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "gitHead": "cb547a44650227fca88db312833e3fc50b6cfeb0", "_id": "vue-types@2.0.2", "_nodeVersion": "12.16.3", "_npmVersion": "6.14.8", "dist": {"shasum": "a995ba3f22f6a1a81bd1d04096a548fcea97e11e", "size": 70826, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-2.0.2.tgz", "integrity": "sha512-YXhDIGS//wc8NAGnMP6CTZ4AkU8PNEUzJ3hXAD+pKzdce+ea6JIfsqY3EWhzJopWPmlfyooTGHLZ7w1Ze/sRzQ=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_2.0.2_1602322182550_0.6023759541481237"}, "_hasShrinkwrap": false, "publish_time": 1602322182700, "_cnpm_publish_time": 1602322182700, "_cnpmcore_publish_time": "2021-12-16T16:05:02.331Z"}, "3.0.0": {"name": "vue-types", "version": "3.0.0", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/vue-types.js", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "types": "dist/index.d.ts", "engines": {"node": ">=10.15.0"}, "scripts": {"prepublishOnly": "run-s lint test build", "build": "run-s build:clean build:copy build:cjs build:shim:cjs build:ts build:umd", "build:clean": "del dist", "build:copy": "cpy src/*.d.ts dist", "build:ts": "microbundle --external=vue --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.js --no-pkg-main --format=cjs", "build:shim:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o dist/shim.js --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --external=vue --tsconfig=./tsconfig.build.json --format=umd", "test": "karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:saucelab": "karma start karma-sauce.conf.js --single-run", "lint": "eslint  '{src,test,.}/**/*.{ts,js}'", "examples": "cross-env NODE_ENV=development node ./scripts/run-examples.js", "docs:dev": "vuepress dev docs", "docs:build": "cross-env NODE_ENV=production vuepress build docs"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.10.4", "@babel/plugin-proposal-optional-chaining": "7.10.4", "@types/jasmine": "3.5.11", "@types/node": "14.0.23", "@typescript-eslint/eslint-plugin": "3.6.1", "@typescript-eslint/parser": "3.6.1", "babel-plugin-transform-node-env-inline": "0.4.3", "browser-sync": "2.26.12", "core-js": "3.6.5", "core-js-bundle": "3.6.5", "cpy-cli": "3.1.1", "cross-env": "7.0.2", "del": "5.1.0", "del-cli": "3.0.1", "eslint": "7.4.0", "eslint-config-prettier": "6.11.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-vue": "6.2.2", "jasmine": "3.5.0", "karma": "5.1.0", "karma-chrome-launcher": "3.1.0", "karma-jasmine": "3.3.1", "karma-sauce-launcher": "4.1.4", "karma-typescript": "5.0.3", "karma-typescript-es6-transform": "5.0.3", "microbundle": "0.12.3", "npm-run-all": "4.1.5", "prettier": "2.0.5", "puppeteer": "5.1.0", "typescript": "3.9.6", "vue": "3.0.0-rc.7", "vue-class-component": "7.2.3", "vuepress": "1.5.2"}, "dependencies": {"is-plain-object": "3.0.1"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "readmeFilename": "README.md", "gitHead": "6f1ffc00828c018fe2fa3dfd05c18ccaadac9123", "_id": "vue-types@3.0.0", "_nodeVersion": "12.16.3", "_npmVersion": "6.14.4", "dist": {"shasum": "68d11b0af6a5906e3cb0c5f0aa1008c8af4fd827", "size": 70235, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-3.0.0.tgz", "integrity": "sha512-xzq2D7zLGbzn3bPQ0CWWYx4W78JWdGYZAFfRkzxz1aTm9DaNoH0qxYDiLXBofBEQ5X78l32uIq/G9INQ2E8X/Q=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_3.0.0_1599362453182_0.07992470387265604"}, "_hasShrinkwrap": false, "publish_time": 1599362453313, "_cnpm_publish_time": 1599362453313, "_cnpmcore_publish_time": "2021-12-16T16:05:02.893Z"}, "2.0.1": {"name": "vue-types", "version": "2.0.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/vue-types.js", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "types": "dist/index.d.ts", "engines": {"node": ">=10.15.0"}, "scripts": {"prepublishOnly": "run-s lint test build", "build": "run-s build:clean build:copy build:cjs build:shim:cjs build:ts build:umd", "build:clean": "del dist", "build:copy": "cpy src/*.d.ts dist", "build:ts": "microbundle --external=vue --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.js --no-pkg-main --format=cjs", "build:shim:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o dist/shim.js --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --external=vue --tsconfig=./tsconfig.build.json --format=umd", "test": "karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:saucelab": "karma start karma-sauce.conf.js --single-run", "lint": "eslint  '{src,test,.}/**/*.{ts,js}'", "examples": "cross-env NODE_ENV=development node ./scripts/run-examples.js", "docs:dev": "vuepress dev docs", "docs:build": "cross-env NODE_ENV=production vuepress build docs"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.10.4", "@babel/plugin-proposal-optional-chaining": "7.10.4", "@types/jasmine": "3.5.11", "@types/node": "14.0.23", "@typescript-eslint/eslint-plugin": "3.6.1", "@typescript-eslint/parser": "3.6.1", "babel-plugin-transform-node-env-inline": "0.4.3", "browser-sync": "2.26.12", "core-js": "3.6.5", "cpy-cli": "3.1.1", "cross-env": "7.0.2", "del": "5.1.0", "del-cli": "3.0.1", "eslint": "7.4.0", "eslint-config-prettier": "6.11.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-vue": "6.2.2", "jasmine": "3.5.0", "karma": "5.1.0", "karma-chrome-launcher": "3.1.0", "karma-jasmine": "3.3.1", "karma-sauce-launcher": "4.1.5", "karma-typescript": "5.0.3", "karma-typescript-es6-transform": "5.0.3", "microbundle": "0.12.3", "npm-run-all": "4.1.5", "prettier": "2.0.5", "puppeteer": "5.1.0", "typescript": "3.9.6", "vue": "^2.0.0", "vue-class-component": "7.2.3", "vuepress": "1.5.2"}, "dependencies": {"is-plain-object": "3.0.1"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "gitHead": "584227f25a0df2fb670902bcf1831c5c5a485f40", "_id": "vue-types@2.0.1", "_nodeVersion": "12.16.0", "_npmVersion": "6.13.4", "dist": {"shasum": "bb17bf0f407a77d92eef4382be198e0d2f175682", "size": 70729, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-2.0.1.tgz", "integrity": "sha512-aagbujhnqfTvIa16Ncc38EQRsSTuTWvbMYrzscblJ74G8ghfg0p/+LM732ZsTnl6pQUe5THpZ5Cko0hCBfYy5A=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_2.0.1_1597380268539_0.7727617769038209"}, "_hasShrinkwrap": false, "publish_time": 1597380268718, "_cnpm_publish_time": 1597380268718, "_cnpmcore_publish_time": "2021-12-16T16:05:03.315Z"}, "2.0.1-rc.1": {"name": "vue-types", "version": "2.0.1-rc.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/vue-types.js", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "types": "dist/index.d.ts", "engines": {"node": ">=10.15.0"}, "scripts": {"prepublishOnly": "run-s lint test build", "build": "run-s build:clean build:copy build:cjs build:shim:cjs build:ts build:umd", "build:clean": "del dist", "build:copy": "cpy src/*.d.ts dist", "build:ts": "microbundle --external=vue --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.js --no-pkg-main --format=cjs", "build:shim:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o dist/shim.js --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --external=vue --tsconfig=./tsconfig.build.json --format=umd", "test": "karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:saucelab": "karma start karma-sauce.conf.js --single-run", "lint": "eslint  '{src,test,.}/**/*.{ts,js}'", "examples": "cross-env NODE_ENV=development node ./scripts/run-examples.js", "docs:dev": "vuepress dev docs", "docs:build": "cross-env NODE_ENV=production vuepress build docs"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.10.4", "@babel/plugin-proposal-optional-chaining": "7.10.4", "@types/jasmine": "3.5.11", "@types/node": "14.0.23", "@typescript-eslint/eslint-plugin": "3.6.1", "@typescript-eslint/parser": "3.6.1", "babel-plugin-transform-node-env-inline": "0.4.3", "browser-sync": "2.26.12", "core-js": "3.6.5", "cpy-cli": "3.1.1", "cross-env": "7.0.2", "del": "5.1.0", "del-cli": "3.0.1", "eslint": "7.4.0", "eslint-config-prettier": "6.11.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-vue": "6.2.2", "jasmine": "3.5.0", "karma": "5.1.0", "karma-chrome-launcher": "3.1.0", "karma-jasmine": "3.3.1", "karma-sauce-launcher": "4.1.5", "karma-typescript": "5.0.3", "karma-typescript-es6-transform": "5.0.3", "microbundle": "0.12.3", "npm-run-all": "4.1.5", "prettier": "2.0.5", "puppeteer": "5.1.0", "typescript": "3.9.6", "vue": "^2.0.0", "vue-class-component": "7.2.3", "vuepress": "1.5.2"}, "dependencies": {"is-plain-object": "3.0.1"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "readmeFilename": "README.md", "gitHead": "a8549889806b0cfde065e1dd7016c15316a5891c", "_id": "vue-types@2.0.1-rc.1", "_nodeVersion": "12.16.0", "_npmVersion": "6.13.4", "dist": {"shasum": "dee4dc2db72a7816c8aae69f9b9a939a367800af", "size": 70732, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-2.0.1-rc.1.tgz", "integrity": "sha512-d2oDkEqMSoI/eVlVp21lYDrxPaPAHS+dUt39zRAbXcATYOw7SWCaxtAvMxyYKzEgdM/3+brW8Ryi2ZMZzqqhSA=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_2.0.1-rc.1_1597321420040_0.46813850382644384"}, "_hasShrinkwrap": false, "publish_time": 1597321420180, "_cnpm_publish_time": 1597321420180, "_cnpmcore_publish_time": "2021-12-16T16:05:03.870Z"}, "2.0.0": {"name": "vue-types", "version": "2.0.0", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/vue-types.js", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "types": "dist/index.d.ts", "engines": {"node": ">=10.15.0"}, "scripts": {"prepublishOnly": "run-s lint test build", "build": "run-s build:clean build:copy build:ts build:umd", "build:clean": "del dist", "build:copy": "cpy src/*.d.ts dist", "build:ts": "microbundle --external=vue --tsconfig=./tsconfig.build.json --format=modern,es,cjs", "build:umd": "cross-env NODE_ENV=production microbundle --external=vue --tsconfig=./tsconfig.build.json --format=umd", "test": "karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:saucelab": "karma start karma-sauce.conf.js --single-run", "lint": "eslint src/*.ts test/*.ts", "examples": "cross-env NODE_ENV=development node ./scripts/run-examples.js", "docs:dev": "vuepress dev docs", "docs:build": "cross-env NODE_ENV=production vuepress build docs"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.10.4", "@babel/plugin-proposal-optional-chaining": "7.10.4", "@types/jasmine": "3.5.11", "@types/node": "14.0.23", "@typescript-eslint/eslint-plugin": "3.6.1", "@typescript-eslint/parser": "3.6.1", "babel-plugin-transform-node-env-inline": "0.4.3", "browser-sync": "2.26.12", "core-js": "3.6.5", "cpy-cli": "3.1.1", "cross-env": "7.0.2", "del": "5.1.0", "del-cli": "3.0.1", "eslint": "7.4.0", "eslint-config-prettier": "6.11.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-vue": "6.2.2", "jasmine": "3.5.0", "karma": "5.1.0", "karma-chrome-launcher": "3.1.0", "karma-jasmine": "3.3.1", "karma-sauce-launcher": "4.1.5", "karma-typescript": "5.0.3", "karma-typescript-es6-transform": "5.0.3", "microbundle": "0.12.3", "npm-run-all": "4.1.5", "prettier": "2.0.5", "puppeteer": "5.1.0", "typescript": "3.9.6", "vue": "^2.0.0", "vue-class-component": "7.2.3", "vuepress": "1.5.2"}, "dependencies": {"is-plain-object": "3.0.1"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "gitHead": "0adf67bed9580f1e8afb2a7f160cf04fb1f643f2", "_id": "vue-types@2.0.0", "_nodeVersion": "12.16.3", "_npmVersion": "6.14.4", "dist": {"shasum": "74d05c8964621762004092b9fe63eb9bcb7fdf6d", "size": 69905, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-2.0.0.tgz", "integrity": "sha512-GYQfidsC6uyKIkGscyOxJV0S1cLdmzt/ELEeSL3kmCe7+ZDhQDPC1jLIStnZUPUZY1+hcM/NBpI6/13P1o+2DA=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_2.0.0_1596965833607_0.5818698073502979"}, "_hasShrinkwrap": false, "publish_time": 1596965833801, "_cnpm_publish_time": 1596965833801, "_cnpmcore_publish_time": "2021-12-16T16:05:04.454Z"}, "2.0.0-rc.1": {"name": "vue-types", "version": "2.0.0-rc.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/vue-types.js", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "types": "dist/index.d.ts", "engines": {"node": ">=10.15.0"}, "scripts": {"prepublishOnly": "run-s lint test build", "build": "run-s build:clean build:copy build:ts build:umd", "build:clean": "del dist", "build:copy": "cpy src/*.d.ts dist", "build:ts": "microbundle --external=vue --tsconfig=./tsconfig.build.json --format=modern,es,cjs", "build:umd": "cross-env NODE_ENV=production microbundle --external=vue --tsconfig=./tsconfig.build.json --format=umd", "test": "karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:saucelab": "karma start karma-sauce.conf.js --single-run", "lint": "eslint src/*.ts test/*.ts", "build:examples": "cross-env NODE_ENV=production node ./scripts/build-examples.js", "serve": "sirv examples", "examples": "run-s build:examples serve", "docs:dev": "vuepress dev docs", "docs:build": "cross-env NODE_ENV=production vuepress build docs"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.10.4", "@babel/plugin-proposal-optional-chaining": "7.10.4", "@types/jasmine": "3.5.11", "@types/node": "14.0.23", "@typescript-eslint/eslint-plugin": "3.6.1", "@typescript-eslint/parser": "3.6.1", "babel-plugin-transform-node-env-inline": "0.4.3", "core-js": "3.6.5", "cpy-cli": "3.1.1", "cross-env": "7.0.2", "del": "5.1.0", "del-cli": "3.0.1", "eslint": "7.4.0", "eslint-config-prettier": "6.11.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-vue": "6.2.2", "jasmine": "3.5.0", "karma": "5.1.0", "karma-chrome-launcher": "3.1.0", "karma-jasmine": "3.3.1", "karma-sauce-launcher": "4.1.5", "karma-typescript": "5.0.3", "karma-typescript-es6-transform": "5.0.3", "microbundle": "0.12.3", "npm-run-all": "4.1.5", "prettier": "2.0.5", "puppeteer": "5.1.0", "sirv-cli": "1.0.3", "typescript": "3.9.6", "vue": "^2.0.0", "vue-class-component": "7.2.3", "vuepress": "1.5.2"}, "dependencies": {"is-plain-object": "3.0.1"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "readmeFilename": "README.md", "gitHead": "b88c3cb1e6cac8747abf84827efa853ebcc5d2b7", "_id": "vue-types@2.0.0-rc.1", "_nodeVersion": "12.16.3", "_npmVersion": "6.14.4", "dist": {"shasum": "3afe23110b23210aa9ae573c0ff46a060e256b4b", "size": 75189, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-2.0.0-rc.1.tgz", "integrity": "sha512-TjzivrTx76hgYWfbUY7uf6Cr3p/WBJQcIiAjY1fPmVaUKLCMngFDBx42Bs+Isbm6qHXHgwj/3t+AHg0zpJ8pzw=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_2.0.0-rc.1_1596259687810_0.7446919295059837"}, "_hasShrinkwrap": false, "publish_time": 1596259687969, "_cnpm_publish_time": 1596259687969, "_cnpmcore_publish_time": "2021-12-16T16:05:05.055Z"}, "2.0.0-rc.0": {"name": "vue-types", "version": "2.0.0-rc.0", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/vue-types.js", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "types": "dist/index.d.ts", "engines": {"node": ">=10.15.0"}, "scripts": {"prepublishOnly": "run-s lint test build", "build": "run-s build:clean build:copy build:ts build:umd", "build:clean": "del dist", "build:copy": "cpy src/*.d.ts dist", "build:ts": "microbundle --external=vue --tsconfig=./tsconfig.build.json --format=modern,es,cjs", "build:umd": "cross-env NODE_ENV=production microbundle --external=vue --tsconfig=./tsconfig.build.json --format=umd", "test": "karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:saucelab": "karma start karma-sauce.conf.js --single-run", "lint": "eslint src/*.ts test/*.ts", "build:examples": "cross-env NODE_ENV=production node ./scripts/build-examples.js", "serve": "sirv examples", "examples": "run-s build:examples serve", "docs:dev": "vuepress dev docs", "docs:build": "vuepress build docs"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.10.4", "@babel/plugin-proposal-optional-chaining": "7.10.4", "@types/jasmine": "3.5.11", "@types/node": "14.0.23", "@typescript-eslint/eslint-plugin": "3.6.1", "@typescript-eslint/parser": "3.6.1", "babel-plugin-transform-node-env-inline": "0.4.3", "core-js": "3.6.5", "cpy-cli": "3.1.1", "cross-env": "7.0.2", "del": "5.1.0", "del-cli": "3.0.1", "eslint": "7.4.0", "eslint-config-prettier": "6.11.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-vue": "6.2.2", "jasmine": "3.5.0", "karma": "5.1.0", "karma-chrome-launcher": "3.1.0", "karma-jasmine": "3.3.1", "karma-sauce-launcher": "4.1.5", "karma-typescript": "5.0.3", "karma-typescript-es6-transform": "5.0.3", "microbundle": "0.12.3", "npm-run-all": "4.1.5", "prettier": "2.0.5", "puppeteer": "5.1.0", "sirv-cli": "1.0.3", "typescript": "3.9.6", "vue": "^2.0.0", "vue-class-component": "7.2.3", "vuepress": "1.5.2"}, "dependencies": {"is-plain-object": "3.0.1"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "readmeFilename": "README.md", "gitHead": "72d03a75e96c3a6afa587d8ccf6983fb9084d506", "_id": "vue-types@2.0.0-rc.0", "_nodeVersion": "12.16.0", "_npmVersion": "6.13.4", "dist": {"shasum": "c745e833f0c82b5f9578b0559b95df3209dabd1a", "size": 73512, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-2.0.0-rc.0.tgz", "integrity": "sha512-kgfX2/MmoEMVBjGL+HqPdMbVcRyDndXkiCMsMuhCr6wnTI+ynY2nFV6TqYaWi7t09/hT8n7kXAsYA7d47C4pzA=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_2.0.0-rc.0_1594802249517_0.9704293540113356"}, "_hasShrinkwrap": false, "publish_time": 1594802249666, "_cnpm_publish_time": 1594802249666, "_cnpmcore_publish_time": "2021-12-16T16:05:05.624Z"}, "1.8.1-beta.2": {"name": "vue-types", "version": "1.8.1-beta.2", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.cjs.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "typings": "types/index.d.ts", "engines": {"node": ">=10.15.0"}, "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm run test:types && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "test:types": "tsc --noEmit -p ./types/test/", "lint": "eslint src/*.js test/*.js types/**/*.ts types/**/*.d.ts", "start": "rollup -c ./examples/rollup.config.js -w"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "*"}, "devDependencies": {"@babel/cli": "7.5.0", "@babel/core": "7.5.0", "@babel/preset-env": "7.5.0", "@types/node": "12.0.12", "@typescript-eslint/eslint-plugin": "1.11.0", "@typescript-eslint/parser": "1.11.0", "core-js": "3.1.4", "cross-env": "5.2.0", "eslint": "^5.16.0", "eslint-config-prettier": "6.0.0", "eslint-plugin-node": "9.1.0", "eslint-plugin-prettier": "3.1.0", "eslint-plugin-vue": "5.2.3", "expect": "1.20.2", "karma": "4.1.0", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-rollup-preprocessor": "7.0.0", "karma-sauce-launcher": "2.0.2", "mocha": "6.1.4", "prettier": "1.18.2", "puppeteer": "1.18.1", "rimraf": "2.6.3", "rollup": "1.18.0", "rollup-plugin-alias": "1.5.2", "rollup-plugin-babel": "4.3.3", "rollup-plugin-commonjs": "10.0.2", "rollup-plugin-filesize": "6.1.1", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.4.0", "rollup-plugin-node-resolve": "5.2.0", "rollup-plugin-replace": "2.2.0", "rollup-plugin-serve": "1.0.1", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "6.0.2", "typescript": "3.5.2", "vue": "~2.6.10", "vue-class-component": "~7.1.0"}, "dependencies": {"is-plain-object": "3.0.0"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "readmeFilename": "README.md", "gitHead": "ace49faf01ef0795e31e5d0e8b5e449edafd221d", "_id": "vue-types@1.8.1-beta.2", "_nodeVersion": "12.16.0", "_npmVersion": "6.13.4", "dist": {"shasum": "ba271a227d93680174f2d68862faf65a77c6b7f4", "size": 45110, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.8.1-beta.2.tgz", "integrity": "sha512-l0T0eE4FAxecpX9DoFx0qBybF1R+F/tjeMM53DvMLYjidgRFGy9fc8MDxaYL8mtiyWWw3wRpKPpfNuX5Rx/Hhw=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_1.8.1-beta.2_1593067455305_0.886166950956325"}, "_hasShrinkwrap": false, "publish_time": 1593067455503, "_cnpm_publish_time": 1593067455503, "_cnpmcore_publish_time": "2021-12-16T16:05:06.037Z"}, "1.8.1-beta.1": {"name": "vue-types", "version": "1.8.1-beta.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.cjs.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "typings": "types/index.d.ts", "engines": {"node": ">=10.15.0"}, "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm run test:types && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "test:types": "tsc --noEmit -p ./types/test/", "lint": "eslint src/*.js test/*.js types/**/*.ts types/**/*.d.ts", "start": "rollup -c ./examples/rollup.config.js -w"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "*"}, "devDependencies": {"@babel/cli": "7.5.0", "@babel/core": "7.5.0", "@babel/preset-env": "7.5.0", "@types/node": "12.0.12", "@typescript-eslint/eslint-plugin": "1.11.0", "@typescript-eslint/parser": "1.11.0", "core-js": "3.1.4", "cross-env": "5.2.0", "eslint": "^5.16.0", "eslint-config-prettier": "6.0.0", "eslint-plugin-node": "9.1.0", "eslint-plugin-prettier": "3.1.0", "eslint-plugin-vue": "5.2.3", "expect": "1.20.2", "karma": "4.1.0", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-rollup-preprocessor": "7.0.0", "karma-sauce-launcher": "2.0.2", "mocha": "6.1.4", "prettier": "1.18.2", "puppeteer": "1.18.1", "rimraf": "2.6.3", "rollup": "1.18.0", "rollup-plugin-alias": "1.5.2", "rollup-plugin-babel": "4.3.3", "rollup-plugin-commonjs": "10.0.2", "rollup-plugin-filesize": "6.1.1", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.4.0", "rollup-plugin-node-resolve": "5.2.0", "rollup-plugin-replace": "2.2.0", "rollup-plugin-serve": "1.0.1", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "6.0.2", "typescript": "3.5.2", "vue": "~2.6.10", "vue-class-component": "~7.1.0"}, "dependencies": {"is-plain-object": "3.0.0"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "readmeFilename": "README.md", "gitHead": "5f26b693baa76275f2724ccf0f3853d0e5c1536e", "_id": "vue-types@1.8.1-beta.1", "_nodeVersion": "12.16.0", "_npmVersion": "6.13.4", "dist": {"shasum": "c40168d6f26de32b653b6c06b04983552651795b", "size": 45064, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.8.1-beta.1.tgz", "integrity": "sha512-nn6R+GOTq1NRVuZYl9JvvYkPXLltsAuNEa6Wl5F7t86Ns3sOulnoHaoyXqbdtOhE6QY4r8HV8hHIiq0Ywx4N7A=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_1.8.1-beta.1_1593065369919_0.8695066263189544"}, "_hasShrinkwrap": false, "publish_time": 1593065370208, "_cnpm_publish_time": 1593065370208, "_cnpmcore_publish_time": "2021-12-16T16:05:06.666Z"}, "2.0.0-alpha.1": {"name": "vue-types", "version": "2.0.0-alpha.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/vue-types.js", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "types": "dist/index.d.ts", "engines": {"node": ">=10.15.0"}, "scripts": {"prepublishOnly": "npm run build", "build": "run-s build:clean build:ts build:umd", "build:clean": "del dist", "build:ts": "microbundle --external=vue --tsconfig=./tsconfig.build.json --format=modern,esm,cjs", "build:umd": "cross-env NODE_ENV=production microbundle --external=vue --tsconfig=./tsconfig.build.json --format=umd", "test": "karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:saucelab": "karma start karma-sauce.conf.js --single-run", "lint": "eslint src/*.ts test/*.ts", "build:examples": "node ./scripts/build-examples.js", "serve": "sirv", "examples": "run-s build:examples serve"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "*"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.10.1", "@babel/plugin-proposal-optional-chaining": "7.10.1", "@types/mocha": "7.0.2", "@types/node": "13.13.4", "@typescript-eslint/eslint-plugin": "2.30.0", "@typescript-eslint/parser": "2.30.0", "babel-plugin-transform-node-env-inline": "0.4.3", "core-js": "3.6.5", "cross-env": "7.0.2", "del": "5.1.0", "del-cli": "3.0.1", "eslint": "6.8.0", "eslint-config-prettier": "6.11.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.1.3", "eslint-plugin-vue": "6.2.2", "expect": "1.20.2", "karma": "5.0.4", "karma-chrome-launcher": "3.1.0", "karma-mocha": "2.0.1", "karma-mocha-reporter": "2.2.5", "karma-sauce-launcher": "4.1.4", "karma-typescript": "5.0.3", "karma-typescript-es6-transform": "5.0.3", "microbundle": "0.12.0", "mocha": "7.1.2", "npm-run-all": "4.1.5", "prettier": "2.0.5", "puppeteer": "3.0.2", "sirv-cli": "1.0.0", "typescript": "3.9.5", "vue": "2.6.11", "vue-class-component": "7.2.3"}, "dependencies": {"is-plain-object": "3.0.0"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "readmeFilename": "README.md", "gitHead": "a96092ba33a9b9820dfc62745b1915172dc29f48", "_id": "vue-types@2.0.0-alpha.1", "_nodeVersion": "12.16.0", "_npmVersion": "6.13.4", "dist": {"shasum": "c7c6719ee48cac26add3f9f29c5825a8a48e0797", "size": 56700, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-2.0.0-alpha.1.tgz", "integrity": "sha512-/Q2SPOsXxcVU/PCZeHWLflxj1h1NH9f9Ub5A7WMIvDkeKLZBUDQq9TGGEpmOiacgl6D/1W8zavhwByjcHG6whA=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_2.0.0-alpha.1_1592203032470_0.35459872031483264"}, "_hasShrinkwrap": false, "publish_time": 1592203032673, "_cnpm_publish_time": 1592203032673, "_cnpmcore_publish_time": "2021-12-16T16:05:07.093Z"}, "1.7.0": {"name": "vue-types", "version": "1.7.0", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.cjs.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "typings": "types/index.d.ts", "engines": {"node": ">=10.15.0"}, "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm run test:types && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "test:types": "tsc --noEmit -p ./types/test/", "lint": "eslint src/*.js test/*.js types/**/*.ts types/**/*.d.ts", "start": "rollup -c ./examples/rollup.config.js -w"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "*"}, "devDependencies": {"@babel/cli": "7.5.0", "@babel/core": "7.5.0", "@babel/preset-env": "7.5.0", "@types/node": "12.0.12", "@typescript-eslint/eslint-plugin": "1.11.0", "@typescript-eslint/parser": "1.11.0", "core-js": "3.1.4", "cross-env": "5.2.0", "eslint": "^5.16.0", "eslint-config-prettier": "6.0.0", "eslint-plugin-node": "9.1.0", "eslint-plugin-prettier": "3.1.0", "eslint-plugin-vue": "5.2.3", "expect": "1.20.2", "karma": "4.1.0", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-rollup-preprocessor": "7.0.0", "karma-sauce-launcher": "2.0.2", "mocha": "6.1.4", "prettier": "1.18.2", "puppeteer": "1.18.1", "rimraf": "2.6.3", "rollup": "1.18.0", "rollup-plugin-alias": "1.5.2", "rollup-plugin-babel": "4.3.3", "rollup-plugin-commonjs": "10.0.2", "rollup-plugin-filesize": "6.1.1", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.4.0", "rollup-plugin-node-resolve": "5.2.0", "rollup-plugin-replace": "2.2.0", "rollup-plugin-serve": "1.0.1", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "6.0.2", "typescript": "3.5.2", "vue": "~2.6.10", "vue-class-component": "~7.1.0"}, "dependencies": {"is-plain-object": "3.0.0"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "gitHead": "97a7536691129f0fed00a2d1ca38156b1af03c7f", "_id": "vue-types@1.7.0", "_nodeVersion": "10.15.3", "_npmVersion": "6.11.3", "dist": {"shasum": "07b273342aba7cc43d9460d9c4f4a4cf5eeb00b1", "size": 43852, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.7.0.tgz", "integrity": "sha512-<PERSON><PERSON><PERSON><PERSON>ukmcBJHdfXAdLerbP29i3j2HAJoKwEv7No2Jh/YNl9dOKsNBfvMOkf0SS8VCuWd1/JBX4NnGaGUojbkUA=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_1.7.0_1574747667222_0.7454419352729633"}, "_hasShrinkwrap": false, "publish_time": 1574747667367, "_cnpm_publish_time": 1574747667367, "_cnpmcore_publish_time": "2021-12-16T16:05:07.399Z"}, "1.6.2": {"name": "vue-types", "version": "1.6.2", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.cjs.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "typings": "types/index.d.ts", "engines": {"node": ">=10.15.0"}, "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm run test:types && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "test:types": "tsc --noEmit -p ./types/test/", "lint": "eslint src/*.js test/*.js types/**/*.ts types/**/*.d.ts", "start": "rollup -c ./examples/rollup.config.js -w"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "*"}, "devDependencies": {"@babel/cli": "7.5.0", "@babel/core": "7.5.0", "@babel/preset-env": "7.5.0", "@types/node": "12.0.12", "@typescript-eslint/eslint-plugin": "1.11.0", "@typescript-eslint/parser": "1.11.0", "core-js": "3.1.4", "cross-env": "5.2.0", "eslint": "^5.16.0", "eslint-config-prettier": "6.0.0", "eslint-plugin-node": "9.1.0", "eslint-plugin-prettier": "3.1.0", "eslint-plugin-vue": "5.2.3", "expect": "1.20.2", "karma": "4.1.0", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-rollup-preprocessor": "7.0.0", "karma-sauce-launcher": "2.0.2", "mocha": "6.1.4", "prettier": "1.18.2", "puppeteer": "1.18.1", "rimraf": "2.6.3", "rollup": "1.18.0", "rollup-plugin-alias": "1.5.2", "rollup-plugin-babel": "4.3.3", "rollup-plugin-commonjs": "10.0.2", "rollup-plugin-filesize": "6.1.1", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.4.0", "rollup-plugin-node-resolve": "5.2.0", "rollup-plugin-replace": "2.2.0", "rollup-plugin-serve": "1.0.1", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "6.0.2", "typescript": "3.5.2", "vue": "~2.6.10", "vue-class-component": "~7.1.0"}, "dependencies": {"is-plain-object": "3.0.0"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "gitHead": "03c39321632844388acf24c02281148079eb493b", "_id": "vue-types@1.6.2", "_nodeVersion": "10.15.3", "_npmVersion": "6.11.3", "dist": {"shasum": "2931b000134a8e1e7f02b02bca1ab4f8f94f7004", "size": 40197, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.6.2.tgz", "integrity": "sha512-C6/YZH8u/Gffw75RYRvAujmSP6U+QhIOlbX6M0ao0y38ZXqzy2njUlWQlmVMbkEaQeZvbHq0ViBg+2FdwnJBbA=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_1.6.2_1573532005243_0.590822464041078"}, "_hasShrinkwrap": false, "publish_time": 1573532005420, "_cnpm_publish_time": 1573532005420, "_cnpmcore_publish_time": "2021-12-16T16:05:07.732Z"}, "1.6.1": {"name": "vue-types", "version": "1.6.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.cjs.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "typings": "types/index.d.ts", "engines": {"node": ">=10.15.0"}, "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm run test:types && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "test:types": "tsc --noEmit -p ./types/test/", "lint": "eslint src/*.js test/*.js types/**/*.ts types/**/*.d.ts", "start": "rollup -c ./examples/rollup.config.js -w"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "*"}, "devDependencies": {"@babel/cli": "7.5.0", "@babel/core": "7.5.0", "@babel/preset-env": "7.5.0", "@types/node": "12.0.12", "@typescript-eslint/eslint-plugin": "1.11.0", "@typescript-eslint/parser": "1.11.0", "core-js": "3.1.4", "cross-env": "5.2.0", "eslint": "^5.16.0", "eslint-config-prettier": "6.0.0", "eslint-plugin-node": "9.1.0", "eslint-plugin-prettier": "3.1.0", "eslint-plugin-vue": "5.2.3", "expect": "1.20.2", "karma": "4.1.0", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-rollup-preprocessor": "7.0.0", "karma-sauce-launcher": "2.0.2", "mocha": "6.1.4", "prettier": "1.18.2", "puppeteer": "1.18.1", "rimraf": "2.6.3", "rollup": "1.18.0", "rollup-plugin-alias": "1.5.2", "rollup-plugin-babel": "4.3.3", "rollup-plugin-commonjs": "10.0.2", "rollup-plugin-filesize": "6.1.1", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.4.0", "rollup-plugin-node-resolve": "5.2.0", "rollup-plugin-replace": "2.2.0", "rollup-plugin-serve": "1.0.1", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "6.0.2", "typescript": "3.5.2", "vue": "~2.6.10", "vue-class-component": "~7.1.0"}, "dependencies": {"is-plain-object": "3.0.0"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "gitHead": "b1235764c25e5174196a46edb811a56b41978c42", "_id": "vue-types@1.6.1", "_nodeVersion": "10.15.3", "_npmVersion": "6.11.3", "dist": {"shasum": "ea92701ead0f55ce99b7d5f67e496d9040681fad", "size": 40200, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.6.1.tgz", "integrity": "sha512-6k5toqXki5YM4r3TD07U+wLqgb7PAplmUGe9Y9Ww1WYr5i0iHkwBATHr1eGPp2i/J4DBpCCzGy5hNS06sBPSmA=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_1.6.1_1573197159157_0.9224425707517019"}, "_hasShrinkwrap": false, "publish_time": 1573197159351, "_cnpm_publish_time": 1573197159351, "_cnpmcore_publish_time": "2021-12-16T16:05:07.996Z"}, "1.6.1-beta.1": {"name": "vue-types", "version": "1.6.1-beta.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.cjs.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "typings": "types/index.d.ts", "engines": {"node": ">=10.15.0"}, "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm run test:types && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "test:types": "tsc --noEmit -p ./types/test/", "lint": "eslint src/*.js test/*.js types/**/*.ts types/**/*.d.ts", "start": "rollup -c ./examples/rollup.config.js -w"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "*"}, "devDependencies": {"@babel/cli": "7.5.0", "@babel/core": "7.5.0", "@babel/preset-env": "7.5.0", "@types/node": "12.0.12", "@typescript-eslint/eslint-plugin": "1.11.0", "@typescript-eslint/parser": "1.11.0", "core-js": "3.1.4", "cross-env": "5.2.0", "eslint": "^5.16.0", "eslint-config-prettier": "6.0.0", "eslint-plugin-node": "9.1.0", "eslint-plugin-prettier": "3.1.0", "eslint-plugin-vue": "5.2.3", "expect": "1.20.2", "karma": "4.1.0", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-rollup-preprocessor": "7.0.0", "karma-sauce-launcher": "2.0.2", "mocha": "6.1.4", "prettier": "1.18.2", "puppeteer": "1.18.1", "rimraf": "2.6.3", "rollup": "1.18.0", "rollup-plugin-alias": "1.5.2", "rollup-plugin-babel": "4.3.3", "rollup-plugin-commonjs": "10.0.2", "rollup-plugin-filesize": "6.1.1", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.4.0", "rollup-plugin-node-resolve": "5.2.0", "rollup-plugin-replace": "2.2.0", "rollup-plugin-serve": "1.0.1", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "6.0.2", "typescript": "3.5.2", "vue": "~2.6.10", "vue-class-component": "~7.1.0"}, "dependencies": {"is-plain-object": "3.0.0"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "readmeFilename": "README.md", "gitHead": "2e7458f400a177a3aadcb809b568d6846288ad01", "_id": "vue-types@1.6.1-beta.1", "_nodeVersion": "10.15.3", "_npmVersion": "6.11.3", "dist": {"shasum": "b064c7dc4e9ebd08a7a469e376b5df822888fe48", "size": 40203, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.6.1-beta.1.tgz", "integrity": "sha512-OCTOfF3pBN9VUncCK+G5pUZF9iesmyEMSiDyOQV+IriDIgdqe939yIndh79olbBEPk+0sr9TOr56QZwOzUgJoA=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_1.6.1-beta.1_1573187600289_0.6874151188829722"}, "_hasShrinkwrap": false, "publish_time": 1573187600392, "_cnpm_publish_time": 1573187600392, "_cnpmcore_publish_time": "2021-12-16T16:05:08.328Z"}, "1.7.0-beta.3": {"name": "vue-types", "version": "1.7.0-beta.3", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.cjs.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "typings": "types/index.d.ts", "engines": {"node": ">=10.15.0"}, "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm run test:types && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "test:types": "tsc --noEmit -p ./types/test/", "lint": "eslint src/*.js test/*.js types/**/*.ts types/**/*.d.ts", "start": "rollup -c ./examples/rollup.config.js -w"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "*"}, "devDependencies": {"@babel/cli": "7.5.0", "@babel/core": "7.5.0", "@babel/preset-env": "7.5.0", "@types/node": "12.0.12", "@typescript-eslint/eslint-plugin": "1.11.0", "@typescript-eslint/parser": "1.11.0", "core-js": "3.1.4", "cross-env": "5.2.0", "eslint": "^5.16.0", "eslint-config-prettier": "6.0.0", "eslint-plugin-node": "9.1.0", "eslint-plugin-prettier": "3.1.0", "eslint-plugin-vue": "5.2.3", "expect": "1.20.2", "karma": "4.1.0", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-rollup-preprocessor": "7.0.0", "karma-sauce-launcher": "2.0.2", "mocha": "6.1.4", "prettier": "1.18.2", "puppeteer": "1.18.1", "rimraf": "2.6.3", "rollup": "1.18.0", "rollup-plugin-alias": "1.5.2", "rollup-plugin-babel": "4.3.3", "rollup-plugin-commonjs": "10.0.2", "rollup-plugin-filesize": "6.1.1", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.4.0", "rollup-plugin-node-resolve": "5.2.0", "rollup-plugin-replace": "2.2.0", "rollup-plugin-serve": "1.0.1", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "6.0.2", "typescript": "3.5.2", "vue": "~2.6.10", "vue-class-component": "~7.1.0"}, "dependencies": {"is-plain-object": "3.0.0"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "readmeFilename": "README.md", "gitHead": "079538d3d8c63b25d9f2504f3973af75fb5d344d", "_id": "vue-types@1.7.0-beta.3", "_nodeVersion": "10.15.3", "_npmVersion": "6.11.3", "dist": {"shasum": "412e67dd1db3a31c777435edb00afac55122661f", "size": 42960, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.7.0-beta.3.tgz", "integrity": "sha512-IdhQHsC6t83vuhO5BKSXcg7T2bZiS4BAitBUgGFiZZaWKO/DDYYzJPrVT0wSe6udTCwXwGJ0v6YJRVqAjM1ywA=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_1.7.0-beta.3_1571805288677_0.27408856483134647"}, "_hasShrinkwrap": false, "publish_time": 1571805288858, "_cnpm_publish_time": 1571805288858, "_cnpmcore_publish_time": "2021-12-16T16:05:09.048Z"}, "1.7.0-beta.1": {"name": "vue-types", "version": "1.7.0-beta.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.cjs.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "typings": "types/index.d.ts", "engines": {"node": ">=10.15.0"}, "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm run test:types && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "test:types": "tsc --noEmit -p ./types/test/", "lint": "eslint src/*.js test/*.js types/**/*.ts types/**/*.d.ts", "start": "rollup -c ./examples/rollup.config.js -w"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "*"}, "devDependencies": {"@babel/cli": "7.5.0", "@babel/core": "7.5.0", "@babel/preset-env": "7.5.0", "@types/node": "12.0.12", "@typescript-eslint/eslint-plugin": "1.11.0", "@typescript-eslint/parser": "1.11.0", "core-js": "3.1.4", "cross-env": "5.2.0", "eslint": "^5.16.0", "eslint-config-prettier": "6.0.0", "eslint-plugin-node": "9.1.0", "eslint-plugin-prettier": "3.1.0", "eslint-plugin-vue": "5.2.3", "expect": "1.20.2", "karma": "4.1.0", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-rollup-preprocessor": "7.0.0", "karma-sauce-launcher": "2.0.2", "mocha": "6.1.4", "prettier": "1.18.2", "puppeteer": "1.18.1", "rimraf": "2.6.3", "rollup": "1.18.0", "rollup-plugin-alias": "1.5.2", "rollup-plugin-babel": "4.3.3", "rollup-plugin-commonjs": "10.0.2", "rollup-plugin-filesize": "6.1.1", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.4.0", "rollup-plugin-node-resolve": "5.2.0", "rollup-plugin-replace": "2.2.0", "rollup-plugin-serve": "1.0.1", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "6.0.2", "typescript": "3.5.2", "vue": "~2.6.10", "vue-class-component": "~7.1.0"}, "dependencies": {"is-plain-object": "3.0.0"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "readmeFilename": "README.md", "gitHead": "4d47c9a1611981e32ab6daf81017a8ba3f953704", "_id": "vue-types@1.7.0-beta.1", "_nodeVersion": "10.15.3", "_npmVersion": "6.11.2", "dist": {"shasum": "389c67186782ddd4cd14181edf03255239265830", "size": 42318, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.7.0-beta.1.tgz", "integrity": "sha512-TGRoY5TffU0J/tlHnOy4pNEuoXc1j/y8znjAC6T0FiVYnorGD0DiIGc0GWPQuV1gzZgMtttnHfoXH1QgtFNnVg=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_1.7.0-beta.1_1567399389517_0.9690993127212169"}, "_hasShrinkwrap": false, "publish_time": 1567399389779, "_cnpm_publish_time": 1567399389779, "_cnpmcore_publish_time": "2021-12-16T16:05:09.417Z"}, "1.6.0": {"name": "vue-types", "version": "1.6.0", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.cjs.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "typings": "types/index.d.ts", "engines": {"node": ">=10.15.0"}, "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm run test:types && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "test:types": "tsc --noEmit -p ./types/test/", "lint": "eslint src/*.js test/*.js types/**/*.ts types/**/*.d.ts", "start": "rollup -c ./examples/rollup.config.js -w"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "*"}, "devDependencies": {"@babel/cli": "7.5.0", "@babel/core": "7.5.0", "@babel/preset-env": "7.5.0", "@types/node": "12.0.12", "@typescript-eslint/eslint-plugin": "1.11.0", "@typescript-eslint/parser": "1.11.0", "core-js": "3.1.4", "cross-env": "5.2.0", "eslint": "^5.16.0", "eslint-config-prettier": "6.0.0", "eslint-plugin-node": "9.1.0", "eslint-plugin-prettier": "3.1.0", "eslint-plugin-vue": "5.2.3", "expect": "1.20.2", "karma": "4.1.0", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-rollup-preprocessor": "7.0.0", "karma-sauce-launcher": "2.0.2", "mocha": "6.1.4", "prettier": "1.18.2", "puppeteer": "1.18.1", "rimraf": "2.6.3", "rollup": "1.18.0", "rollup-plugin-alias": "1.5.2", "rollup-plugin-babel": "4.3.3", "rollup-plugin-commonjs": "10.0.2", "rollup-plugin-filesize": "6.1.1", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.4.0", "rollup-plugin-node-resolve": "5.2.0", "rollup-plugin-replace": "2.2.0", "rollup-plugin-serve": "1.0.1", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "6.0.2", "typescript": "3.5.2", "vue": "~2.6.10", "vue-class-component": "~7.1.0"}, "dependencies": {"is-plain-object": "3.0.0"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "gitHead": "cbc16f4df6fcd411b2c4e393ef1cd9881bad84ba", "_id": "vue-types@1.6.0", "_nodeVersion": "10.15.3", "_npmVersion": "6.10.1", "dist": {"shasum": "d803cd9755f4991717de9f8d222d654e9ce06da7", "size": 39658, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.6.0.tgz", "integrity": "sha512-G8j5tAgD+yGbq42+T33jfYO+bSEI6qIrIkx1NFLwE8GAE23kEeuXMVEhX8cuSA0O+YR+WRb3+RQEMcz5HUgITQ=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_1.6.0_1564984897101_0.6999829588439099"}, "_hasShrinkwrap": false, "publish_time": 1564984897307, "_cnpm_publish_time": 1564984897307, "_cnpmcore_publish_time": "2021-12-16T16:05:09.691Z"}, "1.5.7": {"name": "vue-types", "version": "1.5.7", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.cjs.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "typings": "types/index.d.ts", "engines": {"node": ">=10.15.0"}, "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm run test:types && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "test:types": "tsc --noEmit -p ./types/test/", "lint": "eslint src/*.js test/*.js types/**/*.ts types/**/*.d.ts", "start": "rollup -c ./examples/rollup.config.js -w", "snyk-protect": "snyk protect", "prepublish": "npm run snyk-protect"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "*"}, "devDependencies": {"@babel/cli": "7.5.0", "@babel/core": "7.5.0", "@babel/preset-env": "7.5.0", "@types/lodash": "4.14.135", "@types/node": "12.0.12", "@typescript-eslint/eslint-plugin": "1.11.0", "@typescript-eslint/parser": "1.11.0", "core-js": "3.1.4", "cross-env": "5.2.0", "eslint": "^5.16.0", "eslint-config-prettier": "6.0.0", "eslint-plugin-node": "9.1.0", "eslint-plugin-prettier": "3.1.0", "eslint-plugin-vue": "5.2.3", "expect": "1.20.2", "karma": "4.1.0", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-rollup-preprocessor": "7.0.0", "karma-sauce-launcher": "2.0.2", "mocha": "6.1.4", "prettier": "1.18.2", "puppeteer": "1.18.1", "rimraf": "2.6.3", "rollup": "1.16.6", "rollup-plugin-alias": "1.5.2", "rollup-plugin-babel": "4.3.3", "rollup-plugin-commonjs": "10.0.1", "rollup-plugin-filesize": "6.1.1", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.4.0", "rollup-plugin-node-resolve": "5.2.0", "rollup-plugin-replace": "2.2.0", "rollup-plugin-serve": "1.0.1", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "6.0.2", "typescript": "3.5.2", "vue": "~2.6.10", "vue-class-component": "~7.1.0"}, "dependencies": {"lodash": "^4.17.10", "snyk": "^1.192.0"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "snyk": true, "gitHead": "b73b64312fd60ce5c6ff17575f8c2151f4df72b9", "_id": "vue-types@1.5.7", "_nodeVersion": "10.15.3", "_npmVersion": "6.10.1", "dist": {"shasum": "72508bbaab45b1c959491d58d1737181f9ddc956", "size": 42682, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.5.7.tgz", "integrity": "sha512-JibheLfDfqKCrLyCAdVDkGS3SKdBxegyH5Yr2tznANj3WVUqwZPnoVbRd6d9Sww55zpvh9QzKApuQr44OifNXA=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_1.5.7_1563344328449_0.5104120297616077"}, "_hasShrinkwrap": false, "publish_time": 1563344328660, "_cnpm_publish_time": 1563344328660, "_cnpmcore_publish_time": "2021-12-16T16:05:10.191Z"}, "1.5.6": {"name": "vue-types", "version": "1.5.6", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.cjs.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "typings": "types/index.d.ts", "engines": {"node": ">=10.15.0"}, "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm run test:types && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "test:types": "tsc --noEmit -p ./types/test/", "lint": "eslint src/*.js test/*.js types/**/*.ts types/**/*.d.ts", "start": "rollup -c ./examples/rollup.config.js -w", "snyk-protect": "snyk protect", "prepublish": "npm run snyk-protect"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "*"}, "devDependencies": {"@babel/cli": "7.5.0", "@babel/core": "7.5.0", "@babel/preset-env": "7.5.0", "@types/lodash": "4.14.135", "@types/node": "12.0.12", "@typescript-eslint/eslint-plugin": "1.11.0", "@typescript-eslint/parser": "1.11.0", "core-js": "3.1.4", "cross-env": "5.2.0", "eslint": "^5.16.0", "eslint-config-prettier": "6.0.0", "eslint-plugin-node": "9.1.0", "eslint-plugin-prettier": "3.1.0", "eslint-plugin-vue": "5.2.3", "expect": "1.20.2", "karma": "4.1.0", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-rollup-preprocessor": "7.0.0", "karma-sauce-launcher": "2.0.2", "mocha": "6.1.4", "prettier": "1.18.2", "puppeteer": "1.18.1", "rimraf": "2.6.3", "rollup": "1.16.6", "rollup-plugin-alias": "1.5.2", "rollup-plugin-babel": "4.3.3", "rollup-plugin-commonjs": "10.0.1", "rollup-plugin-filesize": "6.1.1", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.4.0", "rollup-plugin-node-resolve": "5.2.0", "rollup-plugin-replace": "2.2.0", "rollup-plugin-serve": "1.0.1", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "6.0.2", "typescript": "3.5.2", "vue": "~2.6.10", "vue-class-component": "~7.1.0"}, "dependencies": {"lodash": "^4.17.10", "snyk": "^1.192.0"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "snyk": true, "gitHead": "1ddb47c6247645a650314119649ce4223eac95a3", "_id": "vue-types@1.5.6", "_nodeVersion": "10.16.0", "_npmVersion": "6.9.0", "dist": {"shasum": "721e89b5a4ba1b2fe199cce2eae4e48629e50920", "size": 41117, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.5.6.tgz", "integrity": "sha512-kYwcaCETkdPKhKpw+92POfcm0BRzkxozxXfgRX2+0QlPf/tn3tAJlVQMac4krmxXcYqeoctXw54hs2f1cwX1Cw=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_1.5.6_1562810213387_0.25815558179453824"}, "_hasShrinkwrap": false, "publish_time": 1562810213575, "_cnpm_publish_time": 1562810213575, "_cnpmcore_publish_time": "2021-12-16T16:05:10.461Z"}, "1.5.5": {"name": "vue-types", "version": "1.5.5", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.cjs.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "typings": "types/index.d.ts", "engines": {"node": ">=10.15.0"}, "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm run test:types && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "test:types": "tsc --noEmit -p ./types/test/", "lint": "eslint src/*.js test/*.js types/**/*.ts types/**/*.d.ts", "start": "rollup -c ./examples/rollup.config.js -w", "snyk-protect": "snyk protect", "prepublish": "npm run snyk-protect"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "*"}, "devDependencies": {"@babel/cli": "7.5.0", "@babel/core": "7.5.0", "@babel/preset-env": "7.5.0", "@types/lodash": "4.14.135", "@types/node": "12.0.12", "@typescript-eslint/eslint-plugin": "1.11.0", "@typescript-eslint/parser": "1.11.0", "core-js": "3.1.4", "cross-env": "5.2.0", "eslint": "^5.16.0", "eslint-config-prettier": "6.0.0", "eslint-plugin-node": "9.1.0", "eslint-plugin-prettier": "3.1.0", "eslint-plugin-vue": "5.2.3", "expect": "1.20.2", "karma": "4.1.0", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-rollup-preprocessor": "7.0.0", "karma-sauce-launcher": "2.0.2", "mocha": "6.1.4", "prettier": "1.18.2", "puppeteer": "1.18.1", "rimraf": "2.6.3", "rollup": "1.16.6", "rollup-plugin-alias": "1.5.2", "rollup-plugin-babel": "4.3.3", "rollup-plugin-commonjs": "10.0.1", "rollup-plugin-filesize": "6.1.1", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.4.0", "rollup-plugin-node-resolve": "5.2.0", "rollup-plugin-replace": "2.2.0", "rollup-plugin-serve": "1.0.1", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "6.0.2", "typescript": "3.5.2", "vue": "~2.6.10", "vue-class-component": "~7.1.0"}, "dependencies": {"lodash": "^4.17.10", "snyk": "^1.192.0"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "snyk": true, "gitHead": "d726df099185cc8040d11f6a74a23b804a90b20a", "_id": "vue-types@1.5.5", "_nodeVersion": "10.15.3", "_npmVersion": "6.9.0", "dist": {"shasum": "f7c520d5c260d74dd1fad43507d2c24d3b24fad6", "size": 41012, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.5.5.tgz", "integrity": "sha512-QR0UBeSSFuaSXru+0wZtvv+8wVuZcyjRpOUSJjKr0AFHAixDPh6g2r9WNW3ayOPLQrYt0CO2B/ljHdbYmTxgqA=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_1.5.5_1562304206919_0.02616845148370084"}, "_hasShrinkwrap": false, "publish_time": 1562304207020, "_cnpm_publish_time": 1562304207020, "_cnpmcore_publish_time": "2021-12-16T16:05:10.853Z"}, "1.5.4": {"name": "vue-types", "version": "1.5.4", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.cjs.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "typings": "types/index.d.ts", "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm run test:types && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "test:types": "tsc -p ./types/test/tsconfig.json", "lint": "eslint src/*.js test/*.js && npm run lint:types", "lint:types": "dtslint types", "start": "rollup -c ./examples/rollup.config.js -w", "snyk-protect": "snyk protect", "prepublish": "npm run snyk-protect"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "*"}, "devDependencies": {"@babel/cli": "7.2.3", "@babel/core": "7.2.2", "@babel/preset-env": "7.3.1", "@types/lodash": "4.14.121", "@types/node": "11.9.3", "core-js": "2.6.3", "cross-env": "5.2.0", "dtslint": "0.4.2", "eslint": "5.12.1", "eslint-plugin-vue-libs": "3.0.0", "expect": "1.20.2", "karma": "3.1.4", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-rollup-preprocessor": "7.0.0-rc.2", "karma-sauce-launcher": "1.2.0", "mocha": "5.2.0", "puppeteer": "1.11.0", "rimraf": "2.6.3", "rollup": "1.1.2", "rollup-plugin-alias": "1.5.1", "rollup-plugin-babel": "4.3.2", "rollup-plugin-commonjs": "9.2.0", "rollup-plugin-filesize": "6.0.0", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.4.0", "rollup-plugin-node-resolve": "4.0.0", "rollup-plugin-replace": "2.1.0", "rollup-plugin-serve": "1.0.0", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "6.0.1", "tslint": "5.12.1", "typescript": "3.3.3", "vue": "~2.5.17", "vue-class-component": "~6.3.2"}, "dependencies": {"lodash": "^4.17.10", "snyk": "^1.189.0"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "snyk": true, "gitHead": "e466eca2ae5068b2778a6dee9db08fa64883f9d8", "_id": "vue-types@1.5.4", "_npmVersion": "6.4.1", "_nodeVersion": "8.16.0", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "2f1a89cf997ac0f0066eafa127779d7e1b69fc3d", "size": 40114, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.5.4.tgz", "integrity": "sha512-0pYRpmAlTHRc3+pT5tV+CDSm0tw1UlAV34YGXxXyQdTcDe1eVmy4eguuMhmPpkKlAn0Xd0idw5SgGJVREc/CwQ=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_1.5.4_1562296904538_0.7380798044120869"}, "_hasShrinkwrap": false, "publish_time": 1562296904763, "_cnpm_publish_time": 1562296904763, "_cnpmcore_publish_time": "2021-12-16T16:05:11.275Z"}, "1.5.3": {"name": "vue-types", "version": "1.5.3", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.cjs.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "typings": "types/index.d.ts", "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm run test:types && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "test:types": "tsc -p ./types/test/tsconfig.json", "lint": "eslint src/*.js test/*.js && npm run lint:types", "lint:types": "dtslint types", "start": "rollup -c ./examples/rollup.config.js -w"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "*"}, "devDependencies": {"@babel/cli": "7.2.3", "@babel/core": "7.2.2", "@babel/preset-env": "7.3.1", "@types/lodash": "4.14.121", "@types/node": "11.9.3", "core-js": "2.6.3", "cross-env": "5.2.0", "dtslint": "0.4.2", "eslint": "5.12.1", "eslint-plugin-vue-libs": "3.0.0", "expect": "1.20.2", "karma": "3.1.4", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-rollup-preprocessor": "7.0.0-rc.2", "karma-sauce-launcher": "1.2.0", "mocha": "5.2.0", "puppeteer": "1.11.0", "rimraf": "2.6.3", "rollup": "1.1.2", "rollup-plugin-alias": "1.5.1", "rollup-plugin-babel": "4.3.2", "rollup-plugin-commonjs": "9.2.0", "rollup-plugin-filesize": "6.0.0", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.4.0", "rollup-plugin-node-resolve": "4.0.0", "rollup-plugin-replace": "2.1.0", "rollup-plugin-serve": "1.0.0", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "6.0.1", "tslint": "5.12.1", "typescript": "3.3.3", "vue": "~2.5.17", "vue-class-component": "~6.3.2"}, "dependencies": {"lodash": "^4.17.10"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "gitHead": "716f1a28d727c5fadfebdf0aeb2e884615156225", "_id": "vue-types@1.5.3", "_nodeVersion": "8.11.2", "_npmVersion": "6.7.0", "dist": {"shasum": "b51fd276dac93d56bb2903b11743fb0027ff6345", "size": 40078, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.5.3.tgz", "integrity": "sha512-VHeAqjIAC5+RtQEzOVLAIKhCngXKFXcggAQJIO74VTCBqbviCHUQHkx0ZxiUAWBeoCMVT4U+SKplvEX8ENkkGg=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_1.5.3_1554166785116_0.7574884530426582"}, "_hasShrinkwrap": false, "publish_time": 1554166785299, "_cnpm_publish_time": 1554166785299, "_cnpmcore_publish_time": "2021-12-16T16:05:11.524Z"}, "1.5.2": {"name": "vue-types", "version": "1.5.2", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.cjs.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "typings": "types/index.d.ts", "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm run test:types && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "test:types": "tsc -p ./types/test/tsconfig.json", "lint": "eslint src/*.js test/*.js && npm run lint:types", "lint:types": "dtslint types", "start": "rollup -c ./examples/rollup.config.js -w"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "*"}, "devDependencies": {"@babel/cli": "7.2.3", "@babel/core": "7.2.2", "@babel/preset-env": "7.3.1", "@types/lodash": "4.14.121", "@types/node": "11.9.3", "core-js": "2.6.3", "cross-env": "5.2.0", "dtslint": "0.4.2", "eslint": "5.12.1", "eslint-plugin-vue-libs": "3.0.0", "expect": "1.20.2", "karma": "3.1.4", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-rollup-preprocessor": "7.0.0-rc.2", "karma-sauce-launcher": "1.2.0", "mocha": "5.2.0", "puppeteer": "1.11.0", "rimraf": "2.6.3", "rollup": "1.1.2", "rollup-plugin-alias": "1.5.1", "rollup-plugin-babel": "4.3.2", "rollup-plugin-commonjs": "9.2.0", "rollup-plugin-filesize": "6.0.0", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.4.0", "rollup-plugin-node-resolve": "4.0.0", "rollup-plugin-replace": "2.1.0", "rollup-plugin-serve": "1.0.0", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "6.0.1", "tslint": "5.12.1", "typescript": "3.3.3", "vue": "~2.5.17", "vue-class-component": "~6.3.2"}, "dependencies": {"lodash": "^4.17.10"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "readmeFilename": "README.md", "gitHead": "02546aeeb14f80b83c1e90910f263181c8efb9c4", "_id": "vue-types@1.5.2", "_nodeVersion": "8.11.2", "_npmVersion": "6.7.0", "dist": {"shasum": "a43509b183b4f1d310662ad2fba1e95be835acfd", "size": 40079, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.5.2.tgz", "integrity": "sha512-QWCzUrQfujrrEQKD+PswRkTlEYUXTG/EahksSLDfPILPZGERqtsGPQyDYP7n7qVwI/53ciYe3Z4/nDtqGVRSbg=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_1.5.2_1554101618773_0.24276339255184198"}, "_hasShrinkwrap": false, "publish_time": 1554101619123, "_cnpm_publish_time": 1554101619123, "_cnpmcore_publish_time": "2021-12-16T16:05:11.719Z"}, "1.5.1": {"name": "vue-types", "version": "1.5.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.cjs.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "typings": "types/index.d.ts", "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm run test:types && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "test:types": "tsc -p ./types/test/tsconfig.json", "lint": "eslint src/*.js test/*.js && npm run lint:types", "lint:types": "dtslint types", "start": "rollup -c ./examples/rollup.config.js -w"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "*"}, "devDependencies": {"@babel/cli": "7.2.3", "@babel/core": "7.2.2", "@babel/preset-env": "7.3.1", "@types/lodash": "4.14.121", "@types/node": "11.9.3", "core-js": "2.6.3", "cross-env": "5.2.0", "dtslint": "0.4.2", "eslint": "5.12.1", "eslint-plugin-vue-libs": "3.0.0", "expect": "1.20.2", "karma": "3.1.4", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-rollup-preprocessor": "7.0.0-rc.2", "karma-sauce-launcher": "1.2.0", "mocha": "5.2.0", "puppeteer": "1.11.0", "rimraf": "2.6.3", "rollup": "1.1.2", "rollup-plugin-alias": "1.5.1", "rollup-plugin-babel": "4.3.2", "rollup-plugin-commonjs": "9.2.0", "rollup-plugin-filesize": "6.0.0", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.4.0", "rollup-plugin-node-resolve": "4.0.0", "rollup-plugin-replace": "2.1.0", "rollup-plugin-serve": "1.0.0", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "6.0.1", "tslint": "5.12.1", "typescript": "3.3.3", "vue": "~2.5.17", "vue-class-component": "~6.3.2"}, "dependencies": {"lodash": "^4.17.10"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "readmeFilename": "README.md", "gitHead": "c976c2de2733f713bf7783d5a73e484125474cbe", "_id": "vue-types@1.5.1", "_nodeVersion": "8.11.2", "_npmVersion": "6.7.0", "dist": {"shasum": "23cc0e3aaa63153a650fbaa78c9003d7d0eed0b0", "size": 39987, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.5.1.tgz", "integrity": "sha512-l+/Rf/WMj3fEokqO967g2jXN4Isg84LhfOwrDMzkKjsxVBV59b3RDKdNtoMcptatF7kmWsSz4SnHKZIHQpBLog=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_1.5.1_1554091928847_0.2142518386061818"}, "_hasShrinkwrap": false, "publish_time": 1554091929020, "_cnpm_publish_time": 1554091929020, "_cnpmcore_publish_time": "2021-12-16T16:05:11.963Z"}, "1.5.0": {"name": "vue-types", "version": "1.5.0", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.cjs.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "typings": "types/index.d.ts", "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm run test:types && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "test:types": "tsc -p ./types/test/tsconfig.json", "lint": "eslint src/*.js test/*.js && npm run lint:types", "lint:types": "dtslint types", "start": "rollup -c ./examples/rollup.config.js -w"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "*"}, "devDependencies": {"@babel/cli": "7.2.3", "@babel/core": "7.2.2", "@babel/preset-env": "7.3.1", "@types/lodash": "4.14.121", "@types/node": "11.9.3", "core-js": "2.6.3", "cross-env": "5.2.0", "dtslint": "0.4.2", "eslint": "5.12.1", "eslint-plugin-vue-libs": "3.0.0", "expect": "1.20.2", "karma": "3.1.4", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-rollup-preprocessor": "7.0.0-rc.2", "karma-sauce-launcher": "1.2.0", "mocha": "5.2.0", "puppeteer": "1.11.0", "rimraf": "2.6.3", "rollup": "1.1.2", "rollup-plugin-alias": "1.5.1", "rollup-plugin-babel": "4.3.2", "rollup-plugin-commonjs": "9.2.0", "rollup-plugin-filesize": "6.0.0", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.4.0", "rollup-plugin-node-resolve": "4.0.0", "rollup-plugin-replace": "2.1.0", "rollup-plugin-serve": "1.0.0", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "6.0.1", "tslint": "5.12.1", "typescript": "3.3.3", "vue": "~2.5.17", "vue-class-component": "~6.3.2"}, "dependencies": {"lodash": "^4.17.10"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "gitHead": "67c332f46fee6f1b8c14ffd257a79d8f2d8e42da", "_id": "vue-types@1.5.0", "_nodeVersion": "8.11.2", "_npmVersion": "6.7.0", "dist": {"shasum": "ed7988896bdb4fb1fe47df293d289e5c36d7d418", "size": 39689, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.5.0.tgz", "integrity": "sha512-sXtq47f+KOlknfJC5k+m+BsMB8vYeBWTO0fkRt/GDYONlnRSnI2Uz4WnLhpLWUKdckgeHqGnyS68JAAn7LQFPg=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_1.5.0_1550116551718_0.4225562073506848"}, "_hasShrinkwrap": false, "publish_time": 1550116551854, "_cnpm_publish_time": 1550116551854, "_cnpmcore_publish_time": "2021-12-16T16:05:12.347Z"}, "1.3.4": {"name": "vue-types", "version": "1.3.4", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.cjs.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "typings": "types/index.d.ts", "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm run test:types && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "test:types": "tsc -p ./types/test/tsconfig.json", "lint": "eslint src/*.js test/*.js && npm run lint:types", "lint:types": "dtslint types", "start": "node ./server/index.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "*"}, "devDependencies": {"@babel/cli": "7.0.0", "@babel/core": "7.0.0", "@babel/preset-env": "7.0.0", "@types/lodash": "4.14.116", "@types/node": "10.9.4", "cross-env": "5.2.0", "dtslint": "0.3.0", "eslint": "4.19.1", "eslint-plugin-vue-libs": "3.0.0", "expect": "1.20.2", "express": "4.16.3", "karma": "3.0.0", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-rollup-preprocessor": "6.0.1", "karma-sauce-launcher": "1.2.0", "mocha": "5.2.0", "puppeteer": "1.7.0", "rimraf": "2.6.2", "rollup": "0.65.0", "rollup-plugin-babel": "4.0.2", "rollup-plugin-commonjs": "9.1.6", "rollup-plugin-filesize": "4.0.1", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.2.1", "rollup-plugin-node-resolve": "3.3.0", "rollup-plugin-replace": "2.0.0", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "5.0.2", "serve-index": "1.9.1", "tslint": "5.11.0", "typescript": "3.0.3", "vue": "~2.5.17", "vue-class-component": "~6.2.0"}, "dependencies": {"lodash": "^4.17.10"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "gitHead": "76c042d659f358a337d0c40fed1c0cd3198cd31f", "_id": "vue-types@1.3.4", "_npmVersion": "6.5.0", "_nodeVersion": "10.13.0", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "78c53ddd0007e209d1502d76cc51dd5b7e1d5fe6", "size": 34001, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.3.4.tgz", "integrity": "sha512-c8a0wShFBSOkPUWTtv/OKi55cEAScTTQx/kOd2EuahSyMFPB8f+vYQbsn9RvTQylEtR3ERu9G/em6sGb9Nvmbw=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_1.3.4_1544765813622_0.2562256355816168"}, "_hasShrinkwrap": false, "publish_time": 1544765813807, "_cnpm_publish_time": 1544765813807, "_cnpmcore_publish_time": "2021-12-16T16:05:12.677Z"}, "1.3.4-beta.1": {"name": "vue-types", "version": "1.3.4-beta.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.cjs.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "typings": "types/index.d.ts", "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm run test:types && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "test:types": "tsc -p ./types/test/tsconfig.json", "lint": "eslint src/*.js test/*.js && npm run lint:types", "lint:types": "dtslint types", "start": "node ./server/index.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "*"}, "devDependencies": {"@babel/cli": "7.0.0", "@babel/core": "7.0.0", "@babel/preset-env": "7.0.0", "@types/lodash": "4.14.116", "@types/node": "10.9.4", "cross-env": "5.2.0", "dtslint": "0.3.0", "eslint": "4.19.1", "eslint-plugin-vue-libs": "3.0.0", "expect": "1.20.2", "express": "4.16.3", "karma": "3.0.0", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-rollup-preprocessor": "6.0.1", "karma-sauce-launcher": "1.2.0", "mocha": "5.2.0", "puppeteer": "1.7.0", "rimraf": "2.6.2", "rollup": "0.65.0", "rollup-plugin-babel": "4.0.2", "rollup-plugin-commonjs": "9.1.6", "rollup-plugin-filesize": "4.0.1", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.2.1", "rollup-plugin-node-resolve": "3.3.0", "rollup-plugin-replace": "2.0.0", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "5.0.2", "serve-index": "1.9.1", "tslint": "5.11.0", "typescript": "3.0.3", "vue": "~2.5.17", "vue-class-component": "~6.2.0"}, "dependencies": {"lodash": "^4.17.10"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "readmeFilename": "README.md", "gitHead": "01d8a1ea21d2a37615b82672df4a5341e9cfa79c", "_id": "vue-types@1.3.4-beta.1", "_npmVersion": "6.5.0", "_nodeVersion": "10.13.0", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "aa8d2026927c57c829b4d621031095e018414a4e", "size": 34017, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.3.4-beta.1.tgz", "integrity": "sha512-L9GT7DPu3EBfj67I2ebchaimCO5JJdKsvvqSPd5ToQTMWJOyJMwTJlq7ip1NcTlYA1R1HdJdPYlfJktD6ab/jQ=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_1.3.4-beta.1_1544593597775_0.2880806237372229"}, "_hasShrinkwrap": false, "publish_time": 1544593597900, "_cnpm_publish_time": 1544593597900, "_cnpmcore_publish_time": "2021-12-16T16:05:12.884Z"}, "1.3.4-beta.0": {"name": "vue-types", "version": "1.3.4-beta.0", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.cjs.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "typings": "types/index.d.ts", "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm run test:types && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "test:types": "tsc -p ./types/test/tsconfig.json", "lint": "eslint src/*.js test/*.js && npm run lint:types", "lint:types": "dtslint types", "start": "node ./server/index.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "*"}, "devDependencies": {"@babel/cli": "7.0.0", "@babel/core": "7.0.0", "@babel/preset-env": "7.0.0", "@types/lodash": "4.14.116", "@types/node": "10.9.4", "cross-env": "5.2.0", "dtslint": "0.3.0", "eslint": "4.19.1", "eslint-plugin-vue-libs": "3.0.0", "expect": "1.20.2", "express": "4.16.3", "karma": "3.0.0", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-rollup-preprocessor": "6.0.1", "karma-sauce-launcher": "1.2.0", "mocha": "5.2.0", "puppeteer": "1.7.0", "rimraf": "2.6.2", "rollup": "0.65.0", "rollup-plugin-babel": "4.0.2", "rollup-plugin-commonjs": "9.1.6", "rollup-plugin-filesize": "4.0.1", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.2.1", "rollup-plugin-node-resolve": "3.3.0", "rollup-plugin-replace": "2.0.0", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "5.0.2", "serve-index": "1.9.1", "tslint": "5.11.0", "typescript": "3.0.3", "vue": "~2.5.17", "vue-class-component": "~6.2.0"}, "dependencies": {"lodash": "^4.17.10"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "readmeFilename": "README.md", "gitHead": "3408dde4a7a830dba6e08fa761cb1ca156d5ecd6", "_id": "vue-types@1.3.4-beta.0", "_npmVersion": "6.4.1", "_nodeVersion": "8.11.2", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "20681dc13dfe92fcb203bbff46e9c1e87b65541d", "size": 34013, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.3.4-beta.0.tgz", "integrity": "sha512-eZp8mDa3GGQtOOFcteC8U4xpG8G9Byk/qDppmf6QuInDTsbRtae8Wnd2HNVxD9WTGiV/foMrH20wN5r0qODSQQ=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_1.3.4-beta.0_1538629363971_0.7209695438609947"}, "_hasShrinkwrap": false, "publish_time": 1538629364198, "_cnpm_publish_time": 1538629364198, "_cnpmcore_publish_time": "2021-12-16T16:05:13.138Z"}, "1.3.3": {"name": "vue-types", "version": "1.3.3", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.cjs.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "typings": "types/index.d.ts", "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm run test:types && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "test:types": "tsc -p ./types/test/tsconfig.json", "lint": "eslint src/*.js test/*.js", "lint:types": "dtslint types", "start": "node ./server/index.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "*"}, "devDependencies": {"@babel/cli": "7.0.0", "@babel/core": "7.0.0", "@babel/preset-env": "7.0.0", "@types/lodash": "4.14.116", "@types/node": "10.9.4", "cross-env": "5.2.0", "dtslint": "0.3.0", "eslint": "4.19.1", "eslint-plugin-vue-libs": "3.0.0", "expect": "1.20.2", "express": "4.16.3", "karma": "3.0.0", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-rollup-preprocessor": "6.0.1", "karma-sauce-launcher": "1.2.0", "mocha": "5.2.0", "puppeteer": "1.7.0", "rimraf": "2.6.2", "rollup": "0.65.0", "rollup-plugin-babel": "4.0.2", "rollup-plugin-commonjs": "9.1.6", "rollup-plugin-filesize": "4.0.1", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.2.1", "rollup-plugin-node-resolve": "3.3.0", "rollup-plugin-replace": "2.0.0", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "5.0.2", "serve-index": "1.9.1", "tslint": "5.11.0", "typescript": "3.0.3", "vue": "~2.5.17", "vue-class-component": "~6.2.0"}, "dependencies": {"lodash": "^4.17.10"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"], "gitHead": "d940604a16ae0b46eab28086341291216077abb7", "_id": "vue-types@1.3.3", "_npmVersion": "6.4.0", "_nodeVersion": "8.11.2", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "90ac9244363f16dbbc22f2ce28e732b6605dd3ad", "size": 33983, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.3.3.tgz", "integrity": "sha512-JOFyA5iLRlUVScBbBxDksniFp3Zheg53sAmJ0rPXI/+/KHQMOIx5gWgj/azFXZuQbf8a/cEROhHJVrEcQoIS3g=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_1.3.3_1535947657754_0.8156219144170922"}, "_hasShrinkwrap": false, "publish_time": 1535947657867, "_cnpm_publish_time": 1535947657867, "_cnpmcore_publish_time": "2021-12-16T16:05:13.375Z"}, "1.3.2": {"name": "vue-types", "version": "1.3.2", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "typings": "types/index.d.ts", "files": ["dist", "es", "src", "umd", "types/*.d.ts"], "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm run test:types && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "test:types": "tsc -p ./types/test/tsconfig.json", "lint": "eslint src/*.js test/*.js", "lint:types": "dtslint types", "start": "node ./server/index.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "*"}, "devDependencies": {"@types/lodash.isplainobject": "4.0.3", "@types/node": "10.1.4", "babel-cli": "6.26.0", "babel-core": "6.26.3", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-external-helpers": "6.22.0", "babel-preset-env": "1.7.0", "cross-env": "5.1.6", "dtslint": "0.3.0", "eslint": "4.19.1", "eslint-plugin-vue-libs": "3.0.0", "expect": "1.20.2", "express": "4.16.3", "karma": "2.0.2", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-opera-launcher": "1.0.0", "karma-rollup-preprocessor": "6.0.0", "karma-safari-launcher": "1.0.0", "karma-sauce-launcher": "1.2.0", "mocha": "5.2.0", "puppeteer": "1.5.0", "rimraf": "2.6.2", "rollup": "0.59.4", "rollup-plugin-babel": "3.0.4", "rollup-plugin-commonjs": "9.1.3", "rollup-plugin-filesize": "1.5.0", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.2.1", "rollup-plugin-node-resolve": "3.3.0", "rollup-plugin-replace": "2.0.0", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "4.0.0", "serve-index": "1.9.1", "tslint": "5.10.0", "typescript": "2.8.3", "vue": "~2.5.0", "vue-class-component": "~6.2.0"}, "dependencies": {"lodash.isplainobject": "4.0.6"}, "browserslist": {"targets": {"browsers": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"]}}, "gitHead": "5769a2c3b502e79499fbd1de6a4c2542b7ea4d7e", "_id": "vue-types@1.3.2", "_npmVersion": "6.1.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "d28b8f7ce2d74536025eaf6f1904fe9ec965c127", "size": 30778, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.3.2.tgz", "integrity": "sha512-nDxexHdChDy4Aj02nvQ6A2NmdGQOXlO6TtYonL6KHmkk4DIkbE1+nBf7gh1n4fcTMvidKWCQht6EUc1XBfXO7A=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_1.3.2_1529836151788_0.8795907830736147"}, "_hasShrinkwrap": false, "publish_time": 1529836151888, "_cnpm_publish_time": 1529836151888, "_cnpmcore_publish_time": "2021-12-16T16:05:13.951Z"}, "1.3.1": {"name": "vue-types", "version": "1.3.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "typings": "types/index.d.ts", "files": ["dist", "es", "src", "umd", "types/*.d.ts"], "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm run test:types && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers PhantomJS", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "test:types": "tsc -p ./types/test/tsconfig.json", "lint": "eslint src/*.js test/*.js", "lint:types": "dtslint types", "start": "node ./server/index.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "*"}, "devDependencies": {"@types/lodash.isplainobject": "4.0.3", "@types/node": "10.1.4", "babel-cli": "6.26.0", "babel-core": "6.26.3", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-external-helpers": "6.22.0", "babel-preset-env": "1.7.0", "cross-env": "5.1.6", "dtslint": "0.3.0", "eslint": "4.19.1", "eslint-plugin-vue-libs": "3.0.0", "expect": "1.20.2", "express": "4.16.3", "karma": "2.0.2", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-opera-launcher": "1.0.0", "karma-phantomjs-launcher": "1.0.4", "karma-rollup-preprocessor": "6.0.0", "karma-safari-launcher": "1.0.0", "karma-sauce-launcher": "1.2.0", "mocha": "5.2.0", "rimraf": "2.6.2", "rollup": "0.59.4", "rollup-plugin-babel": "3.0.4", "rollup-plugin-commonjs": "9.1.3", "rollup-plugin-filesize": "1.5.0", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.2.1", "rollup-plugin-node-resolve": "3.3.0", "rollup-plugin-replace": "2.0.0", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "4.0.0", "serve-index": "1.9.1", "tslint": "5.10.0", "typescript": "2.8.3", "vue": "~2.5.0", "vue-class-component": "~6.2.0"}, "dependencies": {"lodash.isplainobject": "4.0.6"}, "browserslist": {"targets": {"browsers": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"]}}, "gitHead": "d5e55b342e09c12b9ec0ffd98b8378e970cef7ca", "_id": "vue-types@1.3.1", "_npmVersion": "6.1.0", "_nodeVersion": "8.11.2", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "e6674220ba2826c132044059adc2954a94cb153e", "size": 30344, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.3.1.tgz", "integrity": "sha512-ifeFd5R7A+8trUxNCueJh4kyBywZA6Y6Y4F6Bx8dbD1cB6St13ybdPJSN0Ubsj0ie00gtQCCz2nvuyBPSaiOPA=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_1.3.1_1528775532045_0.5818593350899273"}, "_hasShrinkwrap": false, "publish_time": 1528775532887, "_cnpm_publish_time": 1528775532887, "_cnpmcore_publish_time": "2021-12-16T16:05:14.225Z"}, "1.3.0": {"name": "vue-types", "version": "1.3.0", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "typings": "types/index.d.ts", "files": ["dist", "es", "src", "umd", "types/*.d.ts"], "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm run test:types && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers PhantomJS", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "test:types": "tsc -p ./types/test/tsconfig.json", "lint": "eslint src/*.js test/*.js", "lint:types": "dtslint types", "start": "node ./server/index.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "*"}, "devDependencies": {"@types/lodash.isplainobject": "4.0.3", "@types/node": "10.1.4", "babel-cli": "6.26.0", "babel-core": "6.26.3", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-external-helpers": "6.22.0", "babel-preset-env": "1.7.0", "cross-env": "5.1.6", "dtslint": "0.3.0", "eslint": "4.19.1", "eslint-plugin-vue-libs": "3.0.0", "expect": "1.20.2", "express": "4.16.3", "karma": "2.0.2", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-opera-launcher": "1.0.0", "karma-phantomjs-launcher": "1.0.4", "karma-rollup-preprocessor": "6.0.0", "karma-safari-launcher": "1.0.0", "karma-sauce-launcher": "1.2.0", "mocha": "5.2.0", "rimraf": "2.6.2", "rollup": "0.59.4", "rollup-plugin-babel": "3.0.4", "rollup-plugin-commonjs": "9.1.3", "rollup-plugin-filesize": "1.5.0", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.2.1", "rollup-plugin-node-resolve": "3.3.0", "rollup-plugin-replace": "2.0.0", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "4.0.0", "serve-index": "1.9.1", "tslint": "5.10.0", "typescript": "2.8.3", "vue": "~2.5.0", "vue-class-component": "~6.2.0"}, "dependencies": {"lodash.isplainobject": "4.0.6"}, "browserslist": {"targets": {"browsers": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"]}}, "gitHead": "864eb0bc4f3af4d19dcce077e2a9e34e9f5b4269", "_id": "vue-types@1.3.0", "_npmVersion": "6.0.1", "_nodeVersion": "8.11.0", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "9f71882537a017b285269c9584beb16c6c03e630", "size": 30309, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.3.0.tgz", "integrity": "sha512-XjV11tZdNNfUbIKzZZuJ5SdV+34PiXk1Weac7pxJVCCDF5Nzp5N0ckVFscN5v2iB49IQ//OuCQOMXLTcwZX9kA=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_1.3.0_1527669519653_0.9812742712504436"}, "_hasShrinkwrap": false, "publish_time": 1527669519737, "_cnpm_publish_time": 1527669519737, "_cnpmcore_publish_time": "2021-12-16T16:05:14.637Z"}, "1.2.3": {"name": "vue-types", "version": "1.2.3", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "files": ["dist", "es", "src", "umd"], "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers PhantomJS", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "lint": "eslint src/*.js test/*.js", "start": "node ./server/index.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "*"}, "devDependencies": {"babel-cli": "6.26.0", "babel-core": "6.26.3", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-external-helpers": "6.22.0", "babel-preset-env": "1.7.0", "cross-env": "5.1.5", "eslint": "4.19.1", "eslint-config-vue": "2.0.2", "eslint-plugin-html": "4.0.3", "eslint-plugin-vue": "4.5.0", "expect": "1.20.2", "express": "4.16.3", "karma": "2.0.2", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-opera-launcher": "1.0.0", "karma-phantomjs-launcher": "1.0.4", "karma-rollup-preprocessor": "6.0.0", "karma-safari-launcher": "1.0.0", "karma-sauce-launcher": "1.2.0", "mocha": "5.1.1", "rimraf": "2.6.2", "rollup": "0.58.2", "rollup-plugin-babel": "3.0.4", "rollup-plugin-commonjs": "9.1.3", "rollup-plugin-filesize": "1.5.0", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.2.1", "rollup-plugin-node-resolve": "3.3.0", "rollup-plugin-replace": "2.0.0", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "3.0.0", "serve-index": "1.9.1", "vue": "2.5.16"}, "dependencies": {"lodash.isplainobject": "4.0.6"}, "browserslist": {"targets": {"browsers": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"]}}, "gitHead": "0a7789daac51e943f98c74c35bb723dd807955a3", "_id": "vue-types@1.2.3", "_npmVersion": "6.0.0", "_nodeVersion": "8.11.0", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "550777d60e459c9c4c54a5a0f0f97dc981bbab9f", "size": 28605, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.2.3.tgz", "integrity": "sha512-dRglSI0PZQTpFJUgmig1gUjB09nr9pD/dLBj3u3/PSTs/R4Lahj3efUqrw8GRPwtM/xtHP7+p4xM/t/j/ZVvFQ=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_1.2.3_1526265039555_0.8473623797188512"}, "_hasShrinkwrap": false, "publish_time": 1526265039679, "_cnpm_publish_time": 1526265039679, "_cnpmcore_publish_time": "2021-12-16T16:05:14.935Z"}, "1.2.2": {"name": "vue-types", "version": "1.2.2", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "files": ["dist", "es", "src", "umd"], "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers PhantomJS", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "lint": "eslint src/*.js test/*.js", "start": "node ./server/index.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "devDependencies": {"babel-cli": "6.26.0", "babel-core": "6.26.0", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-external-helpers": "6.22.0", "babel-preset-env": "1.6.1", "cross-env": "5.1.3", "eslint": "4.18.2", "eslint-config-vue": "2.0.2", "eslint-plugin-html": "4.0.2", "eslint-plugin-vue": "4.3.0", "expect": "1.20.2", "express": "4.16.2", "karma": "2.0.0", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-opera-launcher": "1.0.0", "karma-phantomjs-launcher": "1.0.4", "karma-rollup-preprocessor": "5.1.1", "karma-safari-launcher": "1.0.0", "karma-sauce-launcher": "1.2.0", "mocha": "5.0.2", "rimraf": "2.6.2", "rollup": "0.56.4", "rollup-plugin-babel": "3.0.3", "rollup-plugin-commonjs": "9.0.0", "rollup-plugin-filesize": "1.5.0", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.1.0", "rollup-plugin-node-resolve": "3.0.3", "rollup-plugin-replace": "2.0.0", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "3.0.0", "serve-index": "1.9.1", "vue": "2.5.16"}, "dependencies": {"lodash.isplainobject": "4.0.6"}, "browserslist": {"targets": {"browsers": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"]}}, "gitHead": "c7976196006501c78cc38c378c1f5df8089b59ca", "_id": "vue-types@1.2.2", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "fc32401f69a881f1be81b648ba6cf5d37671a45f", "size": 304026, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.2.2.tgz", "integrity": "sha512-QpOoDOtutCWn+U/G2/j6iyGSo5om+geGu5WaLoBA9+8a7qp+feGiqa6AhsWMJaojLSlDe4OTCstnh5y4B/VjzQ=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_1.2.2_1521450853845_0.6751757659832793"}, "_hasShrinkwrap": false, "publish_time": 1521450854103, "_cnpm_publish_time": 1521450854103, "_cnpmcore_publish_time": "2021-12-16T16:05:15.253Z"}, "1.2.1": {"name": "vue-types", "version": "1.2.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "files": ["dist", "es", "src", "umd"], "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers PhantomJS", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "lint": "eslint src/*.js test/*.js", "start": "node ./server/index.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "devDependencies": {"babel-cli": "6.26.0", "babel-core": "6.26.0", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-external-helpers": "6.22.0", "babel-preset-env": "1.6.1", "cross-env": "5.1.3", "eslint": "4.18.2", "eslint-config-vue": "2.0.2", "eslint-plugin-html": "4.0.2", "eslint-plugin-vue": "4.3.0", "expect": "1.20.2", "express": "4.16.2", "karma": "2.0.0", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-opera-launcher": "1.0.0", "karma-phantomjs-launcher": "1.0.4", "karma-rollup-preprocessor": "5.1.1", "karma-safari-launcher": "1.0.0", "karma-sauce-launcher": "1.2.0", "mocha": "5.0.2", "rimraf": "2.6.2", "rollup": "0.56.4", "rollup-plugin-babel": "3.0.3", "rollup-plugin-commonjs": "9.0.0", "rollup-plugin-filesize": "1.5.0", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.1.0", "rollup-plugin-node-resolve": "3.0.3", "rollup-plugin-replace": "2.0.0", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "3.0.0", "serve-index": "1.9.1", "vue": "2.5.16"}, "dependencies": {"lodash.isplainobject": "4.0.6"}, "browserslist": {"targets": {"browsers": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"]}}, "gitHead": "f7c5aeb284befd4931a1587447ac86e397bf554c", "_id": "vue-types@1.2.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "37777385ffccc0dc5b7ff7b7fc2fc62489550494", "size": 304027, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.2.1.tgz", "integrity": "sha512-rR+KIa8OaRoWa7TyFPEQ1lk9woVEiFPcDX0PEqxNC3C35hTjv3udxN+aqyL4UM1dd5DcSRCfPlFVGNIGWwWpNw=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_1.2.1_1521441062569_0.0308524921767539"}, "_hasShrinkwrap": false, "publish_time": 1521441062762, "_cnpm_publish_time": 1521441062762, "_cnpmcore_publish_time": "2021-12-16T16:05:15.581Z"}, "1.2.0": {"name": "vue-types", "version": "1.2.0", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "files": ["dist", "es", "src", "umd"], "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers PhantomJS", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "lint": "eslint src/*.js test/*.js", "start": "node ./server/index.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "devDependencies": {"babel-cli": "6.26.0", "babel-core": "6.26.0", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-external-helpers": "6.22.0", "babel-preset-env": "1.6.1", "cross-env": "5.1.3", "eslint": "4.18.2", "eslint-config-vue": "2.0.2", "eslint-plugin-html": "4.0.2", "eslint-plugin-vue": "4.3.0", "expect": "1.20.2", "express": "4.16.2", "karma": "2.0.0", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-opera-launcher": "1.0.0", "karma-phantomjs-launcher": "1.0.4", "karma-rollup-preprocessor": "5.1.1", "karma-safari-launcher": "1.0.0", "karma-sauce-launcher": "1.2.0", "mocha": "5.0.2", "rimraf": "2.6.2", "rollup": "0.56.4", "rollup-plugin-babel": "3.0.3", "rollup-plugin-commonjs": "9.0.0", "rollup-plugin-filesize": "1.5.0", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.1.0", "rollup-plugin-node-resolve": "3.0.3", "rollup-plugin-replace": "2.0.0", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "3.0.0", "serve-index": "1.9.1"}, "dependencies": {"lodash.isplainobject": "4.0.6"}, "browserslist": {"targets": {"browsers": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"]}}, "gitHead": "c0891f4418c18686b982ac32f714e9da27d4736b", "_id": "vue-types@1.2.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "8569232dd27d9d541ded745777ddc7d414ea66b0", "size": 26919, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.2.0.tgz", "integrity": "sha512-NoAYINHlwgH6ErNn/YgIjLiHaiIUJLFhMxbGTOagQ/8qC4Cim02gCqbJ1q92HbE1V7ZuBrpDQd2At5RrtNeJOQ=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_1.2.0_1520314963464_0.11483656236841622"}, "_hasShrinkwrap": false, "publish_time": 1520314963652, "_cnpm_publish_time": 1520314963652, "_cnpmcore_publish_time": "2021-12-16T16:05:16.076Z"}, "1.1.3": {"name": "vue-types", "version": "1.1.3", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "files": ["dist", "es", "src", "umd"], "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers PhantomJS", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "lint": "eslint src/*.js test/*.js", "start": "node ./server/index.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "devDependencies": {"babel-cli": "6.26.0", "babel-core": "6.26.0", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-external-helpers": "6.22.0", "babel-preset-es2015": "6.24.1", "cross-env": "5.1.3", "eslint": "4.15.0", "eslint-config-vue": "2.0.2", "eslint-plugin-html": "4.0.1", "eslint-plugin-vue": "4.2.0", "expect": "1.20.2", "express": "4.16.2", "karma": "2.0.0", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-opera-launcher": "1.0.0", "karma-phantomjs-launcher": "1.0.4", "karma-rollup-preprocessor": "5.1.1", "karma-safari-launcher": "1.0.0", "karma-sauce-launcher": "1.2.0", "mocha": "4.1.0", "rimraf": "2.6.2", "rollup": "0.54.0", "rollup-plugin-babel": "3.0.3", "rollup-plugin-commonjs": "8.2.6", "rollup-plugin-filesize": "1.5.0", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.1.0", "rollup-plugin-node-resolve": "3.0.2", "rollup-plugin-replace": "2.0.0", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "2.0.1", "serve-index": "1.9.1"}, "dependencies": {"lodash.isplainobject": "4.0.6"}, "gitHead": "3c80aba0bbfbaf68de88e4be607f524784403b18", "_id": "vue-types@1.1.3", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.2", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "114e7b9baf4e041d88b6fd3708f159dcae6400bf", "size": 26248, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.1.3.tgz", "integrity": "sha512-tcPj4GgJtY0R2jVrHJ678dHyX+B0E4HGaC5lXGtMHOcU7HgtiFJl5jDmMnzSWG6fUB+xOA7e0nVdf+Fiw1BMwg=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types-1.1.3.tgz_1516193098405_0.9884764056187123"}, "directories": {}, "publish_time": 1516193098514, "_hasShrinkwrap": false, "_cnpm_publish_time": 1516193098514, "_cnpmcore_publish_time": "2021-12-16T16:05:16.321Z"}, "1.1.2": {"name": "vue-types", "version": "1.1.2", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "files": ["dist", "es", "src", "umd"], "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers PhantomJS", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "lint": "eslint src/*.js test/*.js", "start": "node ./server/index.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "devDependencies": {"babel-cli": "6.26.0", "babel-core": "6.26.0", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-external-helpers": "6.22.0", "babel-preset-es2015": "6.24.1", "cross-env": "5.1.1", "eslint": "4.13.1", "eslint-config-vue": "2.0.2", "eslint-plugin-html": "4.0.1", "eslint-plugin-vue": "2.1.0", "expect": "1.20.2", "express": "4.16.2", "karma": "2.0.0", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-opera-launcher": "1.0.0", "karma-phantomjs-launcher": "1.0.4", "karma-rollup-preprocessor": "5.0.2", "karma-safari-launcher": "1.0.0", "karma-sauce-launcher": "1.2.0", "mocha": "4.0.1", "rimraf": "2.6.2", "rollup": "0.52.3", "rollup-plugin-babel": "3.0.3", "rollup-plugin-commonjs": "8.2.6", "rollup-plugin-filesize": "1.5.0", "rollup-plugin-node-builtins": "2.1.2", "rollup-plugin-node-globals": "1.1.0", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-replace": "2.0.0", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "2.0.1", "serve-index": "1.9.1"}, "dependencies": {"lodash.isplainobject": "4.0.6"}, "gitHead": "b6a0ea0648ea1ea2bd28d32c255022790ecbbac0", "_id": "vue-types@1.1.2", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "89564a46a64aff3882295902e99abac2d67e09c2", "size": 26181, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.1.2.tgz", "integrity": "sha512-wII4jhxm+CTVkfk+JO9q8DzT4oPvoS4/tGzfqnwNV6OWq1r2RwmremCbzMz3JBH+VxcdQzz7Cw+YSIneq1p8XQ=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types-1.1.2.tgz_1515968150060_0.07766140252351761"}, "directories": {}, "publish_time": 1515968150163, "_hasShrinkwrap": false, "_cnpm_publish_time": 1515968150163, "_cnpmcore_publish_time": "2021-12-16T16:05:16.616Z"}, "1.1.1": {"name": "vue-types", "version": "1.1.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "files": ["dist", "es", "src", "umd"], "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers PhantomJS", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "lint": "eslint src/*.js test/*.js", "start": "node ./server/index.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "devDependencies": {"babel-cli": "6.26.0", "babel-core": "6.26.0", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-external-helpers": "6.22.0", "babel-preset-es2015": "6.24.1", "cross-env": "5.1.1", "eslint": "4.13.1", "eslint-config-vue": "2.0.2", "eslint-plugin-html": "4.0.1", "eslint-plugin-vue": "2.1.0", "expect": "1.20.2", "express": "4.16.2", "karma": "2.0.0", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-opera-launcher": "1.0.0", "karma-phantomjs-launcher": "1.0.4", "karma-rollup-preprocessor": "5.0.2", "karma-safari-launcher": "1.0.0", "karma-sauce-launcher": "1.2.0", "mocha": "4.0.1", "rimraf": "2.6.2", "rollup": "0.52.3", "rollup-plugin-babel": "3.0.3", "rollup-plugin-commonjs": "8.2.6", "rollup-plugin-filesize": "1.5.0", "rollup-plugin-node-globals": "1.1.0", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-replace": "2.0.0", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "2.0.1", "serve-index": "1.9.1"}, "dependencies": {"lodash.isplainobject": "4.0.6"}, "gitHead": "9380e8873363c0aa3db4abfd9e9fda9d94bfb83e", "_id": "vue-types@1.1.1", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.2", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "9cb9d59d0121de29394406e4ef89a0633adc19c4", "size": 26151, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.1.1.tgz", "integrity": "sha512-rghQreXpkxdB6ODwcxo/9tVM+3daEYqaM0h1v8ZmB+2ffMDxHR3mwTYrcHt2pb6TqKTmT4H/wfmitDV9lp+mKA=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types-1.1.1.tgz_1513881389338_0.5929292514920235"}, "directories": {}, "publish_time": 1513881390413, "_hasShrinkwrap": false, "_cnpm_publish_time": 1513881390413, "_cnpmcore_publish_time": "2021-12-16T16:05:17.296Z"}, "1.1.0": {"name": "vue-types", "version": "1.1.0", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "files": ["dist", "es", "src", "umd"], "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers PhantomJS", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox", "test:saucelab": "cross-env BABEL_ENV=rollup karma start karma-sauce.conf.js --single-run", "lint": "eslint src/*.js test/*.js", "start": "node ./server/index.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "devDependencies": {"babel-cli": "6.26.0", "babel-core": "6.26.0", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-external-helpers": "6.22.0", "babel-preset-es2015": "6.24.1", "cross-env": "5.1.1", "eslint": "4.13.1", "eslint-config-vue": "2.0.2", "eslint-plugin-html": "4.0.1", "eslint-plugin-vue": "2.1.0", "expect": "1.20.2", "express": "4.16.2", "karma": "2.0.0", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-opera-launcher": "1.0.0", "karma-phantomjs-launcher": "1.0.4", "karma-rollup-preprocessor": "5.0.2", "karma-safari-launcher": "1.0.0", "karma-sauce-launcher": "1.2.0", "mocha": "4.0.1", "rimraf": "2.6.2", "rollup": "0.52.3", "rollup-plugin-babel": "3.0.3", "rollup-plugin-commonjs": "8.2.6", "rollup-plugin-filesize": "1.5.0", "rollup-plugin-node-globals": "1.1.0", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-replace": "2.0.0", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "2.0.1", "serve-index": "1.9.1"}, "dependencies": {"lodash.isplainobject": "4.0.6"}, "gitHead": "b065ac80103349a5e7e2e8a89b68f43ce2f13bf9", "_id": "vue-types@1.1.0", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.2", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "11de67b8291c6bcc5f761f0d0e334bf8bf7c096e", "size": 26129, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.1.0.tgz", "integrity": "sha512-B3TUUkY4TXH2q2AuluRGNtP2eORhSsSJnmyTe/A7djLYaQegmcDS3+AUJ06OnMKbIXe0V6aljnfKaO180M61jg=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types-1.1.0.tgz_1513881070156_0.8524417777080089"}, "directories": {}, "publish_time": 1513881071213, "_hasShrinkwrap": false, "_cnpm_publish_time": 1513881071213, "_cnpmcore_publish_time": "2021-12-16T16:05:17.584Z"}, "1.0.3": {"name": "vue-types", "version": "1.0.3", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "files": ["dist", "es", "src", "umd"], "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers PhantomJS", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox,IE,Safari,Opera", "test:saucelab": "cross-env BABEL_ENV=cjs karma start karma-sauce.conf.js --single-run", "lint": "eslint src/*.js test/*.js", "start": "node ./server/index.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "devDependencies": {"babel-cli": "6.26.0", "babel-core": "6.26.0", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-external-helpers": "6.22.0", "babel-preset-es2015": "6.24.1", "cross-env": "5.1.1", "eslint": "4.13.1", "eslint-config-vue": "2.0.2", "eslint-plugin-html": "4.0.1", "eslint-plugin-vue": "2.1.0", "expect": "1.20.2", "express": "4.16.2", "karma": "2.0.0", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "karma-opera-launcher": "1.0.0", "karma-phantomjs-launcher": "1.0.4", "karma-rollup-preprocessor": "5.0.2", "karma-safari-launcher": "1.0.0", "karma-sauce-launcher": "1.2.0", "mocha": "4.0.1", "rimraf": "2.6.2", "rollup": "0.52.3", "rollup-plugin-babel": "3.0.3", "rollup-plugin-commonjs": "8.2.6", "rollup-plugin-filesize": "1.5.0", "rollup-plugin-node-globals": "1.1.0", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-replace": "2.0.0", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "2.0.1", "serve-index": "1.9.1"}, "dependencies": {"lodash.isplainobject": "4.0.6"}, "gitHead": "cebab28c12f4ddceb6a1d6306ca32734240d6618", "_id": "vue-types@1.0.3", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.2", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "4c8b5b70494d4702e04bb0d09a1e99321bd23496", "size": 25672, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.0.3.tgz", "integrity": "sha512-dm8QC9SpaiIJDf6QqWT5qp6or2awoTV1Zm+K+x1fSUNkKxV3J8cVEEAi3yRKks+Xa1rd6wKV4xtTrCumkAEGig=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types-1.0.3.tgz_1513875538136_0.17660692194476724"}, "directories": {}, "publish_time": 1513875539197, "_hasShrinkwrap": false, "_cnpm_publish_time": 1513875539197, "_cnpmcore_publish_time": "2021-12-16T16:05:18.437Z"}, "1.0.2": {"name": "vue-types", "version": "1.0.2", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/index.js", "unpkg": "umd/vue-types.min.js", "jsnext:main": "es/index.js", "module": "es/index.js", "files": ["dist", "es", "src", "umd"], "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm test && npm run build:cjs && npm run build:es && npm run build:umd", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "rimraf umd && cross-env BABEL_ENV=rollup rollup -c rollup.config.js", "test": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers PhantomJS", "test:all": "cross-env BABEL_ENV=rollup karma start karma.conf.js --single-run --browsers Chrome,Firefox,IE,Safari,Opera", "test:saucelab": "cross-env BABEL_ENV=cjs karma start karma-sauce.conf.js --single-run", "lint": "eslint src/*.js test/*.js", "start": "node ./server/index.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "devDependencies": {"babel-cli": "6.24.1", "babel-core": "6.25.0", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-external-helpers": "6.22.0", "babel-preset-es2015": "6.24.1", "cross-env": "5.0.5", "eslint": "4.4.1", "eslint-config-vue": "2.0.2", "eslint-plugin-html": "3.2.0", "eslint-plugin-vue": "2.1.0", "expect": "1.20.2", "express": "4.15.4", "karma": "1.7.0", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.0.1", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.3", "karma-opera-launcher": "1.0.0", "karma-phantomjs-launcher": "1.0.4", "karma-rollup-preprocessor": "4.0.3", "karma-safari-launcher": "1.0.0", "karma-sauce-launcher": "1.1.0", "mocha": "3.5.0", "rimraf": "2.6.1", "rollup": "0.47.4", "rollup-plugin-babel": "3.0.2", "rollup-plugin-bundle-size": "1.0.1", "rollup-plugin-commonjs": "8.1.0", "rollup-plugin-node-globals": "1.1.0", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-replace": "1.1.1", "rollup-plugin-stub": "1.2.0", "rollup-plugin-uglify": "2.0.1", "serve-index": "1.9.0"}, "dependencies": {"lodash.isplainobject": "4.0.6"}, "gitHead": "f68df02cd5b8e36d5454ff4185efd92b0ade5095", "_id": "vue-types@1.0.2", "_npmVersion": "5.2.0", "_nodeVersion": "6.10.3", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "8a6504deaee92dec8ee75a38ed52fddc7f066c98", "size": 21718, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.0.2.tgz", "integrity": "sha512-njmWYJIy56mkuesOhAMdZezKKOIogZ52y3H4qAcN2cHtoenlBdh4DD0BMXN+QGO5lGKrrGnmrrTsGh9tdXyRZw=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types-1.0.2.tgz_1502895903883_0.283496260875836"}, "directories": {}, "publish_time": 1502895905024, "_hasShrinkwrap": false, "_cnpm_publish_time": 1502895905024, "_cnpmcore_publish_time": "2021-12-16T16:05:18.765Z"}, "1.0.1": {"name": "vue-types", "version": "1.0.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "main": "dist/index.js", "jsnext:main": "es/index.js", "module": "es/index.js", "files": ["dist", "es", "src", "umd"], "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm test && npm run build:cjs && npm run build:es && npm run build:umd && npm run build:min", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=development webpack --config webpack.umd.js", "build:min": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=production webpack --config webpack.umd.js", "test": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers PhantomJS", "test:all": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers Chrome,Firefox,IE,Safari,Opera", "test:saucelab": "cross-env BABEL_ENV=cjs karma start karma-sauce.conf.js --single-run", "lint": "eslint src *.js", "start": "node ./server/index.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "devDependencies": {"babel-cli": "6.24.0", "babel-core": "6.24.0", "babel-loader": "6.4.1", "babel-plugin-add-module-exports": "0.2.1", "babel-preset-es2015": "6.24.0", "cross-env": "4.0.0", "eslint": "3.19.0", "eslint-config-vue": "2.0.2", "eslint-plugin-html": "2.0.1", "eslint-plugin-vue": "2.0.1", "expect": "1.20.2", "express": "4.15.2", "karma": "1.5.0", "karma-chrome-launcher": "2.0.0", "karma-firefox-launcher": "1.0.1", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.3", "karma-opera-launcher": "1.0.0", "karma-phantomjs-launcher": "1.0.4", "karma-safari-launcher": "1.0.0", "karma-sauce-launcher": "1.1.0", "karma-sourcemap-loader": "0.3.7", "karma-webpack": "2.0.3", "mocha": "3.2.0", "rimraf": "2.6.1", "serve-index": "1.8.0", "webpack": "2.3.3"}, "dependencies": {"lodash.isplainobject": "4.0.6"}, "gitHead": "0fc2f46377715daeab474c913d8ed1e602b63879", "homepage": "https://github.com/dwightjack/vue-types#readme", "_id": "vue-types@1.0.1", "_shasum": "09e66d9afb2cd3872acb9d0e3c78a77e08baf8c0", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "6.10.0", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "09e66d9afb2cd3872acb9d0e3c78a77e08baf8c0", "size": 14442, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.0.1.tgz", "integrity": "sha512-ImyOMZOOdPW+UWfiQ2iNOXvZnHdDV14IhLaseArXe44ZP5p7M0KhEScNd6vTD2VQA1/vgT7Q050NQrWkoT05Eg=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types-1.0.1.tgz_1496060405678_0.8750407702755183"}, "directories": {}, "publish_time": 1496060406809, "_hasShrinkwrap": false, "_cnpm_publish_time": 1496060406809, "_cnpmcore_publish_time": "2021-12-16T16:05:19.025Z"}, "1.0.0": {"name": "vue-types", "version": "1.0.0", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "main": "dist/index.js", "jsnext:main": "es/index.js", "module": "es/index.js", "files": ["dist", "es", "src", "umd"], "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm test && npm run build:cjs && npm run build:es && npm run build:umd && npm run build:min", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=development webpack --config webpack.umd.js", "build:min": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=production webpack --config webpack.umd.js", "test": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers PhantomJS", "test:all": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers Chrome,Firefox,IE,Safari,Opera", "test:saucelab": "cross-env BABEL_ENV=cjs karma start karma-sauce.conf.js --single-run", "lint": "eslint src *.js", "start": "node ./server/index.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "devDependencies": {"babel-cli": "6.24.0", "babel-core": "6.24.0", "babel-loader": "6.4.1", "babel-plugin-add-module-exports": "0.2.1", "babel-preset-es2015": "6.24.0", "cross-env": "4.0.0", "eslint": "3.19.0", "eslint-config-vue": "2.0.2", "eslint-plugin-html": "2.0.1", "eslint-plugin-vue": "2.0.1", "expect": "1.20.2", "express": "4.15.2", "karma": "1.5.0", "karma-chrome-launcher": "2.0.0", "karma-firefox-launcher": "1.0.1", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.3", "karma-opera-launcher": "1.0.0", "karma-phantomjs-launcher": "1.0.4", "karma-safari-launcher": "1.0.0", "karma-sauce-launcher": "1.1.0", "karma-sourcemap-loader": "0.3.7", "karma-webpack": "2.0.3", "mocha": "3.2.0", "rimraf": "2.6.1", "serve-index": "1.8.0", "webpack": "2.3.3"}, "dependencies": {"lodash.isplainobject": "4.0.6"}, "gitHead": "b135780318a25cf6be665178009671907741aa00", "homepage": "https://github.com/dwightjack/vue-types#readme", "_id": "vue-types@1.0.0", "_shasum": "906fbeb81fe30236366ee83e503335006ca19f4f", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "6.10.2", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "906fbeb81fe30236366ee83e503335006ca19f4f", "size": 14240, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.0.0.tgz", "integrity": "sha512-bJoGAWWOmAopS+g99hGX0l8IWilnk7zqGC4Di0tZHUc7X8Ty8PZbjF2f5S/D2haWjeSNHU+Bn4wOhpS6KNLRNQ=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types-1.0.0.tgz_1495910735422_0.38300911406986415"}, "directories": {}, "publish_time": 1495910736554, "_hasShrinkwrap": false, "_cnpm_publish_time": 1495910736554, "_cnpmcore_publish_time": "2021-12-16T16:05:19.377Z"}, "1.0.0-beta.1": {"name": "vue-types", "version": "1.0.0-beta.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "main": "dist/index.js", "jsnext:main": "es/index.js", "module": "es/index.js", "files": ["dist", "es", "src", "umd"], "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm test && npm run build:cjs && npm run build:es && npm run build:umd && npm run build:min", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=development webpack --config webpack.umd.js", "build:min": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=production webpack --config webpack.umd.js", "test": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers PhantomJS", "test:all": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers Chrome,Firefox,IE,Safari,Opera", "test:saucelab": "cross-env BABEL_ENV=cjs karma start karma-sauce.conf.js --single-run", "lint": "eslint src *.js", "start": "node ./server/index.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "devDependencies": {"babel-cli": "6.24.0", "babel-core": "6.24.0", "babel-loader": "6.4.1", "babel-plugin-add-module-exports": "0.2.1", "babel-preset-es2015": "6.24.0", "cross-env": "4.0.0", "eslint": "3.19.0", "eslint-config-vue": "2.0.2", "eslint-plugin-html": "2.0.1", "eslint-plugin-vue": "2.0.1", "expect": "1.20.2", "express": "4.15.2", "karma": "1.5.0", "karma-chrome-launcher": "2.0.0", "karma-firefox-launcher": "1.0.1", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.3", "karma-opera-launcher": "1.0.0", "karma-phantomjs-launcher": "1.0.4", "karma-safari-launcher": "1.0.0", "karma-sauce-launcher": "1.1.0", "karma-sourcemap-loader": "0.3.7", "karma-webpack": "2.0.3", "mocha": "3.2.0", "rimraf": "2.6.1", "serve-index": "1.8.0", "webpack": "2.3.3"}, "dependencies": {"lodash.isplainobject": "4.0.6"}, "gitHead": "2f5328013822ff6925945816b91cf8cfee150c59", "homepage": "https://github.com/dwightjack/vue-types#readme", "_id": "vue-types@1.0.0-beta.1", "_shasum": "87d5d816286ca1238ffbf6fdfb4e26c03cd6d8f1", "_from": ".", "_npmVersion": "4.4.1", "_nodeVersion": "6.10.0", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "87d5d816286ca1238ffbf6fdfb4e26c03cd6d8f1", "size": 13759, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-1.0.0-beta.1.tgz", "integrity": "sha512-/Lo2rWqCONaAzWFbIP1qDPi2RwkJM1f1DM29vsvUs6aU5yS9fA0EgB4R0cHIir7hZWhWYboibfPTXEM6e+F0pQ=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/vue-types-1.0.0-beta.1.tgz_1491298960561_0.7058957156259567"}, "directories": {}, "publish_time": 1491298961310, "_hasShrinkwrap": false, "_cnpm_publish_time": 1491298961310, "_cnpmcore_publish_time": "2021-12-16T16:05:19.623Z"}, "0.6.5": {"name": "vue-types", "version": "0.6.5", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "main": "dist/index.js", "jsnext:main": "es/index.js", "module": "es/index.js", "files": ["dist", "es", "src", "umd"], "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm test && npm run build:cjs && npm run build:es && npm run build:umd && npm run build:min", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=development webpack --config webpack.umd.js", "build:min": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=production webpack --config webpack.umd.js", "test": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers PhantomJS", "test:all": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers Chrome,Firefox,IE,Safari,Opera", "test:saucelab": "cross-env BABEL_ENV=cjs karma start karma-sauce.conf.js --single-run", "lint": "eslint src *.js", "start": "node ./server/index.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "devDependencies": {"babel-cli": "6.18.0", "babel-core": "6.18.0", "babel-loader": "6.2.7", "babel-plugin-add-module-exports": "0.2.1", "babel-preset-es2015": "6.18.0", "cross-env": "3.1.3", "eslint": "3.8.1", "eslint-config-vue": "1.1.0", "eslint-plugin-html": "1.5.5", "eslint-plugin-vue": "0.1.1", "expect": "1.20.2", "express": "4.14.0", "karma": "1.3.0", "karma-chrome-launcher": "2.0.0", "karma-firefox-launcher": "1.0.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.2.0", "karma-mocha-reporter": "2.2.0", "karma-opera-launcher": "1.0.0", "karma-phantomjs-launcher": "1.0.2", "karma-safari-launcher": "1.0.0", "karma-sauce-launcher": "1.1.0", "karma-sourcemap-loader": "0.3.7", "karma-webpack": "1.8.0", "mocha": "3.1.2", "rimraf": "2.5.4", "serve-index": "1.8.0", "webpack": "2.1.0-beta.25"}, "dependencies": {"lodash.isplainobject": "4.0.6", "object-assign": "4.1.0"}, "gitHead": "296b601f8092c838182d7fe9cc1117ef7f2c483a", "homepage": "https://github.com/dwightjack/vue-types#readme", "_id": "vue-types@0.6.5", "_shasum": "bb16d084f3d6fb0036a16b13a32e459194e3ef49", "_from": ".", "_npmVersion": "4.4.1", "_nodeVersion": "6.10.0", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "bb16d084f3d6fb0036a16b13a32e459194e3ef49", "size": 14272, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-0.6.5.tgz", "integrity": "sha512-8HmloGu3I0BcGvVUUKqtgSJbLXzhstl0gT+fKIM2a6WHOLlCow2BYQCSEnLJGz269lrbnHwEHT7KIGk+7tCWog=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/vue-types-0.6.5.tgz_1491234830214_0.9658586590085179"}, "directories": {}, "publish_time": 1491234830864, "_hasShrinkwrap": false, "_cnpm_publish_time": 1491234830864, "_cnpmcore_publish_time": "2021-12-16T16:05:19.842Z"}, "0.6.4": {"name": "vue-types", "version": "0.6.4", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "main": "dist/index.js", "jsnext:main": "es/index.js", "module": "es/index.js", "files": ["dist", "es", "src", "umd"], "scripts": {"prepublishOnly": "npm run build", "build": "npm run lint && npm test && npm run build:cjs && npm run build:es && npm run build:umd && npm run build:min", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=development webpack --config webpack.umd.js", "build:min": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=production webpack --config webpack.umd.js", "test": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers PhantomJS", "test:all": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers Chrome,Firefox,IE,Safari,Opera", "test:saucelab": "cross-env BABEL_ENV=cjs karma start karma-sauce.conf.js --single-run", "lint": "eslint src *.js", "start": "node ./server/index.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "devDependencies": {"babel-cli": "6.18.0", "babel-core": "6.18.0", "babel-loader": "6.2.7", "babel-plugin-add-module-exports": "0.2.1", "babel-preset-es2015": "6.18.0", "cross-env": "3.1.3", "eslint": "3.8.1", "eslint-config-vue": "1.1.0", "eslint-plugin-html": "1.5.5", "eslint-plugin-vue": "0.1.1", "expect": "1.20.2", "express": "4.14.0", "karma": "1.3.0", "karma-chrome-launcher": "2.0.0", "karma-firefox-launcher": "1.0.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.2.0", "karma-mocha-reporter": "2.2.0", "karma-opera-launcher": "1.0.0", "karma-phantomjs-launcher": "1.0.2", "karma-safari-launcher": "1.0.0", "karma-sauce-launcher": "1.1.0", "karma-sourcemap-loader": "0.3.7", "karma-webpack": "1.8.0", "mocha": "3.1.2", "rimraf": "2.5.4", "serve-index": "1.8.0", "webpack": "2.1.0-beta.25"}, "dependencies": {"lodash.isplainobject": "4.0.6", "object-assign": "4.1.0"}, "gitHead": "5e4679849b5ecbd9edea48f2571d5942b115c0c1", "homepage": "https://github.com/dwightjack/vue-types#readme", "_id": "vue-types@0.6.4", "_shasum": "98aa1ac2e7059661c92949a6c6849a2952c47664", "_from": ".", "_npmVersion": "4.4.1", "_nodeVersion": "6.10.0", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "98aa1ac2e7059661c92949a6c6849a2952c47664", "size": 14213, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-0.6.4.tgz", "integrity": "sha512-l6kjZ60b1KxcaNCqFgBEaQCJB5eQqS6kLLqyB/BzDPdwKp774L/pbcA+KyE/ahYA84L7qoHfpwI6jcPxulIpFg=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/vue-types-0.6.4.tgz_1491230011038_0.10556208249181509"}, "directories": {}, "publish_time": 1491230012947, "_hasShrinkwrap": false, "_cnpm_publish_time": 1491230012947, "_cnpmcore_publish_time": "2021-12-16T16:05:20.145Z"}, "0.6.2": {"name": "vue-types", "version": "0.6.2", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "main": "dist/index.js", "jsnext:main": "es/index.js", "module": "es/index.js", "files": ["dist", "es", "src", "umd"], "scripts": {"build": "npm run lint && npm test && npm run build:cjs && npm run build:es && npm run build:umd && npm run build:min", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=development webpack --config webpack.umd.js", "build:min": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=production webpack --config webpack.umd.js", "test": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers PhantomJS", "test:all": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers Chrome,Firefox,IE,Safari,Opera", "test:saucelab": "cross-env BABEL_ENV=cjs karma start karma-sauce.conf.js --single-run", "lint": "eslint src *.js", "server": "node ./server/index.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "devDependencies": {"babel-cli": "6.18.0", "babel-core": "6.18.0", "babel-loader": "6.2.7", "babel-plugin-add-module-exports": "0.2.1", "babel-preset-es2015": "6.18.0", "cross-env": "3.1.3", "eslint": "3.8.1", "eslint-config-vue": "1.1.0", "eslint-plugin-html": "1.5.5", "eslint-plugin-vue": "0.1.1", "expect": "1.20.2", "express": "4.14.0", "karma": "1.3.0", "karma-chrome-launcher": "2.0.0", "karma-firefox-launcher": "1.0.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.2.0", "karma-mocha-reporter": "2.2.0", "karma-opera-launcher": "1.0.0", "karma-phantomjs-launcher": "1.0.2", "karma-safari-launcher": "1.0.0", "karma-sauce-launcher": "1.1.0", "karma-sourcemap-loader": "0.3.7", "karma-webpack": "1.8.0", "mocha": "3.1.2", "rimraf": "2.5.4", "serve-index": "1.8.0", "webpack": "2.1.0-beta.25"}, "dependencies": {"lodash.isplainobject": "4.0.6", "object-assign": "4.1.0"}, "gitHead": "6dbcf2e429328b84c463ec04f96658d9e1bbf425", "homepage": "https://github.com/dwightjack/vue-types#readme", "_id": "vue-types@0.6.2", "_shasum": "efa5f6281086a79e0bf6f0a7f7ebf39bf5509d85", "_from": ".", "_npmVersion": "4.4.1", "_nodeVersion": "6.10.0", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "efa5f6281086a79e0bf6f0a7f7ebf39bf5509d85", "size": 14318, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-0.6.2.tgz", "integrity": "sha512-awsCD482eY9Hyk58SPKq9suGgtbg1gpBu/7tCh8nBDvTPly07uYI41nqQ8aXSbu9Yja2L91x56YNv7QOlaQA6w=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/vue-types-0.6.2.tgz_1490890089629_0.16229961882345378"}, "directories": {}, "publish_time": 1490890090721, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490890090721, "_cnpmcore_publish_time": "2021-12-16T16:05:20.357Z"}, "0.6.1": {"name": "vue-types", "version": "0.6.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "main": "dist/index.js", "jsnext:main": "es/index.js", "module": "es/index.js", "files": ["dist", "es", "src", "umd"], "scripts": {"build": "npm run lint && npm test && npm run build:cjs && npm run build:es && npm run build:umd && npm run build:min", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=development webpack --config webpack.umd.js", "build:min": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=production webpack --config webpack.umd.js", "test": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers PhantomJS", "test:all": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers Chrome,Firefox,IE,Safari,Opera", "test:saucelab": "cross-env BABEL_ENV=cjs karma start karma-sauce.conf.js --single-run", "lint": "eslint src *.js", "server": "node ./server/index.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "devDependencies": {"babel-cli": "6.18.0", "babel-core": "6.18.0", "babel-loader": "6.2.7", "babel-plugin-add-module-exports": "0.2.1", "babel-preset-es2015": "6.18.0", "cross-env": "3.1.3", "eslint": "3.8.1", "eslint-config-vue": "1.1.0", "eslint-plugin-html": "1.5.5", "eslint-plugin-vue": "0.1.1", "expect": "1.20.2", "express": "4.14.0", "karma": "1.3.0", "karma-chrome-launcher": "2.0.0", "karma-firefox-launcher": "1.0.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.2.0", "karma-mocha-reporter": "2.2.0", "karma-opera-launcher": "1.0.0", "karma-phantomjs-launcher": "1.0.2", "karma-safari-launcher": "1.0.0", "karma-sauce-launcher": "1.1.0", "karma-sourcemap-loader": "0.3.7", "karma-webpack": "1.8.0", "mocha": "3.1.2", "rimraf": "2.5.4", "serve-index": "1.8.0", "webpack": "2.1.0-beta.25"}, "dependencies": {"lodash.isplainobject": "4.0.6", "object-assign": "4.1.0"}, "gitHead": "c12457dfede3d0dfe370d793d103c9513f94ded4", "homepage": "https://github.com/dwightjack/vue-types#readme", "_id": "vue-types@0.6.1", "_shasum": "a5f9fd96590a5c4faf091d7275e699c1e5702f77", "_from": ".", "_npmVersion": "4.4.1", "_nodeVersion": "6.10.0", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "a5f9fd96590a5c4faf091d7275e699c1e5702f77", "size": 14263, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-0.6.1.tgz", "integrity": "sha512-gm8iYl/+HT8m4UVT9uqvGWrk308hqccS6m8NXKZYiO6ewNiSoMX6nCmQf15Z3ggVAoEkj6vYQSCoVZaWZr9awg=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/vue-types-0.6.1.tgz_1490888121124_0.21559531637467444"}, "directories": {}, "publish_time": 1490888123252, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490888123252, "_cnpmcore_publish_time": "2021-12-16T16:05:20.605Z"}, "0.6.0": {"name": "vue-types", "version": "0.6.0", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "main": "dist/index.js", "jsnext:main": "es/index.js", "module": "es/index.js", "files": ["dist", "es", "src", "umd"], "scripts": {"build": "npm run lint && npm test && npm run build:cjs && npm run build:es && npm run build:umd && npm run build:min", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=development webpack --config webpack.umd.js", "build:min": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=production webpack --config webpack.umd.js", "test": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers PhantomJS", "test:all": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers Chrome,Firefox,IE,Safari,Opera", "test:saucelab": "cross-env BABEL_ENV=cjs karma start karma-sauce.conf.js --single-run", "lint": "eslint src *.js", "server": "node ./server/index.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "devDependencies": {"babel-cli": "6.18.0", "babel-core": "6.18.0", "babel-loader": "6.2.7", "babel-plugin-add-module-exports": "0.2.1", "babel-preset-es2015": "6.18.0", "cross-env": "3.1.3", "eslint": "3.8.1", "eslint-config-vue": "1.1.0", "eslint-plugin-html": "1.5.5", "eslint-plugin-vue": "0.1.1", "expect": "1.20.2", "express": "4.14.0", "karma": "1.3.0", "karma-chrome-launcher": "2.0.0", "karma-firefox-launcher": "1.0.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.2.0", "karma-mocha-reporter": "2.2.0", "karma-opera-launcher": "1.0.0", "karma-phantomjs-launcher": "1.0.2", "karma-safari-launcher": "1.0.0", "karma-sauce-launcher": "1.1.0", "karma-sourcemap-loader": "0.3.7", "karma-webpack": "1.8.0", "mocha": "3.1.2", "rimraf": "2.5.4", "serve-index": "1.8.0", "webpack": "2.1.0-beta.25"}, "dependencies": {"lodash.isplainobject": "4.0.6", "object-assign": "4.1.0"}, "gitHead": "0b5d96772802927833a22a6c8862ec112d62cd60", "homepage": "https://github.com/dwightjack/vue-types#readme", "_id": "vue-types@0.6.0", "_shasum": "1c47598c94d43f6dd3ffa5c62af4c41be71314dc", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.0", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "1c47598c94d43f6dd3ffa5c62af4c41be71314dc", "size": 14211, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-0.6.0.tgz", "integrity": "sha512-bEPifFZffQ0yjjYd3IP0X3rpdoAfPLYiwN1s0OZ+5b3oTHmTmUTsSIpzvfhAPCttzQ3A5+N4dWD9Sby1aXjF6A=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/vue-types-0.6.0.tgz_1488211802240_0.20968188531696796"}, "directories": {}, "publish_time": 1488211802936, "_hasShrinkwrap": false, "_cnpm_publish_time": 1488211802936, "_cnpmcore_publish_time": "2021-12-16T16:05:20.889Z"}, "0.5.2": {"name": "vue-types", "version": "0.5.2", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "main": "dist/index.js", "jsnext:main": "es/index.js", "module": "es/index.js", "files": ["dist", "es", "src", "umd"], "scripts": {"build": "npm run lint && npm test && npm run build:cjs && npm run build:es && npm run build:umd && npm run build:min", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=development webpack --config webpack.umd.js", "build:min": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=production webpack --config webpack.umd.js", "test": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers PhantomJS", "test:all": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers Chrome,Firefox,IE,Safari,Opera", "test:saucelab": "cross-env BABEL_ENV=cjs karma start karma-sauce.conf.js --single-run", "lint": "eslint src *.js", "server": "node ./server/index.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "devDependencies": {"babel-cli": "6.18.0", "babel-core": "6.18.0", "babel-loader": "6.2.7", "babel-plugin-add-module-exports": "0.2.1", "babel-preset-es2015": "6.18.0", "cross-env": "3.1.3", "eslint": "3.8.1", "eslint-config-vue": "1.1.0", "eslint-plugin-html": "1.5.5", "eslint-plugin-vue": "0.1.1", "expect": "1.20.2", "express": "4.14.0", "karma": "1.3.0", "karma-chrome-launcher": "2.0.0", "karma-firefox-launcher": "1.0.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.2.0", "karma-mocha-reporter": "2.2.0", "karma-opera-launcher": "1.0.0", "karma-phantomjs-launcher": "1.0.2", "karma-safari-launcher": "1.0.0", "karma-sauce-launcher": "1.1.0", "karma-sourcemap-loader": "0.3.7", "karma-webpack": "1.8.0", "mocha": "3.1.2", "rimraf": "2.5.4", "serve-index": "1.8.0", "webpack": "2.1.0-beta.25"}, "dependencies": {"lodash.isplainobject": "4.0.6", "object-assign": "4.1.0"}, "gitHead": "352470230b0738bfd2b4bc67230099ae1bee857c", "homepage": "https://github.com/dwightjack/vue-types#readme", "_id": "vue-types@0.5.2", "_shasum": "6fe5a7a2e63821d01eacdffb24e8a9b349d981aa", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.1", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "6fe5a7a2e63821d01eacdffb24e8a9b349d981aa", "size": 11365, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-0.5.2.tgz", "integrity": "sha512-IoW4b+o77q8pXavXYbMPNhMaSfgvO9nwmLXfCRmJkGWcwfSNAZlhYARRTO4iJYBuRxJMABkbyjHOWdo/qC3Pyg=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/vue-types-0.5.2.tgz_1479115920853_0.11566721461713314"}, "directories": {}, "publish_time": 1479115921439, "_hasShrinkwrap": false, "_cnpm_publish_time": 1479115921439, "_cnpmcore_publish_time": "2021-12-16T16:05:21.171Z"}, "0.5.1": {"name": "vue-types", "version": "0.5.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "main": "dist/index.js", "jsnext:main": "es/index.js", "module": "es/index.js", "files": ["dist", "es", "src"], "scripts": {"build": "npm run lint && npm test && npm run build:cjs && npm run build:es && npm run build:umd && npm run build:min", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=development webpack --config webpack.umd.js", "build:min": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=production webpack --config webpack.umd.js", "test": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers PhantomJS", "test:all": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers Chrome,Firefox,IE,Safari,Opera", "test:saucelab": "cross-env BABEL_ENV=cjs karma start karma-sauce.conf.js --single-run", "lint": "eslint src *.js", "server": "node ./server/index.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "devDependencies": {"babel-cli": "6.18.0", "babel-core": "6.18.0", "babel-loader": "6.2.7", "babel-plugin-add-module-exports": "0.2.1", "babel-preset-es2015": "6.18.0", "cross-env": "3.1.3", "eslint": "3.8.1", "eslint-config-vue": "1.1.0", "eslint-plugin-html": "1.5.5", "eslint-plugin-vue": "0.1.1", "expect": "1.20.2", "express": "4.14.0", "karma": "1.3.0", "karma-chrome-launcher": "2.0.0", "karma-firefox-launcher": "1.0.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.2.0", "karma-mocha-reporter": "2.2.0", "karma-opera-launcher": "1.0.0", "karma-phantomjs-launcher": "1.0.2", "karma-safari-launcher": "1.0.0", "karma-sauce-launcher": "1.1.0", "karma-sourcemap-loader": "0.3.7", "karma-webpack": "1.8.0", "mocha": "3.1.2", "rimraf": "2.5.4", "serve-index": "1.8.0", "webpack": "2.1.0-beta.25"}, "dependencies": {"lodash.isplainobject": "4.0.6", "object-assign": "4.1.0"}, "gitHead": "96f4ddd786c5c4e5fb4c1846e4a027ddc6b843d5", "homepage": "https://github.com/dwightjack/vue-types#readme", "_id": "vue-types@0.5.1", "_shasum": "697d113e22f4395fc2903c6e9b59c9135bd6851b", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.1", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "697d113e22f4395fc2903c6e9b59c9135bd6851b", "size": 6712, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-0.5.1.tgz", "integrity": "sha512-2xvMyVdCoNJ8o1czmfdhdyefk5zMJ2xFv18KOTaGdVIfuiobz3cnZjzIqY5EuLjxj+1HRqTY4BtctXkdRJXFAw=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/vue-types-0.5.1.tgz_1479115693479_0.45489502139389515"}, "directories": {}, "publish_time": 1479115694010, "_hasShrinkwrap": false, "_cnpm_publish_time": 1479115694010, "_cnpmcore_publish_time": "2021-12-16T16:05:21.375Z"}, "0.5.0": {"name": "vue-types", "version": "0.5.0", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "main": "dist/index.js", "jsnext:main": "es/index.js", "module": "es/index.js", "files": ["dist", "es", "src"], "scripts": {"build": "npm run lint && npm test && npm run build:cjs && npm run build:es && npm run build:umd && npm run build:min", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=development webpack --config webpack.umd.js", "build:min": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=production webpack --config webpack.umd.js", "test": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers PhantomJS", "test:all": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers Chrome,Firefox,IE,Safari,Opera", "test:saucelab": "cross-env BABEL_ENV=cjs karma start karma-sauce.conf.js --single-run", "lint": "eslint src *.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "devDependencies": {"babel-cli": "6.18.0", "babel-core": "6.18.0", "babel-loader": "6.2.7", "babel-plugin-add-module-exports": "0.2.1", "babel-preset-es2015": "6.18.0", "cross-env": "3.1.3", "eslint": "3.8.1", "eslint-config-vue": "1.1.0", "eslint-plugin-html": "1.5.5", "eslint-plugin-vue": "0.1.1", "expect": "1.20.2", "karma": "1.3.0", "karma-chrome-launcher": "2.0.0", "karma-firefox-launcher": "1.0.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.2.0", "karma-mocha-reporter": "2.2.0", "karma-opera-launcher": "1.0.0", "karma-phantomjs-launcher": "1.0.2", "karma-safari-launcher": "1.0.0", "karma-sauce-launcher": "1.1.0", "karma-sourcemap-loader": "0.3.7", "karma-webpack": "1.8.0", "mocha": "3.1.2", "rimraf": "2.5.4", "webpack": "2.1.0-beta.25"}, "dependencies": {"lodash.isplainobject": "4.0.6", "object-assign": "4.1.0"}, "gitHead": "d0f99f55e0a679def0d4596cfd1c99bff7b2003a", "homepage": "https://github.com/dwightjack/vue-types#readme", "_id": "vue-types@0.5.0", "_shasum": "a8b82fba346d74d972ff48d46dc498c845b6a7ef", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.1", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "a8b82fba346d74d972ff48d46dc498c845b6a7ef", "size": 6597, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-0.5.0.tgz", "integrity": "sha512-t7Yqn1hAlo6TQGr7qroXPZP4nBDDoBxxC8qvLp4Cq1PyzDitDn9E+iEjtsUTeLfPbwXc6LeDoP+XF7I916UQAA=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/vue-types-0.5.0.tgz_1478857519319_0.5795847631525248"}, "directories": {}, "publish_time": 1478857521426, "_hasShrinkwrap": false, "_cnpm_publish_time": 1478857521426, "_cnpmcore_publish_time": "2021-12-16T16:05:21.591Z"}, "0.4.0": {"name": "vue-types", "version": "0.4.0", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "main": "dist/index.js", "jsnext:main": "es/index.js", "module": "es/index.js", "files": ["dist", "es", "src"], "scripts": {"build": "npm run lint && npm test && npm run build:cjs && npm run build:es && npm run build:umd && npm run build:min", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=development webpack --config webpack.umd.js", "build:min": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=production webpack --config webpack.umd.js", "test": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers PhantomJS", "test:all": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers Chrome,Firefox,IE,Safari,Opera", "test:saucelab": "cross-env BABEL_ENV=cjs karma start karma-sauce.conf.js --single-run", "lint": "eslint src *.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "devDependencies": {"babel-cli": "6.18.0", "babel-core": "6.18.0", "babel-loader": "6.2.7", "babel-plugin-add-module-exports": "0.2.1", "babel-preset-es2015": "6.18.0", "cross-env": "3.1.3", "eslint": "3.8.1", "eslint-config-vue": "1.1.0", "eslint-plugin-html": "1.5.5", "eslint-plugin-vue": "0.1.1", "expect": "1.20.2", "karma": "1.3.0", "karma-chrome-launcher": "2.0.0", "karma-firefox-launcher": "1.0.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.2.0", "karma-mocha-reporter": "2.2.0", "karma-opera-launcher": "1.0.0", "karma-phantomjs-launcher": "1.0.2", "karma-safari-launcher": "1.0.0", "karma-sourcemap-loader": "0.3.7", "karma-webpack": "1.8.0", "mocha": "3.1.2", "rimraf": "2.5.4", "webpack": "2.1.0-beta.25"}, "dependencies": {"lodash.isplainobject": "4.0.6", "object-assign": "4.1.0"}, "gitHead": "553c08505bd723b4792c04c20ef68affbc4065f0", "homepage": "https://github.com/dwightjack/vue-types#readme", "_id": "vue-types@0.4.0", "_shasum": "09be378d5fd910891bf33e6e8745fa3f4b19c128", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.1", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "09be378d5fd910891bf33e6e8745fa3f4b19c128", "size": 6440, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-0.4.0.tgz", "integrity": "sha512-SbBu/JWcoO9FMvkrMgR4UrpcPG0EFBF0kaoQXQv2OnubAFRCyIT0R5fL/7PRMe4XywV5QKoszE0wKvks3L4uzg=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/vue-types-0.4.0.tgz_1477749778145_0.6888070323038846"}, "directories": {}, "publish_time": 1477749778721, "_hasShrinkwrap": false, "_cnpm_publish_time": 1477749778721, "_cnpmcore_publish_time": "2021-12-16T16:05:21.808Z"}, "0.3.0": {"name": "vue-types", "version": "0.3.0", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "main": "dist/index.js", "jsnext:main": "es/index.js", "module": "es/index.js", "files": ["dist", "es", "src"], "scripts": {"build": "npm run lint && npm test && npm run build:cjs && npm run build:es && npm run build:umd && npm run build:min", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=development webpack --config webpack.umd.js", "build:min": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=production webpack --config webpack.umd.js", "test": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers PhantomJS", "test:all": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers Chrome,Firefox,IE,Safari,Opera", "test:saucelab": "cross-env BABEL_ENV=cjs karma start karma-sauce.conf.js --single-run", "lint": "eslint src *.js"}, "keywords": ["vue", "props"], "devDependencies": {"babel-cli": "6.18.0", "babel-core": "6.18.0", "babel-loader": "6.2.7", "babel-plugin-add-module-exports": "0.2.1", "babel-polyfill": "6.16.0", "babel-preset-es2015": "6.18.0", "cross-env": "3.1.3", "eslint": "3.8.1", "eslint-config-vue": "1.1.0", "eslint-plugin-html": "1.5.5", "eslint-plugin-vue": "0.1.1", "expect": "1.20.2", "karma": "1.3.0", "karma-chrome-launcher": "2.0.0", "karma-firefox-launcher": "1.0.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.2.0", "karma-mocha-reporter": "2.2.0", "karma-opera-launcher": "1.0.0", "karma-phantomjs-launcher": "1.0.2", "karma-safari-launcher": "1.0.0", "karma-sourcemap-loader": "0.3.7", "karma-webpack": "1.8.0", "mocha": "3.1.2", "rimraf": "2.5.4", "webpack": "2.1.0-beta.25"}, "dependencies": {"lodash.isplainobject": "4.0.6", "object-assign": "4.1.0"}, "gitHead": "39c6a22abf41d404c2e263d7fb3f6b3917af1e54", "_id": "vue-types@0.3.0", "_shasum": "b0049cbd2c00cd8081be781ba77421f2f6e587d1", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.1", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "b0049cbd2c00cd8081be781ba77421f2f6e587d1", "size": 6396, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-0.3.0.tgz", "integrity": "sha512-WKmA8jO31RKEXXeDtpn1SUBkvq0teSHpm6/fNAA3Oz4nh7DQGwPGzXIAEcdpl5ugeNMGRIZbtWrF5/4LxsjG7Q=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/vue-types-0.3.0.tgz_1477748641228_0.7186208600178361"}, "directories": {}, "publish_time": 1477748641768, "_hasShrinkwrap": false, "_cnpm_publish_time": 1477748641768, "_cnpmcore_publish_time": "2021-12-16T16:05:22.112Z"}, "0.2.0": {"name": "vue-types", "version": "0.2.0", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "main": "dist/index.js", "jsnext:main": "es/index.js", "module": "es/index.js", "files": ["dist", "es", "src"], "scripts": {"build": "npm run lint && npm test && npm run build:cjs && npm run build:es && npm run build:umd && npm run build:min", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=development webpack --config webpack.umd.js", "build:min": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=production webpack --config webpack.umd.js", "test": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers PhantomJS", "test:all": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers Chrome,Firefox,IE,Safari,Opera", "lint": "eslint src *.js"}, "keywords": ["vue", "props"], "devDependencies": {"babel-cli": "6.18.0", "babel-core": "6.18.0", "babel-loader": "6.2.7", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-transform-runtime": "6.15.0", "babel-polyfill": "6.16.0", "babel-preset-es2015": "6.18.0", "babel-runtime": "6.18.0", "cross-env": "3.1.3", "eslint": "3.8.1", "eslint-config-vue": "1.1.0", "eslint-plugin-html": "1.5.5", "eslint-plugin-vue": "0.1.1", "expect": "1.20.2", "karma": "1.3.0", "karma-chrome-launcher": "2.0.0", "karma-firefox-launcher": "1.0.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.2.0", "karma-mocha-reporter": "2.2.0", "karma-opera-launcher": "1.0.0", "karma-phantomjs-launcher": "1.0.2", "karma-safari-launcher": "1.0.0", "karma-sourcemap-loader": "0.3.7", "karma-webpack": "1.8.0", "mocha": "3.1.2", "rimraf": "2.5.4", "webpack": "2.1.0-beta.25"}, "dependencies": {"lodash.isplainobject": "4.0.6"}, "gitHead": "3ed7121c42b759df0e4b368c9255965e86415e8b", "_id": "vue-types@0.2.0", "_shasum": "8f893f5038ff56f30475fa3da8f53fb98c402395", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.1", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "8f893f5038ff56f30475fa3da8f53fb98c402395", "size": 5888, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-0.2.0.tgz", "integrity": "sha512-bnOG2mL4uQoLEIKauDyIq4P6hDkr6l0xKfStD8VValsZ0Ue8tsspW1zY/l6wIISOMljdUaqDaMRcRpzsgSZzPA=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/vue-types-0.2.0.tgz_1477670163043_0.42925416748039424"}, "directories": {}, "publish_time": 1477670165848, "_hasShrinkwrap": false, "_cnpm_publish_time": 1477670165848, "_cnpmcore_publish_time": "2021-12-16T16:05:22.320Z"}, "0.1.0": {"name": "vue-types", "version": "0.1.0", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "main": "dist/index.js", "jsnext:main": "es/index.js", "module": "es/index.js", "files": ["dist", "es", "src"], "scripts": {"build": "npm run lint && npm test && npm run build:cjs && npm run build:es && npm run build:umd && npm run build:min", "build:cjs": "rimraf dist && cross-env BABEL_ENV=cjs babel ./src -d dist", "build:es": "rimraf es && cross-env BABEL_ENV=es babel ./src -d es", "build:umd": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=development webpack --config webpack.umd.js", "build:min": "cross-env BABEL_ENV=cjs cross-env NODE_ENV=production webpack --config webpack.umd.js", "test": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers PhantomJS", "test:all": "cross-env BABEL_ENV=cjs karma start karma.conf.js --single-run --browsers Chrome,Firefox,IE,Safari,Opera", "lint": "eslint src *.js"}, "keywords": ["vue", "props"], "devDependencies": {"babel-cli": "6.18.0", "babel-core": "6.18.0", "babel-loader": "6.2.7", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-transform-runtime": "6.15.0", "babel-polyfill": "6.16.0", "babel-preset-es2015": "6.18.0", "babel-runtime": "6.18.0", "cross-env": "3.1.3", "eslint": "3.8.1", "eslint-config-vue": "1.1.0", "eslint-plugin-html": "1.5.5", "eslint-plugin-vue": "0.1.1", "expect": "1.20.2", "karma": "1.3.0", "karma-chrome-launcher": "2.0.0", "karma-firefox-launcher": "1.0.0", "karma-ie-launcher": "1.0.0", "karma-mocha": "1.2.0", "karma-mocha-reporter": "2.2.0", "karma-opera-launcher": "1.0.0", "karma-phantomjs-launcher": "1.0.2", "karma-safari-launcher": "1.0.0", "karma-sourcemap-loader": "0.3.7", "karma-webpack": "1.8.0", "mocha": "3.1.2", "rimraf": "2.5.4", "webpack": "2.1.0-beta.25"}, "dependencies": {"lodash.isplainobject": "4.0.6"}, "gitHead": "4de07ff859053db10ba64c4384b9ae8837d0275a", "_id": "vue-types@0.1.0", "_shasum": "2778e354136de5ef00c29e73bd4cdf39066176b1", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.1", "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "dist": {"shasum": "2778e354136de5ef00c29e73bd4cdf39066176b1", "size": 5899, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-0.1.0.tgz", "integrity": "sha512-8f1BofYsMhjMMA6WhSMYVIzzL+bfT9x8IArjAIKshRd5o87PKkxvGzdLozuyeYa4C9eJlif8Lk+H0jyEOaubwA=="}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/vue-types-0.1.0.tgz_1477669278441_0.3968394894618541"}, "directories": {}, "publish_time": 1477669280319, "_hasShrinkwrap": false, "_cnpm_publish_time": 1477669280319, "_cnpmcore_publish_time": "2021-12-16T16:05:22.553Z"}, "4.2.0-rc.1": {"name": "vue-types", "version": "4.2.0-rc.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"require": "./shim/index.cjs.js", "import": "./shim/index.modern.js"}, "./nuxt": "./nuxt/module.js"}, "types": "dist/index.d.ts", "engines": {"node": ">=12.16.0"}, "scripts": {"prepublishOnly": "run-s lint lint:ts test build", "build": "run-s 'clean:*' copy 'build:**'", "clean:dist": "del dist", "clean:shim": "del \"shim/*.*\" \"!shim/package.json\"", "copy": "cpy src/*.d.ts dist", "build:ts": "microbundle --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json --format=umd", "build:shim:ts": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.ts -o shim/index.js --format=modern,es --no-sourcemap", "build:shim:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.cjs.js --no-pkg-main --format=cjs --no-sourcemap", "build:shim:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.js --format=umd --no-sourcemap", "test": "jest", "lint": "run-s lint:*", "lint:ts": "tsc --noEmit -p ./examples", "lint:src": "eslint '{examples,src,__tests__,.}/**/*.{ts,js,cjs}'", "examples": "vite examples -c examples/vite.config.js", "docs:dev": "vuepress dev docs", "docs:build": "cross-env NODE_ENV=production vuepress build docs"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.16.7", "@babel/plugin-proposal-optional-chaining": "7.16.7", "@nuxt/types": "2.15.8", "@types/jest": "27.4.0", "@types/node": "16.11.21", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "babel-plugin-transform-node-env-inline": "0.4.3", "cpy-cli": "3.1.1", "cross-env": "7.0.3", "del": "6.0.0", "del-cli": "4.0.1", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "4.0.0", "eslint-plugin-vue": "8.3.0", "jest": "27.4.7", "microbundle": "0.14.2", "npm-run-all": "4.1.5", "prettier": "2.5.1", "ts-jest": "27.1.3", "typescript": "4.5.5", "vite": "2.7.13", "vue2": "npm:vue@2.6.14", "vue3": "npm:vue@3.2.28", "vuepress": "1.9.7"}, "dependencies": {"is-plain-object": "5.0.0"}, "readmeFilename": "README.md", "gitHead": "28e1d3f2afd41ceec55b3dd9367a2518eaef4a76", "_id": "vue-types@4.2.0-rc.1", "_nodeVersion": "16.14.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-6ufbn9dbqHf2p12rX0JnD2E2VEItnmKwrTZTl9IdcPu8R4BG3mSKb7//EjS0UCHihdz9qJbCXmmGHkHm+aDeRg==", "shasum": "27008769ddc669bfa006d93aa78ee321e2ed65e5", "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-4.2.0-rc.1.tgz", "fileCount": 55, "unpackedSize": 312424, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiH0E4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqD6Q//dT1jPVHB5QMpX7RytHs86XSuejnAc/lqi84RX0aPpiDaWduv\r\najHhc8bsXqdcSncAMKsSFws/OzfarzLDn/oDenBpaGIAK0s0Y2tJluc+NYuk\r\nA4ZT4E9GM777Id1zRA4bokTcG0dzWjtf31Y3DO2NbCOhzwe01/yX7LLlGutJ\r\nSMmP6hJOvyw2sj6YoJitzSpfrquslg4pD/oPfF8XBxeN8FO/hS9IiKg7X0au\r\nERN3gsRj20o2CxmE+AA1ik14U1i3tjGAhXkbx8VRbpmPSQpttsOWODTqL2kU\r\npIepqkQFh1F8XzP6zZjAmpwRnHVZTFfNiOfWkEo1BWbX6hTYqk3vwMmye5+x\r\nXsBZuwWbzUPoinrhX/35fE/s5KScuwjqExcsoUyrHXIjrlIg1zWeRPzsVy/H\r\nOdasnvJxcLYeOF4Hxv9NNnlgcoRktRy+nWgE9f7jAFqn0H3KjLIWA2Wjtkvm\r\njGCX6DKvYwbheed/+jio5uoWgCrsrs2adaxr/ImFn80xIBxfAFHYnPMWuJ9/\r\nNL4N6K8ZyRpM/OJhfbMZFnyHQigaSkK666yERkWjBWFtrZIMD6FAxSQ5ahB/\r\nLztmBbBmzKOVJ8WF9tFCGjU8thuB6CzwazqLFh85EvwDaak+NH5c8ZPPR163\r\nj3VB54icdgST24F3hEckEN5zXRPOlPxGtFY=\r\n=TQkM\r\n-----END PGP SIGNATURE-----\r\n", "size": 74239}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_4.2.0-rc.1_1646215480456_0.3975929437702246"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-02T10:04:59.703Z"}, "4.2.0": {"name": "vue-types", "version": "4.2.0", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"require": "./shim/index.cjs.js", "import": "./shim/index.modern.js"}, "./nuxt": "./nuxt/module.js"}, "types": "dist/index.d.ts", "engines": {"node": ">=12.16.0"}, "scripts": {"prepublishOnly": "run-s lint lint:ts test build", "build": "run-s 'clean:*' copy 'build:**'", "clean:dist": "del dist", "clean:shim": "del \"shim/*.*\" \"!shim/package.json\"", "copy": "cpy src/*.d.ts dist", "build:ts": "microbundle --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json --format=umd", "build:shim:ts": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.ts -o shim/index.js --format=modern,es --no-sourcemap", "build:shim:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.cjs.js --no-pkg-main --format=cjs --no-sourcemap", "build:shim:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.js --format=umd --no-sourcemap", "test": "vitest run", "lint": "run-s lint:*", "lint:ts": "tsc --noEmit -p ./examples", "lint:src": "eslint '{examples,src,__tests__,.}/**/*.{ts,js,cjs}'", "examples": "vite examples -c examples/vite.config.js", "docs:dev": "vuepress dev docs", "docs:build": "cross-env NODE_ENV=production vuepress build docs"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.16.7", "@babel/plugin-proposal-optional-chaining": "7.16.7", "@nuxt/types": "2.15.8", "@types/node": "16.11.45", "@typescript-eslint/eslint-plugin": "5.21.0", "@typescript-eslint/parser": "5.21.0", "babel-plugin-transform-node-env-inline": "0.4.3", "c8": "7.11.3", "cpy-cli": "4.1.0", "cross-env": "7.0.3", "del": "6.0.0", "del-cli": "5.0.0", "eslint": "8.14.0", "eslint-config-prettier": "8.5.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "4.0.0", "eslint-plugin-vue": "8.7.1", "happy-dom": "6.0.4", "microbundle": "0.15.0", "npm-run-all": "4.1.5", "prettier": "2.6.2", "typescript": "4.6.4", "vite": "2.9.14", "vitest": "0.10.5", "vue2": "npm:vue@2.6.14", "vue3": "npm:vue@3.2.37", "vuepress": "1.9.7"}, "dependencies": {"is-plain-object": "5.0.0"}, "gitHead": "ed96f724a2c678a0adacad0f64967ccfa96754ba", "_id": "vue-types@4.2.0", "_nodeVersion": "16.16.0", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-iMllg63ZQ61DP7C2ZR1Ez7D6hqcP5/pcNP9SgXg6Nmrt82r2Mx06+xEw7GRrrysR6Sfoh3w+TLLflMTk6I/D0A==", "shasum": "db054e242a099f006c84d62fd4ab831767accf18", "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-4.2.0.tgz", "fileCount": 57, "unpackedSize": 313213, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB/iGj2DZ1g4Q6i6pKDNcST7lAHUURFEYO/B6cgOMhknAiAn8KZ14FztoIKy1r4Ysn5ekxAteu3c4CZFmQs9yxiKDA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi214pACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpnLw/+JeI2Wgjl4Wn+PBgK6oa+D4zqWcGX49xt0MjrofmH7TldNxXt\r\nM1syEWBkU9/TC/OqtJxmvecYNRHf2a4YQflcFlFhdtoIINYoejI/H07MWf+o\r\nyaG19gxLDSGkTR12m/SWhkg+tAOABKgLWW8eD76S2dRiCS/bvxk+p7wzalVh\r\nFNk6/TgQAeJGmRuxFybA8/KeNoQrHLEXRlocZEeIJO3asU/lSR13SHxG+KKh\r\nh2CKJJQa9VaiuIEfeIWgg7MW7GPi3AOY3EOglg+cLF1Ldfag0UgeSz9EF4HY\r\ncezRZwBPuwd+CpxEqgGEW5pDBNuivnIrdcVPsluUcsV3oRD3Hd6c7OOVFNdH\r\n/LYWgj9aAgGaRNJCjFJx7XAoanvKN+2Pjd/wiJDAoqXznA9TGfh08XJJxrIV\r\nlkr3OC4M2xoMd4PnmX6+owjNC85+GSaG45iYSvrxeafBVwZR18lMHhTE5yQ6\r\nJchn252MM8GsZagc+laa3JOJRzkQfVprohMpvXEGKN5VnJrmyznqWbOtAruA\r\nmtb8oxaawaMrr2h0fMJgknHgvyQPBoUzBBUbKeKuA4Wj3AJiKdJBOANMt3ZR\r\niqVYYZRyAsN7AojgVo/qZuGQZEP1B9nhN98A6UHwcQLbHS995FU6pSPCFaEK\r\nQKyrDs2irOtX+pZ74qRS+YA2Ps9cxNbZAMk=\r\n=1VxO\r\n-----END PGP SIGNATURE-----\r\n", "size": 74592}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_4.2.0_1658543656761_0.6138170423863882"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-23T02:36:38.957Z"}, "4.2.1": {"name": "vue-types", "version": "4.2.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"require": "./shim/index.cjs.js", "import": "./shim/index.modern.js"}, "./nuxt": "./nuxt/module.js"}, "types": "dist/index.d.ts", "engines": {"node": ">=12.16.0"}, "scripts": {"prepublishOnly": "run-s lint lint:ts test build", "build": "run-s 'clean:*' copy 'build:**'", "clean:dist": "del dist", "clean:shim": "del \"shim/*.*\" \"!shim/package.json\"", "copy": "cpy --flat src/*.d.ts dist", "build:ts": "microbundle --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json --format=umd", "build:shim:ts": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.ts -o shim/index.js --format=modern,es --no-sourcemap", "build:shim:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.cjs.js --no-pkg-main --format=cjs --no-sourcemap", "build:shim:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.js --format=umd --no-sourcemap", "test": "vitest run", "lint": "run-s lint:*", "lint:ts": "tsc --noEmit -p ./examples", "lint:src": "eslint '{examples,src,__tests__,.}/**/*.{ts,js,cjs}'", "examples": "vite examples -c examples/vite.config.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.16.7", "@babel/plugin-proposal-optional-chaining": "7.16.7", "@nuxt/types": "2.15.8", "@types/node": "16.11.47", "@typescript-eslint/eslint-plugin": "5.21.0", "@typescript-eslint/parser": "5.21.0", "babel-plugin-transform-node-env-inline": "0.4.3", "c8": "7.11.3", "cpy-cli": "4.1.0", "cross-env": "7.0.3", "del": "6.0.0", "del-cli": "5.0.0", "eslint": "8.14.0", "eslint-config-prettier": "8.5.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "4.0.0", "eslint-plugin-vue": "8.7.1", "happy-dom": "6.0.4", "microbundle": "0.15.0", "npm-run-all": "4.1.5", "prettier": "2.6.2", "typescript": "4.6.4", "vite": "2.9.14", "vitest": "0.10.5", "vue2": "npm:vue@2.6.14", "vue3": "npm:vue@3.2.37"}, "dependencies": {"is-plain-object": "5.0.0"}, "gitHead": "f8448fca0bf0cb84d5b28dad4da04a4dd3a1666a", "_id": "vue-types@4.2.1", "_nodeVersion": "16.16.0", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-DNQZmJuOvovLUIp0BENRkdnZHbI0V4e2mNvjAZOAXKD56YGvRchtUYOXA/XqTxdv7Ng5SJLZqRKRpAhm5NLaPQ==", "shasum": "f8f7e5fb42d4a6acda6d92c9736b510e5534c753", "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-4.2.1.tgz", "fileCount": 57, "unpackedSize": 323803, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGsOvLBKSFfCwfF7HV6cLbQbapqMeOWUH5yS2Mokqho1AiBo5pEpYNftAQL6qIqb2blrRJDKQy0bU0oUj+6xHbPw7Q=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi52zgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoTSw/+MlPNDXx5kyuYAg2O/N5cIiGFPntjiHWByo4ticFl09VUIwDu\r\nqqLfnQyhjpHqoYFSxoWgpu5WVv/HJZWI4ll6TyydY+lK5ImZdR7/lMeL7ket\r\nvHchix0wR4S3fUdwnovDyv+jQpdfOdJ6CaNR2FrDYO71uSu8+7LtDVDgaP3d\r\nDBvVUwLmDRs/XiwqCwhZ2JaIuY8hMdpfbm9qUDN69jCavexx5jATI6HZpLm+\r\naxIzgcEHWOnWQdWMa4BGdYGnZQrDv0mXewNjzpXipnazxLOjYseDNlP2/w/+\r\nblQI4uM+O5SGm5oQroLgTiKMU+0OV4yaMUnjOPG3VTKTBhsHlOSWwEaT7F3H\r\nqH9KUij0nJtORBZXkWO9wJgIg5FL9OksEtxF3CswsLOQdb40EwuP14LrNlGJ\r\nD78TqJlTU2iBuwJ6IMe8KarkNClpRB1u249GtqWgS3XLGQpJp7EE/CA0el8U\r\nXP07tHPL5+IBFpNtKk+nIbPnZ9hua0qexZFTShusitGjwabE99TILf7YPKv8\r\nsCTF1qYLukJeI0McljXVZY14FSNWYp/4gDeldC13VEHw2j+XbIlVgujMj/v6\r\nAXtaHt1BCzfoLxrK8OJvJIohjZ8YqIRQaxPtJxC5VMWXlQIZ/C8ZHpgW8nRx\r\nUH+2/rn0gtfwyUJz5/UCpVZsGyQTvmy82f8=\r\n=2N3J\r\n-----END PGP SIGNATURE-----\r\n", "size": 79556}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_4.2.1_1659333856464_0.6762558337280973"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-08-01T06:04:24.612Z"}, "5.0.0-beta.1": {"name": "vue-types", "version": "5.0.0-beta.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"require": "./shim/index.cjs.js", "import": "./shim/index.modern.js"}, "./nuxt": "./nuxt/module.js"}, "types": "dist/index.d.ts", "engines": {"node": ">=12.16.0"}, "scripts": {"prepublishOnly": "run-s lint lint:ts test build", "build": "run-s 'clean:*' copy 'build:**'", "clean:dist": "del dist", "clean:shim": "del \"shim/*.*\" \"!shim/package.json\"", "copy": "cpy --flat src/*.d.ts dist", "build:ts": "microbundle --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json --format=umd", "build:shim:ts": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.ts -o shim/index.js --format=modern,es --no-sourcemap", "build:shim:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.cjs.js --no-pkg-main --format=cjs --no-sourcemap", "build:shim:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.js --format=umd --no-sourcemap", "test": "vitest run", "lint": "run-s lint:*", "lint:ts": "tsc --noEmit -p ./examples", "lint:src": "eslint '{examples,src,__tests__,.}/**/*.{ts,js,cjs}'", "examples": "vite examples -c examples/vite.config.js"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.16.7", "@babel/plugin-proposal-optional-chaining": "7.16.7", "@nuxt/types": "2.15.8", "@types/node": "16.11.47", "@typescript-eslint/eslint-plugin": "5.21.0", "@typescript-eslint/parser": "5.21.0", "babel-plugin-transform-node-env-inline": "0.4.3", "c8": "7.11.3", "cpy-cli": "4.1.0", "cross-env": "7.0.3", "del": "6.0.0", "del-cli": "5.0.0", "eslint": "8.14.0", "eslint-config-prettier": "8.5.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "4.0.0", "eslint-plugin-vue": "8.7.1", "happy-dom": "6.0.4", "microbundle": "0.15.0", "npm-run-all": "4.1.5", "prettier": "2.6.2", "typescript": "4.6.4", "vite": "2.9.14", "vitest": "0.10.5", "vue2": "npm:vue@2.6.14", "vue3": "npm:vue@3.2.37"}, "dependencies": {"is-plain-object": "5.0.0"}, "readmeFilename": "README.md", "gitHead": "27280fc0d231ec20fea27020eb7d758bd7aea5a0", "_id": "vue-types@5.0.0-beta.1", "_nodeVersion": "16.16.0", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-k+8kop9PvZ5MC9190LpifH8QHZt+bs3wmVsCTgQXTh2HsJTlS5ze4wrDalstRP5+vCi2I6sn8bgOAyw+NSgoFg==", "shasum": "3f613d75584b9db2a70fd0b5ae6b02a452a0bc0d", "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-5.0.0-beta.1.tgz", "fileCount": 40, "unpackedSize": 289173, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDaf9V4o8HJuqDLFl9Pwsf4BGFChxMkS40XV+lsJgnUQgIgWiBIfmdT0lXVu9+LAAAsE5LGpeCclMXAf3/ai0SLPio="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi8KMMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp3nRAAg70+e6bv/RzmUPGB3/LSO10d0XJxfiVuZA3Nr9nQIcvzRsPw\r\nj9DtDwuwbdQzXISJ7DMMuFUVHUTsPcK6bQKX0rtZ1xx3BR6fsiy4CG5OasI5\r\nMznp5ZAJxfzsPx9n/h/Thhw8z0JWKv5VdNK43QFLOdeaftYAee2y7kroOh/I\r\nB9gr4x4eMreozLzpE/HF8MI/JT4mMuHOe4VeQq5PIfyUycla+OAkTwwOjRu2\r\niq4NWdI4y040Xq6gQhfEiKrtmAG2/+S27dlIB301OxRb0k2uWB8BPYRvyGwx\r\nzk6KDP0zr0mfR8oYJ0YvPJbP4rjwdTCbRQGQ9iYuxRHInb4Stf5RHcabZycB\r\nlN7ApxqrJgxXuPXWqkx9NLyUcXC05oPXYA4L3fpH/QoFjWcZZokeKecU3twT\r\nFQ4+H0LKvqBpsxm9d6+HYx1XvzBzAV8h2Q2ds0H7/tRuJk5Ytlog7Dw0iU+o\r\nRYdNgVQTlHAJiFQLEzF7DtMbP8Ec+32hjVGgSVSRtJhlBBnPWyZ6d3ijhifK\r\n1vEQq6WV4la19O/QdWk7Vt0karnR4jqRj51dlfab1FwWSph7TGIs7qCbc7Hf\r\nMc6kByLn+xMuwfrwsapgV9DdT+/XHT602CwhOcMXzSWZhee9OdamOl9tnn+5\r\n8GO6ypGLUSPMpiMcD0Bwd2OiM0XgVpb/u+k=\r\n=yb6E\r\n-----END PGP SIGNATURE-----\r\n", "size": 72397}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_5.0.0-beta.1_1659937548061_0.18898111081531188"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-08-08T05:45:57.026Z"}, "5.0.0-beta.2": {"name": "vue-types", "version": "5.0.0-beta.2", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"require": "./shim/index.cjs.js", "import": "./shim/index.modern.js"}}, "types": "dist/index.d.ts", "engines": {"node": ">=14.0.0"}, "scripts": {"prepublishOnly": "run-s lint test build", "build": "run-s 'clean:*' copy 'build:**'", "clean:dist": "del dist", "clean:shim": "del \"shim/*.*\" \"!shim/package.json\"", "copy": "cpy --flat src/*.d.ts dist", "build:ts": "microbundle --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json --format=umd", "build:shim:ts": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.ts -o shim/index.js --format=modern,es --no-sourcemap", "build:shim:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.cjs.js --no-pkg-main --format=cjs --no-sourcemap", "build:shim:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.js --format=umd --no-sourcemap", "test": "vitest run", "lint": "eslint '{src,__tests__,.}/**/*.{ts,js,cjs}'"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.16.7", "@babel/plugin-proposal-optional-chaining": "7.16.7", "@types/node": "16.11.68", "babel-plugin-transform-node-env-inline": "0.4.3", "cpy-cli": "4.1.0", "cross-env": "7.0.3", "del": "7.0.0", "del-cli": "5.0.0", "microbundle": "0.15.1", "npm-run-all": "4.1.5", "typescript": "4.6.4"}, "dependencies": {"is-plain-object": "5.0.0"}, "peerDependenciesMeta": {"vue": {"optional": true}}, "gitHead": "9cb9db7ec2d57502f8b7db5694c4a5bfc4c17926", "_id": "vue-types@5.0.0-beta.2", "_nodeVersion": "16.16.0", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-eRg3KbHCHGdJ1Y6js3Ql5Bm6I/8Z7uHqFOd26waw5ghOqGWMrTAHv3QTPdnkqePHeRn4+XVCtWwu5O6UiSGtkA==", "shasum": "0920aae4607ec0f48b4f5533dd02d1bfd2a79362", "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-5.0.0-beta.2.tgz", "fileCount": 36, "unpackedSize": 283005, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFVTy6b9dbpqOybByNow6jz+Gn/oNzUguG/wf+X/XyCbAiAM2G0pN7sP9OPH/55J5FrnVMff+EMah2gcJeS2ddX9aQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjfGr3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoImQ//Qoz97uBvkUNzCQJJGShHkMgdyDyRray/b4tthlelXLCNJ7SB\r\n1Djkxs4PR8BAstS52XKPWYw1NlDft1fY5Y0kPz1jluCrxTcphexgf0TSNXsU\r\n43qskVR0OJA1sMr8s5Licr+RXaOX7N26SCwOR9C1MQm3wCLhsLqe2YIJHiw9\r\n2jquUguT1xrQhflGDrMem4UxpQi1hxa2OoG8iTKETdpvF24KKZaws73yd47b\r\n6ybLbvotJTZmDaYam4/cQPAvdLeqG+YJqmpWM47sU0Ncw2aQZQU2IhlQ5nfv\r\naQr43SXkDuJlxcBcfhs8pYd3rxj6x6bgVWlHzR7+xDGKU4uuutHaUk7JhWzX\r\nWcpBkQVlDGujD6v8m2GiIZKRbZCGss34Aggf6s/2fTFZ3viSzhbP7cUcERRJ\r\ndxzepExc1M1at9/4a5bLoqwJDlw+qKMfdJnTpvPbMZEByv4/BnH5ycf8V9uj\r\nxocGthSccKAQ22dK542t7DSObfWPMu6C25EJkd/MoDDTSo6PsWzF4ukFEhJc\r\ny9nU+slSDlgZ4BKwvF3A5dTIdiE8g2EzLfI7ypT02GeQkwmki7doX3iBw91l\r\nKHFKe4HNa8ondrnd3GMr1g1WjYY/fD9Oi3HTJXQdZqRWjRHC1e/axlAWLAhN\r\nRyzVm+ULYUUd+qs/umigwWgX0O87Aw1u0Sk=\r\n=czxA\r\n-----END PGP SIGNATURE-----\r\n", "size": 70318}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_5.0.0-beta.2_1669098231336_0.12890589307438138"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-11-22T06:23:57.911Z"}, "5.0.0": {"name": "vue-types", "version": "5.0.0", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"require": "./shim/index.cjs.js", "import": "./shim/index.modern.js"}}, "types": "dist/index.d.ts", "engines": {"node": ">=14.0.0"}, "scripts": {"prepublishOnly": "run-s lint test build", "build": "run-s 'clean:*' copy 'build:**'", "clean:dist": "del dist", "clean:shim": "del \"shim/*.*\" \"!shim/package.json\"", "copy": "cpy --flat src/*.d.ts dist", "build:ts": "microbundle --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json --format=umd", "build:shim:ts": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.ts -o shim/index.js --format=modern,es --no-sourcemap", "build:shim:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.cjs.js --no-pkg-main --format=cjs --no-sourcemap", "build:shim:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.js --format=umd --no-sourcemap", "test": "vitest run", "lint": "eslint '{src,__tests__,.}/**/*.{ts,js,cjs}'"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.16.7", "@babel/plugin-proposal-optional-chaining": "7.16.7", "@types/node": "16.11.68", "babel-plugin-transform-node-env-inline": "0.4.3", "cpy-cli": "4.1.0", "cross-env": "7.0.3", "del": "7.0.0", "del-cli": "5.0.0", "microbundle": "0.15.1", "npm-run-all": "4.1.5", "typescript": "4.6.4"}, "dependencies": {"is-plain-object": "5.0.0"}, "peerDependenciesMeta": {"vue": {"optional": true}}, "gitHead": "b2254dc65f006fe0664d578990cf62d6551d0761", "_id": "vue-types@5.0.0", "_nodeVersion": "16.16.0", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-giq56UtZRugrJXAO71croWBrYlhBrtvRZPqBqcng3yq4D4NwmwZdTeJu/x76aoUAAghJ24CvALoVt1sc23IaFw==", "shasum": "2c75082f72aecdb08e16c1327803960dfb12cd26", "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-5.0.0.tgz", "fileCount": 36, "unpackedSize": 282998, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDj8fwSVb+WlwOzPOxRNiE5G7S4SaLoMVB4R1qYJ0HBUAIgY7wA+aEFg++Kl2zajnBY4fWLBZxDp7WNXBEO+mvDqu4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjfIt/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoZCQ/7BDNXU8XRJh9m2XG+h+Sb3LRbITQXc3iyrrXE2LtrjQWc5aGW\r\nKr3q7rJLbD+pJHTJtM1qTiri2GU0p3KPEeQEHDxAL92jjFC2GDzUTQeIwzRR\r\nL2bQprEOZY6cthIVKqu/iaUCcJvZW9o3bp56p8saOo/QbHtPdemJ+kJETnp4\r\n8a69VOr/iq0CAeXOKaSegN/bSaGIiApiQRMimUdiS0i1n3JkNznvthNnnnHc\r\ncD53PpkGZ+BnWhQbEkK5euflWP/6xB/Iymgi3SZeB02Oy6e0ebzvZTc68Lo+\r\nKLMiwHV0kMmsgm9ITbTARY4dGeJntyK2nGMoYmBLJ5iU4rSx2+azLX8k+l7w\r\nBUkJ8axT437oa61ITU5PIN1G5yWA7qQAFPTBfz+euUoXkP/N98vtKODowE87\r\n95xyuY4N1mB8d3qRs0m29CO4vctG6hdwoyheLtJvLA1E3/NASeQdJ8vjWmyk\r\n5RwBj7Z4c5D+dDpL7BhBxUatetJuFxW6yKgG+2bT1Nfz5MRv5maUOeAZAON5\r\nqF4B1UmgW9AU2ld7QjiIsI+lYs3P2Fhy7kcb2uYXhbnR0TXyz8RTk5T+4KQe\r\nzokI7sw9uYZpayc67Anh3/Fl8zXAp9d/oTyVtTxjFneTSeKsP6t/8sU1pdXN\r\n2vsr8NxdUYnC9+8AX/Z5v3jFFk6HbzlIZw4=\r\n=ma9Z\r\n-----END PGP SIGNATURE-----\r\n", "size": 70309}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_5.0.0_1669106558801_0.3084340112714421"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-11-22T08:44:29.788Z"}, "5.0.1": {"name": "vue-types", "version": "5.0.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"require": "./shim/index.cjs.js", "import": "./shim/index.modern.js"}}, "types": "dist/index.d.ts", "engines": {"node": ">=14.0.0"}, "scripts": {"prepublishOnly": "run-s lint test build", "build": "run-s 'clean:*' copy 'build:**'", "clean:dist": "del dist", "clean:shim": "del \"shim/*.*\" \"!shim/package.json\"", "copy": "cpy --flat src/*.d.ts dist", "build:ts": "microbundle --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json --format=umd", "build:shim:ts": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.ts -o shim/index.js --format=modern,es --no-sourcemap", "build:shim:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.cjs.js --no-pkg-main --format=cjs --no-sourcemap", "build:shim:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.js --format=umd --no-sourcemap", "test": "vitest run --coverage", "test:watch": "vitest watch", "lint": "eslint '{src,__tests__,.}/**/*.{ts,js,cjs}'"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.16.7", "@babel/plugin-proposal-optional-chaining": "7.16.7", "@types/node": "16.11.68", "babel-plugin-transform-node-env-inline": "0.4.3", "cpy-cli": "4.1.0", "cross-env": "7.0.3", "del": "7.0.0", "del-cli": "5.0.0", "microbundle": "0.15.1", "npm-run-all": "4.1.5", "typescript": "4.6.4"}, "dependencies": {"is-plain-object": "5.0.0"}, "peerDependenciesMeta": {"vue": {"optional": true}}, "gitHead": "0ca3503b99e8a6b2a43d9c6df3b9d9f9badcecda", "_id": "vue-types@5.0.1", "_nodeVersion": "16.16.0", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-pHahh/Ht+mxAhSGXfFe0WQidZukYUoCevPoQtePpONrmNP+OjX3/ps/XjYLgw/Zbv4rks242eiVJcjE3EELsyw==", "shasum": "296d9939b3743f00de0ff7bb78fffb6c44ca3d6c", "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-5.0.1.tgz", "fileCount": 36, "unpackedSize": 289531, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCHY+N8v3Rc7SEG2JnNp8jQxrW4cMBf9aiG5UD+Wp7TYwIgCQMw93MWArhOaVh+gsadoe9tLthcyuiU9iA8MVCD9Gs="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjfb/DACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmplmg/+Jdbuq40bREx4FE8RG1flB2LkzN7skCS1NTm6wG3/ILAoSUaU\r\nGUMdzDPvdlWR4vuR6UKUEFoMJMOYGQjNzn/FrjUGqbN0Y9x/Tdzk4w315tqd\r\nIMrgOLwYRyQg2IXlmEHR5LkQ33QDA1XD7uHmK/wbQhJ+joZQfU8/0a6RSrBT\r\nWSj04ICUgUf/GA8Hjpdzq7k3lnmmUcJzF2l7JziIGpPinwWIv2CtyXgBEN03\r\nW2BrQTl5WgPpaP8es3q+H7bTQ2CBAAr8VtDC6OoJYoMwAU4+lq3lgSAuJ0gM\r\nyKaUZJ1WZmFh8SKIdRgFcMbAIbFDTzHkq7hr66Zcg8Q9QW2DE0weJSk7UpjS\r\n2B6j4mMXQtGIPN0zkXCt6blhkszaxO9HRFJJmbCt10f09J8LOHHU2blIa94I\r\n6Tvx0qg5jvgJvc59H6XfM4QagHGZMuA8GTSnD3lDgcv39D2h4NsyiK5+jRX2\r\nlH1wICIJCij/eirDsQF4WjjT/uQXF2XDU91KPDIkRj5Bs3soiC5sy5R8YpUx\r\nMF4Hj+1B+7jxIlU451ZYCL9LKAABKEjfne0ZscKBUePENNTIIxzCIDNDnvMY\r\nYgkZHJ8ELt2sCceENp0wGDECczKEvQUS+deZKgMqSAboDJj9i92ry0gOMA1d\r\nA40+nVY/vyju7oJ1DcIN6XEzp14dW0w5sNc=\r\n=19vp\r\n-----END PGP SIGNATURE-----\r\n", "size": 71966}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_5.0.1_1669185474845_0.48516996093472287"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-11-23T06:38:04.078Z"}, "5.0.2": {"name": "vue-types", "version": "5.0.2", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"require": "./shim/index.cjs.js", "import": "./shim/index.modern.js"}}, "types": "dist/index.d.ts", "engines": {"node": ">=14.0.0"}, "scripts": {"prepublishOnly": "run-s lint test build", "build": "run-s 'clean:*' copy 'build:**'", "clean:dist": "del dist", "clean:shim": "del \"shim/*.*\" \"!shim/package.json\"", "copy": "cpy --flat src/*.d.ts dist", "build:ts": "microbundle --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json --format=umd", "build:shim:ts": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.ts -o shim/index.js --format=modern,es --no-sourcemap", "build:shim:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.cjs.js --no-pkg-main --format=cjs --no-sourcemap", "build:shim:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.js --format=umd --no-sourcemap", "test": "vitest run --coverage", "test:watch": "vitest watch", "lint": "eslint '{src,__tests__,.}/**/*.{ts,js,cjs}'"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.16.7", "@babel/plugin-proposal-optional-chaining": "7.16.7", "@types/node": "16.11.68", "babel-plugin-transform-node-env-inline": "0.4.3", "cpy-cli": "4.1.0", "cross-env": "7.0.3", "del": "7.0.0", "del-cli": "5.0.0", "microbundle": "0.15.1", "npm-run-all": "4.1.5", "typescript": "4.6.4"}, "dependencies": {"is-plain-object": "5.0.0"}, "peerDependenciesMeta": {"vue": {"optional": true}}, "gitHead": "3035a93e6ad5e003463f4abf101980eb80958106", "_id": "vue-types@5.0.2", "_nodeVersion": "16.16.0", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-+/5hnQ65XOYqPs+tEUF8GGTJX95UFVH0wPQo71IJJYh5TKMfik2tGKTLkZ42JqAczANA9hGu5FrZmPgxn20nnA==", "shasum": "d04966bd416510db878f23030014f424a2e6378e", "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-5.0.2.tgz", "fileCount": 36, "unpackedSize": 290587, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHx2YSNvb4DaSWwTA56Wa+2bdqNJsbDveKFHAK9Kg+eFAiEAtmZGSrD5gRcRX9BYGoB9oZo+Lrhz0Gp3ErHmHvYnTkQ="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjo8qTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr8eA/5AdQA06sEkVZlzcy/XRMjLvvZc/fv4YvCBj0vFcWb15L1dXt+\r\n3GjjbX3O1U8Z7MQ8Kjokcfw+MMNnbsmmuJ+5HmSYL1O6nss+RJEgD38b7FPu\r\nyC/fZ4nA6xgekfBzhFFLF0Kmk5ZAIYr5irJsN9XExqqHTx1udxNWmJv97f84\r\nOYfwfJwho9rpcGvq3eUPBzExcIiCuVIoFWmgEjtit9nL7ie7DPCUzYReXMnm\r\nSJmSZ1FHvhezPwPi8qxwp59eMM8YhN45k9Fu5d5g6WLGbQIFlgD3mmidcaeQ\r\n5o49/Vz7PzmYoHD0t4JWKavr0JEFEnJ9Ia6de++qHf/UJkXOWBTqGp4btO7l\r\nGbwXsnHA4L0ubWoTjVSHwneg9jFR35zjPlISv4U+Rn724TC2EdvGYAW+81P/\r\nJLZZm1yuUmZAUWTcYPcvC1ZrljC6Dx6JrDOMkjuIBcFZYs2cK/BxYitLPPzG\r\neS6HGbQgH3vtJPAQXF2jUPErBEsLd3IdIOreiN4YOeqFAtObamvA0vAV15Sq\r\nin/5raJNIG5OM4duiqvj2tLZpdASnqdEJqgu7YT5J1c2h0YlI+V/WbZ3ofcu\r\nrTfFFUu91PVCxF1K0MNAXLtU30HrM3jiIqpITmhEA3QHyyWIne/eQ4oKyyGA\r\nZJP3hph+ZcRDg78Oplu+0ppdCGSDsHf9X+k=\r\n=cSAR\r\n-----END PGP SIGNATURE-----\r\n", "size": 71835}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_5.0.2_1671678611295_0.8268858382831754"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-22T03:18:55.618Z"}, "5.0.3-next.0": {"name": "vue-types", "version": "5.0.3-next.0", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"require": "./shim/index.cjs.js", "import": "./shim/index.modern.js"}}, "types": "dist/index.d.ts", "engines": {"node": ">=14.0.0"}, "scripts": {"prepublishOnly": "run-s lint test build", "build": "run-s 'clean:*' copy 'build:**'", "clean:dist": "del dist", "clean:shim": "del \"shim/*.*\" \"!shim/package.json\"", "copy": "cpy --flat src/*.d.ts dist", "build:ts": "microbundle --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json --format=umd", "build:shim:ts": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.ts -o shim/index.js --format=modern,es --no-sourcemap", "build:shim:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.cjs.js --no-pkg-main --format=cjs --no-sourcemap", "build:shim:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.js --format=umd --no-sourcemap", "test": "vitest run --coverage", "test:watch": "vitest watch", "lint": "eslint '{src,__tests__,.}/**/*.{ts,js,cjs}'"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@types/node": "18.15.13", "babel-plugin-transform-node-env-inline": "0.4.3", "cpy-cli": "4.2.0", "cross-env": "7.0.3", "del": "7.0.0", "del-cli": "5.0.0", "microbundle": "0.15.1", "npm-run-all": "4.1.5", "typescript": "4.9.5"}, "dependencies": {"is-plain-object": "5.0.0"}, "peerDependenciesMeta": {"vue": {"optional": true}}, "gitHead": "a025af4d6cb9ae3a4c8d0d85e061a3af7c0f40d2", "_id": "vue-types@5.0.3-next.0", "_nodeVersion": "18.15.0", "_npmVersion": "9.5.0", "dist": {"integrity": "sha512-lzxckbAYP3AgWnBTaTJ4AwcJARlXwLrH57autbUickdHzr41WtJ6yDSw9P5o5PAMoWo+CeyCgCOcdjyuCvxTJg==", "shasum": "5a4ad24a6a8359ac70deff3115a4a6a0d0dd9520", "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-5.0.3-next.0.tgz", "fileCount": 36, "unpackedSize": 295611, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDEuGcpKyCxRsUYKXuHkuEXaTPu1jVcjVNuWYQk7//TvQIgMgdExFB5xSdvHhW5rtX9GGivjI7yG5Cqhj3DjzDjiH4="}], "size": 72530}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_5.0.3-next.0_1684120171912_0.3991282918668302"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-15T03:09:32.053Z", "publish_time": 1684120172053, "_source_registry_name": "default"}, "5.0.3": {"name": "vue-types", "version": "5.0.3", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"require": "./shim/index.cjs.js", "import": "./shim/index.modern.js"}}, "types": "dist/index.d.ts", "engines": {"node": ">=14.0.0"}, "scripts": {"prepublishOnly": "run-s lint test build", "build": "run-s 'clean:*' copy 'build:**'", "clean:dist": "del dist", "clean:shim": "del \"shim/*.*\" \"!shim/package.json\"", "copy": "cpy --flat src/*.d.ts dist", "build:ts": "microbundle --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json --format=umd", "build:shim:ts": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.ts -o shim/index.js --format=modern,es --no-sourcemap", "build:shim:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.cjs.js --no-pkg-main --format=cjs --no-sourcemap", "build:shim:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.js --format=umd --no-sourcemap", "test": "vitest run --coverage", "test:watch": "vitest watch", "lint": "eslint '{src,__tests__,.}/**/*.{ts,js,cjs}'"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@types/node": "18.15.13", "babel-plugin-transform-node-env-inline": "0.4.3", "cpy-cli": "4.2.0", "cross-env": "7.0.3", "del": "7.0.0", "del-cli": "5.0.0", "microbundle": "0.15.1", "npm-run-all": "4.1.5", "typescript": "4.9.5"}, "dependencies": {"is-plain-object": "5.0.0"}, "peerDependenciesMeta": {"vue": {"optional": true}}, "gitHead": "ee5a8caeea72c3888429c7e12a170372a647259f", "_id": "vue-types@5.0.3", "_nodeVersion": "18.15.0", "_npmVersion": "9.5.0", "dist": {"integrity": "sha512-7+yOUXlRrpkJ4mtgmB6nIfDYLdLmxHQ/IMZR3hsEziYnHK4Y/Rf+rlCIg1rwNnlbyTiDf4v/vz1LEumo3RgwrQ==", "shasum": "4a86a0491a94f52ba27cd0a2b926a25a6cfb25c5", "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-5.0.3.tgz", "fileCount": 36, "unpackedSize": 295604, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCMeW6j3xK/Yu3iKxxVZcucWVan8r47iIUPrU2QjA5XXgIhAN1w1rLlgPKXEXublYlxnT8K7Y/xNzsCSLaF6MW239sE"}], "size": 72526}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_5.0.3_1684120352514_0.3733879001938014"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-15T03:12:32.757Z", "publish_time": 1684120352757, "_source_registry_name": "default"}, "5.0.4": {"name": "vue-types", "version": "5.0.4", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"require": "./shim/index.cjs.js", "import": "./shim/index.modern.js"}}, "types": "dist/index.d.ts", "engines": {"node": ">=14.0.0"}, "scripts": {"prepublishOnly": "run-s lint test build", "build": "run-s 'clean:*' copy 'build:**'", "clean:dist": "del dist", "clean:shim": "del \"shim/*.*\" \"!shim/package.json\"", "copy": "cpy --flat src/*.d.ts dist", "build:ts": "microbundle --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json --format=umd", "build:shim:ts": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.ts -o shim/index.js --format=modern,es --no-sourcemap", "build:shim:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.cjs.js --no-pkg-main --format=cjs --no-sourcemap", "build:shim:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.js --format=umd --no-sourcemap", "test": "vitest run --coverage", "test:watch": "vitest watch", "lint": "eslint '{src,__tests__,.}/**/*.{ts,js,cjs}'"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@types/node": "20.1.4", "babel-plugin-transform-node-env-inline": "0.4.3", "cpy-cli": "4.2.0", "cross-env": "7.0.3", "del": "7.0.0", "del-cli": "5.0.0", "microbundle": "0.15.1", "npm-run-all": "4.1.5", "typescript": "5.0.4"}, "dependencies": {"is-plain-object": "5.0.0"}, "peerDependenciesMeta": {"vue": {"optional": true}}, "gitHead": "b106148ca9a3fbb604d36b6685b9e8408d35ba1b", "_id": "vue-types@5.0.4", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-ksYUQpvhk1Xl/K43OPkcm54VcX4tvxQoNYjYLk+n45NOocDsg9+DnviPq/KfDLjGs4P23iAosFPR8JSzuh9IPA==", "shasum": "7840ed82cb8ee9572a9db6e553e66c83fafb882d", "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-5.0.4.tgz", "fileCount": 36, "unpackedSize": 295474, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCZMVJNHZezR827BdS/cH66rrTJHIp5gIS5d6NSnBYR8QIgdfWTEC3voc88tL7c/GsGV25UK/E4gJRcI3f62JhyUSg="}], "size": 72581}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_5.0.4_1686811068779_0.1990098959777178"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-15T06:37:48.957Z", "publish_time": 1686811068957, "_source_registry_name": "default"}, "5.1.0": {"name": "vue-types", "version": "5.1.0", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"require": "./shim/index.cjs.js", "import": "./shim/index.modern.js"}}, "types": "dist/index.d.ts", "engines": {"node": ">=14.0.0"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@types/node": "20.1.4", "babel-plugin-transform-node-env-inline": "0.4.3", "cpy-cli": "4.2.0", "cross-env": "7.0.3", "del": "7.0.0", "del-cli": "5.0.0", "microbundle": "0.15.1", "typescript": "5.0.4"}, "dependencies": {"is-plain-object": "5.0.0"}, "peerDependenciesMeta": {"vue": {"optional": true}}, "scripts": {"build": "pnpm run \"/^clean:.*/\" && pnpm run copy && pnpm run \"/^build:.*/\"", "clean:dist": "del dist", "clean:shim": "del \"shim/*.*\" \"!shim/package.json\"", "copy": "cpy --flat src/*.d.ts dist", "build:ts": "microbundle --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json --format=umd", "build:shim:ts": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.ts -o shim/index.js --format=modern,es --no-sourcemap", "build:shim:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.cjs.js --no-pkg-main --format=cjs --no-sourcemap", "build:shim:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.js --format=umd --no-sourcemap", "test": "vitest run --coverage", "test:watch": "vitest watch", "lint": "eslint '{src,__tests__,.}/**/*.{ts,js,cjs}'"}, "_id": "vue-types@5.1.0", "_integrity": "sha512-oCSq5MawTli+Jqaf07sCZgJr/FcDCFF5U/VE4WG58S3EAPxqi8nZlbrQ9I50rD5MZdJ3fjgi/IX1tVLL3QUFzA==", "_resolved": "/private/var/folders/hm/70y4t3rx1_vdwmcvl3lscf180000gp/T/3c3596ce79c0a87e560c1a437ec56765/vue-types-5.1.0.tgz", "_from": "file:vue-types-5.1.0.tgz", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-oCSq5MawTli+Jqaf07sCZgJr/FcDCFF5U/VE4WG58S3EAPxqi8nZlbrQ9I50rD5MZdJ3fjgi/IX1tVLL3QUFzA==", "shasum": "680126b3338afaa633753448816efd19adf6614d", "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-5.1.0.tgz", "fileCount": 37, "unpackedSize": 297670, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGIfYKSoYxMkOVQXqOCyBm1bDHQQVcWLbjObBkSoA/nyAiBT5VdvKVBeSuxf3lrOg6Zrmap1kCbkrQ66Kswh9f/guw=="}], "size": 73819}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_5.1.0_1687497639379_0.5764302034419133"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-23T05:20:39.574Z", "publish_time": 1687497639574, "_source_registry_name": "default"}, "0.0.0-20230720044845": {"name": "vue-types", "version": "0.0.0-20230720044845", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"type": "./dist/shim.d.ts", "require": "./shim/index.cjs.js", "import": "./shim/index.modern.js"}}, "types": "dist/index.d.ts", "engines": {"node": ">=14.0.0"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@types/node": "20.1.7", "babel-plugin-transform-node-env-inline": "0.4.3", "cpy-cli": "4.2.0", "cross-env": "7.0.3", "del": "7.0.0", "del-cli": "5.0.0", "microbundle": "0.15.1", "typescript": "5.0.4"}, "dependencies": {"is-plain-object": "5.0.0"}, "peerDependenciesMeta": {"vue": {"optional": true}}, "scripts": {"build": "pnpm run \"/^clean:.*/\" && pnpm run copy && pnpm run \"/^build:.*/\"", "clean:dist": "del dist", "clean:shim": "del \"shim/*.*\" \"!shim/package.json\"", "copy": "cpy --flat src/*.d.ts dist", "build:ts": "microbundle --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json --format=umd", "build:shim:ts": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.ts -o shim/index.js --format=modern,es --no-sourcemap", "build:shim:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.cjs.js --no-pkg-main --format=cjs --no-sourcemap", "build:shim:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.js --format=umd --no-sourcemap", "test": "vitest run --coverage", "test:watch": "vitest watch", "lint": "eslint '{src,__tests__,.}/**/*.{ts,js,cjs}'"}, "_id": "vue-types@0.0.0-20230720044845", "_integrity": "sha512-d1zKF57yr4pHx+70N7APMT9B4vSRLXSaimSpBeThPNawUBZxuk75LfmmhLjCZ7uvnHG2i2Teb2IkVMNVgwYtPg==", "_resolved": "/tmp/5084adc257f4eb7dc757ba213a753f74/vue-types-0.0.0-20230720044845.tgz", "_from": "file:vue-types-0.0.0-20230720044845.tgz", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-d1zKF57yr4pHx+70N7APMT9B4vSRLXSaimSpBeThPNawUBZxuk75LfmmhLjCZ7uvnHG2i2Teb2IkVMNVgwYtPg==", "shasum": "8c584eac251a21fc39195cbd859e23d75e8abbb1", "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-0.0.0-20230720044845.tgz", "fileCount": 37, "unpackedSize": 297755, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCrzN51+oQc4Gl+nt++H9Wrn8Vs3NSoIC802mq7JLxmrAIhAKVKE2V1wDN86cRlsveeSGgKaAhlOyLMlCO1F0ayETQH"}], "size": 73843}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_0.0.0-20230720044845_1689828560479_0.8176438112109237"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-20T04:49:20.651Z", "publish_time": 1689828560651, "_source_registry_name": "default"}, "0.0.0-20230721024211": {"name": "vue-types", "version": "0.0.0-20230721024211", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"types": "./dist/shim.d.ts", "require": "./shim/index.cjs.js", "import": "./shim/index.modern.js"}}, "types": "dist/index.d.ts", "engines": {"node": ">=14.0.0"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@types/node": "20.1.7", "babel-plugin-transform-node-env-inline": "0.4.3", "cpy-cli": "4.2.0", "cross-env": "7.0.3", "del": "7.0.0", "del-cli": "5.0.0", "microbundle": "0.15.1", "typescript": "5.0.4"}, "dependencies": {"is-plain-object": "5.0.0"}, "peerDependenciesMeta": {"vue": {"optional": true}}, "scripts": {"build": "pnpm run \"/^clean:.*/\" && pnpm run copy && pnpm run \"/^build:.*/\"", "clean:dist": "del dist", "clean:shim": "del \"shim/*.*\" \"!shim/package.json\"", "copy": "cpy --flat src/*.d.ts dist", "build:ts": "microbundle --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json --format=umd", "build:shim:ts": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.ts -o shim/index.js --format=modern,es --no-sourcemap", "build:shim:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.cjs.js --no-pkg-main --format=cjs --no-sourcemap", "build:shim:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.js --format=umd --no-sourcemap", "test": "vitest run --coverage", "test:watch": "vitest watch", "lint": "eslint '{src,__tests__,.}/**/*.{ts,js,cjs}'"}, "_id": "vue-types@0.0.0-20230721024211", "_integrity": "sha512-u5E1GBCbvnrR1QW4XAXygVAkURJz1ZT31+mjwQNX+5oakUGGpQGA9F7IHPvDZhHX4p0NXcdcNk+3RK8Zv0CnjQ==", "_resolved": "/tmp/c979d86c846292cc4721ef07ed6232da/vue-types-0.0.0-20230721024211.tgz", "_from": "file:vue-types-0.0.0-20230721024211.tgz", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-u5E1GBCbvnrR1QW4XAXygVAkURJz1ZT31+mjwQNX+5oakUGGpQGA9F7IHPvDZhHX4p0NXcdcNk+3RK8Zv0CnjQ==", "shasum": "491670ad4f9d084396f2ff56ea678e1ef66f008f", "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-0.0.0-20230721024211.tgz", "fileCount": 37, "unpackedSize": 297888, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICOH18/0OgyB1JskHQ7y44aysuIc4Y1r3N1MFYo2ecydAiEA41nvSd37SWMEZ3E8+iMvODmwz/PxCFf60KCGFBdM7iU="}], "size": 73872}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_0.0.0-20230721024211_1689907364436_0.17996609090135052"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-21T02:42:44.594Z", "publish_time": 1689907364594, "_source_registry_name": "default"}, "5.1.1": {"name": "vue-types", "version": "5.1.1", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"types": "./dist/shim.d.ts", "require": "./shim/index.cjs.js", "import": "./shim/index.modern.js"}}, "types": "dist/index.d.ts", "engines": {"node": ">=14.0.0"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@types/node": "20.1.7", "babel-plugin-transform-node-env-inline": "0.4.3", "cpy-cli": "4.2.0", "cross-env": "7.0.3", "del": "7.0.0", "del-cli": "5.0.0", "microbundle": "0.15.1", "typescript": "5.0.4"}, "dependencies": {"is-plain-object": "5.0.0"}, "peerDependenciesMeta": {"vue": {"optional": true}}, "scripts": {"build": "pnpm run \"/^clean:.*/\" && pnpm run copy && pnpm run \"/^build:.*/\"", "clean:dist": "del dist", "clean:shim": "del \"shim/*.*\" \"!shim/package.json\"", "copy": "cpy --flat src/*.d.ts dist", "build:ts": "microbundle --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json --format=umd", "build:shim:ts": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.ts -o shim/index.js --format=modern,es --no-sourcemap", "build:shim:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.cjs.js --no-pkg-main --format=cjs --no-sourcemap", "build:shim:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.js --format=umd --no-sourcemap", "test": "vitest run --coverage", "test:watch": "vitest watch", "lint": "eslint '{src,__tests__,.}/**/*.{ts,js,cjs}'"}, "_id": "vue-types@5.1.1", "_integrity": "sha512-FMY/JCLWePXgGIcMDqYdJsQm1G0CDxEjq6W0+tZMJZlX37q/61eSGSIa/XFRwa9T7kkKXuxxl94/2kgxyWQqKw==", "_resolved": "/tmp/988ec0229264ab972080318c5c610f5f/vue-types-5.1.1.tgz", "_from": "file:vue-types-5.1.1.tgz", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-FMY/JCLWePXgGIcMDqYdJsQm1G0CDxEjq6W0+tZMJZlX37q/61eSGSIa/XFRwa9T7kkKXuxxl94/2kgxyWQqKw==", "shasum": "1052b85f440a90ad4ea8249d5aa6f231b92d062e", "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-5.1.1.tgz", "fileCount": 37, "unpackedSize": 297873, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD8j8DmzcNthTJaeEIDVvzY/RWvlAUT/HeOIka2rTt2fQIhAIYDDafitWwQ9r3ZPFtBZ0kvOrIkR9Dgjc8vuCwvUZt5"}], "size": 73861}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_5.1.1_1690346829276_0.6065462235283536"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-26T04:47:09.412Z", "publish_time": 1690346829412, "_source_registry_name": "default"}, "0.0.0-20240502090343": {"name": "vue-types", "version": "0.0.0-20240502090343", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"types": "./dist/shim.d.ts", "require": "./shim/index.cjs.js", "import": "./shim/index.modern.js"}}, "types": "dist/index.d.ts", "engines": {"node": ">=14.0.0"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@types/node": "20.12.7", "babel-plugin-transform-node-env-inline": "0.4.3", "cpy-cli": "5.0.0", "cross-env": "7.0.3", "del": "7.1.0", "del-cli": "5.1.0", "microbundle": "0.15.1", "typescript": "5.4.5"}, "dependencies": {"is-plain-object": "5.0.0"}, "peerDependenciesMeta": {"vue": {"optional": true}}, "scripts": {"build": "pnpm run \"/^clean:.*/\" && pnpm run copy && pnpm run \"/^build:.*/\"", "clean:dist": "del dist", "clean:shim": "del \"shim/*.*\" \"!shim/package.json\"", "copy": "cpy --flat src/*.d.ts dist", "build:ts": "microbundle --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json --format=umd", "build:shim:ts": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.ts -o shim/index.js --format=modern,es --no-sourcemap", "build:shim:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.cjs.js --no-pkg-main --format=cjs --no-sourcemap", "build:shim:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.js --format=umd --no-sourcemap", "test": "vitest run --coverage", "test:watch": "vitest watch", "lint": "eslint '{src,__tests__,.}/**/*.{ts,js,cjs}'"}, "_id": "vue-types@0.0.0-20240502090343", "_integrity": "sha512-X1g2f72JhLxV0MmYYpHcQxkVR4f+lVpJR2NJGDJilLaxWmOgpiaqNfScMdtP6X2lceYU21qKAZ/flKutyg6hxA==", "_resolved": "/tmp/383f7e76c424beff27756cb00ba25800/vue-types-0.0.0-20240502090343.tgz", "_from": "file:vue-types-0.0.0-20240502090343.tgz", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-X1g2f72JhLxV0MmYYpHcQxkVR4f+lVpJR2NJGDJilLaxWmOgpiaqNfScMdtP6X2lceYU21qKAZ/flKutyg6hxA==", "shasum": "915e4872140cd9b67205e7c37e5fd36ae6ad53ab", "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-0.0.0-20240502090343.tgz", "fileCount": 37, "unpackedSize": 298179, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/vue-types@0.0.0-20240502090343", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBU5MTf6WbTqkx8TJtcpqZZrrXzgO55lV5eo9i+dirb0AiEA4ZHhnj4+ViC83S3xV9qCTtLhjnFIsob41GD/x+V38y0="}], "size": 74333}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_0.0.0-20240502090343_1714640648047_0.5159869883907231"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-02T09:04:08.275Z", "publish_time": 1714640648275, "_source_registry_name": "default"}, "0.0.0-20240502090959": {"name": "vue-types", "version": "0.0.0-20240502090959", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"types": "./dist/shim.d.ts", "require": "./shim/index.cjs.js", "import": "./shim/index.modern.js"}}, "types": "dist/index.d.ts", "engines": {"node": ">=14.0.0"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git", "directory": "packages/core"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@types/node": "20.12.7", "babel-plugin-transform-node-env-inline": "0.4.3", "cpy-cli": "5.0.0", "cross-env": "7.0.3", "del": "7.1.0", "del-cli": "5.1.0", "microbundle": "0.15.1", "typescript": "5.4.5"}, "dependencies": {"is-plain-object": "5.0.0"}, "peerDependenciesMeta": {"vue": {"optional": true}}, "scripts": {"build": "pnpm run \"/^clean:.*/\" && pnpm run copy && pnpm run \"/^build:.*/\"", "clean:dist": "del dist", "clean:shim": "del \"shim/*.*\" \"!shim/package.json\"", "copy": "cpy --flat src/*.d.ts dist", "build:ts": "microbundle --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json --format=umd", "build:shim:ts": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.ts -o shim/index.js --format=modern,es --no-sourcemap", "build:shim:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.cjs.js --no-pkg-main --format=cjs --no-sourcemap", "build:shim:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.js --format=umd --no-sourcemap", "test": "vitest run --coverage", "test:watch": "vitest watch", "lint": "eslint '{src,__tests__,.}/**/*.{ts,js,cjs}'"}, "_id": "vue-types@0.0.0-20240502090959", "_integrity": "sha512-NNMHyCuDykte1DWU5a4ETqfrZeB9+u5Bz3y59L02QutpSSQF6f4rR+6k3bBX3YIw0tWazinWJtfmKCYj9pMMKw==", "_resolved": "/tmp/dc469feacfd9dfd859f21e83c48900fe/vue-types-0.0.0-20240502090959.tgz", "_from": "file:vue-types-0.0.0-20240502090959.tgz", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-NNMHyCuDykte1DWU5a4ETqfrZeB9+u5Bz3y59L02QutpSSQF6f4rR+6k3bBX3YIw0tWazinWJtfmKCYj9pMMKw==", "shasum": "caa1c93ad0d67764873952441685c69256f34b20", "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-0.0.0-20240502090959.tgz", "fileCount": 37, "unpackedSize": 298213, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/vue-types@0.0.0-20240502090959", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAWJRtzPgPacgid8obklUIC+rhrEswpKlqsqSxnKMveKAiAJ8scpbv68swP3IEg/XZJWckfR+kc2hivY2SWv8QY8Cg=="}], "size": 74348}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_0.0.0-20240502090959_1714641022625_0.35757052810594825"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-02T09:10:22.837Z", "publish_time": 1714641022837, "_source_registry_name": "default"}, "0.0.0-20240502091121": {"name": "vue-types", "version": "0.0.0-20240502091121", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"types": "./dist/shim.d.ts", "require": "./shim/index.cjs.js", "import": "./shim/index.modern.js"}}, "types": "dist/index.d.ts", "engines": {"node": ">=14.0.0"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git", "directory": "packages/core"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@types/node": "20.12.7", "babel-plugin-transform-node-env-inline": "0.4.3", "cpy-cli": "5.0.0", "cross-env": "7.0.3", "del": "7.1.0", "del-cli": "5.1.0", "microbundle": "0.15.1", "typescript": "5.4.5"}, "dependencies": {"is-plain-object": "5.0.0"}, "peerDependenciesMeta": {"vue": {"optional": true}}, "scripts": {"build": "pnpm run \"/^clean:.*/\" && pnpm run copy && pnpm run \"/^build:.*/\"", "clean:dist": "del dist", "clean:shim": "del \"shim/*.*\" \"!shim/package.json\"", "copy": "cpy --flat src/*.d.ts dist", "build:ts": "microbundle --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json --format=umd", "build:shim:ts": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.ts -o shim/index.js --format=modern,es --no-sourcemap", "build:shim:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.cjs.js --no-pkg-main --format=cjs --no-sourcemap", "build:shim:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.js --format=umd --no-sourcemap", "test": "vitest run --coverage", "test:watch": "vitest watch", "lint": "eslint '{src,__tests__,.}/**/*.{ts,js,cjs}'"}, "_id": "vue-types@0.0.0-20240502091121", "_integrity": "sha512-+KydsJhbaBdsHAeMvUYU/YModEOEex2H6+EqPN3dg2RrsQKK2AJqB97FDIW/EsLKwetcgnX6DtkZxXWlGvUmvQ==", "_resolved": "/tmp/13c5f4c4a8a05afff8532d8344ed2ae4/vue-types-0.0.0-20240502091121.tgz", "_from": "file:vue-types-0.0.0-20240502091121.tgz", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-+KydsJhbaBdsHAeMvUYU/YModEOEex2H6+EqPN3dg2RrsQKK2AJqB97FDIW/EsLKwetcgnX6DtkZxXWlGvUmvQ==", "shasum": "d1f811d151f702922ca35f5357c4c8338e255012", "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-0.0.0-20240502091121.tgz", "fileCount": 37, "unpackedSize": 298213, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/vue-types@0.0.0-20240502091121", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAfqbHgJFpWsdgM0X40ir8uIKGrwMwJY51p28q6EyfxYAiBjuvVF/0DNjqj3Df9qMxULvJrnHzm+TDEt4ar3FFhMog=="}], "size": 74348}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_0.0.0-20240502091121_1714641104845_0.17791597005276238"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-02T09:11:45.012Z", "publish_time": 1714641105012, "_source_registry_name": "default"}, "5.1.2": {"name": "vue-types", "version": "5.1.2", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./shim": {"types": "./dist/shim.d.ts", "require": "./shim/index.cjs.js", "import": "./shim/index.modern.js"}}, "types": "dist/index.d.ts", "engines": {"node": ">=14.0.0"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git", "directory": "packages/core"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@types/node": "20.12.7", "babel-plugin-transform-node-env-inline": "0.4.3", "cpy-cli": "5.0.0", "cross-env": "7.0.3", "del": "7.1.0", "del-cli": "5.1.0", "microbundle": "0.15.1", "typescript": "5.4.5"}, "dependencies": {"is-plain-object": "5.0.0"}, "peerDependenciesMeta": {"vue": {"optional": true}}, "scripts": {"build": "pnpm run \"/^clean:.*/\" && pnpm run copy && pnpm run \"/^build:.*/\"", "clean:dist": "del dist", "clean:shim": "del \"shim/*.*\" \"!shim/package.json\"", "copy": "cpy --flat src/*.d.ts dist", "build:ts": "microbundle --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json --format=umd", "build:shim:ts": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.ts -o shim/index.js --format=modern,es --no-sourcemap", "build:shim:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.cjs.js --no-pkg-main --format=cjs --no-sourcemap", "build:shim:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.js --format=umd --no-sourcemap", "test": "vitest run --coverage", "test:watch": "vitest watch", "lint": "eslint '{src,__tests__,.}/**/*.{ts,js,cjs}'"}, "_id": "vue-types@5.1.2", "_integrity": "sha512-NdwLhEl2hn2PRsHb+eYgwBfimFZB2Y0s11YqFRX0VEMrDZZvzPXYxcUJ+aH7AIHyxGc47qdgakVzcjw6bZElBw==", "_resolved": "/tmp/edf778945b7d7e911df03533060187b9/vue-types-5.1.2.tgz", "_from": "file:vue-types-5.1.2.tgz", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-NdwLhEl2hn2PRsHb+eYgwBfimFZB2Y0s11YqFRX0VEMrDZZvzPXYxcUJ+aH7AIHyxGc47qdgakVzcjw6bZElBw==", "shasum": "8733d8c52e5e95fa9718e3d362760d1516320c87", "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-5.1.2.tgz", "fileCount": 37, "unpackedSize": 298198, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/vue-types@5.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCk3YrFh8Z17buA2wtUICcqXFrcL6Xw+pFM4yWqzPhEJQIhAJXympX6o59NShWCEpGsxQZB+ahAd05l0yAvB3MeMgSc"}], "size": 74333}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_5.1.2_1714641865521_0.9619556011171331"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-02T09:24:25.688Z", "publish_time": 1714641865688, "_source_registry_name": "default"}, "0.0.0-20240701051251": {"name": "vue-types", "version": "0.0.0-20240701051251", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./dist/types": {"types": "./dist/types.d.ts"}, "./shim": {"types": "./dist/shim.d.ts", "require": "./shim/index.cjs.js", "import": "./shim/index.modern.js"}}, "types": "dist/index.d.ts", "engines": {"node": ">=14.0.0"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git", "directory": "packages/core"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@types/node": "20.14.2", "babel-plugin-transform-node-env-inline": "0.4.3", "cpy-cli": "5.0.0", "cross-env": "7.0.3", "del": "7.1.0", "del-cli": "5.1.0", "microbundle": "0.15.1", "typescript": "5.4.5"}, "dependencies": {"is-plain-object": "5.0.0"}, "peerDependenciesMeta": {"vue": {"optional": true}}, "scripts": {"build": "pnpm run \"/^clean:.*/\" && pnpm run copy && pnpm run \"/^build:.*/\"", "clean:dist": "del dist", "clean:shim": "del \"shim/*.*\" \"!shim/package.json\"", "copy": "cpy --flat src/*.d.ts dist", "build:ts": "microbundle --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json --format=umd", "build:shim:ts": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.ts -o shim/index.js --format=modern,es --no-sourcemap", "build:shim:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.cjs.js --no-pkg-main --format=cjs --no-sourcemap", "build:shim:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.js --format=umd --no-sourcemap", "test": "vitest run --coverage", "test:watch": "vitest watch", "lint": "eslint '{src,__tests__,.}/**/*.{ts,js,cjs}'"}, "_id": "vue-types@0.0.0-20240701051251", "_integrity": "sha512-Q06gZgbgveG2DezWmOY45WTtPm7Y2rQtOb+puM2B7widI70Ve0WFqQJYAkXrWZgkzl1lp8NtDFEe2Ibnzd1MjA==", "_resolved": "/tmp/3a406fdcb13f04e1fd4af0b179c66309/vue-types-0.0.0-20240701051251.tgz", "_from": "file:vue-types-0.0.0-20240701051251.tgz", "_nodeVersion": "20.14.0", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-Q06gZgbgveG2DezWmOY45WTtPm7Y2rQtOb+puM2B7widI70Ve0WFqQJYAkXrWZgkzl1lp8NtDFEe2Ibnzd1MjA==", "shasum": "5993a0f35d60d6cd3ac21b9358582bfdb4c8a407", "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-0.0.0-20240701051251.tgz", "fileCount": 37, "unpackedSize": 298129, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/vue-types@0.0.0-20240701051251", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCtz8U4D34Gbqb7MrnReL4ou9qgEr7YiL0DWR8rBfD9wgIhAK9xl+6cSZQ47TNmWvPa9PoHdx5o4Ci2AuNMFwT0pglR"}], "size": 74457}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_0.0.0-20240701051251_1719810796314_0.8805643760782387"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-01T05:13:16.549Z", "publish_time": 1719810796549, "_source_registry_name": "default"}, "5.1.3": {"name": "vue-types", "version": "5.1.3", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/vue-types.cjs", "type": "module", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/vue-types.cjs", "import": "./dist/vue-types.modern.js"}, "./dist/types": {"types": "./dist/types.d.ts"}, "./shim": {"types": "./dist/shim.d.ts", "require": "./shim/index.cjs.js", "import": "./shim/index.modern.js"}}, "types": "dist/index.d.ts", "engines": {"node": ">=14.0.0"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git", "directory": "packages/core"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^2.0.0 || ^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@types/node": "20.14.2", "babel-plugin-transform-node-env-inline": "0.4.3", "cpy-cli": "5.0.0", "cross-env": "7.0.3", "del": "7.1.0", "del-cli": "5.1.0", "microbundle": "0.15.1", "typescript": "5.4.5"}, "dependencies": {"is-plain-object": "5.0.0"}, "peerDependenciesMeta": {"vue": {"optional": true}}, "scripts": {"build": "pnpm run \"/^clean:.*/\" && pnpm run copy && pnpm run \"/^build:.*/\"", "clean:dist": "del dist", "clean:shim": "del \"shim/*.*\" \"!shim/package.json\"", "copy": "cpy --flat src/*.d.ts dist", "build:ts": "microbundle --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.cjs --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json --format=umd", "build:shim:ts": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.ts -o shim/index.js --format=modern,es --no-sourcemap", "build:shim:cjs": "microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.cjs.js --no-pkg-main --format=cjs --no-sourcemap", "build:shim:umd": "cross-env NODE_ENV=production microbundle --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o shim/index.js --format=umd --no-sourcemap", "test": "vitest run --coverage", "test:watch": "vitest watch", "lint": "eslint '{src,__tests__,.}/**/*.{ts,js,cjs}'"}, "_id": "vue-types@5.1.3", "_integrity": "sha512-3Wy6QcZl0VusCCHX3vYrWSILFlrOB2EQDoySnuYmASM5cUp1FivJGfkS5lp1CutDgyRb41g32r/1QCmiBj5i1Q==", "_resolved": "/tmp/dabb1ef25cc6e06eabb9e7d1461dd933/vue-types-5.1.3.tgz", "_from": "file:vue-types-5.1.3.tgz", "_nodeVersion": "20.14.0", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-3Wy6QcZl0VusCCHX3vYrWSILFlrOB2EQDoySnuYmASM5cUp1FivJGfkS5lp1CutDgyRb41g32r/1QCmiBj5i1Q==", "shasum": "f1476d2dc1d8cf8be9a723595e866aedcb0f2fb3", "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-5.1.3.tgz", "fileCount": 37, "unpackedSize": 298114, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/vue-types@5.1.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDmjUAmRXdm+ofV0OS2255U16/ExdiaKd/jhKb6Ezu8fgIhAN/UzdoGmNIL8eobmMV2BOKNjiavcL0ygr1LO072+PZW"}], "size": 74444}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vue-types_5.1.3_1720487714433_0.9459039990391616"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-09T01:15:14.652Z", "publish_time": 1720487714652, "_source_registry_name": "default"}, "0.0.0-20241212014929": {"name": "vue-types", "version": "0.0.0-20241212014929", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/index.cjs", "type": "module", "sideEffects": false, "unpkg": "dist/index.umd.js", "umd:main": "dist/index.umd.js", "module": "dist/index.mjs", "esmodule": "dist/index.mjs", "exports": {".": {"require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}, "import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}}, "./shim": {"require": {"types": "./dist/shim.d.cts", "default": "./dist/shim.cjs"}, "import": {"types": "./dist/shim.d.mts", "default": "./dist/shim.mjs"}}}, "types": "dist/index.d.ts", "engines": {"node": ">=14.0.0"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git", "directory": "packages/core"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^3.0.0"}, "devDependencies": {"@types/node": "20.17.6", "typescript": "5.6.3", "unbuild": "2.0.0"}, "peerDependenciesMeta": {"vue": {"optional": true}}, "scripts": {"build": "unbuild", "test": "vitest run --coverage", "test:watch": "vitest watch", "lint": "pnpm run \"/^lint:.*/\"", "lint:type": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "lint:js": "eslint '{src,__tests__,.}/**/*.{ts,js,cjs}'"}, "_id": "vue-types@0.0.0-20241212014929", "_integrity": "sha512-skIOc6sbBg2Y1iuGU1/ckMhMeJVwboXpK2GnM/YkhSd0cZkDK/FCGodCXx0ROVw2zhzZQ46X22VA8vusYvRjJQ==", "_resolved": "/tmp/1748da3b1dcb716287b93b6c788b783f/vue-types-0.0.0-20241212014929.tgz", "_from": "file:vue-types-0.0.0-20241212014929.tgz", "_nodeVersion": "22.12.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-skIOc6sbBg2Y1iuGU1/ckMhMeJVwboXpK2GnM/YkhSd0cZkDK/FCGodCXx0ROVw2zhzZQ46X22VA8vusYvRjJQ==", "shasum": "23d9aa201d02e4da8b2ca7233de08d9b26a8b1b1", "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-0.0.0-20241212014929.tgz", "fileCount": 26, "unpackedSize": 228221, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/vue-types@0.0.0-20241212014929", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGh0QXt7b8weSYrnwXTHKmmywta1E1/EJhktrxBUxIdiAiArfI3UkONNAqtE9qNwhA5a2dICbsZz+R+6/sOCzje7kA=="}], "size": 48291}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/vue-types_0.0.0-20241212014929_1733968179432_0.17197500562931478"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-12T01:49:39.649Z", "publish_time": 1733968179649, "_source_registry_name": "default"}, "0.0.0-20250212054805": {"name": "vue-types", "version": "0.0.0-20250212054805", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://dwightjack.github.io/vue-types/", "main": "dist/index.cjs", "type": "module", "sideEffects": false, "unpkg": "dist/index.umd.js", "umd:main": "dist/index.umd.js", "module": "dist/index.mjs", "esmodule": "dist/index.mjs", "exports": {".": {"require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}, "import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}}, "./shim": {"require": {"types": "./dist/shim.d.cts", "default": "./dist/shim.cjs"}, "import": {"types": "./dist/shim.d.mts", "default": "./dist/shim.mjs"}}}, "types": "dist/index.d.ts", "engines": {"node": ">=14.0.0"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git", "directory": "packages/core"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^3.0.0"}, "devDependencies": {"@types/node": "20.17.11", "typescript": "5.6.3", "unbuild": "2.0.0"}, "peerDependenciesMeta": {"vue": {"optional": true}}, "scripts": {"build": "unbuild", "test": "vitest run --coverage", "test:watch": "vitest watch", "lint": "pnpm run \"/^lint:.*/\"", "lint:type": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "lint:js": "eslint '{src,__tests__,.}/**/*.{ts,js,cjs}'"}, "_id": "vue-types@0.0.0-20250212054805", "_integrity": "sha512-GdJOBhwbzsd110zyUpqNV8L/W2ZLCBJCXf5xKUPXzjUN49b6IcY5LVY3DpYPeo3NIJZLZhU7GG7Cyb2RCpZefQ==", "_resolved": "/tmp/04014d1a3d0ba1b644d1e19caf024410/vue-types-0.0.0-20250212054805.tgz", "_from": "file:vue-types-0.0.0-20250212054805.tgz", "_nodeVersion": "22.12.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-GdJOBhwbzsd110zyUpqNV8L/W2ZLCBJCXf5xKUPXzjUN49b6IcY5LVY3DpYPeo3NIJZLZhU7GG7Cyb2RCpZefQ==", "shasum": "dade7679d035361c72f3be155e1a6272e7a690d6", "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-0.0.0-20250212054805.tgz", "fileCount": 26, "unpackedSize": 228222, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/vue-types@0.0.0-20250212054805", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIBid7sGi/8Mu/uBsZMr4gOVn3lRbllZq7fuaogyWPmuxAiEA8DYmQdQmpd6UB2qHIJTqYTjknd66L4z2Ca6R1e9vQ8Y="}], "size": 48291}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/vue-types_0.0.0-20250212054805_1739339295820_0.37423110415707295"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-12T05:48:16.081Z", "publish_time": 1739339296081, "_source_registry_name": "default"}, "6.0.0": {"name": "vue-types", "version": "6.0.0", "description": "Prop types utility for Vue", "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://vue-types.codeful.dev/", "main": "dist/index.cjs", "type": "module", "sideEffects": false, "unpkg": "dist/index.umd.js", "umd:main": "dist/index.umd.js", "module": "dist/index.mjs", "esmodule": "dist/index.mjs", "exports": {".": {"require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}, "import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}}, "./shim": {"require": {"types": "./dist/shim.d.cts", "default": "./dist/shim.cjs"}, "import": {"types": "./dist/shim.d.mts", "default": "./dist/shim.mjs"}}}, "types": "dist/index.d.ts", "engines": {"node": ">=14.0.0"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git", "directory": "packages/core"}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "peerDependencies": {"vue": "^3.0.0"}, "devDependencies": {"@types/node": "20.17.11", "typescript": "5.6.3", "unbuild": "2.0.0"}, "peerDependenciesMeta": {"vue": {"optional": true}}, "scripts": {"build": "unbuild", "test": "vitest run --coverage", "test:watch": "vitest watch", "lint": "pnpm run \"/^lint:.*/\"", "lint:npm": "publint .", "lint:type": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "lint:js": "eslint '{src,__tests__,.}/**/*.{ts,js,cjs}'"}, "_id": "vue-types@6.0.0", "_integrity": "sha512-fBgCA4nrBrB8SCU/AN40tFq8HUxLGBvU2ds7a5+SEDse6dYc+TJyvy8mWiwwL8oWIC/aGS/8nTqmhwxApgU5eA==", "_resolved": "/tmp/0538ac31f9adacc037da4a59f8c4a40a/vue-types-6.0.0.tgz", "_from": "file:vue-types-6.0.0.tgz", "_nodeVersion": "22.12.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-fBgCA4nrBrB8SCU/AN40tFq8HUxLGBvU2ds7a5+SEDse6dYc+TJyvy8mWiwwL8oWIC/aGS/8nTqmhwxApgU5eA==", "shasum": "3a9be5624491d5936652f3d1f2469ae3f53c6a94", "tarball": "https://registry.npmmirror.com/vue-types/-/vue-types-6.0.0.tgz", "fileCount": 26, "unpackedSize": 228231, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/vue-types@6.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCdePXqcaNrm4hQqYAPwEEVO8bdzyKFu9lCNeshoWiLogIhAKgI1cnI909G8XGJvVkoglgzLwsEUkr/sC2C4lIxmZ7l"}], "size": 48300}, "_npmUser": {"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "dwightjack", "email": "marco.sola<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/vue-types_6.0.0_1739756385563_0.8468212559149333"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-17T01:39:45.768Z", "publish_time": 1739756385768, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/dwightjack/vue-types/issues"}, "homepage": "https://vue-types.codeful.dev/", "keywords": ["vue", "props"], "repository": {"type": "git", "url": "git+https://github.com/dwightjack/vue-types.git", "directory": "packages/core"}, "_source_registry_name": "default"}