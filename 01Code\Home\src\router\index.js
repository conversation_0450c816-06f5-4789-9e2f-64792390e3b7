import { createRouter, createWebHistory } from 'vue-router'
import publicRoutes from './publicRoutes'
import { useAuthStore } from '@/stores/auth/auth.js'
import { addDynamicRoutes } from './dynamicRoutes'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [...publicRoutes], // 初始只加载公共路由
  scrollBehavior(to, from, savedPosition) {
    // 保持页面滚动位置
    return savedPosition || { top: 0 }
  }
})

// 需要认证但允许所有角色的路由名称
const AUTH_ROUTE_NAMES = ['Profile', 'Notifications']

/**
 * 检查路由是否需要特定角色权限
 */
const requiresRolePermission = (to) => {
  return to.meta.role !== undefined && 
         !AUTH_ROUTE_NAMES.includes(to.name)
}

router.beforeEach(async (to) => {
  const authStore = useAuthStore()
  
  // 1. 同步初始化认证状态（处理页面刷新）
  if (!authStore.isAuthenticated && localStorage.getItem('token')) {
    authStore.initializeAuthState()
  }

  // 2. 动态路由处理逻辑
  if (authStore.isAuthenticated) {
    // 确保角色信息存在
    if (authStore.user?.role === undefined) {
      authStore.logout()
      return '/login'
    }

    // 如果访问的是动态路由但未加载，则立即加载
    const isRouteMissing = !router.hasRoute(to.name)
    const isAuthRoute = AUTH_ROUTE_NAMES.includes(to.name)
    
    if (isRouteMissing && !isAuthRoute) {
      addDynamicRoutes(authStore.user.role)
      return to.fullPath // 重试当前导航
    }
  }

  // 3. 路由权限检查
  // 需要登录但未登录 → 跳转登录页
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    return {
      path: '/login',
      query: { redirect: to.fullPath } // 保存目标路由用于登录后跳转
    }
  }

  // 已登录但访问登录页 → 跳转到首页
  if (to.path === '/login' && authStore.isAuthenticated) {
    return authStore.isTeacher ? '/teacher/dashboard' : '/student/dashboard'
  }

  // 角色权限检查
  if (
    requiresRolePermission(to) && 
    Number(to.meta.role) !== Number(authStore.user?.role)
  ) {
    return '/unauthorized'
  }

  // 4. 处理动态路由加载后仍不匹配的情况（404）
  if (!to.matched.length) {
    if (authStore.isAuthenticated) {
      // 可能是动态路由加载失败，尝试重新加载
      addDynamicRoutes(authStore.user.role)
      return to.fullPath
    }
    return '/404'
  }

  return true
})

export default router