<template>
  <div class="teacher-team-page">
    <TeamList
      :createdTeams="createdTeams"
      :joinedTeams="joinedTeams"
      :loading="loading"
      @action="handleTeamAction"
      @member-removed="fetchTeamMembers"
    />

    <TeamModals
      :active-modal="activeModal"
      :selected-team="selectedTeam"
      @close="closeModals"
      @confirm="handleModalConfirm"
      @add-member-success="fetchTeamMembers"
    />
  </div>
</template>

<script>
import { ref, onMounted } from 'vue';
import { useUserStore } from '@/stores/userStore';
import { getTeachingTeams, disbandTeam,getCourseTeachers } from '@/api/teacher/courseTeam';
import TeamList from './components/TeamList.vue';
import TeamModals from './components/TeamModals.vue';

export default {
  components: { TeamList, TeamModals },
  setup() {
    const userStore = useUserStore();
    const createdTeams = ref([]);
    const joinedTeams = ref([]);
    const activeModal = ref(null);
    const selectedTeam = ref(null);
    const loading = ref(true);
    const dataLoaded = ref(false);

    const getRoleName = (roleCode) => {
      const roles = {
        1: '创建者',
        2: '教师',
        3: '助教',
      };
      return roles[roleCode] || '成员';
    };

    const loadTeamData = async () => {
  try {
    loading.value = true;

    const [createdRes, joinedRes] = await Promise.all([
      getTeachingTeams({ teamRole: 1 }),
      getTeachingTeams({ teamRole: 2 }),
    ]);

    const fetchTeamMembers = async (courseId) => {
      const response = await getCourseTeachers(courseId);
      if (response && response.result && Array.isArray(response.result)) {
        return response.result.map(member => ({
          teacherId: member.id,
          teacherName: member.teacherName,
          teacherTitle: member.teacherTitle,
          teacherImg: member.teacherImg,
          teacherType: member.teacherType,
          teacherIntro: member.teacherIntro,
          teamRole: member.teamRole,
          roleName: getRoleName(member.teamRole),
        }));
      }
      return [];
    };

    if (createdRes && Array.isArray(createdRes)) {
      createdTeams.value = await Promise.all(createdRes.map(async (course) => {
        const members = await fetchTeamMembers(course.courseId);
        return {
          id: course.courseId,
          name: course.courseName || '未命名课程',
          courseId: course.courseId,
          createTime: new Date().toISOString(),
          members,
        };
      }));
    }

    if (joinedRes && Array.isArray(joinedRes)) {
      joinedTeams.value = await Promise.all(joinedRes.map(async (course) => {
        const members = await fetchTeamMembers(course.courseId);
        return {
          id: course.courseId,
          name: course.courseName || '未命名课程',
          courseId: course.courseId,
          joinTime: new Date().toISOString(),
          members: members.filter(member => member.teamRole !== 1),
        };
      }));
    }

    dataLoaded.value = true;
  } catch (error) {
    console.error('加载团队数据失败:', error);
  } finally {
    loading.value = false;
  }
};

    const handleTeamAction = async ({ type, team }) => {
      selectedTeam.value = team;
      if (type === 'add-member') {
        activeModal.value = 'add-member';
      } else if (type === 'disband') {
        try {
          await disbandTeam(team.courseId);
          await loadTeamData(); // 刷新数据
        } catch (error) {
          console.error('解散团队失败:', error);
          alert('解散团队失败，请稍后再试');
        }
      } else {
        activeModal.value = type;
      }
    };

    const closeModals = () => {
      activeModal.value = null;
      selectedTeam.value = null;
    };

    const fetchTeamMembers = async () => {
      try {
        await loadTeamData(); // 刷新数据
      } catch (error) {
        console.error('刷新团队成员列表失败:', error);
      }
    };

    const handleMemberRemoved = ({ courseId, teacherId }) => {
    // 更新我创建的团队
    createdTeams.value = createdTeams.value.map(team => {
      if (team.courseId === courseId) {
        return {
          ...team,
          members: team.members.filter(m => m.teacherId !== teacherId)
        };
      }
      return team;
    });

    // 更新我加入的团队
    joinedTeams.value = joinedTeams.value.map(team => {
      if (team.courseId === courseId) {
        return {
          ...team,
          members: team.members.filter(m => m.teacherId !== teacherId)
        };
      }
      return team;
    });
  };

    const handleModalConfirm = async () => {
      try {
        await fetchTeamMembers(); // 刷新团队成员列表
        closeModals();
      } catch (error) {
        console.error('操作失败:', error);
      }
    };

    onMounted(() => {
      loadTeamData();
    });

    return {
      createdTeams,
      joinedTeams,
      activeModal,
      selectedTeam,
      loading,
      dataLoaded,
      handleTeamAction,
      closeModals,
      handleModalConfirm,
      fetchTeamMembers
    };
  },
};
</script>

<style scoped>
.teacher-team-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}
</style>      