<template>
  <div class="project-download">
    <div class="content-header">
      <h2>项目压缩包下载</h2>
      <div class="header-divider"></div>
    </div>
    
    <div class="download-overview">
      <div class="overview-card">
        <div class="overview-icon">
          <i class="fas fa-download"></i>
        </div>
        <div class="overview-content">
          <h3>完整项目资源包</h3>
          <p>包含实验平台、文档资料、示例数据等完整资源，支持离线使用</p>
          <div class="package-info">
            <span class="package-size">总大小: {{ packageInfo.totalSize }}</span>
            <span class="package-version">版本: {{ packageInfo.version }}</span>
            <span class="package-date">更新时间: {{ packageInfo.updateDate }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <div class="download-packages">
      <h3>可选下载包</h3>
      <div class="packages-grid">
        <div 
          class="package-item" 
          v-for="pkg in downloadPackages" 
          :key="pkg.id"
        >
          <div class="package-header">
            <div class="package-icon">
              <i :class="pkg.icon"></i>
            </div>
            <div class="package-title">
              <h4>{{ pkg.name }}</h4>
              <span class="package-size">{{ pkg.size }}</span>
            </div>
          </div>
          
          <div class="package-description">
            <p>{{ pkg.description }}</p>
          </div>
          
          <div class="package-contents">
            <h5>包含内容:</h5>
            <ul>
              <li v-for="content in pkg.contents" :key="content">{{ content }}</li>
            </ul>
          </div>
          
          <div class="package-actions">
            <button 
              class="download-btn" 
              @click="downloadPackage(pkg)"
              :disabled="pkg.downloading"
            >
              <i class="fas fa-download" v-if="!pkg.downloading"></i>
              <i class="fas fa-spinner fa-spin" v-else></i>
              {{ pkg.downloading ? '下载中...' : '立即下载' }}
            </button>
            <div class="download-progress" v-if="pkg.downloading">
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: pkg.progress + '%' }"></div>
              </div>
              <span class="progress-text">{{ pkg.progress }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="system-requirements">
      <h3>系统要求</h3>
      <div class="requirements-grid">
        <div class="requirement-category" v-for="category in systemRequirements" :key="category.type">
          <h4>{{ category.type }}</h4>
          <ul>
            <li v-for="req in category.requirements" :key="req">{{ req }}</li>
          </ul>
        </div>
      </div>
    </div>
    
    <div class="installation-guide">
      <h3>安装说明</h3>
      <div class="guide-steps">
        <div class="step-item" v-for="(step, index) in installationSteps" :key="index">
          <div class="step-number">{{ index + 1 }}</div>
          <div class="step-content">
            <h4>{{ step.title }}</h4>
            <p>{{ step.description }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const packageInfo = ref({
  totalSize: '1.2GB',
  version: 'v2.1.0',
  updateDate: '2024-03-20'
})

const downloadPackages = ref([
  {
    id: 1,
    name: '核心实验平台',
    size: '450MB',
    description: '包含虚拟仿真实验的核心功能模块，支持基本的实验操作',
    icon: 'fas fa-cube',
    contents: [
      '虚拟实验室环境',
      '基础实验工具',
      '标本采集模块',
      '检测分析功能'
    ],
    downloading: false,
    progress: 0
  },
  {
    id: 2,
    name: '扩展资源包',
    size: '320MB',
    description: '包含额外的实验场景、案例库和高级功能模块',
    icon: 'fas fa-puzzle-piece',
    contents: [
      '高级实验场景',
      '临床案例库',
      '3D模型资源',
      '音频视频素材'
    ],
    downloading: false,
    progress: 0
  },
  {
    id: 3,
    name: '教学文档包',
    size: '180MB',
    description: '完整的教学资料、操作手册和参考文档',
    icon: 'fas fa-book',
    contents: [
      '教师指导手册',
      '学生操作指南',
      '实验报告模板',
      '评估标准文档'
    ],
    downloading: false,
    progress: 0
  },
  {
    id: 4,
    name: '示例数据包',
    size: '250MB',
    description: '包含各种典型的实验数据和结果示例',
    icon: 'fas fa-database',
    contents: [
      '标准检测数据',
      '异常结果示例',
      '质控数据样本',
      '统计分析模板'
    ],
    downloading: false,
    progress: 0
  }
])

const systemRequirements = ref([
  {
    type: '操作系统',
    requirements: [
      'Windows 10/11 (64位)',
      'macOS 10.15 或更高版本',
      'Ubuntu 18.04 或更高版本'
    ]
  },
  {
    type: '硬件配置',
    requirements: [
      'CPU: Intel i5 或 AMD 同等性能',
      '内存: 8GB RAM (推荐16GB)',
      '存储: 2GB 可用空间',
      '显卡: 支持OpenGL 3.3'
    ]
  },
  {
    type: '软件环境',
    requirements: [
      'Chrome 90+ / Firefox 88+ / Safari 14+',
      '.NET Framework 4.7.2 或更高',
      'Visual C++ 2019 运行库'
    ]
  }
])

const installationSteps = ref([
  {
    title: '下载安装包',
    description: '选择合适的安装包下载到本地计算机'
  },
  {
    title: '解压文件',
    description: '使用解压软件将下载的压缩包解压到指定目录'
  },
  {
    title: '运行安装程序',
    description: '双击setup.exe文件，按照向导完成安装'
  },
  {
    title: '配置环境',
    description: '根据系统要求配置必要的运行环境和依赖项'
  },
  {
    title: '启动验证',
    description: '启动程序并进行功能验证，确保安装成功'
  }
])

const downloadPackage = (pkg) => {
  if (pkg.downloading) return
  
  pkg.downloading = true
  pkg.progress = 0
  
  // 模拟下载进度
  const interval = setInterval(() => {
    pkg.progress += Math.random() * 15
    if (pkg.progress >= 100) {
      pkg.progress = 100
      pkg.downloading = false
      clearInterval(interval)
      
      // 模拟下载完成
      setTimeout(() => {
        pkg.progress = 0
        console.log(`${pkg.name} 下载完成`)
      }, 1000)
    }
  }, 200)
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.project-download {
  padding: 24px;
  max-width: 1000px;
  
  .content-header {
    margin-bottom: 32px;
    
    h2 {
      font-size: 28px;
      font-weight: 700;
      color: $text-color;
      margin: 0 0 16px 0;
    }
    
    .header-divider {
      width: 60px;
      height: 4px;
      background: linear-gradient(90deg, $primary-color, lighten($primary-color, 20%));
      border-radius: 2px;
    }
  }
  
  .download-overview {
    margin-bottom: 32px;
    
    .overview-card {
      background: linear-gradient(135deg, rgba($primary-color, 0.1), rgba($primary-color, 0.05));
      border: 1px solid rgba($primary-color, 0.2);
      border-radius: 16px;
      padding: 32px;
      display: flex;
      align-items: center;
      gap: 24px;
      
      .overview-icon {
        width: 80px;
        height: 80px;
        background: $primary-color;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        
        i {
          font-size: 32px;
          color: white;
        }
      }
      
      .overview-content {
        flex: 1;
        
        h3 {
          font-size: 24px;
          font-weight: 700;
          color: $text-color;
          margin: 0 0 12px 0;
        }
        
        p {
          font-size: 16px;
          line-height: 1.6;
          color: rgba($text-color, 0.8);
          margin: 0 0 16px 0;
        }
        
        .package-info {
          display: flex;
          gap: 24px;
          font-size: 14px;
          
          span {
            color: rgba($text-color, 0.6);
            font-weight: 500;
          }
        }
      }
    }
  }
  
  .download-packages {
    margin-bottom: 32px;
    
    h3 {
      font-size: 20px;
      font-weight: 600;
      color: $text-color;
      margin-bottom: 24px;
      position: relative;
      padding-left: 16px;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background: $primary-color;
        border-radius: 2px;
      }
    }
    
    .packages-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap: 24px;
      
      .package-item {
        background: white;
        border: 1px solid $border-color;
        border-radius: 12px;
        padding: 24px;
        transition: all 0.3s ease;
        
        &:hover {
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
          transform: translateY(-4px);
        }
        
        .package-header {
          display: flex;
          align-items: center;
          gap: 16px;
          margin-bottom: 16px;
          
          .package-icon {
            width: 48px;
            height: 48px;
            background: rgba($primary-color, 0.1);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            
            i {
              font-size: 20px;
              color: $primary-color;
            }
          }
          
          .package-title {
            flex: 1;
            
            h4 {
              font-size: 18px;
              font-weight: 600;
              color: $text-color;
              margin: 0 0 4px 0;
            }
            
            .package-size {
              font-size: 14px;
              color: rgba($text-color, 0.6);
              font-weight: 500;
            }
          }
        }
        
        .package-description {
          margin-bottom: 16px;
          
          p {
            font-size: 14px;
            line-height: 1.6;
            color: rgba($text-color, 0.8);
            margin: 0;
          }
        }
        
        .package-contents {
          margin-bottom: 20px;
          
          h5 {
            font-size: 14px;
            font-weight: 600;
            color: $text-color;
            margin: 0 0 8px 0;
          }
          
          ul {
            list-style: none;
            padding: 0;
            margin: 0;
            
            li {
              font-size: 13px;
              line-height: 1.4;
              color: rgba($text-color, 0.7);
              margin-bottom: 4px;
              padding-left: 16px;
              position: relative;
              
              &::before {
                content: '•';
                position: absolute;
                left: 0;
                color: $primary-color;
                font-weight: bold;
              }
            }
          }
        }
        
        .package-actions {
          .download-btn {
            width: 100%;
            padding: 12px;
            background: $primary-color;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            
            &:hover:not(:disabled) {
              background: darken($primary-color, 10%);
              transform: translateY(-1px);
            }
            
            &:disabled {
              opacity: 0.7;
              cursor: not-allowed;
            }
          }
          
          .download-progress {
            margin-top: 12px;
            
            .progress-bar {
              width: 100%;
              height: 6px;
              background: rgba($primary-color, 0.1);
              border-radius: 3px;
              overflow: hidden;
              margin-bottom: 8px;
              
              .progress-fill {
                height: 100%;
                background: $primary-color;
                transition: width 0.3s ease;
                border-radius: 3px;
              }
            }
            
            .progress-text {
              font-size: 12px;
              color: rgba($text-color, 0.6);
              text-align: center;
              display: block;
            }
          }
        }
      }
    }
  }
  
  .system-requirements {
    margin-bottom: 32px;
    
    h3 {
      font-size: 20px;
      font-weight: 600;
      color: $text-color;
      margin-bottom: 24px;
      position: relative;
      padding-left: 16px;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background: $warning-color;
        border-radius: 2px;
      }
    }
    
    .requirements-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      
      .requirement-category {
        background: rgba($warning-color, 0.05);
        border: 1px solid rgba($warning-color, 0.2);
        border-radius: 8px;
        padding: 20px;
        
        h4 {
          font-size: 16px;
          font-weight: 600;
          color: $text-color;
          margin: 0 0 12px 0;
          padding-bottom: 8px;
          border-bottom: 1px solid rgba($warning-color, 0.2);
        }
        
        ul {
          list-style: none;
          padding: 0;
          margin: 0;
          
          li {
            font-size: 14px;
            line-height: 1.5;
            color: rgba($text-color, 0.8);
            margin-bottom: 8px;
            padding-left: 16px;
            position: relative;
            
            &::before {
              content: '✓';
              position: absolute;
              left: 0;
              color: $success-color;
              font-weight: bold;
            }
          }
        }
      }
    }
  }
  
  .installation-guide {
    h3 {
      font-size: 20px;
      font-weight: 600;
      color: $text-color;
      margin-bottom: 24px;
      position: relative;
      padding-left: 16px;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background: $success-color;
        border-radius: 2px;
      }
    }
    
    .guide-steps {
      .step-item {
        display: flex;
        align-items: flex-start;
        gap: 16px;
        margin-bottom: 20px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .step-number {
          width: 32px;
          height: 32px;
          background: $success-color;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 14px;
          flex-shrink: 0;
        }
        
        .step-content {
          flex: 1;
          
          h4 {
            font-size: 16px;
            font-weight: 600;
            color: $text-color;
            margin: 0 0 8px 0;
          }
          
          p {
            font-size: 14px;
            line-height: 1.6;
            color: rgba($text-color, 0.8);
            margin: 0;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .project-download {
    padding: 16px;
    
    .content-header h2 {
      font-size: 24px;
    }
    
    .overview-card {
      flex-direction: column;
      text-align: center;
      gap: 16px;
      
      .package-info {
        flex-direction: column;
        gap: 8px;
      }
    }
    
    .packages-grid {
      grid-template-columns: 1fr;
    }
    
    .requirements-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>
