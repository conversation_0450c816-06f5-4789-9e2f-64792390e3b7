import { defineStore } from 'pinia'
import { register, login, logout, checkCaptchaRequired, generateCaptcha } from '@/api/auth/auth.js'
import { addDynamicRoutes } from '@/router/dynamicRoutes'
import router from '@/router'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    token: localStorage.getItem('token') || null,
    user: JSON.parse(localStorage.getItem('user')) || null,
    captchaRequired: false,
    captchaToken: null,
    captchaImageUrl: null
  }),

  getters: {
    isAuthenticated: (state) => !!state.token,
    isTeacher: (state) => state.user?.role === 2,
    isStudent: (state) => state.user?.role === 1,
    roleName: (state) => {
      switch(state.user?.role) {
        case 1: return '学生'
        case 2: return '教师'
        case 0: return '管理员'
        default: return '未知角色'
      }
    }
  },

  actions: {
    // 同步初始化认证状态（用于页面刷新时）
    initializeAuthState() {
      const token = localStorage.getItem('token')
      const user = JSON.parse(localStorage.getItem('user'))
      
      if (token && user) {
        this.token = token
        this.user = user
        return true
      }
      return false
    },

    // 检查是否需要验证码
    async checkCaptchaRequired(account) {
      try {
        this.captchaRequired = await checkCaptchaRequired(account)
        return this.captchaRequired
      } catch (error) {
        console.error('检查验证码需求失败:', error)
        return false
      }
    },

    // 生成验证码
    async generateNewCaptcha(account) {
      try {
        const { imageUrl, token } = await generateCaptcha(account)
        this.captchaImageUrl = imageUrl
        this.captchaToken = token
        return { imageUrl, token }
      } catch (error) {
        console.error('生成验证码失败:', error)
        throw error
      }
    },

    // 登录
    async login(credentials) {
      try {
        const userData = await login({
          ...credentials,
          captchaToken: this.captchaToken
        })

        // 更新状态
        this.token = userData.token
        this.user = {
          id: userData.id,
          account: userData.account,
          role: Number(userData.role),
          name: userData.name || userData.account,
          avatar: userData.avatar || ''
        }

        // 持久化存储
        localStorage.setItem('token', this.token)
        localStorage.setItem('user', JSON.stringify(this.user))

        // 清除验证码相关状态
        this.captchaToken = null
        this.captchaImageUrl = null

        // 加载动态路由
        addDynamicRoutes(this.user.role)

        // 返回重定向路径
        return this.user.role === 2 ? '/teacher/dashboard' : '/student/dashboard'
      } catch (error) {
        this.clearAuthState()
        throw error
      }
    },

    // 注册
    async register(userData) {
      try {
        const response = await register({
          ...userData,
          captchaToken: this.captchaToken
        })

        if (response.success) {
          // 注册后自动登录
          return this.login({
            account: userData.account,
            password: userData.password
          })
        }
      } catch (error) {
        this.clearAuthState()
        throw error
      }
    },

    // 注销
    async logout() {
      try {
        await logout()
      } catch (error) {
        console.error('注销请求失败:', error)
      } finally {
        this.clearAuthState()
        router.push('/login')
      }
    },

    // 清除认证状态（内部方法）
    clearAuthState() {
      this.token = null
      this.user = null
      this.captchaToken = null
      this.captchaImageUrl = null
      localStorage.removeItem('token')
      localStorage.removeItem('user')
    }
  }
})