<template>
  <div class="thesis-paper">
    <div class="paper-header">
      <h2>毕业设计论文</h2>
      <div class="paper-meta">
        <span class="meta-item">
          <i class="fas fa-calendar"></i>
          完成时间: {{ thesisData.completionDate }}
        </span>
        <span class="meta-item">
          <i class="fas fa-file-pdf"></i>
          文件大小: {{ thesisData.fileSize }}
        </span>
        <span class="meta-item">
          <i class="fas fa-eye"></i>
          浏览次数: {{ thesisData.viewCount }}
        </span>
      </div>
    </div>
    
    <div class="paper-abstract">
      <h3>论文摘要</h3>
      <div class="abstract-content">
        <p>{{ thesisData.abstract }}</p>
      </div>
    </div>
    
    <div class="paper-outline">
      <h3>论文大纲</h3>
      <div class="outline-tree">
        <div 
          class="outline-item" 
          v-for="(chapter, index) in thesisData.outline" 
          :key="index"
        >
          <div class="chapter-title">
            <span class="chapter-number">第{{ index + 1 }}章</span>
            <span class="chapter-name">{{ chapter.title }}</span>
          </div>
          <ul class="section-list" v-if="chapter.sections">
            <li 
              v-for="(section, sIndex) in chapter.sections" 
              :key="sIndex"
              class="section-item"
            >
              {{ index + 1 }}.{{ sIndex + 1 }} {{ section }}
            </li>
          </ul>
        </div>
      </div>
    </div>
    
    <div class="paper-keywords">
      <h3>关键词</h3>
      <div class="keywords-list">
        <span 
          class="keyword-tag" 
          v-for="keyword in thesisData.keywords" 
          :key="keyword"
        >
          {{ keyword }}
        </span>
      </div>
    </div>
    
    <div class="paper-actions">
      <button class="action-btn primary" @click="downloadPaper">
        <i class="fas fa-download"></i>
        下载完整论文
      </button>
      <button class="action-btn secondary" @click="previewPaper">
        <i class="fas fa-eye"></i>
        在线预览
      </button>
      <button class="action-btn secondary" @click="sharePaper">
        <i class="fas fa-share"></i>
        分享论文
      </button>
    </div>
    
    <!-- 预览模态框 -->
    <div v-if="showPreview" class="preview-modal" @click="closePreview">
      <div class="preview-content" @click.stop>
        <div class="preview-header">
          <h3>论文预览</h3>
          <button class="close-btn" @click="closePreview">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="preview-body">
          <div class="preview-pages">
            <div class="page-placeholder">
              <i class="fas fa-file-pdf"></i>
              <p>论文预览功能</p>
              <small>点击下载按钮获取完整PDF文档</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const showPreview = ref(false)

const thesisData = ref({
  title: "基于虚拟仿真技术的环境色彩设计教学研究",
  completionDate: "2024年6月",
  fileSize: "3.2MB",
  viewCount: 1247,
  abstract: "本研究基于虚拟仿真技术，构建了环境色彩设计教学平台，通过理论学习、单人模式和双人协作模式，实现了沉浸式的色彩设计教学体验。研究采用3D虚拟数字人技术，模拟真实的设计师与客户交互场景，包括客户沟通、色彩数据采集、项目建立等完整工作流程。实验结果表明，该平台能够有效提升学生的色彩理论掌握程度和实践设计能力，为环境色彩设计教学提供了新的解决方案。",
  outline: [
    {
      title: "绪论",
      sections: [
        "研究背景与意义",
        "国内外研究现状",
        "研究内容与方法",
        "论文结构安排"
      ]
    },
    {
      title: "相关理论基础",
      sections: [
        "色彩理论基础",
        "虚拟仿真技术原理",
        "教学设计理论",
        "人机交互设计"
      ]
    },
    {
      title: "系统设计与实现",
      sections: [
        "系统架构设计",
        "功能模块设计",
        "用户界面设计",
        "技术实现方案"
      ]
    },
    {
      title: "实验设计与分析",
      sections: [
        "实验设计方案",
        "数据采集与处理",
        "结果分析与讨论",
        "效果评估"
      ]
    },
    {
      title: "总结与展望",
      sections: [
        "研究成果总结",
        "创新点与贡献",
        "不足与改进",
        "未来研究方向"
      ]
    }
  ],
  keywords: ["虚拟仿真", "环境色彩设计", "教学平台", "3D虚拟数字人", "沉浸式体验", "协作学习"]
})

const downloadPaper = () => {
  console.log('下载论文')
  // 实际项目中这里会触发文件下载
}

const previewPaper = () => {
  showPreview.value = true
}

const sharePaper = () => {
  console.log('分享论文')
  // 实际项目中这里会打开分享功能
}

const closePreview = () => {
  showPreview.value = false
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.thesis-paper {
  padding: 32px;
  max-width: 800px;
  
  .paper-header {
    margin-bottom: 32px;
    
    h2 {
      font-size: 28px;
      font-weight: 700;
      color: $text-color;
      margin: 0 0 16px 0;
    }
    
    .paper-meta {
      display: flex;
      gap: 24px;
      flex-wrap: wrap;
      
      .meta-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        color: rgba($text-color, 0.6);
        
        i {
          color: $primary-color;
        }
      }
    }
  }
  
  .paper-abstract {
    margin-bottom: 32px;
    
    h3 {
      font-size: 20px;
      font-weight: 600;
      color: $text-color;
      margin: 0 0 16px 0;
      position: relative;
      padding-left: 16px;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background: $primary-color;
        border-radius: 2px;
      }
    }
    
    .abstract-content {
      background: rgba($primary-color, 0.03);
      border: 1px solid rgba($primary-color, 0.1);
      border-radius: 8px;
      padding: 20px;
      
      p {
        font-size: 16px;
        line-height: 1.8;
        color: $text-color;
        margin: 0;
        text-align: justify;
      }
    }
  }
  
  .paper-outline {
    margin-bottom: 32px;
    
    h3 {
      font-size: 20px;
      font-weight: 600;
      color: $text-color;
      margin: 0 0 16px 0;
      position: relative;
      padding-left: 16px;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background: $success-color;
        border-radius: 2px;
      }
    }
    
    .outline-tree {
      .outline-item {
        margin-bottom: 20px;
        
        .chapter-title {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 12px;
          
          .chapter-number {
            background: $primary-color;
            color: white;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 14px;
            font-weight: 600;
          }
          
          .chapter-name {
            font-size: 16px;
            font-weight: 600;
            color: $text-color;
          }
        }
        
        .section-list {
          list-style: none;
          padding: 0;
          margin: 0 0 0 20px;
          
          .section-item {
            font-size: 14px;
            line-height: 1.6;
            color: rgba($text-color, 0.8);
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
            
            &::before {
              content: '•';
              position: absolute;
              left: 0;
              color: $primary-color;
              font-weight: bold;
            }
          }
        }
      }
    }
  }
  
  .paper-keywords {
    margin-bottom: 32px;
    
    h3 {
      font-size: 20px;
      font-weight: 600;
      color: $text-color;
      margin: 0 0 16px 0;
      position: relative;
      padding-left: 16px;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background: $warning-color;
        border-radius: 2px;
      }
    }
    
    .keywords-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      .keyword-tag {
        background: rgba($primary-color, 0.1);
        color: $primary-color;
        padding: 6px 12px;
        border-radius: 16px;
        font-size: 14px;
        font-weight: 500;
        border: 1px solid rgba($primary-color, 0.2);
      }
    }
  }
  
  .paper-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    
    .action-btn {
      padding: 12px 20px;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;
      
      &.primary {
        background: $primary-color;
        color: white;
        
        &:hover {
          background: darken($primary-color, 10%);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba($primary-color, 0.3);
        }
      }
      
      &.secondary {
        background: white;
        color: $primary-color;
        border: 1px solid $primary-color;
        
        &:hover {
          background: $primary-color;
          color: white;
          transform: translateY(-2px);
        }
      }
    }
  }
  
  .preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    
    .preview-content {
      background: white;
      border-radius: 12px;
      max-width: 80vw;
      max-height: 80vh;
      width: 600px;
      overflow: hidden;
      
      .preview-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20px;
        border-bottom: 1px solid $border-color;
        
        h3 {
          font-size: 18px;
          font-weight: 600;
          color: $text-color;
          margin: 0;
        }
        
        .close-btn {
          width: 32px;
          height: 32px;
          background: none;
          border: none;
          border-radius: 50%;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          color: rgba($text-color, 0.5);
          transition: all 0.3s ease;
          
          &:hover {
            background: rgba($text-color, 0.1);
            color: $text-color;
          }
        }
      }
      
      .preview-body {
        padding: 40px;
        text-align: center;
        
        .page-placeholder {
          i {
            font-size: 48px;
            color: rgba($text-color, 0.3);
            margin-bottom: 16px;
          }
          
          p {
            font-size: 18px;
            color: $text-color;
            margin: 0 0 8px 0;
          }
          
          small {
            color: rgba($text-color, 0.6);
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .thesis-paper {
    padding: 20px;
    
    .paper-header h2 {
      font-size: 24px;
    }
    
    .paper-meta {
      flex-direction: column;
      gap: 8px;
    }
    
    .paper-actions {
      flex-direction: column;
      
      .action-btn {
        justify-content: center;
      }
    }
    
    .preview-content {
      width: 95vw;
      max-height: 90vh;
    }
  }
}
</style>
